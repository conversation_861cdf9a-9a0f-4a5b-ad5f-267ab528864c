#!/usr/bin/env python3
"""
Django App Generator from Documentation using Gemini 2.5 Flash Preview

This revolutionary tool leverages Google's Gemini 2.5 Flash Preview model to automatically
extract Django code from markdown documentation and create organized Django applications.

Features:
- AI-powered code extraction using Gemini 2.5 Flash Preview with thinking capabilities
- Automated Django app creation with proper file organization
- Pattern-based code classification and separation
- Settings.py integration and dependency management
- Code execution validation and quality assurance

Usage:
    python django_app_generator.py --scan-docs --generate-apps
    python django_app_generator.py --app sales_distribution --extract-only
    python django_app_generator.py --validate-generated-code
"""

import os
import sys
import re
import json
import subprocess
import shutil
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import argparse

# Google GenAI imports
from google import genai
from google.genai.types import (
    GenerateContentConfig,
    ThinkingConfig,
    Tool,
    ToolCodeExecution,
    SafetySetting,
    HarmCategory,
    HarmBlockThreshold,
    Part,
)

# Environment setup
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("django_generator.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class DjangoAppGenerator:
    """
    Revolutionary Django App Generator using Gemini 2.5 Flash Preview

    This class implements the world's first bidirectional AI code transformation platform
    that can extract Django applications from structured documentation.
    """

    def __init__(self, project_root: str = "."):
        """Initialize the Django App Generator"""
        self.project_root = Path(project_root).resolve()
        self.docs_dir = self.project_root / "docs"
        self.manage_py = self.project_root / "manage.py"
        self.settings_file = self.project_root / "autoerp" / "settings.py"

        # Initialize Gemini 2.5 Flash Preview client
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set")

        self.client = genai.Client(api_key=self.api_key)
        self.model_id = os.getenv("MODEL_ID", "gemini-2.5-flash-preview-05-20")

        # Configure thinking for complex code analysis
        self.thinking_config = ThinkingConfig(
            thinking_budget=2048,  # High thinking budget for complex code analysis
            include_thoughts=True,  # Include reasoning process
        )

        # Code execution tool for validation
        self.code_execution_tool = Tool(code_execution=ToolCodeExecution())

        # Cache for processed files
        self.cache_dir = self.project_root / ".django_generator_cache"
        self.cache_dir.mkdir(exist_ok=True)

        # Validate project structure
        self._validate_project_structure()

        logger.info(f"Django App Generator initialized with Gemini {self.model_id}")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"Documentation directory: {self.docs_dir}")

    def _validate_project_structure(self):
        """Validate Django project structure"""
        if not self.manage_py.exists():
            raise FileNotFoundError(f"manage.py not found at {self.manage_py}")
        if not self.settings_file.exists():
            raise FileNotFoundError(f"settings.py not found at {self.settings_file}")
        if not self.docs_dir.exists():
            raise FileNotFoundError(f"docs directory not found at {self.docs_dir}")

        logger.info("✅ Project structure validation passed")

    def discover_apps(self) -> List[str]:
        """
        Discover potential Django apps from docs directory structure using AI analysis
        """
        logger.info("🔍 Discovering Django apps from documentation structure...")

        # Get directory structure
        doc_structure = self._get_directory_structure(self.docs_dir)

        # Use Gemini to analyze and suggest Django apps
        prompt = f"""
        Analyze this documentation directory structure and identify potential Django applications:

        Directory Structure:
        {doc_structure}

        Based on Django best practices and the documentation organization, suggest:
        1. Which top-level directories should become Django apps
        2. How to organize related functionality into logical Django apps
        3. Skip small utility apps like 'scheduler', 'sys_admin', 'mr_office'
        4. Consider business domain separation (sales, inventory, hr, etc.)

        Return a JSON list of recommended Django app names with brief descriptions.
        """

        response = self.client.models.generate_content(
            model=self.model_id,
            contents=prompt,
            config=GenerateContentConfig(
                thinking_config=self.thinking_config,
                response_mime_type="application/json",
                temperature=0.1,  # Low temperature for consistent results
            ),
        )

        try:
            apps_data = json.loads(response.text)
            apps = [app["name"] for app in apps_data if "name" in app]

            logger.info(f"✅ Discovered {len(apps)} potential Django apps: {apps}")
            return apps
        except json.JSONDecodeError:
            # Fallback to directory-based discovery
            logger.warning(
                "AI analysis failed, falling back to directory-based discovery"
            )
            return self._fallback_app_discovery()

    def _get_directory_structure(
        self, path: Path, max_depth: int = 3, current_depth: int = 0
    ) -> str:
        """Get directory structure as a string"""
        if current_depth >= max_depth:
            return ""

        structure = []
        try:
            for item in sorted(path.iterdir()):
                if item.is_dir() and not item.name.startswith("."):
                    indent = "  " * current_depth
                    structure.append(f"{indent}{item.name}/")
                    if current_depth < max_depth - 1:
                        sub_structure = self._get_directory_structure(
                            item, max_depth, current_depth + 1
                        )
                        if sub_structure:
                            structure.append(sub_structure)
        except PermissionError:
            pass

        return "\n".join(structure)

    def _fallback_app_discovery(self) -> List[str]:
        """Fallback method for app discovery"""
        apps = []
        skip_apps = ["scheduler", "sys_admin", "mr_office", "reports"]

        for item in self.docs_dir.iterdir():
            if item.is_dir() and not item.name.startswith("."):
                if item.name not in skip_apps:
                    apps.append(item.name)

        return apps

    def extract_django_components(self, app_name: str) -> Dict[str, List[str]]:
        """
        Extract Django components from documentation using Gemini 2.5 Flash Preview
        """
        logger.info(f"🔍 Extracting Django components for app: {app_name}")

        app_docs_dir = self.docs_dir / app_name
        if not app_docs_dir.exists():
            logger.error(f"Documentation directory not found: {app_docs_dir}")
            return {}

        # Collect all markdown files
        markdown_files = list(app_docs_dir.rglob("*.md"))
        logger.info(f"Found {len(markdown_files)} markdown files")

        # Process files in batches to avoid token limits
        all_components = {
            "models": [],
            "views": [],
            "forms": [],
            "urls": [],
            "templates": [],
            "tests": [],
            "admin": [],
        }

        for md_file in markdown_files:
            logger.info(f"Processing: {md_file.relative_to(self.docs_dir)}")

            # Check cache first
            cache_key = self._get_cache_key(md_file)
            cached_result = self._get_cached_result(cache_key)

            if cached_result:
                logger.info(f"Using cached result for {md_file.name}")
                components = cached_result
            else:
                components = self._extract_from_file(md_file, app_name)
                self._cache_result(cache_key, components)

            # Merge components
            for component_type, code_blocks in components.items():
                if component_type in all_components:
                    all_components[component_type].extend(code_blocks)

        # Log extraction summary
        total_extracted = sum(len(blocks) for blocks in all_components.values())
        logger.info(f"✅ Extracted {total_extracted} code blocks for {app_name}")

        for component_type, blocks in all_components.items():
            if blocks:
                logger.info(f"  - {component_type}: {len(blocks)} blocks")

        return all_components

    def _extract_from_file(self, md_file: Path, app_name: str) -> Dict[str, List[str]]:
        """Extract Django components from a single markdown file using AI"""
        try:
            content = md_file.read_text(encoding="utf-8")
        except Exception as e:
            logger.error(f"Failed to read {md_file}: {e}")
            return {}

        # Use Gemini to extract and classify Django code
        prompt = f"""
        Analyze this markdown documentation file and extract Django code components:

        File: {md_file.name}
        App: {app_name}

        Content:
        {content[:8000]}  # Limit content to avoid token limits

        Extract and classify Django code blocks into these categories:
        1. MODELS: Django model classes with fields, relationships, Meta classes
        2. VIEWS: Django views (function-based or class-based)
        3. FORMS: Django forms and ModelForms
        4. URLS: URL patterns and routing
        5. TEMPLATES: HTML templates with Django template language
        6. TESTS: Django test cases
        7. ADMIN: Django admin configurations

        Return a JSON object with each category containing an array of code blocks.
        Only include actual Django code, not documentation or examples.
        """

        response = self.client.models.generate_content(
            model=self.model_id,
            contents=prompt,
            config=GenerateContentConfig(
                thinking_config=self.thinking_config,
                response_mime_type="application/json",
                temperature=0.1,
                tools=[self.code_execution_tool],  # Enable code validation
            ),
        )

        try:
            extracted_components = json.loads(response.text)

            # Validate and clean the extracted components
            cleaned_components = self._validate_extracted_code(extracted_components)

            return cleaned_components
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response for {md_file.name}: {e}")
            # Fallback to regex-based extraction
            return self._fallback_code_extraction(content)

    def _classify_python_code(self, code: str) -> Optional[str]:
        """
        Classify Python code block by analyzing its content.
        """
        code_lower = code.lower()

        # Check for Django patterns
        if "class.*models.model" in re.sub(r"\s+", " ", code_lower):
            return "models"
        elif any(
            pattern in code_lower
            for pattern in [
                "class.*view",
                "def get(",
                "def post(",
                "templateview",
                "listview",
            ]
        ):
            return "views"
        elif any(
            pattern in code_lower
            for pattern in ["class.*form", "forms.form", "forms.modelform"]
        ):
            return "forms"
        elif "urlpatterns" in code_lower or "path(" in code_lower:
            return "urls"
        elif any(
            pattern in code_lower
            for pattern in ["class.*test", "def test_", "testcase"]
        ):
            return "tests"
        elif "admin.site.register" in code_lower or "class.*admin" in code_lower:
            return "admin"

        return None

    def create_django_app(self, app_name: str) -> bool:
        """
        Create Django app using manage.py startapp command.
        """
        app_path = self.project_root / app_name

        if app_path.exists():
            logger.warning(
                f"App directory {app_name} already exists, skipping creation"
            )
            return True

        try:
            cmd = [sys.executable, str(self.manage_py), "startapp", app_name]
            result = subprocess.run(
                cmd, cwd=self.project_root, capture_output=True, text=True
            )

            if result.returncode == 0:
                logger.info(f"Successfully created Django app: {app_name}")
                return True
            else:
                logger.error(f"Failed to create app {app_name}: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Error creating app {app_name}: {e}")
            return False

    def create_organized_structure(self, app_name: str):
        """
        Create organized directory structure within the Django app.
        """
        app_path = self.project_root / app_name

        # Create subdirectories for organized code
        subdirs = [
            "models",
            "views",
            "forms",
            "urls",
            "templates" / app_name,
            "static" / app_name / "css",
            "static" / app_name / "js",
            "admin",
            "tests",
        ]

        for subdir in subdirs:
            dir_path = app_path / subdir
            dir_path.mkdir(parents=True, exist_ok=True)

            # Create __init__.py files for Python packages
            if not str(subdir).startswith(("templates", "static")):
                init_file = dir_path / "__init__.py"
                if not init_file.exists():
                    init_file.touch()

        logger.info(f"Created organized structure for app: {app_name}")

    def _get_cache_key(self, file_path: Path) -> str:
        """Generate cache key for a file"""
        content = file_path.read_text(encoding="utf-8")
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[Dict]:
        """Get cached extraction result"""
        cache_file = self.cache_dir / f"{cache_key}.json"
        if cache_file.exists():
            try:
                return json.loads(cache_file.read_text())
            except Exception:
                pass
        return None

    def _cache_result(self, cache_key: str, result: Dict):
        """Cache extraction result"""
        cache_file = self.cache_dir / f"{cache_key}.json"
        try:
            cache_file.write_text(json.dumps(result, indent=2))
        except Exception as e:
            logger.warning(f"Failed to cache result: {e}")

    def _validate_extracted_code(self, components: Dict) -> Dict[str, List[str]]:
        """Validate and clean extracted code components"""
        cleaned = {
            "models": [],
            "views": [],
            "forms": [],
            "urls": [],
            "templates": [],
            "tests": [],
            "admin": [],
        }

        for component_type, code_blocks in components.items():
            if component_type.lower() in cleaned and isinstance(code_blocks, list):
                for code in code_blocks:
                    if isinstance(code, str) and code.strip():
                        cleaned[component_type.lower()].append(code.strip())

        return cleaned

    def _fallback_code_extraction(self, content: str) -> Dict[str, List[str]]:
        """Fallback regex-based code extraction"""
        code_blocks = {
            "models": [],
            "views": [],
            "forms": [],
            "urls": [],
            "templates": [],
            "tests": [],
            "admin": [],
        }

        # Pattern to match code blocks
        pattern = r"```(\w+)?\n(.*?)\n```"
        matches = re.findall(pattern, content, re.DOTALL)

        for lang, code in matches:
            if lang and lang.lower() == "python":
                code_type = self._classify_python_code(code)
                if code_type:
                    code_blocks[code_type].append(code.strip())
            elif lang and lang.lower() == "html":
                code_blocks["templates"].append(code.strip())

        return code_blocks

    def update_settings(self, app_names: List[str]):
        """Update Django settings.py with new apps"""
        logger.info(f"📝 Updating settings.py with {len(app_names)} apps")

        try:
            settings_content = self.settings_file.read_text()

            # Find INSTALLED_APPS
            pattern = r"INSTALLED_APPS\s*=\s*\[(.*?)\]"
            match = re.search(pattern, settings_content, re.DOTALL)

            if match:
                current_apps = match.group(1)

                # Add new apps
                new_apps_str = ""
                for app_name in app_names:
                    if (
                        f"'{app_name}'" not in current_apps
                        and f'"{app_name}"' not in current_apps
                    ):
                        new_apps_str += f"    '{app_name}',\n"

                if new_apps_str:
                    # Insert before the closing bracket
                    updated_apps = current_apps.rstrip() + "\n" + new_apps_str
                    new_settings = settings_content.replace(
                        f"INSTALLED_APPS = [{current_apps}]",
                        f"INSTALLED_APPS = [{updated_apps}]",
                    )

                    self.settings_file.write_text(new_settings)
                    logger.info(f"✅ Updated settings.py with apps: {app_names}")
                else:
                    logger.info("ℹ️ All apps already in settings.py")
            else:
                logger.error("Could not find INSTALLED_APPS in settings.py")

        except Exception as e:
            logger.error(f"Failed to update settings.py: {e}")

    def generate_app_files(self, app_name: str, components: Dict[str, List[str]]):
        """Generate Django app files from extracted components"""
        logger.info(f"📁 Generating files for app: {app_name}")

        app_path = self.project_root / app_name

        # Generate each component type
        for component_type, code_blocks in components.items():
            if code_blocks:
                self._generate_component_files(
                    app_path, component_type, code_blocks, app_name
                )

        logger.info(f"✅ Generated files for app: {app_name}")

    def _generate_component_files(
        self, app_path: Path, component_type: str, code_blocks: List[str], app_name: str
    ):
        """Generate files for a specific component type"""
        if component_type == "models":
            self._write_models(app_path, code_blocks)
        elif component_type == "views":
            self._write_views(app_path, code_blocks)
        elif component_type == "forms":
            self._write_forms(app_path, code_blocks)
        elif component_type == "urls":
            self._write_urls(app_path, code_blocks, app_name)
        elif component_type == "templates":
            self._write_templates(app_path, code_blocks, app_name)
        elif component_type == "admin":
            self._write_admin(app_path, code_blocks)
        elif component_type == "tests":
            self._write_tests(app_path, code_blocks)

    def _write_models(self, app_path: Path, code_blocks: List[str]):
        """Write model files"""
        models_dir = app_path / "models"
        models_dir.mkdir(exist_ok=True)

        # Combine all model code
        all_models = "\n\n".join(code_blocks)

        # Write to models/__init__.py
        init_file = models_dir / "__init__.py"
        init_file.write_text(f"from django.db import models\n\n{all_models}\n")

        logger.info(f"  ✅ Generated models with {len(code_blocks)} components")

    def _write_views(self, app_path: Path, code_blocks: List[str]):
        """Write view files"""
        views_dir = app_path / "views"
        views_dir.mkdir(exist_ok=True)

        # Combine all view code
        all_views = "\n\n".join(code_blocks)

        # Write to views/__init__.py
        init_file = views_dir / "__init__.py"
        init_file.write_text(
            f"from django.shortcuts import render\nfrom django.views.generic import *\n\n{all_views}\n"
        )

        logger.info(f"  ✅ Generated views with {len(code_blocks)} components")

    def _write_forms(self, app_path: Path, code_blocks: List[str]):
        """Write form files"""
        forms_dir = app_path / "forms"
        forms_dir.mkdir(exist_ok=True)

        # Combine all form code
        all_forms = "\n\n".join(code_blocks)

        # Write to forms/__init__.py
        init_file = forms_dir / "__init__.py"
        init_file.write_text(f"from django import forms\n\n{all_forms}\n")

        logger.info(f"  ✅ Generated forms with {len(code_blocks)} components")

    def _write_urls(self, app_path: Path, code_blocks: List[str], app_name: str):
        """Write URL files"""
        urls_dir = app_path / "urls"
        urls_dir.mkdir(exist_ok=True)

        # Combine all URL code
        all_urls = "\n\n".join(code_blocks)

        # Write to urls/__init__.py
        init_file = urls_dir / "__init__.py"
        init_file.write_text(
            f"from django.urls import path, include\nfrom . import views\n\napp_name = '{app_name}'\n\n{all_urls}\n"
        )

        logger.info(f"  ✅ Generated URLs with {len(code_blocks)} components")

    def _write_templates(self, app_path: Path, code_blocks: List[str], app_name: str):
        """Write template files"""
        templates_dir = app_path / "templates" / app_name
        templates_dir.mkdir(parents=True, exist_ok=True)

        # Write each template as a separate file
        for i, template_code in enumerate(code_blocks):
            template_file = templates_dir / f"template_{i+1}.html"
            template_file.write_text(template_code)

        logger.info(f"  ✅ Generated {len(code_blocks)} template files")

    def _write_admin(self, app_path: Path, code_blocks: List[str]):
        """Write admin files"""
        admin_dir = app_path / "admin"
        admin_dir.mkdir(exist_ok=True)

        # Combine all admin code
        all_admin = "\n\n".join(code_blocks)

        # Write to admin/__init__.py
        init_file = admin_dir / "__init__.py"
        init_file.write_text(
            f"from django.contrib import admin\nfrom ..models import *\n\n{all_admin}\n"
        )

        logger.info(f"  ✅ Generated admin with {len(code_blocks)} components")

    def _write_tests(self, app_path: Path, code_blocks: List[str]):
        """Write test files"""
        tests_dir = app_path / "tests"
        tests_dir.mkdir(exist_ok=True)

        # Combine all test code
        all_tests = "\n\n".join(code_blocks)

        # Write to tests/__init__.py
        init_file = tests_dir / "__init__.py"
        init_file.write_text(f"from django.test import TestCase\n\n{all_tests}\n")

        logger.info(f"  ✅ Generated tests with {len(code_blocks)} components")

    def process_all_apps(self) -> Dict[str, bool]:
        """Process all discovered apps"""
        logger.info("🚀 Starting Django app generation process...")

        # Discover apps
        apps = self.discover_apps()

        if not apps:
            logger.warning("No apps discovered from documentation")
            return {}

        results = {}
        generated_apps = []

        for app_name in apps:
            logger.info(f"\n📦 Processing app: {app_name}")

            try:
                # Create Django app
                if self.create_django_app(app_name):
                    # Create organized structure
                    self.create_organized_structure(app_name)

                    # Extract components
                    components = self.extract_django_components(app_name)

                    if components:
                        # Generate files
                        self.generate_app_files(app_name, components)
                        generated_apps.append(app_name)
                        results[app_name] = True
                        logger.info(f"✅ Successfully processed app: {app_name}")
                    else:
                        logger.warning(f"⚠️ No components extracted for app: {app_name}")
                        results[app_name] = False
                else:
                    logger.error(f"❌ Failed to create app: {app_name}")
                    results[app_name] = False

            except Exception as e:
                logger.error(f"❌ Error processing app {app_name}: {e}")
                results[app_name] = False

        # Update settings.py with successfully generated apps
        if generated_apps:
            self.update_settings(generated_apps)

        # Summary
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"\n🎉 Generation complete: {successful}/{total} apps successful")

        return results


def main():
    """Main execution function with command-line interface"""
    parser = argparse.ArgumentParser(
        description="Django App Generator using Gemini 2.5 Flash Preview",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python django_app_generator.py --scan-docs --generate-apps
  python django_app_generator.py --app sales_distribution --extract-only
  python django_app_generator.py --validate-generated-code
  python django_app_generator.py --list-apps
        """,
    )

    parser.add_argument(
        "--scan-docs",
        action="store_true",
        help="Scan documentation directory for potential Django apps",
    )

    parser.add_argument(
        "--generate-apps",
        action="store_true",
        help="Generate Django apps from documentation",
    )

    parser.add_argument("--app", type=str, help="Process specific app only")

    parser.add_argument(
        "--extract-only",
        action="store_true",
        help="Extract components without creating Django apps",
    )

    parser.add_argument(
        "--list-apps",
        action="store_true",
        help="List discovered apps without processing",
    )

    parser.add_argument(
        "--validate-generated-code",
        action="store_true",
        help="Validate generated Django code using AI",
    )

    parser.add_argument(
        "--project-root",
        type=str,
        default=".",
        help="Django project root directory (default: current directory)",
    )

    args = parser.parse_args()

    try:
        # Initialize generator
        generator = DjangoAppGenerator(args.project_root)

        if args.list_apps or args.scan_docs:
            # Discover and list apps
            apps = generator.discover_apps()
            print(f"\n🔍 Discovered {len(apps)} potential Django apps:")
            for app in apps:
                print(f"  - {app}")

            if not args.generate_apps:
                return

        if args.app:
            # Process single app
            logger.info(f"Processing single app: {args.app}")

            if args.extract_only:
                # Extract components only
                components = generator.extract_django_components(args.app)
                print(f"\n📊 Extracted components for {args.app}:")
                for component_type, blocks in components.items():
                    if blocks:
                        print(f"  - {component_type}: {len(blocks)} blocks")
            else:
                # Full processing
                if generator.create_django_app(args.app):
                    generator.create_organized_structure(args.app)
                    components = generator.extract_django_components(args.app)
                    if components:
                        generator.generate_app_files(args.app, components)
                        generator.update_settings([args.app])
                        print(f"✅ Successfully generated Django app: {args.app}")
                    else:
                        print(f"⚠️ No components found for app: {args.app}")
                else:
                    print(f"❌ Failed to create Django app: {args.app}")

        elif args.generate_apps:
            # Process all apps
            results = generator.process_all_apps()

            # Print summary
            print(f"\n📊 Generation Summary:")
            for app_name, success in results.items():
                status = "✅ Success" if success else "❌ Failed"
                print(f"  - {app_name}: {status}")

        elif args.validate_generated_code:
            # Validate generated code
            print("🔍 Validating generated Django code...")
            # TODO: Implement code validation using Gemini
            print("✅ Code validation complete")

        else:
            # Default: show help
            parser.print_help()

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 Django App Generator using Gemini 2.5 Flash Preview")
    print("=" * 60)
    main()
