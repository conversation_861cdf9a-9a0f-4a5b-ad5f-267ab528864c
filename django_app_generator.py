#!/usr/bin/env python3
"""
Django App Generator from Documentation

This tool extracts Django code from markdown documentation files and creates
organized Django applications with proper file structure.

Usage:
    python django_app_generator.py

Features:
- Scans docs/ directory for Django app documentation
- Extracts models, views, forms, URLs, templates from markdown files
- Creates Django apps using manage.py startapp
- Organizes code into proper Django file structure
- Updates settings.py with new apps
"""

import os
import re
import sys
import subprocess
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DjangoAppGenerator:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.docs_dir = self.project_root / "docs"
        self.manage_py = self.project_root / "manage.py"
        self.settings_file = self.project_root / "autoerp" / "settings.py"
        
        # Validate project structure
        if not self.manage_py.exists():
            raise FileNotFoundError(f"manage.py not found at {self.manage_py}")
        if not self.settings_file.exists():
            raise FileNotFoundError(f"settings.py not found at {self.settings_file}")
        if not self.docs_dir.exists():
            raise FileNotFoundError(f"docs directory not found at {self.docs_dir}")
    
    def discover_apps(self) -> List[str]:
        """
        Discover potential Django apps from docs directory structure.
        Returns list of app names based on top-level docs folders.
        """
        apps = []
        for item in self.docs_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # Skip small apps as per user preferences
                if item.name in ['scheduler', 'sys_admin', 'mr_office']:
                    logger.info(f"Skipping small app: {item.name}")
                    continue
                apps.append(item.name)
        
        logger.info(f"Discovered potential apps: {apps}")
        return apps
    
    def extract_code_blocks(self, markdown_content: str) -> Dict[str, List[str]]:
        """
        Extract Django code blocks from markdown content.
        Returns dictionary with code type as key and list of code blocks as value.
        """
        code_blocks = {
            'models': [],
            'views': [],
            'forms': [],
            'urls': [],
            'templates': [],
            'tests': [],
            'admin': []
        }
        
        # Pattern to match code blocks with language specification
        pattern = r'```(\w+)?\n(.*?)\n```'
        matches = re.findall(pattern, markdown_content, re.DOTALL)
        
        for lang, code in matches:
            if lang.lower() == 'python':
                # Analyze Python code to determine type
                code_type = self._classify_python_code(code)
                if code_type:
                    code_blocks[code_type].append(code.strip())
            elif lang.lower() == 'html':
                code_blocks['templates'].append(code.strip())
        
        return code_blocks
    
    def _classify_python_code(self, code: str) -> Optional[str]:
        """
        Classify Python code block by analyzing its content.
        """
        code_lower = code.lower()
        
        # Check for Django patterns
        if 'class.*models.model' in re.sub(r'\s+', ' ', code_lower):
            return 'models'
        elif any(pattern in code_lower for pattern in ['class.*view', 'def get(', 'def post(', 'templateview', 'listview']):
            return 'views'
        elif any(pattern in code_lower for pattern in ['class.*form', 'forms.form', 'forms.modelform']):
            return 'forms'
        elif 'urlpatterns' in code_lower or 'path(' in code_lower:
            return 'urls'
        elif any(pattern in code_lower for pattern in ['class.*test', 'def test_', 'testcase']):
            return 'tests'
        elif 'admin.site.register' in code_lower or 'class.*admin' in code_lower:
            return 'admin'
        
        return None
    
    def create_django_app(self, app_name: str) -> bool:
        """
        Create Django app using manage.py startapp command.
        """
        app_path = self.project_root / app_name
        
        if app_path.exists():
            logger.warning(f"App directory {app_name} already exists, skipping creation")
            return True
        
        try:
            cmd = [sys.executable, str(self.manage_py), 'startapp', app_name]
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Successfully created Django app: {app_name}")
                return True
            else:
                logger.error(f"Failed to create app {app_name}: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Error creating app {app_name}: {e}")
            return False
    
    def create_organized_structure(self, app_name: str):
        """
        Create organized directory structure within the Django app.
        """
        app_path = self.project_root / app_name
        
        # Create subdirectories for organized code
        subdirs = [
            'models',
            'views', 
            'forms',
            'urls',
            'templates' / app_name,
            'static' / app_name / 'css',
            'static' / app_name / 'js',
            'admin',
            'tests'
        ]
        
        for subdir in subdirs:
            dir_path = app_path / subdir
            dir_path.mkdir(parents=True, exist_ok=True)
            
            # Create __init__.py files for Python packages
            if not str(subdir).startswith(('templates', 'static')):
                init_file = dir_path / '__init__.py'
                if not init_file.exists():
                    init_file.touch()
        
        logger.info(f"Created organized structure for app: {app_name}")
