from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib import messages
from .models import *


def inventory_dashboard(request):
    """Dashboard view for inventory app"""
    context = {
        'title': 'Inventory Dashboard',
        'app_name': 'inventory'
    }
    return render(request, 'inventory/dashboard.html', context)

class CategoryListView(ListView):
    model = Category
    template_name = 'inventory/category_list.html'
    context_object_name = 'categories'
    paginate_by = 20

class CategoryCreateView(CreateView):
    model = Category
    template_name = 'inventory/category_form.html'
    fields = ['name', 'description', 'is_active']
    success_url = '/inventory/categories/'
