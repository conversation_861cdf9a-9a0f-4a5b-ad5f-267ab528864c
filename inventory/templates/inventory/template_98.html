{# inventory/templates/inventory/workorder/_custom_search_tab.html #}
{# This partial template represents the "WIP" (Custom Search) tab content #}
<div id="customSearch_content"> {# This div is loaded into #tabContent #}
    <form hx-post="{% url 'inventory:workorder_custom_search' %}" 
          hx-target="this" {# Target self to display form errors back in the same place #}
          hx-swap="outerHTML"> {# Replace the entire form with the updated version (including errors) #}
        {% csrf_token %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end mb-6">
            <div>
                <label for="{{ custom_search_form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date:</label>
                {{ custom_search_form.from_date }}
                {% if custom_search_form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ custom_search_form.from_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ custom_search_form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date:</label>
                {{ custom_search_form.to_date }}
                {% if custom_search_form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ custom_search_form.to_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ custom_search_form.wo_no_report.id_for_label }}" class="block text-sm font-medium text-gray-700">WONo:</label>
                {{ custom_search_form.wo_no_report }}
            </div>
            <div class="flex items-center">
                <label for="{{ custom_search_form.overheads.id_for_label }}" class="block text-sm font-medium text-gray-700 mr-2">Overheads:</label>
                <div class="flex-grow">
                    {{ custom_search_form.overheads }}
                    {% if custom_search_form.overheads.errors %}<p class="text-red-500 text-xs mt-1">{{ custom_search_form.overheads.errors }}</p>{% endif %}
                </div>
                <span class="ml-1 text-gray-700">%</span>
            </div>
            <div>
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </div>
        {# Display non-field errors if any #}
        {% if custom_search_form.non_field_errors %}
            <div class="text-red-500 text-sm mb-4">
                {% for error in custom_search_form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
    </form>
    
    <div class="mt-6">
        {# The iframe that will display the report, its src updated by HTMX #}
        <iframe id="reportIframe" width="100%" height="410px" frameborder="0" scrolling="auto" class="border border-gray-300 rounded-md shadow-inner"></iframe>
    </div>
</div>