<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">
        Are you sure you want to delete the report "<strong class="font-medium text-red-600">{{ report.report_name }}</strong>"? This action cannot be undone.
    </p>
    <form hx-post="{% url 'report_delete' report.pk %}" 
          hx-swap="none" 
          hx-indicator="#delete-spinner"
          hx-on::after.response="if(event.detail.xhr.status === 204) {
            document.querySelector('#modal')._x_dataStack[0].showModal = false; 
            // Re-hide modal after successful HTMX delete
          }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click document.querySelector('#modal')._x_dataStack[0].showModal = false
                   then remove children from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
                <i id="delete-spinner" class="fas fa-spinner fa-spin ml-2 htmx-indicator"></i>
            </button>
        </div>
    </form>
</div>