{# inventory/templates/inventory/workorder/_workorder_table.html #}
{# This partial template contains only the Work Order table, designed to be loaded by HTMX #}
<table id="workOrderTable" class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Report Type</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if workorders %}
            {% for wo in workorders %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ wo.wono }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ wo.sysdate|date:"d-m-Y" }}</td> {# Format date to match ASP.NET dd-MM-yyyy #}
                <td class="py-3 px-6 text-sm text-gray-900">{{ wo.prjtitle }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900 text-center">
                    {# Dropdown for report type, mimicking drpIssueShortage #}
                    <select id="drpIssueShortage-{{ wo.id }}" class="form-select block w-full px-2 py-1 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="0" selected>Transaction wise Issue</option>
                        <option value="1">Issue List</option>
                        <option value="2">Shortage List</option>
                    </select>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-center">
                    {# Dry Run button (links to assembly page) #}
                    <a href="{{ wo.get_assembly_link }}" 
                       class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 mr-2"
                       target="_blank" rel="noopener noreferrer"> {# Opens in new tab #}
                        Dry Run
                    </a>
                    {# View button (triggers redirect to report based on dropdown) #}
                    <button type="button" 
                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            {# Dynamically construct URL based on selected dropdown value and open in new tab #}
                            hx-on:click="window.open(htmx.find('#drpIssueShortage-{{ wo.id }}').value == '0' ? '{{ wo.get_report_link(0) }}' : (htmx.find('#drpIssueShortage-{{ wo.id }}').value == '1' ? '{{ wo.get_report_link(1) }}' : '{{ wo.get_report_link(2) }}'), '_blank')">
                        View
                    </button>
                    {# Placeholder for Edit button (demonstrates HTMX modal for CRUD) #}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md shadow-sm text-xs transition duration-300 ease-in-out ml-2"
                        hx-get="{% url 'inventory:workorder_edit' wo.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        data-modal-title="Edit Work Order">
                        Edit
                    </button>
                    {# Placeholder for Delete button (demonstrates HTMX modal for CRUD) #}
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md shadow-sm text-xs transition duration-300 ease-in-out ml-1"
                        hx-get="{% url 'inventory:workorder_delete' wo.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        data-modal-title="Delete Work Order">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500 text-lg font-bold">No data found to display</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // DataTables initialization for the loaded table content
    // This script runs when the partial is loaded by HTMX
    $(document).ready(function() {
        // Only initialize if DataTables hasn't been initialized on this table yet
        // (The htmx:afterSwap listener in list.html should handle destroy/re-init)
        if (!$.fn.DataTable.isDataTable('#workOrderTable')) { 
            $('#workOrderTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "searching": true,
                "ordering": true,
                "paging": true
            });
        }
    });
</script>