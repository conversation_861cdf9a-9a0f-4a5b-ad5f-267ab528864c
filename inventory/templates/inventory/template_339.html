{# This partial is loaded into #search-code-container #}
{# It dynamically sets the visibility based on itemType and searchCode value handled by Alpine.js in parent template #}
<div>
    <label for="{{ search_code_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
    <div x-bind:data-search-code-field-value="itemType" x-bind:data-category-field-value="search_form.category.value">
        {{ search_code_field }}
        <input type="hidden" name="{{ search_code_field.name }}" x-model="searchCode" x-show="false"> {# Sync hidden input #}
    </div>
</div>

<div id="search-input-container">
    {# This is swapped when search_code changes #}
    {% include 'inventory_challan/partials/_search_input_field.html' with item_type=item_type category_id=category_id search_code=search_code_field.value search_term_text_field=search_term_text_field search_term_location_field=search_term_location_field search_form=search_form %}
</div>