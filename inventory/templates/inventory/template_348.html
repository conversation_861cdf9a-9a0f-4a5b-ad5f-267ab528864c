{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-4">Supplier Challan Details: {{ challan.sc_no }}</h2>
    <div class="bg-white shadow-md rounded-lg p-6">
        <p class="mb-2"><strong>ID:</strong> {{ challan.id }}</p>
        <p class="mb-2"><strong>Financial Year:</strong> {{ challan.fin_year }}</p>
        <p class="mb-2"><strong>Supplier Name:</strong> {{ challan.supplier_name }}</p>
        <p class="mb-2"><strong>Supplier Code:</strong> {{ challan.supplier_id }}</p>
        <p class="mb-2"><strong>Company ID:</strong> {{ challan.comp_id }}</p>
        {% if supplier_id_from_url %}
        <p class="mb-2 text-sm text-gray-600">Initial Supplier ID from URL: {{ supplier_id_from_url }}</p>
        {% endif %}
        <a href="{% url 'inventory:supplierchallan_list' %}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Back to List</a>
    </div>
</div>
{% endblock %}