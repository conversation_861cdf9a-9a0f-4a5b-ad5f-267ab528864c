{# inventory/templates/inventory/workorder/list.html #}
{% extends 'core/base.html' %} {# Assumes core/base.html provides the main layout, headers, footers, etc. #}
{% load crispy_forms_tags %} {# Optional: Using django-crispy-forms for cleaner form rendering #}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'searchWO' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">WIS Print</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'inventory:workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
            data-modal-title="Add New Work Order"> {# Data attribute for Alpine.js to set modal title #}
            Add New Work Order
        </button>
    </div>

    {# Tab Container structure using Alpine.js for active state and HTMX for loading content #}
    <div class="bg-white rounded-lg shadow-xl overflow-hidden">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-4" aria-label="Tabs">
                <a href="#" 
                   @click.prevent="activeTab = 'searchWO'" 
                   :class="{'border-indigo-500 text-indigo-600': activeTab === 'searchWO', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'searchWO'}" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-300 ease-in-out"
                   hx-get="{% url 'inventory:workorder_search_tab' %}" {# HTMX will load this tab content #}
                   hx-target="#tabContent"
                   hx-swap="innerHTML"
                   hx-trigger="click">
                    &nbsp;&nbsp; Search by WO No &nbsp;
                </a>
                <a href="#" 
                   @click.prevent="activeTab = 'customSearch'" 
                   :class="{'border-indigo-500 text-indigo-600': activeTab === 'customSearch', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'customSearch'}" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-300 ease-in-out"
                   hx-get="{% url 'inventory:workorder_custom_search_tab' %}" {# HTMX will load this tab content #}
                   hx-target="#tabContent"
                   hx-swap="innerHTML"
                   hx-trigger="click">
                    &nbsp;&nbsp;WIP&nbsp;&nbsp;
                </a>
            </nav>
        </div>

        <div id="tabContent" class="p-6">
            {# Initial content for the first tab (Search by WO No) is loaded here #}
            {# This is a direct include for the initial page load. Subsequent loads are via HTMX #}
            {% include 'inventory/workorder/_search_wo_tab.html' with filter_form=filter_form %}
        </div>
    </div>
</div>

<!-- Global Modal Structure for HTMX-loaded forms (Add, Edit, Delete) -->
<div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 hidden items-center justify-center z-50 transition-opacity ease-out duration-300 opacity-0"
     _="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from me then remove .scale-100 from #modalContent"
     x-data="{ title: '' }" {# Alpine.js state for modal title #}
     @set-modal-title.window="title = $event.detail.title"> {# Listen for custom event to set title #}
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full transform scale-95 transition-transform ease-out duration-300">
        <h3 class="text-xl font-semibold text-gray-800 mb-4" x-text="title"></h3> {# Display modal title #}
        <!-- Content loaded by HTMX will go here (forms, confirm_delete, etc.) -->
    </div>
</div>

{% endblock %}

{% block extra_js %}
{# Include DataTables CSS and JS (assuming CDN links are in base.html or explicitly added here for clarity) #}
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>

<script>
    // Global HTMX and Alpine.js interactions setup
    document.addEventListener('alpine:init', () => {
        // Alpine.js components specific to this page can be defined here if needed
    });

    // Listen for htmx:afterSwap to re-initialize DataTables when the table content is loaded/refreshed
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Check if the swapped content is our work order table container
        if (evt.detail.target.id === 'workOrderTable-container') {
            // Destroy existing DataTable instance before re-initializing if it exists
            if ($.fn.DataTable.isDataTable('#workOrderTable')) {
                $('#workOrderTable').DataTable().destroy();
            }
            // Initialize DataTables on the new table element
            $('#workOrderTable').DataTable({
                "pageLength": 15, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "searching": true, // Enable client-side search box
                "ordering": true,  // Enable sorting
                "paging": true     // Enable pagination
            });
        }
        // Listener for modal close on successful form submission (triggered by HX-Trigger header)
        if (evt.detail.trigger === 'refreshWorkOrderList') {
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            if (modal) {
                // Hide modal gracefully using Alpine.js and HTMX's _ attributes
                modal.classList.remove('flex');
                modal.classList.remove('opacity-100');
                modalContent.classList.remove('scale-100');
            }
        }
    });

    // Custom event listener for setting iframe src from HTMX (from CustomSearchReportView)
    document.body.addEventListener('setReportIframeSrc', function(evt) {
        const iframe = document.getElementById('reportIframe');
        if (iframe && evt.detail && evt.detail.value) {
            iframe.src = evt.detail.value;
            console.log("Iframe src set to:", evt.detail.value);
        }
    });

    // Event listener to set modal title based on data-modal-title attribute of the triggering button
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            const button = evt.detail.elt; // The element that triggered the hx-get
            const title = button.dataset.modalTitle;
            if (title) {
                // Update the Alpine.js data property for the modal title
                document.getElementById('modal')._x_dataStack[0].title = title;
            }
        }
    });
</script>
{% endblock %}