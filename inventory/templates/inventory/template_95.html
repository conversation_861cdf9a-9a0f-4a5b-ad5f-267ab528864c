{# inventory/templates/inventory/workordermaster/_workordermaster_table.html #}
{# This partial template is loaded via HTMX #}

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    {% if work_order_masters_data %}
    <table id="workOrderMasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rel Count</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rel Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rel Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Release By</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in work_order_masters_data %}
            {% with latest_release_info=obj.get_latest_release_info release_count=obj.get_release_count %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.wo_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.sys_date|date:"d/m/Y" }}</td>
                <td class="py-3 px-4 text-sm text-gray-900 min-w-48">{{ obj.task_project_title }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-center">
                    <button class="text-blue-600 hover:text-blue-900 font-medium"
                        hx-get="{% url 'inventory:workordermaster_release_details' wo_no=obj.wo_no %}"
                        hx-trigger="click">
                        {{ release_count }}
                    </button>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ latest_release_info.date|date:"d/m/Y"|default:"-" }}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ latest_release_info.time|default:"-" }}
                </td>
                <td class="py-3 px-4 text-sm text-gray-900 min-w-32">
                    {{ latest_release_info.by|default:"-" }}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                    {% if not obj.release_wis %}
                    <button 
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1.5 px-3 rounded text-xs transition duration-200 ease-in-out"
                        hx-post="{% url 'inventory:workordermaster_toggle_release' pk=obj.pk %}"
                        hx-confirm="Are you sure you want to RELEASE WO {{ obj.wo_no }}?"
                        hx-indicator=".htmx-indicator">
                        Release
                    </button>
                    {% else %}
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded text-xs transition duration-200 ease-in-out"
                        hx-post="{% url 'inventory:workordermaster_toggle_release' pk=obj.pk %}"
                        hx-confirm="Are you sure you want to STOP WO {{ obj.wo_no }}?"
                        hx-indicator=".htmx-indicator">
                        Stop
                    </button>
                    {% endif %}
                </td>
            </tr>
            {% endwith %}
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="py-10 text-center text-red-500 font-bold text-lg">
        No data found to display
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization issues
        if ($.fn.DataTable.isDataTable('#workOrderMasterTable')) {
            $('#workOrderMasterTable').DataTable().destroy();
        }
        $('#workOrderMasterTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>