{# This partial is loaded into #search-options-container #}
{# It dynamically sets the visibility based on itemType value handled by Alpine.js in parent template #}
<div>
    <label for="{{ category_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
    <div x-bind:data-category-field-value="itemType">
        {{ category_field }}
        <input type="hidden" name="{{ category_field.name }}" x-model="search_form.category.value" x-show="false"> {# Sync hidden input #}
    </div>
</div>

<div id="search-code-container">
    {# This is swapped when category changes #}
    {% include 'inventory_challan/partials/_search_code_options.html' with item_type=item_type category_id=category_field.value search_code_field=search_code_field search_term_text_field=search_term_text_field search_term_location_field=search_term_location_field search_form=search_form %}
</div>