<!-- This template is loaded into #autocomplete-results by HTMX -->
<ul>
    {% for suggestion in suggestions %}
        <li class="p-2 hover:bg-gray-100 cursor-pointer" 
            x-on:click="
                searchText = '{{ suggestion }}'; 
                selectedSupplierId = '{{ suggestion.split('[')[1]|cut:"]"|cut:" "|safe }}'; 
                showSuggestions = false; 
                document.getElementById('searchForm').requestSubmit(); // Trigger form submission
            ">
            {{ suggestion }}
        </li>
    {% empty %}
        {% if query %}
            <li class="p-3 text-gray-500">No suggestions found.</li>
        {% endif %}
    {% endfor %}
</ul>