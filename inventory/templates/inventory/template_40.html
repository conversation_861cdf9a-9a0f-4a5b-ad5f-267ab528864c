{% if report_items %}
<h3 class="text-xl font-semibold mb-4">{{ report_header }}</h3>
<table id="itemMovementReportTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Closing Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
        </tr>
    </thead>
    <tbody>
        {% for item in report_items %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.category }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.uom }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.gqn_qty|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.issue_qty|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.opening_qty|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.closing_qty|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.rate_reg|floatformat:2 }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists before re-initialization
    if ($.fn.DataTable.isDataTable('#itemMovementReportTable')) {
        $('#itemMovementReportTable').DataTable().destroy();
    }
    $('#itemMovementReportTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "order": [] // Disable initial sorting
    });
});
</script>
{% elif form_errors %}
    <div class="text-red-500">
        <p>Error validating report parameters:</p>
        <ul>
            {% for field, errors in form_errors.items %}
                <li>{{ field }}: {{ errors|join:", " }}</li>
            {% endfor %}
        </ul>
    </div>
{% else %}
    <p class="text-center text-gray-600">No report data found for the selected parameters. Please adjust your criteria and try again.</p>
{% endif %}