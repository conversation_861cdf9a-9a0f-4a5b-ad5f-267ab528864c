{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Supplier Challan - Print</h2>
        <!-- No "Add New" button here as per ASP.NET page's functionality -->
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <div class="flex items-center space-x-4" x-data="{
            showSuggestions: false,
            supplierSearchInput: '{{ search_form.supplier_search_value.value|default:"" }}',
            selectedSupplierId: '',
            selectSuggestion(value) {
                this.supplierSearchInput = value;
                this.showSuggestions = false;
                // Trigger form submission or direct table refresh
                document.getElementById('searchForm').requestSubmit();
            },
            hideSuggestionsDelayed() {
                // Delay hiding to allow click event to register
                setTimeout(() => { this.showSuggestions = false; }, 100);
            }
        }">
            <form id="searchForm" hx-get="{% url 'inventory:supplierchallan_table' %}" hx-target="#supplierChallanTable-container" hx-swap="innerHTML" class="flex-grow flex items-center space-x-4">
                {% csrf_token %}
                <label for="txtSearchValue" class="block text-sm font-medium text-gray-700 whitespace-nowrap">
                    Supplier Name:
                </label>
                <div class="relative flex-grow">
                    {{ search_form.supplier_search_value }}
                    <div id="supplier-suggestions" x-show="showSuggestions" class="absolute top-full left-0 w-full" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-95">
                        <!-- Autocomplete suggestions will be loaded here by HTMX -->
                    </div>
                </div>
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                    Search
                </button>
            </form>
        </div>
    </div>
    
    <div id="supplierChallanTable-container"
         hx-trigger="load, refreshSupplierChallanList from:body"
         hx-get="{% url 'inventory:supplierchallan_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Challans...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DataTables initialization (will be applied when _supplierchallan_table is loaded)
    document.addEventListener('DOMContentLoaded', function() {
        // This is a global function if needed, or put inside an Alpine component
    });

    // Ensure DataTables re-initializes on HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'supplierChallanTable-container') {
            const table = document.getElementById('supplierChallanTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 17, // Matches ASP.NET PageSize
                    "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 1, 2] }, // Disable sorting for SN, Select, Print Type
                        { "searchable": false, "targets": [0, 1, 2] } // Disable searching for SN, Select, Print Type
                    ]
                });
            }
        }
    });

    // Optional: Alpine.js for general page state if more complex UI is needed.
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component for the whole page required beyond the search field.
    });
</script>
{% endblock %}