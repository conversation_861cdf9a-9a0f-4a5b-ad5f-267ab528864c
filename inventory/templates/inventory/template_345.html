<div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
    {% if supplier_challans %}
    <table id="supplierChallanTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-3/100">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-6/100">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20/100">Print Type</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-12/100">Fin Yrs</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10/100">SCNo</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10/100">Code</th>
                <!-- 'Id' column is hidden -->
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in supplier_challans %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button
                        class="text-blue-600 hover:text-blue-900 font-medium"
                        onclick="window.location.href='{% url 'inventory:supplierchallan_print_details' pk=obj.pk %}?print_type=' + document.getElementById('drpPrintType_{{ obj.pk }}').value">
                        Select
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <select id="drpPrintType_{{ obj.pk }}" class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3">
                        <option value="0">ORIGINAL</option>
                        <option value="1">DUPLICATE</option>
                        <option value="2">TRIPLICATE</option>
                        <option value="3">ACKNOWLEDGEMENT</option>
                    </select>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.financial_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.challan_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier_name_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.supplier.id }}</td>
                <!-- Hidden Id can be accessed via obj.pk if needed, not displayed directly -->
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-10 font-medium text-lg text-maroon-600">
        <p>No data to display !</p>
    </div>
    {% endif %}
</div>

<!-- DataTables initialization script. This runs when the HTMX-swapped content is in the DOM. -->
<script>
    // Only initialize DataTables if it hasn't been initialized on this table yet
    // This is important because HTMX might swap content multiple times.
    $(document).ready(function() {
        const table = $('#supplierChallanTable');
        if (table.length && !$.fn.DataTable.isDataTable(table)) {
            table.DataTable({
                "pageLength": 17, // Matches ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, 1, 2] }, // Disable sorting for SN, Select, Print Type
                    { "searchable": false, "targets": [0, 1, 2] } // Disable searching for SN, Select, Print Type
                ],
                "language": { // Optional: Customize DataTables messages
                    "emptyTable": "No data to display !",
                    "zeroRecords": "No matching records found"
                }
            });
        }
    });
</script>