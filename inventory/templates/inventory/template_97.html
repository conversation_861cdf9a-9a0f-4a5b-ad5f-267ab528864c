{# inventory/templates/inventory/workorder/_search_wo_tab.html #}
{# This partial template represents the "Search by WO No" tab content #}
<div id="searchWO_content"> {# This div is loaded into #tabContent #}
    <div class="mb-4 flex flex-wrap items-end space-x-4">
        <div class="flex-grow">
            <label for="{{ filter_form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700">WO Category</label>
            {{ filter_form.wo_category }}
            {% if filter_form.wo_category.errors %}<p class="text-red-500 text-xs mt-1">{{ filter_form.wo_category.errors }}</p>{% endif %}
        </div>
        <div class="flex-grow">
            <label for="{{ filter_form.wo_number.id_for_label }}" class="block text-sm font-medium text-gray-700">WONo</label>
            {{ filter_form.wo_number }}
            {% if filter_form.wo_number.errors %}<p class="text-red-500 text-xs mt-1">{{ filter_form.wo_number.errors }}</p>{% endif %}
        </div>
        <div>
            <button type="button" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
                hx-get="{% url 'inventory:workorder_table' %}" {# HTMX will fetch the table content #}
                hx-target="#workOrderTable-container"
                hx-swap="innerHTML"
                hx-indicator="#wo-table-spinner"
                {# Pass current values of filter inputs as parameters to the HTMX request #}
                hx-vals='{"wo_number": document.getElementById("id_wo_number").value, "wo_category": document.getElementById("id_wo_category").value}'>
                Search
            </button>
        </div>
    </div>
    
    {# Loading spinner for HTMX requests #}
    <div id="wo-table-spinner" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading Work Orders...</p>
    </div>

    {# Container for the dynamically loaded Work Order table #}
    <div id="workOrderTable-container"
         {# HTMX triggers to load initial table and refresh it after CRUD operations #}
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'inventory:workorder_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#wo-table-spinner">
        <!-- The DataTables table content will be loaded here via HTMX -->
        <!-- Initial content will be the loading spinner, then replaced -->
    </div>
</div>