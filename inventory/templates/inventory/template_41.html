<!-- inventory_reports/stockledger/report.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold mb-6 text-gray-800">Stock Ledger Report</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form hx-post="{% url 'stockledger_report' %}" hx-target="#stockLedgerTable-container" hx-swap="innerHTML" hx-indicator="#loadingIndicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="{{ form.item_id.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.item_id.label }}
                    </label>
                    {{ form.item_id }}
                    {% if form.item_id.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.item_id.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.from_date.label }}
                    </label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.to_date.label }}
                    </label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Generate Report
                </button>
                <a href="{% url 'stockledger_report' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-6 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Reset
                </a>
            </div>
            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mt-4">
                    {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="loadingIndicator" class="text-center py-4 htmx-indicator">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading Report...</p>
    </div>

    <div id="stockLedgerTable-container" class="bg-white shadow-md rounded-lg p-6">
        <!-- The _stockledger_table.html partial will be loaded here via HTMX POST from the form -->
        <p class="text-center text-gray-500">Select an item and date range, then click "Generate Report" to view the ledger.</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}