<div x-data="challanForm" class="bg-white p-6 rounded-lg shadow-lg">
    <h3 class="text-xl font-semibold mb-4">Add New Customer Challan</h3>

    <form hx-post="{% url 'inventory_challan:challan_create' %}?WONo={{ work_order_no }}"
          hx-target="#item-table-container"
          hx-swap="innerHTML"
          hx-indicator="#loading-indicator">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 items-end">
            <div>
                <label for="{{ search_form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
                {{ search_form.type }}
                <input type="hidden" name="type" x-model="itemType"> {# Hidden input to ensure type is sent with other form fields #}
            </div>

            <div id="search-options-container" class="contents">
                {# Initial load of dynamic search options and input fields #}
                {% include 'inventory_challan/partials/_search_options.html' with item_type=search_form.type.value|default:"Select" category_field=search_form.category search_code_field=search_form.search_code search_term_text_field=search_form.search_term_text search_term_location_field=search_form.search_term_location search_form=search_form %}
            </div>

            <div>
                <button type="button" class="mt-6 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="{% url 'inventory_challan:item_table_partial' %}?WONo={{ work_order_no }}"
                        hx-target="#item-table-container"
                        hx-swap="innerHTML"
                        hx-include="closest form"
                        hx-indicator="#loading-indicator">
                    Search Items
                </button>
            </div>
        </div>

        <div class="relative h-96 overflow-auto border border-gray-200 rounded-md p-2 mb-6">
            <div id="loading-indicator" class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-700">Loading items...</p>
            </div>
            <div id="item-table-container"
                 hx-trigger="load, refreshItemList from:body"
                 hx-get="{% url 'inventory_challan:item_table_partial' %}?WONo={{ work_order_no }}"
                 hx-swap="innerHTML">
                <!-- DataTables will be loaded here via HTMX -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2">Loading item list...</p>
                </div>
            </div>
        </div>

        <div class="flex justify-center space-x-4">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                Submit Challan
            </button>
            <a href="{% url 'inventory_challan:customer_challan_new' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded">
                Cancel
            </a>
        </div>
    </form>
</div>