{# This partial is loaded into #search-input-container #}
{# It dynamically sets the visibility based on searchCode value handled by Alpine.js in parent template #}
<div x-bind:data-search-code-value="searchCode">
    <label for="{{ search_term_text_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Term</label>
    {{ search_term_text_field }}
    {{ search_term_location_field }}
</div>