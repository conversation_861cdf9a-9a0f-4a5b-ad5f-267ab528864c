{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Moving/Non-Moving Items Report</h2>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Report Parameters</h3>
        <form hx-get="{% url 'item_movement_report_table' %}" hx-target="#reportTableContainer" hx-indicator="#reportLoading" hx-swap="innerHTML">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.comp_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.comp_id.label }}</label>
                    {{ form.comp_id }}
                </div>
                <div>
                    <label for="{{ form.fin_year_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.fin_year_id.label }}</label>
                    {{ form.fin_year_id }}
                </div>
                <div>
                    <label for="{{ form.category_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.category_id.label }}</label>
                    {{ form.category_id }}
                </div>
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}</label>
                    {{ form.from_date }}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_date.label }}</label>
                    {{ form.to_date }}
                </div>
                <div>
                    <label for="{{ form.opening_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.opening_date.label }}</label>
                    {{ form.opening_date }}
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700">{{ form.rad_val.label }}</label>
                    {{ form.rad_val }}
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700">{{ form.rad_moving_item_val.label }}</label>
                    {{ form.rad_moving_item_val }}
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-4">
                <button type="reset" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Reset
                </button>
                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Generate Report
                </button>
            </div>
        </form>
    </div>

    <div id="reportLoading" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading report data...</p>
    </div>

    <div id="reportTableContainer" class="bg-white p-6 rounded-lg shadow-lg">
        <!-- Report table will be loaded here via HTMX -->
        <p class="text-center text-gray-600">Select parameters and click "Generate Report" to view results.</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}