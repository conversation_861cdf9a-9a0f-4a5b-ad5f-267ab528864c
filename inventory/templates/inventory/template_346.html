{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" 
     x-data="{ 
         showSuggestions: false, 
         searchText: '{{ search_form.search_supplier_display.value|default:"" }}', 
         selectedSupplierId: '{{ initial_supplier_id }}' 
     }">
    
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Supplier Challans</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'inventory:supplierchallan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Supplier Challan
        </button>
    </div>
    
    <div class="mb-4">
        <form id="searchForm" hx-trigger="submit" hx-swap="none">
            {{ search_form.search_supplier_display.label_tag }}
            {{ search_form.search_supplier_display }}
            <!-- Hidden field to store the actual supplier ID for submission -->
            <input type="hidden" name="supplier_id_actual" x-model="selectedSupplierId">

            <div id="autocomplete-results-container" class="relative">
                <div id="autocomplete-results" 
                     x-show="showSuggestions && searchText.length > 0" 
                     class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">
                    <!-- HTMX will load autocomplete suggestions here via hx-target #autocomplete-results -->
                </div>
            </div>
            
            <button type="submit" class="mt-4 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Search</button>
        </form>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table id="supplierChallanTable" class="min-w-full bg-white">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SC No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- DataTables will populate this tbody dynamically via AJAX -->
            </tbody>
        </table>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery and DataTables CDN links in base.html if not already there -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script>
    $(document).ready(function() {
        const supplierChallanTable = $('#supplierChallanTable').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "{% url 'inventory:supplierchallan_json_data' %}",
                "type": "GET",
                "data": function (d) {
                    // Pass current search form values to the server for filtering
                    d.search_supplier_display = $('#id_search_supplier_display').val();
                    d.supplier_id_actual = $('#id_supplier_id_actual').val();
                    // Pass initial supid from URL if present (mimics ASP.NET Page_Load)
                    const urlParams = new URLSearchParams(window.location.search);
                    if (urlParams.has('supid')) {
                        d.supid = urlParams.get('supid');
                    }
                }
            },
            "columns": [
                {"data": null, "orderable": false}, // SN (serial number generated on server)
                {"data": 1}, // Fin Yrs
                {"data": 2}, // SC No (HTML link)
                {"data": 3}, // Supplier Name
                {"data": 4}, // Code
                {"data": 5, "orderable": false}, // Actions (HTML buttons)
            ],
            "order": [[2, 'desc']], // Default order by SC No (column index 2) descending
            "pageLength": 17, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "language": {
                "emptyTable": "No data to display !",
                "loadingRecords": "Loading...",
                "processing": "<div class='inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500'></div><p class='mt-2'>Loading...</p>"
            }
        });

        // Event listener for HTMX to reload the DataTables
        document.body.addEventListener('refreshSupplierChallanList', function() {
            supplierChallanTable.ajax.reload(null, false); // Reload without resetting pagination
            $('#modal').removeClass('is-active').addClass('hidden'); // Close modal
        });

        // Trigger DataTables reload when the search form is submitted
        $('#searchForm').on('submit', function(e) {
            e.preventDefault(); // Prevent default form submission
            supplierChallanTable.ajax.reload(); // Reload DataTables data
        });
    });
</script>
<style>
    /* Basic styling for autocomplete suggestions */
    #autocomplete-results ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    #autocomplete-results li {
        padding: 8px 12px;
        cursor: pointer;
    }
    #autocomplete-results li:hover {
        background-color: #f0f0f0;
    }
</style>
{% endblock %}