<div id="search-fields-container" class="flex items-center space-x-4">
    {% if search_form.search_by.value == '0' %} {# Employee Name is selected #}
        <div class="flex items-center">
            <label for="{{ search_form.employee_name.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">{{ search_form.employee_name.label }}:</label>
            {{ search_form.employee_name }}
            {# Autocomplete suggestions container #}
            <div id="employee-suggestions-container" class="autocomplete-list"></div>
        </div>
    {% else %} {# MRN No is selected #}
        <div class="flex items-center">
            <label for="{{ search_form.mrn_no.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">{{ search_form.mrn_no.label }}:</label>
            {{ search_form.mrn_no }}
        </div>
    {% endif %}
</div>