{% load django_tables2 %}
{% comment %}
This template is swapped into `item-table-container`.
It includes the DataTables initialization script.
The formset `item_formset` contains ChallanItemForm instances for each item.
{% endcomment %}

<table id="itemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if items %}
            {% for form in item_formset %}
                <tr x-data="{ isChecked: {{ form.is_selected.value|yesno:'true,false' }} }">
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">
                        {{ form.is_selected }}
                        {{ form.item_id }} {# Hidden fields for item data #}
                        {{ form.item_code }}
                        {{ form.manf_desc }}
                        {{ form.uom_basic }}
                        {{ form.stock_qty }}
                        {{ form.location }}
                        {# Management form prefix for formset #}
                        <input type="hidden" name="{{ form.prefix }}-id" value="{{ form.instance.pk|default:'' }}">
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ form.initial.item_code }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ form.initial.manf_desc }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ form.initial.uom_basic }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ form.initial.stock_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ form.initial.location }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">
                        {{ form.challan_qty }}
                        {% if show_errors and form.challan_qty.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.challan_qty.errors|first }}</p>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            {# Management form for the formset #}
            {{ item_formset.management_form }}
        {% else %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-sm text-gray-500">No data to display!</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Only initialize DataTables if it hasn't been initialized already
    if (!$.fn.DataTable.isDataTable('#itemTable')) {
        $('#itemTable').DataTable({
            "pageLength": 15, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "responsive": true,
            "order": [], // Disable initial sorting
            "paging": true,
            "info": true,
            "searching": true // Enable searching (global filter)
        });
    }
});
</script>