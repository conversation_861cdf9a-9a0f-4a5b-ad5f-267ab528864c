from django import forms
from .models import Employee

class EmployeeForm(forms.ModelForm):
    class Meta:
        model = Employee
        fields = ['first_name', 'last_name', 'email', 'phone_number', 'hire_date', 'department']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'last_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'phone_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'hire_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'department': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
        
    def clean_email(self):
        email = self.cleaned_data['email']
        # Ensure email is unique, excluding the current instance during update
        if self.instance.pk:
            if Employee.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("An employee with this email already exists.")
        else:
            if Employee.objects.filter(email=email).exists():
                raise forms.ValidationError("An employee with this email already exists.")
        return email

from django import forms
from .models import Report

class ReportForm(forms.ModelForm):
    """
    Form for creating and updating Report instances.
    """
    generated_at = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        initial=forms.DateTimeInput().initial
    )

    class Meta:
        model = Report
        fields = ['name', 'generated_at', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(
                choices=[('Completed', 'Completed'), ('Pending', 'Pending'), ('Failed', 'Failed'), ('Processing', 'Processing')],
                attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
            ),
        }
        labels = {
            'name': 'Report Name',
            'generated_at': 'Generated Date',
            'status': 'Status',
        }
    
    def clean_name(self):
        """
        Custom validation for the report name.
        """
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise forms.ValidationError("Report Name must be at least 3 characters long.")
        return name

from django import forms
from .models import Employee

class EmployeeForm(forms.ModelForm):
    class Meta:
        model = Employee
        fields = ['employee_id', 'first_name', 'last_name', 'email', 'date_of_joining', 'is_active']
        widgets = {
            'employee_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Employee ID'}),
            'first_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'First Name'}),
            'last_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Last Name'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Email Address'}),
            'date_of_joining': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'employee_id': 'Employee ID',
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email',
            'date_of_joining': 'Date of Joining',
            'is_active': 'Is Active',
        }

    def clean_employee_id(self):
        employee_id = self.cleaned_data['employee_id']
        # If updating, ensure the employee_id isn't being changed to an existing one
        if self.instance.pk: # This means it's an update operation
            if Employee.objects.filter(employee_id=employee_id).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("This Employee ID already exists for another employee.")
        else: # This means it's a create operation
            if Employee.objects.filter(employee_id=employee_id).exists():
                raise forms.ValidationError("This Employee ID already exists.")
        return employee_id

    def clean_email(self):
        email = self.cleaned_data['email']
        # Check for uniqueness, excluding the current instance if it's an update
        if self.instance.pk:
            if Employee.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("An employee with this email already exists.")
        else:
            if Employee.objects.filter(email=email).exists():
                raise forms.ValidationError("An employee with this email already exists.")
        return email

from django import forms
from .models import WorkingDay, FinancialYear, Month
from django.core.exceptions import ValidationError

class WorkingDayForm(forms.ModelForm):
    # ModelChoiceFields to represent ForeignKey relationships as dropdowns
    fin_year = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(),
        to_field_name='fin_year_id', # Use fin_year_id as the value for the option
        empty_label="Select Financial Year",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Financial Year"
    )
    month = forms.ModelChoiceField(
        queryset=Month.objects.all(),
        to_field_name='id', # Use id as the value for the option
        empty_label="Select Month",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Month"
    )

    class Meta:
        model = WorkingDay
        fields = ['fin_year', 'month', 'days'] # 'comp_id' will be set in the view
        widgets = {
            'days': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter number of days'}),
        }
        labels = {
            'days': 'Number of Days',
        }

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # Get request from kwargs to access session
        super().__init__(*args, **kwargs)
        
        # Prefill dropdowns on edit
        if self.instance.pk:
            self.fields['fin_year'].initial = self.instance.fin_year.fin_year_id
            self.fields['month'].initial = self.instance.month.id
            # Disable fin_year and month on edit, as per ASP.NET original behavior
            self.fields['fin_year'].widget.attrs['disabled'] = 'disabled'
            self.fields['month'].widget.attrs['disabled'] = 'disabled'
        else:
            # If creating a new record, set initial queryset for financial year
            # based on current financial year from session (if available)
            if self.request and 'finyear' in self.request.session:
                current_fin_year_id = self.request.session['finyear']
                # Filter FinancialYear queryset to only include current_fin_year_id or older
                # This matches the ASP.NET query logic 'FinYearId<='" + FinYearId + "'"
                self.fields['fin_year'].queryset = FinancialYear.objects.filter(fin_year_id__lte=current_fin_year_id)
            else:
                self.fields['fin_year'].queryset = FinancialYear.objects.all()

    def clean(self):
        """
        Custom form-level validation.
        Handles the unique constraint for (comp_id, fin_year, month) for user-friendly error messages.
        """
        cleaned_data = super().clean()
        fin_year = cleaned_data.get('fin_year')
        month = cleaned_data.get('month')
        days = cleaned_data.get('days')

        # Check for non-positive days as per ASP.NET validation
        if days is not None and days <= 0:
            self.add_error('days', "Days must be a positive number.")

        # Ensure unique combination for comp_id, fin_year, month on creation
        if not self.instance.pk and self.request: # Only for new records
            comp_id = self.request.session.get('compid')
            if comp_id and fin_year and month:
                if WorkingDay.objects.filter(comp_id=comp_id, fin_year=fin_year, month=month).exists():
                    raise ValidationError(
                        "This month already has working days defined for this financial year.",
                        code='duplicate_entry'
                    )
        
        return cleaned_data

from django import forms
from .models import Holiday
from datetime import datetime, date

class HolidayForm(forms.ModelForm):
    # Use a DateField for proper date handling in the form,
    # despite the model storing it as a string.
    holiday_date = forms.DateField(
        label="Date", # Label for the form field
        widget=forms.DateInput(attrs={
            'type': 'date', # This provides a native date picker in modern browsers
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }),
        # Allow parsing 'DD-MM-YYYY' and default 'YYYY-MM-DD' formats
        input_formats=['%d-%m-%Y', '%Y-%m-%d'], 
        error_messages={'invalid': 'Please enter date in DD-MM-YYYY format (e.g., 01-01-2023).'}
    )

    class Meta:
        model = Holiday
        # Specify fields that the user can edit through this form.
        # 'holiday_date' in the form maps to 'holiday_date_str' in the model via clean/save.
        fields = ['title', 'holiday_date'] 
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter holiday title'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If the form is being used to edit an existing instance,
        # populate the 'holiday_date' form field using the model's 'holiday_date' property.
        if self.instance.pk and self.instance.holiday_date_str:
            self.initial['holiday_date'] = self.instance.holiday_date 

    def clean_holiday_date(self) -> str:
        """
        Custom cleaning method for holiday_date.
        Ensures the date is converted to the 'DD-MM-YYYY' string format expected by the model.
        """
        date_obj: date = self.cleaned_data['holiday_date']
        return date_obj.strftime('%d-%m-%Y')

    def save(self, commit=True):
        """
        Overrides the default save method to ensure the cleaned 'holiday_date' 
        (which is now a 'DD-MM-YYYY' string) is correctly assigned to the model's 
        'holiday_date_str' field before saving.
        """
        instance: Holiday = super().save(commit=False)
        # Assign the cleaned and formatted date string to the model field
        instance.holiday_date_str = self.cleaned_data['holiday_date'] 
        if commit:
            instance.save()
        return instance

from django import forms
from .models import GatePassReason

class GatePassReasonForm(forms.ModelForm):
    class Meta:
        model = GatePassReason
        fields = ['reason'] # Only 'reason' is editable by the user
        widgets = {
            'reason': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter gate pass reason',
            }),
        }
        
    def clean_reason(self):
        """
        Custom validation for reason field if needed,
        e.g., to ensure uniqueness case-insensitively, or specific formats.
        Model already has unique=True.
        """
        reason = self.cleaned_data['reason']
        # Example of additional cleaning/validation:
        # if not reason.strip():
        #     raise forms.ValidationError("Reason cannot be empty.")
        return reason

# hr_masters/forms.py
from django import forms
from .models import IntercomExtNo, Department

class IntercomExtNoForm(forms.ModelForm):
    """
    Form for creating and updating IntercomExtNo records.
    """
    department = forms.ModelChoiceField(
        queryset=Department.objects.all().order_by('symbol'),
        empty_label="Select Department",
        label="Department",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    class Meta:
        model = IntercomExtNo
        fields = ['ext_no', 'department']
        widgets = {
            'ext_no': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 1234'
            }),
        }
        labels = {
            'ext_no': 'Ext. No',
        }

    def clean_ext_no(self):
        ext_no = self.cleaned_data['ext_no']
        # Example of custom validation: ensure ExtNo is numeric if desired, or check for uniqueness
        if not ext_no.isdigit():
            raise forms.ValidationError("Extension number must contain only digits.")
        
        # Check for uniqueness, excluding the current instance during an update
        query = IntercomExtNo.objects.filter(ext_no=ext_no)
        if self.instance.pk: # If updating an existing instance
            query = query.exclude(pk=self.instance.pk)
        
        if query.exists():
            raise forms.ValidationError("This extension number already exists.")
            
        return ext_no

# hr/forms.py
from django import forms
from .models import SwapCard

class SwapCardForm(forms.ModelForm):
    """
    Form for creating and updating SwapCard objects.
    Enforces validation, including a unique check for swap_card_no.
    """
    class Meta:
        model = SwapCard
        fields = ['swap_card_no']
        widgets = {
            'swap_card_no': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Swap Card Number'
            }),
        }
        
    def clean_swap_card_no(self):
        """
        Custom validation to ensure SwapCardNo is unique.
        This mirrors the implicit expectation for master data and prevents duplicates.
        """
        swap_card_no = self.cleaned_data['swap_card_no']
        
        # Check if a SwapCard with this number already exists, excluding the current instance
        # (important for update operations)
        qs = SwapCard.objects.filter(swap_card_no=swap_card_no)
        if self.instance.pk: # If updating an existing instance
            qs = qs.exclude(pk=self.instance.pk)
            
        if qs.exists():
            raise forms.ValidationError("This Swap Card Number already exists.")
            
        return swap_card_no

# hr_masters/forms.py
from django import forms
from .models import CorporateMobileNo

class CorporateMobileNoForm(forms.ModelForm):
    class Meta:
        model = CorporateMobileNo
        fields = ['mobile_no', 'limit_amount']
        widgets = {
            'mobile_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., +1234567890'}),
            'limit_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., 500.00'}),
        }
        
    def clean_limit_amount(self):
        """
        Custom validation for limit_amount (e.g., ensure it's positive).
        This mirrors the implicit validation often seen in ASP.NET numeric fields.
        """
        limit_amount = self.cleaned_data['limit_amount']
        if limit_amount < 0:
            raise forms.ValidationError("Limit amount cannot be negative.")
        return limit_amount

from django import forms
from .models import PF_Slab
import re

class PF_SlabForm(forms.ModelForm):
    """
    Form for creating and updating PF_Slab instances.
    Includes custom validation rules derived from ASP.NET RegularExpressionValidator.
    """
    class Meta:
        model = PF_Slab
        fields = ['pf_employee', 'pf_company', 'active']
        widgets = {
            'pf_employee': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter PF Employee Share'
            }),
            'pf_company': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter PF Company Share'
            }),
            'active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded',
                'x-model': 'activeStatus', # Alpine.js binding for dynamic UI
                'x-bind:disabled': 'activeStatusDisabled' # Alpine.js binding for dynamic disabling
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically disable the 'active' checkbox based on existing active slabs,
        # mirroring ASP.NET behavior for UI feedback.
        # Server-side validation (in clean_active) remains the primary safeguard.
        active_slab_exists = PF_Slab.objects.filter(active=True).exists()
        
        # Default Alpine.js state for the checkbox and its disabled status
        self.fields['active'].widget.attrs['x-init'] = f"activeStatus = {str(self.initial.get('active', False)).lower()}; activeStatusDisabled = false;"

        if self.instance.pk:  # If this is an update form for an existing instance
            if self.instance.active:
                # If the current instance is active, allow it to be toggled
                self.fields['active'].widget.attrs['x-init'] = f"activeStatus = true; activeStatusDisabled = false;"
            elif active_slab_exists and not self.instance.active:
                # If another active slab exists and this one is inactive, disable its checkbox
                self.fields['active'].widget.attrs['x-init'] = f"activeStatus = false; activeStatusDisabled = true;"
        else:  # If this is a create form for a new instance
            if active_slab_exists:
                # If an active slab exists, disable the checkbox for new entries
                self.fields['active'].widget.attrs['x-init'] = f"activeStatus = false; activeStatusDisabled = true;"

    def clean_pf_employee(self):
        pf_employee = self.cleaned_data['pf_employee']
        # Regex from ASP.NET: ^\d{1,15}(\.\d{0,3})?$
        if not re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", str(pf_employee)):
            raise forms.ValidationError("PF Employee must be a number with up to 15 digits and 3 decimal places.")
        return pf_employee

    def clean_pf_company(self):
        pf_company = self.cleaned_data['pf_company']
        # Regex from ASP.NET: ^\d{1,15}(\.\d{0,3})?$
        if not re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", str(pf_company)):
            raise forms.ValidationError("PF Company must be a number with up to 15 digits and 3 decimal places.")
        return pf_company

    def clean(self):
        cleaned_data = super().clean()
        active = cleaned_data.get('active')

        # Model's clean method handles this more robustly, but form can also add specific UI-driven checks.
        # This form's `clean` method is primarily for field-specific validation.
        # The model's `clean` method provides the ultimate validation for the active status.
        return cleaned_data

# hr_masters/forms.py
from django import forms
from .models import BusinessGroup

class BusinessGroupForm(forms.ModelForm):
    class Meta:
        model = BusinessGroup
        fields = ['name', 'symbol', 'incharge']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'incharge': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_name(self):
        name = self.cleaned_data.get('name')
        # Example of custom validation: ensure name is unique (case-insensitive)
        # This assumes 'Name' is unique in the original ASP.NET application based on typical master data patterns
        if self.instance.pk: # If updating an existing instance
            if BusinessGroup.objects.filter(name__iexact=name).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("A Business Group with this name already exists.")
        else: # If creating a new instance
            if BusinessGroup.objects.filter(name__iexact=name).exists():
                raise forms.ValidationError("A Business Group with this name already exists.")
        return name

# hr_app/forms.py
from django import forms
from .models import Department

class DepartmentForm(forms.ModelForm):
    class Meta:
        model = Department
        # Exclude 'id' as it's typically auto-generated or managed by the database
        # and not directly editable by the user during creation/update.
        fields = ['description', 'symbol']
        widgets = {
            'description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter Department Description'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter Department Symbol'}),
        }
        labels = {
            'description': 'Description',
            'symbol': 'Symbol',
        }
        
    # Custom validation can be added here if needed, for example:
    def clean_symbol(self):
        symbol = self.cleaned_data['symbol']
        # Example: Ensure symbol is unique (already handled by unique=True in model if set, but good for custom rules)
        # if Department.objects.filter(symbol=symbol).exclude(pk=self.instance.pk).exists():
        #     raise forms.ValidationError("This symbol is already in use.")
        return symbol

from django import forms
from .models import Grade

class GradeForm(forms.ModelForm):
    class Meta:
        model = Grade
        fields = ['description', 'symbol'] # Exclude 'id' as it's auto-assigned or exists
        widgets = {
            'description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter grade description'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter symbol'}),
        }
    
    # Custom validation logic mirroring ASP.NET's RequiredFieldValidator and business rules
    def clean(self):
        cleaned_data = super().clean()
        
        description = cleaned_data.get('description')
        symbol = cleaned_data.get('symbol')

        # Mimic ASP.NET's RequiredFieldValidator
        if not description:
            self.add_error('description', 'Description is required.')
        if not symbol:
            self.add_error('symbol', 'Symbol is required.')

        # Apply model-level uniqueness check (fat model principle)
        # Create a temporary instance to use the model's is_unique method
        # If it's an update, the instance.id will be set from the form's instance
        temp_grade = Grade(
            id=self.instance.id if self.instance.pk else None, 
            description=description, 
            symbol=symbol
        )
        is_unique, message = temp_grade.is_unique()
        if not is_unique:
            # Add general form error or specific field error
            if "description" in message:
                self.add_error('description', message)
            elif "symbol" in message:
                self.add_error('symbol', message)
            else:
                self.add_error(None, message) # General form error
        
        return cleaned_data

from django import forms
from .models import Designation

class DesignationForm(forms.ModelForm):
    class Meta:
        model = Designation
        fields = ['type', 'symbol'] # Corresponding to 'Type' and 'Symbol' in ASP.NET
        widgets = {
            'type': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter designation type'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter designation symbol'
            }),
        }
        
    def clean_type(self):
        designation_type = self.cleaned_data['type']
        # Example of custom validation: ensure type is unique (if not handled at DB level or for soft checks)
        # if Designation.objects.filter(type=designation_type).exclude(pk=self.instance.pk).exists():
        #     raise forms.ValidationError("This designation type already exists.")
        return designation_type

    def clean_symbol(self):
        designation_symbol = self.cleaned_data['symbol']
        # Example of custom validation: ensure symbol is a certain format
        # if not designation_symbol.isalpha():
        #     raise forms.ValidationError("Symbol must contain only letters.")
        return designation_symbol

from django import forms
from django.forms import formset_factory
from .models import (
    TourIntimation, Employee, BusinessGroup, Country, State, City,
    TourAdvanceTemp, TourExpenseType, TourAdvanceDetail
)
import datetime

# Common Tailwind CSS classes for form inputs
INPUT_CLASSES = "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
RADIO_CLASSES = "h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"

class TourIntimationForm(forms.ModelForm):
    # Radio button fields
    employee_selection_type = forms.ChoiceField(
        choices=[('0', 'Self'), ('1', 'Others')],
        widget=forms.RadioSelect(attrs={'class': 'flex space-x-4'}),
        initial='0',
        label='Employee Type'
    )
    # The actual employee field. If 'Self', this is current user. If 'Others', selected via autocomplete.
    # It will be hidden/shown via Alpine.js.
    # Store employee_name for 'Others' via autocomplete, then resolve to emp_id in view/model logic.
    other_employee_name = forms.CharField(
        max_length=255, 
        required=False, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': INPUT_CLASSES, 
            'placeholder': 'Start typing employee name...',
            'hx-get': '/hr/employees/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off', # Disable browser autocomplete
            '@input': 'clearSelectedEmployee()', # Alpine.js
            'x-bind:disabled': 'employeeType === "0"' # Disabled if "Self"
        })
    )
    # Hidden input to store the resolved emp_id from autocomplete
    employee_emp_id = forms.CharField(
        max_length=50, 
        required=False, 
        widget=forms.HiddenInput(attrs={'x-model': 'selectedEmployeeId'})
    )


    wo_group_selection_type = forms.ChoiceField(
        choices=[('0', 'WO No'), ('1', 'BG Group')],
        widget=forms.RadioSelect(attrs={'class': 'flex space-x-4'}),
        initial='0',
        label='WO/BG Selection'
    )

    class Meta:
        model = TourIntimation
        fields = [
            # 'employee_selection_type', # Handled as extra field
            # 'other_employee_name', # Handled as extra field
            # 'employee_emp_id', # Handled as extra field
            'project_name',
            # 'wo_group_selection_type', # Handled as extra field
            'wo_no',
            'business_group',
            'place_of_tour_country',
            'place_of_tour_state',
            'place_of_tour_city',
            'tour_start_date',
            'tour_start_time',
            'tour_end_date',
            'tour_end_time',
            'no_of_days',
            'name_address_ser_provider',
            'contact_person',
            'contact_no',
            'email',
        ]
        widgets = {
            'project_name': forms.TextInput(attrs={'class': INPUT_CLASSES}),
            'wo_no': forms.TextInput(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': 'woGroupType === "1"'}),
            'business_group': forms.Select(attrs={'class': INPUT_CLASSES, 'x-bind:disabled': 'woGroupType === "0"'}),
            'place_of_tour_country': forms.Select(attrs={
                'class': INPUT_CLASSES,
                'hx-get': '/hr/locations/states/',
                'hx-target': '#id_place_of_tour_state',
                'hx-trigger': 'change',
                'hx-include': 'this', # Include selected country ID
                'hx-swap': 'outerHTML'
            }),
            'place_of_tour_state': forms.Select(attrs={
                'class': INPUT_CLASSES,
                'hx-get': '/hr/locations/cities/',
                'hx-target': '#id_place_of_tour_city',
                'hx-trigger': 'change',
                'hx-include': 'this', # Include selected state ID
                'hx-swap': 'outerHTML'
            }),
            'place_of_tour_city': forms.Select(attrs={'class': INPUT_CLASSES}),
            'tour_start_date': forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'}),
            'tour_start_time': forms.TimeInput(attrs={'class': INPUT_CLASSES, 'type': 'time'}),
            'tour_end_date': forms.DateInput(attrs={'class': INPUT_CLASSES, 'type': 'date'}),
            'tour_end_time': forms.TimeInput(attrs={'class': INPUT_CLASSES, 'type': 'time'}),
            'no_of_days': forms.NumberInput(attrs={'class': INPUT_CLASSES}),
            'name_address_ser_provider': forms.Textarea(attrs={'class': INPUT_CLASSES, 'rows': 3}),
            'contact_person': forms.TextInput(attrs={'class': INPUT_CLASSES}),
            'contact_no': forms.TextInput(attrs={'class': INPUT_CLASSES}),
            'email': forms.EmailInput(attrs={'class': INPUT_CLASSES}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dynamic dropdowns
        self.fields['business_group'].queryset = BusinessGroup.objects.all()
        self.fields['place_of_tour_country'].queryset = Country.objects.all().order_by('name')
        
        # Initial population of state and city based on selected country/state for editing
        if self.instance.pk:
            if self.instance.place_of_tour_country:
                self.fields['place_of_tour_state'].queryset = State.objects.filter(country=self.instance.place_of_tour_country).order_by('name')
            if self.instance.place_of_tour_state:
                self.fields['place_of_tour_city'].queryset = City.objects.filter(state=self.instance.place_of_tour_state).order_by('name')
            
            # Set initial values for radio buttons if instance exists
            if self.instance.employee.emp_id == self.instance.session_id: # Assuming session_id is current user's emp_id for 'self'
                self.fields['employee_selection_type'].initial = '0'
                self.initial['other_employee_name'] = ''
                self.initial['employee_emp_id'] = self.instance.employee.emp_id
            else:
                self.fields['employee_selection_type'].initial = '1'
                self.initial['other_employee_name'] = str(self.instance.employee) # Display full name for others
                self.initial['employee_emp_id'] = self.instance.employee.emp_id

            if self.instance.wo_no and self.instance.wo_no != 'NA':
                self.fields['wo_group_selection_type'].initial = '0'
            elif self.instance.business_group:
                self.fields['wo_group_selection_type'].initial = '1'
        else:
            # For new forms, initialize state/city to empty
            self.fields['place_of_tour_state'].queryset = State.objects.none()
            self.fields['place_of_tour_city'].queryset = City.objects.none()

        # Add generic initial 'Select' option for all dropdowns
        self.fields['place_of_tour_country'].empty_label = 'Select'
        self.fields['place_of_tour_state'].empty_label = 'Select'
        self.fields['place_of_tour_city'].empty_label = 'Select'
        self.fields['business_group'].empty_label = 'Select'


    def clean(self):
        cleaned_data = super().clean()
        employee_selection_type = cleaned_data.get('employee_selection_type')
        other_employee_name = cleaned_data.get('other_employee_name')
        employee_emp_id = cleaned_data.get('employee_emp_id')
        wo_group_selection_type = cleaned_data.get('wo_group_selection_type')
        wo_no = cleaned_data.get('wo_no')
        business_group = cleaned_data.get('business_group')

        # Employee Validation
        if employee_selection_type == '1': # Others selected
            if not other_employee_name or not employee_emp_id:
                self.add_error('other_employee_name', 'Employee name and ID are required for "Others".')
            else:
                # Validate if the resolved emp_id actually corresponds to a valid employee
                if not Employee.get_employee_by_code(employee_emp_id, comp_id=self.instance.comp_id if self.instance.pk else 1, fin_year_id=self.instance.fin_year_id if self.instance.pk else 1):
                    self.add_error('other_employee_name', 'Invalid Employee selected.')

        # WO/BG Validation
        if wo_group_selection_type == '0': # WO No selected
            if not wo_no:
                self.add_error('wo_no', 'WO No is required.')
            # You might add `TourIntimation.check_wo_validity(wo_no, ...)` here for server-side check
            # if not TourIntimation.check_wo_validity(wo_no, self.instance.comp_id, self.instance.fin_year_id):
            #     self.add_error('wo_no', 'Entered WO No is not valid!')
            cleaned_data['business_group'] = None # Ensure BG is cleared
        else: # BG Group selected
            if not business_group:
                self.add_error('business_group', 'Business Group is required.')
            cleaned_data['wo_no'] = 'NA' # Ensure WO No is set to 'NA'

        # Date Validation (can be handled by Django's DateField, but adding specific checks for range/format if needed)
        start_date = cleaned_data.get('tour_start_date')
        end_date = cleaned_data.get('tour_end_date')
        if start_date and end_date and start_date > end_date:
            self.add_error('tour_end_date', 'End Date cannot be before Start Date.')
        
        # No of Days validation (original had RegEx for decimal, Django NumberInput covers basic numbers)
        no_of_days = cleaned_data.get('no_of_days')
        if no_of_days is not None and no_of_days < 0:
            self.add_error('no_of_days', 'Number of days cannot be negative.')

        # Required dropdowns
        if cleaned_data.get('place_of_tour_country') is None:
            self.add_error('place_of_tour_country', 'Country is required.')
        if cleaned_data.get('place_of_tour_state') is None:
            self.add_error('place_of_tour_state', 'State is required.')
        if cleaned_data.get('place_of_tour_city') is None:
            self.add_error('place_of_tour_city', 'City is required.')


        return cleaned_data


class TourAdvanceTempForm(forms.ModelForm):
    # Field to display and get Employee name from autocomplete
    employee_full_name = forms.CharField(
        max_length=255, 
        required=True, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': INPUT_CLASSES, 
            'placeholder': 'Start typing employee name...',
            'hx-get': '/hr/employees/autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-autocomplete-results-advance-temp',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off',
            '@input': 'clearSelectedEmployee()', # Alpine.js
        })
    )
    # Hidden field to store the actual emp_id from autocomplete
    employee_emp_id = forms.CharField(
        max_length=50, 
        required=True, 
        widget=forms.HiddenInput(attrs={'x-model': 'selectedEmployeeId'})
    )

    class Meta:
        model = TourAdvanceTemp
        fields = ['amount', 'remarks']
        widgets = {
            'amount': forms.NumberInput(attrs={'class': INPUT_CLASSES}),
            'remarks': forms.TextInput(attrs={'class': INPUT_CLASSES}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk: # For edit mode
            self.initial['employee_full_name'] = str(self.instance.employee)
            self.initial['employee_emp_id'] = self.instance.employee.emp_id
        
        # Remove direct employee field from here, use employee_full_name and employee_emp_id
        del self.fields['employee'] 

    def clean(self):
        cleaned_data = super().clean()
        employee_emp_id = cleaned_data.get('employee_emp_id')
        amount = cleaned_data.get('amount')

        if not employee_emp_id:
            self.add_error('employee_full_name', 'Employee is required.')
        else:
            # Resolve EmpId to Employee object
            try:
                # Assuming CompId and FinYearId are handled by the view or request context
                # For demo, using dummy defaults
                cleaned_data['employee'] = Employee.objects.get(emp_id=employee_emp_id, comp_id=1, fin_year_id=1) 
            except Employee.DoesNotExist:
                self.add_error('employee_full_name', 'Invalid Employee ID.')

        if amount is not None and amount <= 0:
            self.add_error('amount', 'Amount must be positive.')
            
        return cleaned_data

class TourExpenseDetailForm(forms.Form):
    # This form is for the individual row in GridView2, for which we input Amount and Remarks
    # Terms is read-only, passed via initial data
    expense_type_id = forms.IntegerField(widget=forms.HiddenInput(), required=False) # Store the ID
    terms = forms.CharField(max_length=255, widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'readonly': 'readonly'}), required=False)
    amount = forms.DecimalField(
        max_digits=18, decimal_places=2, required=False, 
        widget=forms.NumberInput(attrs={'class': INPUT_CLASSES, 'placeholder': 'Amount'})
    )
    remarks = forms.CharField(
        max_length=255, required=False, 
        widget=forms.TextInput(attrs={'class': INPUT_CLASSES, 'placeholder': 'Remarks'})
    )

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount is not None and amount < 0:
            raise forms.ValidationError("Amount cannot be negative.")
        return amount

# Formset for the Tour Expense Details Grid (GridView2 equivalent)
# This will be passed to the main TourIntimationCreateUpdateView
TourExpenseDetailFormSet = formset_factory(TourExpenseDetailForm, extra=0) # extra=0 as all rows are pre-populated from TourExpenseType

from django import forms
from .models import TourIntimation

class TourIntimationForm(forms.ModelForm):
    class Meta:
        model = TourIntimation
        fields = [
            'intimation_date', 
            'employee_name', 
            'destination', 
            'start_date', 
            'end_date', 
            'purpose', 
            'status'
        ]
        widgets = {
            'intimation_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'destination': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'purpose': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}, 
                                   choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')]),
        }
        
    def clean(self):
        """
        Custom validation for start and end dates.
        Ensures end_date is not before start_date.
        """
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', 'End date cannot be before start date.')
        
        return cleaned_data

from django import forms
from .models import TourIntimation, BusinessGroup, Employee

class TourIntimationSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('0', 'TI No'),
        ('1', 'Employee Name'),
        ('2', 'WO No'),
        ('3', 'BG Group'),
        ('4', 'Project Name'),
    ]

    drp_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/hr/tourintimation/search_form_fields/', # HTMX endpoint to dynamically update fields
            'hx-target': '#search-fields-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )

    # These fields will be toggled visibility via Alpine.js based on drp_field selection
    txt_mrs = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter value...',
        })
    )

    txt_emp_name = forms.CharField(
        max_length=255,
        required=False,
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-get': '/hr/employees/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
        })
    )

    drp_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False,
        label="Business Group",
        empty_label="Select Business Group",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial visibility based on default or loaded data
        # For simplicity in this initial rendering, all might be visible,
        # Alpine.js/HTMX will handle dynamic display.
        # This part will be mostly managed by the HTMX partial response for form fields.
        if 'drp_field' in self.data and self.data['drp_field'] != 'Select':
            selected_field = self.data['drp_field']
            if selected_field == '1': # Employee Name
                self.fields['txt_mrs'].widget.attrs['style'] = 'display:none;'
                self.fields['drp_group'].widget.attrs['style'] = 'display:none;'
            elif selected_field == '3': # BG Group
                self.fields['txt_mrs'].widget.attrs['style'] = 'display:none;'
                self.fields['txt_emp_name'].widget.attrs['style'] = 'display:none;'
            elif selected_field in ['0', '2', '4']: # TI No, WO No, Project Name
                self.fields['txt_emp_name'].widget.attrs['style'] = 'display:none;'
                self.fields['drp_group'].widget.attrs['style'] = 'display:none;'
        else: # Default 'Select' or no selection
            self.fields['txt_emp_name'].widget.attrs['style'] = 'display:none;'
            self.fields['drp_group'].widget.attrs['style'] = 'display:none;'
            # For 'Select' default, ASP.NET showed TxtMrs.
            # We'll make it visible, and Alpine.js/HTMX will manage on dropdown change.

# Generic ModelForm (placeholder, not strictly from ASP.NET code for *this* page)
class TourIntimationForm(forms.ModelForm):
    class Meta:
        model = TourIntimation
        fields = [
            'company', 'financial_year_id', 'employee', 'ti_no', 'wo_no',
            'business_group', 'project_name', 'place_of_tour_city',
            'place_of_tour_state', 'place_of_tour_country',
            'tour_start_date', 'tour_end_date'
        ]
        widgets = {
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ti_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'business_group': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tour_start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'tour_end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }

from django import forms
from .models import GatePass, Employee

class GatePassSearchForm(forms.Form):
    """
    Form for capturing search criteria for Gate Pass reports.
    This is not a ModelForm as it's for filtering, not creating/updating a model instance.
    """
    from_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    to_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    employee_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            # HTMX attributes for autocomplete
            'hx-get': '/hr/gatepass/employees-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup, with a delay
            'hx-target': '#employee-suggestions', # Target div for suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
        })
    )

class GatePassForm(forms.ModelForm):
    """
    Standard form for creating and updating GatePass records.
    """
    class Meta:
        model = GatePass
        fields = ['gp_no', 'emp', 'sys_date', 'authorize', 'authorized_by', 'authorize_date', 'authorize_time']
        widgets = {
            'gp_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'emp': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'authorized_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Custom validation can be added here if needed, e.g., to ensure date logic is correct.

from django import forms
from .models import GatePass, GatePassDetail, Employee, GatePassReason

class GatePassSearchForm(forms.Form):
    from_date = forms.DateField(
        label="From Date",
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-trigger': 'change, keyup delay:500ms', # Optional: allow live search on date change
            'hx-get': "{% url 'gatepass_app:authorize_gatepass_table' %}",
            'hx-target': '#gatepassTable-container',
            'hx-swap': 'innerHTML'
        })
    )
    to_date = forms.DateField(
        label="To Date",
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-trigger': 'change, keyup delay:500ms', # Optional: allow live search on date change
            'hx-get': "{% url 'gatepass_app:authorize_gatepass_table' %}",
            'hx-target': '#gatepassTable-container',
            'hx-swap': 'innerHTML'
        })
    )
    # Employee name for autocomplete
    employee_name = forms.CharField(
        label="Employee Name",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-get': "{% url 'gatepass_app:employee_autocomplete' %}", # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:300ms, search',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'outerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
            'list': 'employee-datalist' # Link to datalist for native autocomplete
        })
    )

class GatePassAuthorizeForm(forms.Form):
    """
    A form to handle the bulk authorization of gate passes.
    The actual IDs to authorize will be passed via request.POST from checkboxes.
    """
    # This form doesn't need fields, as data comes from the list of checkboxes
    # We will validate against the presence of 'selected_ids' in the request.POST
    pass

class GatePassDetailForm(forms.ModelForm):
    # This form is for completeness based on template, though not directly used in the ASP.NET
    # authorization page for CRUD. It would be used in a separate "manage details" page.
    class Meta:
        model = GatePassDetail
        fields = [
            'gate_pass', 'from_date', 'from_time', 'to_time', 'place',
            'contact_person', 'contact_no', 'reason', 'type_of_code', 'type_for_text', 'detail_employee'
        ]
        widgets = {
            'gate_pass': forms.HiddenInput(), # Usually set in view or JS for related records
            'from_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'from_time': forms.TextInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'to_time': forms.TextInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'place': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'contact_person': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'reason': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'type_of_code': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'type_for_text': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
            'detail_employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
        }

from django import forms
from .models import BusinessGroup, OfficeStaff, TourIntimation

class TourIntimationSearchForm(forms.Form):
    SEARCH_FIELDS = [
        ('Select', 'Select'),
        ('0', 'TI No'),
        ('1', 'Employee Name'),
        ('2', 'WO No'),
        ('3', 'BG Group'),
        ('4', 'Project Name'),
    ]
    
    drp_field = forms.ChoiceField(
        choices=SEARCH_FIELDS,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3'}),
        initial='Select'
    )
    
    txt_mrs = forms.CharField(
        label="Search Text (TI No, WO No, Project Name)",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-[150px]', 'placeholder': 'Enter value'})
    )
    
    txt_emp_name = forms.CharField(
        label="Employee Name",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-[250px]', 'placeholder': 'Type employee name', 'hx-get': '/hr_tour/autocomplete/employees/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#emp-suggestions', 'autocomplete': 'off'})
    )

    # Use a hidden field for actual EmpId, populated by autocomplete selection
    emp_id_code = forms.CharField(
        widget=forms.HiddenInput(), 
        required=False
    )
    
    drp_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        label="BG Group",
        required=False,
        empty_label="Select Business Group",
        widget=forms.Select(attrs={'class': 'box3'})
    )

    # Custom validation for employee name to extract ID from string
    def clean_txt_emp_name(self):
        emp_name_with_id = self.cleaned_data.get('txt_emp_name')
        if emp_name_with_id:
            # Assumes format "Employee Name [EmpId]"
            import re
            match = re.search(r'\[(.*?)\]$', emp_name_with_id)
            if match:
                emp_id = match.group(1)
                if not OfficeStaff.objects.filter(emp_id=emp_id).exists():
                    raise forms.ValidationError("Invalid Employee selected.")
                self.cleaned_data['emp_id_code'] = emp_id # Store the actual EmpId
                return emp_name_with_id
            else:
                raise forms.ValidationError("Please select an employee from the autocomplete suggestions.")
        return emp_name_with_id


# Placeholder for TourIntimation (Edit) Form
class TourIntimationForm(forms.ModelForm):
    class Meta:
        model = TourIntimation
        fields = [
            'ti_no', 'emp_id_code', 'fin_year_id', 'wo_no', 
            'bg_group_id', 'project_name', 'place_of_tour_city_id', 
            'place_of_tour_state_id', 'place_of_tour_country_id', 
            'tour_start_date', 'tour_end_date'
            # SessionId and CompId usually derived from context/user
        ]
        widgets = {
            'ti_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'emp_id_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bg_group_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_city_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_state_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_country_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tour_start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'tour_end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }

from django import forms
from django.forms import inlineformset_factory
from .models import (
    TourIntimation, Employee, BusinessGroup, Country, State, City, 
    TourExpenseType, TourAdvanceDetail, TourAdvance
)
from datetime import time, datetime

class TourIntimationForm(forms.ModelForm):
    # These fields correspond to the main form inputs
    # EmpName is a display field, EmpId is the actual FK. We'll handle this in clean/save.
    employee_full_name = forms.CharField(
        max_length=255, 
        required=True, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/tour-intimation/employee-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'placeholder': 'Start typing employee name...'
        })
    )
    # Autocomplete suggestion container
    employee_suggestions = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={'id': 'employee-suggestions'}) # Hidden input to mark the container
    )

    # Radio button for WO No / BG Group logic
    wo_no_group_selection = forms.ChoiceField(
        choices=[('0', 'WO No'), ('1', 'BG Group')],
        widget=forms.RadioSelect(attrs={
            'hx-post': '/tour-intimation/toggle-wo-group/',
            'hx-target': '#wo-group-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change'
        }),
        initial='0', # Default to WO No
        label="" # No label needed for radio list itself
    )

    # Custom fields for date and time to handle separate inputs
    tour_start_date_input = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-[70px]'}),
        label="Tour Start Date",
        input_formats=['%Y-%m-%d', '%d-%m-%Y'] # Allow both formats if needed
    )
    tour_start_time_input = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time', 'class': 'box3 w-[70px]'}),
        label="Time"
    )
    tour_end_date_input = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-[70px]'}),
        label="Tour End Date",
        input_formats=['%Y-%m-%d', '%d-%m-%Y']
    )
    tour_end_time_input = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time', 'class': 'box3 w-[70px]'}),
        label="Time"
    )

    class Meta:
        model = TourIntimation
        fields = [
            'TINo', 'WONo', 'BGGroupId', 'ProjectName', 
            'tour_start_date_input', 'tour_start_time_input', 
            'tour_end_date_input', 'tour_end_time_input',
            'NoOfDays', 'NameAddressSerProvider', 'ContactPerson', 
            'ContactNo', 'Email', 'PlaceOfTourCountry', 
            'PlaceOfTourState', 'PlaceOfTourCity', 'Id' # Id for update form
        ]
        widgets = {
            'TINo': forms.TextInput(attrs={'class': 'box3 w-full', 'readonly': 'readonly'}),
            'WONo': forms.TextInput(attrs={'class': 'box3'}),
            'BGGroupId': forms.Select(attrs={'class': 'box3'}),
            'ProjectName': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'NoOfDays': forms.NumberInput(attrs={'class': 'box3', 'min': '0'}),
            'NameAddressSerProvider': forms.Textarea(attrs={'class': 'box3 w-full h-[30px]', 'rows': 2}),
            'ContactPerson': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'ContactNo': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'Email': forms.EmailInput(attrs={'class': 'box3 w-[75%]'}),
            'PlaceOfTourCountry': forms.Select(attrs={'class': 'box3', 'hx-post': '/tour-intimation/get-states/', 'hx-target': '#id_PlaceOfTourState', 'hx-swap': 'outerHTML'}),
            'PlaceOfTourState': forms.Select(attrs={'class': 'box3', 'hx-post': '/tour-intimation/get-cities/', 'hx-target': '#id_PlaceOfTourCity', 'hx-swap': 'outerHTML'}),
            'PlaceOfTourCity': forms.Select(attrs={'class': 'box3'}),
            'Id': forms.HiddenInput(), # Primary key, might be from query string, or hidden
        }
        labels = {
            'WONo': '', # Label handled by radio button list
            'BGGroupId': '', # Label handled by radio button list
            'NameAddressSerProvider': 'Name & Address of Accommodation Service Provider',
            'NoOfDays': 'No. of Days',
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values for custom fields from instance
        if self.instance.pk:
            self.fields['employee_full_name'].initial = self.instance.full_employee_name
            self.fields['tour_start_date_input'].initial = self.instance.TourStartDate
            self.fields['tour_start_time_input'].initial = datetime.strptime(self.instance.TourStartTime, "%I:%M:%S:%p").time() if self.instance.TourStartTime else None
            self.fields['tour_end_date_input'].initial = self.instance.TourEndDate
            self.fields['tour_end_time_input'].initial = datetime.strptime(self.instance.TourEndTime, "%I:%M:%S:%p").time() if self.instance.TourEndTime else None
            
            # Set initial value for WO No/BG Group selection
            if self.instance.BGGroupId and self.instance.BGGroupId.Id != 1: # Assuming 1 is default/NA for WO No
                self.fields['wo_no_group_selection'].initial = '1'
            else:
                self.fields['wo_no_group_selection'].initial = '0'

        # Dynamically filter choices for dropdowns
        self.fields['BGGroupId'].queryset = BusinessGroup.objects.all()
        self.fields['PlaceOfTourCountry'].queryset = Country.objects.all()
        
        # Initial filtering for State and City based on existing instance data
        if self.instance.PlaceOfTourCountry:
            self.fields['PlaceOfTourState'].queryset = State.objects.filter(CId=self.instance.PlaceOfTourCountry.CId)
        else:
            self.fields['PlaceOfTourState'].queryset = State.objects.none()

        if self.instance.PlaceOfTourState:
            self.fields['PlaceOfTourCity'].queryset = City.objects.filter(SId=self.instance.PlaceOfTourState.SId)
        else:
            self.fields['PlaceOfTourCity'].queryset = City.objects.none()

        # Mark BGGroup related fields as not required if WO No is selected, vice versa
        self.toggle_wo_group_visibility(self.fields['wo_no_group_selection'].initial)


    def toggle_wo_group_visibility(self, selection_value):
        """
        Adjusts visibility and required status of WONo and BGGroupId based on selection.
        This is primarily for frontend hints and initial form rendering.
        Actual validation happens in clean().
        """
        if selection_value == '0': # WO No selected
            self.fields['WONo'].required = True
            self.fields['BGGroupId'].required = False
            # self.fields['BGGroupId'].widget.attrs['disabled'] = 'disabled' # For display purposes
        else: # BG Group selected
            self.fields['WONo'].required = False
            self.fields['BGGroupId'].required = True
            # self.fields['WONo'].widget.attrs['disabled'] = 'disabled' # For display purposes


    def clean(self):
        cleaned_data = super().clean()
        
        # --- Employee Name Validation ---
        employee_full_name = cleaned_data.get('employee_full_name')
        emp_id_str = Employee.get_emp_id_from_full_name(employee_full_name)
        
        if not emp_id_str:
            self.add_error('employee_full_name', 'Invalid Employee Name. Please select from suggestions.')
            return cleaned_data # Return early if employee name is invalid

        try:
            employee_obj = Employee.objects.get(EmpId=emp_id_str)
            cleaned_data['EmpId'] = employee_obj # Assign actual Employee object to EmpId
        except Employee.DoesNotExist:
            self.add_error('employee_full_name', 'Employee not found. Please select a valid employee.')

        # --- WO No / BG Group Validation ---
        wo_no_group_selection = self.data.get('wo_no_group_selection', self.fields['wo_no_group_selection'].initial)
        wo_no = cleaned_data.get('WONo')
        bg_group_id = cleaned_data.get('BGGroupId') # This is the BGGroup object

        if wo_no_group_selection == '0': # WO No selected
            if not wo_no:
                self.add_error('WONo', 'WO No is required.')
            # You would integrate `fun.CheckValidWONo` here.
            # For simplicity, we assume it's just a text field for now.
            # if not check_valid_wo_no(wo_no, self.request.session['compid'], self.request.session['finyear']):
            #     self.add_error('WONo', 'Entered WO No is not valid!')
            cleaned_data['BGGroupId'] = BusinessGroup.objects.get(Id=1) # Default BGGroup ID 1 if WO No selected
        else: # BG Group selected
            if not bg_group_id:
                self.add_error('BGGroupId', 'Business Group is required.')
            cleaned_data['WONo'] = 'NA' # Set WONo to 'NA' if BG Group selected

        # --- Date and Time Parsing and Validation ---
        start_date = cleaned_data.get('tour_start_date_input')
        start_time = cleaned_data.get('tour_start_time_input')
        end_date = cleaned_data.get('tour_end_date_input')
        end_time = cleaned_data.get('tour_end_time_input')

        if start_date and start_time:
            cleaned_data['TourStartDate'] = start_date
            cleaned_data['TourStartTime'] = start_time.strftime("%I:%M:%S:%p")
        else:
            if not start_date: self.add_error('tour_start_date_input', 'Tour Start Date is required.')
            if not start_time: self.add_error('tour_start_time_input', 'Tour Start Time is required.')

        if end_date and end_time:
            cleaned_data['TourEndDate'] = end_date
            cleaned_data['TourEndTime'] = end_time.strftime("%I:%M:%S:%p")
        else:
            if not end_date: self.add_error('tour_end_date_input', 'Tour End Date is required.')
            if not end_time: self.add_error('tour_end_time_input', 'Tour End Time is required.')

        if start_date and end_date and start_date > end_date:
            self.add_error('tour_end_date_input', 'Tour End Date cannot be before Tour Start Date.')
        
        # NoOfDays validation, ASP.NET uses `fun.NumberValidationQty`
        no_of_days = cleaned_data.get('NoOfDays')
        if no_of_days is not None and no_of_days < 0:
            self.add_error('NoOfDays', 'Number of Days cannot be negative.')

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Manually assign EmpId which was resolved in clean()
        instance.EmpId = self.cleaned_data['EmpId']
        # The WONo/BGGroupId logic is handled in clean() which modifies cleaned_data['WONo'] and cleaned_data['BGGroupId']
        instance.WONo = self.cleaned_data['WONo']
        instance.BGGroupId = self.cleaned_data['BGGroupId']
        instance.TourStartDate = self.cleaned_data['tour_start_date_input']
        instance.TourStartTime = self.cleaned_data['tour_start_time_input']
        instance.TourEndDate = self.cleaned_data['tour_end_date_input']
        instance.TourEndTime = self.cleaned_data['tour_end_time_input']

        if commit:
            instance.save()
        return instance


class TourAdvanceDetailForm(forms.ModelForm):
    class Meta:
        model = TourAdvanceDetail
        fields = ['Id', 'ExpencessId', 'Amount', 'Remarks', 'MId']
        widgets = {
            'Id': forms.HiddenInput(), # PK for existing records
            'ExpencessId': forms.HiddenInput(), # FK to expense type
            'MId': forms.HiddenInput(), # FK to main tour intimation
            'Amount': forms.NumberInput(attrs={'class': 'box3 w-full'}),
            'Remarks': forms.TextInput(attrs={'class': 'box3 w-full'}),
        }
        labels = {
            'Amount': '', # Label is in grid header
            'Remarks': '', # Label is in grid header
        }

    def clean_Amount(self):
        amount = self.cleaned_data.get('Amount')
        if amount is not None and amount < 0:
            raise forms.ValidationError("Amount cannot be negative.")
        return amount

# Formset for TourAdvanceDetail (GridView2 equivalent)
# Extra=0 because these are pre-defined expense types, we're just updating amounts/remarks.
# Can be `extra=0, can_delete=False` for simplicity, assuming they are always there.
TourAdvanceDetailFormSet = inlineformset_factory(
    TourIntimation, 
    TourAdvanceDetail, 
    form=TourAdvanceDetailForm, 
    extra=0, 
    can_delete=False,
    fields=['Id', 'ExpencessId', 'Amount', 'Remarks', 'MId']
)


class TourAdvanceForm(forms.ModelForm):
    employee_full_name = forms.CharField(
        max_length=255, 
        required=True, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/tour-intimation/employee-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#advance-employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'placeholder': 'Start typing employee name...'
        })
    )
    employee_suggestions = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={'id': 'advance-employee-suggestions'})
    )

    class Meta:
        model = TourAdvance
        fields = ['Id', 'MId', 'employee_full_name', 'Amount', 'Remarks']
        widgets = {
            'Id': forms.HiddenInput(),
            'MId': forms.HiddenInput(),
            'Amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'Remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
        }
        labels = {
            'employee_full_name': 'Employee Name',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.fields['employee_full_name'].initial = self.instance.EmpId.__str__() # Pre-populate with full employee name

    def clean(self):
        cleaned_data = super().clean()
        employee_full_name = cleaned_data.get('employee_full_name')
        
        emp_id_str = Employee.get_emp_id_from_full_name(employee_full_name)
        
        if not emp_id_str:
            self.add_error('employee_full_name', 'Invalid Employee Name. Please select from suggestions.')
            return cleaned_data

        try:
            employee_obj = Employee.objects.get(EmpId=emp_id_str)
            cleaned_data['EmpId'] = employee_obj
        except Employee.DoesNotExist:
            self.add_error('employee_full_name', 'Employee not found. Please select a valid employee.')

        amount = cleaned_data.get('Amount')
        if amount is not None and amount <= 0:
            self.add_error('Amount', 'Amount must be positive.')

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.EmpId = self.cleaned_data['EmpId']
        if commit:
            instance.save()
        return instance

# hr_transactions/forms.py
from django import forms
from .models import Employee

class EmployeeForm(forms.ModelForm):
    class Meta:
        model = Employee
        fields = ['first_name', 'last_name', 'email', 'hire_date', 'is_active']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'last_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'hire_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'}),
        }
        
    def clean_email(self):
        email = self.cleaned_data['email']
        # Check if email is unique, excluding the current instance during update
        if Employee.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("This email address is already in use.")
        return email

from django import forms
from .models import BankLoan # Although no direct form is needed for 'print' list, keeping for future CRUD

class BankLoanSearchForm(forms.Form):
    SEARCH_OPTIONS = [
        ('0', 'Emp Wise'),
        ('1', 'All'),
    ]
    search_option = forms.ChoiceField(
        choices=SEARCH_OPTIONS,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'}),
        initial='0'
    )
    employee_name_search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-get': '/bankloans/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms', # Trigger on keyup, with delay
            'hx-target': '#employee-suggestions', # Target div for suggestions
            'hx-swap': 'innerHTML',
            'name': 'employee_name_search', # Ensure name is present for form data
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-model': 'selectedEmployee', # Alpine.js binding
            '@input': 'clearSelectedId()', # Alpine.js method to clear hidden ID
            '@focus': 'showSuggestions = true', # Show suggestions on focus
            '@blur.away': 'setTimeout(() => showSuggestions = false, 100)', # Hide on blur away
        })
    )
    # Hidden field to store the actual EmpId from autocomplete selection
    selected_employee_id = forms.CharField(
        widget=forms.HiddenInput(attrs={'x-model': 'selectedEmployeeId'}),
        required=False
    )

    # Note: DrpField "Employee Name" is hardcoded to "0", so no need for a dropdown list in the form.
    # TxtMrs is not used in the ASP.NET code for this specific function.

    def clean(self):
        cleaned_data = super().clean()
        search_option = cleaned_data.get('search_option')
        employee_name_search = cleaned_data.get('employee_name_search')
        selected_employee_id = cleaned_data.get('selected_employee_id')

        if search_option == '0': # Emp Wise
            # If search is emp wise, ensure an employee is selected or text is provided
            if not employee_name_search and not selected_employee_id:
                self.add_error('employee_name_search', 'Employee name or selection is required for "Emp Wise" search.')
            elif employee_name_search and not selected_employee_id:
                 # Attempt to parse code if direct input without selection
                 parsed_code = BankLoan().get_employee_code_from_name(employee_name_search)
                 if parsed_code:
                     cleaned_data['selected_employee_id'] = parsed_code
                 else:
                     # If the input doesn't match the [ID] format, it might be an invalid direct entry
                     # A more robust solution might validate against existing employees.
                     pass # Allow partial match, but warn if no ID found if strict.
        return cleaned_data

# Skeleton for actual BankLoan CRUD form (if needed in other modules)
class BankLoanForm(forms.ModelForm):
    class Meta:
        model = BankLoan
        fields = ['db_column_empid', 'db_column_bankname', 'db_column_branch', 'db_column_amount', 'db_column_installment', 'db_column_fromdate', 'db_column_todate']
        widgets = {
            'db_column_empid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_bankname': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_branch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_installment': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'db_column_fromdate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD/MM/YYYY'}),
            'db_column_todate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD/MM/YYYY'}),
        }

    def clean_db_column_fromdate(self):
        date_str = self.cleaned_data['db_column_fromdate']
        try:
            datetime.strptime(date_str, '%d/%m/%Y')
        except ValueError:
            raise forms.ValidationError("Invalid date format. Use DD/MM/YYYY.")
        return date_str

    def clean_db_column_todate(self):
        date_str = self.cleaned_data['db_column_todate']
        try:
            datetime.strptime(date_str, '%d/%m/%Y')
        except ValueError:
            raise forms.ValidationError("Invalid date format. Use DD/MM/YYYY.")
        return date_str

from django import forms
from .models import BankLoan, OfficeStaff, Company, FinancialYear

class BankLoanForm(forms.ModelForm):
    # This field will be used for inputting or displaying the EmpId string,
    # which we then resolve to an OfficeStaff object in clean_employee_emp_id.
    employee_emp_id = forms.CharField(
        label="Employee ID",
        max_length=50,
        help_text="Enter the Employee ID (e.g., E001)",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = BankLoan
        # Exclude ForeignKey fields (employee, company, financial_year) from 'fields' to handle them manually.
        # This gives more control over how related objects are presented in the form (e.g., via CharField for ID).
        fields = ['employee_emp_id', 'bank_name', 'branch', 'amount', 'installment', 'from_date', 'to_date']
        widgets = {
            'bank_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'branch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'installment': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'from_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'to_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            self.fields['employee_emp_id'].initial = self.instance.employee.emp_id
            # Assuming company and financial_year are determined by the application context
            # rather than direct form input for this specific loan.
            # If they were user selectable, they would be ModelChoiceFields.

    def clean_employee_emp_id(self):
        emp_id = self.cleaned_data['employee_emp_id']
        try:
            employee = OfficeStaff.objects.get(emp_id=emp_id)
        except OfficeStaff.DoesNotExist:
            raise forms.ValidationError("Employee ID does not exist. Please enter a valid Employee ID.")
        self.cleaned_data['employee'] = employee # Attach the actual employee object to cleaned_data
        return emp_id

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.employee = self.cleaned_data['employee']
        
        # For simplicity, assign to the first available company and financial year.
        # In a real application, these would come from the user's session, profile,
        # or be selected via a separate form field if the context is dynamic.
        try:
            instance.company = Company.objects.first()
            if not instance.company:
                raise forms.ValidationError("No companies configured. Please add a company record.")
        except Company.DoesNotExist:
            raise forms.ValidationError("No company found. Please ensure Company model has data.")

        try:
            instance.financial_year = FinancialYear.objects.first()
            if not instance.financial_year:
                raise forms.ValidationError("No financial years configured. Please add a financial year record.")
        except FinancialYear.DoesNotExist:
            raise forms.ValidationError("No financial year found. Please ensure FinancialYear model has data.")

        if commit:
            instance.save()
        return instance

from django import forms
from django.forms import modelformset_factory
from .models import BankLoan, OfficeStaff
import re

class EmployeeSearchForm(forms.Form):
    """
    Form for searching employees.
    """
    FIELD_CHOICES = [('0', 'Employee Name')] # Simplified as only '0' was truly active
    
    drp_field = forms.ChoiceField(
        choices=FIELD_CHOICES,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-48 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    txt_emp_name = forms.CharField(
        label="Employee Name",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-96 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 
                                       'placeholder': 'Start typing employee name...',
                                       'hx-get': '/hr/autocomplete/employee/', # HTMX for autocomplete suggestions
                                       'hx-trigger': 'keyup changed delay:500ms, search',
                                       'hx-target': '#employee-suggestions',
                                       'hx-swap': 'innerHTML',
                                       'autocomplete': 'off'}) # Disable native autocomplete
    )
    # The actual employee ID will be parsed from the selected value (e.g., "Name [ID]")
    # Or, preferably, passed as a hidden field if a more complex autocomplete is used.
    # For now, we'll assume `txt_emp_name` will contain "EmployeeName [EmpId]"
    # and the view logic will extract the EmpId.

    def clean_txt_emp_name(self):
        emp_name_with_id = self.cleaned_data.get('txt_emp_name', '')
        if emp_name_with_id:
            match = re.search(r'\[(.*?)\]$', emp_name_with_id)
            if match:
                return match.group(1) # Return just the EmpId
            else:
                # If no ID found, try to match by name, or raise error
                # For this migration, we'll assume the format is always "Name [ID]" if selected
                # Or handle partial matches, which is more complex.
                # Let's assume the autocomplete ensures the format is correct for now.
                # If it's a partial name search, we might need a different approach.
                # For consistency with ASP.NET's fun.getCode, we assume the format.
                raise forms.ValidationError("Please select a valid employee from the suggestions.")
        return None # No employee selected

class BankLoanForm(forms.ModelForm):
    """
    Form for a single BankLoan record, used within the formset.
    """
    # Checkbox to enable/disable row editing (will be handled by Alpine.js)
    # This field is not directly bound to the model.
    select_for_edit = forms.BooleanField(required=False, initial=False, 
                                         widget=forms.CheckboxInput(attrs={'x-model': 'isSelected', 'x-on:change': 'toggleFields'}))
    
    class Meta:
        model = BankLoan
        fields = ['bankname', 'branch', 'amount', 'installment', 'fromdate', 'todate']
        widgets = {
            'bankname': forms.TextInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'branch': forms.TextInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'amount': forms.NumberInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'installment': forms.NumberInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'fromdate': forms.DateInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected', 'type': 'date'}),
            'todate': forms.DateInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected', 'type': 'date'}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        is_selected_for_edit = cleaned_data.get('select_for_edit')
        
        # Only perform required field validation if the row is selected for edit
        if is_selected_for_edit:
            required_fields = ['bankname', 'branch', 'amount', 'installment', 'fromdate', 'todate']
            for field_name in required_fields:
                if not cleaned_data.get(field_name):
                    self.add_error(field_name, "This field is required.")
        return cleaned_data

# Formset for batch editing multiple BankLoan records
BankLoanFormSet = modelformset_factory(
    BankLoan,
    form=BankLoanForm,
    extra=0, # No extra blank forms by default
    can_delete=False # No delete functionality in original
)

from django import forms
from .models import OfferLetter, OfferAccessory, EmployeeType, Designation, DutyHour, OTHour, OverTimeType, IncludesIn, OfficeStaff
from decimal import Decimal
import re

class OfferLetterForm(forms.ModelForm):
    # Overriding fields to use specific models for dropdowns and for cleaner display
    staff_type = forms.ModelChoiceField(
        queryset=EmployeeType.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'change, keyup delay:300ms', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'})
    )
    type_of = forms.ChoiceField(
        choices=[('0', 'Select'), ('1', 'SAPL'), ('2', 'NEHA')],
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'change, keyup delay:300ms', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'})
    )
    designation = forms.ModelChoiceField(
        queryset=Designation.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    duty_hrs = forms.ModelChoiceField(
        queryset=DutyHour.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    ot_hrs = forms.ModelChoiceField(
        queryset=OTHour.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    overtime_applicable = forms.ModelChoiceField(
        queryset=OverTimeType.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    interviewed_by = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.all(),
        empty_label="Select Employee", # Simplified from AutocompleteExtender
        widget=forms.Select(attrs={'class': 'box3'})
    )
    authorized_by = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.all(),
        empty_label="Select Employee", # Simplified from AutocompleteExtender
        widget=forms.Select(attrs={'class': 'box3'})
    )

    class Meta:
        model = OfferLetter
        fields = [
            'title', 'employee_name', 'designation', 'duty_hrs', 'ot_hrs', 'overtime_applicable',
            'contact_no', 'address', 'email_id', 'staff_type', 'type_of',
            'interviewed_by', 'authorized_by', 'reference_by', 'gross_salary',
            'header_text', 'footer_text', 'remarks', 'increment_for_the_year', 'effect_from',
            'ex_gratia', 'vehicle_allowance', 'lta', 'loyalty', 'paid_leaves',
            'bonus', 'att_bonus_per1', 'att_bonus_per2', 'pf_employee_per', 'pf_company_per'
        ]
        widgets = {
            'title': forms.Select(choices=[('Mr.', 'Mr.'), ('Mrs.', 'Mrs.'), ('Miss.', 'Miss.')], attrs={'class': 'box3'}),
            'employee_name': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Employee Name'}),
            'contact_no': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Contact Number'}),
            'address': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'placeholder': 'Address'}),
            'email_id': forms.EmailInput(attrs={'class': 'box3', 'placeholder': 'Email Id'}),
            'reference_by': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Reference By'}),
            'gross_salary': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'header_text': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'placeholder': 'Header Text'}),
            'footer_text': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'placeholder': 'Footer Text'}),
            'remarks': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Remarks'}),
            'increment_for_the_year': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Ex- 2014 - 2015'}),
            'effect_from': forms.DateInput(attrs={'class': 'box3', 'type': 'date'}), # HTML5 date input
            
            # Monetary fields, include HTMX trigger for calculation
            'ex_gratia': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'vehicle_allowance': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'lta': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'loyalty': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'paid_leaves': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'bonus': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'att_bonus_per1': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'att_bonus_per2': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'pf_employee_per': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
            'pf_company_per': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'value': '0', 'hx-get': '/hr-offer/calculate-salary/', 'hx-trigger': 'keyup changed delay:300ms, change', 'hx-target': '#salary-calculation-results', 'hx-swap': 'innerHTML'}),
        }
        labels = {
            'gross_salary': 'Gross Salary',
            'att_bonus_per1': 'Attend Bonus - 1 (%)',
            'att_bonus_per2': 'Attend Bonus - 2 (%)',
            'pf_employee_per': 'PF-Employee (%)',
            'pf_company_per': 'PF-Company (%)',
            'ex_gratia': 'Ex Gratia',
            'vehicle_allowance': 'Vehicle Allowance',
            'lta': 'LTA',
            'loyalty': 'Loyalty Benefits',
            'paid_leaves': 'Paid Leaves',
            'bonus': 'Bonus (Monthly)',
            'header_text': 'Header',
            'footer_text': 'Footer',
            'increment_for_the_year': 'Increment For The Year',
            'effect_from': 'Effect From',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply general Tailwind/ERP CSS classes to all fields
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.CheckboxInput, forms.RadioSelect, forms.Select, forms.DateInput)):
                field.widget.attrs.update({'class': 'box3'})
            # Set default values for number fields as per ASP.NET
            if isinstance(field, forms.DecimalField) and field.initial is None:
                field.initial = Decimal('0.00')

    def clean(self):
        cleaned_data = super().clean()
        # Custom validations similar to ASP.NET RequiredFieldValidator and RegularExpressionValidator
        # Example: Email format validation (already handled by EmailField, but can be customized)
        email = cleaned_data.get('email_id')
        if email and not re.match(r"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$", email):
            self.add_error('email_id', 'Enter a valid email address.')
        
        # Example: Gross Salary validation (already handled by DecimalField, but can be customized)
        gross_salary = cleaned_data.get('gross_salary')
        if gross_salary is not None and not (Decimal('0') <= gross_salary <= Decimal('999999999999999.99')): # Max 15 digits integer, 3 decimal
             self.add_error('gross_salary', 'Gross Salary must be a valid number (max 15 digits integer, 2 decimal).')

        # Conditional validation for increment fields
        if self.instance and self.instance.increment > 0 or self.data.get('action') == 'increment':
            if not cleaned_data.get('increment_for_the_year'):
                self.add_error('increment_for_the_year', 'This field is required for increment letters.')
            if not cleaned_data.get('effect_from'):
                self.add_error('effect_from', 'This field is required for increment letters.')

        # Ensure mandatory fields for Update/Increment are filled
        if self.data.get('action') in ['update', 'increment']:
            required_fields = ['employee_name', 'address', 'gross_salary', 'header_text', 'footer_text']
            for field_name in required_fields:
                if not cleaned_data.get(field_name):
                    self.add_error(field_name, 'This field is required.')
            if cleaned_data.get('type_of') == '0':
                self.add_error('type_of', 'Please select a Type of Employee.')

        return cleaned_data

class OfferAccessoryForm(forms.ModelForm):
    includes_in = forms.ModelChoiceField(
        queryset=IncludesIn.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    class Meta:
        model = OfferAccessory
        fields = ['particulars', 'qty', 'amount', 'includes_in']
        widgets = {
            'particulars': forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Particulars'}),
            'qty': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'placeholder': 'Qty'}),
            'amount': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01', 'placeholder': 'Amount'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        qty = cleaned_data.get('qty')
        amount = cleaned_data.get('amount')

        # Validation similar to fun.NumberValidationQty
        if qty is not None and not (Decimal('0') <= qty <= Decimal('999999999999999.99')):
            self.add_error('qty', 'Quantity must be a valid number.')
        if amount is not None and not (Decimal('0') <= amount <= Decimal('999999999999999.99')):
            self.add_error('amount', 'Amount must be a valid number.')
        
        return cleaned_data

from django import forms
from .models import OfferMaster, EmployeeType, Designation, DutyHour, OfficeStaff

class OfferMasterForm(forms.ModelForm):
    # Fetch choices dynamically from lookup tables for dropdowns
    staff_type_id = forms.ModelChoiceField(
        queryset=EmployeeType.objects.all(),
        to_field_name='id', # Use 'id' as the value passed to the model field
        label="Employee Type",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    designation_id = forms.ModelChoiceField(
        queryset=Designation.objects.all(),
        to_field_name='id',
        label="Designation",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    duty_hrs_id = forms.ModelChoiceField(
        queryset=DutyHour.objects.all(),
        to_field_name='id',
        label="Duty Hours",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    interviewed_by_id = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.all(),
        to_field_name='emp_id',
        label="Interviewed By",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = OfferMaster
        fields = [
            'sys_date', 'type_of', 'staff_type_id', 'title', 'employee_name',
            'designation_id', 'duty_hrs_id', 'interviewed_by_id', 'contact_no',
            'salary', 'comp_id', 'fin_year_id'
        ]
        widgets = {
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'type_of': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'salary': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'sys_date': 'Date',
            'type_of': 'Type Of',
            'title': 'Title',
            'employee_name': 'Employee Name',
            'contact_no': 'Contact No',
            'salary': 'Gross Salary',
            'comp_id': 'Company ID',
            'fin_year_id': 'Financial Year ID',
        }

    # Custom validation example (if needed, e.g., for specific date ranges)
    def clean_salary(self):
        salary = self.cleaned_data['salary']
        if salary <= 0:
            raise forms.ValidationError("Salary must be a positive value.")
        return salary

# hrmodules/forms.py
from django import forms
from django.forms import inlineformset_factory
from django.core.validators import RegexValidator
from .models import (
    OfferLetter, Designation, DutyHour, OTHour, OverTime, 
    EmployeeType, IncludesIn, OfferAccessory, PF_Slab, OfficeStaff
)

# Common Tailwind CSS classes for form fields
COMMON_ATTRS = {
    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
    'x-model': '', # Placeholder for Alpine.js binding
}

class OfferLetterForm(forms.ModelForm):
    # Static choices for Title (Mr./Mrs./Miss.)
    TITLE_CHOICES = [
        ('Mr.', 'Mr.'),
        ('Mrs.', 'Mrs.'),
        ('Miss.', 'Miss.'),
    ]
    title = forms.ChoiceField(choices=TITLE_CHOICES, widget=forms.Select(attrs=COMMON_ATTRS))

    # Static choices for Type of Employee (SAPL/NEHA)
    TYPE_OF_CHOICES = [
        ('0', 'Select'), # InitialValue="0" in ASP.NET validator
        ('1', 'SAPL'),
        ('2', 'NEHA'),
    ]
    type_of = forms.ChoiceField(
        choices=TYPE_OF_CHOICES, 
        widget=forms.Select(attrs={**COMMON_ATTRS, 'hx-post': 'hx-post="{% url "hrmodules:calculate_salary_partial" %}" hx-target="#salaryBreakdown" hx-trigger="change delay:300ms, updateSalary"'})
    )

    # Auto-complete fields - we will render these as text inputs and handle autocomplete via HTMX
    interviewed_by_name = forms.CharField(
        label="Interviewed By", 
        required=True, 
        widget=forms.TextInput(attrs={
            **COMMON_ATTRS,
            'hx-get': "{% url 'hrmodules:staff_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms",
            'hx-target': "#interviewed-by-suggestions",
            'hx-swap': "innerHTML",
            'autocomplete': 'off',
            'id': 'Txtinterviewedby' # Match original ID for easier mapping
        })
    )
    authorized_by_name = forms.CharField(
        label="Authorized By", 
        required=True, 
        widget=forms.TextInput(attrs={
            **COMMON_ATTRS,
            'hx-get': "{% url 'hrmodules:staff_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms",
            'hx-target': "#authorized-by-suggestions",
            'hx-swap': "innerHTML",
            'autocomplete': 'off',
            'id': 'TxtAuthorizedby' # Match original ID for easier mapping
        })
    )

    # Email validation from ASP.NET RegularExpressionValidator
    email_regex_validator = RegexValidator(
        regex=r'^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$',
        message='Please enter a valid email address.'
    )
    email_id = forms.CharField(
        validators=[email_regex_validator],
        widget=forms.TextInput(attrs=COMMON_ATTRS)
    )

    # Numeric fields with regex validation from ASP.NET RegularExpressionValidator
    numeric_regex_validator = RegexValidator(
        regex=r'^\d{1,15}(\.\d{0,3})?$',
        message='Enter a valid number (up to 15 digits, 3 decimal places).'
    )
    
    salary = forms.FloatField(
        label="Gross Salary", 
        validators=[numeric_regex_validator],
        widget=forms.NumberInput(attrs={
            **COMMON_ATTRS, 
            'value': '0', # Default value
            'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 
            'hx-target': "#salaryBreakdown", 
            'hx-trigger': "change delay:300ms, updateSalary",
            'x-model': 'grossSalary', # Alpine.js binding
            'min': '0'
        })
    )
    # Other numeric fields from the form
    ex_gratia = forms.FloatField(label="Ex Gratia", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    vehicle_allowance = forms.FloatField(label="Vehicle Allowance", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    lta = forms.FloatField(label="LTA", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    loyalty = forms.FloatField(label="Loyalty Benefits", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    paid_leaves = forms.FloatField(label="Paid Leaves", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    pf_employee_perc = forms.FloatField(label="PF-Employee (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    pf_company_perc = forms.FloatField(label="PF-Company (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    att_bonus_per1 = forms.FloatField(label="Attend. Bonus - 1 (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    att_bonus_per2 = forms.FloatField(label="Attend. Bonus - 2 (%)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))
    bonus = forms.FloatField(label="Bonus (Monthly)", validators=[numeric_regex_validator], widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'value': '0', 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}", 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary", 'min': '0'}))


    class Meta:
        model = OfferLetter
        fields = [
            'title', 'employee_name', 'designation', 'duty_hrs', 'ot_hrs', 'overtime_applicable', 
            'type_of', 'staff_type', 'contact_no', 'address', 'email_id', 'reference_by', 
            'salary', 'ex_gratia', 'vehicle_allowance', 'lta', 'loyalty', 'paid_leaves',
            'pf_employee_perc', 'pf_company_perc', 'att_bonus_per1', 'att_bonus_per2', 'bonus',
            'header_text', 'footer_text', 'remarks'
        ]
        # Exclude interviewed_by and authorized_by from Meta.fields, will handle manually for string input to ID mapping
        # We manually define these two fields above.
        
        widgets = {
            'employee_name': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Enter Employee Name'}),
            'address': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 3, 'placeholder': 'Enter Address'}),
            'contact_no': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Enter Contact Number'}),
            'reference_by': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Reference By (Optional)'}),
            'header_text': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 4, 'placeholder': 'Offer letter header content'}),
            'footer_text': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 4, 'placeholder': 'Offer letter footer content'}),
            'remarks': forms.Textarea(attrs={**COMMON_ATTRS, 'rows': 2, 'placeholder': 'Any remarks?'}),
            'designation': forms.Select(attrs=COMMON_ATTRS),
            'duty_hrs': forms.Select(attrs=COMMON_ATTRS),
            'ot_hrs': forms.Select(attrs=COMMON_ATTRS),
            'overtime_applicable': forms.Select(attrs=COMMON_ATTRS),
            'staff_type': forms.Select(attrs={**COMMON_ATTRS, 'hx-post': "{% url 'hrmodules:calculate_salary_partial' %}" , 'hx-target': "#salaryBreakdown", 'hx-trigger': "change delay:300ms, updateSalary"})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dropdowns from database
        self.fields['designation'].queryset = Designation.objects.all()
        self.fields['duty_hrs'].queryset = DutyHour.objects.all()
        self.fields['ot_hrs'].queryset = OTHour.objects.all()
        self.fields['overtime_applicable'].queryset = OverTime.objects.all()
        self.fields['staff_type'].queryset = EmployeeType.objects.all()
        
        # Set initial values for auto-complete fields if instance exists
        if self.instance.pk:
            if self.instance.interviewed_by:
                self.fields['interviewed_by_name'].initial = f"{self.instance.interviewed_by.employee_name} [{self.instance.interviewed_by.emp_id}]"
            if self.instance.authorized_by:
                self.fields['authorized_by_name'].initial = f"{self.instance.authorized_by.employee_name} [{self.instance.authorized_by.emp_id}]"

        # Set initial PF Slab values for a new form
        if not self.instance.pk:
            pf_slab = PF_Slab.objects.filter(active=True).first()
            if pf_slab:
                self.fields['pf_employee_perc'].initial = pf_slab.pf_employee
                self.fields['pf_company_perc'].initial = pf_slab.pf_company
        
        # Initial Att Bonus based on TypeOf (SAPL/NEHA)
        # This will be handled by HTMX when TypeOf changes, but for initial load:
        if self.instance.type_of == 1: # SAPL
            self.fields['att_bonus_per1'].initial = 10
            self.fields['att_bonus_per2'].initial = 20
        elif self.instance.type_of == 2: # NEHA
            self.fields['att_bonus_per1'].initial = 5
            self.fields['att_bonus_per2'].initial = 15

        # Handle "Casuals" logic (disable/set to 0)
        if self.instance.staff_type and self.instance.staff_type.description == "Casuals":
            self.fields['bonus'].initial = 0
            self.fields['bonus'].widget.attrs['disabled'] = 'disabled'
            self.fields['att_bonus_per1'].initial = 0
            self.fields['att_bonus_per1'].widget.attrs['disabled'] = 'disabled'
            self.fields['att_bonus_per2'].initial = 0
            self.fields['att_bonus_per2'].widget.attrs['disabled'] = 'disabled'
            self.fields['pf_employee_perc'].initial = 0
            self.fields['pf_employee_perc'].widget.attrs['disabled'] = 'disabled'
            self.fields['pf_company_perc'].initial = 0
            self.fields['pf_company_perc'].widget.attrs['disabled'] = 'disabled'

    def clean(self):
        cleaned_data = super().clean()
        
        # Manually assign FKs for auto-complete fields
        interviewed_by_name = cleaned_data.get('interviewed_by_name')
        authorized_by_name = cleaned_data.get('authorized_by_name')

        if interviewed_by_name:
            try:
                emp_id = int(interviewed_by_name.split(' [')[-1][:-1]) # Extract ID from "Name [ID]"
                cleaned_data['interviewed_by'] = OfficeStaff.objects.get(emp_id=emp_id)
            except (ValueError, IndexError, OfficeStaff.DoesNotExist):
                self.add_error('interviewed_by_name', 'Invalid Interviewed By selection.')
        else:
            self.add_error('interviewed_by_name', 'This field is required.')

        if authorized_by_name:
            try:
                emp_id = int(authorized_by_name.split(' [')[-1][:-1])
                cleaned_data['authorized_by'] = OfficeStaff.objects.get(emp_id=emp_id)
            except (ValueError, IndexError, OfficeStaff.DoesNotExist):
                self.add_error('authorized_by_name', 'Invalid Authorized By selection.')
        else:
            self.add_error('authorized_by_name', 'This field is required.')

        # Required field validation for TypeOf (ASP.NET InitialValue="0")
        if cleaned_data.get('type_of') == '0':
            self.add_error('type_of', 'Please select a Type of Employee.')
            
        return cleaned_data

class OfferAccessoryForm(forms.ModelForm):
    numeric_regex_validator = RegexValidator(
        regex=r'^\d{1,15}(\.\d{0,3})?$',
        message='Enter a valid number (up to 15 digits, 3 decimal places).'
    )

    qty = forms.FloatField(label="Qty", validators=[numeric_regex_validator], 
                           widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'min': '0'}))
    amount = forms.FloatField(label="Amount", validators=[numeric_regex_validator], 
                              widget=forms.NumberInput(attrs={**COMMON_ATTRS, 'min': '0'}))

    class Meta:
        model = OfferAccessory
        fields = ['perticulars', 'qty', 'amount', 'includes_in']
        widgets = {
            'perticulars': forms.TextInput(attrs={**COMMON_ATTRS, 'placeholder': 'Perticulars'}),
            'includes_in': forms.Select(attrs=COMMON_ATTRS),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['includes_in'].queryset = IncludesIn.objects.all()


# Formset for handling multiple OfferAccessory instances related to an OfferLetter
OfferAccessoryFormSet = inlineformset_factory(
    OfferLetter,
    OfferAccessory,
    form=OfferAccessoryForm,
    extra=1, # One empty form initially
    can_delete=True,
    min_num=0,
    validate_min=False, # Allows form to be valid even if no accessories
)

from django import forms
from .models import SalaryDetail, SalaryMaster, Employee, Company, FinancialYear
import calendar

# A helper for month choices
MONTH_CHOICES = [(i, calendar.month_name[i]) for i in range(1, 13)]

class SalaryDetailForm(forms.ModelForm):
    # These fields correspond to the various textboxes in the ASP.NET form
    present = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Present days is required.'}
    )
    absent = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Absent days is required.'}
    )
    late_in = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Late in is required.'}
    )
    half_day = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Half day is required.'}
    )
    sunday = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Sunday is required.'}
    )
    coff = forms.DecimalField(
        max_digits=18,
    decimal_places=3,
    initial=0,
    required=True,
    widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
    error_messages={'required': 'C-off is required.'}
    )
    pl = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'PL is required.'}
    )
    over_time_hrs = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Over time hours is required.'}
    )
    installment = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[100px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Installment is required.'}
    )
    mobile_exe_amt = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[50px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Mobile excess amount is required.'}
    )
    addition = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[100px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Addition is required.'}
    )
    deduction = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[100px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Deduction is required.'}
    )
    remarks1 = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 w-[250px] h-[45px]', 'rows': 3}),
        required=False
    )
    remarks2 = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 w-[250px] h-[45px]', 'rows': 3}),
        required=False
    )

    class Meta:
        model = SalaryDetail
        fields = [
            'present', 'absent', 'late_in', 'half_day', 'sunday', 'coff', 'pl',
            'over_time_hrs', 'installment', 'mobile_exe_amt', 'addition',
            'remarks1', 'deduction', 'remarks2'
        ]

    # No need for specific RegexValidator as DecimalField handles numeric types
    # and required is handled by required=True and error_messages.

class MonthSelectionForm(forms.Form):
    fmonth = forms.ChoiceField(
        choices=MONTH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3',
            'hx-post': 'this.dataset.hxUrl', # Will be set dynamically by template
            'hx-trigger': 'change',
            'hx-target': '#salary_data_container',
            'hx-swap': 'innerHTML',
            'data-hx-url': '' # Placeholder to be filled in template
        }),
        label="Salary for the Month of",
        initial=timezone.now().month
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If there's a dynamic list of months, populate it here
        # For now, it's 1-12 as per general behavior
        pass

from django import forms
from .models import SalaryDetail

class SalaryDetailForm(forms.ModelForm):
    # Define fields explicitly to apply custom widgets and validation
    present = forms.DecimalField(
        label="Present", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    absent = forms.DecimalField(
        label="Absent", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    late_in = forms.DecimalField(
        label="Late In", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    half_day = forms.DecimalField(
        label="Half Day", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    sunday = forms.DecimalField(
        label="Sunday", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    coff = forms.DecimalField(
        label="C-off", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    pl = forms.DecimalField(
        label="PL", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    over_time_hrs = forms.DecimalField(
        label="Over Time Hrs.", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    installment = forms.DecimalField(
        label="Installment", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    mobile_exe_amt = forms.DecimalField(
        label="Exe.Amt", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    addition = forms.DecimalField(
        label="Addition", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    deduction = forms.DecimalField(
        label="Deduction", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    remarks1 = forms.CharField(
        label="Remarks", required=False,
        widget=forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'})
    )
    remarks2 = forms.CharField(
        label="Remarks", required=False,
        widget=forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'})
    )

    class Meta:
        model = SalaryDetail
        fields = [
            'present', 'absent', 'late_in', 'half_day', 'sunday', 'coff', 'pl',
            'over_time_hrs', 'installment', 'mobile_exe_amt', 'addition',
            'remarks1', 'deduction', 'remarks2'
        ]

from django import forms
from .models import OfficeStaff, Department, BusinessGroup, FinancialYear, Designation

class OfficeStaffForm(forms.ModelForm):
    class Meta:
        model = OfficeStaff
        # Include relevant fields for a hypothetical add/edit form
        fields = ['empid', 'title', 'employeename', 'finyearid', 'joiningdate', 
                  'department', 'bggroup', 'designation', 'resignationdate', 'compid']
        widgets = {
            'empid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employeename': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finyearid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'joiningdate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bggroup': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'designation': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'resignationdate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'empid': 'Employee ID',
            'employeename': 'Employee Name',
            'finyearid': 'Financial Year',
            'joiningdate': 'Joining Date',
            'department': 'Department Name',
            'bggroup': 'BG Group',
            'designation': 'Designation',
            'resignationdate': 'Resignation Date',
            'compid': 'Company ID',
        }

class OfficeStaffSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'Dept Name'),
        ('2', 'BG Group'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={'class': 'block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        initial='0',
        label="" # Hide label as it's part of the visual layout
    )
    search_text_mrs = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter search value', 'x-show': "searchField !== '0'", 'x-model': 'searchTextMrs'})
    )
    search_text_empname = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Employee Name', 'x-show': "searchField === '0'", 'x-model': 'searchTextEmpName', 'hx-get': "/hr/autocomplete_employees/", 'hx-trigger': "keyup changed delay:500ms", 'hx-target': "#autocomplete-results", 'hx-indicator': "#autocomplete-spinner"}),
    )
    
    # Hidden input to store the actual selected employee name (from autocomplete)
    # This is for form submission, the visible input is for user typing and HTMX
    selected_emp_id = forms.CharField(widget=forms.HiddenInput(), required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate choices for dropdowns from models if they were not ForeignKeys in OfficeStaff
        # For ForeignKeys, Django automatically handles choices.
        # This form is for search, so it doesn't need to bind to an OfficeStaff instance.

from django import forms
from .models import HrSalaryMaster

class HrSalaryMasterForm(forms.ModelForm):
    class Meta:
        model = HrSalaryMaster
        fields = ['emp_id', 'financial_month', 'company_id', 'financial_year_id', 'increment']
        widgets = {
            'emp_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_month': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'increment': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Example custom validation (if needed, based on ASP.NET logic)
    def clean_financial_month(self):
        month = self.cleaned_data['financial_month']
        if not (1 <= month <= 12):
            raise forms.ValidationError("Financial month must be between 1 and 12.")
        return month

from django import forms
from .models import SalaryDetail

class SalaryDetailForm(forms.ModelForm):
    class Meta:
        model = SalaryDetail
        fields = [
            'present', 'absent', 'late_in', 'half_day', 'sunday', 'coff', 'pl',
            'overtime_hrs', 'overtime_rate', 'installment', 'mobile_exe_amt',
            'addition', 'deduction'
        ]
        widgets = {
            'present': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'absent': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'late_in': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'half_day': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sunday': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'coff': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pl': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'overtime_hrs': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'overtime_rate': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'installment': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mobile_exe_amt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'addition': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'deduction': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

from django import forms
from .models import OfficeStaff, Department, BusinessGroup

class StaffSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'Dept Name'),
        ('2', 'BG Group'),
    ]
    
    # Initial 'Select' option from ASP.NET is not directly mapped here
    # It will be handled by UI logic in Alpine.js / HTMX to toggle input visibility
    
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Search By"
    )
    
    search_term_employee = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'id': 'TxtEmpName', # Matches ASP.NET ID for mental mapping
            'hx-get': '/hr_staff/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        }),
        label="" # Label handled by placeholder or layout
    )
    
    search_term_other = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search term',
            'id': 'TxtMrs', # Matches ASP.NET ID for mental mapping
        }),
        label=""
    )

class OfficeStaffForm(forms.ModelForm):
    class Meta:
        model = OfficeStaff
        fields = [
            'emp_id', 'employee_name', 'department', 'bg_group',
            'designation', 'mobile_no', 'joining_date', 'resignation_date'
        ]
        widgets = {
            'emp_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bg_group': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'designation': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mobile_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'joining_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'resignation_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
        labels = {
            'emp_id': 'Employee ID',
            'employee_name': 'Employee Name',
            'department': 'Department',
            'bg_group': 'Business Group',
        }
        
    def clean_emp_id(self):
        emp_id = self.cleaned_data['emp_id']
        # Example validation: ensure emp_id is unique if creating new
        if self.instance.pk is None and OfficeStaff.objects.filter(emp_id=emp_id).exists():
            raise forms.ValidationError("An employee with this ID already exists.")
        return emp_id

# hr_staff/forms.py

from django import forms
from django.core.validators import RegexValidator
from .models import (
    OfficeStaff, Designation, Department, SwapCard, CorporateMobileNo,
    BusinessGroup, Grade, IntercomExtension, OfferMaster
)

class OfficeStaffForm(forms.ModelForm):
    # Custom choices for fields that were hardcoded or had specific filtering logic
    GENDER_CHOICES = [
        ('Select', 'Select'), # Mimic InitialValue="Select"
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    BLOOD_GROUP_CHOICES = [
        ('Select', 'Select'),
        ('Not Known', 'Not Known'),
        ('O+', 'O+'), ('O-', 'O-'),
        ('A+', 'A+'), ('A-', 'A-'),
        ('B+', 'B+'), ('B-', 'B-'),
        ('AB+', 'AB+'), ('AB-', 'AB-'),
    ]
    
    TITLE_CHOICES = [
        ('Mr', 'Mr'),
        ('Mrs', 'Mrs'),
        ('Miss', 'Miss'),
    ]

    # Redefine fields to use specific widgets and initial data logic
    title = forms.ChoiceField(choices=TITLE_CHOICES)
    joining_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off', # Prevent browser autocomplete
        }),
        required=True
    )
    resignation_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off',
        }),
        required=False
    )
    date_of_birth = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off',
        }),
        required=True
    )
    passport_expiry_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'autocomplete': 'off',
        }),
        required=False
    )

    # Use BooleanField for radio buttons
    martial_status = forms.BooleanField(
        widget=forms.RadioSelect(choices=[(True, 'Married'), (False, 'Unmarried')]),
        required=False,
        initial=False # Default to Unmarried
    )
    physically_handycapped = forms.BooleanField(
        widget=forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')]),
        required=False,
        initial=False # Default to No
    )

    gender = forms.ChoiceField(
        choices=GENDER_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    blood_group = forms.ChoiceField(
        choices=BLOOD_GROUP_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # File fields
    photo = forms.ImageField(required=False)
    cv = forms.FileField(required=False)

    class Meta:
        model = OfficeStaff
        # Exclude auto-generated or system fields for direct form input
        exclude = [
            'offer', 'emp_id', 'sys_date', 'sys_time', 'fin_year_id',
            'comp_id', 'session_id', 'photo_file_name', 'photo_size', 'photo_content_type',
            'cv_file_name', 'cv_size', 'cv_content_type',
        ]
        # Map fields to their corresponding widgets with Tailwind CSS classes
        widgets = {
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'erp_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'permanent_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'correspondence_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'personal_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'height': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'weight': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'religion': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cast': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'educational_qualification': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'additional_qualification': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'last_company_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'working_duration': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_experience': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'current_ctc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_account_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'passport_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'additional_information': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'photo': forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}),
            'cv': forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}),
        }
        labels = {
            'erp_email': 'ERP-Mail',
            'personal_email': 'Personal Email',
            'passport_expiry_date': 'Passport Expiry Date',
            'contact_no': 'Contact No.',
            'corporate_mobile_no': 'Corp. Mobile No.',
            'swap_card': 'Swap Card No.',
            'business_group': 'Under BG',
            'director_name': 'Director of Dept.',
            'group_leader': 'Group Leader',
            'dept_head': 'Dept. Head',
            'joining_date': 'Joining Date',
            'resignation_date': 'Resignation Date',
            'date_of_birth': 'Date Of Birth',
            'martial_status': 'Martial Status',
            'physically_handycapped': 'Physically Handicapped',
            'educational_qualification': 'Educational Qualification',
            'additional_qualification': 'Additional Qualification',
            'last_company_name': 'Last Company Name',
            'total_experience': 'Total Experience',
            'working_duration': 'Working Duration',
            'current_ctc': 'Current CTC',
            'bank_account_no': 'Bank Acc No.',
            'pf_no': 'PF No.',
            'pan_no': 'PAN No.',
            'passport_no': 'Passport No.',
            'additional_information': 'Additional Information',
            'photo': 'Upload Photo',
            'cv': 'Upload CV',
        }

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None) # Pass comp_id from view
        offer_id = kwargs.pop('offer_id', None) # Pass offer_id from view
        super().__init__(*args, **kwargs)

        # Dynamically populate dropdowns
        self.fields['designation'].queryset = Designation.objects.all()
        self.fields['department'].queryset = Department.objects.all()
        self.fields['business_group'].queryset = BusinessGroup.objects.all()
        self.fields['grade'].queryset = Grade.objects.all()
        self.fields['extension_no'].queryset = IntercomExtension.objects.all()

        # Specific query for available swap cards
        # Mimic ASP.NET logic: 'Id=1' OR NOT IN (selected)
        used_swap_cards = OfficeStaff.objects.filter(comp_id=comp_id, swap_card__isnull=False).values_list('swap_card__id', flat=True)
        self.fields['swap_card'].queryset = SwapCard.objects.filter(
            forms.Q(id=1) | ~forms.Q(id__in=used_swap_cards)
        )
        self.fields['swap_card'].empty_label = "Select" # Mimic InitialValue="Select"

        # Specific query for available corporate mobile numbers
        # Mimic ASP.NET logic: 'Id=1' OR NOT IN (selected)
        used_mobile_nos = OfficeStaff.objects.filter(comp_id=comp_id, corporate_mobile_no__isnull=False).values_list('corporate_mobile_no__id', flat=True)
        self.fields['corporate_mobile_no'].queryset = CorporateMobileNo.objects.filter(
            forms.Q(id=1) | ~forms.Q(id__in=used_mobile_nos)
        )
        self.fields['corporate_mobile_no'].empty_label = "Select" # Mimic InitialValue="Select"

        # Dynamically populate Director, Dept Head, Group Leader based on Designation
        # Assuming Designation.id 2, 3 for Directors, 7 for Group Leaders, etc.
        # This will need an actual mapping for Designation IDs from tblHR_Designation.
        self.fields['director_name'].queryset = OfficeStaff.objects.filter(
            designation__id__in=[2, 3] # Assuming 2 & 3 are director designations
        )
        self.fields['director_name'].empty_label = "Select"

        self.fields['group_leader'].queryset = OfficeStaff.objects.filter(
            designation__id=7 # Assuming 7 is group leader designation
        )
        self.fields['group_leader'].empty_label = "Select"
        
        self.fields['dept_head'].queryset = OfficeStaff.objects.exclude(id=1) # Exclude user with ID 1
        self.fields['dept_head'].empty_label = "Select"

        # Set initial values for new staff from OfferMaster
        if offer_id and not self.instance.pk: # Only for new instances if offer_id is provided
            try:
                offer = OfferMaster.objects.get(offer_id=offer_id)
                self.fields['employee_name'].initial = offer.employee_name
                self.fields['title'].initial = offer.title
            except OfferMaster.DoesNotExist:
                pass # Offer not found, proceed without initial values

        # Set empty labels for all Foreign Key fields that are not initially required
        for field_name in ['designation', 'department', 'swap_card', 'corporate_mobile_no',
                           'business_group', 'director_name', 'dept_head', 'group_leader',
                           'grade', 'extension_no']:
            if self.fields[field_name].empty_label is None:
                self.fields[field_name].empty_label = "Select"

    def clean(self):
        cleaned_data = super().clean()
        
        # Replicate ASP.NET Email Validation
        company_email = cleaned_data.get('company_email')
        erp_email = cleaned_data.get('erp_email')
        personal_email = cleaned_data.get('personal_email')

        email_regex = r'^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$'
        if company_email and not RegexValidator(email_regex)(company_email):
            self.add_error('company_email', 'Enter a valid company email address.')
        if erp_email and not RegexValidator(email_regex)(erp_email):
            self.add_error('erp_email', 'Enter a valid ERP email address.')
        if personal_email and not RegexValidator(email_regex)(personal_email):
            self.add_error('personal_email', 'Enter a valid personal email address.')

        # Replicate ASP.NET Date Validation (format dd-MM-yyyy)
        # Django DateField handles basic date format validation, but specific regex
        # validation for 'dd-MM-yyyy' could be added if needed, though typically not for DateField.
        # Ensure dates are valid and not future if applicable (e.g. DOB)

        return cleaned_data

from django import forms
from .models import OfferMaster

class OfferMasterForm(forms.ModelForm):
    """
    Form for creating and updating OfferMaster records.
    """
    class Meta:
        model = OfferMaster
        fields = ['offer_id', 'employee_name', 'staff_type']
        widgets = {
            'offer_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Offer ID (e.g., 123)'
            }),
            'employee_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Employee Name'
            }),
            'staff_type': forms.TextInput(attrs={ # Could be a ChoiceField if StaffType is enum
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Staff Type (e.g., 1 for Office Staff)'
            }),
        }

    # Add custom validation methods here if needed, e.g., to ensure OfferId is unique
    # def clean_offer_id(self):
    #     offer_id = self.cleaned_data['offer_id']
    #     if self.instance.pk is None and OfferMaster.objects.filter(offer_id=offer_id).exists():
    #         raise forms.ValidationError("An offer with this ID already exists.")
    #     return offer_id

# hr/forms.py
from django import forms
from .models import (
    OfficeStaff, Designation, Department, BusinessGroup, Grade, IntercomExtension, 
    SwapCard, CorporateMobileNo
)
import io

class OfficeStaffForm(forms.ModelForm):
    # Custom form fields for specific rendering or complex logic
    # ChoiceField for radio buttons and dropdowns with specific choices
    title = forms.ChoiceField(
        choices=OfficeStaff.TITLE_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Title"
    )
    gender = forms.ChoiceField(
        choices=OfficeStaff.GENDER_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Gender"
    )
    # Boolean fields rendered as radio buttons
    marital_status = forms.BooleanField(
        required=False, # Required is handled by model validation
        widget=forms.RadioSelect(choices=[(True, 'Married'), (False, 'Unmarried')]),
        label="Martial Status"
    )
    physically_handicapped = forms.BooleanField(
        required=False,
        widget=forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')]),
        label="Physically Handicapped"
    )
    blood_group = forms.ChoiceField(
        choices=OfficeStaff.BLOOD_GROUP_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Blood Group"
    )

    # File Upload Fields: These are not directly mapped to BinaryField in model
    # They are used to accept file uploads and then manual handling in clean()
    photo_file = forms.FileField(
        required=False, 
        label="Upload Photo", 
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
        })
    )
    cv_file = forms.FileField(
        required=False, 
        label="Upload CV", 
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
        })
    )

    # Hidden fields to indicate if existing binary data should be preserved if no new file is uploaded
    _photo_data_unchanged = forms.CharField(widget=forms.HiddenInput(), required=False)
    _cv_data_unchanged = forms.CharField(widget=forms.HiddenInput(), required=False)


    class Meta:
        model = OfficeStaff
        fields = [
            # Official Info Tab
            'emp_id', 'offer_id', 'title', 'employee_name', 'swap_card_no', 
            'department', 'bg_group', 'directors_name', 'dept_head', 'group_leader',
            'designation', 'grade', 'mobile_no', 'contact_no', 'company_email',
            'erp_mail', 'extension_no', 'joining_date', 'resignation_date',
            'photo_file', '_photo_data_unchanged', # Custom file fields, original model fields will be hidden
            # Personal Info Tab
            'permanent_address', 'correspondence_address', 'personal_email', 'date_of_birth',
            'gender', 'marital_status', 'blood_group', 'height', 'weight',
            'physically_handicapped', 'religion', 'caste',
            # Edu. Quali. & Work Experience Tab
            'educational_qualification', 'additional_qualification', 'last_company_name',
            'working_duration', 'total_experience',
            # Others Tab
            'current_ctc', 'bank_account_no', 'pf_no', 'pan_no', 'passport_no',
            'expiry_date', 'additional_information',
            'cv_file', '_cv_data_unchanged', # Custom file fields, original model fields will be hidden
        ]
        # Common CSS class for all text inputs and select elements
        common_input_attrs = {'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
        # Specific widgets with custom styles
        widgets = {
            'emp_id': forms.TextInput(attrs={**common_input_attrs, 'readonly': 'readonly'}),
            'offer_id': forms.TextInput(attrs={**common_input_attrs, 'readonly': 'readonly'}),
            'employee_name': forms.TextInput(attrs={**common_input_attrs, 'style': 'width:300px'}),
            'contact_no': forms.TextInput(attrs=common_input_attrs),
            'company_email': forms.EmailInput(attrs={**common_input_attrs, 'style': 'width:200px'}),
            'erp_mail': forms.EmailInput(attrs=common_input_attrs),
            'joining_date': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}), 
            'resignation_date': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}),
            'permanent_address': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:374px; height:60px'}),
            'correspondence_address': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:408px; height:60px'}),
            'personal_email': forms.EmailInput(attrs={**common_input_attrs, 'style': 'width:200px'}),
            'date_of_birth': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}),
            'height': forms.TextInput(attrs=common_input_attrs),
            'weight': forms.TextInput(attrs=common_input_attrs),
            'religion': forms.TextInput(attrs=common_input_attrs),
            'caste': forms.TextInput(attrs=common_input_attrs),
            'educational_qualification': forms.TextInput(attrs=common_input_attrs),
            'additional_qualification': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:336px; height:68px'}),
            'last_company_name': forms.TextInput(attrs={**common_input_attrs, 'style': 'width:250px'}),
            'working_duration': forms.TextInput(attrs=common_input_attrs),
            'total_experience': forms.TextInput(attrs=common_input_attrs),
            'current_ctc': forms.TextInput(attrs=common_input_attrs),
            'bank_account_no': forms.TextInput(attrs=common_input_attrs),
            'pf_no': forms.TextInput(attrs=common_input_attrs),
            'pan_no': forms.TextInput(attrs=common_input_attrs),
            'passport_no': forms.TextInput(attrs=common_input_attrs),
            'expiry_date': forms.DateInput(attrs={**common_input_attrs, 'type': 'date', 'readonly': 'readonly'}),
            'additional_information': forms.Textarea(attrs={**common_input_attrs, 'style': 'width:325px; height:66px'}),

            # Foreign Key dropdowns apply common_input_attrs
            'designation': forms.Select(attrs={**common_input_attrs, 'style': 'width:180px'}),
            'department': forms.Select(attrs=common_input_attrs),
            'bg_group': forms.Select(attrs=common_input_attrs),
            'directors_name': forms.Select(attrs=common_input_attrs),
            'dept_head': forms.Select(attrs={**common_input_attrs, 'style': 'width:180px'}),
            'group_leader': forms.Select(attrs=common_input_attrs),
            'grade': forms.Select(attrs=common_input_attrs),
            'swap_card_no': forms.Select(attrs=common_input_attrs),
            'mobile_no': forms.Select(attrs=common_input_attrs),
            'extension_no': forms.Select(attrs=common_input_attrs),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get('instance')
        comp_id = instance.comp_id if instance else None # Assuming comp_id from the instance
        
        # Populate self-referential dropdowns (Directors, Dept Heads, Group Leaders)
        # Assuming they should show all staff members for selection within the same company
        if comp_id:
            staff_members_qs = OfficeStaff.objects.filter(comp_id=comp_id).order_by('employee_name')
            self.fields['directors_name'].queryset = staff_members_qs
            self.fields['dept_head'].queryset = staff_members_qs
            self.fields['group_leader'].queryset = staff_members_qs
        else:
            # If no instance (e.g., in a create view, though this is an edit form),
            # provide an empty queryset or a default
            self.fields['directors_name'].queryset = OfficeStaff.objects.none()
            self.fields['dept_head'].queryset = OfficeStaff.objects.none()
            self.fields['group_leader'].queryset = OfficeStaff.objects.none()
        
        # Logic for Swap Card No and Corporate Mobile No dropdowns
        # The ASP.NET logic was: "Id='1' OR Id='[current_id]' OR Id NOT IN (select [FK_column] from tblHR_OfficeStaff where [FK_column] is not null AND CompId='[CompId]')"
        # This translates to: Include 'Not Applicable' (assuming ID=1), the current staff's assigned one, and all other unassigned ones.
        
        current_swap_card_id = instance.swap_card_no.id if instance and instance.swap_card_no else None
        current_mobile_no_id = instance.mobile_no.id if instance and instance.mobile_no else None

        # Get IDs of swap cards currently assigned to other staff members in the same company
        used_swap_card_ids = OfficeStaff.objects.filter(
            comp_id=comp_id, 
            swap_card_no__isnull=False
        ).exclude(pk=instance.pk).values_list('swap_card_no', flat=True)
        
        # Construct queryset: ID=1 (NA) OR current staff's assigned ID OR IDs not in used list
        swap_card_qs = SwapCard.objects.filter(id=1) 
        if current_swap_card_id:
            swap_card_qs = swap_card_qs.union(SwapCard.objects.filter(id=current_swap_card_id))
        swap_card_qs = swap_card_qs.union(SwapCard.objects.exclude(id__in=used_swap_card_ids))
        self.fields['swap_card_no'].queryset = swap_card_qs.distinct().order_by('id')

        # Get IDs of corporate mobile numbers currently assigned to other staff members
        used_mobile_no_ids = OfficeStaff.objects.filter(
            comp_id=comp_id, 
            mobile_no__isnull=False
        ).exclude(pk=instance.pk).values_list('mobile_no', flat=True)

        # Construct queryset for mobile numbers
        mobile_no_qs = CorporateMobileNo.objects.filter(id=1) 
        if current_mobile_no_id:
            mobile_no_qs = mobile_no_qs.union(CorporateMobileNo.objects.filter(id=current_mobile_no_id))
        mobile_no_qs = mobile_no_qs.union(CorporateMobileNo.objects.exclude(id__in=used_mobile_no_ids))
        self.fields['mobile_no'].queryset = mobile_no_qs.distinct().order_by('id')

        # Set initial values for boolean fields (marital_status, physically_handicapped)
        # Django's form typically handles this, but explicit initial setting ensures correctness
        if instance:
            self.fields['marital_status'].initial = instance.marital_status
            self.fields['physically_handicapped'].initial = instance.physically_handicapped

        # For file fields, ensure that if no new file is uploaded, the existing binary data is retained
        # This is managed by passing `_photo_data_unchanged` and `_cv_data_unchanged` initial values
        if self.instance.pk:
            if self.instance.photo_data:
                self.initial['_photo_data_unchanged'] = 'True'
            if self.instance.cv_data:
                self.initial['_cv_data_unchanged'] = 'True'
            
    def clean(self):
        cleaned_data = super().clean()
        
        # Handle file uploads and binary data storage (Photo)
        photo_file = cleaned_data.get('photo_file')
        if photo_file:
            # Read the uploaded file into binary data
            photo_data = photo_file.read()
            cleaned_data['photo_file_name'] = photo_file.name
            cleaned_data['photo_size'] = photo_file.size
            cleaned_data['photo_content_type'] = photo_file.content_type
            cleaned_data['photo_data'] = photo_data
        elif self.initial.get('_photo_data_unchanged') == 'True' and not photo_file and self.instance.photo_data:
            # If no new file was uploaded AND there was existing data, retain it
            cleaned_data['photo_file_name'] = self.instance.photo_file_name
            cleaned_data['photo_size'] = self.instance.photo_size
            cleaned_data['photo_content_type'] = self.instance.photo_content_type
            cleaned_data['photo_data'] = self.instance.photo_data
        else:
            # If no new file and no existing data, or if `_photo_data_unchanged` is not 'True' (e.g., file removed)
            cleaned_data['photo_file_name'] = None
            cleaned_data['photo_size'] = None
            cleaned_data['photo_content_type'] = None
            cleaned_data['photo_data'] = None

        # Handle file uploads and binary data storage (CV)
        cv_file = cleaned_data.get('cv_file')
        if cv_file:
            cv_data = cv_file.read()
            cleaned_data['cv_file_name'] = cv_file.name
            cleaned_data['cv_size'] = cv_file.size
            cleaned_data['cv_content_type'] = cv_file.content_type
            cleaned_data['cv_data'] = cv_data
        elif self.initial.get('_cv_data_unchanged') == 'True' and not cv_file and self.instance.cv_data:
            cleaned_data['cv_file_name'] = self.instance.cv_file_name
            cleaned_data['cv_size'] = self.instance.cv_size
            cleaned_data['cv_content_type'] = self.instance.cv_content_type
            cleaned_data['cv_data'] = self.instance.cv_data
        else:
            cleaned_data['cv_file_name'] = None
            cleaned_data['cv_size'] = None
            cleaned_data['cv_content_type'] = None
            cleaned_data['cv_data'] = None

        return cleaned_data

from django import forms
from .models import OfficeStaff, Department, BusinessGroup, Designation, Grade, CorporateMobileNo, IntercomExt, SwapCard

class OfficeStaffForm(forms.ModelForm):
    # Dynamically populate choices for FKs if needed, or rely on default ModelChoiceField behavior
    department_fk = forms.ModelChoiceField(
        queryset=Department.objects.all(),
        required=False,
        label="Department",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    bggroup_fk = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False,
        label="Business Group",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    designation_fk = forms.ModelChoiceField(
        queryset=Designation.objects.all(),
        required=False,
        label="Designation",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    grade_fk = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        label="Grade",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    mobile_no_fk = forms.ModelChoiceField(
        queryset=CorporateMobileNo.objects.all(),
        required=False,
        label="Corporate Mobile No.",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    extension_no_fk = forms.ModelChoiceField(
        queryset=IntercomExt.objects.all(),
        required=False,
        label="Intercom Extension No.",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    swap_card_no_fk = forms.ModelChoiceField(
        queryset=SwapCard.objects.all(),
        required=False,
        label="Swap Card No.",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = OfficeStaff
        # Include all fields that are typically editable for an OfficeStaff member
        fields = [
            'title', 'employee_name', 'userid', 'photo_file_name', 'department_fk',
            'bggroup_fk', 'designation_fk', 'grade_fk', 'mobile_no_fk',
            'extension_no_fk', 'contact_no', 'company_email', 'email_id1', 'email_id2',
            'joining_date', 'resignation_date', 'date_of_birth', 'permanent_address',
            'correspondence_address', 'gender', 'martial_status', 'blood_group',
            'physically_handycapped', 'height', 'weight', 'religion', 'cast',
            'educational_qualification', 'additional_qualification', 'last_company_name',
            'working_duration', 'total_experience', 'current_ctc', 'bank_account_no',
            'pf_no', 'pan_no', 'pass_port_no', 'expiry_date', 'additional_information',
            'dept_head_userid', 'group_leader_userid', 'directors_name_userid', 'swap_card_no_fk' # FKs
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'photo_file_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email_id1': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email_id2': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'joining_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'resignation_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'date_of_birth': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'permanent_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'correspondence_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'gender': forms.Select(choices=[('Male', 'Male'), ('Female', 'Female'), ('Other', 'Other')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'martial_status': forms.Select(choices=[(1, 'Married'), (0, 'Unmarried')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'blood_group': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'physically_handycapped': forms.Select(choices=[(1, 'Yes'), (0, 'No')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'height': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'weight': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'religion': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cast': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'educational_qualification': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
            'additional_qualification': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
            'last_company_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'working_duration': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_experience': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'current_ctc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_account_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pass_port_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'additional_information': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
            'dept_head_userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'group_leader_userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'directors_name_userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    # Custom validation example (e.g., if employee_name must be unique or conform to a pattern)
    def clean_employee_name(self):
        name = self.cleaned_data['employee_name']
        # Add your validation logic here, e.g.,
        # if not name.isalpha():
        #     raise forms.ValidationError("Employee name must contain only alphabetic characters.")
        return name

from django import forms
from .models import NewsNotice
from django.core.exceptions import ValidationError

class NewsNoticeForm(forms.ModelForm):
    # Add a FileField for upload, as BinaryField doesn't directly map to file uploads
    # This field is not bound to the model directly, will be processed manually in view/model.
    uploaded_file = forms.FileField(
        label="Upload File",
        required=False,
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 '
                     'file:rounded-md file:border-0 file:text-sm file:font-semibold '
                     'file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
        })
    )

    class Meta:
        model = NewsNotice
        fields = ['title', 'in_details', 'from_date', 'to_date'] # Exclude file fields for direct mapping, handle separately
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter news title'
            }),
            'in_details': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-32',
                'placeholder': 'Enter news description'
            }),
            'from_date': forms.DateInput(attrs={
                'type': 'date', # HTML5 date picker
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'to_date': forms.DateInput(attrs={
                'type': 'date', # HTML5 date picker
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }
        labels = {
            'title': 'Title',
            'in_details': 'Description',
            'from_date': 'Date on Display (From)',
            'to_date': 'Date on Display (To)',
        }

    def clean(self):
        """
        Custom validation for date range.
        Replicates implicit validation from ASP.NET for FromDate <= ToDate.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', ValidationError('To Date cannot be earlier than From Date.'))
        return cleaned_data

from django import forms
from .models import MobileBill, TaxMaster
from decimal import Decimal

class MonthSelectionForm(forms.Form):
    """
    Form for selecting the bill month.
    """
    bill_month = forms.ChoiceField(
        label="Month Of Bill",
        choices=[], # Populated dynamically in view
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )

    def __init__(self, *args, **kwargs):
        months_choices = kwargs.pop('months_choices', [])
        super().__init__(*args, **kwargs)
        self.fields['bill_month'].choices = months_choices

class MobileBillRowForm(forms.ModelForm):
    """
    Form for a single row in the mobile bill grid, for editing BillAmt and Taxes.
    This will be used in a formset.
    """
    # Override fields to apply styling and validation specific to the UI
    bill_amt = forms.DecimalField(
        label="Bill Amt",
        required=False, # It becomes required when checkbox is checked, handled client-side by Alpine.js
        max_digits=18,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Bill Amount',
            'x-bind:class': "{ 'border-red-500': form.errors.bill_amt }", # Alpine.js for error styling
            'x-show': 'row.isChecked', # Alpine.js to show/hide
        })
    )
    taxes = forms.ModelChoiceField(
        queryset=TaxMaster.objects.all(),
        label="Taxes",
        required=False,
        empty_label="Select Tax",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-show': 'row.isChecked',
        })
    )
    
    # Custom fields for handling data from the aggregated query, not direct model fields
    # These fields are for display and contextual data within the formset, not direct save.
    user_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    emp_id = forms.CharField(widget=forms.HiddenInput(), required=True) # Important for identifying the bill to update
    employee_name = forms.CharField(label="Emp Name", widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm'}), required=False)
    mobile_no = forms.CharField(label="Mobile No", widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm'}), required=False)
    limit_amt = forms.DecimalField(label="Limit Amt", max_digits=18, decimal_places=2, widget=forms.NumberInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm'}), required=False)
    excess_amount = forms.DecimalField(label="Excess Amount", max_digits=18, decimal_places=2, widget=forms.NumberInput(attrs={'readonly': 'readonly', 'class': 'bg-gray-100 block w-full px-3 py-2 border border-gray-300 rounded-md sm:text-sm', 'x-show': 'row.isChecked'}), required=False)
    
    # Checkbox for enabling/disabling edit fields, driven by Alpine.js
    is_checked = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500',
            'x-model': 'row.isChecked', # Alpine.js model binding
            'hx-post': 'this.dataset.url', # Trigger post to update local state if needed
            'hx-trigger': 'change',
            'hx-swap': 'none', # No swap, Alpine.js handles UI
        })
    )
    
    # Field to hold the actual MobileBill DB ID, for updates
    mobile_bill_db_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)


    class Meta:
        model = MobileBill
        # These are the *only* fields that directly map to MobileBill model
        fields = ['bill_amt', 'taxes_fk']
        labels = {
            'taxes_fk': 'Taxes',
        }
    
    def __init__(self, *args, **kwargs):
        initial_data = kwargs.pop('initial', {})
        super().__init__(*args, **kwargs)

        # Populate non-model fields with initial data
        self.fields['user_id'].initial = initial_data.get('user_id')
        self.fields['emp_id'].initial = initial_data.get('emp_id')
        self.fields['employee_name'].initial = initial_data.get('employee_name')
        self.fields['mobile_no'].initial = initial_data.get('mobile_no')
        self.fields['limit_amt'].initial = initial_data.get('limit_amt')
        self.fields['excess_amount'].initial = initial_data.get('excess_amount')
        self.fields['is_checked'].initial = initial_data.get('has_existing_bill', False)
        self.fields['mobile_bill_db_id'].initial = initial_data.get('mobile_bill_db_id')

        # Conditional validation based on is_checked
        # This will be handled in the view or formset clean method for batch validation.
        # Here we only set initial states for styling.
        if initial_data.get('has_existing_bill'):
            # If a bill exists, inputs are initially visible and required
            self.fields['bill_amt'].required = True
            self.fields['taxes'].required = True
        else:
            # If no bill exists, checkbox is visible, inputs are hidden and not required by default
            # They become required when checkbox is checked.
            self.fields['is_checked'].widget.attrs['class'] += ' inline-block' # Make sure checkbox is visible
            self.fields['bill_amt'].widget.attrs['x-show'] = 'row.isChecked'
            self.fields['taxes'].widget.attrs['x-show'] = 'row.isChecked'
            self.fields['excess_amount'].widget.attrs['x-show'] = 'row.isChecked'


    def clean(self):
        cleaned_data = super().clean()
        is_checked = cleaned_data.get('is_checked')
        bill_amt = cleaned_data.get('bill_amt')
        taxes = cleaned_data.get('taxes')

        # Apply required validation if checkbox is checked
        if is_checked:
            if not bill_amt:
                self.add_error('bill_amt', 'Bill Amount is required when checked.')
            if not taxes:
                self.add_error('taxes', 'Taxes is required when checked.')
            
            # Additional regex validation from ASP.NET: ^\d{1,15}(\.\d{0,3})?$
            # DecimalField handles basic numeric validation.
            # max_digits and decimal_places constrain the format, but 3 decimal places
            # might need custom validation if DecimalField(decimal_places=2) is too strict.
            # Assuming 2 decimal places for Currency in Django DecimalField.
            if bill_amt is not None and not isinstance(bill_amt, Decimal):
                 # This check should be redundant if forms.DecimalField works as expected,
                 # but mimics ASP.NET's type check and regex.
                try:
                    Decimal(str(bill_amt)) # Attempt conversion to ensure numeric
                except ValueError:
                    self.add_error('bill_amt', 'Bill Amount must be a valid number.')
        
        return cleaned_data


# Create a formset for batch editing multiple MobileBill instances
MobileBillFormSet = forms.formset_factory(
    MobileBillRowForm,
    extra=0, # No extra blank forms by default
    can_delete=False # Not deleting, only updating/creating
)

# hr_reports/forms.py
from django import forms
from .models import MobileBill, OfficeStaff, CoporateMobileNo, ExciseMaster, Company

class MobileBillForm(forms.ModelForm):
    # Add a custom field for the corporate mobile number to allow selection/input
    # This might be more complex if corporate mobile is directly managed here,
    # but for simplicity, we'll assume it's pre-existing.
    # The ForeignKey in MobileBill points to OfficeStaff, so we need to select an employee.
    
    class Meta:
        model = MobileBill
        fields = ['employee', 'bill_amount', 'taxes', 'bill_month', 'financial_year_id', 'company']
        widgets = {
            'employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'taxes': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_month': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., Jan-2023'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Custom validation example (e.g., ensure bill_month format)
    def clean_bill_month(self):
        bill_month = self.cleaned_data['bill_month']
        # Simple regex check for Month-YYYY format, e.g., "Jan-2023"
        import re
        if not re.match(r'^[A-Za-z]{3}-\d{4}$', bill_month):
            raise forms.ValidationError("Bill month must be in 'MMM-YYYY' format (e.g., Jan-2023).")
        return bill_month

from django import forms
from .models import NewsNotice, FinancialYear

class NewsNoticeForm(forms.ModelForm):
    # If FinYear needs to be selected by the user, provide a dropdown
    finyear = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(),
        empty_label="Select Financial Year",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    class Meta:
        model = NewsNotice
        fields = ['title', 'fromdate_str', 'todate_str', 'filename', 'finyear'] # 'compid' would be set by view/middleware
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fromdate_str': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'}),
            'todate_str': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'}),
            'filename': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'fromdate_str': 'From Date (DD-MM-YYYY)',
            'todate_str': 'To Date (DD-MM-YYYY)',
            'finyear': 'Financial Year',
        }

    def clean(self):
        cleaned_data = super().clean()
        from_date_str = cleaned_data.get('fromdate_str')
        to_date_str = cleaned_data.get('todate_str')

        # Custom validation for date format
        try:
            if from_date_str:
                datetime.strptime(from_date_str, '%d-%m-%Y')
        except ValueError:
            self.add_error('fromdate_str', 'Invalid date format. Please use DD-MM-YYYY.')
        
        try:
            if to_date_str:
                datetime.strptime(to_date_str, '%d-%m-%Y')
        except ValueError:
            self.add_error('todate_str', 'Invalid date format. Please use DD-MM-YYYY.')

        return cleaned_data

from django import forms
from .models import MobileBill, ExciseServiceTax
from django.db.models import Max

class MobileBillForm(forms.ModelForm):
    # Additional fields if needed for context (e.g., employee name, limit)
    # These would be read-only or used for display purposes, not for saving directly.

    class Meta:
        model = MobileBill
        # 'employee', 'bill_month', 'comp_id', 'fin_year_id' should be handled by view context
        # 'sys_date', 'sys_time', 'session_id' will be set automatically by view/model
        fields = ['bill_amount', 'tax']
        widgets = {
            'bill_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter Bill Amount',
                'min': '0', # Add HTML5 validation
                'step': '0.01'
            }),
            'tax': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate the tax dropdown with live excise service taxes
        self.fields['tax'].queryset = ExciseServiceTax.objects.filter(live=True).order_by('value')
        # Set a default value if available, mimicking the ASP.NET behavior
        default_tax = ExciseServiceTax.objects.filter(live=True).first()
        if default_tax:
            self.fields['tax'].initial = default_tax.id

    def clean_bill_amount(self):
        bill_amount = self.cleaned_data['bill_amount']
        if bill_amount < 0:
            raise forms.ValidationError("Bill amount cannot be negative.")
        return bill_amount
    
    # This form is designed for a single MobileBill.
    # For bulk operations, the view will process individual form data or use a formset if needed.

# Month selection form (not a ModelForm)
class BillMonthForm(forms.Form):
    # Dummy method to get months, replace with actual DB retrieval or utility
    def get_months_for_dropdown():
        # In a real app, this would come from a lookup table or calculated based on financial year
        # For this example, we'll use a simple range
        return [(i, date(2000, i, 1).strftime('%B')) for i in range(1, 13)]
        
    bill_month = forms.ChoiceField(
        choices=get_months_for_dropdown(),
        widget=forms.Select(attrs={
            'id': 'id_bill_month', # Important for HTMX targeting
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': '{{% url "hr_mobile_bills:mobilebill_table" %}}', # HTMX endpoint
            'hx-target': '#mobileBillTable-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
            'name': 'bill_month', # Ensure name is set for POST/GET data
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add a "Select" option if it was in original ASP.NET dropdown.
        # This will need to be handled by the view to prevent data loading if "Select" is chosen.
        # choices = [('', 'Select')] + list(self.fields['bill_month'].choices)
        # self.fields['bill_month'].choices = choices

from django import forms
from .models import NewsNotice
from django.core.validators import RegexValidator

class NewsNoticeForm(forms.ModelForm):
    # Add a separate FileField for upload, which will be processed in the model's save method
    upload_file = forms.FileField(
        required=False,
        label="Upload File",
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none',
            'hx-post': 'true', # Example for HTMX if file upload needs special handling (not directly used here)
        })
    )

    class Meta:
        model = NewsNotice
        fields = ['title', 'in_details', 'from_date', 'to_date'] # Exclude system and file data fields
        # Widgets with Tailwind CSS classes
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter news title',
            }),
            'in_details': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-28',
                'placeholder': 'Enter news description',
            }),
            'from_date': forms.DateInput(
                attrs={
                    'type': 'date', # HTML5 date picker
                    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                    'readonly': 'readonly' # Mimic ASP.NET readonly attribute
                },
                format='%Y-%m-%d' # Format for HTML5 date input value
            ),
            'to_date': forms.DateInput(
                attrs={
                    'type': 'date', # HTML5 date picker
                    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                    'readonly': 'readonly' # Mimic ASP.NET readonly attribute
                },
                format='%Y-%m-%d' # Format for HTML5 date input value
            ),
        }

    # Custom clean methods for date validation and consistency, mirroring ASP.NET RegExValidator
    def clean_from_date(self):
        from_date = self.cleaned_data['from_date']
        # No explicit RegExValidator needed if using type="date" and Django's DateInput.
        # Django's DateField handles basic date validity.
        # If the input format was dd-MM-yyyy for text input, input_formats would be crucial.
        # Since we use type="date", browsers enforce YYYY-MM-DD input.
        return from_date

    def clean_to_date(self):
        to_date = self.cleaned_data['to_date']
        from_date = self.cleaned_data.get('from_date')

        if from_date and to_date and to_date < from_date:
            raise forms.ValidationError("To Date cannot be earlier than From Date.")
        return to_date