from django.urls import path
from .views import EmployeeListView, EmployeeCreateView, EmployeeUpdateView, EmployeeDeleteView, EmployeeTablePartialView

urlpatterns = [
    path('employees/', EmployeeListView.as_view(), name='employee_list'),
    path('employees/add/', EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/edit/<int:pk>/', EmployeeUpdateView.as_view(), name='employee_edit'),
    path('employees/delete/<int:pk>/', EmployeeDeleteView.as_view(), name='employee_delete'),
    # HTMX-specific endpoint to render just the table for refresh
    path('employees/table/', EmployeeTablePartialView.as_view(), name='employee_table'),
]

from django.urls import path
from .views import ReportListView, ReportCreateView, ReportUpdateView, ReportDeleteView, ReportTablePartialView

app_name = 'hr_reports' # Namespace for this application's URLs

urlpatterns = [
    path('reports/', ReportListView.as_view(), name='report_list'),
    path('reports/table/', ReportTablePartialView.as_view(), name='report_table'), # HTMX endpoint for table content
    path('reports/add/', ReportCreateView.as_view(), name='report_add'),
    path('reports/edit/<int:pk>/', ReportUpdateView.as_view(), name='report_edit'),
    path('reports/delete/<int:pk>/', ReportDeleteView.as_view(), name='report_delete'),
]

from django.urls import path
from .views import OfficeStaffListView, SubCriteriaOptionsView, OfficeStaffTablePartialView, EmployeeAutocompleteView, OfficeStaffDetailRedirectView

app_name = 'hr_reports'

urlpatterns = [
    path('multiple-reports/', OfficeStaffListView.as_view(), name='office_staff_list'),
    # HTMX endpoints
    path('sub-criteria-options/', SubCriteriaOptionsView.as_view(), name='sub_criteria_options'),
    path('office-staff-table/', OfficeStaffTablePartialView.as_view(), name='office_staff_table'),
    path('employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    path('office-staff-details/<str:emp_id>/', OfficeStaffDetailRedirectView.as_view(), name='office_staff_detail_redirect'),
    
    # Placeholder for the actual detail view, as it's outside this module
    # path('hr/transactions/office_staff_print_details/<str:emp_id>/', YourOfficeStaffDetailView.as_view(), name='office_staff_details'),
    # Make sure 'office_staff_details' points to the correct URL in your actual project
]

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime
from unittest.mock import patch

from .models import (
    FinancialYear, Department, BusinessGroup, Designation, Grade,
    CorporateMobileNo, SwapCard, OfficeStaff
)
from .forms import OfficeStaffFilterForm

# --- Model Tests ---

class LookupModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for lookup tables
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024", comp_id=101)
        cls.dept = Department.objects.create(id=1, symbol="HR", description="Human Resources")
        cls.bg = BusinessGroup.objects.create(id=1, symbol="CORP")
        cls.designation = Designation.objects.create(id=1, symbol="MGR", type="Manager")
        cls.grade = Grade.objects.create(id=1, symbol="A1")
        cls.mobile_no = CorporateMobileNo.objects.create(id=1, mobile_no="9876543210")
        cls.swap_card = SwapCard.objects.create(id=1, swap_card_no="SC001")

    def test_financial_year_creation(self):
        self.assertEqual(self.fin_year.fin_year, "2023-2024")
        self.assertEqual(str(self.fin_year), "2023-2024")

    def test_department_creation(self):
        self.assertEqual(self.dept.description, "Human Resources")
        self.assertEqual(str(self.dept), "HR - Human Resources")

    def test_business_group_creation(self):
        self.assertEqual(self.bg.symbol, "CORP")
        self.assertEqual(str(self.bg), "CORP")

    def test_designation_creation(self):
        self.assertEqual(self.designation.type, "Manager")
        self.assertEqual(str(self.designation), "MGR - Manager")

    def test_grade_creation(self):
        self.assertEqual(self.grade.symbol, "A1")
        self.assertEqual(str(self.grade), "A1")

    def test_corporate_mobile_no_creation(self):
        self.assertEqual(self.mobile_no.mobile_no, "9876543210")
        self.assertEqual(str(self.mobile_no), "9876543210")

    def test_swap_card_creation(self):
        self.assertEqual(self.swap_card.swap_card_no, "SC001")
        self.assertEqual(str(self.swap_card), "SC001")

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024", comp_id=101)
        cls.dept = Department.objects.create(id=1, symbol="IT", description="Information Technology")
        cls.bg = BusinessGroup.objects.create(id=1, symbol="TECH")
        cls.designation = Designation.objects.create(id=1, symbol="DEV", type="Developer")
        cls.grade = Grade.objects.create(id=1, symbol="B2")
        cls.mobile_no = CorporateMobileNo.objects.create(id=1, mobile_no="9988776655")
        cls.swap_card = SwapCard.objects.create(id=1, swap_card_no="SWP001")

        # Create OfficeStaff instances
        cls.staff1 = OfficeStaff.objects.create(
            id=101, # UserID for first staff
            fin_year=cls.fin_year,
            emp_id="EMP001",
            gender="Male",
            department=cls.dept,
            business_group=cls.bg,
            designation=cls.designation,
            swap_card=cls.swap_card,
            grade=cls.grade,
            title="Mr.",
            employee_name="John Doe",
            mobile_no=cls.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2020, 1, 1)),
            resignation_date=None,
            comp_id=101
        )
        cls.staff2 = OfficeStaff.objects.create(
            id=102, # UserID for second staff
            fin_year=cls.fin_year,
            emp_id="EMP002",
            gender="Female",
            department=cls.dept,
            business_group=cls.bg,
            designation=cls.designation,
            swap_card=cls.swap_card,
            grade=cls.grade,
            title="Ms.",
            employee_name="Jane Smith",
            mobile_no=cls.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2019, 5, 10)),
            resignation_date=timezone.make_aware(datetime(2021, 10, 15)),
            comp_id=101
        )

    def test_office_staff_creation(self):
        self.assertEqual(OfficeStaff.objects.count(), 2)
        self.assertEqual(self.staff1.emp_id, "EMP001")
        self.assertEqual(self.staff1.employee_name, "John Doe")
        self.assertEqual(self.staff1.department.description, "Information Technology")

    def test_get_full_name(self):
        self.assertEqual(self.staff1.get_full_name(), "Mr. John Doe")
        self.assertEqual(self.staff2.get_full_name(), "Ms. Jane Smith")

    def test_get_formatted_joining_date(self):
        self.assertEqual(self.staff1.get_formatted_joining_date(), "01-01-2020")
        self.assertEqual(self.staff2.get_formatted_joining_date(), "10-05-2019")

    def test_get_formatted_resignation_date(self):
        self.assertEqual(self.staff1.get_formatted_resignation_date(), "")
        self.assertEqual(self.staff2.get_formatted_resignation_date(), "15-10-2021")

    def test_get_details_url(self):
        # This test relies on 'office_staff_details' URL being defined somewhere
        # Mocking or defining a dummy URL for testing purposes
        with self.settings(ROOT_URLCONF='hr_reports.test_urls'): # Use a temporary URLconf for this test
            self.assertEqual(self.staff1.get_details_url(), reverse('office_staff_details', args=['EMP001']))

class OfficeStaffFilterFormTest(TestCase):
    def test_empty_form_validity(self):
        form = OfficeStaffFilterForm({})
        self.assertTrue(form.is_valid())

    def test_get_sub_criteria_choices_department(self):
        Department.objects.create(id=1, symbol="HR", description="Human Resources")
        form = OfficeStaffFilterForm()
        choices = form.get_sub_criteria_choices('tblHR_Departments.DeptName')
        self.assertIn(('1', 'HR - Human Resources'), choices)
        self.assertIn(('Select', 'Select'), choices)

    def test_get_employees_for_autocomplete(self):
        FinancialYear.objects.create(fin_year_id=1, fin_year="2023-2024", comp_id=101)
        OfficeStaff.objects.create(
            id=101, fin_year_id=1, emp_id="E001", employee_name="Alice Brown", comp_id=101
        )
        OfficeStaff.objects.create(
            id=102, fin_year_id=1, emp_id="E002", employee_name="Bob White", comp_id=101
        )
        
        form = OfficeStaffFilterForm()
        suggestions = form.get_employees_for_autocomplete("ali", 101)
        self.assertEqual(suggestions, ["Alice Brown [E001]"])

    def test_get_emp_id_from_autocomplete_text(self):
        form = OfficeStaffFilterForm()
        self.assertEqual(form.get_emp_id_from_autocomplete_text("Alice Brown [E001]"), "E001")
        self.assertEqual(form.get_emp_id_from_autocomplete_text("Just Name"), "Just Name")

# --- View Tests ---

class OfficeStaffViewsTest(TestCase):
    fixtures = ['initial_data.json'] # Load initial data from a fixture file

    def setUp(self):
        self.client = Client()
        # Mock session data for tests
        session = self.client.session
        session['compid'] = 101
        session['finyear'] = 1
        session.save()

        # Create necessary related objects if not using fixtures
        self.fin_year = FinancialYear.objects.get(fin_year_id=1)
        self.dept = Department.objects.get(id=1) # Example from fixture
        self.bg = BusinessGroup.objects.get(id=1)
        self.designation = Designation.objects.get(id=1)
        self.grade = Grade.objects.get(id=1)
        self.mobile_no = CorporateMobileNo.objects.get(id=1)
        self.swap_card = SwapCard.objects.get(id=1)

        # Ensure test staff exists
        OfficeStaff.objects.create(
            id=2, # UserID 2 (not 1 to pass filter)
            fin_year=self.fin_year,
            emp_id="EMP001",
            gender="Male",
            department=self.dept,
            business_group=self.bg,
            designation=self.designation,
            swap_card=self.swap_card,
            grade=self.grade,
            title="Mr.",
            employee_name="John Doe",
            mobile_no=self.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2020, 1, 1)),
            resignation_date=None, # Not resigned
            comp_id=101
        )
        OfficeStaff.objects.create(
            id=3,
            fin_year=self.fin_year,
            emp_id="EMP002",
            gender="Female",
            department=self.dept,
            business_group=self.bg,
            designation=self.designation,
            swap_card=self.swap_card,
            grade=self.grade,
            title="Ms.",
            employee_name="Jane Smith",
            mobile_no=self.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2019, 5, 10)),
            resignation_date=timezone.make_aware(datetime(2021, 10, 15)), # Resigned
            comp_id=101
        )
        OfficeStaff.objects.create( # Staff for different company
            id=4,
            fin_year=self.fin_year,
            emp_id="EMP003",
            gender="Male",
            department=self.dept,
            business_group=self.bg,
            designation=self.designation,
            swap_card=self.swap_card,
            grade=self.grade,
            title="Mr.",
            employee_name="Company B Staff",
            mobile_no=self.mobile_no,
            email_id1="<EMAIL>",
            email_id2="<EMAIL>",
            joining_date=timezone.make_aware(datetime(2020, 1, 1)),
            resignation_date=None,
            comp_id=102 # Different company
        )


    def test_list_view_get(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/office_staff/list.html')
        self.assertTrue('office_staff_list' in response.context)
        # Default filter: only not resigned employees for compid 101
        self.assertEqual(response.context['office_staff_list'].count(), 1) # John Doe (EMP001)

    def test_office_staff_table_partial_view_get(self):
        # HTMX request to load the table
        response = self.client.get(reverse('hr_reports:office_staff_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/office_staff/_office_staff_table.html')
        self.assertTrue('office_staff_list' in response.context)
        self.assertEqual(response.context['office_staff_list'].count(), 1) # Still filtered for not resigned

    def test_list_view_filter_by_department(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'tblHR_Departments.DeptName',
            'sub_criteria': self.dept.id,
            'search_field': 'Select', # Keep other fields as select
            'search_text': '',
            'emp_name_search': ''
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/office_staff/list.html')
        # Two employees are in self.dept, but one resigned.
        self.assertEqual(response.context['office_staff_list'].count(), 1) # John Doe, not resigned

    def test_list_view_filter_by_emp_id(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'Select',
            'sub_criteria': 'Select',
            'search_field': '0', # EmpId
            'search_text': 'EMP001',
            'emp_name_search': ''
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['office_staff_list'].count(), 1)
        self.assertEqual(response.context['office_staff_list'][0].emp_id, 'EMP001')

    def test_list_view_filter_by_employee_name(self):
        # When searching by EmployeeName, the search_text is not used, emp_name_search is
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'Select',
            'sub_criteria': 'Select',
            'search_field': '1', # EmployeeName
            'search_text': '', # This should be ignored
            'emp_name_search': 'John Doe [EMP001]' # This is what autocomplete would provide
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['office_staff_list'].count(), 1)
        self.assertEqual(response.context['office_staff_list'][0].employee_name, 'John Doe')

    def test_list_view_filter_by_resigned(self):
        response = self.client.get(reverse('hr_reports:office_staff_list'), {
            'criteria': 'Select',
            'sub_criteria': 'Select',
            'search_field': '5', # Resigned
            'search_text': '',
            'emp_name_search': ''
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['office_staff_list'].count(), 1)
        self.assertEqual(response.context['office_staff_list'][0].emp_id, 'EMP002')


    def test_sub_criteria_options_view(self):
        response = self.client.post(reverse('hr_reports:sub_criteria_options'), 
                                   {'criteria': 'tblHR_Departments.DeptName'},
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/_dropdown_options.html')
        self.assertContains(response, f'<option value="{self.dept.id}">{self.dept.symbol} - {self.dept.description}</option>')


    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('hr_reports:employee_autocomplete'), 
                                   {'q': 'John'}, 
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn("John Doe [EMP001]", response.content.decode())
        self.assertNotIn("Jane Smith [EMP002]", response.content.decode()) # Jane resigned, but autocomplete queries all

    def test_office_staff_detail_redirect_view(self):
        # Mock the `random.choices` for predictable testing
        with patch('hr_reports.views.random.choices', return_value='ABCDEFGHIJ'):
            response = self.client.get(reverse('hr_reports:office_staff_detail_redirect', args=['EMP001']), HTTP_HX_REQUEST='true')
            self.assertEqual(response.status_code, 204) # HTMX redirect status
            expected_redirect_url = "/module/hr/transactions/office_staff_print_details.aspx?EmpId=EMP001&ModId=12&SubModId=&PagePrev=2&Key=ABCDEFGHIJ"
            self.assertEqual(response['HX-Redirect'], expected_redirect_url)

            # Test non-HTMX redirect
            response = self.client.get(reverse('hr_reports:office_staff_detail_redirect', args=['EMP001']))
            self.assertEqual(response.status_code, 302) # Standard redirect status
            self.assertEqual(response.url, expected_redirect_url)


# To run these tests, you'll need a fixture file.
# Create hr_reports/fixtures/initial_data.json like this (simplified):
# [
#   {
#     "model": "hr_reports.financialyear",
#     "pk": 1,
#     "fields": {
#       "fin_year": "2023-2024",
#       "comp_id": 101
#     }
#   },
#   {
#     "model": "hr_reports.department",
#     "pk": 1,
#     "fields": {
#       "symbol": "IT",
#       "description": "Information Technology"
#     }
#   },
#   {
#     "model": "hr_reports.businessgroup",
#     "pk": 1,
#     "fields": {
#       "symbol": "TECH"
#     }
#   },
#   {
#     "model": "hr_reports.designation",
#     "pk": 1,
#     "fields": {
#       "symbol": "DEV",
#       "type": "Developer"
#     }
#   },
#   {
#     "model": "hr_reports.grade",
#     "pk": 1,
#     "fields": {
#       "symbol": "B2"
#     }
#   },
#   {
#     "model": "hr_reports.corporatemobileno",
#     "pk": 1,
#     "fields": {
#       "mobile_no": "9988776655"
#     }
#   },
#   {
#     "model": "hr_reports.swapcard",
#     "pk": 1,
#     "fields": {
#       "swap_card_no": "SWP001"
#     }
#   }
# ]

# You also need to temporarily define 'office_staff_details' URL for model tests,
# or mock reverse() in the test_get_details_url.
# Example test_urls.py for the test:
# from django.urls import path
# from django.views.generic import View
# class DummyDetailView(View): pass # Dummy view for URL reversal
# urlpatterns = [
#     path('hr/office-staff/<str:emp_id>/details/', DummyDetailView.as_view(), name='office_staff_details'),
# ]

from django.urls import path
from .views import (
    EmployeeListView,
    EmployeeCreateView,
    EmployeeUpdateView,
    EmployeeDeleteView,
    EmployeeTablePartialView
)

urlpatterns = [
    path('employees/', EmployeeListView.as_view(), name='employee_list'),
    # HTMX endpoints for modal forms and table
    path('employees/add/', EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/edit/<int:pk>/', EmployeeUpdateView.as_view(), name='employee_edit'),
    path('employees/delete/<int:pk>/', EmployeeDeleteView.as_view(), name='employee_delete'),
    path('employees/table/', EmployeeTablePartialView.as_view(), name='employee_table'),
]

from django.urls import path
from .views import (
    WorkingDayListView, 
    WorkingDayTablePartialView,
    WorkingDayCreateView, 
    WorkingDayUpdateView, 
    WorkingDayDeleteView
)

urlpatterns = [
    # Main page for working days list
    path('workingdays/', WorkingDayListView.as_view(), name='workingday_list'),
    
    # HTMX endpoint for the table partial
    path('workingdays/table/', WorkingDayTablePartialView.as_view(), name='workingday_table'),
    
    # HTMX endpoint for adding a new working day (modal form)
    path('workingdays/add/', WorkingDayCreateView.as_view(), name='workingday_add'),
    
    # HTMX endpoint for editing an existing working day (modal form)
    path('workingdays/edit/<int:pk>/', WorkingDayUpdateView.as_view(), name='workingday_edit'),
    
    # HTMX endpoint for deleting a working day (modal confirmation)
    path('workingdays/delete/<int:pk>/', WorkingDayDeleteView.as_view(), name='workingday_delete'),
]

from django.urls import path
from .views import HolidayListView, HolidayCreateView, HolidayUpdateView, HolidayDeleteView, HolidayTablePartialView

urlpatterns = [
    # Main list view (full page load)
    path('holiday/', HolidayListView.as_view(), name='holiday_list'),
    
    # HTMX-specific endpoint for refreshing the table content
    path('holiday/table/', HolidayTablePartialView.as_view(), name='holiday_table'),
    
    # Endpoints for CRUD operations, primarily used by HTMX in modals
    path('holiday/add/', HolidayCreateView.as_view(), name='holiday_add'),
    path('holiday/edit/<int:pk>/', HolidayUpdateView.as_view(), name='holiday_edit'),
    path('holiday/delete/<int:pk>/', HolidayDeleteView.as_view(), name='holiday_delete'),
]

from django.urls import path
from .views import (
    GatePassReasonListView, 
    GatePassReasonTablePartialView,
    GatePassReasonCreateView, 
    GatePassReasonUpdateView, 
    GatePassReasonDeleteView
)

urlpatterns = [
    path('gatepassreason/', GatePassReasonListView.as_view(), name='gatepassreason_list'),
    path('gatepassreason/table/', GatePassReasonTablePartialView.as_view(), name='gatepassreason_table'), # HTMX-specific for table refresh
    path('gatepassreason/add/', GatePassReasonCreateView.as_view(), name='gatepassreason_add'),
    path('gatepassreason/edit/<int:pk>/', GatePassReasonUpdateView.as_view(), name='gatepassreason_edit'),
    path('gatepassreason/delete/<int:pk>/', GatePassReasonDeleteView.as_view(), name='gatepassreason_delete'),
]

# hr_masters/urls.py
from django.urls import path
from .views import (
    IntercomExtNoListView,
    IntercomExtNoTablePartialView,
    IntercomExtNoCreateView,
    IntercomExtNoUpdateView,
    IntercomExtNoDeleteView,
)

urlpatterns = [
    path('intercom-ext-no/', IntercomExtNoListView.as_view(), name='intercomextno_list'),
    path('intercom-ext-no/table/', IntercomExtNoTablePartialView.as_view(), name='intercomextno_table'),
    path('intercom-ext-no/add/', IntercomExtNoCreateView.as_view(), name='intercomextno_add'),
    path('intercom-ext-no/edit/<int:pk>/', IntercomExtNoUpdateView.as_view(), name='intercomextno_edit'),
    path('intercom-ext-no/delete/<int:pk>/', IntercomExtNoDeleteView.as_view(), name='intercomextno_delete'),
]

# In your project's main urls.py (e.g., myproject/urls.py), you would include:
# from django.urls import path, include
# urlpatterns = [
#     path('hr/', include('hr_masters.urls')),
# ]

# hr/urls.py
from django.urls import path
from .views import (
    SwapCardListView, 
    SwapCardCreateView, 
    SwapCardUpdateView, 
    SwapCardDeleteView,
    SwapCardTablePartialView # Added for HTMX-driven table refreshes
)

urlpatterns = [
    # Main list view (full page load, also serves as initial container for HTMX)
    path('swapcard/', SwapCardListView.as_view(), name='swapcard_list'),
    
    # HTMX endpoint for dynamic loading/refreshing of the table content
    path('swapcard/table/', SwapCardTablePartialView.as_view(), name='swapcard_table'),

    # HTMX endpoint for adding a new Swap Card (loads form into modal)
    path('swapcard/add/', SwapCardCreateView.as_view(), name='swapcard_add'),
    
    # HTMX endpoint for editing an existing Swap Card (loads form into modal)
    path('swapcard/edit/<int:pk>/', SwapCardUpdateView.as_view(), name='swapcard_edit'),
    
    # HTMX endpoint for confirming deletion of a Swap Card (loads confirmation into modal)
    path('swapcard/delete/<int:pk>/', SwapCardDeleteView.as_view(), name='swapcard_delete'),
]

# hr_masters/urls.py
from django.urls import path
from .views import (
    CorporateMobileNoListView, 
    CorporateMobileNoCreateView, 
    CorporateMobileNoUpdateView, 
    CorporateMobileNoDeleteView,
    CorporateMobileNoTablePartialView, # New view for HTMX table refresh
)

urlpatterns = [
    # Main list page
    path('corporate-mobile-no/', CorporateMobileNoListView.as_view(), name='corporatemobileno_list'),
    
    # HTMX endpoint for table content only
    path('corporate-mobile-no/table/', CorporateMobileNoTablePartialView.as_view(), name='corporatemobileno_table'),
    
    # CRUD operations using HTMX modals
    path('corporate-mobile-no/add/', CorporateMobileNoCreateView.as_view(), name='corporatemobileno_add'),
    path('corporate-mobile-no/edit/<int:pk>/', CorporateMobileNoUpdateView.as_view(), name='corporatemobileno_edit'),
    path('corporate-mobile-no/delete/<int:pk>/', CorporateMobileNoDeleteView.as_view(), name='corporatemobileno_delete'),
]

from django.urls import path
from .views import (
    PF_SlabListView, 
    PF_SlabCreateView, 
    PF_SlabUpdateView, 
    PF_SlabDeleteView,
    PF_SlabTablePartialView
)

urlpatterns = [
    # Main list view for PF Slabs
    path('pf-slab/', PF_SlabListView.as_view(), name='pf_slab_list'),
    
    # HTMX endpoint for the DataTables partial
    path('pf-slab/table/', PF_SlabTablePartialView.as_view(), name='pf_slab_table'),
    
    # HTMX endpoint for adding a new PF Slab (loads form in modal)
    path('pf-slab/add/', PF_SlabCreateView.as_view(), name='pf_slab_add'),
    
    # HTMX endpoint for editing an existing PF Slab (loads form in modal)
    path('pf-slab/edit/<int:pk>/', PF_SlabUpdateView.as_view(), name='pf_slab_edit'),
    
    # HTMX endpoint for deleting a PF Slab (loads confirmation in modal)
    path('pf-slab/delete/<int:pk>/', PF_SlabDeleteView.as_view(), name='pf_slab_delete'),
]

# hr_masters/urls.py
from django.urls import path
from .views import (
    BusinessGroupListView,
    BusinessGroupTablePartialView,
    BusinessGroupCreateView,
    BusinessGroupUpdateView,
    BusinessGroupDeleteView,
)

urlpatterns = [
    path('businessgroup/', BusinessGroupListView.as_view(), name='businessgroup_list'),
    path('businessgroup/table/', BusinessGroupTablePartialView.as_view(), name='businessgroup_table'),
    path('businessgroup/add/', BusinessGroupCreateView.as_view(), name='businessgroup_add'),
    path('businessgroup/edit/<int:pk>/', BusinessGroupUpdateView.as_view(), name='businessgroup_edit'),
    path('businessgroup/delete/<int:pk>/', BusinessGroupDeleteView.as_view(), name='businessgroup_delete'),
]

# hr_app/urls.py
from django.urls import path
from .views import (
    DepartmentListView,
    DepartmentCreateView,
    DepartmentUpdateView,
    DepartmentDeleteView,
    DepartmentTablePartialView,
)

urlpatterns = [
    # Main list view
    path('departments/', DepartmentListView.as_view(), name='department_list'),
    
    # HTMX partial for the table (used by list.html)
    path('departments/table/', DepartmentTablePartialView.as_view(), name='department_table'),

    # HTMX partial for add form (rendered in modal)
    path('departments/add/', DepartmentCreateView.as_view(), name='department_add'),
    
    # HTMX partial for edit form (rendered in modal)
    path('departments/edit/<int:pk>/', DepartmentUpdateView.as_view(), name='department_edit'),
    
    # HTMX partial for delete confirmation (rendered in modal)
    path('departments/delete/<int:pk>/', DepartmentDeleteView.as_view(), name='department_delete'),
]

from django.urls import path
from .views import GradeListView, GradeCreateView, GradeUpdateView, GradeDeleteView, GradeTablePartialView

urlpatterns = [
    # Main list page
    path('grades/', GradeListView.as_view(), name='grade_list'),
    
    # HTMX endpoint for loading/refreshing the DataTables table
    path('grades/table/', GradeTablePartialView.as_view(), name='grade_table'),

    # HTMX endpoints for modal forms
    path('grades/add/', GradeCreateView.as_view(), name='grade_add'),
    path('grades/edit/<int:pk>/', GradeUpdateView.as_view(), name='grade_edit'),
    path('grades/delete/<int:pk>/', GradeDeleteView.as_view(), name='grade_delete'),
]

from django.urls import path
from .views import (
    DesignationListView, 
    DesignationCreateView, 
    DesignationUpdateView, 
    DesignationDeleteView,
    DesignationTablePartialView # New view for HTMX partial
)

urlpatterns = [
    path('designations/', DesignationListView.as_view(), name='designation_list'),
    path('designations/table/', DesignationTablePartialView.as_view(), name='designation_table'), # HTMX target for table content
    path('designations/add/', DesignationCreateView.as_view(), name='designation_add'),
    path('designations/edit/<int:pk>/', DesignationUpdateView.as_view(), name='designation_edit'),
    path('designations/delete/<int:pk>/', DesignationDeleteView.as_view(), name='designation_delete'),
]

from django.urls import path
from .views import GatePassReportView, GatePassReportTablePartialView

urlpatterns = [
    path('gatepass-report/', GatePassReportView.as_view(), name='gatepass_report_list'),
    path('gatepass-report/table/', GatePassReportTablePartialView.as_view(), name='gatepass_report_table'),
]

from django.urls import path
from .views import (
    TourIntimationCreateUpdateView,
    EmployeeAutocompleteView,
    StateDropdownView, CityDropdownView,
    TourAdvanceTempListView, TourAdvanceTempCreateView, TourAdvanceTempUpdateView, TourAdvanceTempDeleteView
)

urlpatterns = [
    # Main Tour Intimation Form
    path('tourintimation/create/', TourIntimationCreateUpdateView.as_view(), name='tourintimation_create'),

    # HTMX Endpoints for Autocomplete
    path('employees/autocomplete/', EmployeeAutocompleteView.as_view(), name='employees_autocomplete'),

    # HTMX Endpoints for Chained Location Dropdowns
    path('locations/states/', StateDropdownView.as_view(), name='tourintimation_locations_states'),
    path('locations/cities/', CityDropdownView.as_view(), name='tourintimation_locations_cities'),

    # HTMX Endpoints for Temporary Advance Grid (CRUD)
    path('tourintimation/advance_temp/list/', TourAdvanceTempListView.as_view(), name='tourintimation_advance_temp_list'),
    path('tourintimation/advance_temp/add/', TourAdvanceTempCreateView.as_view(), name='tourintimation_advance_temp_add'),
    path('tourintimation/advance_temp/edit/<int:pk>/', TourAdvanceTempUpdateView.as_view(), name='tourintimation_advance_temp_edit'),
    path('tourintimation/advance_temp/delete/<int:pk>/', TourAdvanceTempDeleteView.as_view(), name='tourintimation_advance_temp_delete'),
]

from django.urls import path
from .views import (
    TourIntimationListView, 
    TourIntimationCreateView, 
    TourIntimationUpdateView, 
    TourIntimationDeleteView,
    TourIntimationTablePartialView # Added for HTMX partial loading
)

urlpatterns = [
    path('tourintimations/', TourIntimationListView.as_view(), name='tourintimation_list'),
    path('tourintimations/table/', TourIntimationTablePartialView.as_view(), name='tourintimation_table'), # HTMX partial
    path('tourintimations/add/', TourIntimationCreateView.as_view(), name='tourintimation_add'),
    path('tourintimations/edit/<int:pk>/', TourIntimationUpdateView.as_view(), name='tourintimation_edit'),
    path('tourintimations/delete/<int:pk>/', TourIntimationDeleteView.as_view(), name='tourintimation_delete'),
]

from django.urls import path
from .views import (
    TourIntimationListView,
    TourIntimationTablePartialView,
    TourIntimationDetailView,
    EmployeeAutocompleteView,
    TourIntimationSearchFormFieldsView,
    # CRUD Views
    TourIntimationCreateView,
    TourIntimationUpdateView,
    TourIntimationDeleteView,
)

urlpatterns = [
    # Main List View (initial page load)
    path('tourintimation/', TourIntimationListView.as_view(), name='tourintimation_list'),

    # HTMX endpoint for the table content (search results)
    path('tourintimation/table/', TourIntimationTablePartialView.as_view(), name='tourintimation_table'),

    # HTMX endpoint for dynamically updating search form fields
    path('tourintimation/search_form_fields/', TourIntimationSearchFormFieldsView.as_view(), name='tourintimation_search_form_fields'),

    # HTMX/API endpoint for employee autocomplete
    path('employees/autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Detail View (for LinkButton1 functionality)
    path('tourintimation/detail/<int:pk>/', TourIntimationDetailView.as_view(), name='tourintimation_detail'),

    # --- Generic CRUD URLs (Optional, as per template) ---
    path('tourintimation/add/', TourIntimationCreateView.as_view(), name='tourintimation_add'),
    path('tourintimation/edit/<int:pk>/', TourIntimationUpdateView.as_view(), name='tourintimation_edit'),
    path('tourintimation/delete/<int:pk>/', TourIntimationDeleteView.as_view(), name='tourintimation_delete'),
]

from django.urls import path
from .views import (
    GatePassPrintView, 
    GatePassTablePartialView, 
    EmployeeAutocompleteView,
    GatePassListView, 
    GatePassCreateView, 
    GatePassUpdateView, 
    GatePassDeleteView
)

urlpatterns = [
    # Main Gate Pass Print/Search functionality
    path('gatepass/print/', GatePassPrintView.as_view(), name='gatepass_print_search'),
    path('gatepass/table/', GatePassTablePartialView.as_view(), name='gatepass_table'),
    path('gatepass/employees-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Standard CRUD operations for GatePass (as per modernization guidelines)
    path('gatepass/', GatePassListView.as_view(), name='gatepass_list'),
    path('gatepass/add/', GatePassCreateView.as_view(), name='gatepass_add'),
    path('gatepass/edit/<int:pk>/', GatePassUpdateView.as_view(), name='gatepass_edit'),
    path('gatepass/delete/<int:pk>/', GatePassDeleteView.as_view(), name='gatepass_delete'),
]

from django.urls import path
from .views import (
    AuthorizeGatePassListView,
    AuthorizeGatePassTablePartialView,
    GatePassAuthorizeActionView,
    GatePassDetailTablePartialView,
    GatePassDetailDeleteView,
    EmployeeAutocompleteView,
)

app_name = 'gatepass_app' # Define app_name for namespacing URLs

urlpatterns = [
    # Main list view (handles initial load and full page refreshes)
    path('authorize/', AuthorizeGatePassListView.as_view(), name='authorize_gatepass_list'),
    
    # HTMX endpoint for refreshing only the main gate pass table
    path('authorize/table/', AuthorizeGatePassTablePartialView.as_view(), name='authorize_gatepass_table'),
    
    # HTMX endpoint for authorizing selected gate passes
    path('authorize/action/authorize/', GatePassAuthorizeActionView.as_view(), name='authorize_gatepass_action_authorize'),
    
    # HTMX endpoint for displaying details of a specific gate pass
    path('details/<int:pk>/table/', GatePassDetailTablePartialView.as_view(), name='gatepass_details_table'),
    
    # HTMX endpoint for deleting a gate pass detail
    path('details/delete/<int:pk>/', GatePassDetailDeleteView.as_view(), name='gatepass_detail_delete'),

    # HTMX endpoint for displaying delete confirmation modal
    path('details/delete/<int:pk>/confirm/', GatePassDetailDeleteView.as_view(template_name='gatepass_app/authorize_gatepass/_gatepass_detail_delete_confirm.html'), name='gatepass_detail_delete_confirm'),

    # HTMX endpoint for employee name autocomplete
    path('employee/autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

from django.urls import path
from .views import (
    TourIntimationListView, 
    TourIntimationTablePartialView, 
    TourIntimationAutocompleteView,
    TourIntimationUpdateView,
    TourIntimationCreateView,
    TourIntimationDeleteView,
)
from .forms import TourIntimationSearchForm # Needed to render the form for the partial

urlpatterns = [
    # Main list page
    path('tourintimation/', TourIntimationListView.as_view(), name='tourintimation_list'),
    
    # HTMX endpoints for partials
    path('tourintimation/table/', TourIntimationTablePartialView.as_view(), name='tourintimation_table'),
    path('tourintimation/search-form/', lambda request: render(request, 'hr_tour/tourintimation/_search_form.html', {'search_form': TourIntimationSearchForm(request.GET)}), name='tourintimation_search_form'),
    path('autocomplete/employees/', TourIntimationAutocompleteView.as_view(), name='employee_autocomplete'),

    # CRUD operations
    path('tourintimation/add/', TourIntimationCreateView.as_view(), name='tourintimation_add'),
    path('tourintimation/edit/<int:pk>/', TourIntimationUpdateView.as_view(), name='tourintimation_edit'),
    path('tourintimation/delete/<int:pk>/', TourIntimationDeleteView.as_view(), name='tourintimation_delete'),
]

from django.urls import path
from .views import (
    TourIntimationEditView, 
    EmployeeAutocompleteView, 
    GetStatesByCountryView, 
    GetCitiesByStateView,
    ToggleWoGroupView,
    TourAdvanceTablePartialView,
    TourAdvanceCreateView,
    TourAdvanceUpdateView,
    TourAdvanceDeleteView,
)

urlpatterns = [
    # Main Tour Intimation Edit Page
    path('<int:pk>/edit/', TourIntimationEditView.as_view(), name='tour_intimation_edit'),

    # HTMX Endpoints for main form
    path('employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    path('get-states/', GetStatesByCountryView.as_view(), name='tour_intimation_get_states'),
    path('get-cities/', GetCitiesByStateView.as_view(), name='tour_intimation_get_cities'),
    path('toggle-wo-group/', ToggleWoGroupView.as_view(), name='tour_intimation_toggle_wo_group'),

    # HTMX Endpoints for Tour Advances (GridView1 equivalent)
    path('<int:pk>/advances/table/', TourAdvanceTablePartialView.as_view(), name='tour_intimation_advance_table'),
    path('<int:tour_intimation_pk>/advances/add/', TourAdvanceCreateView.as_view(), name='tour_intimation_advance_add'),
    path('<int:tour_intimation_pk>/advances/edit/<int:pk>/', TourAdvanceUpdateView.as_view(), name='tour_intimation_advance_edit'),
    path('<int:tour_intimation_pk>/advances/delete/<int:pk>/', TourAdvanceDeleteView.as_view(), name='tour_intimation_advance_delete'),
    
    # Placeholder for a list view if needed for redirection
    path('', TourIntimationEditView.as_view(), name='tour_intimation_list'), # Redirect to this if no id
]

# hr_transactions/urls.py
from django.urls import path
from .views import (
    EmployeeListView, 
    EmployeeCreateView, 
    EmployeeUpdateView, 
    EmployeeDeleteView,
    EmployeeTablePartialView # New view for HTMX partial
)

urlpatterns = [
    path('employees/', EmployeeListView.as_view(), name='employee_list'),
    path('employees/add/', EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/edit/<int:pk>/', EmployeeUpdateView.as_view(), name='employee_edit'),
    path('employees/delete/<int:pk>/', EmployeeDeleteView.as_view(), name='employee_delete'),
    # HTMX specific endpoint for the DataTables content
    path('employees/table/', EmployeeTablePartialView.as_view(), name='employee_table'),
]

# hr_payroll_reports/urls.py
from django.urls import path
from .views import (
    MonthlySalarySummaryReportView,
    MonthlySalarySummaryTablePartialView,
    SalaryPrintRedirectView,
    OfferLetterDetailsView, # Placeholder for the offer letter details page
)

app_name = 'hr_payroll_reports'

urlpatterns = [
    # Main report view
    path('monthly-salary-summary/', MonthlySalarySummaryReportView.as_view(), name='monthly_salary_summary_report'),
    
    # HTMX partial view for the table content
    path('monthly-salary-summary/table-partial/', MonthlySalarySummaryTablePartialView.as_view(), name='monthly_salary_summary_table_partial'),

    # Redirect for the Cancel button
    path('salary-print-redirect/', SalaryPrintRedirectView.as_view(), name='salary_print_redirect'),
    
    # Placeholder for the offer letter details page (e.g., if it's in another module)
    # This URL should match how it's constructed in the SalaryReportService
    path('offer-letter-details/', OfferLetterDetailsView.as_view(), name='offer_letter_details'),
    
    # Add other report URLs if needed
    # path('salary-print/', YourSalaryPrintView.as_view(), name='salary_print'), # Define your Salary_Print.aspx equivalent here
]

from django.urls import path
from .views import (
    SalaryReportListView, 
    SalaryReportCreateView, 
    SalaryReportUpdateView, 
    SalaryReportDeleteView,
    SalaryReportTablePartialView,
    SalaryPrintRedirectView,
)

urlpatterns = [
    # Main Report View (GET parameters for filters)
    path('consolidated-summary-report/', SalaryReportListView.as_view(), name='salarysummaryreport_list'),
    
    # HTMX Partial for the DataTables content
    path('consolidated-summary-report/table/', SalaryReportTablePartialView.as_view(), name='salarysummaryreport_table'),

    # CRUD operations for SalaryReportEntry (Simulated, as explained)
    path('consolidated-summary-report/add/', SalaryReportCreateView.as_view(), name='salarysummaryreport_add'),
    path('consolidated-summary-report/edit/<str:pk>/', SalaryReportUpdateView.as_view(), name='salarysummaryreport_edit'),
    path('consolidated-summary-report/delete/<str:pk>/', SalaryReportDeleteView.as_view(), name='salarysummaryreport_delete'),

    # Redirect for Cancel button (mimics ASP.NET flow)
    path('salary-print-redirect/', SalaryPrintRedirectView.as_view(), name='salary_print_redirect'),
]

from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('hr/', include('hr_reports.urls')), # Assuming 'hr_reports' is your app name
    # You might also have a URL for the hypothetical 'salary_print_list'
    path('salary-print-list/', lambda request: HttpResponse("This is the Salary Print page"), name='salary_print_list'),
]

from django.urls import path
from .views import BankLoanPrintListView, BankLoanAutocompleteView, BankLoanPrintDetailRedirectView

app_name = 'bankloans_app'

urlpatterns = [
    path('bankloans/', BankLoanPrintListView.as_view(), name='bankloan_list'),
    path('bankloans/autocomplete/', BankLoanAutocompleteView.as_view(), name='bankloan_autocomplete'),
    # This URL is a placeholder for the actual "Bank Loan Print Details" page.
    # In a full migration, this would be a separate Django view/module.
    path('bankloans/details/<str:emp_id>/', BankLoanPrintDetailRedirectView.as_view(), name='bankloan_print_details'),
]

from django.urls import path
from .views import (
    BankLoanListView, 
    BankLoanTablePartialView, 
    BankLoanCreateView, 
    BankLoanUpdateView, 
    BankLoanDeleteView
)

urlpatterns = [
    # Main list view (for initial page load and filtering)
    path('bankloan/', BankLoanListView.as_view(), name='bankloan_list'),
    
    # HTMX endpoint for refreshing the table content
    path('bankloan/table/', BankLoanTablePartialView.as_view(), name='bankloan_table'),
    
    # CRUD operations via HTMX modals
    path('bankloan/add/', BankLoanCreateView.as_view(), name='bankloan_add'),
    path('bankloan/edit/<int:pk>/', BankLoanUpdateView.as_view(), name='bankloan_edit'),
    path('bankloan/delete/<int:pk>/', BankLoanDeleteView.as_view(), name='bankloan_delete'),
]

from django.urls import path
from .views import BankLoanListView, BankLoanTablePartialView, EmployeeAutocompleteView

app_name = 'hr_bankloan'

urlpatterns = [
    path('bankloan/', BankLoanListView.as_view(), name='bankloan_list'),
    path('bankloan/table/', BankLoanTablePartialView.as_view(), name='bankloan_table_partial'),
    path('bankloan/autocomplete/employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

from django.urls import path
from .views import BankLoanManagementView, BankLoanTablePartialView, EmployeeAutocompleteView

urlpatterns = [
    path('bankloan/edit/', BankLoanManagementView.as_view(), name='bankloan_management'),
    path('bankloan/table/', BankLoanTablePartialView.as_view(), name='bankloan_table'),
    path('autocomplete/employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

from django.urls import path
from .views import (
    OfferLetterDetailView,
    OfferAccessoryTablePartialView,
    OfferAccessoryCreateHTMXView,
    OfferAccessoryUpdateHTMXView,
    OfferAccessoryDeleteHTMXView,
    CalculateSalaryHTMXView,
)

urlpatterns = [
    # Main Offer Letter Edit/Increment View
    path('offer-letter/<str:pk>/edit/', OfferLetterDetailView.as_view(), name='offerletter_edit'),
    
    # HTMX endpoints for Offer Accessories (CRUD for the inline grid)
    path('offer-letter/<str:offer_id>/accessories/table/', OfferAccessoryTablePartialView.as_view(), name='offeraccessory_table_partial'),
    path('offer-letter/<str:offer_id>/accessories/add/', OfferAccessoryCreateHTMXView.as_view(), name='offeraccessory_add_htmx'),
    path('offer-letter/<str:offer_id>/accessories/<int:accessory_pk>/edit/', OfferAccessoryUpdateHTMXView.as_view(), name='offeraccessory_edit_htmx'),
    path('offer-letter/<str:offer_id>/accessories/<int:accessory_pk>/delete/', OfferAccessoryDeleteHTMXView.as_view(), name='offeraccessory_delete_htmx'),

    # HTMX endpoint for dynamic salary calculation
    path('hr-offer/calculate-salary/<str:pk>/', CalculateSalaryHTMXView.as_view(), name='calculate_salary_partial'),
    
    # Placeholder for a list view (not implemented in source but implied by redirect)
    path('offer-letters/', OfferLetterDetailView.as_view(), name='offerletter_list'), # Redirect target, adjust as needed.
]

from django.urls import path
from .views import (
    OfferMasterListView,
    OfferMasterTablePartialView,
    OfferMasterCreateView,
    OfferMasterUpdateView,
    OfferMasterDeleteView,
    EmployeeNameAutocompleteView
)

urlpatterns = [
    # Main list page for offer letters
    path('offermaster/', OfferMasterListView.as_view(), name='offermaster_list'),

    # HTMX endpoint for the DataTables content
    path('offermaster/table/', OfferMasterTablePartialView.as_view(), name='offermaster_table'),

    # HTMX endpoint for adding a new offer letter (form in modal)
    path('offermaster/add/', OfferMasterCreateView.as_view(), name='offermaster_add'),

    # HTMX endpoint for editing an existing offer letter (form in modal)
    path('offermaster/edit/<int:pk>/', OfferMasterUpdateView.as_view(), name='offermaster_edit'),

    # Placeholder for increment functionality (could be a separate view/modal later)
    # Redirects to a new details page as per original ASP.NET
    # For now, this will just load the edit form
    path('offermaster/increment/<int:pk>/', OfferMasterUpdateView.as_view(), name='offermaster_increment'),

    # HTMX endpoint for deleting an offer letter (confirmation in modal)
    path('offermaster/delete/<int:pk>/', OfferMasterDeleteView.as_view(), name='offermaster_delete'),

    # HTMX endpoint for employee name autocomplete
    path('offermaster/autocomplete/', EmployeeNameAutocompleteView.as_view(), name='employee_name_autocomplete'),
]

from django.urls import path
from .views import (
    ReportListView, OfferLetterPrintView, 
    IncrementLetterPrintView, ReportCancelView,
)

app_name = 'hr' # Namespace for HR app URLs

urlpatterns = [
    # Main Report List View
    path('reports/', ReportListView.as_view(), name='report_list'),
    path('reports/table-partial/', ReportListView.as_view(), name='report_table_partial'), # HTMX partial load

    # Report Print Views
    path('offer-letter/print/<int:offer_id>/', OfferLetterPrintView.as_view(), name='offer_letter_print'),
    path('increment-letter/print/<int:offer_id>/<int:increment_level>/', IncrementLetterPrintView.as_view(), name='increment_letter_print'),

    # Cancel Button Redirects (mapping to example dummy views/paths)
    path('reports/cancel/', ReportCancelView.as_view(), name='report_cancel'),
    
    # Dummy paths for cancel redirects (these would be implemented as actual views/modules)
    path('hr/offer-letter-list/', ReportListView.as_view(), name='offer_letter_list_main'), # Example redirect from type=1
    path('hr/salary-summary/<str:etype>/<int:month_id>/<int:bggroup_id>/', View.as_view(), name='salary_sapl_neha_summary'), # Placeholder
    path('hr/all-month-summary/<str:etype>/<int:month_id>/<int:bggroup_id>/', View.as_view(), name='all_month_summary_report'), # Placeholder
    path('hr/consolidated-summary/<str:etype>/<int:month_id>/<int:bggroup_id>/', View.as_view(), name='consolidated_summary_report'), # Placeholder
]

from django.urls import path
from .views import (
    OfferLetterListView, 
    OfferLetterTablePartialView,
    OfferLetterDetailRedirectView,
    OfferLetterReportExportView,
    EmployeeNameAutocompleteView,
    OfferLetterPrintDetailsView,
)

app_name = 'hr_offer' # Namespace for URLs

urlpatterns = [
    # Main list page
    path('offerletter/', OfferLetterListView.as_view(), name='offerletter_list'),
    
    # HTMX endpoint for the data table (partial refresh)
    path('offerletter/table/', OfferLetterTablePartialView.as_view(), name='offerletter_table'),
    
    # Endpoint for the "Select" action, redirecting to details/print
    path('offerletter/details/<int:pk>/', OfferLetterDetailRedirectView.as_view(), name='offerletter_detail_redirect'),
    
    # Placeholder for the actual details/print page (which is the target of the redirect)
    path('offerletter/print_details/<int:pk>/', OfferLetterPrintDetailsView.as_view(), name='offerletter_print_details'),

    # Endpoint for exporting the salary report
    path('offerletter/report/export/', OfferLetterReportExportView.as_view(), name='offerletter_report_export'),
    
    # HTMX endpoint for employee name autocomplete suggestions
    path('offerletter/autocomplete/employee_name/', EmployeeNameAutocompleteView.as_view(), name='autocomplete_employee_name'),
]

# hrmodules/urls.py
from django.urls import path
from .views import (
    OfferLetterCreateUpdateView, SalaryBreakdownPartialView, 
    OfferAccessoryTablePartialView, StaffAutoCompleteView,
    OfferLetterListView, OfferLetterTablePartialView, OfferLetterDeleteView
)

app_name = 'hrmodules' # Namespace for URLs

urlpatterns = [
    # Main Offer Letter Form (Create and Update)
    path('offer-letter/add/', OfferLetterCreateUpdateView.as_view(), name='offerletter_add'),
    path('offer-letter/edit/<int:pk>/', OfferLetterCreateUpdateView.as_view(), name='offerletter_edit'),
    
    # HTMX Endpoints for partial updates
    path('offer-letter/calculate-salary/', SalaryBreakdownPartialView.as_view(), name='calculate_salary_partial'),
    path('offer-letter/accessories-table/', OfferAccessoryTablePartialView.as_view(), name='offerletter_accessories_table_partial'),
    
    # Auto-complete endpoint
    path('staff-autocomplete/', StaffAutoCompleteView.as_view(), name='staff_autocomplete'),

    # List and Delete Views
    path('offer-letters/', OfferLetterListView.as_view(), name='offerletter_list'),
    path('offer-letters/table/', OfferLetterTablePartialView.as_view(), name='offerletter_table_partial'),
    path('offer-letters/delete/<int:pk>/', OfferLetterDeleteView.as_view(), name='offerletter_delete'),

    # Example for fetching PF slab
    path('get-pf-slab/', lambda r: JsonResponse(model_to_dict(PF_Slab.objects.filter(active=True).first())), name='get_pf_slab'),
]

from django.urls import path
from .views import (
    SalaryNewDetailsView, SalaryDynamicDataPartialView, 
    OfferAccessoriesPartialView, EmployeeImageView,
    SalaryProceedView, SalaryListView # Dummy list view
)

app_name = 'hr_payroll'

urlpatterns = [
    # Main salary new/details view for a specific employee
    path('salary/new/<str:emp_id>/', SalaryNewDetailsView.as_view(), name='salary_new_details'),

    # HTMX endpoint for dynamic month-selected data (attendance, misc, bank loan, etc.)
    path('salary/new/<str:emp_id>/month_data/', SalaryDynamicDataPartialView.as_view(), name='salary_month_data'),

    # HTMX endpoint for the Offer Accessories DataTables
    path('salary/new/<str:emp_id>/accessories_grid/', OfferAccessoriesPartialView.as_view(), name='offer_accessories_table'),

    # Endpoint for serving employee images (replaces Handler1.ashx)
    path('employee/image/<str:emp_id>/<int:comp_id>/', EmployeeImageView.as_view(), name='employee_image'),

    # Endpoint for processing salary creation/update
    path('salary/proceed/<str:emp_id>/', SalaryProceedView.as_view(), name='salary_proceed'),

    # Dummy URL for redirection after proceeding (replace with actual salary list URL)
    path('salary/list/', SalaryListView.as_view(), name='salary_list'),
]

from django.urls import path
from .views import (
    SalaryBankStatementCheckListView,
    SalaryBankStatementCheckTablePartialView,
    SalaryBankStatementCheckBulkUpdateView,
    salary_check_cancel_view
)

urlpatterns = [
    path('salary-bank-statement-check/', SalaryBankStatementCheckListView.as_view(), name='salary_check_list'),
    path('salary-bank-statement-check/table/', SalaryBankStatementCheckTablePartialView.as_view(), name='salary_check_table_partial'),
    path('salary-bank-statement-check/submit/', SalaryBankStatementCheckBulkUpdateView.as_view(), name='salary_check_bulk_update'),
    path('salary-bank-statement-check/cancel/', salary_check_cancel_view, name='salary_print_redirect'), # Mock redirect
]

from django.urls import path
from .views import SalaryDetailEditView, OfferAccessoryTablePartialView, EmployeePhotoView

app_name = 'hr_salary' # Namespace for the app

urlpatterns = [
    # Main salary edit page, accessed with query parameters
    path('salary/edit/', SalaryDetailEditView.as_view(), name='salary_detail_edit'),
    # Placeholder for the redirect success URL, adjust as per your actual list view
    path('salary/list/', SalaryDetailEditView.as_view(), name='salary_edit_list'), 
    # HTMX endpoint to load the Offer Accessory DataTable dynamically
    path('salary/offer-accessories-table/', OfferAccessoryTablePartialView.as_view(), name='offer_accessory_table'),
    # Endpoint to serve employee photos stored as binary data
    path('employee/photo/<str:emp_id>/', EmployeePhotoView.as_view(), name='employee_photo'),
]

from django.urls import path
from .views import (
    SalaryPrintView, ChequeTransactionsPartialView, EmployeeAutocompleteView,
    ReportGenerationView, UpdateFormVisibilityPartialView,
    ChequeTransactionEditView, ChequeTransactionPrintView,
    GenericReportView # Placeholder for other reports
)

app_name = 'hr_reports'

urlpatterns = [
    path('salary-print/', SalaryPrintView.as_view(), name='salary_print_list'),

    # HTMX endpoints for dynamic content
    path('salary-print/cheque-transactions/', ChequeTransactionsPartialView.as_view(), name='get_cheque_transactions'),
    path('salary-print/employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    path('salary-print/generate-report/', ReportGenerationView.as_view(), name='generate_report'),
    path('salary-print/update-form-visibility/', UpdateFormVisibilityPartialView.as_view(), name='update_form_visibility'),

    # HTMX/Redirect targets for GridView actions
    path('salary-print/cheque-transaction-edit/<str:trans_no>/<int:month_id>/', ChequeTransactionEditView.as_view(), name='cheque_transaction_edit'),
    path('salary-print/cheque-transaction-print/<str:trans_no>/<int:month_id>/', ChequeTransactionPrintView.as_view(), name='cheque_transaction_print'),

    # Placeholder URLs for actual report generation
    path('reports/salary-slip/<str:emp_id>/<int:month_id>/', GenericReportView.as_view(), name='generate_salary_slip'),
    path('reports/bank-statement/<str:cheque_no>/<str:cheque_date>/<int:bank_id>/<str:emp_direct>/<int:bg_group_id>/<int:month_id>/', GenericReportView.as_view(), name='generate_bank_statement'),
    path('reports/generic/<str:report_type>/', GenericReportView.as_view(), name='generate_generic_report'),
    path('reports/generic/<str:report_type>/<int:month_id>/<int:bg_group_id>/', GenericReportView.as_view(), name='generate_generic_report'),

]

# hr_reports/urls.py
from django.urls import path
from .views import SalaryOvertimeReportMainView, SalaryOvertimeReportTablePartialView, cancel_salary_overtime_report

app_name = 'hr_reports' # Define app_name for namespacing

urlpatterns = [
    path('salaryovertime/', SalaryOvertimeReportMainView.as_view(), name='salaryovertimereport_list'),
    path('salaryovertime/table/', SalaryOvertimeReportTablePartialView.as_view(), name='salaryovertimereport_table_partial'),
    path('salaryovertime/cancel/', cancel_salary_overtime_report, name='cancel_salary_overtime_report'),
    # Placeholder for the Salary_Print.aspx equivalent
    path('salaryprint/', SalaryOvertimeReportMainView.as_view(), name='salary_print_view'), # This would be a different view in reality
]

from django.urls import path
from .views import SalaryReportView, SalaryReportTablePartialView

urlpatterns = [
    path('salary-report/', SalaryReportView.as_view(), name='salary_report_list'),
    path('salary-report/table/', SalaryReportTablePartialView.as_view(), name='salary_report_table'),
]

# hr_payroll/urls.py
from django.urls import path
from .views import (
    SalaryEditListView,
    EmployeeTablePartialView,
    SearchInputPartialView,
    EmployeeAutocomplete,
)

app_name = 'hr_payroll' # Namespace for URLs

urlpatterns = [
    # Main list view for Salary Edit
    path('salary-edit/', SalaryEditListView.as_view(), name='salary_edit_list'),
    
    # HTMX endpoint to refresh the employee table
    path('salary-edit/table/', EmployeeTablePartialView.as_view(), name='salary_edit_table_partial'),
    
    # HTMX endpoint to dynamically load search input field based on dropdown selection
    path('salary-edit/get_search_input_type/', SearchInputPartialView.as_view(), name='get_search_input_type'),
    
    # Endpoint for employee name autocomplete functionality
    path('autocomplete/employee/', EmployeeAutocomplete.as_view(), name='employee_autocomplete'),

    # Placeholder for the "Salary_Edit_Details" page, assuming it's a separate view
    path('salary-edit-details/<str:emp_id>/', View.as_view(), name='salary_edit_details'), # Replace View.as_view() with actual detail view
]

from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffCreateView, OfficeStaffUpdateView, OfficeStaffDeleteView,
    OfficeStaffTablePartialView, EmployeeAutocompleteView
)

urlpatterns = [
    path('officestaff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('officestaff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('officestaff/edit/<int:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    path('officestaff/delete/<int:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'),
    # HTMX-specific endpoints
    path('officestaff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    path('autocomplete_employees/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

from django.urls import path
from .views import (
    SalarySummaryReportView, SalarySummaryTablePartialView,
    HrSalaryMasterListView, HrSalaryMasterCreateView, HrSalaryMasterUpdateView, HrSalaryMasterDeleteView
)

urlpatterns = [
    # URLs for the Salary Summary Report (main migration focus)
    # The 'report_selection' would be the previous page leading to this summary report.
    path('salary-report/', SalarySummaryReportView.as_view(), name='salary_summary_report'),
    path('salary-report/table/', SalarySummaryTablePartialView.as_view(), name='salary_summary_table'),
    # Dummy URL for the cancel button, assuming it goes back to a selection page
    path('salary-report/selection/', SalarySummaryTablePartialView.as_view(), name='hr_salary_report_selection'),

    # URLs for HrSalaryMaster CRUD operations (for demonstrating the pattern)
    path('hr-salary-masters/', HrSalaryMasterListView.as_view(), name='hr_salary_master_list'),
    path('hr-salary-masters/add/', HrSalaryMasterCreateView.as_view(), name='hr_salary_master_add'),
    path('hr-salary-masters/edit/<int:pk>/', HrSalaryMasterUpdateView.as_view(), name='hr_salary_master_edit'),
    path('hr-salary-masters/delete/<int:pk>/', HrSalaryMasterDeleteView.as_view(), name='hr_salary_master_delete'),
    # HTMX partial for HrSalaryMaster list
    path('hr-salary-masters/table/', HrSalaryMasterListView.as_view(template_name='hr_salary/hrsalarymaster/_hrsalarymaster_table.html'), name='hr_salary_master_table'),
]

from django.urls import path
from .views import (
    SalaryDetailListView, 
    SalaryDetailTablePartialView,
    SalaryDetailCreateView, 
    SalaryDetailUpdateView, 
    SalaryDetailDeleteView
)

app_name = 'hr_salary'

urlpatterns = [
    # Main list page for salary details (initial load)
    path('salary/edit/', SalaryDetailListView.as_view(), name='salarydetail_list'),
    
    # HTMX endpoint to load the DataTables content
    path('salary/edit/table/', SalaryDetailTablePartialView.as_view(), name='salarydetail_table'),
    
    # HTMX endpoints for CRUD operations (rendered in modal)
    path('salary/edit/add/', SalaryDetailCreateView.as_view(), name='salarydetail_add'),
    path('salary/edit/edit/<int:pk>/', SalaryDetailUpdateView.as_view(), name='salarydetail_edit'),
    path('salary/edit/delete/<int:pk>/', SalaryDetailDeleteView.as_view(), name='salarydetail_delete'),
]

# hr_payroll/urls.py
from django.urls import path
from .views import SalarySlipReportView, SalarySlipListView, SalarySlipTablePartialView
from django.http import HttpResponse # For PDF dummy response
from django.shortcuts import render
from django.template.loader import render_to_string
import pdfkit # Assuming you have WeasyPrint or wkhtmltopdf installed and configured

def salary_slip_download_pdf_view(request, emp_id, month_id):
    # This view would be responsible for generating the PDF
    # It would call the SalaryCalculator, render the HTML, and convert to PDF
    
    comp_id = request.session.get('comp_id', 1)
    fin_year_id = request.session.get('fin_year_id', 1)

    try:
        month_id_int = int(month_id)
        fin_year_id_int = int(fin_year_id)
        comp_id_int = int(comp_id)

        calculator = SalaryCalculator(emp_id, month_id_int, comp_id_int, fin_year_id_int)
        report_data = calculator.calculate_salary_slip()

        # Render HTML template for PDF conversion (without base.html for clean PDF)
        # You might need a dedicated PDF template for optimal layout
        html_content = render_to_string('hr_payroll/salary_slip_report_pdf.html', {
            'report_data': report_data,
            'current_date': report_data.get('current_date'),
            'month_name': report_data.get('month_name'),
            'year': report_data.get('year'),
        })

        # Configure pdfkit (ensure wkhtmltopdf is installed and path is set)
        # config = pdfkit.configuration(wkhtmltopdf='/usr/local/bin/wkhtmltopdf') # Adjust path
        # pdf = pdfkit.from_string(html_content, False, configuration=config) # Use config if needed
        
        # For demonstration, use a dummy PDF content.
        # In production, use pdfkit/weasyprint.
        pdf = b"This is a dummy PDF content for the salary slip."
        
        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="salary_slip_{emp_id}_{month_id}_{report_data.get("year")}.pdf"'
        return response

    except Exception as e:
        # Handle errors during PDF generation
        return HttpResponse(f"Error generating PDF: {e}", status=500)


urlpatterns = [
    # Entry point for selecting parameters
    path('salary-slips/', SalarySlipListView.as_view(), name='salary_slip_list'),
    # Displays the detailed salary slip for a specific employee and month
    path('salary-slips/<str:emp_id>/<int:month_id>/', SalarySlipReportView.as_view(), name='salary_slip_report'),
    # Endpoint to download the report as PDF
    path('salary-slips/<str:emp_id>/<int:month_id>/download-pdf/', salary_slip_download_pdf_view, name='salary_slip_download_pdf'),
    # No direct table partial needed for this detail report, but included for the list context
    # path('salary-slips/table/', SalarySlipTablePartialView.as_view(), name='salary_slips_table'), # Example if there was a list of slips
]

from django.urls import path
from .views import SalaryReportInputView, SalaryReportTableView

urlpatterns = [
    path('salary-report/', SalaryReportInputView.as_view(), name='salary_report_input'),
    path('salary-report/table/', SalaryReportTableView.as_view(), name='salary_report_table'),
]

from django.urls import path
from .views import SalaryBankStatementReportView, SalaryBankStatementTablePartialView

urlpatterns = [
    # Main report page with parameter form
    path('salary-bank-statement/', SalaryBankStatementReportView.as_view(), name='salarybankstatement_report'),
    
    # HTMX endpoint to load the report table partial (triggered by form submission or initial load)
    path('salary-bank-statement/table/', SalaryBankStatementTablePartialView.as_view(), name='salarybankstatement_table_partial'),
]

# hr_salary/urls.py
from django.urls import path
from .views import SalaryCheckEditListView, SalaryCheckEditTablePartialView, SalaryCheckEditUpdateView
from django.shortcuts import redirect # For simple redirect in URLs

app_name = 'hr_salary'

urlpatterns = [
    # Main page for salary check/edit
    path('check-edit/', SalaryCheckEditListView.as_view(), name='salary_check_edit_list'),
    
    # HTMX endpoint to load/refresh the table content
    path('check-edit/table/', SalaryCheckEditTablePartialView.as_view(), name='salary_check_edit_table_partial'),
    
    # HTMX endpoint to handle the bulk update
    path('check-edit/update/', SalaryCheckEditUpdateView.as_view(), name='salary_check_edit_update'),

    # Redirect for Cancel button (mimicking original ASP.NET navigation)
    # This assumes 'salary_print' is a separate view/app
    path('salary-print-redirect/', lambda r: redirect(f'/salary/print?MonthId={r.GET.get("MonthId", "")}&ModId={r.GET.get("ModId", "")}&SubModId={r.GET.get("SubModId", "")}'), name='salary_print_redirect'),
    # More robust way would be to reverse the actual salary_print URL if it existed
    # path('salary-print-redirect/<int:month_id>/<int:mod_id>/<int:sub_mod_id>/', 
    #      lambda r, month_id, mod_id, sub_mod_id: redirect(reverse_lazy('hr_salary:salary_print', kwargs={'month_id': month_id, 'mod_id': mod_id, 'sub_mod_id': sub_mod_id})),
    #      name='salary_print_redirect_detailed'),
]

# Example of how salary_print URL might look in the future (in a 'reports' app, for instance)
# urlpatterns += [
#     path('salary/print/', SalaryPrintView.as_view(), name='salary_print'),
# ]

from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffTablePartialView, 
    OfficeStaffCreateView, OfficeStaffUpdateView, OfficeStaffDeleteView,
    OfficeStaffDetailView, StaffAutocompleteView, StaffExportExcelView
)

urlpatterns = [
    # Main Staff List Page
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    
    # HTMX partial for the staff table (used for initial load and search/refresh)
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    
    # CRUD operations
    path('staff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('staff/edit/<str:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'), # pk is str (EmpId)
    path('staff/delete/<str:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'), # pk is str (EmpId)
    
    # Detail view (for "select" action in modal)
    path('staff/detail/<str:pk>/', OfficeStaffDetailView.as_view(), name='officestaff_detail'), # pk is str (EmpId)

    # Autocomplete endpoint for employee name search
    path('staff/autocomplete/', StaffAutocompleteView.as_view(), name='officestaff_autocomplete'),
    
    # Export to Excel
    path('staff/export/', StaffExportExcelView.as_view(), name='officestaff_export_excel'),
]

# hr_staff/urls.py

from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffCreateView,
    OfficeStaffUpdateView, OfficeStaffDeleteView,
    OfficeStaffTablePartialView
)

urlpatterns = [
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'), # HTMX partial
    path('staff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('staff/edit/<int:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    path('staff/delete/<int:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'),
]

from django.urls import path
from .views import (
    OfferMasterListView,
    OfferMasterTablePartialView,
    OfferMasterCreateView,
    OfferMasterUpdateView,
    OfferMasterDeleteView
)

urlpatterns = [
    # Main list view for pending offers (equivalent to original ASPX page)
    path('offermaster/', OfferMasterListView.as_view(), name='offermaster_list'),

    # HTMX endpoint for refreshing the table content
    path('offermaster/table/', OfferMasterTablePartialView.as_view(), name='offermaster_table'),

    # HTMX endpoint for loading the 'add new offer' form in a modal
    path('offermaster/add/', OfferMasterCreateView.as_view(), name='offermaster_add'),

    # HTMX endpoint for loading the 'edit offer' form in a modal (maps to 'Select' action)
    path('offermaster/edit/<int:pk>/', OfferMasterUpdateView.as_view(), name='offermaster_edit'),

    # HTMX endpoint for loading the 'delete offer' confirmation in a modal
    path('offermaster/delete/<int:pk>/', OfferMasterDeleteView.as_view(), name='offermaster_delete'),
]

# hr/urls.py
from django.urls import path
from .views import (
    OfficeStaffListView, OfficeStaffTablePartialView, OfficeStaffUpdateView,
    FileDeleteView, DownloadFileView
)

app_name = 'hr' # Define the app namespace

urlpatterns = [
    # URL for displaying the list of office staff members (for DataTables)
    path('officestaff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    
    # URL for fetching the DataTables content dynamically via HTMX
    path('officestaff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),

    # URL for editing an existing office staff member.
    # Uses 'emp_id' from the URL path as a unique identifier.
    path('officestaff/edit/<str:emp_id>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    
    # HTMX-specific endpoint for deleting associated files (photo/CV)
    # Uses 'pk' (the userid from the model) and 'file_type' (e.g., 'photo', 'cv')
    path('officestaff/<int:pk>/delete_file/<str:file_type>/', FileDeleteView.as_view(), name='file_delete'),
    
    # HTMX-specific endpoint for downloading associated files (e.g., CV)
    path('officestaff/<int:pk>/download_file/<str:file_type>/', DownloadFileView.as_view(), name='file_download'),

    # Conceptual URL for a staff creation page, if one were to be implemented
    # path('officestaff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
]

from django.urls import path
from .views import (
    OfficeStaffListView,
    OfficeStaffCreateView,
    OfficeStaffUpdateView,
    OfficeStaffDeleteView,
    OfficeStaffTablePartialView,
    OfficeStaffDetailReportView,
    ImageView
)

urlpatterns = [
    # Office Staff List and CRUD (HTMX driven)
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    path('staff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('staff/edit/<int:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    path('staff/delete/<int:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'),

    # Detailed Staff Report View (replaces ASP.NET OfficeStaff_Print_Details.aspx)
    path('staff/<int:pk>/report/', OfficeStaffDetailReportView.as_view(), name='staff_detail_report'),

    # Image serving view for photo data stored in DB
    path('image/<int:pk>/<str:field_name>/', ImageView.as_view(), name='image_view'),
]

from django.urls import path
from .views import OfficeStaffListView, OfficeStaffTablePartialView, EmployeeAutocompleteView

app_name = 'hr_staff' # Define app_name for namespacing

urlpatterns = [
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    path('staff/autocomplete/employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

from django.urls import path
from .views import (
    NewsNoticeListView,
    NewsNoticeUpdateView,
    NewsNoticeTablePartialView,
    NewsNoticeClearFileView,
    NewsNoticeDownloadFileView,
)

urlpatterns = [
    # Main list page for News Notices
    path('newsnotice/', NewsNoticeListView.as_view(), name='newsnotice_list'),

    # HTMX endpoint to load/refresh the DataTables content
    path('newsnotice/table/', NewsNoticeTablePartialView.as_view(), name='newsnotice_table'),

    # Edit an existing News Notice (replaces ASP.NET edit page)
    path('newsnotice/edit/<int:pk>/', NewsNoticeUpdateView.as_view(), name='newsnotice_edit'),

    # HTMX endpoint to clear/remove an attached file
    path('newsnotice/clear-file/<int:pk>/', NewsNoticeClearFileView.as_view(), name='newsnotice_clear_file'),

    # Endpoint to download the attached file (BLOB data)
    path('newsnotice/download-file/<int:pk>/', NewsNoticeDownloadFileView.as_view(), name='newsnotice_download_file'),

    # If an "Add" functionality was needed (CreateView):
    # path('newsnotice/add/', NewsNoticeCreateView.as_view(), name='newsnotice_add'),

    # If a "Delete" functionality was needed (DeleteView):
    # path('newsnotice/delete/<int:pk>/', NewsNoticeDeleteView.as_view(), name='newsnotice_delete'),
]

from django.urls import path
from .views import MobileBillReportView, MobileBillReportContentHTMXView

app_name = 'hr_reports' # Namespace for this app's URLs

urlpatterns = [
    path('mobile_bill_report/', MobileBillReportView.as_view(), name='mobile_bill_report_selector'),
    path('mobile_bill_report/content/', MobileBillReportContentHTMXView.as_view(), name='mobile_bill_report_content'),
]

from django.urls import path
from .views import MobileBillEditView, MobileBillTablePartialView

urlpatterns = [
    path('mobile_bills/edit/', MobileBillEditView.as_view(), name='mobile_bills_edit_list'),
    path('mobile_bills/table_partial/', MobileBillTablePartialView.as_view(), name='mobile_bills_table_partial'),
]

# hr_reports/urls.py
from django.urls import path
from .views import (
    MobileBillReportView, MobileBillReportTablePartialView,
    MobileBillListView, MobileBillCreateView, MobileBillUpdateView, MobileBillDeleteView
)

urlpatterns = [
    # Mobile Bill Report URLs (replaces original ASP.NET page)
    path('mobile-bill-report/', MobileBillReportView.as_view(), name='mobile_bill_report_list'),
    path('mobile-bill-report/table/', MobileBillReportTablePartialView.as_view(), name='mobile_bill_report_table'),

    # Mobile Bill Management (CRUD) URLs (added for full modernization)
    path('mobile-bills/', MobileBillListView.as_view(), name='mobile_bill_list'),
    path('mobile-bills/add/', MobileBillCreateView.as_view(), name='mobile_bill_add'),
    path('mobile-bills/edit/<int:pk>/', MobileBillUpdateView.as_view(), name='mobile_bill_edit'),
    path('mobile-bills/delete/<int:pk>/', MobileBillDeleteView.as_view(), name='mobile_bill_delete'),
    # HTMX partial for the list table
    path('mobile-bills/table/', MobileBillListView.as_view(template_name='hr_reports/mobilebill/_mobile_bill_table.html'), name='mobile_bill_table'),
]

from django.urls import path
from .views import (
    NewsNoticeListView, NewsNoticeCreateView, NewsNoticeUpdateView, 
    NewsNoticeDeleteView, NewsNoticeTablePartialView
)

urlpatterns = [
    path('newsnotice/', NewsNoticeListView.as_view(), name='newsnotice_list'),
    path('newsnotice/table/', NewsNoticeTablePartialView.as_view(), name='newsnotice_table'), # For HTMX partial load
    path('newsnotice/add/', NewsNoticeCreateView.as_view(), name='newsnotice_add'),
    path('newsnotice/edit/<int:pk>/', NewsNoticeUpdateView.as_view(), name='newsnotice_edit'),
    path('newsnotice/delete/<int:pk>/', NewsNoticeDeleteView.as_view(), name='newsnotice_delete'),
]

from django.urls import path
from .views import MobileBillListView, MobileBillTablePartialView, MobileBillUpdateView, MobileBillDeleteView

app_name = 'hr_mobile_bills'

urlpatterns = [
    # Main page for mobile bills, including month filter
    path('mobile_bills/', MobileBillListView.as_view(), name='mobilebill_list'),
    
    # HTMX endpoint for the mobile bill table content (reads and handles bulk inserts)
    path('mobile_bills/table/', MobileBillTablePartialView.as_view(), name='mobilebill_table'),
    
    # Individual MobileBill update (for existing entries)
    path('mobile_bills/edit/<int:pk>/', MobileBillUpdateView.as_view(), name='mobilebill_edit'),
    
    # Individual MobileBill delete (for existing entries)
    path('mobile_bills/delete/<int:pk>/', MobileBillDeleteView.as_view(), name='mobilebill_delete'),
]

from django.urls import path
from .views import (
    NewsNoticeListView,
    NewsNoticeTablePartialView,
    NewsNoticeCreateView,
    NewsNoticeUpdateView,
    NewsNoticeDeleteView,
    NewsNoticeDownloadFileView,
)

urlpatterns = [
    # List view for News Notices
    path('newsnotice/', NewsNoticeListView.as_view(), name='newsnotice_list'),
    
    # HTMX endpoint for the table content
    path('newsnotice/table/', NewsNoticeTablePartialView.as_view(), name='newsnotice_table'),
    
    # Create new News Notice
    path('newsnotice/add/', NewsNoticeCreateView.as_view(), name='newsnotice_add'),
    
    # Edit existing News Notice
    path('newsnotice/edit/<int:pk>/', NewsNoticeUpdateView.as_view(), name='newsnotice_edit'),
    
    # Delete News Notice
    path('newsnotice/delete/<int:pk>/', NewsNoticeDeleteView.as_view(), name='newsnotice_delete'),

    # Download attached file
    path('newsnotice/<int:pk>/download/', NewsNoticeDownloadFileView.as_view(), name='newsnotice_download'),
]