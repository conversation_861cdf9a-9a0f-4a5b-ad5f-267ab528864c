from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib import messages
from .models import *


def hr_dashboard(request):
    """Dashboard view for hr app"""
    context = {
        'title': 'Hr Dashboard',
        'app_name': 'hr'
    }
    return render(request, 'hr/dashboard.html', context)

class CategoryListView(ListView):
    model = Category
    template_name = 'hr/category_list.html'
    context_object_name = 'categories'
    paginate_by = 20

class CategoryCreateView(CreateView):
    model = Category
    template_name = 'hr/category_form.html'
    fields = ['name', 'description', 'is_active']
    success_url = '/hr/categories/'
