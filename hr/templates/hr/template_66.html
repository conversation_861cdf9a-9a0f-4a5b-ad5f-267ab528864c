<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <div class="p-4 flex justify-between items-center">
        <h4 class="text-md font-semibold text-gray-700">Current Advance Transfers</h4>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md"
            hx-get="{% url 'tourintimation_advance_temp_add' %}"
            hx-target="#advance-temp-modal-content"
            hx-trigger="click"
            _="on click add .is-active to #advance-temp-modal">
            Add New Advance
        </button>
    </div>
    <div class="p-4">
        <table id="advanceTempTable" class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                    <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                    <th class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if advance_temps %}
                    {% for temp in advance_temps %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ temp.employee.employee_name }} [{{ temp.employee.emp_id }}]</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ temp.amount|floatformat:2 }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ temp.remarks|default:"-" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <button 
                                    class="text-indigo-600 hover:text-indigo-900 mx-1"
                                    hx-get="{% url 'tourintimation_advance_temp_edit' temp.pk %}"
                                    hx-target="#advance-temp-modal-content"
                                    hx-trigger="click"
                                    _="on click add .is-active to #advance-temp-modal">
                                    Edit
                                </button>
                                <button 
                                    class="text-red-600 hover:text-red-900 mx-1"
                                    hx-get="{% url 'tourintimation_advance_temp_delete' temp.pk %}"
                                    hx-target="#advance-temp-modal-content"
                                    hx-trigger="click"
                                    _="on click add .is-active to #advance-temp-modal">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">No advance transfers added yet.</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<script>
    // Ensure DataTable is initialized only once or re-initialized correctly
    // Destroy existing DataTable if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#advanceTempTable')) {
        $('#advanceTempTable').DataTable().destroy();
    }
    // Initialize DataTables
    $(document).ready(function() {
        $('#advanceTempTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, -1], [5, 10, 25, "All"]],
            "searching": false, // Disable search for this simple temporary grid
            "paging": true,
            "info": false,
            "columnDefs": [
                { "orderable": false, "targets": [4] }, // Disable sorting for Actions column
                { "width": "5%", "targets": 0 },
                { "width": "30%", "targets": 1 },
                { "width": "15%", "targets": 2 },
                { "width": "40%", "targets": 3 },
                { "width": "10%", "targets": 4 }
            ]
        });
    });
</script>