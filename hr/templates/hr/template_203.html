<!-- hr/officestaff/_officestaff_table.html (Partial template for DataTables, loaded via HTMX) -->
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="officestaffTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for staff in officestaffs %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.emp_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.get_full_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.department }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.designation }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ staff.joining_date|date:"d-M-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    {# HTMX buttons to open edit/delete forms in a modal #}
                    <button 
                       hx-get="{% url 'hr:officestaff_edit' emp_id=staff.emp_id %}" 
                       hx-target="#modalContent" 
                       hx-trigger="click" 
                       _="on click send open-modal to #modal"
                       class="text-indigo-600 hover:text-indigo-900 mr-4 cursor-pointer focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md">Edit</button>
                    {# Assuming a delete view like OfficeStaffDeleteView exists, it would open a confirmation modal #}
                    {# The provided instructions require a DeleteView, so we'll link to it conceptually. #}
                    {# For this specific ASP.NET page (edit), delete wasn't a main button, but part of a list typically #}
                    <button 
                       hx-get="{% url 'hr:officestaff_delete' pk=staff.userid %}" {# Assuming userid as PK for delete #}
                       hx-target="#modalContent"
                       hx-trigger="click"
                       _="on click send open-modal to #modal"
                       class="text-red-600 hover:text-red-900 cursor-pointer focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-md">Delete</button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No staff members found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# Initialize DataTables on the loaded table #}
<script>
    $(document).ready(function() {
        $('#officestaffTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 10
        });
    });
</script>