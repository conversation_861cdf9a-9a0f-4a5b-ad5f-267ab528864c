{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    searchType: '{{ search_form.search_field.value|default:'0' }}',
    searchTerm: '{{ search_form.search_term.value|default:'' }}',
    bgGroupInput: '{{ search_form.bg_group_input.value|default:'' }}',
    selectedEmpId: '{{ search_form.selected_empid.value|default:'' }}',
    showAutocomplete: false,
    autocompleteResults: '',
    
    handleSearchChange() {
        this.selectedEmpId = ''; // Clear selected EmpId when search type changes
        // Potentially trigger a search directly or update UI based on type
    },
    
    // For autocomplete selection
    handleSelectEmployee(event) {
        this.searchTerm = event.detail.value;
        this.selectedEmpId = this.searchTerm.split('[').pop().replace(']', ''); // Extract EmpId
        this.showAutocomplete = false; // Hide results after selection
    },
    
    // For general focus/blur on search input to hide autocomplete
    handleSearchFocus() {
        if (this.searchType === '0' && this.searchTerm.length > 0) {
            this.showAutocomplete = true;
        }
    },
    handleSearchBlur() {
        // Delay hiding to allow click event on results
        setTimeout(() => { this.showAutocomplete = false; }, 200);
    },
}">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Bank Loan - New</h2>

    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'hr_bankloan:bankloan_table_partial' %}"
              hx-target="#bankLoanTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_search_field">
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_field }}
                </div>
                <div class="relative">
                    <label for="id_search_term" class="block text-sm font-medium text-gray-700">Search Term</label>
                    <div x-show="searchType === '0'" x-cloak>
                        <input type="text" id="id_search_term" name="search_term" 
                                class="box3 w-full" placeholder="Enter Employee Name..."
                                hx-get="{% url 'hr_bankloan:employee_autocomplete' %}"
                                hx-trigger="keyup changed delay:500ms from:#id_search_term"
                                hx-target="#autocomplete-results"
                                hx-swap="innerHTML"
                                autocomplete="off"
                                x-model="searchTerm"
                                x-on:focus="handleSearchFocus()"
                                x-on:blur="handleSearchBlur()"
                                x-on:select-employee.window="handleSelectEmployee($event)">
                        <input type="hidden" name="selected_empid" x-model="selectedEmpId">
                        <div id="autocomplete-results" class="absolute w-full z-10" x-show="showAutocomplete" x-cloak></div>
                    </div>
                    <div x-show="searchType === '2'" x-cloak>
                        <input type="text" id="id_bg_group_input" name="bg_group_input" 
                                class="box3 w-full" placeholder="Enter BG Group Symbol..."
                                x-model="bgGroupInput">
                    </div>
                </div>
                <div>
                    <button type="submit" class="redbox w-full py-2">Search</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Bank Loan Table Container -->
    <form hx-post="{% url 'hr_bankloan:bankloan_list' %}"
          hx-target="body"
          hx-swap="none"
          x-data="{ showErrors: false }">
        {% csrf_token %}
        <div id="bankLoanTable-container"
             hx-trigger="load, refreshBankLoanList from:body"
             hx-get="{% url 'hr_bankloan:bankloan_table_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
             hx-target="#bankLoanTable-container"
             hx-swap="innerHTML">
            <!-- Table content loaded here by HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Employees...</p>
            </div>
        </div>

        <div class="mt-6 text-center">
            <button type="submit" class="redbox py-2 px-6" x-on:click="showErrors = true; $nextTick(() => { if (!document.querySelector('.has-error')) { return true; } else { event.preventDefault(); alert('Please correct the highlighted errors.'); } })">Submit</button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('loanRow', (initialSelected = false) => ({
            selected: initialSelected,
            toggleFields() {
                // Logic already handled by x-bind:disabled and x-bind:required
                // just toggle the selected state
            },
            initDatepicker(element) {
                flatpickr(element, {
                    dateFormat: "d-m-Y", // dd-MM-yyyy format
                    allowInput: false,   // Disable manual input
                    onClose: function(selectedDates, dateStr, instance) {
                        // Manually dispatch change event for HTMX/Alpine to pick up
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });
            }
        }));

        // General event listener for messages (Django messages framework)
        document.body.addEventListener('htmx:afterSwap', function(event) {
            const messagesContainer = document.getElementById('messages');
            if (messagesContainer && event.detail.xhr.getResponseHeader('HX-Trigger')) {
                // If the HTMX response triggered a message, ensure it's displayed
                // This assumes your base.html has a messages section that reloads or shows
            }
        });
    });
</script>
{% endblock %}