<!-- hr/officestaff/edit.html (Main template for editing staff details, includes Alpine.js for tabs) -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Staff - Edit Details: {{ object.get_full_name }}</h2>

    {# Alpine.js component for managing tab state #}
    <div x-data="{ 
        activeTab: {{ initial_tab_index }},
        currentPhotoFile: '{{ current_photo_file_name|default:'' }}', 
        currentCvFile: '{{ current_cv_file_name|default:'' }}', 
        staffUserId: '{{ staff_userid }}',
        init() {
            // Retrieve last active tab from session storage or default to 0
            const storedTabIndex = sessionStorage.getItem('staff_edit_active_tab');
            if (storedTabIndex !== null) {
                this.activeTab = parseInt(storedTabIndex);
            }
            // Watch for changes in activeTab and save to session storage
            this.$watch('activeTab', value => {
                sessionStorage.setItem('staff_edit_active_tab', value);
            });
        }
    }" 
    id="staffEditFormContainer"
    {# This div will be reloaded via HTMX when a file is removed to update display #}
    {# hx-target="this" hx-swap="outerHTML" is typically for dynamic loading of the form itself #}
    {# For a static page, direct JS refresh or HTMX triggering a refresh of the page is alternative #}
    {# To reload just the form part, we'd wrap the <form> in a hx-targetable div #}
    >
        
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button type="button" @click="activeTab = 0" :class="{'border-indigo-500 text-indigo-600': activeTab === 0, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 0}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Official Info
                </button>
                <button type="button" @click="activeTab = 1" :class="{'border-indigo-500 text-indigo-600': activeTab === 1, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 1}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Personal Info
                </button>
                <button type="button" @click="activeTab = 2" :class="{'border-indigo-500 text-indigo-600': activeTab === 2, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 2}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Edu. Quali. & Work Experience
                </button>
                <button type="button" @click="activeTab = 3" :class="{'border-indigo-500 text-indigo-600': activeTab === 3, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 3}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Others
                </button>
            </nav>
        </div>

        {# The entire form is wrapped to allow multipart/form-data for file uploads #}
        <form method="post" enctype="multipart/form-data" 
              hx-post="{% url 'hr:officestaff_edit' emp_id=object.emp_id %}" 
              hx-swap="none" {# Swap none on successful POST; HTMX will handle triggers/redirects #}
              hx-trigger="submit">
            {% csrf_token %}
            
            {# Display non-field errors and field-specific errors #}
            {% if form.errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
                <strong class="font-bold">Validation Error!</strong>
                <span class="block sm:inline">Please correct the following errors:</span>
                <ul class="mt-2 list-disc list-inside">
                    {% for field in form %}
                        {% if field.errors %}
                            <li>{{ field.label }}: {{ field.errors|join:", " }}</li>
                        {% endif %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}

            <!-- Tab Content Container -->
            <div class="mt-6 p-6 bg-white rounded-lg shadow-md">
                <!-- Official Info Tab Content -->
                <div x-show="activeTab === 0" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {# Emp Id and Offer Id are read-only #}
                        <div>
                            <label for="{{ form.emp_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.emp_id.label }}</label>
                            {{ form.emp_id }}
                        </div>
                        <div>
                            <label for="{{ form.offer_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.offer_id.label }}</label>
                            {{ form.offer_id }}
                        </div>
                        <div class="md:col-span-2 flex flex-wrap items-center space-x-2">
                            <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.title.label }}</label>
                            {{ form.title }}
                            <label for="{{ form.employee_name.id_for_label }}" class="sr-only">{{ form.employee_name.label }}</label> {# Screen reader only label #}
                            {{ form.employee_name }}
                            {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                        </div>
                        {# Remaining fields in Official Info #}
                        <div><label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.designation.label }}</label>{{ form.designation }}{% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.department.label }}</label>{{ form.department }}{% if form.department.errors %}<p class="text-red-500 text-xs mt-1">{{ form.department.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.swap_card_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.swap_card_no.label }}</label>{{ form.swap_card_no }}{% if form.swap_card_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.swap_card_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.directors_name.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.directors_name.label }}</label>{{ form.directors_name }}</div>
                        <div><label for="{{ form.bg_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.bg_group.label }}</label>{{ form.bg_group }}{% if form.bg_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bg_group.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.group_leader.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.group_leader.label }}</label>{{ form.group_leader }}</div>
                        <div><label for="{{ form.dept_head.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.dept_head.label }}</label>{{ form.dept_head }}</div>
                        <div><label for="{{ form.mobile_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.mobile_no.label }}</label>{{ form.mobile_no }}{% if form.mobile_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.mobile_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.grade.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.grade.label }}</label>{{ form.grade }}{% if form.grade.errors %}<p class="text-red-500 text-xs mt-1">{{ form.grade.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.contact_no.label }}</label>{{ form.contact_no }}{% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.company_email.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.company_email.label }}</label>{{ form.company_email }}{% if form.company_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.company_email.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.erp_mail.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.erp_mail.label }}</label>{{ form.erp_mail }}{% if form.erp_mail.errors %}<p class="text-red-500 text-xs mt-1">{{ form.erp_mail.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.extension_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.extension_no.label }}</label>{{ form.extension_no }}{% if form.extension_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.extension_no.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.joining_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.joining_date.label }}</label>{{ form.joining_date }}{% if form.joining_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.joining_date.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.resignation_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.resignation_date.label }}</label>{{ form.resignation_date }}{% if form.resignation_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.resignation_date.errors }}</p>{% endif %}
                            <p class="text-red-500 text-xs mt-1">* Reset Swap Card No and Corp. Mobile No. to Not Applicable.</p>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="button" @click="activeTab = 1" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Next</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>

                <!-- Personal Info Tab Content -->
                <div x-show="activeTab === 1" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="{{ form.permanent_address.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.permanent_address.label }}</label>
                            {{ form.permanent_address }}
                            {% if form.permanent_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.permanent_address.errors }}</p>{% endif %}
                        </div>
                        <div class="md:col-span-2">
                            <label for="{{ form.correspondence_address.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.correspondence_address.label }}</label>
                            {{ form.correspondence_address }}
                            {% if form.correspondence_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.correspondence_address.errors }}</p>{% endif %}
                        </div>
                        <div><label for="{{ form.personal_email.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.personal_email.label }}</label>{{ form.personal_email }}{% if form.personal_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.personal_email.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.date_of_birth.label }}</label>{{ form.date_of_birth }}{% if form.date_of_birth.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_of_birth.errors }}</p>{% endif %}</div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ form.gender.label }}</label>
                            <div class="flex space-x-4">
                                {% for radio in form.gender %}
                                    <label class="inline-flex items-center">
                                        {{ radio.tag }}
                                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                            {% if form.gender.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gender.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ form.marital_status.label }}</label>
                            <div class="flex space-x-4">
                                {% for radio in form.marital_status %}
                                    <label class="inline-flex items-center">
                                        {{ radio.tag }}
                                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                            {% if form.marital_status.errors %}<p class="text-red-500 text-xs mt-1">{{ form.marital_status.errors }}</p>{% endif %}
                        </div>
                        <div><label for="{{ form.blood_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.blood_group.label }}</label>{{ form.blood_group }}{% if form.blood_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.blood_group.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.height.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.height.label }}</label>{{ form.height }}{% if form.height.errors %}<p class="text-red-500 text-xs mt-1">{{ form.height.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.weight.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.weight.label }}</label>{{ form.weight }}{% if form.weight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.weight.errors }}</p>{% endif %}</div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ form.physically_handicapped.label }}</label>
                            <div class="flex space-x-4">
                                {% for radio in form.physically_handicapped %}
                                    <label class="inline-flex items-center">
                                        {{ radio.tag }}
                                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                            {% if form.physically_handicapped.errors %}<p class="text-red-500 text-xs mt-1">{{ form.physically_handicapped.errors }}</p>{% endif %}
                        </div>
                        <div><label for="{{ form.religion.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.religion.label }}</label>{{ form.religion }}{% if form.religion.errors %}<p class="text-red-500 text-xs mt-1">{{ form.religion.errors }}</p>{% endif %}</div>
                        <div><label for="{{ form.caste.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.caste.label }}</label>{{ form.caste }}{% if form.caste.errors %}<p class="text-red-500 text-xs mt-1">{{ form.caste.errors }}</p>{% endif %}</div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="button" @click="activeTab = 2" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Next</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>

                <!-- Edu. Quali. & Work Experience Tab Content -->
                <div x-show="activeTab === 2" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><label for="{{ form.educational_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.educational_qualification.label }}</label>{{ form.educational_qualification }}{% if form.educational_qualification.errors %}<p class="text-red-500 text-xs mt-1">{{ form.educational_qualification.errors }}</p>{% endif %}</div>
                        <div class="md:col-span-2"><label for="{{ form.additional_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.additional_qualification.label }}</label>{{ form.additional_qualification }}</div>
                        <div><label for="{{ form.last_company_name.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.last_company_name.label }}</label>{{ form.last_company_name }}</div>
                        <div><label for="{{ form.total_experience.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.total_experience.label }}</label>{{ form.total_experience }}</div>
                        <div><label for="{{ form.working_duration.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.working_duration.label }}</label>{{ form.working_duration }}</div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="button" @click="activeTab = 3" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Next</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>

                <!-- Others Tab Content -->
                <div x-show="activeTab === 3" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><label for="{{ form.current_ctc.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.current_ctc.label }}</label>{{ form.current_ctc }}</div>
                        <div><label for="{{ form.bank_account_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.bank_account_no.label }}</label>{{ form.bank_account_no }}</div>
                        <div><label for="{{ form.pf_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.pf_no.label }}</label>{{ form.pf_no }}</div>
                        <div><label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.pan_no.label }}</label>{{ form.pan_no }}</div>
                        <div><label for="{{ form.passport_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.passport_no.label }}</label>{{ form.passport_no }}</div>
                        <div><label for="{{ form.expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.expiry_date.label }}</label>{{ form.expiry_date }}{% if form.expiry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.expiry_date.errors }}</p>{% endif %}</div>
                        <div class="md:col-span-2"><label for="{{ form.additional_information.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.additional_information.label }}</label>{{ form.additional_information }}</div>
                        
                        {# Photo Upload/Management #}
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">{{ form.photo_file.label }}</label>
                            {% if current_photo_file_name %}
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="text-sm text-gray-900">{{ current_photo_file_name }}</span>
                                    {# HTMX button to trigger file deletion #}
                                    <button 
                                        type="button" 
                                        hx-post="{% url 'hr:file_delete' pk=staff_userid file_type='photo' %}" 
                                        hx-target="#staffEditFormContainer" {# Target the whole container to refresh #}
                                        hx-swap="outerHTML" {# Replace the whole container #}
                                        hx-confirm="Are you sure you want to remove the photo?"
                                        class="text-red-500 hover:text-red-700 text-xs focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg> Remove
                                    </button>
                                </div>
                                <span class="text-xs text-gray-500 block mt-1">Upload new file to replace.</span>
                            {% else %}
                                {{ form.photo_file }}
                            {% endif %}
                            {{ form._photo_data_unchanged }} {# Hidden field to preserve existing data #}
                        </div>

                        {# CV Upload/Management #}
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">{{ form.cv_file.label }}</label>
                            {% if current_cv_file_name %}
                                <div class="flex items-center space-x-2 mt-2">
                                    {# Link to download existing CV #}
                                    <a href="{% url 'hr:file_download' pk=staff_userid file_type='cv' %}" class="text-blue-600 hover:underline text-sm">{{ current_cv_file_name }}</a>
                                    {# HTMX button to trigger file deletion #}
                                    <button 
                                        type="button" 
                                        hx-post="{% url 'hr:file_delete' pk=staff_userid file_type='cv' %}" 
                                        hx-target="#staffEditFormContainer" {# Target the whole container to refresh #}
                                        hx-swap="outerHTML" {# Replace the whole container #}
                                        hx-confirm="Are you sure you want to remove the CV?"
                                        class="text-red-500 hover:text-red-700 text-xs focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg> Remove
                                    </button>
                                </div>
                                <span class="text-xs text-gray-500 block mt-1">Upload new file to replace.</span>
                            {% else %}
                                {{ form.cv_file }}
                            {% endif %}
                            {{ form._cv_data_unchanged }} {# Hidden field to preserve existing data #}
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="submit" class="redbox bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Update</button>
                        <a href="{% url 'hr:officestaff_list' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm ml-2">Cancel</a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js initialization for tab management and HTMX event listeners #}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for the staff edit form
        Alpine.data('staffEditForm', () => ({
            activeTab: 0, // Default active tab
            // The init function runs when the component is initialized
            init() {
                // Restore last active tab from session storage if available
                const storedTabIndex = sessionStorage.getItem('staff_edit_active_tab');
                if (storedTabIndex !== null) {
                    this.activeTab = parseInt(storedTabIndex);
                } else {
                    // Otherwise, use the value passed from Django context (initial_tab_index)
                    this.activeTab = parseInt('{{ initial_tab_index }}');
                }

                // Watch for changes in activeTab and save to session storage
                this.$watch('activeTab', value => {
                    sessionStorage.setItem('staff_edit_active_tab', value);
                });
            }
        }));
    });

    // HTMX event listener for when the form content needs to be reloaded (e.g., after file removal)
    document.body.addEventListener('fileRemoved', function(event) {
        // When a file is removed, HTMX will swap the `staffEditFormContainer` div with updated content
        // This event ensures Alpine.js re-initializes on the new content.
        // The hx-target/hx-swap on the file removal buttons ensure this div is updated.
        console.log('File removed, re-initializing Alpine.js on form container.');
        // Re-init Alpine on the new content if it's not handled automatically by hx-swap
        const formContainer = document.getElementById('staffEditFormContainer');
        if (formContainer) {
            Alpine.init(formContainer);
        }
    });

    // HTMX event listener for success messages after form submission
    document.body.addEventListener('staffUpdated', function(event) {
        // If the form is submitted via HTMX, this event is triggered.
        // It provides a client-side indication of success.
        console.log('Staff details updated successfully.');
        // Redirect to the staff list page after update
        window.location.href = '{% url "hr:officestaff_list" %}';
    });
</script>
{% endblock %}