{% if employees %}
    {% for employee in employees %}
        <div class="py-2 px-4 cursor-pointer hover:bg-gray-100 autocomplete-item"
             data-emp-id="{{ employee.emp_id }}"
             data-emp-name="{{ employee.employee_name }} [{{ employee.emp_id }}]">
            {{ employee.employee_name }} [{{ employee.emp_id }}]
        </div>
    {% endfor %}
{% else %}
    <div class="py-2 px-4 text-gray-500">No results found.</div>
{% endif %}