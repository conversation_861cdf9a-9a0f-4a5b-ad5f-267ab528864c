{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Gate Pass - Print / Search</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'gatepass_table' %}" 
              hx-target="#gatepass-results-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date:</label>
                    {{ form.from_date }}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date:</label>
                    {{ form.to_date }}
                </div>
                <div class="relative">
                    <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name:</label>
                    {{ form.employee_name }}
                    <!-- Suggestions will be swapped here by HTMX -->
                    <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto w-full"></div>
                </div>
                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm w-full md:w-auto">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading indicator for HTMX requests -->
    <div id="loading-indicator" class="text-center py-4 htmx-indicator" style="display:none;">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading search results...</p>
    </div>

    <!-- Container for the HTMX-loaded DataTables content -->
    <div id="gatepass-results-container" 
         hx-trigger="load, searchGatePasses from:body" 
         hx-get="{% url 'gatepass_table' %}" 
         hx-swap="innerHTML">
        <!-- Initial content before load/search -->
        <div class="text-center py-8">
            <p class="text-gray-500">Enter search criteria and click 'Search' to view gate passes.</p>
        </div>
    </div>

    <!-- Universal Modal for Create/Update/Delete operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
            <!-- Modal content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be initialized here if more complex UI state is needed
    });

    // Re-initialize DataTables after HTMX swaps the table content
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'gatepass-results-container') {
            // Destroy existing DataTable instance if it exists
            if ($.fn.DataTable.isDataTable('#gatePassTable')) {
                $('#gatePassTable').DataTable().destroy();
            }
            // Re-initialize DataTable
            $('#gatePassTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Handle employee autocomplete suggestions
    document.getElementById('id_employee_name').addEventListener('htmx:afterOnLoad', function(evt) {
        const suggestionsContainer = document.getElementById('employee-suggestions');
        try {
            const data = JSON.parse(evt.detail.xhr.responseText);
            if (data && data.length > 0) {
                suggestionsContainer.innerHTML = data.map(item => `
                    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer" 
                         onclick="document.getElementById('id_employee_name').value='${item}'; document.getElementById('employee-suggestions').innerHTML='';">
                        ${item}
                    </div>
                `).join('');
                suggestionsContainer.classList.remove('hidden');
            } else {
                suggestionsContainer.innerHTML = '';
                suggestionsContainer.classList.add('hidden');
            }
        } catch (e) {
            console.error("Error parsing autocomplete response:", e);
            suggestionsContainer.innerHTML = '';
            suggestionsContainer.classList.add('hidden');
        }
    });

    // Hide suggestions when input loses focus (unless clicking a suggestion)
    document.getElementById('id_employee_name').addEventListener('focusout', function(evt) {
        // A small delay to allow click event on suggestion to fire first
        setTimeout(() => {
            const suggestionsContainer = document.getElementById('employee-suggestions');
            if (!suggestionsContainer.contains(document.activeElement)) {
                suggestionsContainer.classList.add('hidden');
                suggestionsContainer.innerHTML = '';
            }
        }, 100);
    });

    document.getElementById('id_employee_name').addEventListener('focus', function(evt) {
         const suggestionsContainer = document.getElementById('employee-suggestions');
         if (suggestionsContainer.innerHTML.trim() !== '') {
            suggestionsContainer.classList.remove('hidden');
         }
    });
</script>
{% endblock %}