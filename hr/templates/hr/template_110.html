{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Consolidated Salary Summary Report</h2>
        <!-- No direct 'Add New' for a report. This button is hypothetical for prompt. -->
        <!-- In a real scenario, this might link to a data entry page for underlying data. -->
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded hidden"
            hx-get="{% url 'salarysummaryreport_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Report Entry (Simulated)
        </button>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Report Parameters:</h3>
        <p><strong>Month:</strong> {{ month_id|default:"N/A" }}</p>
        <p><strong>Company Address:</strong> {{ company_address|default:"N/A" }}</p>
        <!-- Add more parameters if needed -->
    </div>

    <div id="salarysummaryreportTable-container"
         hx-trigger="load, refreshSalaryReportEntryList from:body"
         hx-get="{% url 'salarysummaryreport_table' %}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}"
         hx-swap="innerHTML"
         class="bg-white p-4 rounded-lg shadow-md">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Cancel button, mimicking ASP.NET's -->
    <div class="text-center mt-6">
        <a href="{% url 'salary_print_redirect' %}?month_id={{ month_id|default:'' }}" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded inline-flex items-center">
           Cancel
        </a>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
        Alpine.data('reportLogic', () => ({
            init() {
                // Any specific Alpine.js initialization for the report
            }
        }));
    });
</script>
{% endblock %}