<form hx-post="{% url 'mobile_bills_edit_list' %}" hx-swap="none" id="mobile-bill-update-form">
    {% csrf_token %}
    <input type="hidden" name="bill_month" value="{{ selected_month }}" />

    {{ formset.management_form }}

    <table id="mobileBillTable" class="min-w-full bg-white border border-gray-200 shadow-sm">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Limit Amt</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Amt</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taxes</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excess Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for form in formset %}
            <tr x-data="{ row: { isChecked: {{ form.is_checked.value|lower }} } }">
                <td class="py-3 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-center">
                    {{ form.is_checked }}
                    {{ form.mobile_bill_db_id }} {# Hidden field to carry DB ID for update #}
                    {{ form.emp_id }} {# Hidden field to carry EmpId #}
                    {{ form.user_id }} {# Hidden field to carry UserId #}
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-center">{{ form.employee_name.value }}</td>
                <td class="py-3 px-4 border-b border-gray-200">{{ form.employee_name.value }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-center">{{ form.mobile_no.value }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-right">{{ form.limit_amt.value|default_if_none:"0.00" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-right relative">
                    <span x-show="!row.isChecked" class="inline-block w-full text-right py-2">{{ form.bill_amt.value|default_if_none:"0.00" }}</span>
                    <span x-show="row.isChecked">
                        {{ form.bill_amt }}
                        {% if form.bill_amt.errors %}<span class="text-red-500 text-xs absolute -bottom-1 left-0">{{ form.bill_amt.errors.as_text }}</span>{% endif %}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-right relative">
                    <span x-show="!row.isChecked" class="inline-block w-full text-right py-2">{{ form.taxes_fk.value|default_if_none:"0.00" }}%</span>
                    <span x-show="row.isChecked">
                        {{ form.taxes }}
                         {% if form.taxes.errors %}<span class="text-red-500 text-xs absolute -bottom-1 left-0">{{ form.taxes.errors.as_text }}</span>{% endif %}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-right">
                    <span x-show="row.isChecked">{{ form.excess_amount.value|default_if_none:"0.00" }}</span>
                    <span x-show="!row.isChecked">{{ form.excess_amount.value|default_if_none:"0.00" }}</span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="9" class="py-3 px-4 border-t border-gray-200 text-right">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Update All Checked
                    </button>
                </td>
            </tr>
        </tfoot>
    </table>
</form>

<script>
    // Initialize DataTables if not already initialized
    // This script block will run each time the partial is swapped in.
    // The htmx:afterSwap listener in the main template handles this more robustly.
    // This inline script primarily helps with initial render or direct partial loads.
    if (typeof $.fn.DataTable === 'function' && !$.fn.DataTable.isDataTable('#mobileBillTable')) {
        $('#mobileBillTable').DataTable({
            "pageLength": 20,
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true
        });
    }
</script>