{# tour_intimation/_wo_group_toggle.html #}
<div id="wo-group-container">
    <label class="block text-sm font-medium text-gray-700">
        WO No / BG Group
    </label>
    <div class="flex items-center space-x-4 mb-2">
        {% for radio in form.wo_no_group_selection %}
            <label class="inline-flex items-center">
                {{ radio.tag }}
                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
            </label>
        {% endfor %}
    </div>
    {% if selection == '0' %} {# WO No selected #}
        <input type="text" name="WONo" id="id_WONo" value="{{ wo_no_value }}" class="box3">
        <select name="BGGroupId" id="id_BGGroupId" class="box3 hidden" disabled>
            {# Hidden/disabled because not active #}
            {% for group in business_groups %}
                <option value="{{ group.Id }}" {% if group.Id == bg_group_selected_value %}selected{% endif %}>{{ group.Symbol }}</option>
            {% endfor %}
        </select>
    {% else %} {# BG Group selected #}
        <input type="text" name="WONo" id="id_WONo" value="{{ wo_no_value }}" class="box3 hidden" disabled>
        <select name="BGGroupId" id="id_BGGroupId" class="box3">
            {% for group in business_groups %}
                <option value="{{ group.Id }}" {% if group.Id == bg_group_selected_value %}selected{% endif %}>{{ group.Symbol }}</option>
            {% endfor %}
        </select>
    {% endif %}
</div>