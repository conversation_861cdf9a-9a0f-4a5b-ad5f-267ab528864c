{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Mobile Bills - New Entry</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex items-center space-x-4">
            <label for="id_bill_month" class="block text-sm font-medium text-gray-700">Month Of Bill:</label>
            {{ bill_month_form.bill_month }}
        </div>
    </div>
    
    <form hx-post="{% url 'hr_mobile_bills:mobilebill_table' %}" hx-swap="none" id="mobileBillForm">
        {% csrf_token %}
        <input type="hidden" name="bill_month_selected" value="" id="bill_month_selected_hidden">
        
        <div id="mobileBillTable-container"
             hx-trigger="load, refreshMobileBillTable from:body"
             hx-get="{% url 'hr_mobile_bills:mobilebill_table' %}"
             hx-target="#mobileBillTable-container"
             hx-swap="innerHTML">
            <!-- Initial loading state -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading mobile bill data...</p>
            </div>
        </div>
    </form>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
    >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto"
             _="on htmx:afterSwap put .is-active on #modal">
            <!-- Content will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterRequest', function(evt) {
        // Close modal after successful HTMX form submission
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.add('hidden'); // Close modal
            }
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        const billMonthDropdown = document.getElementById('id_bill_month');
        const hiddenBillMonthInput = document.getElementById('bill_month_selected_hidden');

        if (billMonthDropdown && hiddenBillMonthInput) {
            // Update hidden input on page load and on change
            hiddenBillMonthInput.value = billMonthDropdown.value;
            billMonthDropdown.addEventListener('change', function() {
                hiddenBillMonthInput.value = this.value;
            });
        }
    });
</script>
{% endblock %}