{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Employee Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'employee_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-user-plus mr-2"></i> Add New Employee
        </button>
    </div>
    
    <div id="employeeTable-container"
         hx-trigger="load, refreshEmployeeList from:body" {# Loads on page load, and on custom trigger #}
         hx-get="{% url 'employee_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Employees...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 ease-out"
             _="on modal.active then add .scale-100 .opacity-100 remove .scale-95 .opacity-0 else remove .scale-100 .opacity-100 add .scale-95 .opacity-0">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.2/js/all.min.js"></script>
<script>
    // Alpine.js is typically initialized globally in base.html if used for complex state.
    // For simple modal show/hide, htmx+Alpine can work via _ syntax.
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">
{% endblock %}