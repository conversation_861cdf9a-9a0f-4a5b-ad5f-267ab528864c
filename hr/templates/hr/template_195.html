{# hr_staff/officestaff/_officestaff_table.html #}

<div class="overflow-x-auto">
    <table id="officestaffTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th class="py-3 px-4 border-b border-gray-200">SN</th>
                <th class="py-3 px-4 border-b border-gray-200">Emp ID</th>
                <th class="py-3 px-4 border-b border-gray-200">Employee Name</th>
                <th class="py-3 px-4 border-b border-gray-200">Designation</th>
                <th class="py-3 px-4 border-b border-gray-200">Department</th>
                <th class="py-3 px-4 border-b border-gray-200">Joining Date</th>
                <th class="py-3 px-4 border-b border-gray-200">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for staff in office_staff_list %}
            <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-4 text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.emp_id }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.title }} {{ staff.employee_name }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.designation.type|default:'N/A' }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.department.description|default:'N/A' }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ staff.joining_date|date:"d-m-Y"|default:'N/A' }}</td>
                <td class="py-3 px-4 text-sm text-gray-700 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'officestaff_edit' staff.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'officestaff_delete' staff.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center text-gray-500">No staff records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization will be handled by the parent list.html via htmx:afterSwap #}