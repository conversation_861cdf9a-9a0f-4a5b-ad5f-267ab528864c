<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">Edit News Notice</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-encoding="multipart/form-data">
        {% csrf_token %}

        <div class="space-y-5">
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.title.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.title.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.in_details.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.in_details.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.in_details }}
                {% if form.in_details.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.in_details.errors }}</p>
                {% endif %}
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.from_date.label }}<span class="text-red-500">*</span>
                    </label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.to_date.label }}<span class="text-red-500">*</span>
                    </label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                    {% endif %}
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Upload File</label>
                {% if has_file %}
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-sm text-gray-800">{{ current_file_name }}</span>
                    <button
                        type="button"
                        class="text-red-500 hover:text-red-700 focus:outline-none"
                        title="Clear File"
                        hx-post="{% url 'newsnotice_clear_file' object.pk %}"
                        hx-confirm="Are you sure you want to remove the attached file?"
                        hx-swap="none"
                        hx-trigger="click"
                        _="on htmx:afterRequest remove .flex from #modal then trigger refreshNewsNoticeList">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                    <a href="{{ object.get_file_download_url }}" class="text-blue-600 hover:underline text-sm ml-2">Download Current File</a>
                </div>
                {% endif %}
                {{ form.uploaded_file }}
                {% if form.uploaded_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uploaded_file.errors }}</p>
                {% endif %}
            </div>
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .flex from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-indicator="#loadingSpinner"
                onclick="return confirm('Are you sure you want to update this News Notice?');" >
                Update
            </button>
            <div id="loadingSpinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
        {% if form.non_field_errors %}
        <div class="mt-4 text-red-500 text-sm">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}
    </form>
</div>