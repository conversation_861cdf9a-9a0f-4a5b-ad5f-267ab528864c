{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Salary Summary Report</h2>
        <!-- Cancel button similar to ASP.NET -->
        <a href="{% url 'hr_salary_report_selection' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
    
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded-md" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    {% if error_message %}
        <div class="text-red-600 text-lg text-center">{{ error_message }}</div>
    {% else %}
        <div id="salarySummaryRowTable-container"
             hx-trigger="load, refreshSalarySummaryRowList from:body"
             hx-get="{% url 'salary_summary_table' %}?BGGroupId={{ request.GET.BGGroupId }}&MonthId={{ request.GET.MonthId }}&EType={{ request.GET.EType }}&Key={{ request.GET.Key }}"
             hx-swap="innerHTML"
             class="bg-white shadow-md rounded-lg p-6">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Report Data...</p>
            </div>
        </div>
    {% endif %}
    
    <!-- Modal for form (not used on this report page, but kept for pattern consistency) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management
    });
</script>
{% endblock %}