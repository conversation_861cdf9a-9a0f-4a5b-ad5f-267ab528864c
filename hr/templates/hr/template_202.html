<!-- hr/officestaff/list.html (Main list page template, for DataTables) -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Office Staff Members</h2>
        {# A conceptual button for adding new staff, redirecting to a create view if implemented #}
        {# This specific ASP.NET page was for EDIT, not CREATE, but a list would have ADD #}
        <a href="#" 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
            Add New Staff Member
        </a>
    </div>
    
    {# Container for the DataTable, loaded via HTMX #}
    <div id="officestaffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'hr:officestaff_table' %}" {# URL to fetch the table partial #}
         hx-swap="innerHTML" {# Replaces the content inside this div #}
         class="min-h-[200px] flex items-center justify-center bg-white rounded-lg shadow-md">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading staff data...</p>
        </div>
    </div>
    
    {# Modal structure (managed by Alpine.js) for edit/delete forms #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false" {# Listens for 'close-modal' event to hide #}
         x-on:open-modal.window="showModal = true" {# Listens for 'open-modal' event to show #}
         x-on:click.self="showModal = false"> {# Click outside modal content to close #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-4 overflow-y-auto max-h-[90vh]" 
             x-show="showModal" 
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
             <!-- Content loaded by HTMX (e.g., _officestaff_form.html, _officestaff_confirm_delete.html) -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# JavaScript block for Alpine.js and HTMX event listeners #}
<script>
    // Listen for custom HTMX events to refresh the staff list or close modals
    document.body.addEventListener('staffUpdated', function() {
        // Trigger a refresh of the staff table after a successful update/create/delete
        htmx.trigger(document.body, 'refreshOfficeStaffList');
        // Close the modal
        htmx.trigger('#modal', 'close-modal');
    });

    // Initialize Alpine.js after DOM is fully loaded or when HTMX swaps content
    document.addEventListener('DOMContentLoaded', () => {
        if (typeof Alpine !== 'undefined') {
            Alpine.start();
        }
    });
</script>
{% endblock %}