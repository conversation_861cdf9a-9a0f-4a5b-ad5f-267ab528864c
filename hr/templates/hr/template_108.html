{# hr_payroll_reports/templates/hr_payroll_reports/monthly_salary_summary_report.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 border-b pb-4">
            <div>
                <h2 class="text-2xl font-extrabold text-gray-900 mb-2">{{ report_title }}</h2>
                {% if company_address %}
                <p class="text-sm text-gray-600 mb-2">{{ company_address|linebreaksbr }}</p>
                {% endif %}
            </div>
            <div class="mt-4 md:mt-0">
                <a href="{% url 'hr_payroll_reports:salary_print_redirect' %}?MonthId={{ request.GET.MonthId|default:1 }}"
                   class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                    Cancel
                </a>
            </div>
        </div>

        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="p-4 text-sm {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% endif %} rounded-lg" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <div id="salarySummaryTable-container"
             hx-trigger="load, hx:afterSwap" {# 'load' on initial page load, 'hx:afterSwap' to re-initialize DataTables #}
             hx-get="{% url 'hr_payroll_reports:monthly_salary_summary_table_partial' %}?MonthId={{ request.GET.MonthId|default:1 }}&BGGroupId={{ request.GET.BGGroupId|default:0 }}&EType={{ request.GET.EType|default:0 }}"
             hx-swap="innerHTML">
            <!-- Loading indicator while HTMX fetches the table -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
                <p class="mt-4 text-lg text-gray-700">Loading Report Data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js is not directly needed for this specific report page, but included as per guidelines #}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    // Re-initialize DataTables after HTMX swaps the content
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.target.id === 'salarySummaryTable-container') {
            // Ensure DataTables is properly destroyed and re-initialized if it exists
            if ($.fn.DataTable.isDataTable('#monthlySalarySummaryTable')) {
                $('#monthlySalarySummaryTable').DataTable().destroy();
            }
            $('#monthlySalarySummaryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "scrollX": true, // Enable horizontal scrolling for many columns
                "responsive": true, // Make table responsive
                "dom": 'lfrtipB', // Add Buttons to the DOM
                "buttons": [
                    'copyHtml5',
                    'excelHtml5',
                    'csvHtml5',
                    'pdfHtml5'
                ]
            });
        }
    });

    // Initial DataTables setup on page load (if not loaded via HTMX right away)
    // This might be redundant if hx-trigger="load" handles it immediately,
    // but useful for direct page access or if HTMX fails.
    $(document).ready(function() {
        if (!$("#salarySummaryTable-container").children().length) {
            // Only initialize if HTMX hasn't already loaded content
            if ($.fn.DataTable.isDataTable('#monthlySalarySummaryTable')) {
                $('#monthlySalarySummaryTable').DataTable().destroy();
            }
            $('#monthlySalarySummaryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "scrollX": true,
                "responsive": true,
                "dom": 'lfrtipB',
                "buttons": [
                    'copyHtml5',
                    'excelHtml5',
                    'csvHtml5',
                    'pdfHtml5'
                ]
            });
        }
    });

</script>
{% endblock %}