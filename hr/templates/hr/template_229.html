<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the news item: "{{ newsnotice.title }}"?</p>
    
    <div class="mt-6 flex justify-end space-x-4">
        <button 
            type="button" 
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
            _="on click remove .flex from #modal then add .hidden to #modal">
            Cancel
        </button>
        <button 
            type="button"
            hx-delete="{% url 'newsnotice_delete' newsnotice.pk %}"
            hx-swap="none"
            hx-indicator="#loadingDelete"
            class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
            Delete
            <span id="loadingDelete" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
        </button>
    </div>
</div>