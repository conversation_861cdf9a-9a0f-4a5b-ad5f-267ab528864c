<!-- hr_transactions/templates/hr_transactions/employee/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Employees Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md"
            hx-get="{% url 'employee_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Employee
        </button>
    </div>
    
    <div id="employeeTable-container"
         hx-trigger="load, refreshEmployeeList from:body"
         hx-get="{% url 'employee_table' %}"
         hx-swap="innerHTML">
        <!-- Loading indicator for HTMX -->
        <div class="flex justify-center items-center h-48" hx-indicator="#loadingIndicator">
            <div id="loadingIndicator" class="htmx-indicator">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Employees...</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me then remove innerHTML of #modalContent">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0 transform transition-all sm:my-8 sm:align-middle">
            <!-- Content will be loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any Alpine.js components here if needed for broader page state -->
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });

        // Listen for HX-Trigger events to close modal
        document.body.addEventListener('htmx:afterSwap', (event) => {
            if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
                document.getElementById('modal').classList.remove('is-active');
            }
        });
    });
</script>
{% endblock %}