{% extends 'core/base.html' %}

{% block title %}Mobile Bill Report{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Mobile Bill - Print</h2>
        
        <form class="space-y-4">
            <div class="flex items-center space-x-4">
                <label for="{{ form.month.id_for_label }}" class="block text-sm font-medium text-gray-700">Month Of Bill:</label>
                <div>
                    {{ form.month }}
                    {% if form.month.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.month.errors }}</p>
                    {% endif %}
                </div>
            </div>
        </form>
    </div>

    <!-- Loading Indicator for HTMX requests -->
    <div id="loadingIndicator" class="text-center htmx-indicator hidden">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading report...</p>
    </div>

    <!-- HTMX Target for Report Content -->
    <div id="reportContent" class="bg-white shadow-md rounded-lg p-6 min-h-[415px]">
        <p class="text-gray-600 text-center">Please select a month to view the mobile bill report.</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is not strictly necessary for this simple interaction,
        // but can be used for more complex UI state management if needed.
        // For example: x-data="{ showModal: false }"
    });
</script>
{% endblock %}