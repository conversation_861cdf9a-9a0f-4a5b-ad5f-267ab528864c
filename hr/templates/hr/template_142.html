{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Offer Letter Details</h2>
        <p class="text-gray-700">{{ message }}</p>
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-800">Selected Offer Details:</h3>
            <p><strong>Offer ID:</strong> {{ offer_letter.offer_id }}</p>
            <p><strong>Employee Name:</strong> {{ offer_letter.full_employee_name }}</p>
            <p><strong>Date:</strong> {{ offer_letter.formatted_sys_date }}</p>
            <p><strong>Gross Salary:</strong> {{ offer_letter.salary }}</p>
            <p><strong>Increment Value:</strong> {{ increment }}</p>
            <p><strong>Type Of Company:</strong> {{ offer_letter.type_of_company }}</p>
            <p><strong>Key Parameter (Example):</strong> {{ key_param }}</p>
            {# Add more offer_letter details as needed for the print view #}
        </div>
        <div class="mt-8">
            <a href="{% url 'hr_offer:offerletter_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Back to List
            </a>
            <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-md shadow-sm ml-4 transition duration-150 ease-in-out">
                Print
            </button>
        </div>
    </div>
</div>
{% endblock %}