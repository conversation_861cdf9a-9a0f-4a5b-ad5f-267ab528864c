{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Tour Intimation Details: {{ tour_intimation.ti_no }}</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm font-medium text-gray-600">TI No:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.ti_no }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Financial Year:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.financial_year_display }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Employee Name:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.employee }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">WO No:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.work_order_or_na }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Business Group:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.business_group_display }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Project Name:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.project_name }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Place of Tour:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.full_place_of_tour }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Tour Start Date:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.tour_start_date|date:"d M Y" }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Tour End Date:</p>
                <p class="text-lg font-semibold text-gray-900">{{ tour_intimation.tour_end_date|date:"d M Y" }}</p>
            </div>
        </div>

        <div class="mt-8 flex justify-end">
            <a href="{% url 'tourintimation_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Back to List
            </a>
            <button
                class="ml-4 bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                hx-get="{% url 'tourintimation_edit' tour_intimation.pk %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Edit
            </button>
            <button
                class="ml-4 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                hx-get="{% url 'tourintimation_delete' tour_intimation.pk %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Delete
            </button>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterOnLoad from #modalContent if event.detail.xhr.status != 204 add .is-active to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad from #modalContent if event.detail.xhr.status == 204 remove .is-active from #modal">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}