<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Offer Letter for <strong>"{{ object.full_employee_name }}" (ID: {{ object.offer_id }})</strong>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'offermaster_delete' object.offer_id %}" hx-swap="none" hx-indicator="#delete-loading-indicator">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .block from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
        {# Loading indicator for delete #}
        <div id="delete-loading-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Deleting...</p>
        </div>
    </form>
</div>