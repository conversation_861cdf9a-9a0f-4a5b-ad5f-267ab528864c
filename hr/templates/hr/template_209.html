{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-800">Staff Detail Report</h2>
        <a href="{{ back_url }}" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
            <i class="fas fa-arrow-left mr-2"></i>Back to Previous
        </a>
    </div>

    <div class="bg-white shadow-2xl rounded-lg p-8 mb-8 overflow-hidden print-area">
        <div class="text-center mb-10">
            <img src="{% url 'image_view' pk=report_data.CompanyID field_name='logo_image' %}" alt="Company Logo" class="h-24 mx-auto mb-4" onerror="this.onerror=null;this.src='/static/images/default_company_logo.png';">
            <h1 class="text-4xl font-bold text-blue-800 mb-2">{{ report_data.CompanyName }}</h1>
            <p class="text-gray-600 text-lg">{{ report_data.Address }}</p>
        </div>

        <div class="border-t border-gray-200 pt-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="md:col-span-1 flex justify-center items-start">
                    <img src="{% url 'image_view' pk=request.resolver_match.kwargs.pk field_name='photodata' %}" alt="Staff Photo" class="w-48 h-48 object-cover rounded-full shadow-lg border-4 border-blue-200" onerror="this.onerror=null;this.src='/static/images/default_user_image.png';">
                </div>
                <div class="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4 text-lg">
                    <p><strong>Employee Name:</strong> {{ report_data.EmpName }}</p>
                    <p><strong>Card No:</strong> {{ report_data.CardNo }}</p>
                    <p><strong>Department:</strong> {{ report_data.Department }}</p>
                    <p><strong>Business Group:</strong> {{ report_data.BussinessGroup }}</p>
                    <p><strong>Designation:</strong> {{ report_data.Designation }}</p>
                    <p><strong>Grade:</strong> {{ report_data.Grade }}</p>
                    <p><strong>Joining Date:</strong> {{ report_data.joindate }}</p>
                    <p><strong>Resignation Date:</strong> {{ report_data.resigndate }}</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6 text-gray-700 text-base">
                <div class="space-y-4">
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Contact Information</h3>
                    <p><strong>Mobile No:</strong> {{ report_data.MobileNo }}</p>
                    <p><strong>Extension No:</strong> {{ report_data.extNo }}</p>
                    <p><strong>Email:</strong> {{ report_data.email }}</p>
                    <p><strong>Permanent Address:</strong> {{ report_data.padd }}</p>
                    <p><strong>Correspondence Address:</strong> {{ report_data.cadd }}</p>
                    
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4 mt-8">Hierarchy</h3>
                    <p><strong>Director:</strong> {{ report_data.DeptDirector }}</p>
                    <p><strong>Department Head:</strong> {{ report_data.DeptHead }}</p>
                    <p><strong>Group Leader:</strong> {{ report_data.GroupLeader }}</p>
                </div>

                <div class="space-y-4">
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Personal Details</h3>
                    <p><strong>Date of Birth:</strong> {{ report_data.birthdate }}</p>
                    <p><strong>Gender:</strong> {{ report_data.gender }}</p>
                    <p><strong>Martial Status:</strong> {{ report_data.martialstatus }}</p>
                    <p><strong>Blood Group:</strong> {{ report_data.bgp }}</p>
                    <p><strong>Height:</strong> {{ report_data.height }}</p>
                    <p><strong>Weight:</strong> {{ report_data.weight }}</p>
                    <p><strong>Religion:</strong> {{ report_data.religion }}</p>
                    <p><strong>Cast:</strong> {{ report_data.cast }}</p>
                    <p><strong>Physically Handicapped:</strong> {{ report_data.physicalstatus }}</p>

                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4 mt-8">Professional Details</h3>
                    <p><strong>Educational Qualification:</strong> {{ report_data.edu }}</p>
                    <p><strong>Additional Qualification:</strong> {{ report_data.adq }}</p>
                    <p><strong>Last Company:</strong> {{ report_data.lc }}</p>
                    <p><strong>Working Duration:</strong> {{ report_data.wd }}</p>
                    <p><strong>Total Experience:</strong> {{ report_data.te }}</p>
                    <p><strong>Current CTC:</strong> {{ report_data.ctc }}</p>
                </div>

                <div class="col-span-1 md:col-span-2 space-y-4">
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Identification & Financial</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <p><strong>Bank Account No:</strong> {{ report_data.ba }}</p>
                        <p><strong>PF No:</strong> {{ report_data.pf }}</p>
                        <p><strong>PAN No:</strong> {{ report_data.pa }}</p>
                        <p><strong>Passport No:</strong> {{ report_data.ps }}</p>
                        <p><strong>Passport Expiry Date:</strong> {{ report_data.ex }}</p>
                    </div>
                    <p><strong>Additional Information:</strong> {{ report_data.inf }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Add print-specific styles */
    @media print {
        body {
            font-size: 10pt; /* Adjust font size for print */
        }
        .container {
            max-width: none; /* Remove max-width for full page print */
            margin: 0;
            padding: 0;
        }
        .bg-white, .shadow-2xl, .rounded-lg, .mb-8 {
            box-shadow: none !important;
            border-radius: 0 !important;
            margin-bottom: 0 !important;
            padding: 0 !important;
        }
        .flex.justify-between.items-center.mb-6, .bg-red-600 {
            display: none; /* Hide header and back button */
        }
        .print-area {
            page-break-after: always; /* Ensure new page for each report if multiple */
        }
    }
</style>
{% endblock %}