<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="gatePassReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GP No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type For</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feedback</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Self/Other Emp</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if gatepass_report_items %}
                {% for item in gatepass_report_items %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.gate_pass_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.sys_date_formatted_display }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.from_date_formatted_display }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.from_time }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.to_time }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.reason_type_text }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.display_type_for }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.reason_text }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.authorized_by_display }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.authorized_date_display_formatted }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.authorized_time_display }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.feedback }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.employee_involved_name }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.place }}</td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ item.contact_person }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.contact_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.requesting_employee_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.business_group_symbol }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="19" class="py-4 px-4 text-center text-sm text-gray-500">No Gate Pass records found for the selected criteria.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- This script block will be executed after HTMX swaps the content -->
<script>
    $(document).ready(function() {
        // Ensure DataTables is initialized only once for this table
        if (!$.fn.DataTable.isDataTable('#gatePassReportTable')) {
            $('#gatePassReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true,
                "order": [[ 0, "asc" ]] 
            });
        }
    });
</script>