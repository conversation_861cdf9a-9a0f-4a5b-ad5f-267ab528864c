{# hr_staff/officestaff/form.html #}

<div class="p-6" x-data="{ activeTab: 1 }"> {# Alpine.js for tab management #}
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Staff Details</h3>
    <form hx-post="{{ request.path }}" hx-encoding="multipart/form-data"> {# multipart for file uploads #}
        {% csrf_token %}
        
        <div class="mb-6 border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button type="button" @click="activeTab = 1" :class="{'border-blue-500 text-blue-600': activeTab === 1, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 1}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Official Info
                </button>
                <button type="button" @click="activeTab = 2" :class="{'border-blue-500 text-blue-600': activeTab === 2, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 2}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Personal Info
                </button>
                <button type="button" @click="activeTab = 3" :class="{'border-blue-500 text-blue-600': activeTab === 3, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 3}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Edu. Quali. & Work Experience
                </button>
                <button type="button" @click="activeTab = 4" :class="{'border-blue-500 text-blue-600': activeTab === 4, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 4}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-150 ease-in-out">
                    Others
                </button>
            </nav>
        </div>

        <div x-show="activeTab === 1" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Official Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name of Employee</label>
                    <div class="mt-1 flex items-center space-x-2">
                        {{ form.title }}
                        {{ form.employee_name }}
                    </div>
                    {% if form.title.errors %}<p class="text-red-500 text-xs mt-1">{{ form.title.errors }}</p>{% endif %}
                    {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">Designation</label>
                    <div class="mt-1">{{ form.designation }}</div>
                    {% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">Department</label>
                    <div class="mt-1">{{ form.department }}</div>
                    {% if form.department.errors %}<p class="text-red-500 text-xs mt-1">{{ form.department.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.swap_card.id_for_label }}" class="block text-sm font-medium text-gray-700">Swap Card No</label>
                    <div class="mt-1">{{ form.swap_card }}</div>
                    {% if form.swap_card.errors %}<p class="text-red-500 text-xs mt-1">{{ form.swap_card.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.director_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Director of Dept.</label>
                    <div class="mt-1">{{ form.director_name }}</div>
                    {% if form.director_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.director_name.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">Under BG</label>
                    <div class="mt-1">{{ form.business_group }}</div>
                    {% if form.business_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.business_group.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.group_leader.id_for_label }}" class="block text-sm font-medium text-gray-700">Group Leader</label>
                    <div class="mt-1">{{ form.group_leader }}</div>
                    {% if form.group_leader.errors %}<p class="text-red-500 text-xs mt-1">{{ form.group_leader.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.dept_head.id_for_label }}" class="block text-sm font-medium text-gray-700">Dept. Head</label>
                    <div class="mt-1">{{ form.dept_head }}</div>
                    {% if form.dept_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.dept_head.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.corporate_mobile_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Corp. Mobile No.</label>
                    <div class="mt-1">{{ form.corporate_mobile_no }}</div>
                    {% if form.corporate_mobile_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.corporate_mobile_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.grade.id_for_label }}" class="block text-sm font-medium text-gray-700">Grade</label>
                    <div class="mt-1">{{ form.grade }}</div>
                    {% if form.grade.errors %}<p class="text-red-500 text-xs mt-1">{{ form.grade.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No.</label>
                    <div class="mt-1">{{ form.contact_no }}</div>
                    {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.company_email.id_for_label }}" class="block text-sm font-medium text-gray-700">Company E-Mail</label>
                    <div class="mt-1">{{ form.company_email }}</div>
                    {% if form.company_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.company_email.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.erp_email.id_for_label }}" class="block text-sm font-medium text-gray-700">ERP-Mail</label>
                    <div class="mt-1">{{ form.erp_email }}</div>
                    {% if form.erp_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.erp_email.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.extension_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Extension No.</label>
                    <div class="mt-1">{{ form.extension_no }}</div>
                    {% if form.extension_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.extension_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.joining_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Joining Date</label>
                    <div class="mt-1">{{ form.joining_date }}</div>
                    {% if form.joining_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.joining_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.resignation_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Resignation Date</label>
                    <div class="mt-1">{{ form.resignation_date }}</div>
                    {% if form.resignation_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.resignation_date.errors }}</p>{% endif %}
                    <p class="text-red-500 text-xs mt-1">* Reset Swap Card No and Corp. Mobile No. to Not Applicable.</p>
                </div>
            </div>
            <div class="mt-6 flex justify-end">
                <button type="button" @click="activeTab = 2" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Next
                </button>
            </div>
        </div>

        <div x-show="activeTab === 2" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Personal Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div class="col-span-2">
                    <label for="{{ form.permanent_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Permanent Address</label>
                    <div class="mt-1">{{ form.permanent_address }}</div>
                    {% if form.permanent_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.permanent_address.errors }}</p>{% endif %}
                </div>
                <div class="col-span-2">
                    <label for="{{ form.correspondence_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Correspondence Address</label>
                    <div class="mt-1">{{ form.correspondence_address }}</div>
                    {% if form.correspondence_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.correspondence_address.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.personal_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-Mail</label>
                    <div class="mt-1">{{ form.personal_email }}</div>
                    {% if form.personal_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.personal_email.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700">Date Of Birth</label>
                    <div class="mt-1">{{ form.date_of_birth }}</div>
                    {% if form.date_of_birth.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_of_birth.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.gender.id_for_label }}" class="block text-sm font-medium text-gray-700">Gender</label>
                    <div class="mt-1">{{ form.gender }}</div>
                    {% if form.gender.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gender.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.martial_status.id_for_label }}" class="block text-sm font-medium text-gray-700">Martial Status</label>
                    <div class="mt-1">{{ form.martial_status }}</div>
                    {% if form.martial_status.errors %}<p class="text-red-500 text-xs mt-1">{{ form.martial_status.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.blood_group.id_for_label }}" class="block text-sm font-medium text-gray-700">Blood Group</label>
                    <div class="mt-1">{{ form.blood_group }}</div>
                    {% if form.blood_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.blood_group.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.height.id_for_label }}" class="block text-sm font-medium text-gray-700">Height</label>
                    <div class="mt-1">{{ form.height }}</div>
                    {% if form.height.errors %}<p class="text-red-500 text-xs mt-1">{{ form.height.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.weight.id_for_label }}" class="block text-sm font-medium text-gray-700">Weight</label>
                    <div class="mt-1">{{ form.weight }}</div>
                    {% if form.weight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.weight.errors }}</p>{% endif %}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Physically Handicapped</label>
                    <div class="mt-1">{{ form.physically_handycapped }}</div>
                    {% if form.physically_handycapped.errors %}<p class="text-red-500 text-xs mt-1">{{ form.physically_handycapped.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.religion.id_for_label }}" class="block text-sm font-medium text-gray-700">Religion</label>
                    <div class="mt-1">{{ form.religion }}</div>
                    {% if form.religion.errors %}<p class="text-red-500 text-xs mt-1">{{ form.religion.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.cast.id_for_label }}" class="block text-sm font-medium text-gray-700">Cast</label>
                    <div class="mt-1">{{ form.cast }}</div>
                    {% if form.cast.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cast.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6 flex justify-between">
                <button type="button" @click="activeTab = 1" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Previous
                </button>
                <button type="button" @click="activeTab = 3" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Next
                </button>
            </div>
        </div>

        <div x-show="activeTab === 3" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Educational Qualification & Work Experience</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.educational_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">Educational Qualification</label>
                    <div class="mt-1">{{ form.educational_qualification }}</div>
                    {% if form.educational_qualification.errors %}<p class="text-red-500 text-xs mt-1">{{ form.educational_qualification.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.additional_qualification.id_for_label }}" class="block text-sm font-medium text-gray-700">Additional Qualification</label>
                    <div class="mt-1">{{ form.additional_qualification }}</div>
                    {% if form.additional_qualification.errors %}<p class="text-red-500 text-xs mt-1">{{ form.additional_qualification.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.last_company_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Last Company Name</label>
                    <div class="mt-1">{{ form.last_company_name }}</div>
                    {% if form.last_company_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.last_company_name.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.total_experience.id_for_label }}" class="block text-sm font-medium text-gray-700">Total Experience</label>
                    <div class="mt-1">{{ form.total_experience }}</div>
                    {% if form.total_experience.errors %}<p class="text-red-500 text-xs mt-1">{{ form.total_experience.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.working_duration.id_for_label }}" class="block text-sm font-medium text-gray-700">Working Duration</label>
                    <div class="mt-1">{{ form.working_duration }}</div>
                    {% if form.working_duration.errors %}<p class="text-red-500 text-xs mt-1">{{ form.working_duration.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6 flex justify-between">
                <button type="button" @click="activeTab = 2" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Previous
                </button>
                <button type="button" @click="activeTab = 4" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Next
                </button>
            </div>
        </div>

        <div x-show="activeTab === 4" class="space-y-4 transition-all duration-300 ease-in-out">
            <h4 class="text-lg font-medium text-gray-700 mb-4">Others</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.current_ctc.id_for_label }}" class="block text-sm font-medium text-gray-700">Current CTC</label>
                    <div class="mt-1">{{ form.current_ctc }}</div>
                    {% if form.current_ctc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.current_ctc.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.bank_account_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Acc No.</label>
                    <div class="mt-1">{{ form.bank_account_no }}</div>
                    {% if form.bank_account_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_account_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.pf_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PF No.</label>
                    <div class="mt-1">{{ form.pf_no }}</div>
                    {% if form.pf_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PAN No.</label>
                    <div class="mt-1">{{ form.pan_no }}</div>
                    {% if form.pan_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pan_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.passport_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Passport No.</label>
                    <div class="mt-1">{{ form.passport_no }}</div>
                    {% if form.passport_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.passport_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.passport_expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Expiry Date</label>
                    <div class="mt-1">{{ form.passport_expiry_date }}</div>
                    {% if form.passport_expiry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.passport_expiry_date.errors }}</p>{% endif %}
                </div>
                <div class="md:col-span-2">
                    <label for="{{ form.additional_information.id_for_label }}" class="block text-sm font-medium text-gray-700">Additional Information</label>
                    <div class="mt-1">{{ form.additional_information }}</div>
                    {% if form.additional_information.errors %}<p class="text-red-500 text-xs mt-1">{{ form.additional_information.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.photo.id_for_label }}" class="block text-sm font-medium text-gray-700">Upload Photo</label>
                    <div class="mt-1">{{ form.photo }}</div>
                    {% if form.photo.errors %}<p class="text-red-500 text-xs mt-1">{{ form.photo.errors }}</p>{% endif %}
                    {% if form.instance.photo %}
                        <p class="text-xs text-gray-500 mt-1">Current: <a href="{{ form.instance.photo.url }}" target="_blank">View Photo</a></p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.cv.id_for_label }}" class="block text-sm font-medium text-gray-700">Upload CV</label>
                    <div class="mt-1">{{ form.cv }}</div>
                    {% if form.cv.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cv.errors }}</p>{% endif %}
                    {% if form.instance.cv %}
                        <p class="text-xs text-gray-500 mt-1">Current: <a href="{{ form.instance.cv.url }}" target="_blank">View CV</a></p>
                    {% endif %}
                </div>
            </div>
            
            <div class="mt-6 flex justify-between">
                <button type="button" @click="activeTab = 3" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Previous
                </button>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                        onclick="return confirm('Are you sure you want to submit?')"> {# Mimic ASP.NET confirmation #}
                    Submit
                </button>
            </div>
        </div>

        {# Display non-field errors #}
        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mt-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}

    </form>
</div>