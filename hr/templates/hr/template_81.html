<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="gatePassTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GP No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Self Employee</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Other Employee (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place (Detail)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for gp in gate_passes %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.gp_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.emp.employee_name|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% for detail in gp.gatepassdetail_set.all %}
                        {{ detail.from_date|date:"d-M-Y" }}<br>
                    {% empty %}
                        N/A
                    {% endfor %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% for detail in gp.gatepassdetail_set.all %}
                        {{ detail.type }}<br>
                    {% empty %}
                        N/A
                    {% endfor %}
                </td>
                <td class="py-3 px-4 text-sm text-gray-900 max-w-xs truncate">{{ gp.gatepassdetail_set.first.reason|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.gatepassdetail_set.first.other_emp.employee_name|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gp.gatepassdetail_set.first.place|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-1"
                        hx-get="{% url 'gatepass_edit' gp.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'gatepass_delete' gp.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 px-4 text-center text-sm text-gray-500">No gate passes found for the selected criteria.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization is handled by the parent template's htmx:afterSwap event listener -->