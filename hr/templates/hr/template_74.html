{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Tour Intimation Print</h2>
        {# Add New button if CRUD is active, otherwise omit #}
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'tourintimation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Tour Intimation
        </button>
    </div>

    {# Search Form Section #}
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <form id="tourIntimationSearchForm"
              hx-get="{% url 'tourintimation_table' %}"
              hx-target="#tourintimation-table-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_drp_group" {# Trigger on submit of form or change of drp_group #}
              class="space-y-4">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ search_form.drp_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.drp_field.label }}
                    </label>
                    {{ search_form.drp_field }}
                </div>
                <div id="search-fields-container" class="md:col-span-2">
                    {# Dynamic fields loaded here via HTMX #}
                    {% include "hr/tourintimation/_search_form_fields.html" with form=search_form_fields selected_field=search_form.drp_field.value %}
                </div>
                <div>
                    <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
            <div id="autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg"></div>
        </form>
    </div>

    {# DataTable Container #}
    <div id="tourintimation-table-container"
         hx-trigger="load, refreshTourIntimationList from:body"
         hx-get="{% url 'tourintimation_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Tour Intimations...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterOnLoad from #modalContent if event.detail.xhr.status != 204 add .is-active to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad from #modalContent if event.detail.xhr.status == 204 remove .is-active from #modal">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('tourIntimation', {
            drpField: 'Select', // Initial value for the dropdown
            init() {
                // Initial state management for search fields visibility if needed by Alpine
                // However, HTMX will largely handle this by swapping _search_form_fields.html
            },
            updateSearchFields: function(event) {
                this.drpField = event.target.value;
                // HTMX is handling the dynamic loading of _search_form_fields.html
                // No need for direct Alpine manipulation of display styles here
            }
        });
    });

    // Helper to select an autocomplete result and update the input field
    function selectEmployee(employeeText) {
        document.getElementById('id_txt_emp_name').value = employeeText;
        document.getElementById('autocomplete-results').innerHTML = ''; // Clear results
    }

    // Initialize DataTables after content is loaded via HTMX
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'tourintimation-table-container') {
            $('#tourintimationTable').DataTable({
                "pageLength": 20, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true, // Allows re-initialization if content is swapped
                "searching": true, // Enable default search box
                "ordering": true,  // Enable column ordering
                "paging": true     // Enable pagination
            });
        }
    });
</script>
{% endblock %}