{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Payroll - New</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'officestaff_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Staff Member
        </button>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{ searchField: '{{ search_form.search_field.value|default:'0' }}', searchTextMrs: '{{ search_form.search_text_mrs.value|default:'' }}', searchTextEmpName: '{{ search_form.search_text_empname.value|default:'' }}' }">
        <form hx-get="{% url 'officestaff_table' %}" hx-target="#officestaffTable-container" hx-indicator="#loading-spinner" hx-swap="innerHTML">
            <div class="flex items-end space-x-4 mb-4">
                <div class="flex-grow">
                    <label for="{{ search_form.search_field.id_for_label }}" class="sr-only">Search By:</label>
                    {{ search_form.search_field|safe }}
                </div>
                
                <div class="flex-grow relative">
                    {{ search_form.search_text_mrs|safe }}
                    {{ search_form.search_text_empname|safe }}
                    
                    <div x-show="searchField === '0' && searchTextEmpName.length > 0" id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto">
                        <!-- Autocomplete suggestions will be loaded here via HTMX -->
                    </div>
                </div>
                
                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
            </div>
            <div class="text-center" id="loading-spinner" style="display: none;">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Searching...</p>
            </div>
        </form>
    </div>

    <div id="officestaffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body"
         hx-get="{% url 'officestaff_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Staff Members...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showMessage: false, message: '', level: '' }"
         @showMessage.window="showMessage = true; message = $event.detail.message; level = $event.detail.level; setTimeout(() => showMessage = false, 3000)">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full">
            <!-- Modal content loaded via HTMX -->
        </div>
        <!-- Toast Notification Area -->
        <template x-if="showMessage">
            <div :class="{ 'bg-green-500': level === 'success', 'bg-red-500': level === 'error', 'bg-blue-500': level === 'info' }"
                 class="fixed bottom-4 right-4 text-white px-4 py-2 rounded-md shadow-lg z-50 transition-transform transform translate-x-0"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-x-full"
                 x-transition:enter-end="opacity-100 translate-x-0"
                 x-transition:leave="transition ease-in duration-300"
                 x-transition:leave-start="opacity-100 translate-x-0"
                 x-transition:leave-end="opacity-0 translate-x-full">
                <span x-text="message"></span>
            </div>
        </template>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('officestaffList', () => ({
            searchField: '{{ search_form.search_field.value|default:'0' }}',
            searchTextMrs: '{{ search_form.search_text_mrs.value|default:'' }}',
            searchTextEmpName: '{{ search_form.search_text_empname.value|default:'' }}',
            selectedEmpId: '', // To hold the EmpId from autocomplete selection

            init() {
                // Initialize text box visibility based on initial searchField value
                this.$watch('searchField', value => {
                    if (value === '0') {
                        this.searchTextMrs = ''; // Clear other search box when switching to emp name
                    } else {
                        this.searchTextEmpName = ''; // Clear emp name search box when switching
                    }
                });

                // HTMX events for autocomplete selection
                this.$el.addEventListener('htmx:afterSwap', (event) => {
                    if (event.detail.target.id === 'autocomplete-results') {
                        // After autocomplete results load, attach click listener to each suggestion
                        event.detail.target.querySelectorAll('div').forEach(item => {
                            item.addEventListener('click', () => {
                                this.searchTextEmpName = item.innerText.trim();
                                this.selectedEmpId = item.dataset.empid; // Store the ID
                                event.detail.target.innerHTML = ''; // Clear suggestions
                            });
                        });
                    }
                });
            }
        }));
    });
</script>
{% endblock %}