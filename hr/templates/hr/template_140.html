{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-5">Offer Letter - Print</h2>
        
        <form class="space-y-4" hx-get="{% url 'hr_offer:offerletter_table' %}" hx-target="#offerletterTable-container" hx-swap="innerHTML" hx-indicator="#loadingIndicator">
            {% csrf_token %}
            <div class="flex items-end space-x-4">
                <div class="flex-grow relative">
                    <label for="{{ search_form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.employee_name.label }}
                    </label>
                    {{ search_form.employee_name }}
                    {# Autocomplete suggestions will be loaded here via HTMX into a separate div #}
                    <div id="employee-suggestions"></div>
                </div>
                
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Search
                </button>
                <button type="button" 
                        class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'hr_offer:offerletter_table' %}"
                        hx-target="#offerletterTable-container"
                        hx-swap="innerHTML"
                        hx-indicator="#loadingIndicator">
                    View All
                </button>
                <a href="{% url 'hr_offer:offerletter_report_export' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out inline-flex items-center"
                   hx-boost="false"> {# Disable HTMX boost to force full page navigation for download #}
                    Export Report
                </a>
            </div>
        </form>
    </div>

    <div id="offerletterTable-container"
         hx-trigger="load, refreshOfferLetterList from:body" {# Load on page load and custom event #}
         hx-get="{% url 'hr_offer:offerletter_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div id="loadingIndicator" class="text-center p-8 hidden" hx-indicator>
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading data...</p>
        </div>
        <div class="text-center p-8">
            <p class="text-gray-500">Loading offer letter data...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<script>
    // Custom event listener for messages, adapting for HTMX triggers
    document.body.addEventListener('showMessage', function(evt) {
        // You would typically have a dedicated message display area (e.g., in base.html)
        // For simplicity, using alert or a temporary div for this example
        let messageText = 'An operation completed successfully.';
        if (evt.detail && evt.detail.messages) {
            messageText = evt.detail.messages.join('<br>');
        } else if (evt.detail && evt.detail.message) {
            messageText = evt.detail.message;
        }
        // Example: Display messages in a div or use a toast library
        console.log("Message from server:", messageText);
        // You can use Alpine.js for a more integrated toast notification system here.
    });

    // Handle Alpine.js for dropdowns/modals as needed (e.g., if dynamically loaded)
    document.addEventListener('alpine:init', () => {
        // Alpine.js components would be defined here if needed for dynamic UI state within rows
    });
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css" rel="stylesheet">
{% endblock %}