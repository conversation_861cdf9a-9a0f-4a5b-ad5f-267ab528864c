<form id="bankloan-edit-form" hx-post="{% url 'bankloan_table' %}" hx-swap="outerHTML" hx-indicator="#loading-indicator">
    {% csrf_token %}
    {{ formset.management_form }}
    
    {# Hidden input to pass search form data back on update, if needed for re-querying #}
    {% for field in search_form %}
        <input type="hidden" name="{{ field.name }}" value="{{ field.value|default_if_none:'' }}">
    {% endfor %}

    <div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
        <table id="bankloanTable" class="min-w-full divide-y divide-gray-200 bg-white">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[4%]">SN</th>
                    <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[4%]">Edit</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[6%]">EmpId</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Emp Name</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[18%]">Bank Name</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Branch</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Amount</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">Installment</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">From Date</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">To Date</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% if formset %}
                    {% for form in formset %}
                        <tr x-data="{ isSelected: {% if form.instance.pk %}false{% else %}true{% endif %}, toggleFields() { /* Logic for enabling fields in Alpine.js */ } }">
                            <td class="py-2 px-4 text-right align-middle text-sm text-gray-900">{{ forloop.counter }}</td>
                            <td class="py-2 px-4 text-center align-middle">
                                {{ form.select_for_edit }}
                            </td>
                            <td class="py-2 px-4 text-center align-middle text-sm text-gray-900">
                                {{ form.instance.empid.empid }}
                                {{ form.id }} {# Hidden ID field for the model instance #}
                            </td>
                            <td class="py-2 px-4 text-left align-middle text-sm text-gray-900">
                                {{ form.instance.get_employee_full_name }}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.bankname }}
                                {% if form.bankname.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bankname.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.branch }}
                                {% if form.branch.errors %}<p class="text-red-500 text-xs mt-1">{{ form.branch.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.amount }}
                                {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.installment }}
                                {% if form.installment.errors %}<p class="text-red-500 text-xs mt-1">{{ form.installment.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.fromdate }}
                                {% if form.fromdate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.fromdate.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.todate }}
                                {% if form.todate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.todate.errors }}</p>{% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="10" class="py-4 px-4 text-center text-sm text-gray-500">
                            No data to display!
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    {% if formset %}
    <div class="mt-6 text-center">
        <button
            type="submit"
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-sm transition duration-150 ease-in-out"
            onclick="return confirm('Are you sure you want to update the selected bank loans?');"
        >
            Update
        </button>
    </div>
    {% endif %}
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables
        $('#bankloanTable').DataTable({
            "pageLength": 15, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "pagingType": "full_numbers", // For full pagination controls like ASP.NET
            "columnDefs": [
                { "orderable": false, "targets": [0, 1] } // SN and Checkbox columns not sortable
            ]
        });

        // Alpine.js initialization for row-level checkbox logic
        // This targets each <tr> element within the formset
        document.querySelectorAll('#bankloanTable tbody tr').forEach(row => {
            Alpine.data('row', () => ({
                isSelected: false,
                init() {
                    // Initialize isSelected based on hidden value if needed
                    // For batch update, initial state is usually 'disabled'
                    // In this case, we rely on x-bind:disabled based on isSelected
                    // Fields are initially disabled, checkbox enables them.
                    this.$nextTick(() => {
                        const checkbox = row.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            checkbox.checked = this.isSelected; // Sync checkbox with Alpine state
                            this.toggleFields(); // Set initial disabled state
                        }
                    });
                },
                toggleFields() {
                    const inputs = row.querySelectorAll('input[type="text"], input[type="number"], input[type="date"]');
                    inputs.forEach(input => {
                        input.disabled = !this.isSelected;
                    });
                }
            }));
        });
    });
</script>