<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="salarydetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DOM</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HD</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sun.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO Days</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">A</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">L</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">H</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO Sun</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coff</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OT Hrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OT Rate</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Loan</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inst.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mob Amt</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Add</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deduct</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for detail in salary_details %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.month_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.days_of_month }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.holidays }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.month_sunday_count }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.working_days }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.present }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.absent }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.late_in }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.half_day }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.sunday }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.coff }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.pl }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.overtime_hrs }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.overtime_rate }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.total_bank_loan }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.installment }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.mobile_exe_amt }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.addition }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.deduction }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'hr_salary:salarydetail_edit' detail.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                        hx-get="{% url 'hr_salary:salarydetail_delete' detail.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="21" class="py-4 px-6 text-center text-gray-500">No salary details found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#salarydetailTable')) {
            $('#salarydetailTable').DataTable().destroy();
        }
        $('#salarydetailTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 20] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>