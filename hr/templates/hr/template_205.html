{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Office Staff Members</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'officestaff_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Staff
        </button>
    </div>

    {% if messages %}
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div id="officestaffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body"
         hx-get="{% url 'officestaff_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Staff Data...</p>
        </div>
    </div>
    
    <!-- Modal for form & confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center hidden z-50"
         _="on hx:afterOnLoad #modalContent remove .hidden from #modal
            on click if event.target.id == 'modal' remove .is-active from me
            on closeModal remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-3xl w-full mx-4 transform transition-all duration-300 scale-95 opacity-0 hidden"
             _="on hx:afterOnLoad set my.style.opacity='1' set my.style.transform='scale(1)'"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('staffModal', () => ({
            isOpen: false,
            openModal() {
                this.isOpen = true;
                document.getElementById('modal').classList.add('is-active');
            },
            closeModal() {
                this.isOpen = false;
                document.getElementById('modal').classList.remove('is-active');
                // Clear modal content if needed
                document.getElementById('modalContent').innerHTML = '';
            }
        }));
    });

    // Handle HTMX triggers for messages and modal closing
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.successful && evt.detail.xhr.status === 204) {
            // Check for custom triggers like 'closeModal'
            const hxTrigger = evt.detail.xhr.getResponseHeader('HX-Trigger');
            if (hxTrigger) {
                try {
                    const triggers = JSON.parse(hxTrigger);
                    if (triggers.closeModal) {
                        document.dispatchEvent(new CustomEvent('closeModal'));
                    }
                } catch (e) {
                    // console.error("Error parsing HX-Trigger:", e);
                }
            }
            // For messages, HTMX automatically handles them via HX-Swap headers,
            // or you can explicitly add them to #messages div on refreshOfficeStaffList
        }
    });

</script>
{% endblock %}