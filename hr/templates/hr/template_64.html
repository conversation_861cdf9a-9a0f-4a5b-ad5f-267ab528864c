<select name="place_of_tour_state" id="id_place_of_tour_state" 
        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        hx-get="{% url 'tourintimation_locations_cities' %}"
        hx-target="#id_place_of_tour_city"
        hx-trigger="change"
        hx-include="this"
        hx-swap="outerHTML">
    <option value="">Select</option>
    {% for state in states %}
        <option value="{{ state.id }}">{{ state.name }}</option>
    {% endfor %}
</select>
{% if form.place_of_tour_state.errors %}
    <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_state.errors }}</p>
{% endif %}