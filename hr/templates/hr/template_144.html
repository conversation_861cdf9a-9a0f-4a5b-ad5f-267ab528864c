<div class="col-span-full" id="salaryBreakdownContent">
    <h3 class="text-lg font-semibold text-gray-800 mb-2">Salary Breakdown</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
        <!-- Monthly Components -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Monthly</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between"><span>Gross Salary:</span> <b>{{ calculated_salary.gross_salary|default:0|floatformat:2 }}</b></div>
                <div class="flex justify-between"><span>Basic (30%):</span> <span>{{ calculated_salary.g_basic|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>DA (20%):</span> <span>{{ calculated_salary.g_da|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>HRA (20%):</span> <span>{{ calculated_salary.g_hra|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Convenience (20%):</span> <span>{{ calculated_salary.g_convenience|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Education (5%):</span> <span>{{ calculated_salary.g_edu|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Medical (5%):</span> <span>{{ calculated_salary.g_wash|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Attend. Bonus - 1:</span> <span>{{ calculated_salary.att_bonus1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Attend. Bonus - 2:</span> <span>{{ calculated_salary.att_bonus2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Gratuity:</span> <span>{{ calculated_salary.gratuity_monthly|default:0|floatformat:2 }}</span></div>
            </div>
        </div>

        <!-- Annual Components -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Annual</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between"><span>Annual Salary:</span> <b>{{ calculated_salary.annual_salary|default:0|floatformat:2 }}</b></div>
                <div class="flex justify-between"><span>Annual Basic:</span> <span>{{ calculated_salary.an_basic|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual DA:</span> <span>{{ calculated_salary.an_da|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual HRA:</span> <span>{{ calculated_salary.an_hra|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Convenience:</span> <span>{{ calculated_salary.an_convenience|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Education:</span> <span>{{ calculated_salary.an_edu|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Medical:</span> <span>{{ calculated_salary.an_wash|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Bonus:</span> <span>{{ calculated_salary.annual_bonus|default:0|floatformat:2 }}</span></div>
            </div>
            <div class="mt-4">
                <h4 class="font-medium text-gray-600 mb-2">Other Annual Benefits</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between"><span>LTA:</span> <span>{{ calculated_salary.lta|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Ex Gratia:</span> <span>{{ calculated_salary.ex_gratia|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Loyalty Benefits:</span> <span>{{ calculated_salary.loyalty|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Vehicle Allowance:</span> <span>{{ calculated_salary.vehicle_allowance|default:0|floatformat:2 }}</span></div>
                    <div class="flex justify-between"><span>Paid Leaves:</span> <span>{{ calculated_salary.paid_leaves|default:0|floatformat:2 }}</span></div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 mt-6">
        <!-- PF & P.Tax -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Deductions</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between"><span>PF-Employee:</span> <span>{{ calculated_salary.pf_employee|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>PF-Company:</span> <span>{{ calculated_salary.pf_company|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>P. Tax:</span> <span>{{ calculated_salary.ptax|default:0|floatformat:2 }}</span></div>
            </div>
        </div>

        <!-- Take Home & CTC -->
        <div>
            <h4 class="font-medium text-gray-600 mb-2">Summary</h4>
            <div class="space-y-2 text-sm font-bold">
                <div class="flex justify-between"><span>Take Home:</span> <span>{{ calculated_salary.take_home|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Take Home (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.take_home_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Take Home (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.take_home_att2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Take Home:</span> <span>{{ calculated_salary.take_home_annual|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Take Home (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.take_home_ann_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual Take Home (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.take_home_ann_att2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>CTC:</span> <span>{{ calculated_salary.ctc|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>CTC (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.ctc_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>CTC (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.ctc_att2|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual CTC:</span> <span>{{ calculated_salary.ctc_annual|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual CTC (Attd. Bonus - 1)*:</span> <span>{{ calculated_salary.ctc_ann_att1|default:0|floatformat:2 }}</span></div>
                <div class="flex justify-between"><span>Annual CTC (Attd. Bonus - 2)*:</span> <span>{{ calculated_salary.ctc_ann_att2|default:0|floatformat:2 }}</span></div>
            </div>
        </div>
    </div>

    <!-- Hidden fields for the calculation to pick up dynamic changes -->
    <input type="hidden" name="att_bonus_per1" value="{{ form_data.att_bonus_per1|default:0 }}">
    <input type="hidden" name="att_bonus_per2" value="{{ form_data.att_bonus_per2|default:0 }}">
    <input type="hidden" name="pf_employee_perc" value="{{ form_data.pf_employee_perc|default:0 }}">
    <input type="hidden" name="pf_company_perc" value="{{ form_data.pf_company_perc|default:0 }}">
    <input type="hidden" name="bonus" value="{{ form_data.bonus|default:0 }}">
    <input type="hidden" name="type_of" value="{{ form_data.type_of|default:'0' }}">
    <input type="hidden" name="staff_type" value="{{ form_data.staff_type|default:'' }}">
</div>