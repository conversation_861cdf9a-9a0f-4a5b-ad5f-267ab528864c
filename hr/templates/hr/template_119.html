<table id="bankLoanTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Installment</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-100">
        {% for loan in bank_loans %}
        <tr>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.employee.emp_id }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.get_employee_full_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.bank_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.branch }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.amount|floatformat:2 }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.installment|floatformat:2 }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.get_formatted_from_date }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ loan.get_formatted_to_date }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2 text-xs"
                    hx-get="{% url 'bankloan_edit' loan.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-get="{% url 'bankloan_delete' loan.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-gray-500">No bank loan records found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        $('#bankLoanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true // Make DataTables responsive
        });
    });
</script>