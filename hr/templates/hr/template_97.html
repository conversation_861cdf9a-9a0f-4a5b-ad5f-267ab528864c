{# tour_intimation/_employee_autocomplete_results.html #}
{% if employees %}
<ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
    {% for employee in employees %}
    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
        hx-on:click="document.getElementById('id_employee_full_name').value = '{{ employee.__str__ }}'; this.closest('ul').remove();">
        {{ employee.__str__ }}
    </li>
    {% endfor %}
</ul>
{% endif %}