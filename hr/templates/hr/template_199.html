<div class="p-4">
    <table id="offermasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staff Type</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for offer in offermasters %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.offer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.employee_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.get_staff_type_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_edit' offer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Select (Edit)
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_delete' offer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-3 px-4 text-center text-gray-500 text-lg">No pending offers to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once and on content load
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#offermasterTable')) {
            $('#offermasterTable').DataTable().destroy(); // Destroy previous instance if it exists
        }
        $('#offermasterTable').DataTable({
            "pageLength": 24, // Matches original ASP.NET PageSize
            "lengthMenu": [[10, 24, 50, -1], [10, 24, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>