{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    employeeType: '{{ form.employee_selection_type.initial }}',
    woGroupType: '{{ form.wo_group_selection_type.initial }}',
    selectedEmployeeId: '{{ form.initial.employee_emp_id|default:"" }}',
    selectedEmployeeName: '{{ form.initial.other_employee_name|default:"" }}',
    clearSelectedEmployee() {
        this.selectedEmployeeId = '';
        this.selectedEmployeeName = '';
        document.getElementById('id_employee_emp_id').value = '';
    },
    selectEmployee(empId, empName) {
        this.selectedEmployeeId = empId;
        this.selectedEmployeeName = empName;
        document.getElementById('id_other_employee_name').value = empName; // Set visible text
        document.getElementById('id_employee_emp_id').value = empId;     // Set hidden ID
        document.getElementById('employee-autocomplete-results').innerHTML = ''; // Clear results
    }
}" x-init="
    $watch('employeeType', value => {
        if (value === '0') { // Self selected
            document.getElementById('id_other_employee_name').value = '';
            document.getElementById('id_employee_emp_id').value = '';
        }
    });
    // Initialize wo_no and business_group based on initial woGroupType
    $nextTick(() => {
        if (woGroupType === '0') { // WO No
            document.getElementById('id_business_group').value = '';
        } else { // BG Group
            document.getElementById('id_wo_no').value = '';
        }
    });
">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Create Tour Intimation</h2>

    <form method="post" class="space-y-6" novalidate>
        {% csrf_token %}
        
        <!-- Main Tour Details Section -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Tour Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                
                <!-- Employee Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Employee Selection</label>
                    <div class="flex items-center space-x-4">
                        {% for radio in form.employee_selection_type %}
                            <label class="inline-flex items-center">
                                <input type="radio" x-model="employeeType" name="{{ radio.name }}" value="{{ radio.choice_value }}" {% if radio.is_checked %}checked{% endif %} class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if form.employee_selection_type.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.employee_selection_type.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Employee Name for Others -->
                <div x-show="employeeType === '1'">
                    <label for="{{ form.other_employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.other_employee_name.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.other_employee_name }}
                    <input type="hidden" name="{{ form.employee_emp_id.name }}" id="{{ form.employee_emp_id.id_for_label }}" x-model="selectedEmployeeId">
                    <div id="employee-autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto"></div>
                    {% if form.other_employee_name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.other_employee_name.errors }}</p>
                    {% endif %}
                </div>

                <!-- Project Name -->
                <div>
                    <label for="{{ form.project_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.project_name.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.project_name }}
                    {% if form.project_name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.project_name.errors }}</p>
                    {% endif %}
                </div>

                <!-- WO No/BG Group Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">WO No / BG Group</label>
                    <div class="flex items-center space-x-4">
                        {% for radio in form.wo_group_selection_type %}
                            <label class="inline-flex items-center">
                                <input type="radio" x-model="woGroupType" name="{{ radio.name }}" value="{{ radio.choice_value }}" {% if radio.is_checked %}checked{% endif %} class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if form.wo_group_selection_type.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.wo_group_selection_type.errors }}</p>
                    {% endif %}
                </div>

                <!-- WO No -->
                <div x-show="woGroupType === '0'">
                    <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.wo_no.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.wo_no }}
                    {% if form.wo_no.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Business Group -->
                <div x-show="woGroupType === '1'">
                    <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.business_group.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.business_group }}
                    {% if form.business_group.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.business_group.errors }}</p>
                    {% endif %}
                </div>

                <!-- Place of Tour -->
                <div>
                    <label for="{{ form.place_of_tour_country.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Country <span class="text-red-500">*</span>
                    </label>
                    {{ form.place_of_tour_country }}
                    {% if form.place_of_tour_country.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_country.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.place_of_tour_state.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        State <span class="text-red-500">*</span>
                    </label>
                    {{ form.place_of_tour_state }}
                    {% if form.place_of_tour_state.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_state.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.place_of_tour_city.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        City <span class="text-red-500">*</span>
                    </label>
                    {{ form.place_of_tour_city }}
                    {% if form.place_of_tour_city.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.place_of_tour_city.errors }}</p>
                    {% endif %}
                </div>

                <!-- Tour Start Date/Time -->
                <div>
                    <label for="{{ form.tour_start_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour Start Date <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_start_date }}
                    {% if form.tour_start_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_start_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.tour_start_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour Start Time <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_start_time }}
                    {% if form.tour_start_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_start_time.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Tour End Date/Time -->
                <div>
                    <label for="{{ form.tour_end_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour End Date <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_end_date }}
                    {% if form.tour_end_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_end_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.tour_end_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Tour End Time <span class="text-red-500">*</span>
                    </label>
                    {{ form.tour_end_time }}
                    {% if form.tour_end_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tour_end_time.errors }}</p>
                    {% endif %}
                </div>

                <!-- No. of Days -->
                <div>
                    <label for="{{ form.no_of_days.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.no_of_days.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.no_of_days }}
                    {% if form.no_of_days.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.no_of_days.errors }}</p>
                    {% endif %}
                </div>

                <!-- Name & Address of Accommodation Service Provider -->
                <div class="col-span-1 md:col-span-2">
                    <label for="{{ form.name_address_ser_provider.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.name_address_ser_provider.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.name_address_ser_provider }}
                    {% if form.name_address_ser_provider.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.name_address_ser_provider.errors }}</p>
                    {% endif %}
                </div>

                <!-- Contact Person -->
                <div>
                    <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.contact_person.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.contact_person }}
                    {% if form.contact_person.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>
                    {% endif %}
                </div>
                
                <!-- Contact No -->
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.contact_no.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.contact_no }}
                    {% if form.contact_no.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>
                    {% endif %}
                </div>

                <!-- Email -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.email.label }}
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.email.errors }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Tabs Section (Advance Details & Advance Trans. To) -->
        <div x-data="{ activeTab: 'advanceDetails' }" class="bg-white shadow-md rounded-lg p-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button type="button" @click="activeTab = 'advanceDetails'" 
                            :class="activeTab === 'advanceDetails' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Details
                    </button>
                    <button type="button" @click="activeTab = 'advanceTransTo'" 
                            :class="activeTab === 'advanceTransTo' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Trans. To
                    </button>
                </nav>
            </div>

            <!-- Advance Details Tab Content -->
            <div x-show="activeTab === 'advanceDetails'" class="pt-6">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Advance Details</h3>
                <div class="overflow-x-auto rounded-md border border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{ expense_formset.management_form }}
                            {% for form in expense_formset %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ form.expense_type_id }}
                                    {{ form.terms }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ form.amount }}
                                    {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ form.remarks }}
                                    {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                            {% if expense_formset.non_field_errors %}
                                <tr><td colspan="4" class="px-6 py-4 text-red-500 text-sm">{{ expense_formset.non_field_errors }}</td></tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Advance Trans. To Tab Content -->
            <div x-show="activeTab === 'advanceTransTo'" class="pt-6">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Advance Trans. To</h3>
                
                <div id="advance-temp-grid-container"
                     hx-trigger="load, refreshAdvanceTempGrid from:body"
                     hx-get="{% url 'tourintimation_advance_temp_list' %}"
                     hx-swap="innerHTML">
                    <!-- Temporary Advance Grid will be loaded here via HTMX -->
                    <div class="text-center py-4">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-500">Loading Advance Transfers...</p>
                    </div>
                </div>

                <!-- Add New Advance Temp Form (initially hidden, shown in modal) -->
                <div id="advance-temp-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
                     _="on click if event.target.id == 'advance-temp-modal' remove .hidden from me">
                    <div id="advance-temp-modal-content" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full">
                        <!-- Content loaded by HTMX for Add/Edit/Delete -->
                    </div>
                </div>

            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center mt-6">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-300 ease-in-out">
                Submit Tour Intimation
            </button>
        </div>
        
        <!-- Form errors for non-field errors or errors from previous submissions -->
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

    </form>
</div>

<!-- Global message display (for Django messages and HTMX alerts) -->
<div id="messages-container" class="fixed top-4 right-4 z-50">
    {% for message in messages %}
        <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded relative mb-2" role="alert"
             x-data="{ show: true }" x-init="setTimeout(() => show = false, 5000)" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <strong class="font-bold">{{ message.tags|capfirst }}!</strong>
            <span class="block sm:inline">{{ message }}</span>
        </div>
    {% endfor %}
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is initialized globally via base.html
    // Specific functions for autocomplete
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'employee-autocomplete-results') {
            // Re-bind click event for dynamically loaded autocomplete results
            event.detail.target.querySelectorAll('.autocomplete-item').forEach(item => {
                item.addEventListener('click', function() {
                    const empId = this.dataset.empId;
                    const empName = this.dataset.empName;
                    Alpine.$data(document.getElementById('id_other_employee_name')).selectEmployee(empId, empName);
                });
            });
        }
        if (event.detail.target.id === 'employee-autocomplete-results-advance-temp') {
            event.detail.target.querySelectorAll('.autocomplete-item').forEach(item => {
                item.addEventListener('click', function() {
                    const empId = this.dataset.empId;
                    const empName = this.dataset.empName;
                    // Assuming advance-temp-form has a similar Alpine context or target specific elements
                    document.getElementById('id_employee_full_name').value = empName;
                    document.getElementById('id_employee_emp_id').value = empId;
                    document.getElementById('employee-autocomplete-results-advance-temp').innerHTML = ''; // Clear results
                });
            });
        }
    });

    // Custom event listener for showing messages globally via HTMX
    document.body.addEventListener('showMessage', function(event) {
        // HTMX will automatically add messages to the global Django message storage if returned via headers
        // This handler ensures immediate display if needed, but Django messages usually handle this.
    });

</script>
{% endblock %}