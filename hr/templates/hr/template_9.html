{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Employee Multiple Reports</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8" x-data="{ searchField: '{{ filter_form.search_field.value|default:'Select' }}' }" x-init="
        updateVisibility = () => {
            if (searchField === '1') { // EmployeeName
                $refs.empNameSearch.style.display = 'block';
                $refs.textSearch.style.display = 'none';
                $refs.textSearch.value = ''; // Clear other search box
            } else {
                $refs.empNameSearch.style.display = 'none';
                $refs.textSearch.style.display = 'block';
                $refs.empNameSearch.value = ''; // Clear other search box
            }
        }; 
        updateVisibility(); // Initial call
    ">
        <form hx-get="{% url 'hr_reports:office_staff_table' %}" hx-target="#office_staff_table_container" hx-swap="innerHTML" hx-indicator="#table-loading">
            {% csrf_token %}
            <table class="w-full">
                <tr>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm">
                        <label for="{{ filter_form.criteria.id_for_label }}" class="sr-only">Criteria</label>
                        {{ filter_form.criteria }}
                        <span id="sub-criteria-loading" class="htmx-indicator inline-block ml-2 text-blue-500">Loading...</span>
                    </td>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm">
                        <label for="{{ filter_form.sub_criteria.id_for_label }}" class="sr-only">Sub-Criteria</label>
                        <div id="id_sub_criteria_container">
                            {{ filter_form.sub_criteria }}
                        </div>
                    </td>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm">
                        <label for="{{ filter_form.search_field.id_for_label }}" class="sr-only">Search By</label>
                        {{ filter_form.search_field }}
                    </td>
                    <td class="fontcsswhite py-2 px-2 text-gray-700 text-sm relative">
                        <label for="{{ filter_form.emp_name_search.id_for_label }}" class="sr-only">Employee Name</label>
                        <div x-ref="empNameSearch" style="display: none;">
                            {{ filter_form.emp_name_search }}
                            <div id="autocomplete-results"></div>
                            <span id="autocomplete-loading" class="htmx-indicator absolute top-0 right-0 p-2">
                                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                            </span>
                        </div>
                        <label for="{{ filter_form.search_text.id_for_label }}" class="sr-only">Search Value</label>
                        <div x-ref="textSearch" style="display: none;">
                            {{ filter_form.search_text }}
                        </div>
                    </td>
                    <td class="py-2 px-2">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out">
                            Search
                        </button>
                    </td>
                </tr>
            </table>
        </form>
    </div>

    <div id="office_staff_table_container"
         hx-trigger="load, reloadTable from:body"
         hx-get="{% url 'hr_reports:office_staff_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div id="table-loading" class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading employee data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DataTables initialization, triggered after HTMX loads the table content
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'office_staff_table_container') {
            $('#officeStaffTable').DataTable({
                "pageLength": 20, // Match paginate_by in Django view
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "pagingType": "full_numbers", // More navigation options
                "responsive": true, // Make table responsive
                "language": {
                    "emptyTable": "No data to display !",
                    "zeroRecords": "No matching records found"
                }
            });
        }
    });

    // Handle autocomplete selection to also trigger a search form submission
    document.addEventListener('click', function(event) {
        if (event.target.closest('.autocomplete-results div')) {
            const empNameInput = document.getElementById('id_emp_name_search');
            if (empNameInput) {
                // Ensure the form gets updated value for search
                empNameInput.value = event.target.textContent.trim();
                // Optionally trigger a search immediately after selection
                // htmx.trigger(empNameInput.form, 'submit'); // Triggers the form submission
            }
        }
    });
</script>
{% endblock %}