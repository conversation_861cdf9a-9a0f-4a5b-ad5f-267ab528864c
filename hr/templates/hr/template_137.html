<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="offerReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer ID</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Salary (Monthly)</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for offer in offers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.offer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.title }} {{ offer.employee_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">₹{{ offer.salary|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.sys_date|date:"d M, Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'hr:offer_letter_print' offer.offer_id %}" target="_blank"
                       class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M11 3a1 1 0 100 2h6a1 1 0 100-2h-6zM11 7a1 1 0 100 2h6a1 1 0 100-2h-6zM11 11a1 1 0 100 2h6a1 1 0 100-2h-6zM13 15a1 1 0 100 2h4a1 1 0 100-2h-4zM7 3h1a1 1 0 011 1v12a1 1 0 01-1 1H7a1 1 0 01-1-1V4a1 1 0 011-1z" clip-rule="evenodd" fill-rule="evenodd"></path>
                        </svg>
                        View Offer
                    </a>
                    {% if offer.increment_count > 0 %}
                    <div x-data="{ open: false }" class="relative inline-block text-left ml-2">
                        <button type="button" @click="open = !open"
                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                            aria-expanded="true" aria-haspopup="true">
                            View Increments ({{ offer.increment_count }})
                            <svg class="-mr-1 ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false"
                            class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                            role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
                            <div class="py-1" role="none">
                                {% for inc in offer.incrementmaster_set.all %}
                                <a href="{% url 'hr:increment_letter_print' offer.offer_id inc.increment_level %}" target="_blank"
                                   class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem" tabindex="-1">
                                    Increment {{ inc.increment_level }} ({{ inc.increment_for_the_year }})
                                </a>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-3 px-4 text-center text-gray-500">No offer letters found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#offerReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>