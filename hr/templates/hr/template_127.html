{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold fontcsswhite" style="background:url(/static/images/hdbg.JPG); padding: 5px;">
            <label id="lblOfferIncrement">{{ offer_increment_label }}</label>&nbsp;&nbsp;&nbsp;&nbsp;
            Offer Id: <label id="lblOfferId">{{ offer_id_label }}</label>
        </h2>
    </div>

    {% if messages %}
    <div x-data="{ show: true }" x-init="setTimeout(() => show = false, 5000)" x-show="show" 
         class="mb-4 p-3 rounded-md shadow-sm bg-green-100 border border-green-400 text-green-700">
        {% for message in messages %}
            <p>{{ message }}</p>
        {% endfor %}
    </div>
    {% endif %}

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <form hx-post="{% url 'offerletter_edit' offer_letter.pk %}" hx-swap="none" hx-indicator="#form-indicator">
            {% csrf_token %}
            <input type="hidden" name="action" id="form-action" value="update">
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Main Offer Details -->
                <div>
                    <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">Designation</label>
                    {{ form.designation }}
                    {% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                    <div class="flex items-center space-x-2">
                        {{ form.title }}
                        {{ form.employee_name }}
                    </div>
                    {% if form.title.errors %}<p class="text-red-500 text-xs mt-1">{{ form.title.errors }}</p>{% endif %}
                    {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                </div>
                <div x-data="{ showHeaderFooter: {{ not is_increment_view and offer_letter.increment == 0 | lower }} }">
                    <label x-show="showHeaderFooter" for="{{ form.header_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Header</label>
                    <textarea x-show="showHeaderFooter" name="{{ form.header_text.name }}" id="{{ form.header_text.id_for_label }}" class="box3 h-24" placeholder="Header Text">{{ form.header_text.value|default:"" }}</textarea>
                    {% if form.header_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.header_text.errors }}</p>{% endif %}

                    <label x-show="showHeaderFooter" for="{{ form.footer_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mt-4">Footer</label>
                    <textarea x-show="showHeaderFooter" name="{{ form.footer_text.name }}" id="{{ form.footer_text.id_for_label }}" class="box3 h-24" placeholder="Footer Text">{{ form.footer_text.value|default:"" }}</textarea>
                    {% if form.footer_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.footer_text.errors }}</p>{% endif %}

                    <div x-data="{ showIncrementFields: {{ is_increment_view or offer_letter.increment > 0 | lower }} }">
                        <label x-show="showIncrementFields" for="{{ form.increment_for_the_year.id_for_label }}" class="block text-sm font-medium text-gray-700 mt-4 font-bold">Effect From For The Year</label>
                        <input x-show="showIncrementFields" type="text" name="{{ form.increment_for_the_year.name }}" id="{{ form.increment_for_the_year.id_for_label }}" class="box3 w-24" value="{{ form.increment_for_the_year.value|default:"" }}" placeholder="Ex- 2014 - 2015">
                        {% if form.increment_for_the_year.errors %}<p class="text-red-500 text-xs mt-1">{{ form.increment_for_the_year.errors }}</p>{% endif %}

                        <label x-show="showIncrementFields" for="{{ form.effect_from.id_for_label }}" class="block text-sm font-medium text-gray-700 mt-4 font-bold">Effect From</label>
                        <input x-show="showIncrementFields" type="date" name="{{ form.effect_from.name }}" id="{{ form.effect_from.id_for_label }}" class="box3 w-24" value="{{ form.effect_from.value|default:""|date:'Y-m-d' }}">
                        {% if form.effect_from.errors %}<p class="text-red-500 text-xs mt-1">{{ form.effect_from.errors }}</p>{% endif %}
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label for="{{ form.duty_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">Duty Hours</label>
                    {{ form.duty_hrs }}
                    {% if form.duty_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.duty_hrs.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Nos.</label>
                    {{ form.contact_no }}
                    {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.ot_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Hours</label>
                    {{ form.ot_hrs }}
                    {% if form.ot_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ot_hrs.errors }}</p>{% endif %}
                </div>
                <div class="row-span-2">
                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                    {{ form.address }}
                    {% if form.address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.address.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.overtime_applicable.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Applicable</label>
                    {{ form.overtime_applicable }}
                    {% if form.overtime_applicable.errors %}<p class="text-red-500 text-xs mt-1">{{ form.overtime_applicable.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.staff_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type of Employee</label>
                    {{ form.type_of }}
                    {{ form.staff_type }}
                    {% if form.type_of.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_of.errors }}</p>{% endif %}
                    {% if form.staff_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.staff_type.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.email_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Email Id</label>
                    {{ form.email_id }}
                    {% if form.email_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email_id.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.interviewed_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Interviewed By</label>
                    {{ form.interviewed_by }}
                    {% if form.interviewed_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.interviewed_by.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.authorized_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Authorized By</label>
                    {{ form.authorized_by }}
                    {% if form.authorized_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.authorized_by.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.reference_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Reference By</label>
                    {{ form.reference_by }}
                    {% if form.reference_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reference_by.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.gross_salary.id_for_label }}" class="block text-sm font-medium text-gray-700">Gross Salary</label>
                    {{ form.gross_salary }}
                    {% if form.gross_salary.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gross_salary.errors }}</p>{% endif %}
                </div>
            </div>

            <div class="mb-6 border p-4 rounded-md">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Salary Components</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
                     id="salary-calculation-results"
                     hx-trigger="refreshSalaryCalculation from:body"
                     hx-get="{% url 'calculate_salary_partial' offer_letter.pk %}"
                     hx-swap="innerHTML">
                    <!-- Initial rendering of salary components -->
                    {% include 'hr_offer/_salary_calculation_results.html' with salary_components=salary_components offer_letter=offer_letter %}
                </div>
            </div>

            <div class="mb-6">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>

            <div class="flex justify-end space-x-4 mt-6">
                <button type="button" class="redbox"
                    onclick="document.getElementById('form-action').value='calculate';
                             htmx.trigger(this.form, 'submit');"
                    hx-post="{% url 'calculate_salary_partial' offer_letter.pk %}"
                    hx-target="#salary-calculation-results"
                    hx-swap="innerHTML">
                    Calculate
                </button>
                <button type="submit" class="redbox" 
                        onclick="document.getElementById('form-action').value='update';">
                    Update
                </button>
                {% if is_increment_view %}
                <button type="submit" class="redbox"
                        onclick="document.getElementById('form-action').value='increment';">
                    Increment
                </button>
                {% endif %}
                <a href="{% url 'offerletter_list' %}" class="redbox">Cancel</a>
            </div>
            <div id="form-indicator" class="htmx-indicator">Loading...</div>
        </form>
    </div>

    <div class="mt-8 bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Offer Accessories</h3>
        <div id="offer-accessories-table-container"
             hx-trigger="load, refreshAccessoryTable from:body"
             hx-get="{% url 'offeraccessory_table_partial' offer_letter.pk %}"
             hx-swap="innerHTML">
            <!-- Accessories table will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Accessories...</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal for form -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize DataTables if a table is swapped in
        if (evt.detail.target.id === 'offer-accessories-table-container') {
            $('#offerAccessoryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Client-side redirect after successful main form submission
    document.body.addEventListener('offerLetterUpdated', function(evt) {
        // This means the main form submission was successful
        // ASP.NET used Response.Redirect, so we simulate a full page navigation
        // You can make this smarter, e.g., only redirect if not an HTMX form, or use HX-Redirect header
        window.location.href = "{% url 'offerletter_list' %}?msg=Employee data updated."; // Example redirect
    });
</script>
{% endblock %}