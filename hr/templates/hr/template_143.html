{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ 
    grossSalary: parseFloat('{{ form.salary.value|default:0 }}'),
    typeOf: '{{ form.type_of.value|default:"0" }}',
    staffType: '{{ form.staff_type.value|default:"" }}',
    attBonusPer1: parseFloat('{{ form.att_bonus_per1.value|default:0 }}'),
    attBonusPer2: parseFloat('{{ form.att_bonus_per2.value|default:0 }}'),
    pfEmployeePerc: parseFloat('{{ form.pf_employee_perc.value|default:0 }}'),
    pfCompanyPerc: parseFloat('{{ form.pf_company_perc.value|default:0 }}'),
    bonus: parseFloat('{{ form.bonus.value|default:0 }}'),
    isCasuals: '{{ form.staff_type.value|default:"" }}' === '{{ staff_type_casuals_id }}' ? true : false,
    init() {
        this.$watch('staffType', value => {
            this.isCasuals = value === '{{ staff_type_casuals_id }}';
            if (this.isCasuals) {
                this.attBonusPer1 = 0;
                this.attBonusPer2 = 0;
                this.pfEmployeePerc = 0;
                this.pfCompanyPerc = 0;
                this.bonus = 0;
            } else {
                // Re-initialize based on typeOf, or fetch from PF_Slab
                this.updateAttBonusAndPF();
            }
            this.$dispatch('updateSalary'); // Trigger salary recalculation
        });
        this.$watch('typeOf', value => {
            this.updateAttBonusAndPF();
            this.$dispatch('updateSalary');
        });

        // Initialize PF values based on active slab on load if not set
        if (!this.pfEmployeePerc || !this.pfCompanyPerc) {
            fetch('/hr-modules/get-pf-slab/') // HTMX endpoint for PF slab
                .then(response => response.json())
                .then(data => {
                    if (data.pf_employee && data.pf_company) {
                        this.pfEmployeePerc = data.pf_employee;
                        this.pfCompanyPerc = data.pf_company;
                        this.$dispatch('updateSalary');
                    }
                });
        }
    },
    updateAttBonusAndPF() {
        if (this.typeOf === '1') { // SAPL
            this.attBonusPer1 = 10;
            this.attBonusPer2 = 20;
        } else if (this.typeOf === '2') { // NEHA
            this.attBonusPer1 = 5;
            this.attBonusPer2 = 15;
        }
    }
}" x-init="init()">
    <h2 class="text-2xl font-bold mb-6">Offer Letter - New</h2>

    <form hx-post="." hx-swap="none" hx-trigger="refreshOfferLetterForm from:body"
          _="on hx-trigger.refreshOfferLetterForm from body set window.location.href = '{{ request.path }}'">
        {% csrf_token %}
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Basic Employee Details -->
                <div class="col-span-full bg-gray-50 p-4 rounded-md mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Basic Details</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">Designation</label>
                            {{ form.designation }}
                            {% if form.designation.errors %}<p class="text-red-500 text-xs mt-1">{{ form.designation.errors }}</p>{% endif %}
                        </div>
                        <div class="flex items-center">
                            <div class="w-1/3 pr-2">
                                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700">Title</label>
                                {{ form.title }}
                            </div>
                            <div class="w-2/3 pl-2">
                                <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                                {{ form.employee_name }}
                                {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div>
                            <label for="{{ form.duty_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">Duty Hours</label>
                            {{ form.duty_hrs }}
                            {% if form.duty_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.duty_hrs.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Nos.</label>
                            {{ form.contact_no }}
                            {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.ot_hrs.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Hours</label>
                            {{ form.ot_hrs }}
                            {% if form.ot_hrs.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ot_hrs.errors }}</p>{% endif %}
                        </div>
                        <div class="row-span-2">
                            <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                            {{ form.address }}
                            {% if form.address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.address.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.overtime_applicable.id_for_label }}" class="block text-sm font-medium text-gray-700">OT Applicable</label>
                            {{ form.overtime_applicable }}
                            {% if form.overtime_applicable.errors %}<p class="text-red-500 text-xs mt-1">{{ form.overtime_applicable.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.type_of.id_for_label }}" class="block text-sm font-medium text-gray-700">Type of Employee</label>
                            {{ form.type_of }}
                            {% if form.type_of.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_of.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.staff_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Staff Type</label>
                            {{ form.staff_type }}
                            {% if form.staff_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.staff_type.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.email_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Email Id</label>
                            {{ form.email_id }}
                            {% if form.email_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email_id.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.interviewed_by_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Interviewed By</label>
                            {{ form.interviewed_by_name }}
                            <div id="interviewed-by-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-auto max-h-48 overflow-y-auto"></div>
                            {% if form.interviewed_by_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.interviewed_by_name.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.authorized_by_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Authorized By</label>
                            {{ form.authorized_by_name }}
                            <div id="authorized-by-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-auto max-h-48 overflow-y-auto"></div>
                            {% if form.authorized_by_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.authorized_by_name.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.reference_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Reference By</label>
                            {{ form.reference_by }}
                            {% if form.reference_by.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reference_by.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.salary.id_for_label }}" class="block text-sm font-medium text-gray-700">Gross Salary</label>
                            {{ form.salary }}
                            {% if form.salary.errors %}<p class="text-red-500 text-xs mt-1">{{ form.salary.errors }}</p>{% endif %}
                        </div>
                    </div>
                </div>

                <!-- Header & Footer Text (Right Column in ASP.NET) -->
                <div class="col-span-full lg:col-span-1 lg:row-span-3 bg-gray-50 p-4 rounded-md">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Offer Letter Content</h3>
                    <div class="mb-4">
                        <label for="{{ form.header_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Header Text</label>
                        {{ form.header_text }}
                        {% if form.header_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.header_text.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.footer_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Footer Text</label>
                        {{ form.footer_text }}
                        {% if form.footer_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.footer_text.errors }}</p>{% endif %}
                    </div>
                </div>
            </div>

            <!-- Salary Breakdown Section (HTMX Target) -->
            <div id="salaryBreakdown" 
                 hx-post="{% url 'hrmodules:calculate_salary_partial' %}" 
                 hx-trigger="updateSalary from:body, change from:#id_salary, change from:#id_ex_gratia, change from:#id_vehicle_allowance, change from:#id_lta, change from:#id_loyalty, change from:#id_paid_leaves, change from:#id_pf_employee_perc, change from:#id_pf_company_perc, change from:#id_att_bonus_per1, change from:#id_att_bonus_per2, change from:#id_bonus, change from:#accessoryFormset"
                 hx-swap="outerHTML" 
                 class="mt-6 bg-gray-50 p-4 rounded-md">
                {% include 'hrmodules/offerletter/_salary_breakdown.html' with calculated_salary=calculated_salary form_data=form.data %}
            </div>

            <!-- Accessories Section (HTMX Target) -->
            <div id="accessorySection" 
                 hx-post="{% url 'hrmodules:offerletter_accessories_table_partial' %}" 
                 hx-trigger="refreshAccessoryTable from:body"
                 hx-swap="innerHTML"
                 class="mt-6 bg-gray-50 p-4 rounded-md">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Accessories</h3>
                {% include 'hrmodules/offerletter/_accessory_table.html' with accessory_formset=accessory_formset includes_in_options=includes_in_options %}
            </div>

            <div class="mt-6">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>

            <div class="mt-8 flex justify-end space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Submit Offer
                </button>
            </div>
        </div>
    </form>
</div>

<script>
    // Live update of Staff Type "Casuals" logic for bonus and PF fields
    document.addEventListener('DOMContentLoaded', function() {
        const staffTypeSelect = document.getElementById('{{ form.staff_type.id_for_label }}');
        const bonusField = document.getElementById('{{ form.bonus.id_for_label }}');
        const attB1Field = document.getElementById('{{ form.att_bonus_per1.id_for_label }}');
        const attB2Field = document.getElementById('{{ form.att_bonus_per2.id_for_label }}');
        const pfEmpField = document.getElementById('{{ form.pf_employee_perc.id_for_label }}');
        const pfCompField = document.getElementById('{{ form.pf_company_perc.id_for_label }}');
        
        function applyCasualsLogic() {
            // Assuming 'Casuals' has ID 5 based on common HR systems or DB inspection
            // This should be dynamically fetched if possible, or hardcoded for exact match.
            // For now, let's assume staff_type_casuals_id is passed from view.
            const isCasuals = staffTypeSelect.value === '{{ staff_type_casuals_id }}'; 

            [bonusField, attB1Field, attB2Field, pfEmpField, pfCompField].forEach(field => {
                if (field) {
                    field.disabled = isCasuals;
                    if (isCasuals) {
                        field.value = '0';
                    }
                }
            });
        }
        
        staffTypeSelect.addEventListener('change', applyCasualsLogic);
        applyCasualsLogic(); // Apply on initial load
    });

    // Handle staff autocomplete suggestions
    htmx.on('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.responseURL.includes('staff-autocomplete')) {
            const targetId = evt.detail.target.id;
            const suggestionsContainerId = targetId === 'Txtinterviewedby' ? 'interviewed-by-suggestions' : 'authorized-by-suggestions';
            const suggestionsDiv = document.getElementById(suggestionsContainerId);
            suggestionsDiv.innerHTML = ''; // Clear previous suggestions
            const suggestions = evt.detail.xhr.responseText.split('\n').filter(s => s.trim() !== '');
            
            if (suggestions.length > 0) {
                suggestions.forEach(suggestion => {
                    const item = document.createElement('div');
                    item.className = 'px-3 py-2 cursor-pointer hover:bg-gray-200';
                    item.textContent = suggestion;
                    item.onclick = function() {
                        document.getElementById(targetId).value = suggestion;
                        suggestionsDiv.innerHTML = ''; // Clear suggestions on selection
                    };
                    suggestionsDiv.appendChild(item);
                });
            }
        }
    });

    // Clear suggestions if input is blurred or clicked outside
    document.addEventListener('click', function(event) {
        const interviewInput = document.getElementById('Txtinterviewedby');
        const authInput = document.getElementById('TxtAuthorizedby');
        const interviewSuggestions = document.getElementById('interviewed-by-suggestions');
        const authSuggestions = document.getElementById('authorized-by-suggestions');

        if (interviewInput && interviewSuggestions && !interviewInput.contains(event.target) && !interviewSuggestions.contains(event.target)) {
            interviewSuggestions.innerHTML = '';
        }
        if (authInput && authSuggestions && !authInput.contains(event.target) && !authSuggestions.contains(event.target)) {
            authSuggestions.innerHTML = '';
        }
    });

    // For DataTable after HTMX swap
    htmx.on('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'accessorySection') {
            $('#accessoryTable').DataTable({
                "paging": false, // Handled by Django formsets for row management
                "searching": false,
                "info": false,
                "lengthChange": false
            });
        }
    });

</script>
{% endblock %}