{% comment %}
    This template is loaded via HTMX into #employeeTableContainer.
    It receives 'employees' (a queryset) and 'page_obj' (if List<PERSON>iew's pagination is active) as context.
{% endcomment %}
<div class="overflow-x-auto">
    <table id="employeeDataTable" class="min-w-full bg-white yui-datatable-theme border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            </tr>
        </thead>
        <tbody>
            {% if employees %}
                {% for employee in employees %}
                <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter + page_obj.start_index - 1 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {# Link to details page, assuming URL 'salary_edit_details' exists #}
                        <a href="{% url 'hr_payroll:salary_edit_details' employee.emp_id %}" class="text-blue-600 hover:underline">Select</a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ employee.fin_year.fin_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ employee.emp_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ employee.full_employee_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ employee.department.description }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ employee.bg_group.symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ employee.designation_rel }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="8" class="py-4 px-4 border-b border-gray-200 text-center text-lg font-bold text-maroon-600">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table content is loaded by HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#employeeDataTable')) {
            $('#employeeDataTable').DataTable().destroy(); // Destroy previous instance if it exists
        }
        $('#employeeDataTable').DataTable({
            "paging": true,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 20, // Matches ASP.NET PageSize
            "searching": true, // Enable client-side search box provided by DataTables
            "ordering": true,  // Enable client-side sorting
            "info": true,      // Show info about number of entries
            "autoWidth": false, // Disable auto-width to allow custom widths
            "columnDefs": [
                { "orderable": false, "targets": [0, 1] }, // Disable sorting on SN and Select columns
            ],
        });
    });
</script>