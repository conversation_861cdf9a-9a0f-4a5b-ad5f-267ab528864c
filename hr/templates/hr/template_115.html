<table id="bankloanTable" class="min-w-full bg-white yui-datatable-theme border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-4%">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-4%">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-6%">EmpId</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20%">Emp Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-18%">Bank Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Branch</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">Installment</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">From Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">To Date</th>
        </tr>
    </thead>
    <tbody>
        {% for loan in bankloans %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'bankloans_app:bankloan_print_details' emp_id=loan.EmpId %}"
                    hx-redirect="{% url 'bankloans_app:bankloan_print_details' emp_id=loan.EmpId %}"
                    hx-target="body" hx-swap="outerHTML"
                    _="on click add .htmx-request to body"
                    title="View Details">
                    Select
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ loan.EmpId }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.EmployeeName }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.BankName }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.Branch }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ loan.Amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ loan.Installment|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.fromDate }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ loan.ToDate }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-8 px-4 text-center font-bold text-lg text-maroon-600">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization (will be re-triggered by HTMX after swap)
    // This script block should only be included within the partial for re-initialization on HTMX swap
    // The main list.html block also has a document.ready() for initial page load.
    // Ensure `destroy: true` is used in the DataTables config to handle re-initialization.
</script>