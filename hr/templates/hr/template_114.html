{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h2 class="text-2xl font-bold text-gray-800">Bank Loan - Print</h2>
        </div>

        <form hx-post="{% url 'bankloans_app:bankloan_list' %}" hx-target="#bankloanTable-container" hx-swap="innerHTML" hx-indicator="#loading-indicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center mb-6">
                <div class="flex items-center">
                    <label class="font-bold text-gray-700 mr-4">Search Option :</label>
                    <div class="flex space-x-4">
                        {% for radio in form.search_option %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 font-bold text-gray-800">{{ radio.choice_label }}</span>
                        </label>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div id="search-panel" x-data="{ searchOption: '{{ form.search_option.value }}', selectedEmployee: '{{ form.employee_name_search.value|default:"" }}', selectedEmployeeId: '{{ form.selected_employee_id.value|default:"" }}', showSuggestions: false }"
                 x-init="$watch('searchOption', value => {
                     if (value === '0') {
                        $el.style.display = 'block';
                        // Trigger a load for Emp Wise search
                        htmx.trigger('#bankloanTable-container', 'refreshTable');
                     } else {
                        // For 'All' option, redirect using HTMX (hx-redirect on form submit)
                        document.querySelector('form').submit(); // Submitting the form will trigger HX-Redirect
                     }
                 })">

                <div x-show="searchOption === '0'" class="mb-4">
                    <div class="flex items-center space-x-2">
                        <!-- Dropdown field is hardcoded to "Employee Name" (value 0) in ASP.NET, so no need for actual select box -->
                        <span class="text-gray-700">Employee Name</span>
                        {{ form.employee_name_search }}
                        {{ form.selected_employee_id }} <!-- Hidden input for selected EmpId -->
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">Search</button>
                    </div>
                    <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-64" x-show="showSuggestions && selectedEmployee.length > 0">
                        <!-- Suggestions will be loaded here by HTMX -->
                    </div>
                    {% if form.employee_name_search.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.employee_name_search.errors }}</p>
                    {% endif %}
                </div>
            </div>
        </form>

        <div class="mb-4">
            <span id="loading-indicator" class="htmx-indicator text-blue-500 text-sm">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                Loading data...
            </span>
            <p class="text-red-500 font-bold" id="label2"></p> {# Placeholder for Label2 #}
            {% if messages %}
            <div class="mt-4">
                {% for message in messages %}
                <div class="p-3 mb-2 rounded-md {% if message.tags %}bg-{{ message.tags }}-100 text-{{ message.tags }}-800{% endif %}" role="alert">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <div id="bankloanTable-container"
             hx-trigger="load, refreshTable from:body, submit from:form"
             hx-get="{% url 'bankloans_app:bankloan_list' %}"
             hx-target="#bankloanTable-container"
             hx-swap="innerHTML"
             class="min-h-[410px] overflow-auto">
            <!-- Initial content or loading indicator -->
            <div class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Bank Loan Data...</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('bankLoanSearch', () => ({
            searchOption: '{{ form.search_option.value }}',
            selectedEmployee: '{{ form.employee_name_search.value|default:"" }}',
            selectedEmployeeId: '{{ form.selected_employee_id.value|default:"" }}',
            showSuggestions: false,

            // Function to handle selection from autocomplete
            selectEmployee(suggestionText) {
                this.selectedEmployee = suggestionText;
                // Extract the ID from the suggestion string (e.g., "Name [ID]")
                const match = suggestionText.match(/\[(.*?)\]$/);
                if (match && match[1]) {
                    this.selectedEmployeeId = match[1];
                } else {
                    this.selectedEmployeeId = '';
                }
                this.showSuggestions = false;
                // Optional: Submit the form immediately after selection
                // htmx.trigger(document.querySelector('form'), 'submit');
            },
            clearSelectedId() {
                // Clear the hidden ID if the user is typing again
                const currentText = this.selectedEmployee;
                const hiddenIdField = document.querySelector('input[name="selected_employee_id"]');
                if (hiddenIdField && hiddenIdField.value && !currentText.includes(hiddenIdField.value)) {
                    this.selectedEmployeeId = '';
                    hiddenIdField.value = ''; // Ensure DOM is updated
                }
            }
        }));
    });

    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Reinitialize DataTables after HTMX swaps the table content
        if (evt.target.id === 'bankloanTable-container' || evt.detail.elt.id === 'bankloanTable') {
            $('#bankloanTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "language": {
                    "emptyTable": "No data to display !"
                }
            });
        }
    });

    // Initial DataTables setup on page load (if not loaded via HTMX first)
    $(document).ready(function() {
        if ($('#bankloanTable').length) {
            $('#bankloanTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "language": {
                    "emptyTable": "No data to display !"
                }
            });
        }
    });
</script>
{% endblock %}