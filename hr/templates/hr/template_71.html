<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="tourintimationTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Intimation Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Employee Name</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Destination</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Start Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">End Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in tourintimations %}
            <tr>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.intimation_date|date:"Y-m-d" }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.employee_name }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.destination }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.start_date|date:"Y-m-d" }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.end_date|date:"Y-m-d" }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.status }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'tourintimation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'tourintimation_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center">No tour intimations found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Ensure DataTables is initialized only once and on new content.
// This script runs when HTMX swaps in the table content.
$(document).ready(function() {
    if (!$.fn.DataTable.isDataTable('#tourintimationTable')) {
        $('#tourintimationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 7] } // Disable sorting for SN and Actions columns
            ]
        });
    }
});
</script>