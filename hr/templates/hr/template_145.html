<div id="accessoryFormset" class="overflow-x-auto">
    <table id="accessoryTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Include In</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Perticulars</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for form in accessory_formset %}
            <tr {% if form.instance.pk %}id="accessory-{{ form.instance.pk }}"{% endif %}>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    {{ form.id }} {# Hidden ID field for existing instances #}
                    {{ form.includes_in }}
                    {% if form.includes_in.errors %}<p class="text-red-500 text-xs mt-1">{{ form.includes_in.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200">
                    {{ form.perticulars }}
                    {% if form.perticulars.errors %}<p class="text-red-500 text-xs mt-1">{{ form.perticulars.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    {{ form.qty }}
                    {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    {{ form.amount }}
                    {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    {# Calculate total dynamically on client-side with Alpine.js or HTMX #}
                    <span x-data="{ qty: parseFloat($el.closest('tr').querySelector('[name$=\"-qty\"]').value || 0), amount: parseFloat($el.closest('tr').querySelector('[name$=\"-amount\"]').value || 0) }"
                          x-init="$watch('$el.closest(\'tr\').querySelector(\'[name$=\"-qty\"]\').value', value => qty = parseFloat(value || 0)); $watch('$el.closest(\'tr\').querySelector(\'[name$=\"-amount\"]\').value', value => amount = parseFloat(value || 0));"
                          x-text="(qty * amount).toFixed(2)">
                        {% if form.instance.pk %}{{ form.instance.total|floatformat:2 }}{% else %}0.00{% endif %}
                    </span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {{ form.DELETE }}
                    <button type="button" 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                            hx-post="{% url 'hrmodules:offerletter_accessories_table_partial' %}" 
                            hx-vals='{"{{ form.DELETE.name }}": "on", "{{ form.prefix }}-TOTAL_FORMS": "{{ accessory_formset.total_form_count }}", "{{ form.prefix }}-INITIAL_FORMS": "{{ accessory_formset.initial_form_count }}", "{{ form.prefix }}-MIN_NUM_FORMS": "{{ accessory_formset.min_num }}", "{{ form.prefix }}-MAX_NUM_FORMS": "{{ accessory_formset.max_num }}", "accessory_formset_id_to_delete": "{{ form.instance.pk }}"}'
                            hx-target="#accessorySection"
                            hx-swap="innerHTML"
                            hx-trigger="click"
                            _="on htmx:afterSwap $dispatch('updateSalary')">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="7" class="py-2 px-4 bg-gray-50 border-t border-gray-200 text-right">
                    <button type="button" 
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                            hx-post="{% url 'hrmodules:offerletter_accessories_table_partial' %}" 
                            hx-vals='{"add_accessory": "true", "{{ accessory_formset.management_form.prefix }}-TOTAL_FORMS": "{{ accessory_formset.total_form_count }}", "{{ accessory_formset.management_form.prefix }}-INITIAL_FORMS": "{{ accessory_formset.initial_form_count }}", "{{ accessory_formset.management_form.prefix }}-MIN_NUM_FORMS": "{{ accessory_formset.min_num }}", "{{ accessory_formset.management_form.prefix }}-MAX_NUM_FORMS": "{{ accessory_formset.max_num }}"}'
                            hx-target="#accessorySection"
                            hx-swap="innerHTML"
                            _="on htmx:afterSwap $dispatch('updateSalary')">
                        Add New Accessory
                    </button>
                </td>
            </tr>
        </tfoot>
    </table>
    {{ accessory_formset.management_form }}
</div>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'accessorySection') {
            const accessoryTable = document.getElementById('accessoryTable');
            if ($.fn.DataTable.isDataTable(accessoryTable)) {
                $(accessoryTable).DataTable().destroy(); // Destroy previous DataTable instance
            }
            $(accessoryTable).DataTable({
                "paging": false,
                "searching": false,
                "info": false,
                "lengthChange": false,
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6] } // Disable sorting for SN and Actions columns
                ]
            });
        }
    });

    // Manually trigger recalculation on change of Qty or Amount in accessory rows
    document.addEventListener('change', function(event) {
        const target = event.target;
        if (target.matches('[name$="-qty"]') || target.matches('[name$="-amount"]') || target.matches('[name$="-includes_in"]')) {
            htmx.trigger(document.body, 'updateSalary');
        }
    });
</script>