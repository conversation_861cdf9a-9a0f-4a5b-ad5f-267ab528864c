<div class="overflow-x-auto">
    <table id="employeeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Full Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Joining</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for employee in employees %}
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.employee_id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.get_full_name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.email }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ employee.date_of_joining|date:"M d, Y" }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if employee.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ employee.is_active|yesno:"Active,Inactive" }}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-4 focus:outline-none"
                        hx-get="{% url 'employee_edit' employee.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal transition ease-out duration-300 transform scale-100"
                        title="Edit"
                    >
                        <i class="fas fa-edit"></i>
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 focus:outline-none"
                        hx-get="{% url 'employee_delete' employee.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal transition ease-out duration-300 transform scale-100"
                        title="Delete"
                    >
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No employee records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // This script block will be executed after HTMX loads the content
    // The DataTables initialization is moved to the list.html's htmx:afterSwap listener
    // to ensure it runs only once the table content is in the DOM and the page is ready.
    // However, for direct rendering or if list.html doesn't exist, this might be needed.
    // Keeping it here for component independence, but list.html handles the trigger.
    // $('#employeeTable').DataTable({
    //     "pageLength": 10,
    //     "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
    //     "responsive": true,
    //     "autoWidth": false,
    // });
</script>