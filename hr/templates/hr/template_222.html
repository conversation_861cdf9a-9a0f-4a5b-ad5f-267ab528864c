{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Mobile Bills Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'mobile_bill_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Mobile Bill
        </button>
    </div>

    <div id="mobileBillTable-container"
         hx-trigger="load, refreshMobileBillList from:body"
         hx-get="{% url 'mobile_bill_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Mobile Bills...</p>
        </div>
    </div>

    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state
        // (e.g., specific form interactions within the modal)
    });

    // Close modal on HX-Trigger if needed (e.g., after a successful form submission)
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.requestHeaders['HX-Trigger'] && evt.detail.requestHeaders['HX-Trigger'].includes('refreshMobileBillList')) {
            // If the response is a 204 (no content) and triggers a list refresh, close the modal.
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Add event listener to close modal if messages are shown (e.g., after non-HTMX form submission)
    document.body.addEventListener('show.bs.modal', function() {
        // This is a placeholder. Real implementation depends on how messages are displayed
        // and if they trigger a modal close.
    });

</script>
{% endblock %}