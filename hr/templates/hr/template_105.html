<!-- hr_transactions/templates/hr_transactions/employee/_employee_table.html -->
<table id="employeeTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">First Name</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Name</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hire Date</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
            <th class="py-3 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for employee in employees %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ employee.first_name }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ employee.last_name }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ employee.email }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ employee.hire_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                {% if employee.is_active %}
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                {% else %}
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-center text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md shadow-sm text-xs mr-2"
                    hx-get="{% url 'employee_edit' employee.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm text-xs"
                    hx-get="{% url 'employee_delete' employee.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No employees found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables only if the table element is present and not already initialized
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#employeeTable')) {
            $('#employeeTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": 'lfrtip', // Default DataTables DOM structure
            });
        }
    });
</script>