{% comment %}
    This template is loaded via HTMX into #search_input_container.
    It receives 'search_field' (the selected dropdown value) and 'search_text' (current input value).
{% endcomment %}
{% if search_field == 'employee_name' %}
    <input type="text" id="id_search_text_employee_name" name="search_text" 
           class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
           placeholder="Enter Employee Name" 
           value="{{ search_text }}"
           autocomplete="off">
    <script>
        // Initialize jQuery UI Autocomplete for employee name
        // This script runs every time this partial is loaded by HTMX.
        $(function() {
            $("#id_search_text_employee_name").autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: "{% url 'hr_payroll:employee_autocomplete' %}",
                        data: {
                            term: request.term
                        },
                        dataType: "json",
                        success: function(data) {
                            response(data);
                        }
                    });
                },
                minLength: 1, // Matches ASP.NET MinimumPrefixLength
                // AutoCompleteExtender has FirstRowSelected=True, CompletionSetCount=2.
                // jQuery UI's behavior is similar for FirstRowSelected. CompletionSetCount not directly applicable.
            });
        });
    </script>
{% else %}
    <input type="text" id="id_search_text_mrs" name="search_text" 
           class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
           placeholder="Enter Department or BG Group Name" 
           value="{{ search_text }}">
{% endif %}