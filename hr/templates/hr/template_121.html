<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Bank Loan for <strong>{{ object.employee.get_employee_full_name }}</strong> from <strong>{{ object.bank_name }}</strong>?</p>
    
    <form hx-post="{% url 'bankloan_delete' object.pk %}" hx-swap="none"> {# No swap, just trigger refresh #}
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md">
                Delete
            </button>
        </div>
    </form>
</div>