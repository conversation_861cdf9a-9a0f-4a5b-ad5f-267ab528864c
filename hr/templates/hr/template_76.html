<table id="tourintimationTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TI No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place of Tour</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour Start Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour End Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in tour_intimations %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.financial_year_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a href="{{ obj.get_absolute_url }}" class="text-blue-600 hover:underline">
                    {{ obj.ti_no }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee }}</td> {# Assumes __str__ on Employee outputs formatted name #}
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.work_order_or_na }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.business_group_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.project_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.full_place_of_tour }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.tour_start_date|date:"d M Y" }}</td> {# Format date as in ASP.NET's FromDateDMY #}
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.tour_end_date|date:"d M Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md text-sm transition duration-300 ease-in-out mr-2"
                    hx-get="{% url 'tourintimation_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'tourintimation_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="11" class="py-4 text-center text-red-700 text-lg font-semibold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>