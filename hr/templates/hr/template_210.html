{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ selectedField: '0' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">{{ page_title }}</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Staff</h3>
        <form hx-post="{% url 'hr_staff:officestaff_table' %}" hx-swap="none" hx-trigger="submit from #searchForm">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div class="mb-4">
                    <label for="{{ form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_field.label }}
                    </label>
                    {{ form.search_field }}
                    {% if form.search_field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.search_field.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <label for="{{ form.search_text_mrs.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_text_mrs.label }}
                    </label>
                    {{ form.search_text_mrs }}
                    {% if form.search_text_mrs.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.search_text_mrs.errors }}</p>
                    {% endif %}
                </div>

                <div class="mb-4" x-data="{
                    autocompleteResults: [],
                    selectedIndex: -1,
                    selectResult(index) {
                        if (index !== -1) {
                            this.$refs.employeeInput.value = this.autocompleteResults[index];
                            this.autocompleteResults = []; // Clear results after selection
                            this.selectedIndex = -1;
                        }
                    },
                    handleKeydown(event) {
                        if (this.autocompleteResults.length === 0) return;
                        if (event.key === 'ArrowDown') {
                            event.preventDefault();
                            this.selectedIndex = (this.selectedIndex + 1) % this.autocompleteResults.length;
                        } else if (event.key === 'ArrowUp') {
                            event.preventDefault();
                            this.selectedIndex = (this.selectedIndex - 1 + this.autocompleteResults.length) % this.autocompleteResults.length;
                        } else if (event.key === 'Enter') {
                            event.preventDefault();
                            this.selectResult(this.selectedIndex);
                        } else if (event.key === 'Escape') {
                            this.autocompleteResults = [];
                            this.selectedIndex = -1;
                        }
                    }
                }">
                    <label for="{{ form.search_text_emp.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_text_emp.label }}
                    </label>
                    <input 
                        type="text" 
                        id="{{ form.search_text_emp.id_for_label }}" 
                        name="{{ form.search_text_emp.name }}"
                        class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        hx-get="{% url 'hr_staff:employee_autocomplete' %}"
                        hx-target="#autocomplete-results"
                        hx-trigger="keyup changed delay:500ms, search"
                        hx-indicator="#autocomplete-loading"
                        value="{{ form.search_text_emp.value|default:'' }}"
                        x-show="selectedField === '0'"
                        x-ref="employeeInput"
                        @keydown="handleKeydown"
                        @input="autocompleteResults = []"
                        @focus="autocompleteResults = []"
                    >
                    <div id="autocomplete-loading" class="htmx-indicator mt-1 text-blue-500 text-xs">Loading...</div>
                    <ul id="autocomplete-results" 
                        class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
                        x-show="autocompleteResults.length > 0">
                        <!-- HTMX will swap results here -->
                    </ul>
                    {% if form.search_text_emp.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.search_text_emp.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="md:col-span-3 text-right">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <div id="staffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body"
         hx-get="{% url 'hr_staff:officestaff_table' %}"
         hx-target="this"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading staff data...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is included in base.html. No additional Alpine.js specific initialization here.
    // The DataTables initialization is handled within the _officestaff_table.html partial
    // because the table itself is loaded dynamically.
</script>
{% endblock %}