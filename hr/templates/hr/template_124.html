{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Bank Loan - Edit</h2>

        <div class="flex items-center space-x-4 mb-6">
            <label for="{{ search_form.drp_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ search_form.drp_field.label }}:
            </label>
            {{ search_form.drp_field }}

            <div class="relative flex-grow">
                <label for="{{ search_form.txt_emp_name.id_for_label }}" class="sr-only">
                    {{ search_form.txt_emp_name.label }}:
                </label>
                {{ search_form.txt_emp_name }}
                <datalist id="employee-suggestions"></datalist> {# Autocomplete suggestions will populate here #}
            </div>

            <button
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'bankloan_table' %}"
                hx-target="#bankloan-table-container"
                hx-swap="innerHTML"
                hx-include="#search-form" {# Include search form fields #}
                hx-indicator="#loading-indicator"
            >
                Search
            </button>
            <span id="loading-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>

        <div id="bankloan-table-container"
             hx-trigger="load, refreshBankLoanTable from:body"
             hx-get="{% url 'bankloan_table' %}"
             hx-swap="innerHTML"
             hx-indicator="#loading-indicator"
             class="min-h-[200px] flex items-center justify-center">
            <!-- Initial content or loading state -->
            <p class="text-gray-500">Use the search to find bank loan records.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is included in base.html. No additional global Alpine.js init needed here.
    // The component logic for rows is directly in the _bankloan_table_partial.html
</script>
{% endblock %}