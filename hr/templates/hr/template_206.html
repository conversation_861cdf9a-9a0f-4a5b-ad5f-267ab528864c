<table id="officestaffTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No.</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for member in officestaff_members %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.title }}. {{ member.employee_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.department_fk.description|default:"N/A" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.designation_fk.type|default:"N/A" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.mobile_no_fk.mobile_no|default:"N/A" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <a href="{% url 'staff_detail_report' member.empid %}"
                   class="text-blue-600 hover:text-blue-900 mr-4">View Report</a>
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg text-xs shadow-sm transition duration-300 ease-in-out mr-2"
                    hx-get="{% url 'officestaff_edit' member.empid %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg text-xs shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'officestaff_delete' member.empid %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-sm text-gray-500">No staff members found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after HTMX loads the content
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#officestaffTable')) {
            $('#officestaffTable').DataTable().destroy();
        }
        $('#officestaffTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [5] } // Disable sorting on Actions column
            ]
        });
    });
</script>