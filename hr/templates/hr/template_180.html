<!-- hr_payroll/salary_slip_report.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 bg-white shadow-lg rounded-lg print:shadow-none print:rounded-none">
    <div class="max-w-4xl mx-auto p-6 border border-gray-300">
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Salary Slip</h1>
            <h2 class="text-xl font-semibold text-gray-700">{{ report_data.month_name }} {{ report_data.year }}</h2>
        </div>

        {% if report_error %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ report_error }}</span>
        </div>
        {% elif report_data %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-6 mb-8 text-sm">
            <div>
                <p class="text-gray-700"><strong class="font-medium">Employee ID:</strong> {{ report_data.emp_id }}</p>
                <p class="text-gray-700"><strong class="font-medium">Employee Name:</strong> {{ report_data.employee_name }}</p>
                <p class="text-gray-700"><strong class="font-medium">Department:</strong> {{ report_data.department }}</p>
                <p class="text-gray-700"><strong class="font-medium">Designation:</strong> {{ report_data.designation }}</p>
                <p class="text-gray-700"><strong class="font-medium">Grade:</strong> {{ report_data.grade }}</p>
            </div>
            <div>
                <p class="text-gray-700"><strong class="font-medium">Status:</strong> {{ report_data.status }}</p>
                <p class="text-gray-700"><strong class="font-medium">PF No:</strong> {{ report_data.pf_no }}</p>
                <p class="text-gray-700"><strong class="font-medium">PAN No:</strong> {{ report_data.pan_no }}</p>
                <p class="text-gray-700"><strong class="font-medium">Bank A/C No:</strong> {{ report_data.bank_account_no }}</p>
                <p class="text-gray-700"><strong class="font-medium">Date:</strong> {{ report_data.current_date }}</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">Earnings</h3>
                <ul class="space-y-2 text-sm">
                    <li class="flex justify-between items-center"><span class="text-gray-700">Basic:</span> <span class="font-medium">{{ report_data.basic_cal|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">DA:</span> <span class="font-medium">{{ report_data.da_cal|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">HRA:</span> <span class="font-medium">{{ report_data.hra_cal|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Conveyance:</span> <span class="font-medium">{{ report_data.conveyance_cal|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Education:</span> <span class="font-medium">{{ report_data.education_cal|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Medical:</span> <span class="font-medium">{{ report_data.medical_cal|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center font-bold text-gray-800 border-t pt-2 mt-2"><span>Gross Total:</span> <span>{{ report_data.gross_total_cal|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Attendance Bonus:</span> <span class="font-medium">{{ report_data.attendance_bonus|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Ex-Gratia:</span> <span class="font-medium">{{ report_data.ex_gratia|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Misc. Additions:</span> <span class="font-medium">{{ report_data.miscellaneous_additions|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center font-bold text-lg text-green-700 border-t border-b py-2 my-2"><span>Total Earnings:</span> <span>{{ report_data.gross_total_payable|floatformat:2 }}</span></li>
                </ul>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">Deductions</h3>
                <ul class="space-y-2 text-sm">
                    <li class="flex justify-between items-center"><span class="text-gray-700">PF of Employee:</span> <span class="font-medium">{{ report_data.pf_of_employee|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Professional Tax:</span> <span class="font-medium">{{ report_data.p_tax|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Personal Loan Installment:</span> <span class="font-medium">{{ report_data.personal_loan_install|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Mobile Bill:</span> <span class="font-medium">{{ report_data.mobile_bill|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Misc. Deductions:</span> <span class="font-medium">{{ report_data.miscellaneous_deductions|floatformat:2 }}</span></li>
                    <li class="flex justify-between items-center font-bold text-lg text-red-700 border-t border-b py-2 my-2"><span>Total Deductions:</span> <span>{{ report_data.total_deductions|floatformat:2 }}</span></li>
                </ul>

                <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2 mt-8">Attendance Summary</h3>
                <ul class="space-y-2 text-sm">
                    <li class="flex justify-between items-center"><span class="text-gray-700">Working Days (Month):</span> <span class="font-medium">{{ report_data.working_days|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Present Days:</span> <span class="font-medium">{{ report_data.present_days|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Absent Days:</span> <span class="font-medium">{{ report_data.absent_days|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Sundays in Month:</span> <span class="font-medium">{{ report_data.sundays_in_month|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Holidays:</span> <span class="font-medium">{{ report_data.holiday_count|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Late In:</span> <span class="font-medium">{{ report_data.late_in|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">COFF:</span> <span class="font-medium">{{ report_data.coff_days|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">Half Days:</span> <span class="font-medium">{{ report_data.half_days|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">PL:</span> <span class="font-medium">{{ report_data.pl_days|floatformat:0 }}</span></li>
                    <li class="flex justify-between items-center"><span class="text-gray-700">LWP:</span> <span class="font-medium">{{ report_data.lwp_days|floatformat:0 }}</span></li>
                </ul>
            </div>
        </div>
        
        <div class="flex justify-between items-center font-bold text-2xl text-blue-700 border-t-2 border-b-2 py-4 mt-8">
            <span>Net Pay:</span>
            <span>₹ {{ report_data.net_pay|floatformat:2 }}</span>
        </div>

        <div class="mt-8 flex justify-center space-x-4 print:hidden">
            <button
                class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                onclick="window.print()">
                Print Salary Slip
            </button>
            <a href="{% url 'salary_slip_list' %}"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-flex items-center">
                Back to Salary Selection
            </a>
            <!-- HTMX example for PDF download (if PDF generation implemented) -->
            <button
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'salary_slip_download_pdf' emp_id=report_data.emp_id month_id=request.resolver_match.kwargs.month_id %}"
                hx-trigger="click"
                hx-swap="none"
                hx-indicator="#pdf-loading-indicator"
                hx-on="htmx:afterRequest: if(event.detail.successful) { alert('PDF download initiated!'); } else { alert('Failed to generate PDF.'); }">
                Download PDF
            </button>
            <div id="pdf-loading-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management
    });
</script>
{% endblock %}