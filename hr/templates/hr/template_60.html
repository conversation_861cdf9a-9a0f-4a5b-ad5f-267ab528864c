{% extends 'core/base.html' %}
{% load tailwind_filters %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Gate Pass Report</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Filter Report</h3>
        <form hx-get="{% url 'gatepass_report_table' %}" 
              hx-target="#gatepassReportTable-container" 
              hx-swap="innerHTML"
              hx-indicator="#loadingIndicator"
              class="space-y-4">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {{ filter_form|crispy }} {# Render form fields with Tailwind CSS via crispy-forms #}
            </div>

            <div class="flex justify-end mt-6">
                <button 
                    type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
    
    <div id="gatepassReportTable-container" 
         hx-trigger="load delay:10ms, filterFormSubmit from:#filter-form" {# Load on page load, and on form submit #}
         hx-get="{% url 'gatepass_report_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center p-8 bg-white shadow-md rounded-lg">
            <div id="loadingIndicator" class="htmx-indicator inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="htmx-indicator mt-4 text-gray-600 text-lg">Loading Gate Pass Report...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management.
        // For example, if you had complex toggles or dynamic elements beyond HTMX.
    });

    // Helper to re-initialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'gatepassReportTable-container') {
            const table = $('#gatePassReportTable');
            if (table.length && !$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": true,
                    "order": [[ 0, "asc" ]] // Example default sort
                });
            }
        }
    });

    // Initial load for DataTables, if not triggered by hx-trigger="load"
    $(document).ready(function() {
        // The hx-trigger="load" on #gatepassReportTable-container handles initial data load
        // so no direct DataTable init needed here for the main table.
        // This block can be used for other page-level JS if necessary.
    });
</script>
{% endblock %}