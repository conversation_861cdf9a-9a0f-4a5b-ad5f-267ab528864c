<form hx-get="{% url 'tourintimation_table' %}" hx-target="#tourintimationTable-container" hx-swap="innerHTML" hx-trigger="submit">
    <div class="flex flex-wrap items-end gap-4">
        <div class="flex flex-col">
            <label for="id_drp_field" class="block text-sm font-medium text-gray-700 mb-1">Search By:</label>
            {{ search_form.drp_field }}
        </div>

        <div class="flex flex-col flex-grow min-w-[200px]">
            {% if search_form.drp_field.value == '0' or search_form.drp_field.value == '2' or search_form.drp_field.value == '4' or search_form.drp_field.value == 'Select' %}
                <label for="id_txt_mrs" class="block text-sm font-medium text-gray-700 mb-1">
                    {% if search_form.drp_field.value == '0' %}TI No{% elif search_form.drp_field.value == '2' %}WO No{% elif search_form.drp_field.value == '4' %}Project Name{% else %}Enter Search Value{% endif %}:
                </label>
                {{ search_form.txt_mrs }}
            {% else %}
                <input type="text" class="hidden" name="txt_mrs" value="">
            {% endif %}

            {% if search_form.drp_field.value == '1' %}
                <label for="id_txt_emp_name" class="block text-sm font-medium text-gray-700 mb-1">Employee Name:</label>
                {{ search_form.txt_emp_name }}
                <input type="hidden" name="emp_id_code" id="id_emp_id_code" value="{{ search_form.emp_id_code.value }}"> {# Hidden field for EmpId #}
                <datalist id="emp-suggestions"></datalist> {# Autocomplete suggestions go here #}
            {% else %}
                <input type="text" class="hidden" name="txt_emp_name" value="">
                <input type="hidden" name="emp_id_code" value="">
            {% endif %}

            {% if search_form.drp_field.value == '3' %}
                <label for="id_drp_group" class="block text-sm font-medium text-gray-700 mb-1">BG Group:</label>
                {{ search_form.drp_group }}
            {% else %}
                <select class="hidden" name="drp_group"></select>
            {% endif %}
        </div>

        <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
            Search
        </button>
    </div>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // HTMX re-initializes scripts in swapped content.
        // This script ensures correct visibility based on the selected dropdown
        // and handles the update of the hidden emp_id_code field when an autocomplete suggestion is selected.
        
        const drpField = document.getElementById('id_drp_field');
        const txtEmpName = document.getElementById('id_txt_emp_name');
        const empIdCodeHidden = document.getElementById('id_emp_id_code');
        const empSuggestions = document.getElementById('emp-suggestions');

        function updateFieldVisibility() {
            const selectedValue = drpField.value;
            // The template itself handles visibility based on Django's `search_form.drp_field.value`
            // and HTMX re-swaps the content. No additional JS is strictly needed for initial visibility.
            // However, this function is useful if we wanted to dynamically toggle without a full hx-swap.
        }

        drpField.addEventListener('change', function() {
            // Re-submit the form via HTMX to update the visible fields
            // This re-renders the search form partial, showing/hiding fields based on the new selection
            this.closest('form').dispatchEvent(new Event('submit', { bubbles: true }));
        });

        // Event listener for when an option is selected from the datalist
        if (txtEmpName) {
            txtEmpName.addEventListener('input', function() {
                const selectedValue = this.value;
                const option = Array.from(empSuggestions.options).find(opt => opt.value === selectedValue);
                if (option) {
                    const empIdMatch = selectedValue.match(/\[(.*?)\]$/);
                    if (empIdMatch) {
                        empIdCodeHidden.value = empIdMatch[1];
                    }
                } else {
                    empIdCodeHidden.value = ''; // Clear if text doesn't match an option
                }
            });
        }

        updateFieldVisibility(); // Initial call
    });
</script>