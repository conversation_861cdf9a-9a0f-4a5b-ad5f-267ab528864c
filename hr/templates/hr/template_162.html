{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white font-bold py-2 px-4 rounded-t-md mb-4">
        &nbsp;PayRoll - Edit
    </div>

    <div class="bg-white p-6 rounded-b-md shadow-md">
        {# Search Form - Uses HTMX to submit and update table #}
        <form hx-get="{% url 'hr_payroll:salary_edit_table_partial' %}" hx-target="#employeeTableContainer" hx-swap="innerHTML">
            <div class="flex items-end space-x-4 mb-6">
                <div class="flex-shrink-0">
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">Search By</label>
                    {{ search_form.search_field }}
                </div>
                
                <div id="search_input_container" class="flex-grow">
                    {# This div will be updated by HTMX when search_field changes #}
                    {# Initial render of search input based on default choice or existing GET param #}
                    {% include 'hr_payroll/salary_edit/_search_input.html' with search_field=search_form.search_field.value search_text=request.GET.search_text %}
                </div>
                
                <div class="flex-shrink-0">
                    <button type="submit" class="redbox bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
                
                {% if message %}
                <div class="ml-4 text-red-500 font-bold">
                    {{ message }}
                </div>
                {% endif %}
            </div>
        </form>

        {# Employee Table Container - Content loaded via HTMX #}
        <div id="employeeTableContainer"
             hx-trigger="load, reloadEmployeeList from:body" {# Loads on page load, and on custom event #}
             hx-get="{% url 'hr_payroll:salary_edit_table_partial' %}"
             hx-swap="innerHTML">
            <!-- Initial loading state while HTMX fetches the table -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading employee data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Required for DataTables and jQuery UI Autocomplete #}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet">
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/SRWaU3fSEKrGVtP6xeOzQkKVLgBqTJblzcNQ=" crossorigin="anonymous"></script>
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">

<script>
    // Alpine.js component initialization if needed for modals or other UI state
    document.addEventListener('alpine:init', () => {
        // e.g., Alpine.data('modal', () => ({ isOpen: false, open: () => this.isOpen = true, close: () => this.isOpen = false }));
    });

    // DataTables initialization is handled within the _employee_table.html partial,
    // ensuring it runs after HTMX loads the table content.
    // jQuery UI Autocomplete init is handled within _search_input.html partial
</script>
{% endblock %}