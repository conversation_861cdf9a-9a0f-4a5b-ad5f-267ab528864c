{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Tour Intimations</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'tourintimation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Tour Intimation
        </button>
    </div>
    
    <div id="tourintimationTable-container"
         hx-trigger="load, refreshTourIntimationList from:body"
         hx-get="{% url 'tourintimation_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Tour Intimations...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>

    {# Acknowledge the original "Page is under constraction" message, if truly needed. #}
    {# For a functional page, this would typically be removed. #}
    {# <p class="text-gray-500 mt-8 text-center text-sm">Note: This module was originally marked as 'under construction' in the legacy system.</p> #}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for broader page state
        // For instance, managing a filter state that influences the HTMX table
    });

    // Handle messages from Django
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Look for messages container and display toasts or similar
        const messagesContainer = document.getElementById('messages');
        if (messagesContainer) {
            // Logic to display Django messages as a toast or notification
            // Example: Alpine.js global store for notifications
            // if (window.Alpine && window.Alpine.store) {
            //     window.Alpine.store('notifications').add(messageText, messageType);
            // }
        }
    });

</script>
{% endblock %}