{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Tour Intimation Edit</h2>

    <!-- Search Form Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div id="tourintimation-search-form-container"
             hx-get="{% url 'tourintimation_search_form' %}"
             hx-trigger="load, refreshSearchForm from:body"
             hx-swap="innerHTML">
            <!-- Search form will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading search form...</p>
            </div>
        </div>
    </div>

    <!-- Tour Intimation List Table -->
    <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-gray-700">Tour Intimations</h3>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'tourintimation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Tour Intimation
        </button>
    </div>
    
    <div id="tourintimationTable-container"
         hx-trigger="load, refreshTourIntimationList from:body"
         hx-get="{% url 'tourintimation_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Tour Intimations...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterSwap from #modalContent if !event.detail.target.closest('.modal-content') remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 md:mx-auto relative">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is primarily for UI state, like modal toggle
        // HTMX handles most of the dynamic content loading.
    });
</script>
{% endblock %}