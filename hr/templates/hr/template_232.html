<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Mobile Bill</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {# Hidden fields for context of the specific bill being edited #}
            <input type="hidden" name="employee" value="{{ form.instance.employee.user_id }}">
            <input type="hidden" name="bill_month" value="{{ form.instance.bill_month }}">
            <input type="hidden" name="comp_id" value="{{ form.instance.comp_id }}">
            <input type="hidden" name="fin_year_id" value="{{ form.instance.fin_year_id }}">

            {# Display employee name (read-only) #}
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Employee Name:</label>
                <p class="mt-1 text-gray-900">{{ form.instance.employee.employee_name }}</p>
            </div>

            {# Display bill month (read-only) #}
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Bill Month:</label>
                <p class="mt-1 text-gray-900">{{ form.instance.bill_month|date:"F" }}</p> {# Assuming bill_month is an integer representing month #}
            </div>

            {# Form fields for Bill Amount and Taxes #}
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Save Changes
            </button>
        </div>
    </form>
</div>