{% comment %} This partial is swapped into #search-fields-container {% endcomment %}
<div class="flex items-end space-x-4">
    {% if selected_field == '0' or selected_field == '2' or selected_field == '4' or selected_field == 'Select' %}
    <div class="flex-grow">
        <label for="{{ form.txt_mrs.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Search Value
        </label>
        {{ form.txt_mrs }}
    </div>
    {% elif selected_field == '1' %}
    <div class="flex-grow relative">
        <label for="{{ form.txt_emp_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Employee Name
        </label>
        {{ form.txt_emp_name }}
        <div id="autocomplete-results" class="absolute bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full z-10 max-h-60 overflow-y-auto">
            {# Autocomplete results will be loaded here via HTMX #}
        </div>
    </div>
    {% elif selected_field == '3' %}
    <div class="flex-grow">
        <label for="{{ form.drp_group.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Business Group
        </label>
        {{ form.drp_group }}
    </div>
    {% endif %}
</div>