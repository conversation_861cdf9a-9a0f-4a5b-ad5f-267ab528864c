<div class="overflow-x-auto">
    <table id="salarysummaryreportTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Total</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PF Emp</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">P. Tax</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Net Pay</th>
                <!-- Add headers for all relevant fields from SalaryReportEntry -->
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in salarysummaryreports %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.empid }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <a href="{{ obj.path }}" target="_blank" class="text-blue-600 hover:underline">
                        {{ obj.employee_name }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.month }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.department }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.designation }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.status }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap">{{ obj.gross_total_cal|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap">{{ obj.pf_of_employee|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap">{{ obj.p_tax|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap font-semibold">{{ obj.net_pay|floatformat:2 }}</td>
                <!-- Add cells for all other fields -->
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <!-- These buttons are hypothetical for a report entry -->
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'salarysummaryreport_edit' obj.pk %}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit (Simulated)
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'salarysummaryreport_delete' obj.pk %}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete (Simulated)
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-4 text-center text-gray-500">No report data available. Please check parameters.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#salarysummaryreportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [], // Disable initial sorting
            "pagingType": "full_numbers", // For pagination controls
            "scrollX": true // Enable horizontal scrolling for many columns
        });
    });
</script>