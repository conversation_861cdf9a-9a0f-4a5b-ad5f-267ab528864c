{% comment %} This partial template is loaded via HTMX into #office_staff_table_container {% endcomment %}
<div class="overflow-x-auto">
    <table id="officeStaffTable" class="min-w-full bg-white border-collapse yui-datatable-theme">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ERP Mail</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email Id</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SwapCard No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resign Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% if office_staff_list %}
                {% for obj in office_staff_list %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.emp_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_full_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.department.description|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.business_group.symbol|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.designation.symbol|default:'' }} - {{ obj.designation.type|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.grade.symbol|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.gender }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.mobile_no.mobile_no|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.email_id1 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.email_id2 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.swap_card.swap_card_no|default:'' }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.get_formatted_joining_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.get_formatted_resignation_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <button
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs"
                            hx-get="{% url 'hr_reports:office_staff_detail_redirect' obj.emp_id %}"
                            hx-swap="none"
                            hx-on::after-request="window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect')"
                        >
                            Select
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="15" class="py-4 px-4 text-center font-bold text-lg text-maroon-600">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>