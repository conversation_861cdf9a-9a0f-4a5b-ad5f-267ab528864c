{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 space-y-4 sm:space-y-0">
        <h2 class="text-3xl font-extrabold text-gray-800">Offer Letter Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'offermaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .block to #modal then add .opacity-100 to #modal backdrop-filter transition duration-300 ease-in-out then add .scale-100 to #modalContent transform transition duration-300 ease-in-out">
            Add New Offer Letter
        </button>
    </div>

    {# Search and Autocomplete Input #}
    <div class="mb-6">
        <div x-data="{ searchTerm: '', suggestions: [], showSuggestions: false, selectedIndex: -1 }"
             @click.away="showSuggestions = false">
            <label for="employeeSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Employee Name:</label>
            <div class="relative">
                <input type="text" id="employeeSearch" name="employee_name"
                       x-model="searchTerm"
                       @input.debounce.300ms="
                            if (searchTerm.length > 0) {
                                hx_get('/hr/offermaster/autocomplete/?prefixText=' + searchTerm)
                                    .then(response => response.json())
                                    .then(data => {
                                        suggestions = data;
                                        showSuggestions = true;
                                        selectedIndex = -1;
                                    })
                            } else {
                                suggestions = [];
                                showSuggestions = false;
                            }
                       "
                       @keydown.arrow-down.prevent="if(showSuggestions) selectedIndex = (selectedIndex + 1) % suggestions.length"
                       @keydown.arrow-up.prevent="if(showSuggestions) selectedIndex = (selectedIndex - 1 + suggestions.length) % suggestions.length"
                       @keydown.enter.prevent="if(selectedIndex !== -1) { searchTerm = suggestions[selectedIndex]; showSuggestions = false; $dispatch('search-triggered'); }"
                       class="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                       placeholder="Type employee name to search...">
                <ul x-show="showSuggestions && suggestions.length > 0"
                    class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">
                    <template x-for="(suggestion, index) in suggestions" :key="index">
                        <li @click="searchTerm = suggestion; showSuggestions = false; $dispatch('search-triggered');"
                            :class="{ 'bg-blue-100': index === selectedIndex }"
                            class="px-4 py-2 cursor-pointer hover:bg-blue-50">
                            <span x-text="suggestion"></span>
                        </li>
                    </template>
                </ul>
            </div>
            <button
                class="mt-3 bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                hx-get="{% url 'offermaster_table' %}?employee_name={{ '{' }}{{ '{' }}searchTerm{{ '}' }}{{ '}' }}"
                hx-target="#offermasterTable-container"
                hx-swap="innerHTML"
                hx-indicator="#loadingIndicator"
                hx-trigger="click, search-triggered from:root">
                Search
            </button>
        </div>
    </div>

    {# Container for the DataTable, loaded via HTMX #}
    <div id="offermasterTable-container"
         hx-trigger="load, refreshOfferLettersList from:body" {# Triggers initial load and list refreshes #}
         hx-get="{% url 'offermaster_table' %}"
         hx-swap="innerHTML">
        {# Loading indicator for HTMX #}
        <div id="loadingIndicator" class="htmx-indicator text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Offer Letters...</p>
        </div>
    </div>

    {# Modal for forms (Create, Update, Delete) #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center opacity-0 hidden transition-opacity duration-300 ease-in-out"
         _="on click if event.target.id == 'modal' remove .block from me then remove .opacity-100 from me then remove .scale-100 from #modalContent"
         x-data="{ isOpen: false }"
         x-init="$watch('isOpen', value => { if(value) { $el.classList.remove('hidden'); requestAnimationFrame(() => $el.classList.add('opacity-100')); } else { $el.classList.remove('opacity-100'); $el.addEventListener('transitionend', () => { if (!$el.classList.contains('opacity-100')) $el.classList.add('hidden'); }, {once: true}); } })"
         @refreshOfferLettersList.document="isOpen = false"> {# Close modal on list refresh #}
        <div id="modalContent" class="bg-white p-8 rounded-xl shadow-2xl max-w-3xl w-full transform scale-95 transition-transform duration-300 ease-in-out"
             _="on htmx:afterOnLoad remove .scale-95 from me add .scale-100 to me"
             @click.stop=""> {# Prevent clicks inside modal from closing it #}
            {# Content loaded here via HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() {
                this.isOpen = true;
            },
            close() {
                this.isOpen = false;
            }
        }));
    });
</script>
{% endblock %}