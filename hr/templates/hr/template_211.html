<div class="bg-white shadow-md rounded-lg overflow-hidden">
    {% if officestaff_list %}
    <table id="officeStaffTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resignation Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in officestaff_list %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.emp_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-left">{{ obj.employee_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.dept_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.bg_group }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.designation }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.mobile_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.joining_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.resignation_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">
                    <a href="/module/hr/transactions/officestaff_edit_details/?EmpId={{ obj.emp_id }}&ModId=12&SubModId=24" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                        Select
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="fontcss text-center text-red-700 text-lg py-8">No data to display !</p>
    {% endif %}
</div>

<script>
// DataTables initialization
// This script runs after HTMX has swapped in the new content
$(document).ready(function() {
    $('#officeStaffTable').DataTable({
        "pageLength": 20, // As per ASP.NET GridView PageSize
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 9] } // SN and Actions columns
        ]
    });
});
</script>