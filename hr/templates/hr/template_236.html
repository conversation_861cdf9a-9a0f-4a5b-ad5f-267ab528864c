<div class="p-6 bg-white rounded-xl shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 text-center">{{ form.instance.pk|yesno:'Edit,Add' }} News Notice</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-encoding="multipart/form-data">
        {% csrf_token %}
        
        <div class="space-y-5">
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.title.label }}
                    {% if form.title.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.title.errors|join:", " }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.in_details.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.in_details.label }}
                    {% if form.in_details.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.in_details }}
                {% if form.in_details.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.in_details.errors|join:", " }}</p>
                {% endif %}
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.from_date.label }}
                        {% if form.from_date.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors|join:", " }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.to_date.label }}
                        {% if form.to_date.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors|join:", " }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div>
                <label for="{{ form.upload_file.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.upload_file.label }}
                    {% if form.upload_file.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.upload_file }}
                {% if form.instance.file_name and not form.instance.file_data == None %}
                    <p class="text-xs text-gray-500 mt-1">Current file: <a href="{{ form.instance.get_file_download_url }}" class="text-blue-600 hover:underline">{{ form.instance.file_name }}</a></p>
                {% endif %}
                {% if form.upload_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.upload_file.errors|join:", " }}</p>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
            <div class="p-4 text-red-700 bg-red-100 rounded-lg" role="alert">
                <p class="font-bold">Error:</p>
                {{ form.non_field_errors }}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                _="on click remove .opacity-100 from #modal then wait 200ms then add .hidden to #modal and remove .flex from #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
            >
                {{ form.instance.pk|yesno:'Update,Create' }} News Notice
            </button>
        </div>
    </form>
</div>