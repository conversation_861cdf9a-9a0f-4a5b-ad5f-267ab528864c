{% load humanize %} {# Optional, for formatting numbers if needed #}

<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="offerletterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Id</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type Of</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Type</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Increment</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duty Hrs</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interviewed by</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Salary</th>
                <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for offer in offer_letters %}
            <tr x-data="{ selectedIncrement: '{{ offer.increment|default_if_none:0 }}' }"> {# Alpine.js to hold selected increment #}
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.formatted_sys_date }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ offer.offer_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.type_of_company }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.employee_type_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    <select x-model="selectedIncrement" class="block w-full py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        {% for inc_option in offer.get_increment_options %}
                            <option value="{{ inc_option }}">{{ inc_option }}</option>
                        {% endfor %}
                    </select>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.full_employee_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.designation_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.duty_hours_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.interviewed_by_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.contact_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ offer.salary|intcomma }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ offer.confirmation_status }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-center">
                    <a href="{% url 'hr_offer:offerletter_detail_redirect' pk=offer.offer_id %}?increment={{ offer.increment|default_if_none:0 }}"
                       class="text-blue-600 hover:text-blue-900 px-3 py-1 border border-blue-600 rounded-md text-xs">
                        Select
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="14" class="py-8 text-center text-gray-500 text-lg">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization must happen after the table is loaded into the DOM
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#offerletterTable')) {
            $('#offerletterTable').DataTable({
                "pageLength": 17, // Replicate ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "responsive": true, // Make table responsive
                "pagingType": "simple_numbers", // Basic pagination
                "info": true, // Show "Showing X to Y of Z entries"
                "searching": true, // Enable search box
                "ordering": true // Enable column sorting
            });
        }
    });

    // Re-initialize DataTables if HTMX swaps the table
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'offerletterTable-container') {
            $('#offerletterTable').DataTable({
                "pageLength": 17,
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "responsive": true,
                "pagingType": "simple_numbers",
                "info": true,
                "searching": true,
                "ordering": true
            });
        }
    });
</script>