<div class="overflow-x-auto">
    <table id="hrReportsTable" class="min-w-full bg-white border-collapse">
        <thead>
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Report Name</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Generated Date</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for report in reports %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ report.name }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ report.generated_at|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full {{ report.get_status_badge_color }}">
                        {{ report.status }}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition-colors duration-200"
                        hx-get="{% url 'hr_reports:report_edit' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        aria-label="Edit Report">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition-colors duration-200"
                        hx-get="{% url 'hr_reports:report_delete' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        aria-label="Delete Report">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">No HR reports found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization for the dynamically loaded table
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#hrReportsTable')) {
            $('#hrReportsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
        }
    });
</script>