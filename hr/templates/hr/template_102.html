{# tour_intimation/_tour_advance_form.html #}
{# This template is for the Add/Edit modal for Tour Advances #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Tour Advance</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        {{ form.Id }} {# Hidden ID for existing records #}
        {{ form.MId }} {# Hidden MId to link to parent TourIntimation #}

        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.employee_full_name.label }}
                </label>
                {{ form.employee_full_name }}
                <div id="advance-employee-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 relative"></div>
                {% if form.employee_full_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.Amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.Amount.label }}
                </label>
                {{ form.Amount }}
                {% if form.Amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.Amount.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.Remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.Remarks.label }}
                </label>
                {{ form.Remarks }}
                {% if form.Remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.Remarks.errors }}</p>{% endif %}
            </div>
        </div>

        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mb-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>