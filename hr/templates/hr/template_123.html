<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="bankLoanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EmpId</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Installment</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if formset.forms %}
                {{ formset.management_form }} {# Essential for formsets #}
                {% for form in formset %}
                <tr x-data="loanRow()" {% if form.errors %}class="has-error"{% endif %}>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ form.select_employee }}
                        {# Hidden employee fields #}
                        {{ form.employee_user_id }}
                        {{ form.employee_empid }}
                        {{ form.employee_name_display }}
                        {{ form.employee_bggroup_display }}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ form.initial.employee_empid }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-900">{{ form.initial.employee_name_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ form.initial.employee_bggroup_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.bank_name }}
                        {% if form.bank_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_name.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.branch }}
                        {% if form.branch.errors %}<p class="text-red-500 text-xs mt-1">{{ form.branch.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.amount }}
                        {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.installment }}
                        {% if form.installment.errors %}<p class="text-red-500 text-xs mt-1">{{ form.installment.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.from_date }}
                        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        {{ form.to_date }}
                        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="11" class="py-8 text-center text-gray-500">No employees found matching your criteria.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization (ensure jQuery is loaded in base.html)
    $(document).ready(function() {
        $('#bankLoanTable').DataTable({
            "paging": true,
            "searching": false, // Handled by Django/HTMX search form
            "ordering": false, // Ordering logic handled by backend query (UserID Desc)
            "info": true,
            "pageLength": {{ view.paginate_by }},
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 5, 6, 7, 8, 9, 10] } // Disable sorting for SN, Checkbox, and input fields
            ]
        });
    });
</script>