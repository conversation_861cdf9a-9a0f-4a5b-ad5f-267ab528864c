{# tour_intimation/_tour_advance_table.html #}
{# This template will be loaded by HTMX into the 'tourAdvanceTable-container' div #}
<table id="tourAdvanceTable" class="min-w-full bg-white divide-y divide-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in tour_advances %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.EmpId }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.Amount }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.Remarks }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'tour_intimation_advance_edit' tour_intimation_pk=obj.MId.Id pk=obj.Id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'tour_intimation_advance_delete' tour_intimation_pk=obj.MId.Id pk=obj.Id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists before re-initializing
        if ($.fn.DataTable.isDataTable('#tourAdvanceTable')) {
            $('#tourAdvanceTable').DataTable().destroy();
        }
        $('#tourAdvanceTable').DataTable({
            "pageLength": 9, /* As per ASP.NET GridView1.PageSize */
            "lengthMenu": [[9, 25, 50, -1], [9, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true
        });
    });
</script>