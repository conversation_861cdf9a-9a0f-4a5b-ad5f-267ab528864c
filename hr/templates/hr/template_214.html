<table id="newsNoticeTable" class="min-w-full bg-white border-collapse border border-gray-300 rounded-lg shadow-sm">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">From Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">To Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">File</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for notice in newsnotices %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.title }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.in_details|truncatechars:70 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ notice.to_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                {% if notice.has_file %}
                    <a href="{{ notice.get_file_download_url }}" class="text-blue-600 hover:underline">{{ notice.file_name }}</a>
                {% else %}
                    No File
                {% endif %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                             {% if notice.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {% if notice.is_active %}Active{% else %}Expired{% endif %}
                </span>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'newsnotice_edit' notice.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal">
                    Edit
                </button>
                <!-- Add Delete button if direct deletion of news item is desired -->
                <!--
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out ml-2"
                    hx-get="{% url 'newsnotice_delete' notice.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal">
                    Delete
                </button>
                -->
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 text-center text-gray-500 text-sm">No news notices found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after content is loaded via HTMX
    // Ensure jQuery is loaded before this script block runs
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.DataTable !== 'undefined') {
        $(document).ready(function() {
            if (!$.fn.DataTable.isDataTable('#newsNoticeTable')) { // Prevent re-initialization
                $('#newsNoticeTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "ordering": true,
                    "info": true,
                    "searching": true,
                    "paging": true
                });
            }
        });
    } else {
        console.warn("jQuery or DataTables not loaded. DataTables might not initialize.");
    }
</script>