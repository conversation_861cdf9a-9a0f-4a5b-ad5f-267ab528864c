<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Gate Pass</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" handles success with HX-Trigger header #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>