{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Employee Directory</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'employee_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal transition ease-out duration-300 transform scale-100"
        >
            <i class="fas fa-plus mr-2"></i> Add New Employee
        </button>
    </div>

    <div id="employeeTable-container"
         hx-trigger="load, refreshEmployeeList from:body"
         hx-get="{% url 'employee_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Loading spinner for initial load and HTMX refresh -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Employee Data...</p>
        </div>
    </div>

    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-80 hidden items-center justify-center z-50 transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then wait 0.3s then remove .flex from me"
         style="opacity: 0;">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 transform scale-95 transition-transform duration-300">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS and CSS are assumed to be in core/base.html -->
<script>
    // Alpine.js component initialization if needed, but HTMX handles most of it
    document.addEventListener('alpine:init', () => {
        // Example: If you had a search input that filters before HTMX request
        Alpine.data('employeeSearch', () => ({
            searchTerm: '',
            filterEmployees() {
                // This would trigger an HTMX request with search params
                // hx-get="{% url 'employee_table' %}?search={{ searchTerm }}"
                // This specific example isn't fully implemented in the current HTMX setup
                // as DataTables handles filtering on client side, but demonstrates Alpine usage.
            }
        }));
    });

    // Event listener for HTMX event to re-initialize DataTables after a swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'employeeTable-container') {
            $('#employeeTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing table if it exists before re-init
                "responsive": true,
                "autoWidth": false,
            });
        }
    });

    // Close modal on successful HTMX form submission (status 204)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('opacity-100');
                setTimeout(() => modal.classList.remove('flex'), 300);
            }
        }
    });
</script>
{% endblock %}