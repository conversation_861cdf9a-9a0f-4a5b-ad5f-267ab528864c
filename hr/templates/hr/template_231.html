{% load humanize %} {# Optional: for better number formatting if needed #}

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="mobileBillTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-3%">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-3%">CK</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">Emp ID</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-25%">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Mobile No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Limit Amt</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Bill Amt</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Taxes</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">Excess Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-8%">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in employees_data %}
            <tr x-data="{ showInputs: false, billAmount: '', taxId: '{{ row.default_tax_id|default:"" }}' }">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {% if not row.existing_bill %}
                    <input type="checkbox" 
                           class="form-checkbox h-4 w-4 text-blue-600"
                           x-model="showInputs"
                           name="checkbox_selected_{{ row.staff.user_id }}">
                    {% else %}
                    <span class="text-gray-400" title="Bill already exists"></span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ row.staff.emp_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ row.staff.employee_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ row.mobile_details.mobile_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.mobile_details.limit_amount|intcomma }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {% if row.existing_bill %}
                        {{ row.existing_bill.bill_amount|intcomma }}
                    {% else %}
                        <input type="number" step="0.01" min="0" 
                               name="bill_amount_{{ row.staff.user_id }}" 
                               x-model="billAmount"
                               x-bind:class="showInputs ? 'block' : 'hidden'" 
                               class="w-28 border border-gray-300 rounded-md px-2 py-1 text-right text-sm"
                               placeholder="0.00">
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {% if row.existing_bill %}
                        {{ row.existing_bill.tax.value|intcomma }}%
                    {% else %}
                        <select name="tax_id_{{ row.staff.user_id }}" 
                                x-model="taxId"
                                x-bind:class="showInputs ? 'block' : 'hidden'"
                                class="w-28 border border-gray-300 rounded-md px-2 py-1 text-sm">
                            {% for tax in row.taxes_options %}
                                <option value="{{ tax.id }}" {% if tax.id == row.default_tax_id %}selected{% endif %}>{{ tax.value|intcomma }}%</option>
                            {% endfor %}
                        </select>
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {% if row.existing_bill %}
                        <span class="text-red-600 font-bold">{{ row.excess_amount|intcomma }}</span>
                    {% else %}
                        <span x-show="showInputs" class="text-sm text-gray-500">Calculate</span>
                        <span x-show="!showInputs" class="text-sm text-gray-500">N/A</span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    {% if row.existing_bill %}
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md text-xs"
                            hx-get="{% url 'hr_mobile_bills:mobilebill_edit' pk=row.existing_bill.id %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-xs mt-1"
                            hx-get="{% url 'hr_mobile_bills:mobilebill_delete' pk=row.existing_bill.id %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    {% else %}
                        <span class="text-gray-400">N/A</span>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="10" class="py-4 px-4 bg-gray-50 text-center">
                    {% if not employees_data %}
                        <span class="text-gray-500">No employees found matching criteria.</span>
                    {% else %}
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                                hx-confirm="Are you sure you want to insert selected mobile bills?"
                                hx-target="#mobileBillTable-container" {# Target self to update table after submit #}
                                hx-swap="innerHTML show:top" {# Swap content and scroll to top of target #}
                                hx-indicator="#loadingIndicator"> {# Show a loading indicator #}
                            Insert Selected Bills
                        </button>
                        <div id="loadingIndicator" class="htmx-indicator ml-3 inline-block">
                            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                            <span class="ml-2">Processing...</span>
                        </div>
                    {% endif %}
                </td>
            </tr>
        </tfoot>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX swaps the table content
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#mobileBillTable')) {
            $('#mobileBillTable').DataTable().destroy();
        }
        $('#mobileBillTable').DataTable({
            "pageLength": 20, // Corresponds to ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "ordering": true,
            "searching": true,
            "paging": true,
            "info": true
        });
    });

    // Ensure the hidden month input is updated when the dropdown changes
    document.getElementById('id_bill_month').addEventListener('change', function() {
        document.getElementById('bill_month_selected_hidden').value = this.value;
    });
</script>