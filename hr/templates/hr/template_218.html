{% extends 'core/base.html' %}

{% block title %}Mobile Bill - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="bg-blue-600 text-white p-4 rounded-t-lg mb-4">
            <h1 class="text-xl font-bold">Mobile Bill - Edit</h1>
        </div>

        <!-- Month Selection Form -->
        <form id="month-form" hx-get="{% url 'mobile_bills_table_partial' %}" hx-target="#mobile-bill-table-container" hx-swap="innerHTML" hx-trigger="change from:#id_bill_month" class="mb-6 flex items-center space-x-4">
            {% csrf_token %}
            <div>
                <label for="{{ form.bill_month.id_for_label }}" class="block text-sm font-bold text-gray-700 mb-1">Month Of Bill</label>
                {{ form.bill_month }}
            </div>
            <!-- Hidden input to pass selected month on initial load/reload -->
            <input type="hidden" name="month" x-model="selectedMonth" />
            <div x-data="{ selectedMonth: {{ selected_month|default:'' }} }" x-init="document.getElementById('id_bill_month').value = selectedMonth;"></div>
        </form>

        <!-- Message Display Area (e.g., from messages framework) -->
        {% if messages %}
            <div id="messages" class="mb-4" hx-swap-oob="outerHTML:#messages">
                {% for message in messages %}
                    <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Mobile Bill Table Container -->
        <div id="mobile-bill-table-container"
             hx-trigger="load, refreshMobileBillTable from:body"
             hx-get="{% url 'mobile_bills_table_partial' %}?month={{ selected_month|default:'' }}"
             hx-swap="innerHTML">
            <!-- Initial loading spinner -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading Mobile Bill Data...</p>
            </div>
        </div>
        
    </div>
</div>

<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'mobile-bill-table-container') {
            // Reinitialize DataTables after new content is swapped in
            // Check if DataTables library is loaded and the table element exists
            if (typeof $.fn.DataTable === 'function' && $('#mobileBillTable').length) {
                $('#mobileBillTable').DataTable({
                    "pageLength": 20, // Initial page size same as ASP.NET GridView
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "destroy": true, // Destroy existing instance if any
                    "responsive": true
                });
            }
        }
    });

    // Listen for custom event to show messages (e.g., after formset submission)
    document.body.addEventListener('showMessages', function() {
        // HTMX usually handles out-of-band swaps for messages.
        // This is a fallback/extra trigger if you want to ensure message visibility or fade.
        console.log("Messages triggered!");
    });
</script>
{% endblock %}