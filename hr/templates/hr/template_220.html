{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Mobile Bill Report</h2>
        <div class="mt-4 md:mt-0 flex items-center space-x-4">
            <label for="month-select" class="block text-sm font-medium text-gray-700">Select Month:</label>
            <select id="month-select" name="month"
                    class="block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    hx-get="{% url 'mobile_bill_report_table' %}"
                    hx-target="#reportTable-container"
                    hx-trigger="change"
                    hx-indicator="#report-loading-indicator">
                {% for month in available_months %}
                <option value="{{ month }}" {% if month == selected_month %}selected{% endif %}>{{ month }}</option>
                {% empty %}
                <option value="">No months available</option>
                {% endfor %}
            </select>
        </div>
    </div>

    <div class="bg-gray-100 p-4 rounded-lg mb-6 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700 mb-2">Company Information:</h3>
        <p class="text-gray-600">{{ company_address|linebreaksbr }}</p>
    </div>
    
    <div id="report-loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-blue-500">Loading Report...</p>
    </div>

    <div id="reportTable-container"
         hx-trigger="load, reloadMobileBillReport from:body"
         hx-get="{% url 'mobile_bill_report_table' %}?month={{ selected_month }}"
         hx-swap="innerHTML">
        <!-- Report table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading initial report data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init if needed for modal or other UI state on this page
    document.addEventListener('alpine:init', () => {
        // No specific Alpine components needed for this page directly
    });
</script>
{% endblock %}