{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">News & Notices - Edit</h2>
        <!-- The original ASP.NET page was an 'Edit' page.
             If there was a list page leading to this, we could have an 'Add' button here.
             For this conversion, we'll assume the primary action is editing existing entries.
             For demo purposes, we will add an "Add" button here for completeness,
             which would lead to a NewsNoticeCreateView.
             For simplicity, here we just show the list for existing ones.
        -->
        <!-- Example Add <PERSON> (if a CreateView was implemented) -->
        <!--
        <button
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'newsnotice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal">
            Add New News Notice
        </button>
        -->
    </div>

    <!-- This div will be updated by HTMX -->
    <div id="newsnoticeTable-container"
         hx-trigger="load, refreshNewsNoticeList from:body"
         hx-get="{% url 'newsnotice_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial Loading State -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading News Notices...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit) and confirmations -->
    <div id="modal"
         class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 transition-opacity duration-300 opacity-0 pointer-events-none"
         x-data="{ showModal: false }"
         x-show="showModal"
         @refreshNewsNoticeList.window="showModal = false"
         @fileCleared.window="showModal = false"
         @click.self="showModal = false">
        <div id="modalContent"
             class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95"
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             @click.away="showModal = false">
            <!-- Content loaded by HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS and jQuery (ensure jQuery is loaded before DataTables) -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css" rel="stylesheet">

<!-- Alpine.js (if not already in base.html) -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            if (modal) {
                // Use Alpine.js to show the modal
                const alpineInstance = Alpine.$data(modal);
                if (alpineInstance) {
                    alpineInstance.showModal = true;
                }
            }
        }
    });

    // Handle closing modal on successful form submission (via HTMX trigger)
    document.body.addEventListener('refreshNewsNoticeList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            const alpineInstance = Alpine.$data(modal);
            if (alpineInstance) {
                alpineInstance.showModal = false;
            }
        }
    });

    // Handle closing modal after file clear (via HTMX trigger)
    document.body.addEventListener('fileCleared', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            const alpineInstance = Alpine.$data(modal);
            if (alpineInstance) {
                alpineInstance.showModal = false;
            }
        }
    });
</script>
{% endblock %}