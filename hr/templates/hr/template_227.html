<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="newsnoticeTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Financial Year</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">From Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">To Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for newsnotice in newsnotices %}
            <tr class="hover:bg-gray-50">
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.title }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.finyear.finyear }}</td> {# Accessing related object's field #}
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.from_date_display }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ newsnotice.to_date_display }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_edit' newsnotice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_delete' newsnotice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center text-red-600 font-bold">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables Initialization Script -->
<script>
    $(document).ready(function() {
        $('#newsnoticeTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "ordering": true, // Enable sorting
            "searching": true // Enable searching
        });
    });
</script>