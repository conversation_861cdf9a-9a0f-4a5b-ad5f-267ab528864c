<div class="overflow-x-auto">
    <table id="tourintimationTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TI No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place of Tour</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour Start Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour End Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in tour_intimations %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter0|add:page_obj.start_index }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.fin_year_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <button class="text-blue-600 hover:text-blue-800 font-semibold"
                        hx-get="{% url 'tourintimation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        {{ obj.ti_no }}
                    </button>
                </td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.emp_name_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.wo_no_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.bg_group_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.project_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.place_of_tour_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.tour_start_date_dmy }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.tour_end_date_dmy }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-sm mr-2 shadow-sm"
                        hx-get="{% url 'tourintimation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-sm shadow-sm"
                        hx-get="{% url 'tourintimation_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" class="py-4 text-center text-lg text-gray-500">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#tourintimationTable').DataTable({
            "pageLength": 10, // DataTables default, will override if pagination is server-side
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "paging": {{ page_obj.paginator.num_pages > 1|yesno:"true,false" }}, // Enable/disable DataTable's paging based on Django pagination
            "info": {{ page_obj.paginator.num_pages > 1|yesno:"true,false" }},
            "searching": true, // Client-side searching (Django handles server-side filtering)
            "order": [], // Disable initial ordering if Django orders
            "dom": '<"flex justify-between items-center mb-4"lfB>rt<"flex justify-between items-center mt-4"ip>',
            "buttons": ['copy', 'csv', 'excel', 'pdf', 'print']
        });
    });
</script>