{# tour_intimation/tour_intimation_detail.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    activeTab: 'main',
    isModalOpen: false,
    modalContent: ''
}">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Tour Intimation Edit - TI No: {{ object.TINo }}</h2>
    </div>

    <form method="post" class="space-y-6" hx-post="{{ request.path }}" hx-target="body" hx-swap="none" hx-indicator="#loading-spinner">
        {% csrf_token %}
        <input type="hidden" name="tour_intimation_pk" value="{{ object.Id }}"> {# Pass tour intimation PK for formset #}

        <!-- Main Form Details -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">General Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.employee_full_name.label }}
                    </label>
                    {{ form.employee_full_name }}
                    <div id="employee-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 relative"></div>
                    {% if form.employee_full_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>{% endif %}
                </div>
                
                <div id="wo-group-container">
                    <label class="block text-sm font-medium text-gray-700">
                        WO No / BG Group
                    </label>
                    <div class="flex items-center space-x-4 mb-2">
                        {% for radio in form.wo_no_group_selection %}
                            <label class="inline-flex items-center">
                                {{ radio.tag }}
                                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if wo_no_group_selection_initial == '0' %}
                        {{ form.WONo }}
                        {% if form.WONo.errors %}<p class="text-red-500 text-xs mt-1">{{ form.WONo.errors }}</p>{% endif %}
                    {% else %}
                        {{ form.BGGroupId }}
                        {% if form.BGGroupId.errors %}<p class="text-red-500 text-xs mt-1">{{ form.BGGroupId.errors }}</p>{% endif %}
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.ProjectName.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.ProjectName.label }}
                    </label>
                    {{ form.ProjectName }}
                    {% if form.ProjectName.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ProjectName.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.PlaceOfTourCountry.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Place of Tour
                    </label>
                    {{ form.PlaceOfTourCountry }}
                    {% if form.PlaceOfTourCountry.errors %}<p class="text-red-500 text-xs mt-1">{{ form.PlaceOfTourCountry.errors }}</p>{% endif %}
                    
                    {{ form.PlaceOfTourState }}
                    {% if form.PlaceOfTourState.errors %}<p class="text-red-500 text-xs mt-1">{{ form.PlaceOfTourState.errors }}</p>{% endif %}

                    {{ form.PlaceOfTourCity }}
                    {% if form.PlaceOfTourCity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.PlaceOfTourCity.errors }}</p>{% endif %}
                </div>
                
                <div class="flex space-x-4 items-center">
                    <div>
                        <label for="{{ form.tour_start_date_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_start_date_input.label }}
                        </label>
                        {{ form.tour_start_date_input }}
                        {% if form.tour_start_date_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_start_date_input.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.tour_start_time_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_start_time_input.label }}
                        </label>
                        {{ form.tour_start_time_input }}
                        {% if form.tour_start_time_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_start_time_input.errors }}</p>{% endif %}
                    </div>
                </div>

                <div class="flex space-x-4 items-center">
                    <div>
                        <label for="{{ form.tour_end_date_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_end_date_input.label }}
                        </label>
                        {{ form.tour_end_date_input }}
                        {% if form.tour_end_date_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_end_date_input.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.tour_end_time_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_end_time_input.label }}
                        </label>
                        {{ form.tour_end_time_input }}
                        {% if form.tour_end_time_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_end_time_input.errors }}</p>{% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.NoOfDays.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.NoOfDays.label }}
                    </label>
                    {{ form.NoOfDays }}
                    {% if form.NoOfDays.errors %}<p class="text-red-500 text-xs mt-1">{{ form.NoOfDays.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.NameAddressSerProvider.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.NameAddressSerProvider.label }}
                    </label>
                    {{ form.NameAddressSerProvider }}
                    {% if form.NameAddressSerProvider.errors %}<p class="text-red-500 text-xs mt-1">{{ form.NameAddressSerProvider.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.ContactPerson.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.ContactPerson.label }}
                    </label>
                    {{ form.ContactPerson }}
                    {% if form.ContactPerson.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ContactPerson.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.ContactNo.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.ContactNo.label }}
                    </label>
                    {{ form.ContactNo }}
                    {% if form.ContactNo.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ContactNo.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.Email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.Email.label }}
                    </label>
                    {{ form.Email }}
                    {% if form.Email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.Email.errors }}</p>{% endif %}
                </div>
            </div>
        </div>

        <!-- Tabs Section -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button type="button" @click="activeTab = 'advance_details'" :class="{'border-indigo-500 text-indigo-600': activeTab === 'advance_details', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advance_details'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Details
                    </button>
                    <button type="button" @click="activeTab = 'advance_trans_to'" :class="{'border-indigo-500 text-indigo-600': activeTab === 'advance_trans_to', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advance_trans_to'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Trans. To
                    </button>
                </nav>
            </div>

            <div x-show="activeTab === 'advance_details'" class="pt-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Advance Details</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{ expense_formset.management_form }}
                            {% for formset_form in expense_formset %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ forloop.counter }}
                                    {{ formset_form.Id }}
                                    {{ formset_form.ExpencessId }}
                                    {{ formset_form.MId }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formset_form.instance.ExpencessId.Terms }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formset_form.Amount }}
                                    {% if formset_form.Amount.errors %}<p class="text-red-500 text-xs mt-1">{{ formset_form.Amount.errors }}</p>{% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formset_form.Remarks }}
                                    {% if formset_form.Remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ formset_form.Remarks.errors }}</p>{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% if expense_formset.non_field_errors %}<p class="text-red-500 text-xs mt-2">{{ expense_formset.non_field_errors }}</p>{% endif %}
                </div>
            </div>

            <div x-show="activeTab === 'advance_trans_to'" class="pt-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Advance Trans. To</h4>
                <div class="flex justify-end mb-4">
                    <button
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        hx-get="{% url 'tour_intimation_advance_add' pk=object.Id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        @click="isModalOpen = true"
                    >
                        Add New Advance
                    </button>
                </div>
                <div id="tourAdvanceTable-container"
                     hx-trigger="load, refreshTourAdvanceList from:body"
                     hx-get="{% url 'tour_intimation_advance_table' pk=object.Id %}"
                     hx-swap="innerHTML">
                    <!-- DataTables for Tour Advances will be loaded here via HTMX -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit and Cancel Buttons -->
        <div class="mt-6 flex items-center justify-center space-x-4">
            <button 
                type="submit" 
                name="tour_expense_details_submit" {# Distinguish main form submission from others #}
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Update Tour Intimation
            </button>
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'tour_intimation_list' %}'"> {# Redirect to list view #}
                Cancel
            </button>
        </div>
    </form>

    <!-- Modal for Add/Edit/Delete Tour Advance -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center"
         x-show="isModalOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
         @click.self="isModalOpen = false"> {# Close modal when clicking outside content #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @click.away="isModalOpen = false" # This closes modal if you click outside of the modalContent
             _="on htmx:afterOnLoad remove .is-active from #modal"> {# Also remove active class if HTMX loads content into it #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
            // If the response is 204 (No Content) and it came from a modal, close the modal.
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Handle formset errors if the main form submission fails
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        if (evt.detail.xhr.status === 400 && evt.detail.requestHeaders['HX-Target'] === 'body') {
            // If the main form POST returns 400 (validation error), we need to handle it.
            // By default, HTMX will swap the whole body. We prevent this and just display errors.
            // This assumes the server renders the full form back with errors.
            console.error("Form submission failed with validation errors.");
            // The backend form_invalid renders the full page including context,
            // so we don't need to do a custom swap here.
            // The main thing is that the 'tourIntimationUpdated' trigger won't fire.
        }
    });
</script>
{% endblock %}