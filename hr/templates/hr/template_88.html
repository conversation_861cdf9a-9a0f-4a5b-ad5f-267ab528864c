<table id="gatepassDetailTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type Of</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type For</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visit Place</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
        </tr>
    </thead>
    <tbody>
        {% for detail in details %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    type="button"
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'gatepass_app:gatepass_detail_delete_confirm' pk=detail.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .hidden to #modal" {# Show modal #}
                    hx-indicator="#loading-spinner"> {# Show loading spinner for modal content #}
                    Delete
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.detail_employee.get_full_name|default:detail.gate_pass.employee.get_full_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.reason.reason_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.get_type_of_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.type_for_text }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.from_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.to_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.place }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.contact_person }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.contact_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.reason.reason_name }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="13" class="py-4 px-4 text-center text-lg text-maroon font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization is handled in the main list.html block extra_js
    // to ensure it re-initializes on HTMX swaps.
</script>