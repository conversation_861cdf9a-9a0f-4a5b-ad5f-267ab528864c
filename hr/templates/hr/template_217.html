{% if error_message %}
    <p class="text-red-600 text-center text-lg mt-4">{{ error_message }}</p>
{% elif mobile_bills %}
    <h3 class="text-xl font-semibold text-gray-700 mb-4">Mobile Bill Report for {{ selected_month_name }}</h3>
    <table id="mobileBillReportTable" class="min-w-full bg-white border-collapse border border-gray-300">
        <thead>
            <tr class="bg-gray-100">
                <th class="py-2 px-4 border border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Employee ID</th>
                <th class="py-2 px-4 border border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Bill Date</th>
                <th class="py-2 px-4 border border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Call Minutes</th>
                <th class="py-2 px-4 border border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Data Usage (MB)</th>
                <th class="py-2 px-4 border border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Details</th>
            </tr>
        </thead>
        <tbody>
            {% for bill in mobile_bills %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                <td class="py-2 px-4 border border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border border-gray-200">{{ bill.employee_id }}</td>
                <td class="py-2 px-4 border border-gray-200">{{ bill.bill_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border border-gray-200 text-right">{{ bill.amount|floatformat:2 }}</td>
                <td class="py-2 px-4 border border-gray-200 text-right">{{ bill.call_minutes|default:"N/A" }}</td>
                <td class="py-2 px-4 border border-gray-200 text-right">{{ bill.data_usage_mb|default:"N/A"|floatformat:2 }}</td>
                <td class="py-2 px-4 border border-gray-200">{{ bill.details|default:"-" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <script>
        $(document).ready(function() {
            // Initialize DataTables after content is loaded via HTMX
            $('#mobileBillReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true, // Enable search box
                "ordering": true,  // Enable column sorting
                "info": true,      // Show "Showing X of Y entries"
                "paging": true     // Enable pagination
            });
        });
    </script>
{% else %}
    <p class="text-gray-600 text-center">No mobile bill data available for the selected month.</p>
{% endif %}