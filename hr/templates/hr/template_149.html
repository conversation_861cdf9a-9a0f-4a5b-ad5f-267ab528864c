<form id="salary_details_form" hx-post="{% url 'hr_payroll:salary_proceed' employee.emp_id %}" hx-swap="outerHTML" hx-target="#salary_data_container">
    {% csrf_token %}
    <input type="hidden" name="fmonth" value="{{ month_id }}">

    <div class="grid grid-cols-12 gap-x-4 gap-y-2 text-sm text-gray-700">
        <!-- Monthly Info Row -->
        <div class="col-span-12 flex justify-between items-center text-sm mb-4">
            <p>Days of Month: <span class="font-bold text-gray-900">{{ month_info.days_in_month }}</span></p>
            <p>Sundays: <span class="font-bold text-gray-900">{{ month_info.sundays }}</span></p>
            <p>Holidays: <span class="font-bold text-gray-900">{{ month_info.holidays }}</span></p>
            <p>Working Days: <span class="font-bold text-gray-900">{{ month_info.working_days }}</span></p>
        </div>

        <!-- Section Headers -->
        <div class="col-span-4 text-center font-bold bg-gray-200 py-2 border-l border-r border-gray-400">Employee Details</div>
        <div class="col-span-4 text-center font-bold bg-gray-200 py-2 border-r border-gray-400">Attendance Details</div>
        <div class="col-span-4 text-center font-bold bg-gray-200 py-2 border-r border-gray-400">Miscellanies</div>

        <!-- Employee Image & Name -->
        <div class="col-span-4 flex items-start gap-4 border-l border-gray-300 row-span-4 p-2">
            <img src="{{ employee.get_photo_url }}" alt="Employee Photo" class="w-20 h-24 object-cover border border-gray-200" />
            <div class="flex-1">
                <p class="font-bold text-lg text-gray-900">{{ employee.title }}. {{ employee.employee_name }} [{{ employee.emp_id }}]</p>
            </div>
        </div>

        <!-- Attendance Inputs -->
        <div class="col-span-2 text-right p-2">Present:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.present }}</div>
        
        <!-- Misc. Details 1 -->
        <div class="col-span-2 text-right p-2">Bank Loan:</div>
        <div class="col-span-2 p-2 font-bold text-gray-900">{{ bank_loan_details.loan_amount }}</div>
        <div class="col-span-2 border-r border-gray-300 p-2">Inst. Paid: <span class="font-bold text-gray-900">{{ bank_loan_details.installment_paid_amount }}</span></div>

        <div class="col-span-2 text-right p-2">Swap Card No:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.swap_card.swap_card_no|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Absent:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.absent }}</div>

        <div class="col-span-2 text-right p-2">Installment:</div>
        <div class="col-span-4 border-r border-gray-300 p-2">{{ salary_form.installment }}</div>

        <div class="col-span-2 text-right p-2">Department:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.department|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Late In:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.late_in }}</div>

        <div class="col-span-2 text-right p-2">Mobile Bill:</div>
        <div class="col-span-2 p-2">Limit: <span class="font-bold text-gray-900">{{ mobile_bill_details.limit }}</span></div>
        <div class="col-span-2 p-2">Bill Amt: <span class="font-bold text-gray-900">{{ mobile_bill_details.bill_amount }}</span></div>
        <div class="col-span-2 border-r border-gray-300 p-2">Exe.Amt: {{ salary_form.mobile_exe_amt }}</div>

        <div class="col-span-4 border-r border-gray-300 p-2"></div> <!-- Empty cell under image -->
        <div class="col-span-2 text-right p-2">Half Day:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.half_day }}</div>

        <div class="col-span-2 text-right p-2">Addition:</div>
        <div class="col-span-4 border-r border-gray-300 p-2">{{ salary_form.addition }}</div>

        <!-- Designation & Sunday/C-off -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Designation:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.designation|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Sunday:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.sunday }}</div>

        <div class="col-span-2 text-right p-2">Remarks:</div>
        <div class="col-span-4 border-r border-gray-300 row-span-2 p-2 flex items-start">{{ salary_form.remarks1 }}</div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300">Grade:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.grade|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">C-off:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.coff }}</div>

        <div class="col-span-2 border-r border-gray-300"></div> <!-- Empty cell for Remarks -->

        <!-- Status & PL -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Status:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">
            {% if employee.offer %}
                {% if employee.offer.type_of == 1 %}SAPL{% elif employee.offer.type_of == 2 %}NEHA{% endif %} - {{ employee.offer.staff_type.description|default:"N/A" }}
            {% else %}N/A{% endif %}
        </div>

        <div class="col-span-2 text-right p-2">PL:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.pl }}</div>

        <div class="col-span-2 text-right p-2">Deduction:</div>
        <div class="col-span-4 border-r border-gray-300 p-2">{{ salary_form.deduction }}</div>

        <!-- Duty Hrs & OT Hrs -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Duty Hrs.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.offer.duty_hrs.hours|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Over Time Hrs.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.over_time_hrs }}</div>

        <div class="col-span-2 text-right p-2">Remarks:</div>
        <div class="col-span-4 border-r border-gray-300 row-span-2 p-2 flex items-start">{{ salary_form.remarks2 }}</div>

        <!-- Employee Over Time Hrs & Over Time Rate -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Over Time Hrs.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.offer.ot_hrs.hours|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Over Time Rate:</div>
        <div class="col-span-2 border-r border-gray-300 p-2 font-bold text-gray-900">{{ ot_rate }}</div>

        <!-- A/C No. & PF No. & PAN No. & Email & Mobile No. -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">A/C No.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.bank_account_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300">PF No.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.pf_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300">PAN No.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.pan_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>
        
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Email:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.company_email|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300 border-b border-gray-300">Mobile No.:</div>
        <div class="col-span-2 border-r border-gray-300 border-b border-gray-300 p-2">{{ employee.mobile_no.mobile_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300 border-b border-gray-300"></div>
    </div>

    <!-- Display form errors -->
    {% if salary_form.errors %}
        <div class="text-red-600 mt-4 p-2 bg-red-100 rounded">
            <p class="font-bold">Please correct the following errors:</p>
            <ul class="list-disc list-inside">
                {% for field in salary_form %}
                    {% if field.errors %}
                        <li>{{ field.label }}: {{ field.errors|join:", " }}</li>
                    {% endif %}
                {% endfor %}
                {% if salary_form.non_field_errors %}
                    <li>{{ salary_form.non_field_errors|join:", " }}</li>
                {% endif %}
            </ul>
        </div>
    {% endif %}

    <!-- Conditionally disable Proceed button -->
    {% if salary_exists or month_info.working_days == 0 %}
        <script>
            // Alpine.js to disable button
            document.querySelector('#salary_details_form button[type="submit"]').setAttribute('disabled', 'true');
            document.querySelector('#salary_details_form button[type="submit"]').classList.add('opacity-50', 'cursor-not-allowed');
            {% if salary_exists %}
                // Alpine.js to show message
                console.log('Salary already exists for this month.');
            {% elif month_info.working_days == 0 %}
                // Alpine.js to show message
                console.log('Working days is not found for selected month.');
            {% endif %}
        </script>
    {% else %}
        <script>
            document.querySelector('#salary_details_form button[type="submit"]').removeAttribute('disabled');
            document.querySelector('#salary_details_form button[type="submit"]').classList.remove('opacity-50', 'cursor-not-allowed');
        </script>
    {% endif %}
</form>