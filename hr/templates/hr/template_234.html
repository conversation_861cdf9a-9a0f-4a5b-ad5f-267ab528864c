{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 sm:mb-0">News Notices</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'newsnotice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
        >
            Add New News Notice
        </button>
    </div>

    <div
        id="newsnoticeTable-container"
        hx-trigger="load, refreshNewsNoticeList from:body"
        hx-get="{% url 'newsnotice_table' %}"
        hx-swap="innerHTML"
        class="bg-white shadow-lg rounded-lg overflow-hidden"
    >
        <!-- Initial loading state for HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading News Notices...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden items-center justify-center transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then wait 200ms then add .hidden to me and remove .flex from me">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 my-8 transform transition-all duration-300 scale-95 opacity-0"
             _="on modal shown set my @class to 'bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 my-8 transform transition-all duration-300 scale-100 opacity-100'">
            <!-- Content loaded via HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js is typically loaded in base.html -->
<script>
    // Example Alpine.js for general UI state if needed, but HTMX handles most of it
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });
</script>
{% endblock %}