<div class="bg-white shadow-xl rounded-lg overflow-hidden">
    {% if offerletters %}
    <table id="offermasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Offer Id</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type Of</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Duty Hrs</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Interviewed by</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Contact No</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Gross Salary</th>
                <th class="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in offerletters %}
            <tr class="hover:bg-gray-50 transition duration-150 ease-in-out">
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.formatted_date }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.offer_id }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.type_of_display }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.employee_type_description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.full_employee_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.designation_description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.duty_hours_description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.interviewed_by_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.contact_no }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.gross_salary_display }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.status }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-4 transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_edit' obj.offer_id %}" {# Use obj.offer_id for PK #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 mr-4 transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_increment' obj.offer_id %}" {# Placeholder for Increment #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Increment
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_delete' obj.offer_id %}" {# Use obj.offer_id for PK #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="text-center text-gray-500 py-8 text-lg">No data to display !</p>
    {% endif %}
</div>

<script>
    // Re-initialize DataTable every time this partial is loaded via HTMX
    // Destroy previous instance if it exists to prevent re-initialization issues
    if ($.fn.DataTable.isDataTable('#offermasterTable')) {
        $('#offermasterTable').DataTable().destroy();
    }
    $(document).ready(function() {
        $('#offermasterTable').DataTable({
            "pageLength": 20, // Matches original PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 12] } // SN and Actions columns not sortable
            ],
            "language": {
                "emptyTable": "No data to display !"
            }
        });
    });
</script>