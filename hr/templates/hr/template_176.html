{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Employee Salary Details</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'hr_salary:salarydetail_add' %}?EmpId={{ employee_id }}&FMonth={{ request.session.fmonth|default:1 }}" {# Pass emp/month for master linking #}
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Salary Entry
        </button>
    </div>
    
    <div id="salarydetailTable-container"
         hx-trigger="load, refreshSalaryDetailList from:body"
         hx-get="{% url 'hr_salary:salarydetail_table' %}?EmpId={{ employee_id }}" {# Pass EmpId for filtering #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Salary Data...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed for this modal,
        // as HTMX handles loading and CSS classes handle visibility.
        // Alpine.js could be used for more complex UI states if they arise.
    });
</script>
{% endblock %}