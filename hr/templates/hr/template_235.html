<div class="p-6">
    <table id="newsnoticeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in news_notices %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.title }}</td>
                <td class="py-4 px-6 whitespace-normal text-sm text-gray-900">{{ obj.in_details|truncatechars:100 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.from_date|date:"d-m-Y" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.to_date|date:"d-m-Y" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm">
                    {% if obj.file_name %}
                        <a href="{{ obj.get_file_download_url }}" class="text-blue-600 hover:underline">{{ obj.file_name }} ({{ obj.file_size|floatformat:0 }} bytes)</a>
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
                    >
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                        hx-get="{% url 'newsnotice_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No news notices found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    // Check if jQuery is loaded (usually by DataTables CDN in base.html)
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(function($) {
            // Destroy existing DataTable instance if it exists to avoid reinitialization errors
            if ($.fn.DataTable.isDataTable('#newsnoticeTable')) {
                $('#newsnoticeTable').DataTable().destroy();
            }
            $('#newsnoticeTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "responsive": true // Make table responsive
            });
        });
    } else {
        console.warn("jQuery or DataTables not loaded. DataTables initialization skipped.");
    }
</script>