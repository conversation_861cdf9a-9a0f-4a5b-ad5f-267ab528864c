<div class="p-6 bg-white rounded-xl shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 text-center">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8 text-center">Are you sure you want to delete the news notice: <strong>"{{ newsnotice.title }}"</strong>?</p>
    
    <form hx-post="{% url 'newsnotice_delete' newsnotice.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                _="on click remove .opacity-100 from #modal then wait 200ms then add .hidden to #modal and remove .flex from #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out"
            >
                Delete
            </button>
        </div>
    </form>
</div>