{% load humanize %} {# For date formatting, if needed, or customize in model #}
<table id="gatepassTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GP No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Authorize</th>
        </tr>
    </thead>
    <tbody>
        {% for gatepass in gatepasses %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ gatepass.financial_year.financial_year_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ gatepass.sys_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button 
                    type="button"
                    class="text-blue-600 hover:underline font-semibold"
                    hx-get="{% url 'gatepass_app:gatepass_details_table' pk=gatepass.pk %}"
                    hx-target="#gatepassDetailTable-container"
                    hx-swap="innerHTML"
                    hx-indicator="#loading-spinner-details">
                    {{ gatepass.gp_no }}
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ gatepass.employee.get_full_name|default:gatepass.session_employee.get_full_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {% if not gatepass.is_authorized %}
                    <input type="checkbox" name="selected_ids" value="{{ gatepass.pk }}" class="form-checkbox h-4 w-4 text-blue-600 rounded">
                {% else %}
                    <span class="text-gray-500 text-xs">{{ gatepass.authorize_date|date:"d-m-Y" }}</span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-lg text-maroon font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<div id="loading-spinner-details" class="htmx-indicator text-center py-4 hidden">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    <p class="mt-2 text-gray-600">Loading Details...</p>
</div>

<script>
    // DataTables initialization is handled in the main list.html block extra_js
    // to ensure it re-initializes on HTMX swaps.
</script>