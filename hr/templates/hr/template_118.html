{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Bank Loan Details</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'bankloan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Bank Loan
        </button>
    </div>

    <div class="mb-4">
        <form hx-get="{% url 'bankloan_list' %}" hx-target="#bankLoanTable-container" hx-swap="innerHTML">
            <label for="id_emp_id_filter" class="block text-sm font-medium text-gray-700">Filter by Employee ID:</label>
            <div class="flex space-x-2 mt-1">
                <input type="text" id="id_emp_id_filter" name="EmpId" value="{{ current_emp_id_filter }}"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       placeholder="e.g., E001">
                <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Filter
                </button>
                <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                        onclick="document.getElementById('id_emp_id_filter').value=''; this.form.submit();">
                    Clear Filter
                </button>
            </div>
        </form>
    </div>

    <div id="bankLoanTable-container"
         hx-trigger="load, refreshBankLoanList from:body"
         hx-get="{% url 'bankloan_table' %}?EmpId={{ current_emp_id_filter }}" {# Pass filter to partial #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading bank loan data...</p>
        </div>
    </div>
    
    <!-- Company Address Display (replicates Crystal Report Parameter) -->
    {% if company_address %}
    <div class="mt-6 p-4 bg-gray-100 rounded-md text-sm text-gray-700">
        <p><strong>Company Address:</strong> {{ company_address }}</p>
    </div>
    {% endif %}

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full h-auto overflow-y-auto max-h-[90vh]"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed (e.g., for complex UI state)
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            isOpen: false,
            openModal() { this.isOpen = true },
            closeModal() { this.isOpen = false }
        }));
    });
</script>
{% endblock %}