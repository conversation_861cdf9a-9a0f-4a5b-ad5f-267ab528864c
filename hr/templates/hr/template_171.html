<table id="salarySummaryRowTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month/Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Offered</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Calc.</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PF Emp</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PTax</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Deduct</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Net Pay</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in salary_summaries %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.emp_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.month_name }} {{ obj.year }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.department_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.designation_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.gross_total_offered|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.gross_total_calculated|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.pf_employee|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.p_tax|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.total_deductions|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right font-semibold">{{ obj.final_net_pay|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <!-- Example Action: View Offer Letter -->
                <a href="{{ obj.offer_letter_path }}" target="_blank"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                    View Offer
                </a>
                <!-- Add other report-specific actions if needed -->
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#salarySummaryRowTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "simple_numbers",
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": [12] } // Disable sorting for 'Actions' column
        ]
    });
});
</script>