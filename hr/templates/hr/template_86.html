{% extends 'core/base.html' %}
{% load tailwind_filters %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg shadow-md">
        <h2 class="text-xl font-bold">GatePass - Authorize</h2>
    </div>

    <div class="bg-white p-6 rounded-b-lg shadow-lg mb-6">
        <form id="searchForm" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ search_form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date:</label>
                    {{ search_form.from_date }}
                </div>
                <div>
                    <label for="{{ search_form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date:</label>
                    {{ search_form.to_date }}
                </div>
                <div>
                    <label for="{{ search_form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name:</label>
                    {{ search_form.employee_name }}
                    <datalist id="employee-datalist" hx-swap="outerHTML">
                        <!-- Suggestions will be loaded here by HTMX -->
                    </datalist>
                </div>
                <div class="flex items-end">
                    <button type="submit" 
                            class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            hx-get="{% url 'gatepass_app:authorize_gatepass_table' %}"
                            hx-target="#gatepassTable-container"
                            hx-swap="innerHTML"
                            hx-indicator="#loading-spinner">
                        Search
                    </button>
                    <button type="button" 
                            class="redbox ml-4 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                            id="authorizeBtn"
                            hx-post="{% url 'gatepass_app:authorize_gatepass_action_authorize' %}"
                            hx-include="#gatepassTable-container" {# Include all form elements in this container #}
                            hx-confirm="Are you sure you want to authorize the selected Gate Passes?"
                            hx-indicator="#loading-spinner">
                        Authorize
                    </button>
                </div>
            </div>
            <div id="employee-suggestions">
                <!-- Autocomplete suggestions for employee name will be loaded here -->
            </div>
        </form>
    </div>

    <div class="flex flex-col md:flex-row gap-6">
        <div class="w-full md:w-2/5">
            <div class="bg-white p-4 rounded-lg shadow-lg">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Gate Passes Pending Authorization</h3>
                <div id="gatepassTable-container"
                     hx-trigger="load, refreshGatePassList from:body"
                     hx-get="{% url 'gatepass_app:authorize_gatepass_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                     hx-swap="innerHTML">
                    <!-- Loading indicator for initial load and HTMX swaps -->
                    <div id="loading-spinner" class="htmx-indicator text-center py-4">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Gate Passes...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full md:w-3/5">
            <div class="bg-white p-4 rounded-lg shadow-lg">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Gate Pass Details</h3>
                <div id="gatepassDetailTable-container"
                     hx-trigger="refreshGatePassDetailTable from:body"
                     hx-swap="innerHTML">
                    <p class="text-gray-500 text-center py-10">Select a Gate Pass from the left to view details.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for confirm delete detail -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
     _="on click if event.target.id == 'modal' remove .hidden from #modal">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full relative">
        <!-- Close button for modal -->
        <button class="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
                _="on click remove .hidden from #modal">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
        <!-- Content will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    // Initialize date pickers using Flatpickr
    document.addEventListener('DOMContentLoaded', function() {
        flatpickr("#id_from_date", {
            dateFormat: "Y-m-d", // Django expects YYYY-MM-DD
            allowInput: true
        });
        flatpickr("#id_to_date", {
            dateFormat: "Y-m-d", // Django expects YYYY-MM-DD
            allowInput: true
        });
    });

    // Handle HTMX triggers for messages and modal
    document.body.addEventListener('showMessage', function(evt) {
        // Assume messages are already rendered by Django's message framework
        // (e.g., in base.html), or you could inject a Toast notification here.
        console.log('HTMX Trigger: showMessage');
    });

    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Reinitialize DataTables after HTMX swap, check for the table ID
        if (evt.detail.target.id === 'gatepassTable-container') {
            $('#gatepassTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before reinitializing
            });
        }
        if (evt.detail.target.id === 'gatepassDetailTable-container') {
            $('#gatepassDetailTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before reinitializing
            });
        }
    });

    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        // Show loading spinner for HTMX requests
        const indicator = evt.detail.elt.closest('.htmx-indicator-parent') ? evt.detail.elt.closest('.htmx-indicator-parent').querySelector('.htmx-indicator') : evt.detail.elt.querySelector('.htmx-indicator');
        if (indicator) {
            indicator.style.display = 'block';
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(evt) {
        // Hide loading spinner after HTMX requests
        const indicator = evt.detail.elt.closest('.htmx-indicator-parent') ? evt.detail.elt.closest('.htmx-indicator-parent').querySelector('.htmx-indicator') : evt.detail.elt.querySelector('.htmx-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    });

    // Alpine.js for modal state
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            open: false,
            show() { this.open = true },
            hide() { this.open = false },
        }));
    });
</script>
{% endblock %}