{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Offer Letters</h2>
        <a href="{% url 'hrmodules:offerletter_add' %}"
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
            Create New Offer
        </a>
    </div>
    
    <div id="offerLetterTableContainer"
         hx-trigger="load, refreshOfferLetterList from:body"
         hx-get="{% url 'hrmodules:offerletter_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Offer Letters...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed (not strictly used here, but good practice for frontend state)
    document.addEventListener('alpine:init', () => {
        Alpine.data('offerLetterList', () => ({
            // Any state for the list page
        }));
    });
</script>
{% endblock %}