{# tour_intimation/_state_dropdown_options.html #}
<select name="PlaceOfTourState" id="id_PlaceOfTourState" 
        class="box3"
        hx-post="{% url 'tour_intimation_get_cities' %}" 
        hx-target="#id_PlaceOfTourCity" 
        hx-swap="outerHTML">
    <option value="">Select State</option>
    {% for state in states %}
        <option value="{{ state.SId }}" {% if state.SId == selected_state_id %}selected{% endif %}>{{ state.StateName }}</option>
    {% endfor %}
</select>