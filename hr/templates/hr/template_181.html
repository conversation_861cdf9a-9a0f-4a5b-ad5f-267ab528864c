<!-- hr_payroll/salary_slip_list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Generate Employee Salary Slip</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
        <form hx-get="{% url 'salary_slip_report' '0' '0' %}" hx-target="#salarySlipReportContainer" hx-swap="outerHTML"
              _="on submit set @href to '/hr_payroll/salary_slip_report/' + #id_employee.value + '/' + #id_month.value + '/' end on submit navigate to @href">
            {% csrf_token %}
            
            <div class="space-y-4">
                <div class="mb-4">
                    <label for="{{ form.employee.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.employee.label }}
                    </label>
                    {{ form.employee }}
                    {% if form.employee.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.employee.errors }}</p>
                    {% endif %}
                </div>
                <div class="mb-4">
                    <label for="{{ form.month.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.month.label }}
                    </label>
                    {{ form.month }}
                    {% if form.month.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.month.errors }}</p>
                    {% endif %}
                </div>
                 <div class="mb-4">
                    <label for="{{ form.financial_year.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.financial_year.label }}
                    </label>
                    {{ form.financial_year }}
                    {% if form.financial_year.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.financial_year.errors }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="mt-6 flex items-center justify-end space-x-4">
                <button 
                    type="submit" 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Generate Salary Slip
                </button>
            </div>
        </form>
    </div>

    <div id="salarySlipReportContainer" class="mt-8">
        <!-- The generated salary slip report will be loaded here via full page navigation -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}