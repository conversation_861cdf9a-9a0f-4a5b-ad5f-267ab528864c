<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="officestaffTable" class="min-w-full bg-white text-sm">
        <thead>
            <tr>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Emp Id</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Emp Name</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Dept Name</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">BG Group</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in officestaff_list %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.empid }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.get_full_name }}</td> {# Assumes get_full_name method in model #}
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.department.description }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.bggroup.symbol }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.designation }}</td>
                <td class="py-3 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.finyearid.finyear }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-center whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-1"
                        hx-get="{% url 'officestaff_edit' obj.userid %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'officestaff_delete' obj.userid %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the table content
    $(document).ready(function() {
        $('#officestaffTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "pagingType": "full_numbers", // Enhanced pagination
            "responsive": true, // Make table responsive
            "searching": true, // Enable local searching (Django handles server-side filtering)
            "info": true, // Show table info
            "ordering": true // Enable ordering
        });
    });
</script>