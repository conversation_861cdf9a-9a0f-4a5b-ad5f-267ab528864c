{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Gate Passes</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'gatepass_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Gate Pass
        </button>
    </div>
    
    <div id="gatepassTable-container"
         hx-trigger="load, refreshGatePassList from:body"
         hx-get="{% url 'gatepass_table' %}" {# Reuse the partial table for general list #}
         hx-swap="innerHTML">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });

    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'gatepassTable-container') {
            // Re-initialize DataTables after HTMX swap
            if ($.fn.DataTable.isDataTable('#gatePassTable')) {
                $('#gatePassTable').DataTable().destroy();
            }
            $('#gatePassTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });
</script>
{% endblock %}