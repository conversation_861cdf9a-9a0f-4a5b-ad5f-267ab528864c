<div x-data="{ selectedEmployeeId: '{{ form.initial.employee_emp_id|default:"" }}', selectedEmployeeName: '{{ form.initial.employee_full_name|default:"" }}' }" class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ temp_advance|yesno:'Edit,Add' }} Advance Transfer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('advance-temp-modal').classList.add('hidden'); } else { console.log('Error, modal stays open'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.employee_full_name.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.employee_full_name }}
                <input type="hidden" name="{{ form.employee_emp_id.name }}" id="{{ form.employee_emp_id.id_for_label }}" x-model="selectedEmployeeId">
                <div id="employee-autocomplete-results-advance-temp" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto"></div>
                {% if form.employee_full_name.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.amount.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.amount }}
                {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
            </div>
            
            <div>
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remarks.label }}
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click add .hidden to #advance-temp-modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>