<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="mobileBillReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Limit Amt</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Am<PERSON></th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Value (%)</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Bill Amt</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in report_items %}
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.EmpId }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.EmployeeName }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.MobileNo }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.LimitAmt|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.BillAmt|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.TaxValue|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.NetBillAmount|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.CompId }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="px-6 py-4 text-center text-sm text-gray-500">No report data available for the selected month.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#mobileBillReportTable')) {
            $('#mobileBillReportTable').DataTable().destroy();
        }
        $('#mobileBillReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            "order": [[2, 'asc']] // Order by Employee Name by default
        });
    });
</script>