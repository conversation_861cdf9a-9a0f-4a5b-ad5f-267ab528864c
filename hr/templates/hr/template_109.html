{# hr_payroll_reports/templates/hr_payroll_reports/_monthly_salary_summary_table.html #}
<div class="overflow-x-auto shadow-md rounded-lg">
    <table id="monthlySalarySummaryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name [ID]</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month/Year</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PAN No</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Basic (P)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">DA (P)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">HRA (P)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Conveyance (P)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Education (P)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Medical (P)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross (P)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Basic (C)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">DA (C)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">HRA (C)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Conveyance (C)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Education (C)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Medical (C)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross (C)</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Att. Bonus</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ex Gratia</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Misc Add</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Add</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PF Emp</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PTax</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Loan Inst.</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile Bill</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Misc Deduct</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Deduct</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Net Pay</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank A/C No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Working Days</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present Days</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent Days</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sundays</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Holidays</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Late In</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">COFF</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Half Days</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LWP</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Letter</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in report_data %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.serial_number }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.employee_full_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.month_name }}/{{ row.year }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.department_symbol }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.designation_symbol }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.employment_status }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.grade_symbol }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.pf_number }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.pan_number }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.basic_salary|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.da_allowance|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.hra_allowance|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.conveyance_allowance|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.education_allowance|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.medical_allowance|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.initial_gross_total|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.calculated_basic|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.calculated_da|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.calculated_hra|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.calculated_conveyance|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.calculated_education|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.calculated_medical|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.calculated_gross_total|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.attendance_bonus|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.ex_gratia|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.miscellaneous_additions|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.total_additions|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.pf_employee|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.professional_tax|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.personal_loan_installment|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.mobile_bill|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.miscellaneous_deductions|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.total_deductions|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right font-semibold">{{ row.final_net_pay|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.bank_account_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.working_days_month|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.present_days|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.absent_days|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.sundays_in_month|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.holidays_in_month|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.late_ins|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.coff_days|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.half_days|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.paid_leaves|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.leave_without_pay|floatformat:0 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ row.report_generation_date }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">
                    {% if row.offer_letter_url and row.offer_letter_url != '#' %}
                        <a href="{{ row.offer_letter_url }}" target="_blank" class="text-blue-600 hover:underline">View</a>
                    {% else %}
                        N/A
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="45" class="py-4 px-4 text-center text-gray-500">No report data available for the selected criteria.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>