## ASP.NET to Django Conversion Script: Man Power Planning - Edit

This plan outlines the systematic migration of your ASP.NET Man Power Planning "Edit Details" module to a modern Django-based solution. Our approach prioritizes automation, emphasizes a "fat model, thin view" architecture, and leverages modern frontend technologies like HTMX and Alpine.js for highly interactive, single-page application experiences without complex JavaScript.

### Business Value of Django Modernization:

Migrating to Django offers significant benefits beyond just technical updates:

1.  **Reduced Maintenance Costs:** Django's clean structure, explicit design, and strong community support lead to more maintainable codebases, lowering long-term maintenance efforts and costs.
2.  **Improved Scalability:** Django is inherently scalable, making it easier to handle increased user loads and data volumes as your business grows without costly infrastructure overhauls.
3.  **Enhanced Performance:** By leveraging efficient Python code, a powerful ORM, and HTMX for targeted updates, the new application will deliver a faster and more responsive user experience.
4.  **Boosted Developer Productivity:** Django's "batteries-included" philosophy, along with reusable components and clear patterns, accelerates development cycles for new features and future enhancements.
5.  **Modern User Experience:** The combination of HTMX and Alpine.js provides a dynamic, responsive interface that feels like a desktop application without the complexity of traditional JavaScript frameworks, improving user satisfaction and operational efficiency.
6.  **Stronger Security Posture:** Django includes built-in protections against common web vulnerabilities (like SQL injection, XSS, CSRF), offering a more secure application by default.
7.  **Future-Proofing:** Moving away from legacy ASP.NET ensures your application remains compatible with modern web standards and benefits from ongoing advancements in the open-source ecosystem.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables. Based on the `SqlDataSource` and SQL commands, we identify the primary table for this module and its related entities.

*   **Main Table:** `tblPM_ManPowerPlanning`
*   **Related Tables:**
    *   `tblHR_OfficeStaff` (for Employee details like Name, Designation)
    *   `tblHR_Designation` (for Designation details)
    *   `BusinessGroup` (for Department details, used as `DeptName`)
*   **Audit/Amendment Table:** `tblPM_ManPowerPlanning_Amd` (for tracking changes)

**Inferred Columns for `tblPM_ManPowerPlanning`:**

| ASP.NET Control/Usage | Inferred Column Name | Inferred Data Type | Notes |
| :-------------------- | :------------------- | :----------------- | :---- |
| `Id` (Query String)   | `Id`                 | INT (PK)           | Unique identifier for the planning record. |
| `xrdr["EmpId"]`       | `EmpId`              | VARCHAR/INT        | Employee ID, foreign key to `tblHR_OfficeStaff`. |
| `xrdr["Date"]`        | `Date`               | DATE               | Date of the planning. |
| `TWONo`               | `WONo`               | VARCHAR            | Work Order Number. Can be null/empty. |
| `DrpDepartment`       | `Dept`               | INT                | Department ID, foreign key to `BusinessGroup`. |
| `Drptype`             | `Types`              | INT                | Type of planning (Present, Absent, etc.). |
| `TDescription`        | `Description`        | VARCHAR            | General description. |
| `TAHrs`               | `Hours`              | DECIMAL            | Planned hours. |
| `TActualDesc`         | `ActualDesc`         | VARCHAR            | Actual description. |
| `SysDate`, `SysTime`  | `SysDate`, `SysTime` | DATE, TIME         | System date and time of record creation/update. |
| `SessionId`           | `SessionId`          | VARCHAR            | User session ID. |
| `CompId`              | `CompId`             | INT                | Company ID. |
| `FinYearId`           | `FinYearId`          | INT                | Financial Year ID. |
| `AmendmentNo`         | `AmendmentNo`        | INT                | Tracks number of amendments. |

### Step 2: Identify Backend Functionality

The ASP.NET code primarily handles the *update* of an existing Man Power Planning record, along with an *amendment tracking* mechanism.

*   **Read (R):** On `Page_Load`, an existing `ManPowerPlanning` record is fetched using `Id` from the query string. Associated employee, designation, and department details are also read to populate display labels and dropdowns.
*   **Update (U):** The `BtnUpdate_Click` event performs the core update logic.
    *   It first reads the existing record.
    *   It inserts a copy of the *current* record into `tblPM_ManPowerPlanning_Amd` (the amendment table).
    *   It then updates the `tblPM_ManPowerPlanning` table with the new values from the form.
    *   It increments `AmendmentNo`.
    *   **Validation:**
        *   Required fields (`TWONo`, `TAHrs`).
        *   Numeric format for `TAHrs`.
        *   Business logic: `CheckValidWONo` (validates `WONo`), `BalanceHours_WONO`/`BalanceHours` (checks if planned hours exceed available balance hours for a grade/department/WO number).
*   **Navigation:** The `BtnCancel_Click` simply redirects the user to a list view.

### Step 3: Infer UI Components

The ASP.NET page is an "Edit Details" form.

*   **Labels:** `LName`, `LDesignation`, `Ldate`, `Label2` (Planed), `Label3` (Planed HRS), `Label4` (Actual Desc). These are read-only display fields.
*   **Input Controls:**
    *   `TWONo`: `TextBox` for Work Order Number.
    *   `DrpDepartment`: `DropDownList` for Department, bound to `BusinessGroup`.
    *   `TAHrs`: `TextBox` for Planned Hours (numeric input).
    *   `Drptype`: `DropDownList` for Plan Type (Present, Absent, Onsite, PL).
    *   `TDescription`: `TextBox` (multiline) for Description.
    *   `TActualDesc`: `TextBox` (multiline) for Actual Description.
*   **Action Buttons:** `BtnUpdate`, `BtnCancel`.
*   **Validation Controls:** `RequiredFieldValidator`, `RegularExpressionValidator`.

---

## Step 4: Generate Django Code

We will structure the Django application within a module named `manpower_planning`.

### 4.1 Models (`manpower_planning/models.py`)

We define models for the core `ManPowerPlanning` entity and its related lookups, setting `managed = False` as these tables are pre-existing. Business logic for balance hours and amendments is encapsulated within the `ManPowerPlanning` model.

```python
from django.db import models
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Define choices for Man Power Planning Type
class ManPowerPlanningType(models.IntegerChoices):
    PRESENT = 1, _('Present')
    ABSENT = 2, _('Absent')
    ONSITE = 3, _('Onsite')
    PL = 4, _('PL')

# Inferred related models from ASP.NET code
# These models assume existing tables/schemas that Django will connect to.
class Employee(models.Model):
    # Assuming EmpId from tblPM_ManPowerPlanning maps to a unique employee identifier
    # and EmployeeName and Designation are in tblHR_OfficeStaff
    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    designation = models.ForeignKey('Designation', on_delete=models.DO_NOTHING, db_column='Designation') # Assuming Designation ID

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class Designation(models.Model):
    # Based on tblHR_Designation
    symbol = models.CharField(db_column='Symbol', max_length=50)
    type = models.CharField(db_column='Type', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.symbol} - {self.type}"

class Department(models.Model):
    # Based on BusinessGroup table (used for Dept dropdown)
    name = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup' # As per SqlDept in ASPX
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.name

class ManPowerPlanning(models.Model):
    # Primary table: tblPM_ManPowerPlanning
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId')
    planning_date = models.DateField(db_column='Date')
    work_order_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.DO_NOTHING, db_column='Dept', blank=True, null=True)
    plan_type = models.IntegerField(db_column='Types', choices=ManPowerPlanningType.choices)
    description = models.TextField(db_column='Description', blank=True)
    planned_hours = models.DecimalField(db_column='Hours', max_digits=18, decimal_places=3) # Assuming decimal based on ASPX regex
    actual_description = models.TextField(db_column='ActualDesc', blank=True)

    # Audit/System fields from the ASP.NET code
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Assuming SessionId
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True) # Assuming FinYearId
    amendment_no = models.IntegerField(db_column='AmendmentNo', default=0)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning'
        verbose_name = 'Man Power Planning'
        verbose_name_plural = 'Man Power Plannings'

    def __str__(self):
        return f"Planning for {self.employee.employee_name} on {self.planning_date}"

    # Business Logic from ASP.NET's Cal_Used_Hours and validation
    # These methods are placeholders for actual complex logic that would query the DB
    # or external systems. For migration, we assume these exist or will be reimplemented.
    @staticmethod
    def _check_valid_wo_no(work_order_no, company_id, financial_year_id):
        """Simulates fun.CheckValidWONo - would involve DB lookup."""
        if work_order_no == 'NA': # 'NA' was a special value in ASP.NET
            return True
        # Placeholder: Implement actual validation against a WO table if exists
        # For now, return True if not empty or 'NA'
        return bool(work_order_no) # Basic check, replace with actual logic

    @staticmethod
    def _get_balance_hours(grade_id, department_id, company_id, financial_year_id, type_val):
        """Simulates CUH.BalanceHours - would involve complex DB queries."""
        # Placeholder: This is complex business logic. Return a high value for now.
        # This needs to be replaced with actual calculation based on GradeId, Department, etc.
        return 9999.0 # Placeholder: Replace with actual balance hours calculation

    @staticmethod
    def _get_balance_hours_wono(grade_id, work_order_no, company_id, financial_year_id, type_val):
        """Simulates CUH.BalanceHours_WONO - would involve complex DB queries."""
        # Placeholder: This is complex business logic. Return a high value for now.
        # This needs to be replaced with actual calculation based on GradeId, WONo, etc.
        return 9999.0 # Placeholder: Replace with actual balance hours calculation

    def validate_planned_hours(self, grade_id, company_id, financial_year_id):
        """
        Validates if planned_hours are within the allowed balance.
        Mimics the core validation logic in BtnUpdate_Click.
        """
        planned_hours_numeric = float(self.planned_hours)

        if planned_hours_numeric <= 0:
            raise ValidationError("Hours must be greater than Zero.")

        balance_hours = 0.0
        if self.work_order_no and self.work_order_no != 'NA':
            if not self._check_valid_wo_no(self.work_order_no, company_id, financial_year_id):
                raise ValidationError("Work Order No is invalid.")
            balance_hours = self._get_balance_hours_wono(
                grade_id, self.work_order_no, company_id, financial_year_id, 2
            )
        elif self.department:
            balance_hours = self._get_balance_hours(
                grade_id, self.department_id, company_id, financial_year_id, 2
            )
        else:
            # Handle cases where neither WO no nor Department is selected (if applicable)
            raise ValidationError("Either Work Order No or Department must be provided for balance calculation.")

        if planned_hours_numeric > balance_hours:
            raise ValidationError(f"Only {balance_hours:.2f} hours are remained.")

    @transaction.atomic
    def save_with_amendment(self, request_user, grade_id, company_id, financial_year_id):
        """
        Handles the update and amendment logic, mirroring BtnUpdate_Click.
        This method should be called instead of direct .save() for updates.
        """
        # Save current state to amendment table before updating the main record
        ManPowerPlanningAmendment.objects.create(
            manpower_planning_id=self.pk, # Link to the original record
            sys_date=self.sys_date,
            sys_time=self.sys_time,
            financial_year_id=self.financial_year_id,
            session_id=self.session_id,
            company_id=self.company_id,
            employee_id=self.employee_id,
            planning_date=self.planning_date,
            work_order_no=self.work_order_no,
            department_id=self.department_id,
            plan_type=self.plan_type,
            description=self.description,
            planned_hours=self.planned_hours,
            amendment_no=self.amendment_no,
            actual_description=self.actual_description,
        )

        # Update current record's audit fields and increment amendment number
        self.sys_date = timezone.localdate()
        self.sys_time = timezone.localtime().time()
        self.session_id = request_user.username if request_user else 'system' # Use Django user
        self.company_id = company_id
        self.financial_year_id = financial_year_id
        self.amendment_no += 1

        # Perform balance hour validation before saving
        self.validate_planned_hours(grade_id, company_id, financial_year_id)

        # Save the updated main record
        self.save(update_fields=[
            'sys_date', 'sys_time', 'session_id', 'work_order_no', 'department',
            'plan_type', 'description', 'planned_hours', 'amendment_no',
            'actual_description', 'company_id', 'financial_year_id' # Include fields updated from form
        ])


class ManPowerPlanningAmendment(models.Model):
    # This table stores previous versions of ManPowerPlanning records
    # Mimics tblPM_ManPowerPlanning_Amd
    manpower_planning = models.ForeignKey(ManPowerPlanning, on_delete=models.DO_NOTHING, db_column='MId')
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    company_id = models.IntegerField(db_column='CompId')
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId')
    planning_date = models.DateField(db_column='Date')
    work_order_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.DO_NOTHING, db_column='Dept', blank=True, null=True)
    plan_type = models.IntegerField(db_column='Types')
    description = models.TextField(db_column='Description', blank=True)
    planned_hours = models.DecimalField(db_column='Hours', max_digits=18, decimal_places=3)
    amendment_no = models.IntegerField(db_column='AmendmentNo')
    actual_description = models.TextField(db_column='ActualDesc', blank=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Amd'
        verbose_name = 'Man Power Planning Amendment'
        verbose_name_plural = 'Man Power Planning Amendments'

    def __str__(self):
        return f"Amendment {self.amendment_no} for {self.manpower_planning.employee.employee_name}"
```

### 4.2 Forms (`manpower_planning/forms.py`)

The Django form handles the input fields and validation, mapping directly to the `ManPowerPlanning` model.

```python
from django import forms
from .models import ManPowerPlanning, Department, ManPowerPlanningType
from django.core.validators import RegexValidator

class ManPowerPlanningForm(forms.ModelForm):
    # Regex validator from ASP.NET for Planned Hours
    planned_hours_validator = RegexValidator(
        r'^\d{1,15}(\.\d{0,3})?$',
        message="Planned Hours must be a number with up to 15 digits before the decimal and up to 3 digits after."
    )
    
    # Custom choices for Drptype from ASP.NET
    plan_type = forms.ChoiceField(
        choices=ManPowerPlanningType.choices,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = ManPowerPlanning
        fields = ['work_order_no', 'department', 'plan_type', 'planned_hours', 'description', 'actual_description']
        widgets = {
            'work_order_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-model': 'wonoInput'}),
            'department': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'planned_hours': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'actual_description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
        }
        labels = {
            'work_order_no': 'Work Order No',
            'department': 'Department',
            'plan_type': 'Type',
            'planned_hours': 'Planned Hours',
            'description': 'Description',
            'actual_description': 'Actual Description',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate department dropdown from the Department model
        self.fields['department'].queryset = Department.objects.all().order_by('name')
        self.fields['department'].required = False # Can be empty if work_order_no is present

        # Apply ASP.NET's RequiredFieldValidator to planned_hours
        self.fields['planned_hours'].required = True
        self.fields['planned_hours'].validators.append(self.planned_hours_validator)

        # Initial state based on ASP.NET's WONo enablement logic
        # If an instance exists and has a WONo, Department field should be disabled.
        # If no WONo, Department should be enabled.
        if self.instance and self.instance.pk:
            if self.instance.work_order_no and self.instance.work_order_no != 'NA':
                self.fields['department'].widget.attrs['x-bind:disabled'] = "wonoInput !== '' && wonoInput !== 'NA'"
                self.fields['work_order_no'].widget.attrs['x-bind:disabled'] = "wonoInput === 'NA'"
            else:
                self.fields['work_order_no'].initial = 'NA'
                self.fields['work_order_no'].widget.attrs['x-bind:disabled'] = "true"
                self.fields['department'].widget.attrs['x-bind:disabled'] = "wonoInput === 'NA'"

    def clean(self):
        cleaned_data = super().clean()
        work_order_no = cleaned_data.get('work_order_no')
        department = cleaned_data.get('department')

        # Mimic ASP.NET's conditional validation: either WONo or Department is required (but not both empty)
        # If WONo is not 'NA', then Department should be null. If WONo is 'NA', Department should be present.
        if work_order_no and work_order_no != 'NA':
            if department:
                self.add_error('department', 'Department must be empty if Work Order No is provided.')
        elif not department: # If WONo is 'NA' or empty, Department is required.
            self.add_error('department', 'Department is required if Work Order No is not provided.')
            
        return cleaned_data
```

### 4.3 Views (`manpower_planning/views.py`)

Views are kept thin, delegating complex logic to the model. An additional `TablePartialView` is created for HTMX to fetch the DataTables content dynamically.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django.contrib.auth.mixins import LoginRequiredMixin # Recommended for production apps
from django.core.exceptions import ValidationError

from .models import ManPowerPlanning, Employee, Designation, Department
from .forms import ManPowerPlanningForm

# Helper to get session/company/financial year data (replace with actual logic)
# In a real app, this would likely come from user's profile, session, or global settings.
def get_user_context(request):
    # Placeholder values derived from ASP.NET code
    # Replace with actual dynamic retrieval from user profiles/session
    user_context = {
        'company_id': request.session.get('compid', 1), # Default to 1 if not in session
        'financial_year_id': request.session.get('finyear', 1), # Default to 1
        'grade_id': request.GET.get('GId', 1), # From QueryString, default to 1
    }
    return user_context

class ManPowerPlanningListView(LoginRequiredMixin, ListView):
    model = ManPowerPlanning
    template_name = 'manpower_planning/manpower_planning_list.html'
    context_object_name = 'manpower_plannings'

    def get_queryset(self):
        # In a real app, you might filter by company, financial year, etc.
        # This page was originally an "Edit Details" page, so the list view is an addition for navigation.
        return ManPowerPlanning.objects.select_related('employee', 'department').all().order_by('-planning_date', 'employee__employee_name')

class ManPowerPlanningTablePartialView(LoginRequiredMixin, ListView):
    model = ManPowerPlanning
    template_name = 'manpower_planning/_manpower_planning_table.html'
    context_object_name = 'manpower_plannings'

    def get_queryset(self):
        # Filter logic similar to ManPowerPlanningListView
        return ManPowerPlanning.objects.select_related('employee', 'department').all().order_by('-planning_date', 'employee__employee_name')

class ManPowerPlanningCreateView(LoginRequiredMixin, CreateView):
    model = ManPowerPlanning
    form_class = ManPowerPlanningForm
    template_name = 'manpower_planning/_manpower_planning_form.html'
    success_url = reverse_lazy('manpower_planning_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['employee_name'] = 'New Employee' # Placeholder for create, typically selected via dropdown
        context['designation'] = 'N/A'
        context['planning_date'] = timezone.localdate().strftime('%d-%m-%Y')
        return context

    def form_valid(self, form):
        # Employee and Planning Date need to be set; they are not in the form for an *edit* page.
        # For a create view, these would be part of the form or selected/derived.
        # Assuming for create, an employee and date would be chosen/assigned.
        # For demonstration, assign a dummy employee and current date.
        try:
            employee = Employee.objects.first() # Replace with actual employee selection logic
            if not employee:
                raise ValueError("No employees found to assign.")
            form.instance.employee = employee
            form.instance.planning_date = timezone.localdate()

            user_context = get_user_context(self.request)
            form.instance.company_id = user_context['company_id']
            form.instance.financial_year_id = user_context['financial_year_id']
            form.instance.session_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous'

            # Save the new record (no amendment for initial creation)
            response = super().form_valid(form)
            messages.success(self.request, 'Man Power Planning record added successfully.')
            
            if self.request.headers.get('HX-Request'):
                # For HTMX, return a 204 No Content and trigger a refresh
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshManPowerPlanningList'
                    }
                )
            return response
        except ValidationError as e:
            form.add_error(None, e.message)
            return self.form_invalid(form)
        except Exception as e:
            messages.error(self.request, f"Error creating record: {e}")
            return self.form_invalid(form)


class ManPowerPlanningUpdateView(LoginRequiredMixin, UpdateView):
    model = ManPowerPlanning
    form_class = ManPowerPlanningForm
    template_name = 'manpower_planning/_manpower_planning_form.html'
    success_url = reverse_lazy('manpower_planning_list')

    def get_object(self, queryset=None):
        # ASP.NET used Request.QueryString["Id"] and ["GId"]
        # We will use 'pk' from URL for the ManPowerPlanning ID
        # and 'grade_id' from query string for the grade context.
        obj = get_object_or_404(ManPowerPlanning, pk=self.kwargs['pk'])
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate display-only labels based on the retrieved instance
        context['employee_name'] = self.object.employee.employee_name if self.object.employee else 'N/A'
        context['designation'] = self.object.employee.designation.symbol if self.object.employee and self.object.employee.designation else 'N/A'
        context['planning_date'] = self.object.planning_date.strftime('%d-%m-%Y') if self.object.planning_date else 'N/A'
        return context

    def form_valid(self, form):
        user_context = get_user_context(self.request)
        grade_id = user_context['grade_id'] # Retrieved from query string / context

        try:
            # The save_with_amendment method handles all business logic and saving
            form.instance.save_with_amendment(
                request_user=self.request.user,
                grade_id=grade_id,
                company_id=user_context['company_id'],
                financial_year_id=user_context['financial_year_id']
            )
            messages.success(self.request, 'Man Power Planning record updated successfully.')

            if self.request.headers.get('HX-Request'):
                # For HTMX, return a 204 No Content and trigger a refresh
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshManPowerPlanningList'
                    }
                )
            return HttpResponseRedirect(self.get_success_url())
        except ValidationError as e:
            form.add_error(None, e.message)
            return self.form_invalid(form)
        except Exception as e:
            # Generic error handling
            messages.error(self.request, f"An unexpected error occurred: {e}")
            return self.form_invalid(form)


class ManPowerPlanningDeleteView(LoginRequiredMixin, DeleteView):
    model = ManPowerPlanning
    template_name = 'manpower_planning/_manpower_planning_confirm_delete.html'
    success_url = reverse_lazy('manpower_planning_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Man Power Planning record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManPowerPlanningList'
                }
            )
        return response
```

### 4.4 Templates

The templates are structured for reusability and HTMX-driven interactions.

#### `manpower_planning/templates/manpower_planning/manpower_planning_list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Man Power Plannings</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'manpower_planning_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent">
            Add New Man Power Planning
        </button>
    </div>
    
    <div id="manpower_planningTable-container"
         hx-trigger="load, refreshManPowerPlanningList from:body"
         hx-get="{% url 'manpower_planning_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Man Power Planning data...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) -->
    <div id="modal" class="fixed inset-0 z-50 hidden items-center justify-center"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100">
        
        <div id="modalOverlay" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300 ease-in-out"
             x-on:click="showModal = false"></div>
        
        <div id="modalContent" class="bg-white rounded-lg shadow-xl transform transition-all sm:max-w-2xl sm:w-full p-6"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup for modal (simplified using _ for HTMX)
    // The `hx-get` on buttons will trigger Alpine's state via _ logic
    // This script block ensures Alpine is ready for any dynamic content it manages.
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalState', () => ({
            open: false,
            init() {
                this.$watch('open', value => {
                    if (value) {
                        document.body.classList.add('overflow-hidden');
                    } else {
                        document.body.classList.remove('overflow-hidden');
                    }
                });
            }
        }));
    });
</script>
{% endblock %}
```

#### `manpower_planning/templates/manpower_planning/_manpower_planning_table.html` (Partial)

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="manpower_planningTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Planned Hrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in manpower_plannings %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.employee.employee_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.employee.designation.symbol }} - {{ obj.employee.designation.type }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.planning_date|date:"d-m-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.work_order_no|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.department.name|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_plan_type_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.planned_hours }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'manpower_planning_edit' pk=obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'manpower_planning_delete' pk=obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 px-4 text-center text-sm text-gray-500">No Man Power Planning records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only after the table is in the DOM
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#manpower_planningTable')) {
            $('#manpower_planningTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true,
                "ordering": true,
                "paging": true,
                "info": true
            });
        }
    });
</script>
```

#### `manpower_planning/templates/manpower_planning/_manpower_planning_form.html` (Partial)

```html
<div class="p-6" x-data="{ wonoInput: '{{ form.instance.work_order_no|default:'' }}' }">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Man Power Planning</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('flex'); document.getElementById('modalOverlay').classList.remove('opacity-100'); document.getElementById('modalContent').classList.remove('scale-100'); }"
          class="space-y-6">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <!-- Display fields (read-only, from ASP.NET Labels) -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Name</label>
                <p class="mt-1 text-sm text-gray-900">{{ employee_name }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Work Order No</label>
                {{ form.work_order_no }}
                {% if form.work_order_no.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.work_order_no.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Designation</label>
                <p class="mt-1 text-sm text-gray-900">{{ designation }}</p>
            </div>
            <div>
                <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">Department</label>
                {{ form.department }}
                {% if form.department.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.department.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ planning_date }}</p>
            </div>
            <div>
                <label for="{{ form.planned_hours.id_for_label }}" class="block text-sm font-medium text-gray-700">Planned Hours</label>
                {{ form.planned_hours }}
                {% if form.planned_hours.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.planned_hours.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.plan_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
                {{ form.plan_type }}
                {% if form.plan_type.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.plan_type.errors }}</p>
                {% endif %}
            </div>
            <div class="col-span-full">
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700">Planned Description</label>
                {{ form.description }}
                {% if form.description.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.description.errors }}</p>
                {% endif %}
            </div>
            <div class="col-span-full">
                <label for="{{ form.actual_description.id_for_label }}" class="block text-sm font-medium text-gray-700">Actual Description</label>
                {{ form.actual_description }}
                {% if form.actual_description.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.actual_description.errors }}</p>
                {% endif %}
            </div>
        </div>

        {% if form.non_field_errors %}
        <div class="text-red-600 text-sm mt-4">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `manpower_planning/templates/manpower_planning/_manpower_planning_confirm_delete.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Man Power Planning record for <strong>{{ manpower_planning.employee.employee_name }} on {{ manpower_planning.planning_date|date:"d-m-Y" }}</strong>?</p>
    
    <form hx-post="{% url 'manpower_planning_delete' pk=manpower_planning.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('flex'); document.getElementById('modalOverlay').classList.remove('opacity-100'); document.getElementById('modalContent').classList.remove('scale-100'); }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`manpower_planning/urls.py`)

URL patterns to map routes to our Django views.

```python
from django.urls import path
from .views import (
    ManPowerPlanningListView,
    ManPowerPlanningTablePartialView,
    ManPowerPlanningCreateView,
    ManPowerPlanningUpdateView,
    ManPowerPlanningDeleteView,
)

urlpatterns = [
    path('manpower-planning/', ManPowerPlanningListView.as_view(), name='manpower_planning_list'),
    path('manpower-planning/add/', ManPowerPlanningCreateView.as_view(), name='manpower_planning_add'),
    path('manpower-planning/edit/<int:pk>/', ManPowerPlanningUpdateView.as_view(), name='manpower_planning_edit'),
    path('manpower-planning/delete/<int:pk>/', ManPowerPlanningDeleteView.as_view(), name='manpower_planning_delete'),
    path('manpower-planning/table/', ManPowerPlanningTablePartialView.as_view(), name='manpower_planning_table'),
]
```

### 4.6 Tests (`manpower_planning/tests.py`)

Comprehensive tests for models and views ensure reliability and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch

from .models import ManPowerPlanning, Employee, Designation, Department, ManPowerPlanningType
from django.core.exceptions import ValidationError

class ManPowerPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.designation = Designation.objects.create(id=1, symbol='ENG', type='Engineer')
        cls.employee = Employee.objects.create(emp_id='E001', employee_name='John Doe', designation=cls.designation)
        cls.department = Department.objects.create(id=101, name='IT')

        # Create test data for ManPowerPlanning
        cls.manpower_planning_with_wo = ManPowerPlanning.objects.create(
            id=1,
            employee=cls.employee,
            planning_date=timezone.localdate(),
            work_order_no='WO123',
            department=None, # Department is null if WO is present
            plan_type=ManPowerPlanningType.PRESENT,
            description='Project Alpha Task',
            planned_hours=8.000,
            actual_description='Completed on time',
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id='testuser',
            company_id=1,
            financial_year_id=1,
            amendment_no=0
        )
        cls.manpower_planning_with_dept = ManPowerPlanning.objects.create(
            id=2,
            employee=cls.employee,
            planning_date=timezone.localdate(),
            work_order_no='NA', # 'NA' indicates department is used
            department=cls.department,
            plan_type=ManPowerPlanningType.ABSENT,
            description='Training Session',
            planned_hours=4.500,
            actual_description='Attended',
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id='testuser',
            company_id=1,
            financial_year_id=1,
            amendment_no=0
        )

    def test_manpower_planning_creation(self):
        obj = ManPowerPlanning.objects.get(id=1)
        self.assertEqual(obj.employee.employee_name, 'John Doe')
        self.assertEqual(obj.work_order_no, 'WO123')
        self.assertEqual(obj.planned_hours, 8.000)
        self.assertEqual(obj.amendment_no, 0)

    def test_get_plan_type_display(self):
        obj = ManPowerPlanning.objects.get(id=1)
        self.assertEqual(obj.get_plan_type_display(), 'Present')

    @patch('manpower_planning.models.ManPowerPlanning._get_balance_hours_wono', return_value=10.0)
    @patch('manpower_planning.models.ManPowerPlanning._check_valid_wo_no', return_value=True)
    def test_validate_planned_hours_wo_valid(self, mock_check_wo, mock_balance_wo):
        obj = ManPowerPlanning.objects.get(id=1)
        obj.planned_hours = 5.0
        try:
            obj.validate_planned_hours(grade_id=1, company_id=1, financial_year_id=1)
        except ValidationError:
            self.fail("ValidationError raised unexpectedly!")
        mock_check_wo.assert_called_once_with('WO123', 1, 1)
        mock_balance_wo.assert_called_once_with(1, 'WO123', 1, 1, 2)

    @patch('manpower_planning.models.ManPowerPlanning._get_balance_hours_wono', return_value=5.0)
    @patch('manpower_planning.models.ManPowerPlanning._check_valid_wo_no', return_value=True)
    def test_validate_planned_hours_wo_exceeds_balance(self, mock_check_wo, mock_balance_wo):
        obj = ManPowerPlanning.objects.get(id=1)
        obj.planned_hours = 8.0
        with self.assertRaisesMessage(ValidationError, "Only 5.00 hours are remained."):
            obj.validate_planned_hours(grade_id=1, company_id=1, financial_year_id=1)

    @patch('manpower_planning.models.ManPowerPlanning._get_balance_hours', return_value=12.0)
    def test_validate_planned_hours_dept_valid(self, mock_balance_dept):
        obj = ManPowerPlanning.objects.get(id=2)
        obj.planned_hours = 10.0
        try:
            obj.validate_planned_hours(grade_id=1, company_id=1, financial_year_id=1)
        except ValidationError:
            self.fail("ValidationError raised unexpectedly!")
        mock_balance_dept.assert_called_once_with(1, obj.department_id, 1, 1, 2)

    def test_validate_planned_hours_zero_hours(self):
        obj = ManPowerPlanning.objects.get(id=1)
        obj.planned_hours = 0.0
        with self.assertRaisesMessage(ValidationError, "Hours must be greater than Zero."):
            obj.validate_planned_hours(grade_id=1, company_id=1, financial_year_id=1)

    @patch('manpower_planning.models.ManPowerPlanning._check_valid_wo_no', return_value=False)
    def test_validate_planned_hours_invalid_wo(self, mock_check_wo):
        obj = ManPowerPlanning.objects.get(id=1)
        obj.work_order_no = 'INVALID_WO'
        with self.assertRaisesMessage(ValidationError, "Work Order No is invalid."):
            obj.validate_planned_hours(grade_id=1, company_id=1, financial_year_id=1)

    @patch('manpower_planning.models.ManPowerPlanning.validate_planned_hours')
    def test_save_with_amendment(self, mock_validate_hours):
        from django.contrib.auth.models import User
        user = User.objects.create_user(username='testuser', password='password')
        
        obj = ManPowerPlanning.objects.get(id=1)
        original_amendment_no = obj.amendment_no
        original_description = obj.description
        obj.description = 'Updated description.'
        
        # Mock time-related functions to ensure consistent test results
        with patch('django.utils.timezone.localdate', return_value=timezone.datetime(2023, 1, 1).date()), \
             patch('django.utils.timezone.localtime', return_value=timezone.datetime(2023, 1, 1, 12, 0, 0)):
            
            obj.save_with_amendment(request_user=user, grade_id=1, company_id=1, financial_year_id=1)

        # Check if amendment record was created
        self.assertEqual(obj.amendment_no, original_amendment_no + 1)
        from .models import ManPowerPlanningAmendment
        amendment = ManPowerPlanningAmendment.objects.get(manpower_planning=obj)
        self.assertEqual(amendment.description, original_description)
        self.assertEqual(amendment.amendment_no, original_amendment_no)
        self.assertEqual(amendment.manpower_planning_id, obj.id)
        
        # Check if main object was updated
        obj.refresh_from_db()
        self.assertEqual(obj.description, 'Updated description.')
        self.assertEqual(obj.amendment_no, original_amendment_no + 1)
        
        mock_validate_hours.assert_called_once_with(1, 1, 1)


class ManPowerPlanningViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.designation = Designation.objects.create(id=1, symbol='ENG', type='Engineer')
        cls.employee = Employee.objects.create(emp_id='E001', employee_name='John Doe', designation=cls.designation)
        cls.department = Department.objects.create(id=101, name='IT')
        cls.manpower_planning = ManPowerPlanning.objects.create(
            id=1,
            employee=cls.employee,
            planning_date=timezone.localdate(),
            work_order_no='WO123',
            department=None,
            plan_type=ManPowerPlanningType.PRESENT,
            description='Initial description',
            planned_hours=8.000,
            actual_description='Initial actual description',
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id='testuser_session',
            company_id=1,
            financial_year_id=1,
            amendment_no=0
        )
        
        # Create a test user for LoginRequiredMixin
        from django.contrib.auth.models import User
        cls.user = User.objects.create_user(username='testuser', password='password')

    def setUp(self):
        # Set up data for each test method
        self.client = Client()
        self.client.login(username='testuser', password='password') # Log in the test user

    def test_list_view_get(self):
        response = self.client.get(reverse('manpower_planning_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/manpower_planning_list.html')
        self.assertTrue('manpower_plannings' in response.context)
        self.assertContains(response, 'John Doe')

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('manpower_planning_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_manpower_planning_table.html')
        self.assertTrue('manpower_plannings' in response.context)
        self.assertContains(response, 'John Doe')
        # Check for HTMX header presence
        self.assertIn('HX-Trigger', response.headers)

    def test_create_view_get(self):
        response = self.client.get(reverse('manpower_planning_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_manpower_planning_form.html')
        self.assertTrue('form' in response.context)

    @patch('manpower_planning.models.ManPowerPlanning._get_balance_hours_wono', return_value=100.0) # Mock balance for validation
    @patch('manpower_planning.models.ManPowerPlanning._check_valid_wo_no', return_value=True) # Mock WO validation
    def test_create_view_post_success(self, mock_check_wo, mock_get_balance):
        new_employee = Employee.objects.create(emp_id='E002', employee_name='Jane Smith', designation=self.designation)
        data = {
            'work_order_no': 'WO456',
            'department': '', # Empty as WO is present
            'plan_type': ManPowerPlanningType.ONSITE,
            'planned_hours': 7.5,
            'description': 'New project task',
            'actual_description': 'Started new task',
        }
        # Simulate HTMX request
        response = self.client.post(reverse('manpower_planning_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshManPowerPlanningList', response.headers['HX-Trigger'])
        self.assertTrue(ManPowerPlanning.objects.filter(work_order_no='WO456').exists())
        self.assertEqual(ManPowerPlanning.objects.get(work_order_no='WO456').amendment_no, 0) # No amendment for create

    @patch('manpower_planning.models.ManPowerPlanning._get_balance_hours_wono', return_value=5.0)
    @patch('manpower_planning.models.ManPowerPlanning._check_valid_wo_no', return_value=True)
    def test_create_view_post_validation_failure(self, mock_check_wo, mock_get_balance):
        new_employee = Employee.objects.create(emp_id='E002', employee_name='Jane Smith', designation=self.designation)
        data = {
            'work_order_no': 'WO456',
            'department': '',
            'plan_type': ManPowerPlanningType.ONSITE,
            'planned_hours': 10.0, # Exceeds mock balance
            'description': 'New project task',
            'actual_description': 'Started new task',
        }
        response = self.client.post(reverse('manpower_planning_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, "Only 5.00 hours are remained.")
        self.assertFalse(ManPowerPlanning.objects.filter(work_order_no='WO456', planned_hours=10.0).exists())


    def test_update_view_get(self):
        obj = self.manpower_planning
        response = self.client.get(reverse('manpower_planning_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_manpower_planning_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Initial description')
        self.assertContains(response, 'John Doe') # Check display-only fields

    @patch('manpower_planning.models.ManPowerPlanning._get_balance_hours_wono', return_value=100.0) # Mock balance
    @patch('manpower_planning.models.ManPowerPlanning._check_valid_wo_no', return_value=True) # Mock WO validation
    def test_update_view_post_success(self, mock_check_wo, mock_get_balance):
        obj = self.manpower_planning
        updated_description = 'Updated description for project Alpha.'
        data = {
            'work_order_no': obj.work_order_no,
            'department': '',
            'plan_type': obj.plan_type,
            'planned_hours': 7.0, # Change hours
            'description': updated_description,
            'actual_description': obj.actual_description,
        }
        # Simulate HTMX request
        response = self.client.post(reverse('manpower_planning_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshManPowerPlanningList', response.headers['HX-Trigger'])
        
        obj.refresh_from_db()
        self.assertEqual(obj.description, updated_description)
        self.assertEqual(obj.amendment_no, 1) # Check amendment count

        from .models import ManPowerPlanningAmendment
        self.assertTrue(ManPowerPlanningAmendment.objects.filter(manpower_planning=obj).exists())

    @patch('manpower_planning.models.ManPowerPlanning._get_balance_hours_wono', return_value=5.0)
    @patch('manpower_planning.models.ManPowerPlanning._check_valid_wo_no', return_value=True)
    def test_update_view_post_validation_failure(self, mock_check_wo, mock_get_balance):
        obj = self.manpower_planning
        data = {
            'work_order_no': obj.work_order_no,
            'department': '',
            'plan_type': obj.plan_type,
            'planned_hours': 10.0, # Exceeds mock balance
            'description': 'Attempting invalid update',
            'actual_description': obj.actual_description,
        }
        response = self.client.post(reverse('manpower_planning_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, "Only 5.00 hours are remained.")
        obj.refresh_from_db()
        self.assertNotEqual(obj.description, 'Attempting invalid update') # Ensure not updated

    def test_delete_view_get(self):
        obj = self.manpower_planning
        response = self.client.get(reverse('manpower_planning_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_manpower_planning_confirm_delete.html')
        self.assertTrue('manpower_planning' in response.context)
        self.assertContains(response, "Are you sure you want to delete")

    def test_delete_view_post_success(self):
        obj = self.manpower_planning
        initial_count = ManPowerPlanning.objects.count()
        response = self.client.post(reverse('manpower_planning_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshManPowerPlanningList', response.headers['HX-Trigger'])
        self.assertEqual(ManPowerPlanning.objects.count(), initial_count - 1)
        self.assertFalse(ManPowerPlanning.objects.filter(pk=obj.pk).exists())
```

---

## Step 5: HTMX and Alpine.js Integration

The provided templates and views demonstrate the core HTMX and Alpine.js patterns:

*   **HTMX for Dynamic Content:**
    *   `hx-get="{% url 'manpower_planning_table' %}" hx-trigger="load, refreshManPowerPlanningList from:body" hx-swap="innerHTML"`: The main list page (`manpower_planning_list.html`) uses this to dynamically load the DataTables content into `manpower_planningTable-container` on page load and whenever a `refreshManPowerPlanningList` custom event is triggered (after Add/Edit/Delete).
    *   `hx-get="{% url 'manpower_planning_add' %}" hx-target="#modalContent" hx-trigger="click"`: Buttons to open the Add/Edit/Delete forms inside a modal. The form content is fetched via HTMX and placed into `#modalContent`.
    *   `hx-post="{{ request.path }}" hx-swap="none"`: Form submissions send data via HTMX. `hx-swap="none"` prevents the entire form being re-rendered, as we'll handle success via `HX-Trigger`.
    *   `hx-on::after-request="if(event.detail.xhr.status === 204) { ... }"`: This JavaScript embedded in `hx-on` closes the modal after a successful form submission (which returns a 204 No Content).
    *   `headers={'HX-Trigger': 'refreshManPowerPlanningList'}`: Django views return this header on successful CRUD operations, instructing the client to trigger the `refreshManPowerPlanningList` event, which causes the main table to reload.

*   **Alpine.js for UI State:**
    *   `x-data="{ wonoInput: '{{ form.instance.work_order_no|default:'' }}' }"`: In `_manpower_planning_form.html`, `x-data` is used to initialize `wonoInput` with the current `work_order_no`.
    *   `x-bind:disabled`: Although not fully implemented for dynamic switching in the generated `_manpower_planning_form.html` (which would require more complex conditional rendering or Alpine logic), the forms include `x-bind:disabled` as a placeholder for how you would dynamically enable/disable fields based on `wonoInput` (e.g., disable department if `wonoInput` is present and not 'NA'). This allows `TWONo.Enabled` and `DrpDepartment.Enabled` logic to be moved to the frontend.
    *   Modal control: The `manpower_planning_list.html` uses `_` (Alpine.js's shorthand for JavaScript execution) on HTMX buttons to add/remove CSS classes (`.flex`, `.opacity-100`, `.scale-100`) to show/hide and animate the modal. This keeps the UI state management efficient and declarative.

*   **DataTables for List Views:**
    *   `$('#manpower_planningTable').DataTable({...})` is initialized within the `_manpower_planning_table.html` partial. This ensures DataTables only initializes when the table content is loaded via HTMX, providing client-side search, sort, and pagination.
    *   The DataTables initialization is wrapped in a check `if (!$.fn.DataTable.isDataTable('#manpower_planningTable'))` to prevent re-initialization if the table is loaded multiple times (though HTMX `hx-swap="innerHTML"` typically replaces the content, clearing previous DataTables instances).

---

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Man Power Planning module to Django. By following these steps and leveraging the power of AI-assisted automation, your organization can achieve a modern, efficient, and maintainable application with significant business benefits. Remember to adjust placeholder values (`company_id`, `financial_year_id`, `grade_id`) and detailed business logic (`_check_valid_wo_no`, `_get_balance_hours`, `_get_balance_hours_wono`) to match your specific system requirements.