This comprehensive modernization plan outlines the strategic transition of your ASP.NET `ManPowerPlanning_Print` module to a robust, modern Django application. Our approach leverages AI-assisted automation to streamline the conversion, focusing on business value and maintainable, high-performance code.

By adopting Django with HTMX and Alpine.js, your organization will benefit from:

*   **Enhanced User Experience:** Dynamic, real-time interactions without full page reloads, similar to a single-page application but with the simplicity of traditional web pages.
*   **Improved Performance:** Efficient data loading and updates through HTMX, minimizing server load and improving responsiveness.
*   **Simplified Frontend Development:** Eliminate complex JavaScript frameworks, relying on HTMX for server-side rendering and Alpine.js for lightweight client-side interactivity, significantly reducing development and maintenance costs.
*   **Maintainable Codebase:** Adherence to Django's "Fat Model, Thin View" architecture promotes clear separation of concerns, making code easier to understand, debug, and extend.
*   **Scalability and Security:** Django's built-in features provide a solid foundation for scalable and secure web applications, ensuring your system can grow with your business needs.
*   **Cost Efficiency:** Automation-driven migration reduces manual coding effort, accelerating the transition and lowering overall project costs.

This plan details each step of the conversion, providing actionable guidance for your team.

---

## ASP.NET to Django Conversion Script: Man Power Planning - Print

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The analysis of the ASP.NET code reveals interactions with several key tables:
1.  **`tblPM_ManPowerPlanning`**: This is the core table for Man Power Planning records, used for filtering based on Employee ID, Work Order Number, Date, and Type. We infer its structure from the query parameters used in `BtnSearch_Click`.
2.  **`BusinessGroup`**: Used for the `DrpCategory` dropdown.
3.  **`tblHR_OfficeStaff`**: Used for the employee name autocomplete.
4.  **`tblFinancial_master`**: Used to determine the financial year range for populating months.

For the purpose of this `ManPowerPlanning_Print` module, the primary focus is filtering and displaying data from `tblPM_ManPowerPlanning`. The other tables serve as lookups or configuration data.

**Identified Tables and Inferred Columns:**

*   **[TABLE_NAME] = `tblPM_ManPowerPlanning`**
    *   `Id` (Primary Key, integer)
    *   `EmpId` (Employee ID, likely an integer or string, foreign key to `tblHR_OfficeStaff`)
    *   `WONo` (Work Order Number, string)
    *   `Date` (Date of planning, date type)
    *   `Types` (Type of presence/absence, integer, e.g., 1 for Present, 2 for Absent)
    *   `BGGroup` (Business Group ID, likely an integer, foreign key to `BusinessGroup`)
    *   *(Additional columns expected but not directly inferred from this specific ASPX/C#)*

*   **[TABLE_NAME] = `BusinessGroup`**
    *   `Id` (Primary Key, integer)
    *   `Symbol` (String, representing the business group name/symbol)

*   **[TABLE_NAME] = `tblHR_OfficeStaff`**
    *   `EmpId` (Primary Key or unique identifier, string/integer)
    *   `EmployeeName` (String)

*   **[TABLE_NAME] = `tblFinancial_master`**
    *   `FinYearId` (Primary Key, integer)
    *   `CompId` (Company ID, integer)
    *   `FinYearFrom` (Date)
    *   `FinYearTo` (Date)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**

The `ManPowerPlanning_Print.aspx` page primarily focuses on **Read (Search/Filter)** operations. It allows users to apply various filters to fetch relevant "Man Power Planning" records, which are then displayed in an iframe on a separate page (`ManPowerPlanning_Print_Details.aspx`). There are no direct Create, Update, or Delete operations for `ManPowerPlanning` records on *this specific page*.

*   **Create:** Not present on this page.
*   **Read (Filter & Display):** This is the core functionality.
    *   A search form captures user input for:
        *   Selecting filter type (WONo or BG Group).
        *   Work Order Number (`TxtWONo`).
        *   Business Group (`DrpCategory`).
        *   Month (`DrpMonths`).
        *   Type (Present, Absent, Onsite, PL - `Drptype`).
        *   Date Range (From `Txtfromdate` to `TxtTodate`).
        *   Employee Name (`TxtEmpName`) with autocomplete.
    *   Upon clicking "Search" (`BtnSearch_Click`), the page constructs query parameters and loads a new page (`ManPowerPlanning_Print_Details.aspx`) into an iframe with these parameters.
*   **Update:** Not present on this page.
*   **Delete:** Not present on this page.

**Validation Logic:**
*   Date format validation using `RegularExpressionValidator`.
*   Client-side alert if "Department" (BG Group) or "WONo" is not selected when required.

In Django, this filtering and display will be achieved by:
*   A main view rendering a search form.
*   HTMX requests triggered by form submission to a partial view that renders the filtered DataTables results, eliminating the need for iframes.
*   Alpine.js for dynamic UI interactions, like showing/hiding input fields based on dropdown selection, mimicking the `ddlSelectBG_WONo_SelectedIndexChanged` behavior.
*   Django Forms for robust validation and data handling.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

| ASP.NET Control                 | Role                                       | Django Equivalent                                        | HTMX/Alpine.js Interaction                               |
| :------------------------------ | :----------------------------------------- | :------------------------------------------------------- | :------------------------------------------------------- |
| `ddlSelectBG_WONo` (Dropdown)   | Selects filter mode                        | `forms.ChoiceField` (Select Widget)                      | `hx-post` to dynamically swap relevant input fields      |
| `TxtWONo` (TextBox)             | Work Order No. input                       | `forms.CharField` (TextInput Widget)                     | Visibility controlled by HTMX swap                       |
| `DrpCategory` (Dropdown)        | Business Group selection                   | `forms.ModelChoiceField` (Select Widget)                 | Populated from `BusinessGroup` model. Visibility controlled by HTMX swap |
| `DrpMonths` (Dropdown)          | Month selection                            | `forms.ChoiceField` (Select Widget)                      | Dynamically populated via Python logic                   |
| `Drptype` (Dropdown)            | Type selection (Present/Absent/Onsite/PL)  | `forms.ChoiceField` (Select Widget)                      | Static choices                                           |
| `Txtfromdate`, `TxtTodate` (TextBox with CalendarExtender) | Date range input                           | `forms.DateField` (DateInput Widget)                     | Use `type="date"` or an external Flatpickr/Pikaday library with Alpine.js |
| `TxtEmpName` (TextBox with AutoCompleteExtender) | Employee Name search/autocomplete          | `forms.CharField` (TextInput Widget)                     | `hx-get` for autocomplete suggestions, Alpine.js for display |
| `BtnSearch` (Button)            | Triggers search/filter                     | `forms.Button` (or implicit form submission)             | `hx-get` to trigger table data refresh                     |
| `Iframe1` (Iframe)              | Displays search results                    | `div` with `hx-target` and `hx-swap` for partial updates | HTMX loads dynamic content into this `div`               |
| `SqlBGGroup` (SqlDataSource)    | Data source for `DrpCategory`              | Django ORM queries (`BusinessGroup.objects.all()`)       | Initial form population                                  |

### Step 4: Generate Django Code

We will create a Django application named `manpowerplanning`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schemas.

**Instructions:**
We define models for `ManPowerPlanning` (the main entity), `BusinessGroup`, `HrOfficeStaff`, and `FinancialMaster`. Note that `managed = False` is used to map to existing database tables.

```python
# manpowerplanning/models.py
from django.db import models
from datetime import date

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class HrOfficeStaff(models.Model):
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be string
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return self.employee_name

class FinancialMaster(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_from = models.DateField(db_column='FinYearFrom')
    fin_year_to = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year Master'
        verbose_name_plural = 'Financial Year Masters'

    def __str__(self):
        return f"FY {self.fin_year_from.year}-{self.fin_year_to.year}"

class ManPowerPlanning(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming an auto-incrementing PK
    emp = models.ForeignKey(HrOfficeStaff, models.DO_NOTHING, db_column='EmpId', blank=True, null=True) # Renamed to 'emp' to avoid clash with 'EmpId' field
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    date = models.DateField(db_column='Date', blank=True, null=True)
    types = models.IntegerField(db_column='Types', blank=True, null=True) # 1:Present, 2:Absent, 3:Onsite, 4:PL
    bg_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroup', blank=True, null=True) # Renamed to 'bg_group'

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning'
        verbose_name = 'Man Power Planning'
        verbose_name_plural = 'Man Power Planning'

    def __str__(self):
        return f"Planning for {self.emp.employee_name if self.emp else 'N/A'} on {self.date}"

    @classmethod
    def get_month_range(cls, comp_id, fin_year_id):
        """
        Fetches the financial year range and generates a list of months.
        This replicates the fun.MonthRange logic from ASP.NET.
        """
        try:
            financial_year = FinancialMaster.objects.get(comp_id=comp_id, fin_year_id=fin_year_id)
            start_date = financial_year.fin_year_from
            end_date = financial_year.fin_year_to
            
            months = []
            current_date = start_date
            while current_date <= end_date:
                months.append((current_date.month, current_date.strftime('%B')))
                # Move to the first day of the next month
                if current_date.month == 12:
                    current_date = date(current_date.year + 1, 1, 1)
                else:
                    current_date = date(current_date.year, current_date.month + 1, 1)
            
            # ASP.NET code specific: 
            # j=4 and k<=8 means April to December. Then January, February, March.
            # This is unusual and might need specific handling if exact month order is critical.
            # For now, we'll return a sorted list of (month_number, month_name).
            # The UI will then map this to its dropdown.
            # Replicating the exact ASP.NET ordering:
            ordered_months = []
            
            # April to December (months 4-12)
            for month_num in range(4, 13):
                ordered_months.append((month_num, date(2000, month_num, 1).strftime('%B')))
            
            # January to March (months 1-3)
            for month_num in range(1, 4):
                ordered_months.append((month_num, date(2000, month_num, 1).strftime('%B')))

            return ordered_months

        except FinancialMaster.DoesNotExist:
            return []
        except Exception as e:
            # Log the exception for debugging
            print(f"Error generating month range: {e}")
            return []

    def get_type_display(self):
        """Returns the human-readable string for the 'types' field."""
        TYPES_CHOICES = {
            1: "Present",
            2: "Absent",
            3: "Onsite",
            4: "PL",
        }
        return TYPES_CHOICES.get(self.types, "NA")

    @classmethod
    def get_manpower_planning_data(cls, emp_id_filter=None, bg_group_id_filter=None,
                                    wo_no_filter=None, month_filter=None,
                                    type_filter=None, from_date_filter=None,
                                    to_date_filter=None):
        """
        Applies filters to ManPowerPlanning records based on search criteria.
        This centralizes the search logic found in BtnSearch_Click.
        """
        queryset = cls.objects.all()

        if emp_id_filter:
            # Assuming emp_id_filter comes from fun.getCode which extracts the ID
            queryset = queryset.filter(emp__empid=emp_id_filter)
        if bg_group_id_filter:
            queryset = queryset.filter(bg_group__id=bg_group_id_filter)
        if wo_no_filter:
            queryset = queryset.filter(wo_no=wo_no_filter)
        if month_filter:
            queryset = queryset.filter(date__month=month_filter)
        if type_filter:
            queryset = queryset.filter(types=type_filter)
        if from_date_filter and to_date_filter:
            queryset = queryset.filter(date__range=(from_date_filter, to_date_filter))
        elif from_date_filter:
            queryset = queryset.filter(date__gte=from_date_filter)
        elif to_date_filter:
            queryset = queryset.filter(date__lte=to_date_filter)

        return queryset.select_related('emp', 'bg_group') # Optimize lookups for related fields

```

#### 4.2 Forms

**Task:** Define Django forms for user input (search form and general CRUD form).

**Instructions:**
We define a `ManPowerPlanningSearchForm` for the filtering interface, which is a regular `forms.Form` as it's not directly tied to creating/updating a model instance. We also include a `ManPowerPlanningForm` as a `ModelForm` for potential CRUD operations, adhering to the prompt's requirements for a complete solution.

```python
# manpowerplanning/forms.py
from django import forms
from .models import ManPowerPlanning, BusinessGroup, HrOfficeStaff, FinancialMaster
from datetime import date
import re

class ManPowerPlanningSearchForm(forms.Form):
    SELECT_OPTIONS = [
        ('0', 'Select WONo or BG Group'),
        ('1', 'BG Group'),
        ('2', 'WONo'),
    ]
    TYPE_OPTIONS = [
        ('0', 'NA'),
        ('1', 'Present'),
        ('2', 'Absent'),
        ('3', 'Onsite'),
        ('4', 'PL'),
    ]

    select_bg_wono = forms.ChoiceField(
        choices=SELECT_OPTIONS,
        initial='0',
        label="Select Option",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': '/manpowerplanning/dynamic-form-fields/', 'hx-trigger': 'change', 'hx-target': '#dynamic-fields-container', 'hx-swap': 'outerHTML'})
    )
    
    wo_no = forms.CharField(
        required=False,
        label="Work Order No.",
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'WO No.', 'x-show': 'selectedOption == "2"'})
    )
    
    category = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False,
        empty_label="Select Category",
        label="Business Group",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'x-show': 'selectedOption == "1"'})
    )

    month = forms.ChoiceField(
        choices=[], # Populated in __init__
        required=False,
        label="Month",
        widget=forms.Select(attrs={'class': 'box3 w-full'})
    )

    type = forms.ChoiceField(
        choices=TYPE_OPTIONS,
        initial='0',
        required=False,
        label="Type",
        widget=forms.Select(attrs={'class': 'box3 w-full'})
    )

    from_date = forms.DateField(
        required=False,
        label="From Date",
        widget=forms.DateInput(attrs={'class': 'box3 w-full', 'type': 'date'}) # 'type="date"' for native date picker
    )

    to_date = forms.DateField(
        required=False,
        label="To Date",
        widget=forms.DateInput(attrs={'class': 'box3 w-full', 'type': 'date'})
    )

    employee_name = forms.CharField(
        required=False,
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Employee Name',
            'hx-get': '/manpowerplanning/employee-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search', # 'search' is for browsers with built-in search input
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-on:focus': 'showSuggestions = true', # Alpine.js: show suggestions on focus
            'x-on:click.outside': 'showSuggestions = false', # Alpine.js: hide suggestions on click outside
        })
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)

        # Populate month dropdown dynamically, mimicking ASP.NET logic
        if comp_id and fin_year_id:
            month_choices = [('0', 'Select')] + [(str(m[0]), m[1]) for m in ManPowerPlanning.get_month_range(comp_id, fin_year_id)]
            self.fields['month'].choices = month_choices
        else:
            self.fields['month'].choices = [('0', 'Select')]

    def clean(self):
        cleaned_data = super().clean()
        select_option = cleaned_data.get('select_bg_wono')
        wo_no = cleaned_data.get('wo_no')
        category = cleaned_data.get('category')
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        employee_name = cleaned_data.get('employee_name')

        # Replicate ASP.NET validation: "Please Select Department/WONo."
        if select_option == '1' and not category:
            self.add_error('category', 'Please select a Business Group.')
        if select_option == '2' and not wo_no:
            self.add_error('wo_no', 'Please enter a Work Order No.')

        # Date range validation
        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', 'To Date cannot be before From Date.')

        # Extract EmpId from employee_name if present
        if employee_name:
            match = re.search(r'\[(\w+)\]$', employee_name) # Assuming format "Employee Name [EmpId]"
            if match:
                cleaned_data['employee_id_parsed'] = match.group(1)
            else:
                # If EmpId not found, try to find by name, or flag an error
                # For simplicity, we'll assume exact match or no match for now
                try:
                    hr_staff = HrOfficeStaff.objects.get(employee_name=employee_name)
                    cleaned_data['employee_id_parsed'] = hr_staff.empid
                except HrOfficeStaff.DoesNotExist:
                    self.add_error('employee_name', 'Invalid employee name selected.')
        
        return cleaned_data

class ManPowerPlanningForm(forms.ModelForm):
    class Meta:
        model = ManPowerPlanning
        fields = ['emp', 'wo_no', 'date', 'types', 'bg_group']
        widgets = {
            'emp': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'types': forms.Select(choices=ManPowerPlanningSearchForm.TYPE_OPTIONS, attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bg_group': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

```

#### 4.3 Views

**Task:** Implement search and CRUD operations using Class-Based Views.

**Instructions:**
We define a main search view (`ManPowerPlanningSearchView`), a partial view for the search results table (`ManPowerPlanningTablePartialView`), a view for dynamic form fields (`DynamicSearchFieldsView`), and an autocomplete view (`EmployeeAutoCompleteView`). Standard CRUD views for `ManPowerPlanning` are also included as per requirements. All views are kept thin, delegating logic to forms and models.

```python
# manpowerplanning/views.py
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.conf import settings
import datetime

from .models import ManPowerPlanning, BusinessGroup, HrOfficeStaff
from .forms import ManPowerPlanningSearchForm, ManPowerPlanningForm

class ManPowerPlanningSearchView(TemplateView):
    template_name = 'manpowerplanning/manpowerplanning_search_page.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Assuming session variables for compid and finyear
        # In a real app, retrieve these from request.session or user profile
        comp_id = self.request.session.get('compid', 1)  # Default to 1 for example
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 for example
        context['form'] = ManPowerPlanningSearchForm(
            self.request.GET or None, 
            comp_id=comp_id, 
            fin_year_id=fin_year_id
        )
        return context

# HTMX partial view for dynamic search fields
class DynamicSearchFieldsView(TemplateView):
    template_name = 'manpowerplanning/_search_form_fields.html'

    def post(self, request, *args, **kwargs):
        selected_option = request.POST.get('select_bg_wono', '0')
        wo_no_value = request.POST.get('wo_no', '') # Pass current value to maintain state
        category_id = request.POST.get('category', '') # Pass current value to maintain state

        context = {
            'selected_option': selected_option,
            'wo_no_value': wo_no_value,
            'category_id': category_id,
            'business_groups': BusinessGroup.objects.all(),
        }
        return self.render_to_response(context)


# HTMX partial view for ManPowerPlanning list table
class ManPowerPlanningTablePartialView(ListView):
    model = ManPowerPlanning
    template_name = 'manpowerplanning/_manpowerplanning_table.html'
    context_object_name = 'manpower_plannings'

    def get_queryset(self):
        # Assuming session variables for compid and finyear, similar to search form
        comp_id = self.request.session.get('compid', 1) 
        fin_year_id = self.request.session.get('finyear', 1)
        
        form = ManPowerPlanningSearchForm(
            self.request.GET, 
            comp_id=comp_id, 
            fin_year_id=fin_year_id
        )
        if form.is_valid():
            cleaned_data = form.cleaned_data
            
            # Extract EmpId from employee_name_parsed if present
            emp_id = cleaned_data.get('employee_id_parsed')

            return ManPowerPlanning.get_manpower_planning_data(
                emp_id_filter=emp_id,
                bg_group_id_filter=cleaned_data.get('category').id if cleaned_data.get('category') else None,
                wo_no_filter=cleaned_data.get('wo_no'),
                month_filter=cleaned_data.get('month'),
                type_filter=cleaned_data.get('type'),
                from_date_filter=cleaned_data.get('from_date'),
                to_date_filter=cleaned_data.get('to_date')
            )
        # If form is not valid (e.g., initial load without search), return empty or default
        return ManPowerPlanning.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the form data or errors to the partial template if needed for debugging
        context['form_errors'] = self.request.GET
        return context

class EmployeeAutoCompleteView(ListView):
    """
    Provides autocomplete suggestions for employee names.
    Mimics the GetCompletionList web method.
    """
    model = HrOfficeStaff
    paginate_by = 10 # Limit results
    
    def get_queryset(self):
        # comp_id = self.request.session.get('compid', 1) # If filtering by company is needed
        query = self.request.GET.get('q', '')
        if query:
            # Filter by employee name starting with prefix, case-insensitive
            queryset = HrOfficeStaff.objects.filter(
                employee_name__istartswith=query
                # , comp_id=comp_id # Apply company filter if needed
            ).order_by('employee_name')
        else:
            queryset = HrOfficeStaff.objects.none()
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # Return a list of suggestions as JSON, or an HTML fragment for HTMX
        # For simplicity, returning JSON, which Alpine.js can consume.
        suggestions = []
        for obj in context['hrofficestaff_list']: # ListView uses object_list as default context name
            suggestions.append(f"{obj.employee_name} [{obj.empid}]")
        return JsonResponse(suggestions, safe=False)

# --- Standard CRUD Views for ManPowerPlanning (as per prompt requirements) ---
# Note: These views handle full CRUD and are separate from the search functionality.

class ManPowerPlanningListView(ListView):
    model = ManPowerPlanning
    template_name = 'manpowerplanning/manpowerplanning_list.html' # Changed name to avoid clash with search page
    context_object_name = 'manpower_plannings'

class ManPowerPlanningCreateView(CreateView):
    model = ManPowerPlanning
    form_class = ManPowerPlanningForm
    template_name = 'manpowerplanning/manpowerplanning_form.html'
    success_url = reverse_lazy('manpowerplanning_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Man Power Planning entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, just trigger client-side event
                headers={
                    'HX-Trigger': 'refreshManPowerPlanningList'
                }
            )
        return response

class ManPowerPlanningUpdateView(UpdateView):
    model = ManPowerPlanning
    form_class = ManPowerPlanningForm
    template_name = 'manpowerplanning/manpowerplanning_form.html'
    success_url = reverse_lazy('manpowerplanning_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Man Power Planning entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManPowerPlanningList'
                }
            )
        return response

class ManPowerPlanningDeleteView(DeleteView):
    model = ManPowerPlanning
    template_name = 'manpowerplanning/manpowerplanning_confirm_delete.html'
    success_url = reverse_lazy('manpowerplanning_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Man Power Planning entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManPowerPlanningList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will be HTML files using Django's template language, HTMX attributes, and Alpine.js for interactivity. They extend `core/base.html`.

```html
{# manpowerplanning/manpowerplanning_search_page.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Man Power Planning - Print</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="searchForm" hx-get="{% url 'manpowerplanning_table' %}" hx-target="#searchResultsContainer" hx-swap="innerHTML" hx-trigger="submit">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" x-data="{ selectedOption: '{{ form.select_bg_wono.value|default:'0' }}' }">
                {# Select WO No or BG Group #}
                <div>
                    <label for="{{ form.select_bg_wono.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.select_bg_wono.label }}</label>
                    <select id="{{ form.select_bg_wono.id_for_label }}" name="{{ form.select_bg_wono.name }}"
                            class="box3 w-full"
                            x-model="selectedOption"
                            hx-post="{% url 'manpowerplanning_dynamic_fields' %}"
                            hx-target="#dynamic-fields-container"
                            hx-swap="outerHTML"
                            hx-vals="js:{'wo_no': $el.form.wo_no.value, 'category': $el.form.category.value}">
                        {% for value, label in form.select_bg_wono.field.choices %}
                            <option value="{{ value }}" {% if form.select_bg_wono.value|stringformat:"s" == value|stringformat:"s" %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                {# Dynamic fields container #}
                <div id="dynamic-fields-container" hx-trigger="load delay:10ms from:body" hx-post="{% url 'manpowerplanning_dynamic_fields' %}" 
                     hx-swap="outerHTML" hx-vals="js:{'select_bg_wono': selectedOption, 'wo_no': $el.form.wo_no.value, 'category': $el.form.category.value}">
                    {# This content will be replaced by _search_form_fields.html via HTMX #}
                    <p class="text-sm text-gray-500">Loading dynamic fields...</p>
                </div>
                
                {# Month #}
                <div>
                    <label for="{{ form.month.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.month.label }}</label>
                    {{ form.month }}
                </div>

                {# Type #}
                <div>
                    <label for="{{ form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.type.label }}</label>
                    {{ form.type }}
                </div>

                {# From Date #}
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                </div>

                {# To Date #}
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_date.label }}</label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                </div>

                {# Employee Name with Autocomplete #}
                <div x-data="{ showSuggestions: false, selectedEmployee: '{{ form.employee_name.value|default:'' }}' }">
                    <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.employee_name.label }}</label>
                    <input type="text" id="{{ form.employee_name.id_for_label }}" name="{{ form.employee_name.name }}"
                           value="{{ form.employee_name.value|default:'' }}"
                           class="box3 w-full" placeholder="Employee Name"
                           hx-get="{% url 'manpowerplanning_employee_autocomplete' %}"
                           hx-trigger="keyup changed delay:500ms from:#{{ form.employee_name.id_for_label }}"
                           hx-target="#employee-suggestions"
                           hx-swap="innerHTML"
                           autocomplete="off"
                           @focus="showSuggestions = true"
                           @click.outside="showSuggestions = false"
                           @input="selectedEmployee = $event.target.value"
                           @keydown.enter.prevent="$event.target.value = $event.target.value; showSuggestions = false;">
                    
                    <div id="employee-suggestions" class="relative z-10">
                        <ul x-show="showSuggestions" class="absolute bg-white border border-gray-300 w-full max-h-48 overflow-y-auto mt-1 rounded-md shadow-lg">
                            {# Suggestions will be loaded here via HTMX #}
                            {# Example of suggestions from HTMX JSON. Alpine.js will render it. #}
                            {# This is an example of how HTMX + Alpine works for suggestions #}
                            {# HTMX will return an array of strings in JSON, Alpine will render the list #}
                            {# So employee-autocomplete view needs to return JSON like ["Name [ID]", "Name2 [ID2]"] #}
                            <template x-for="suggestion in JSON.parse($el.innerHTML || '[]')" :key="suggestion">
                                <li @click="selectedEmployee = suggestion; $el.closest('div').querySelector('#{{ form.employee_name.id_for_label }}').value = suggestion; showSuggestions = false;" 
                                    class="p-2 hover:bg-gray-100 cursor-pointer text-sm">
                                    <span x-text="suggestion"></span>
                                </li>
                            </template>
                        </ul>
                    </div>
                    {% if form.employee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name.errors }}</p>{% endif %}
                </div>

                {# Search Button #}
                <div class="col-span-full flex justify-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mt-4">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
            {% if form.errors %}
                <div class="text-red-500 text-sm mt-4">
                    {% for field_name, errors in form.errors.items %}
                        {% if field_name != '__all__' %}<p>{{ form.fields|get_item:field_name }}: {{ errors|join:", " }}</p>{% endif %}
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    {# Search Results Container (replaces the iframe) #}
    <div id="searchResultsContainer" class="bg-white shadow-md rounded-lg p-6">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading results or awaiting search...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Utility to get dictionary item in template (Django filters can do this, but for JS it's quick)
    function get_item(obj, key) {
        return obj[key];
    }
</script>
{% endblock %}

```

```html
{# manpowerplanning/_search_form_fields.html #}
{# This template is dynamically loaded via HTMX #}
<div {% if selected_option == '1' %}x-show="true"{% else %}x-show="false"{% endif %} id="category_field_container">
    <label for="id_category" class="block text-sm font-medium text-gray-700">Business Group</label>
    <select id="id_category" name="category" class="box3 w-full">
        <option value="">Select Category</option>
        {% for bg in business_groups %}
            <option value="{{ bg.id }}" {% if category_id|stringformat:"s" == bg.id|stringformat:"s" %}selected{% endif %}>{{ bg.symbol }}</option>
        {% endfor %}
    </select>
</div>

<div {% if selected_option == '2' %}x-show="true"{% else %}x-show="false"{% endif %} id="wo_no_field_container">
    <label for="id_wo_no" class="block text-sm font-medium text-gray-700">Work Order No.</label>
    <input type="text" id="id_wo_no" name="wo_no" value="{{ wo_no_value }}" class="box3 w-full" placeholder="WO No.">
</div>

{# If selected_option is '0' or default, both are hidden via x-show. #}
{# The containing div in manpowerplanning_search_page.html will have x-data to manage visibility. #}

```

```html
{# manpowerplanning/_manpowerplanning_table.html #}
{# This template is dynamically loaded via HTMX into #searchResultsContainer #}
<div class="overflow-x-auto">
    <table id="manpowerPlanningTable" class="min-w-full bg-white">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order No.</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Group</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in manpower_plannings %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.emp.employee_name|default:'N/A' }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.wo_no|default:'N/A' }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.date|date:"d-m-Y"|default:'N/A' }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_type_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.bg_group.symbol|default:'N/A' }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'manpowerplanning_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'manpowerplanning_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#manpowerPlanningTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>

```

```html
{# manpowerplanning/manpowerplanning_list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Man Power Planning Entries</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'manpowerplanning_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Entry
        </button>
    </div>
    
    <div id="manpowerplanningTable-container"
         hx-trigger="load, refreshManPowerPlanningList from:body"
         hx-get="{% url 'manpowerplanning_list_partial' %}" {# This is a new URL for the pure list partial #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

```html
{# manpowerplanning/_manpowerplanning_list_partial.html #}
{# This is a separate partial for the standard list view's DataTable. #}
{# It's called by manpowerplanning_list.html via HTMX. #}
<table id="manpowerplanningListDataTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order No.</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Group</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in manpower_plannings %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.emp.employee_name|default:'N/A' }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.wo_no|default:'N/A' }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.date|date:"d-m-Y"|default:'N/A' }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_type_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.bg_group.symbol|default:'N/A' }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'manpowerplanning_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'manpowerplanning_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No entries available.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#manpowerplanningListDataTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

```html
{# manpowerplanning/manpowerplanning_form.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Man Power Planning Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# manpowerplanning/manpowerplanning_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete the Man Power Planning entry for "{{ object }}"?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main search page, the dynamic form fields, the search results table, employee autocomplete, and the standard CRUD operations.

```python
# manpowerplanning/urls.py
from django.urls import path
from .views import (
    ManPowerPlanningSearchView, ManPowerPlanningTablePartialView, DynamicSearchFieldsView,
    EmployeeAutoCompleteView,
    ManPowerPlanningListView, ManPowerPlanningCreateView, ManPowerPlanningUpdateView, ManPowerPlanningDeleteView,
)

urlpatterns = [
    # Search and Filter functionality (mimics ManPowerPlanning_Print.aspx)
    path('manpowerplanning/search/', ManPowerPlanningSearchView.as_view(), name='manpowerplanning_search'),
    path('manpowerplanning/dynamic-form-fields/', DynamicSearchFieldsView.as_view(), name='manpowerplanning_dynamic_fields'),
    path('manpowerplanning/table/', ManPowerPlanningTablePartialView.as_view(), name='manpowerplanning_table'),
    path('manpowerplanning/employee-autocomplete/', EmployeeAutoCompleteView.as_view(), name='manpowerplanning_employee_autocomplete'),

    # Standard CRUD operations for ManPowerPlanning (as required by prompt)
    path('manpowerplanning/', ManPowerPlanningListView.as_view(), name='manpowerplanning_list'),
    path('manpowerplanning/add/', ManPowerPlanningCreateView.as_view(), name='manpowerplanning_add'),
    path('manpowerplanning/edit/<int:pk>/', ManPowerPlanningUpdateView.as_view(), name='manpowerplanning_edit'),
    path('manpowerplanning/delete/<int:pk>/', ManPowerPlanningDeleteView.as_view(), name='manpowerplanning_delete'),
    # This URL is used by the HTMX partial for the standard list view
    path('manpowerplanning/list-partial/', ManPowerPlanningListView.as_view(template_name='manpowerplanning/_manpowerplanning_list_partial.html'), name='manpowerplanning_list_partial'),
]

```

#### 4.6 Tests

**Task:** Write tests for the models and views.

**Instructions:**
Comprehensive unit tests cover model methods and properties. Integration tests validate view responses, form submissions, and HTMX interactions.

```python
# manpowerplanning/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
import datetime
from .models import ManPowerPlanning, BusinessGroup, HrOfficeStaff, FinancialMaster
from .forms import ManPowerPlanningSearchForm

class BusinessGroupModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        BusinessGroup.objects.create(id=1, symbol='BG_A')
        BusinessGroup.objects.create(id=2, symbol='BG_B')

    def test_business_group_creation(self):
        bg = BusinessGroup.objects.get(id=1)
        self.assertEqual(bg.symbol, 'BG_A')
        self.assertEqual(str(bg), 'BG_A')

class HrOfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        HrOfficeStaff.objects.create(empid='E001', employee_name='John Doe')
        HrOfficeStaff.objects.create(empid='E002', employee_name='Jane Smith')

    def test_hr_office_staff_creation(self):
        emp = HrOfficeStaff.objects.get(empid='E001')
        self.assertEqual(emp.employee_name, 'John Doe')
        self.assertEqual(str(emp), 'John Doe')

class FinancialMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialMaster.objects.create(
            fin_year_id=1,
            comp_id=1,
            fin_year_from=datetime.date(2023, 4, 1),
            fin_year_to=datetime.date(2024, 3, 31)
        )

    def test_financial_master_creation(self):
        fy = FinancialMaster.objects.get(fin_year_id=1)
        self.assertEqual(fy.fin_year_from, datetime.date(2023, 4, 1))
        self.assertEqual(str(fy), 'FY 2023-2024')

class ManPowerPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.bg1 = BusinessGroup.objects.create(id=1, symbol='BG_A')
        cls.emp1 = HrOfficeStaff.objects.create(empid='E001', employee_name='John Doe')
        cls.fy1 = FinancialMaster.objects.create(
            fin_year_id=1, comp_id=1,
            fin_year_from=datetime.date(2023, 4, 1),
            fin_year_to=datetime.date(2024, 3, 31)
        )

        ManPowerPlanning.objects.create(
            id=1, emp=cls.emp1, wo_no='WO123', date=datetime.date(2023, 7, 15), types=1, bg_group=cls.bg1
        )
        ManPowerPlanning.objects.create(
            id=2, emp=cls.emp1, wo_no='WO456', date=datetime.date(2023, 8, 10), types=2, bg_group=cls.bg1
        )
        ManPowerPlanning.objects.create(
            id=3, emp=HrOfficeStaff.objects.create(empid='E002', employee_name='Jane Smith'),
            wo_no='WO789', date=datetime.date(2024, 2, 20), types=1, bg_group=BusinessGroup.objects.create(id=3, symbol='BG_C')
        )

    def test_manpower_planning_creation(self):
        mpp = ManPowerPlanning.objects.get(id=1)
        self.assertEqual(mpp.wo_no, 'WO123')
        self.assertEqual(mpp.emp.employee_name, 'John Doe')
        self.assertEqual(mpp.bg_group.symbol, 'BG_A')

    def test_get_type_display(self):
        mpp1 = ManPowerPlanning.objects.get(id=1)
        self.assertEqual(mpp1.get_type_display(), 'Present')
        mpp2 = ManPowerPlanning.objects.get(id=2)
        self.assertEqual(mpp2.get_type_display(), 'Absent')

    def test_get_month_range(self):
        months = ManPowerPlanning.get_month_range(comp_id=1, fin_year_id=1)
        expected_months = [
            (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'),
            (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December'),
            (1, 'January'), (2, 'February'), (3, 'March')
        ]
        self.assertEqual(months, expected_months)

    def test_get_manpower_planning_data_filters(self):
        # Test EmpId filter
        results = ManPowerPlanning.get_manpower_planning_data(emp_id_filter='E001')
        self.assertEqual(results.count(), 2)
        self.assertTrue(ManPowerPlanning.objects.get(id=1) in results)
        self.assertTrue(ManPowerPlanning.objects.get(id=2) in results)

        # Test WONo filter
        results = ManPowerPlanning.get_manpower_planning_data(wo_no_filter='WO789')
        self.assertEqual(results.count(), 1)
        self.assertTrue(ManPowerPlanning.objects.get(id=3) in results)

        # Test Month filter
        results = ManPowerPlanning.get_manpower_planning_data(month_filter=7)
        self.assertEqual(results.count(), 1)
        self.assertTrue(ManPowerPlanning.objects.get(id=1) in results)

        # Test Type filter
        results = ManPowerPlanning.get_manpower_planning_data(type_filter=1)
        self.assertEqual(results.count(), 2)
        self.assertTrue(ManPowerPlanning.objects.get(id=1) in results)
        self.assertTrue(ManPowerPlanning.objects.get(id=3) in results)

        # Test Date range filter
        results = ManPowerPlanning.get_manpower_planning_data(
            from_date_filter=datetime.date(2023, 7, 1),
            to_date_filter=datetime.date(2023, 8, 31)
        )
        self.assertEqual(results.count(), 2)
        self.assertTrue(ManPowerPlanning.objects.get(id=1) in results)
        self.assertTrue(ManPowerPlanning.objects.get(id=2) in results)

        # Test combined filters
        results = ManPowerPlanning.get_manpower_planning_data(
            emp_id_filter='E001', month_filter=7, types=1
        )
        self.assertEqual(results.count(), 1)
        self.assertTrue(ManPowerPlanning.objects.get(id=1) in results)


class ManPowerPlanningSearchFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        BusinessGroup.objects.create(id=1, symbol='BG_A')
        BusinessGroup.objects.create(id=2, symbol='BG_B')
        HrOfficeStaff.objects.create(empid='E001', employee_name='John Doe')
        FinancialMaster.objects.create(
            fin_year_id=1, comp_id=1,
            fin_year_from=datetime.date(2023, 4, 1),
            fin_year_to=datetime.date(2024, 3, 31)
        )

    def test_form_valid_data(self):
        form = ManPowerPlanningSearchForm(data={
            'select_bg_wono': '1', # BG Group
            'category': '1',
            'month': '7',
            'type': '1',
            'from_date': '2023-07-01',
            'to_date': '2023-07-31',
            'employee_name': 'John Doe [E001]',
        }, comp_id=1, fin_year_id=1)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['employee_id_parsed'], 'E001')

    def test_form_wo_no_required_validation(self):
        form = ManPowerPlanningSearchForm(data={
            'select_bg_wono': '2', # WONo
            'wo_no': '', # Missing WO No
        }, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('wo_no', form.errors)

    def test_form_bg_group_required_validation(self):
        form = ManPowerPlanningSearchForm(data={
            'select_bg_wono': '1', # BG Group
            'category': '', # Missing category
        }, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('category', form.errors)

    def test_form_date_range_validation(self):
        form = ManPowerPlanningSearchForm(data={
            'from_date': '2023-08-01',
            'to_date': '2023-07-01',
        }, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('to_date', form.errors)

    def test_employee_name_parsing(self):
        form = ManPowerPlanningSearchForm(data={
            'employee_name': 'John Doe [E001]',
        }, comp_id=1, fin_year_id=1)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['employee_id_parsed'], 'E001')

    def test_employee_name_parsing_no_id(self):
        form = ManPowerPlanningSearchForm(data={
            'employee_name': 'John Doe', # No ID in brackets
        }, comp_id=1, fin_year_id=1)
        self.assertTrue(form.is_valid()) # Assumes we attempt to lookup by name if ID missing
        # Verify employee_id_parsed is set correctly after lookup
        self.assertEqual(form.cleaned_data['employee_id_parsed'], 'E001')

    def test_employee_name_parsing_invalid(self):
        form = ManPowerPlanningSearchForm(data={
            'employee_name': 'Non Existent Employee',
        }, comp_id=1, fin_year_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('employee_name', form.errors)


class ManPowerPlanningViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.bg1 = BusinessGroup.objects.create(id=1, symbol='BG_A')
        cls.emp1 = HrOfficeStaff.objects.create(empid='E001', employee_name='John Doe')
        cls.emp2 = HrOfficeStaff.objects.create(empid='E002', employee_name='Jane Smith')
        cls.fy1 = FinancialMaster.objects.create(
            fin_year_id=1, comp_id=1,
            fin_year_from=datetime.date(2023, 4, 1),
            fin_year_to=datetime.date(2024, 3, 31)
        )

        ManPowerPlanning.objects.create(
            id=1, emp=cls.emp1, wo_no='WO123', date=datetime.date(2023, 7, 15), types=1, bg_group=cls.bg1
        )
        ManPowerPlanning.objects.create(
            id=2, emp=cls.emp2, wo_no='WO456', date=datetime.date(2023, 8, 10), types=2, bg_group=cls.bg1
        )

    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = 1 # Mock session data
        self.client.session['finyear'] = 1


    def test_search_view_get(self):
        response = self.client.get(reverse('manpowerplanning_search'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/manpowerplanning_search_page.html')
        self.assertIsInstance(response.context['form'], ManPowerPlanningSearchForm)

    def test_manpowerplanning_table_partial_view(self):
        # Test with search parameters
        response = self.client.get(reverse('manpowerplanning_table'), {
            'employee_name': 'John Doe [E001]',
            'from_date': '2023-07-01',
            'to_date': '2023-07-31'
        }, HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/_manpowerplanning_table.html')
        self.assertContains(response, 'WO123')
        self.assertNotContains(response, 'WO456')
        self.assertTrue('manpower_plannings' in response.context)
        self.assertEqual(response.context['manpower_plannings'].count(), 1)


    def test_dynamic_search_fields_view(self):
        response = self.client.post(reverse('manpowerplanning_dynamic_fields'), {'select_bg_wono': '1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/_search_form_fields.html')
        self.assertContains(response, '<select id="id_category" name="category"') # Check for category field
        self.assertNotContains(response, '<input type="text" id="id_wo_no" name="wo_no"') # WO No should be hidden

        response = self.client.post(reverse('manpowerplanning_dynamic_fields'), {'select_bg_wono': '2'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/_search_form_fields.html')
        self.assertContains(response, '<input type="text" id="id_wo_no" name="wo_no"') # Check for WO No field
        self.assertNotContains(response, '<select id="id_category" name="category"') # Category should be hidden

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('manpowerplanning_employee_autocomplete'), {'q': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('John Doe [E001]', response.json())
        self.assertNotIn('Jane Smith [E002]', response.json())

        response = self.client.get(reverse('manpowerplanning_employee_autocomplete'), {'q': 'jane'}, HTTP_HX_REQUEST='true')
        self.assertIn('Jane Smith [E002]', response.json())

        response = self.client.get(reverse('manpowerplanning_employee_autocomplete'), {'q': 'nonexistent'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.json(), [])

    # --- Standard CRUD Views Tests ---

    def test_list_view(self):
        response = self.client.get(reverse('manpowerplanning_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/manpowerplanning_list.html')
        self.assertTrue('manpower_plannings' in response.context)
        self.assertEqual(response.context['manpower_plannings'].count(), 2) # All instances
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Jane Smith')

    def test_create_view_get(self):
        response = self.client.get(reverse('manpowerplanning_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/manpowerplanning_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        data = {
            'emp': self.emp1.empid, # Use empid as it's the FK value
            'wo_no': 'NEWWO',
            'date': '2024-01-01',
            'types': '1', # Present
            'bg_group': self.bg1.id,
        }
        response = self.client.post(reverse('manpowerplanning_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(ManPowerPlanning.objects.filter(wo_no='NEWWO').exists())
        self.assertEqual(response['HX-Trigger'], 'refreshManPowerPlanningList')

    def test_create_view_post_invalid(self):
        data = {
            'emp': '', # Invalid data
            'wo_no': '',
            'date': '',
            'types': '',
            'bg_group': '',
        }
        response = self.client.post(reverse('manpowerplanning_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX request, form re-rendered with errors
        self.assertTemplateUsed(response, 'manpowerplanning/manpowerplanning_form.html')
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        obj = ManPowerPlanning.objects.get(id=1)
        response = self.client.get(reverse('manpowerplanning_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/manpowerplanning_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success(self):
        obj = ManPowerPlanning.objects.get(id=1)
        data = {
            'emp': obj.emp.empid,
            'wo_no': 'UPDATEDWO',
            'date': '2023-07-15',
            'types': '1',
            'bg_group': obj.bg_group.id,
        }
        response = self.client.post(reverse('manpowerplanning_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.wo_no, 'UPDATEDWO')
        self.assertEqual(response['HX-Trigger'], 'refreshManPowerPlanningList')

    def test_delete_view_get(self):
        obj = ManPowerPlanning.objects.get(id=1)
        response = self.client.get(reverse('manpowerplanning_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpowerplanning/manpowerplanning_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post_success(self):
        obj_count_before = ManPowerPlanning.objects.count()
        obj = ManPowerPlanning.objects.get(id=1)
        response = self.client.post(reverse('manpowerplanning_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(ManPowerPlanning.objects.count(), obj_count_before - 1)
        self.assertFalse(ManPowerPlanning.objects.filter(id=obj.id).exists())
        self.assertEqual(response['HX-Trigger'], 'refreshManPowerPlanningList')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The integration focuses on mimicking the ASP.NET page's dynamic behavior without full page reloads, using HTMX for server interaction and Alpine.js for lightweight client-side state management.

*   **Dynamic Form Fields (`ddlSelectBG_WONo` logic):**
    *   The `select_bg_wono` dropdown in `manpowerplanning_search_page.html` uses `hx-post` to `{% url 'manpowerplanning_dynamic_fields' %}` on `change`.
    *   `hx-target="#dynamic-fields-container"` and `hx-swap="outerHTML"` ensure that the container holding `TxtWONo` and `DrpCategory` is completely replaced by the response from the server, which renders `_search_form_fields.html` containing only the relevant visible input.
    *   Alpine.js's `x-model="selectedOption"` ensures the JavaScript state (`selectedOption`) tracks the dropdown value, and `x-show` on `wo_no_field_container` and `category_field_container` within the `_search_form_fields.html` handles the initial visibility based on the server-rendered `selected_option`. The `hx-vals` passes the current values of `wo_no` and `category` back to the server so they can be re-rendered if the user switches back and forth without losing input.

*   **Search and Results Display:**
    *   The `searchForm` in `manpowerplanning_search_page.html` uses `hx-get="{% url 'manpowerplanning_table' %}"` on `submit`.
    *   `hx-target="#searchResultsContainer"` and `hx-swap="innerHTML"` ensure that the search results (the DataTables partial) are loaded directly into the designated `div` without a full page refresh. This replaces the ASP.NET iframe functionality.
    *   `hx-trigger="load, refreshManPowerPlanningList from:body"` on `#searchResultsContainer` allows the table to load initially and to be refreshed automatically after CRUD operations on the main list view.

*   **Employee Autocomplete (`TxtEmpName`):**
    *   The `employee_name` input uses `hx-get="{% url 'manpowerplanning_employee_autocomplete' %}"`.
    *   `hx-trigger="keyup changed delay:500ms"` sends a request after the user stops typing for 500ms.
    *   `hx-target="#employee-suggestions"` and `hx-swap="innerHTML"` load the JSON response from `EmployeeAutoCompleteView` into a hidden `div`.
    *   Alpine.js is used to:
        *   Show/hide the suggestion list (`x-show="showSuggestions"`).
        *   Update the input field when a suggestion is clicked (`@click="selectedEmployee = suggestion; $el.closest('div').querySelector('#id_employee_name').value = suggestion; showSuggestions = false;"`).
        *   Manage focus/blur events to show/hide suggestions.
        *   Parse the JSON response into a list of suggestions.

*   **DataTables Integration:**
    *   The `_manpowerplanning_table.html` and `_manpowerplanning_list_partial.html` partials contain the HTML table structure.
    *   A `<script>` block within these partials initializes DataTables on `$(document).ready()`. Since these partials are loaded via HTMX, `$(document).ready()` will fire *after* the content is swapped into the DOM, ensuring DataTables initializes correctly.

*   **CRUD Operations for `ManPowerPlanning` (Modals):**
    *   Buttons like "Add New Entry," "Edit," and "Delete" use `hx-get` to fetch their respective forms (`manpowerplanning_form.html` or `manpowerplanning_confirm_delete.html`).
    *   `hx-target="#modalContent"` loads the form into a modal container.
    *   `_="on click add .is-active to #modal"` (using Alpine.js's "magic attributes" or Hyperscript) controls the modal's visibility.
    *   Form submissions (`hx-post`) on the modal forms return `status=204` (No Content) along with an `HX-Trigger` header (`refreshManPowerPlanningList`). This signal tells the main list view to re-fetch its data (`hx-trigger="load, refreshManPowerPlanningList from:body"`), ensuring the table is always up-to-date.

This orchestrated use of HTMX and Alpine.js provides a modern, responsive user experience while minimizing JavaScript complexity, aligning perfectly with the fat model, thin view philosophy.

---

### Final Notes

*   **Session Management:** The `compid` and `finyear` are assumed to be available in the Django session, similar to ASP.NET. In a real application, ensure these are properly set during user login or selection.
*   **Database Connections:** Django handles database connections automatically via `settings.py`. Ensure your `settings.py` is configured with the correct connection string to your SQL Server database.
*   **Error Handling:** While Django provides robust error handling, specific business logic error messages (like "Please Select Department/WONo") are replicated in the `ManPowerPlanningSearchForm`'s `clean` method.
*   **Security:** Django's ORM protects against SQL injection. Ensure any custom raw SQL (if necessary, though avoided here) is properly parameterized. CSRF tokens are automatically handled by Django forms.
*   **Deployment:** The Django application will be deployed using a WSGI server (e.g., Gunicorn + Nginx) rather than IIS.
*   **`clsFunctions` replacement:** The functionalities of `clsFunctions` (like `Connection()`, `select()`, `MonthRange()`, `getCode()`, `FromDate()`, `GetRandomAlphaNumeric()`) are replaced by Django's ORM, built-in Python date/time functions, and custom methods within the models or forms (e.g., `ManPowerPlanning.get_month_range`, form cleaning for `getCode`). `GetRandomAlphaNumeric` is not directly needed if query parameters are handled by HTMX and Django views directly.