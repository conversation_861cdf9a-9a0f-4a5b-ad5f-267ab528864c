## ASP.NET to Django Conversion Script: Material Credit Note [MCN] Print Report

This modernization plan outlines the strategy to transition your ASP.NET Material Credit Note Print Report page to a modern Django application. Our approach leverages Django's robust ORM, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation, all while adhering to a "fat model, thin view" architecture and prioritizing automation.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in generating the Material Credit Note report.

**Instructions:**
The ASP.NET code extensively queries several tables to compile the report data. We will define Django models for each of these.

**Identified Tables and Key Columns:**

*   **`tblPM_MaterialCreditNote_Master`** (Django Model: `MaterialCreditNoteMaster`)
    *   `Id` (PK)
    *   `SessionId` (FK to `tblHR_OfficeStaff.EmpId`)
    *   `CompId` (FK to Company Master)
    *   `SysDate` (Date of MCN)
    *   `MCNNo` (MCN Number)
    *   `WONo` (FK to `SD_Cust_WorkOrder_Master.WONo`)
    *   `FinYearId` (FK to Financial Year Master)

*   **`tblPM_MaterialCreditNote_Details`** (Django Model: `MaterialCreditNoteDetail`)
    *   `Id` (PK, referred to as `DId` in C#)
    *   `MId` (FK to `tblPM_MaterialCreditNote_Master.Id`)
    *   `PId` (Product/Parent Item ID)
    *   `CId` (Component Item ID)
    *   `MCNQty` (Quantity on MCN)

*   **`tblQc_AuthorizedMCN`** (Django Model: `QcAuthorizedMCN`)
    *   `QAQty` (Quality Assurance Quantity)
    *   `FinYearId`
    *   `CompId`
    *   `MCNId` (FK to `tblPM_MaterialCreditNote_Master.Id`)
    *   `MCNDId` (FK to `tblPM_MaterialCreditNote_Details.Id`)

*   **`tblDG_BOM_Master`** (Django Model: `DgBomMaster`)
    *   `WONo`
    *   `FinYearId`
    *   `CompId`
    *   `PId`
    *   `CId`
    *   `Qty` (BOM Quantity)

*   **`tblDG_Item_Master`** (Django Model: `DgItemMaster`)
    *   `Id` (PK)
    *   `CId` (Category ID, used to determine if `ItemCode` or `PartNo` is displayed)
    *   `ItemCode`
    *   `PartNo`
    *   `ManfDesc` (Manufacturer Description)
    *   `UOMBasic` (FK to `Unit_Master.Id`)

*   **`Unit_Master`** (Django Model: `UnitMaster`)
    *   `Id` (PK)
    *   `Symbol` (Unit of Measure symbol)

*   **`tblHR_OfficeStaff`** (Django Model: `HrOfficeStaff`)
    *   `EmpId` (PK)
    *   `Title` (e.g., Mr., Ms.)
    *   `EmployeeName`
    *   `CompId`

*   **`SD_Cust_WorkOrder_Master`** (Django Model: `SdCustWorkOrderMaster`)
    *   `WONo` (PK)
    *   `CompId`
    *   `FinYearId`
    *   `TaskProjectTitle` (Project Name)
    *   `CustomerId` (FK to `SD_Cust_Master.CustomerId`)

*   **`SD_Cust_Master`** (Django Model: `SdCustMaster`)
    *   `CustomerId` (PK)
    *   `CustomerName`

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data aggregation logic within the ASP.NET code-behind.

**Instructions:**

*   **Create/Update/Delete:** This page does **not** perform CRUD operations on Material Credit Notes. Its sole purpose is to **Read** and display a report.
*   **Read/Report Generation:**
    *   The `Page_Init` method is responsible for fetching all data.
    *   It takes `WONo`, `WOId`, `Id` (MCN Master ID), and `Key` (session key) from query strings.
    *   It takes `username`, `compid`, `finyear` from session.
    *   It performs a series of interconnected SQL queries (effectively a complex join) to gather:
        *   MCN master and detail information (`tblPM_MaterialCreditNote_Master`, `tblPM_MaterialCreditNote_Details`).
        *   QA quantity (`tblQc_AuthorizedMCN`).
        *   Bill of Material quantity (`tblDG_BOM_Master`).
        *   Item details (code, description, UOM) from `tblDG_Item_Master` and `Unit_Master`.
        *   Employee details (`tblHR_OfficeStaff`) for the "checked by" person.
        *   Work Order, Customer, and Project details from `SD_Cust_WorkOrder_Master` and `SD_Cust_Master`.
    *   It then populates a `DataTable` and sets it as the data source for the Crystal Report, passing several parameters.
*   **Navigation:** The `Cancel_Click` event redirects the user to a Material Credit Note details page, implying that this report is launched from a related "manage" or "details" view.
*   **Session State:** The Crystal Report object itself is stored in the session (`Session[Key] = report`). In Django, we will aim for statelessness and re-fetch/aggregate data on demand, or pass necessary identifiers.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern Django/HTMX/Alpine.js equivalents.

**Instructions:**

*   **CrystalReportViewer:** This is the primary display component. In Django, this will be replaced by a standard HTML `<table>` that dynamically renders the aggregated report data.
*   **`asp:Panel`:** Used for layout and scrollability. This will be replaced by standard HTML `<div>` elements with Tailwind CSS for styling and layout.
*   **`asp:Button` (`Cancel`):** A standard submit button. This will be an HTML `<button>` with a Django URL link, possibly using HTMX if it needs to trigger a partial update or a specific navigation flow. Given it's a full redirect, a standard link or form post will suffice. For consistency, we will make it `hx-get` to the previous page.
*   **Layout:** The `<table>` structure for the title and report viewer will be replaced by modern Flexbox/Grid layouts using Tailwind CSS.
*   **No user input forms** are present on this page, only display.

### Step 4: Generate Django Code

We'll organize the Django code within an application named `project_management`.

#### 4.1 Models (`project_management/models.py`)

We'll define the necessary models corresponding to the identified database tables. Foreign keys will be set up where relationships are clear. We'll add a custom manager to `MaterialCreditNoteMaster` to encapsulate the complex report data aggregation logic.

```python
from django.db import models
from django.db.models import F
from django.utils import timezone

# Assuming a central 'company' app for Company and Financial Year data
# and 'auth_app' for user/staff details if not using Django's default User.
# For simplicity, we'll assume direct table mapping for now.

class Company(models.Model):
    # Dummy model for CompId, replace with your actual Company model
    # if it exists in another app or is named differently.
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255, db_column='CompanyName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Replace with actual table name if different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class FinancialYear(models.Model):
    # Dummy model for FinYearId, replace with your actual Financial Year model
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(max_length=50, db_column='FinYearName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancialYearMaster' # Replace with actual table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name or f"Financial Year {self.id}"

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class DgItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    cid = models.IntegerField(db_column='CId', blank=True, null=True) # Category ID
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True) # Manufacturer Description
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or self.part_no or f"Item {self.id}"

    @property
    def display_item_code(self):
        """Returns ItemCode if CId is present, otherwise PartNo."""
        return self.item_code if self.cid is not None else self.part_no

class HrOfficeStaff(models.Model):
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is string based on ASP.NET 'SessionId'
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name or ''}".strip()

class SdCustMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) # Assuming CustomerId is string
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class SdCustWorkOrderMaster(models.Model):
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=100) # Assuming WONo is PK string
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    fin_year_id = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255, blank=True, null=True)
    customer = models.ForeignKey(SdCustMaster, models.DO_NOTHING, db_column='CustomerId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Customer Work Order Master'
        verbose_name_plural = 'Customer Work Order Masters'

    def __str__(self):
        return self.wono

class DgBomMaster(models.Model):
    # No explicit PK found, assuming a composite key or autoincrement ID
    # For now, let Django handle auto-incrementing PK 'id'
    wono = models.CharField(db_column='WONo', max_length=100, blank=True, null=True) # FK to SdCustWorkOrderMaster.wono
    fin_year_id = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    pid = models.IntegerField(db_column='PId', blank=True, null=True) # Product ID
    cid = models.IntegerField(db_column='CId', blank=True, null=True) # Component ID
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True) # BOM Quantity

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for WO: {self.wono}, PId: {self.pid}, CId: {self.cid}"

class MaterialCreditNoteMasterManager(models.Manager):
    def get_report_data(self, mcn_id, wono, comp_id, fin_year_id):
        try:
            mcn_master = self.get(
                id=mcn_id,
                wono=wono,
                comp_id=comp_id,
                fin_year_id=fin_year_id
            )

            # Get master details
            emp_name = mcn_master.session_id.get_checked_by_name() if mcn_master.session_id else "N/A"
            customer_info = mcn_master.wono_ref.customer.get_customer_info() if mcn_master.wono_ref and mcn_master.wono_ref.customer else "N/A"
            project_title = mcn_master.wono_ref.task_project_title if mcn_master.wono_ref else "N/A"

            report_lines = []
            mcn_details = mcn_master.materialcreditnotedetail_set.all()

            for detail in mcn_details:
                item_data = DgItemMaster.objects.filter(
                    id__in=[detail.pid, detail.cid] # Assuming PId/CId map to DgItemMaster.Id
                ).first() # Get the item if PId or CId is an ItemId

                if not item_data:
                    # Handle case where item_data is not found
                    continue

                bom_qty = DgBomMaster.objects.filter(
                    wono=wono,
                    fin_year_id=fin_year_id,
                    comp_id=comp_id,
                    pid=detail.pid,
                    cid=detail.cid
                ).values_list('qty', flat=True).first() or 0.0

                qa_qty_obj = QcAuthorizedMCN.objects.filter(
                    fin_year_id=fin_year_id,
                    comp_id=comp_id,
                    mcn_id=mcn_id,
                    mcnd_id=detail.id
                ).values_list('qa_qty', flat=True).first()
                qa_qty = qa_qty_obj or 0.0

                report_lines.append({
                    'id': mcn_master.id,
                    'item_code': item_data.display_item_code,
                    'description': item_data.manf_desc,
                    'uom': item_data.uom_basic.symbol if item_data.uom_basic else 'N/A',
                    'bom_qty': float(bom_qty),
                    'mcn_qty': float(detail.mcn_qty),
                    'mcn_no': mcn_master.mcn_no,
                    'mcn_date': mcn_master.sys_date.strftime('%d/%m/%Y'),
                    'qa_qty': float(qa_qty),
                    'comp_id': mcn_master.comp_id.id,
                })

            return {
                'master': mcn_master,
                'details': report_lines,
                'customer_info': customer_info,
                'project_title': project_title,
                'emp_name': emp_name,
                # Company address should ideally come from the Company model
                'company_address': mcn_master.comp_id.address if hasattr(mcn_master.comp_id, 'address') else "Company Address Not Available",
            }
        except self.model.DoesNotExist:
            return None
        except Exception as e:
            # Log the exception for debugging
            print(f"Error fetching report data: {e}")
            return None

class MaterialCreditNoteMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.ForeignKey(HrOfficeStaff, models.DO_NOTHING, db_column='SessionId', blank=True, null=True) # Assuming EmpId is char/varchar
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    mcn_no = models.CharField(db_column='MCNNo', max_length=100, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=100, blank=True, null=True) # Will be used for lookup to SdCustWorkOrderMaster
    fin_year_id = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)

    objects = MaterialCreditNoteMasterManager() # Assign custom manager

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Master'
        verbose_name = 'Material Credit Note Master'
        verbose_name_plural = 'Material Credit Note Masters'

    def __str__(self):
        return self.mcn_no or f"MCN {self.id}"

    @property
    def wono_ref(self):
        """Helper to get the SdCustWorkOrderMaster object via WONo string."""
        return SdCustWorkOrderMaster.objects.filter(wono=self.wono).first()

class MaterialCreditNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # This is MCNDId
    m_id = models.ForeignKey(MaterialCreditNoteMaster, models.DO_NOTHING, db_column='MId') # FK to MCN Master
    pid = models.IntegerField(db_column='PId', blank=True, null=True)
    cid = models.IntegerField(db_column='CId', blank=True, null=True)
    mcn_qty = models.DecimalField(db_column='MCNQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Details'
        verbose_name = 'Material Credit Note Detail'
        verbose_name_plural = 'Material Credit Note Details'

    def __str__(self):
        return f"MCN Detail {self.id} for MCN {self.m_id_id}"

class QcAuthorizedMCN(models.Model):
    # No explicit PK found, assuming a composite key or autoincrement ID
    # For now, let Django handle auto-incrementing PK 'id'
    qa_qty = models.DecimalField(db_column='QAQty', max_digits=18, decimal_places=3, blank=True, null=True)
    fin_year_id = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    mcn_id = models.ForeignKey(MaterialCreditNoteMaster, models.DO_NOTHING, db_column='MCNId', blank=True, null=True)
    mcnd_id = models.ForeignKey(MaterialCreditNoteDetail, models.DO_NOTHING, db_column='MCNDId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_AuthorizedMCN'
        verbose_name = 'QC Authorized MCN'
        verbose_name_plural = 'QC Authorized MCNs'

    def __str__(self):
        return f"QA Qty for MCN Detail {self.mcnd_id_id}: {self.qa_qty}"

```

#### 4.2 Forms (`project_management/forms.py`)

Since this page is a report display and does not involve user input to create or modify Material Credit Notes, a `ModelForm` is not required for this specific functionality. We will omit `forms.py` for this modernization task as it's not applicable to a print report view. If CRUD operations on MCNs were to be introduced elsewhere, a `MaterialCreditNoteForm` would be created.

#### 4.3 Views (`project_management/views.py`)

We will use a `TemplateView` or a custom `DetailView` to fetch and display the report. The data aggregation logic will be delegated to the `MaterialCreditNoteMasterManager`. The view will fetch parameters from the URL and session, similar to the ASP.NET `Page_Init`.

```python
from django.views.generic import TemplateView
from django.urls import reverse
from django.shortcuts import redirect
from django.http import Http404
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming authentication
from .models import MaterialCreditNoteMaster, Company # Import Company for address lookup

class MaterialCreditNoteReportView(LoginRequiredMixin, TemplateView):
    template_name = 'project_management/materialcreditnote/report.html'
    
    # Keeping view method thin - logic moved to model manager
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters from URL and session, similar to ASP.NET Page_Init
        mcn_id = self.request.GET.get('Id')
        wono = self.request.GET.get('WONo')
        wo_id = self.request.GET.get('WOId') # Not directly used in report, but for redirect
        
        # Session data
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        
        if not all([mcn_id, wono, comp_id, fin_year_id]):
            raise Http404("Missing required parameters for Material Credit Note Report.")
            
        try:
            # Use the custom manager to get all aggregated report data
            report_data = MaterialCreditNoteMaster.objects.get_report_data(
                mcn_id=int(mcn_id),
                wono=wono,
                comp_id=int(comp_id),
                fin_year_id=int(fin_year_id)
            )
            
            if not report_data:
                raise Http404("Material Credit Note report data not found or could not be generated.")

            context['report'] = report_data
            context['mcn_master'] = report_data['master']
            context['mcn_details'] = report_data['details']
            context['customer_info'] = report_data['customer_info']
            context['project_title'] = report_data['project_title']
            context['emp_name'] = report_data['emp_name']
            
            # Company Address: This was 'fun.CompAdd(CompId)'.
            # We assume Company model might have an address field or a method.
            # Example: Retrieve company address from Company model
            try:
                company_obj = Company.objects.get(id=comp_id)
                context['company_address'] = company_obj.name # Or company_obj.address
            except Company.DoesNotExist:
                context['company_address'] = "Company Address Not Available"
            
            # Parameters for Cancel button redirect
            context['won_o_for_redirect'] = wono
            context['wo_id_for_redirect'] = wo_id

        except ValueError:
            raise Http404("Invalid parameter format.")
        except Exception as e:
            # Log this error properly in a real application
            print(f"Error fetching MCN report: {e}")
            raise Http404("An error occurred while generating the report.")
            
        return context

# A simple redirect view for the "Cancel" button, similar to ASP.NET Response.Redirect
class MaterialCreditNoteCancelView(TemplateView):
    def get(self, request, *args, **kwargs):
        wono = request.GET.get('WONo')
        wo_id = request.GET.get('WOId')
        # Assuming the target ASP.NET page maps to a Django URL 'mcn_print_details_list'
        # with similar query parameters, or this is a placeholder.
        # This will redirect to a Django list view for Material Credit Notes.
        # For simplicity, we'll assume it goes back to a hypothetical MCN list view.
        return redirect(reverse('project_management:mcn_list') + f'?WONo={wono}&WOId={wo_id}')

```

#### 4.4 Templates (`project_management/templates/project_management/materialcreditnote/report.html`)

This template will render the report data dynamically. Since it's a "print report", a simple, well-formatted table is appropriate. We'll use DataTables for client-side functionality if this was a list view, but for a detailed report that's likely printed, a static table is fine. If printing to PDF, styling might be more complex. For this example, we'll render a printable HTML table. The "Cancel" button will use HTMX to potentially navigate back without a full page reload if desired, though a standard redirect is also simple.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl print:p-0">
    <div class="bg-white p-6 rounded-lg shadow-lg print:shadow-none print:p-0">
        <!-- Report Header Section -->
        <div class="mb-6 border-b pb-4 print:border-none print:mb-0">
            <div class="text-center mb-4">
                <h1 class="text-3xl font-bold text-gray-800">{{ company_address }}</h1> {# Company Address #}
                <h2 class="text-xl font-semibold text-gray-700 mt-2">Material Credit Note [MCN] - Print</h2>
            </div>
            <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                    <p><strong>Work Order No:</strong> {{ mcn_master.wono }}</p>
                    <p><strong>Project:</strong> {{ project_title }}</p>
                    <p><strong>Customer:</strong> {{ customer_info }}</p>
                </div>
                <div class="text-right">
                    <p><strong>MCN No:</strong> {{ mcn_master.mcn_no }}</p>
                    <p><strong>MCN Date:</strong> {{ mcn_master.sys_date|date:"d/m/Y" }}</p>
                    <p><strong>Prepared By:</strong> {{ emp_name }}</p>
                </div>
            </div>
        </div>

        <!-- Report Details Table -->
        <div class="overflow-x-auto print:overflow-visible">
            <table class="min-w-full bg-white border border-gray-300">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-2 px-4 border-b border-gray-300 text-left text-xs font-medium text-gray-600 uppercase">SN</th>
                        <th class="py-2 px-4 border-b border-gray-300 text-left text-xs font-medium text-gray-600 uppercase">Item Code</th>
                        <th class="py-2 px-4 border-b border-gray-300 text-left text-xs font-medium text-gray-600 uppercase">Description</th>
                        <th class="py-2 px-4 border-b border-gray-300 text-left text-xs font-medium text-gray-600 uppercase">UOM</th>
                        <th class="py-2 px-4 border-b border-gray-300 text-right text-xs font-medium text-gray-600 uppercase">BOM Qty</th>
                        <th class="py-2 px-4 border-b border-gray-300 text-right text-xs font-medium text-gray-600 uppercase">MCN Qty</th>
                        <th class="py-2 px-4 border-b border-gray-300 text-right text-xs font-medium text-gray-600 uppercase">QA Qty</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in mcn_details %}
                    <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                        <td class="py-2 px-4 border-b border-gray-300 text-sm">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-300 text-sm">{{ detail.item_code }}</td>
                        <td class="py-2 px-4 border-b border-gray-300 text-sm">{{ detail.description }}</td>
                        <td class="py-2 px-4 border-b border-gray-300 text-sm">{{ detail.uom }}</td>
                        <td class="py-2 px-4 border-b border-gray-300 text-right text-sm">{{ detail.bom_qty|floatformat:3 }}</td>
                        <td class="py-2 px-4 border-b border-gray-300 text-right text-sm">{{ detail.mcn_qty|floatformat:3 }}</td>
                        <td class="py-2 px-4 border-b border-gray-300 text-right text-sm">{{ detail.qa_qty|floatformat:3 }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="py-4 px-4 text-center text-gray-500">No MCN details found for this report.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex justify-center space-x-4 print:hidden">
            <button
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'project_management:mcn_cancel_report' %}?WONo={{ won_o_for_redirect }}&WOId={{ wo_id_for_redirect }}"
                hx-trigger="click"
                hx-target="body" hx-swap="outerHTML"> {# Can be 'body' to replace whole content or directly redirect via hx-redirect #}
                Cancel
            </button>
            <button
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                onclick="window.print()">
                Print Report
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components specific to this report, if needed
        // For example, if you had toggles or interactive elements.
    });
</script>
{% endblock %}
```

#### 4.5 URLs (`project_management/urls.py`)

Define the URL pattern for the report view and the cancel redirect.

```python
from django.urls import path
from .views import MaterialCreditNoteReportView, MaterialCreditNoteCancelView

app_name = 'project_management' # Namespace for this app

urlpatterns = [
    # URL for displaying the MCN report
    path('material_credit_note/report/', MaterialCreditNoteReportView.as_view(), name='mcn_report'),
    
    # URL for the Cancel button redirect
    path('material_credit_note/cancel_report/', MaterialCreditNoteCancelView.as_view(), name='mcn_cancel_report'),
    
    # Placeholder for the MCN list/details page that the cancel button redirects to
    # You would typically have a dedicated list view for MCNs, e.g.:
    # path('material_credit_note/list/', MaterialCreditNoteListView.as_view(), name='mcn_list'),
    # For now, this is a target for redirection.
    path('material_credit_note/details_list/', lambda request: redirect('/some-other-mcn-list-page/'), name='mcn_list'), # Placeholder
]
```

#### 4.6 Tests (`project_management/tests.py`)

Comprehensive unit tests for model methods (especially the custom manager's `get_report_data`) and integration tests for the views will ensure reliability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime
from .models import (
    Company, FinancialYear, UnitMaster, DgItemMaster, HrOfficeStaff,
    SdCustMaster, SdCustWorkOrderMaster, DgBomMaster,
    MaterialCreditNoteMaster, MaterialCreditNoteDetail, QcAuthorizedMCN
)

class MaterialCreditNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.uom = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item1 = DgItemMaster.objects.create(id=101, cid=1, item_code='ITEM001', part_no='PART001', manf_desc='Test Item Description 1', uom_basic=cls.uom)
        cls.item2 = DgItemMaster.objects.create(id=102, cid=None, item_code='ITEM002', part_no='PART002', manf_desc='Test Item Description 2', uom_basic=cls.uom) # No CId
        cls.staff = HrOfficeStaff.objects.create(empid='EMP001', title='Mr.', employee_name='John Doe', comp_id=cls.company)
        cls.customer = SdCustMaster.objects.create(customer_id='CUST001', customer_name='Test Customer', comp_id=cls.company)
        cls.work_order = SdCustWorkOrderMaster.objects.create(wono='WO-123', comp_id=cls.company, fin_year_id=cls.fin_year, task_project_title='Project Alpha', customer=cls.customer)
        
        # Create BOM data
        DgBomMaster.objects.create(wono='WO-123', fin_year_id=cls.fin_year, comp_id=cls.company, pid=cls.item1.id, cid=cls.item1.id, qty=100.000)

        # Create MCN Master
        cls.mcn_master = MaterialCreditNoteMaster.objects.create(
            id=1, session_id=cls.staff, comp_id=cls.company, sys_date=timezone.now(), 
            mcn_no='MCN-001', wono='WO-123', fin_year_id=cls.fin_year
        )
        
        # Create MCN Detail
        cls.mcn_detail = MaterialCreditNoteDetail.objects.create(
            id=1, m_id=cls.mcn_master, pid=cls.item1.id, cid=cls.item1.id, mcn_qty=50.000
        )
        
        # Create QC Authorized MCN
        QcAuthorizedMCN.objects.create(
            qa_qty=45.000, fin_year_id=cls.fin_year, comp_id=cls.company, 
            mcn_id=cls.mcn_master, mcnd_id=cls.mcn_detail
        )

    def test_mcn_master_creation(self):
        self.assertEqual(self.mcn_master.mcn_no, 'MCN-001')
        self.assertEqual(self.mcn_master.session_id.employee_name, 'John Doe')
        self.assertEqual(self.mcn_master.comp_id.name, 'Test Company')

    def test_dg_item_master_display_item_code(self):
        self.assertEqual(self.item1.display_item_code, 'ITEM001') # Has CId
        self.assertEqual(self.item2.display_item_code, 'PART002') # No CId

    def test_get_report_data_success(self):
        report_data = MaterialCreditNoteMaster.objects.get_report_data(
            mcn_id=self.mcn_master.id,
            wono=self.mcn_master.wono,
            comp_id=self.mcn_master.comp_id.id,
            fin_year_id=self.mcn_master.fin_year_id.id
        )
        
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['master'].id, self.mcn_master.id)
        self.assertEqual(len(report_data['details']), 1)
        
        detail_line = report_data['details'][0]
        self.assertEqual(detail_line['item_code'], 'ITEM001')
        self.assertEqual(detail_line['bom_qty'], 100.0)
        self.assertEqual(detail_line['mcn_qty'], 50.0)
        self.assertEqual(detail_line['qa_qty'], 45.0)
        self.assertEqual(report_data['customer_info'], 'Test Customer [CUST001]')
        self.assertEqual(report_data['project_title'], 'Project Alpha')
        self.assertEqual(report_data['emp_name'], 'Mr. John Doe')
        
    def test_get_report_data_mcn_not_found(self):
        report_data = MaterialCreditNoteMaster.objects.get_report_data(
            mcn_id=999, wono='WO-XXX', comp_id=self.company.id, fin_year_id=self.fin_year.id
        )
        self.assertIsNone(report_data)

class MaterialCreditNoteReportViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St') # Added address
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.uom = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item1 = DgItemMaster.objects.create(id=101, cid=1, item_code='ITEM001', part_no='PART001', manf_desc='Test Item Description 1', uom_basic=cls.uom)
        cls.staff = HrOfficeStaff.objects.create(empid='EMP001', title='Mr.', employee_name='John Doe', comp_id=cls.company)
        cls.customer = SdCustMaster.objects.create(customer_id='CUST001', customer_name='Test Customer', comp_id=cls.company)
        cls.work_order = SdCustWorkOrderMaster.objects.create(wono='WO-123', comp_id=cls.company, fin_year_id=cls.fin_year, task_project_title='Project Alpha', customer=cls.customer)
        
        DgBomMaster.objects.create(wono='WO-123', fin_year_id=cls.fin_year, comp_id=cls.company, pid=cls.item1.id, cid=cls.item1.id, qty=100.000)

        cls.mcn_master = MaterialCreditNoteMaster.objects.create(
            id=1, session_id=cls.staff, comp_id=cls.company, sys_date=timezone.now(), 
            mcn_no='MCN-001', wono='WO-123', fin_year_id=cls.fin_year
        )
        
        cls.mcn_detail = MaterialCreditNoteDetail.objects.create(
            id=1, m_id=cls.mcn_master, pid=cls.item1.id, cid=cls.item1.id, mcn_qty=50.000
        )
        
        QcAuthorizedMCN.objects.create(
            qa_qty=45.000, fin_year_id=cls.fin_year, comp_id=cls.company, 
            mcn_id=cls.mcn_master, mcnd_id=cls.mcn_detail
        )

    def setUp(self):
        self.client = Client()
        # Simulate authenticated user and session variables
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.fin_year.id
        self.client.session['username'] = self.staff.empid # Though not directly used, good to mimic

    def test_report_view_success(self):
        url = reverse('project_management:mcn_report') + f'?Id={self.mcn_master.id}&WONo={self.mcn_master.wono}&WOId=1'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnote/report.html')
        self.assertContains(response, 'Material Credit Note [MCN] - Print')
        self.assertContains(response, self.mcn_master.mcn_no)
        self.assertContains(response, self.mcn_master.wono)
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'PCS')
        self.assertContains(response, '100.000') # BOM Qty
        self.assertContains(response, '50.000')  # MCN Qty
        self.assertContains(response, '45.000')  # QA Qty
        self.assertContains(response, 'Test Customer [CUST001]')
        self.assertContains(response, 'Project Alpha')
        self.assertContains(response, 'Mr. John Doe')
        self.assertContains(response, 'Test Company') # company_address
        
    def test_report_view_missing_parameters(self):
        url = reverse('project_management:mcn_report') + f'?Id={self.mcn_master.id}&WOId=1' # Missing WONo
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Http404

    def test_report_view_mcn_not_found(self):
        url = reverse('project_management:mcn_report') + f'?Id=999&WONo=NONEXISTENT&WOId=1'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_cancel_view_redirect(self):
        url = reverse('project_management:mcn_cancel_report') + f'?WONo={self.mcn_master.wono}&WOId=1'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('project_management:mcn_list') + f'?WONo={self.mcn_master.wono}&WOId=1')

    def test_report_view_with_htmx_cancel(self):
        url = reverse('project_management:mcn_report') + f'?Id={self.mcn_master.id}&WONo={self.mcn_master.wono}&WOId=1'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Simulate HTMX click on the cancel button
        cancel_url = reverse('project_management:mcn_cancel_report') + f'?WONo={self.mcn_master.wono}&WOId=1'
        htmx_response = self.client.get(cancel_url, HTTP_HX_REQUEST='true')
        
        # HTMX typically expects a status 204 (No Content) for a redirect,
        # and then the 'HX-Redirect' header would be sent if hx-redirect is used.
        # If it's hx-swap outerHTML to body, it might return content.
        # Given it's redirecting to a new page, 302 is standard for non-HTMX request.
        # For HTMX with hx-get, it might still return the content of the target.
        # If the cancel view is a direct redirect, it will be 302.
        # For a hard redirect, HX-Redirect header is also common:
        self.assertEqual(htmx_response.status_code, 302)
        self.assertRedirects(htmx_response, reverse('project_management:mcn_list') + f'?WONo={self.mcn_master.wono}&WOId=1')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Navigation:** The `Cancel` button uses `hx-get` to initiate a request to the `mcn_cancel_report` URL. The `hx-target="body"` and `hx-swap="outerHTML"` are shown as options, but for a full page navigation/redirect as per original ASP.NET, a simple `hx-get` to a redirecting view or `hx-redirect` would be effective. The current setup makes the `Cancel` button trigger a GET request to the cancel URL, which then issues a standard Django redirect (302).
*   **No Dynamic Report Sections:** The original ASP.NET page is primarily a static display once loaded, acting like a print preview. Therefore, complex HTMX partial updates for the report content itself are not required. If sections of the report needed to be dynamically loaded or filtered post-load, more `hx-get` and `hx-swap` would be employed on internal `div` elements.
*   **Alpine.js:** No specific client-side interactivity (like toggles, dynamic forms, or validation on the client for user input) was identified on the original "print report" page. Thus, Alpine.js usage is minimal, but the boilerplate `alpine:init` is included for future expansion.
*   **DataTables:** DataTables is typically used for list views with client-side searching, sorting, and pagination. This "print report" view is a detailed, aggregated report for a *single* MCN, not a list of MCNs or a general data table. Therefore, DataTables is not directly applied to this specific report output table but would be used on the `MaterialCreditNote_MCN_Print_Details.aspx` equivalent Django list view. For this report, a simple HTML table is sufficient, especially for printing.
*   **DRY Template Inheritance:** The `report.html` extends `core/base.html`, ensuring consistent header, footer, and CDN links (for Tailwind CSS, HTMX, Alpine.js, etc.) are inherited without repetition.

---

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete table/column/field names derived from the ASP.NET code.
*   **Fat Model, Thin View:** The complex data aggregation logic previously in `Page_Init` (C#) has been moved into the `MaterialCreditNoteMasterManager.get_report_data()` method, keeping the `MaterialCreditNoteReportView` lean (primarily responsible for fetching parameters and delegating data retrieval).
*   **Test Coverage:** Comprehensive tests for models and views are provided, aiming for high coverage by testing data retrieval, model properties, and view responses, including edge cases like missing parameters.
*   **Business Value:** This modernized Django application provides a more maintainable, scalable, and secure solution. By using the Django ORM, SQL injection risks from manual string concatenation are eliminated. HTMX and Alpine.js ensure a snappy, modern user experience without the complexity of a full JavaScript framework, reducing development and maintenance overhead. The structured `fat model, thin view` approach makes the business logic easily testable and reusable. The transition eliminates reliance on proprietary reporting tools like Crystal Reports, offering greater flexibility in report generation and delivery (e.g., direct HTML rendering, PDF generation via open-source libraries).