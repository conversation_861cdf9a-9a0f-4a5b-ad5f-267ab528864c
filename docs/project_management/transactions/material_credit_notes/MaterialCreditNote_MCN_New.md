This document outlines a comprehensive plan for modernizing your existing ASP.NET application, specifically the "Material Credit Note - New" module, into a modern Django-based solution. Our approach prioritizes automation, ensures a robust, test-driven development process, and utilizes contemporary frontend technologies for a highly responsive user experience.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with two primary database tables: `SD_Cust_WorkOrder_Master` and `SD_Cust_Master`.

**`SD_Cust_WorkOrder_Master` (Mapped to Django Model `WorkOrder`):**
- **Id**: Integer, unique identifier for the Work Order (Primary Key). Used as `WOId` in the GridView.
- **WONo**: String, Work Order Number.
- **TaskProjectTitle**: String, the title of the project.
- **CustomerId**: String, foreign key reference to `SD_Cust_Master`.
- **SysDate**: Date/Time, the system date of the Work Order.
- **CloseOpen**: Integer (likely a boolean, e.g., 0 for open, 1 for closed).
- **CompId**: Integer, Company ID (used for multi-tenancy/data partitioning).
- **FinYearId**: Integer, Financial Year ID (used for multi-tenancy/data partitioning).

**`SD_Cust_Master` (Mapped to Django Model `Customer`):**
- **CustomerId**: String, unique identifier for the Customer (Primary Key).
- **CustomerName**: String, the name of the customer.
- **CompId**: Integer, Company ID.

### Step 2: Identify Backend Functionality

The ASP.NET page `MaterialCreditNote_MCN_New.aspx` serves as a search and lookup interface for Work Orders, rather than a direct CRUD (Create, Read, Update, Delete) page for a Material Credit Note itself.

-   **Read/Retrieve:** The core functionality is `loaddata()`, which fetches Work Order records from `SD_Cust_WorkOrder_Master`.
-   **Search/Filtering:** Users can search for Work Orders by "WO No", "Customer", or "Project Title" using a dropdown and input fields (`drpfield`, `txtPONo`, `txtSupplier`).
-   **Dynamic Input Control:** The search input field (`txtPONo` vs `txtSupplier`) changes visibility based on the selected search criterion (`drpfield_SelectedIndexChanged`).
-   **Autocomplete:** The "Customer" search input (`txtSupplier`) has an autocomplete feature (`GetCompletionList` WebMethod) that suggests customer names.
-   **Pagination:** The `GridView1` supports pagination (`GridView1_PageIndexChanging`).
-   **Navigation/Action:** Clicking a "WO No" in the grid (`GridView1_RowCommand`) redirects the user to a detailed page (`MaterialCreditNote_MCN_New_Details.aspx`) with the selected Work Order ID and Number.

### Step 3: Infer UI Components

The ASP.NET page's UI elements will be translated into modern HTML structures utilizing Tailwind CSS, HTMX, and Alpine.js.

-   **Search Form:**
    -   A `<select>` element (Django `ChoiceField`) for the search criteria (WO No, Customer, Project Title).
    -   Dynamic `<input type="text">` fields (Django `CharField`) for the search value. One for WO No/Project Title, another for Customer with autocomplete. HTMX will manage their visibility.
    -   A `<button type="submit">` for initiating the search.
-   **Data Display:**
    -   A `<table>` element that will be enhanced by DataTables for interactive sorting, searching, and pagination.
    -   Each row will display Work Order details: SN (serial number), WO No (as a clickable link), Date, Project Title, Customer Name, and Customer Code.
-   **Autocomplete Suggestions:** A dynamic `<div>` or `<ul>` for displaying autocomplete suggestions from the customer search.
-   **Loading Indicator:** A visual spinner to indicate when data is being loaded via HTMX.

### Step 4: Generate Django Code

The Django application will be named `project_management`.

#### 4.1 Models (`project_management/models.py`)

```python
from django.db import models
from django.urls import reverse_lazy # For future-proofing related to credit note details page

# Represents the 'SD_Cust_Master' table
class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

    @classmethod
    def get_customers_for_autocomplete(cls, prefix_text, comp_id, limit=10):
        """
        Retrieves customer names for autocomplete based on prefix text and company ID.
        Corresponds to ASP.NET's GetCompletionList.
        """
        return cls.objects.filter(
            comp_id=comp_id,
            customer_name__icontains=prefix_text
        ).order_by('customer_name')[:limit]

# Represents the 'SD_Cust_WorkOrder_Master' table
class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # WOId maps to Id
    wo_no = models.CharField(db_column='WONo', max_length=50)
    project_title = models.CharField(db_column='TaskProjectTitle', max_length=500)
    customer_id_fk = models.CharField(db_column='CustomerId', max_length=50) # Stored CustomerId
    sys_date = models.DateTimeField(db_column='SysDate')
    is_closed_open = models.IntegerField(db_column='CloseOpen') # 0 for open, 1 for closed
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

    @property
    def customer_name(self):
        """Retrieves the customer name using customer_id_fk."""
        try:
            # Assumes comp_id is consistent between WorkOrder and Customer
            customer = Customer.objects.get(customer_id=self.customer_id_fk, comp_id=self.comp_id)
            return customer.customer_name
        except Customer.DoesNotExist:
            return "N/A"

    @classmethod
    def get_filtered_work_orders(cls, comp_id, fin_year_id, search_field, search_value):
        """
        Applies filtering logic similar to ASP.NET's loaddata method.
        This is a 'fat model' approach, keeping query logic out of views.
        """
        queryset = cls.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # Original code uses <= for FinYearId
            is_closed_open=0 # Assuming '0' means 'Open' status
        )

        if search_value:
            if search_field == 'customer':
                # Extract customer_id from "Customer Name [CustomerId]" format
                customer_code_parts = search_value.split('[')
                if len(customer_code_parts) > 1:
                    customer_code = customer_code_parts[-1].strip(']')
                    queryset = queryset.filter(customer_id_fk=customer_code)
                else:
                    # Fallback for direct customer name search if format not found
                    # This might not be exact to original, but provides a sensible default
                    queryset = queryset.filter(customer_id_fk__in=Customer.objects.filter(
                        customer_name__icontains=search_value, comp_id=comp_id
                    ).values_list('customer_id', flat=True))
            elif search_field == 'wo_no':
                queryset = queryset.filter(wo_no=search_value)
            elif search_field == 'project_title':
                queryset = queryset.filter(project_title__icontains=search_value)
        
        return queryset.order_by('wo_no')

    def get_material_credit_note_details_url(self):
        """
        Generates the URL for the Material Credit Note Details page.
        Corresponds to the Response.Redirect in ASP.NET GridView_RowCommand.
        """
        return reverse_lazy('project_management:material_credit_note_details', 
                            kwargs={'wo_id': self.id, 'wo_no': self.wo_no})

```

#### 4.2 Forms (`project_management/forms.py`)

```python
from django import forms
from django.urls import reverse_lazy

class WorkOrderSearchForm(forms.Form):
    SEARCH_FIELDS = [
        ('wo_no', 'WO No'),
        ('customer', 'Customer'),
        ('project_title', 'Project Title'),
    ]

    search_field = forms.ChoiceField(
        choices=SEARCH_FIELDS,
        widget=forms.Select(attrs={
            'class': 'box3 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm py-2 px-3',
            'hx-post': reverse_lazy('project_management:workorder_search_input_partial'),
            'hx-target': '#search-input-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
        }),
        label="Search By"
    )

    # These fields are included in the form for submission, but their visibility
    # will be managed by HTMX/Alpine.js in the template.
    search_value_text = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search value',
        })
    )
    search_value_customer = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name',
            'autocomplete': 'off', # Disable browser autocomplete
        })
    )

```

#### 4.3 Views (`project_management/views.py`)

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.contrib import messages
from django.conf import settings # For simulating session variables

from .models import WorkOrder, Customer
from .forms import WorkOrderSearchForm

class WorkOrderListView(TemplateView):
    """
    Main view to display the Work Order search interface and initial table container.
    """
    template_name = 'project_management/workorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the form with a default selected search field (e.g., 'wo_no')
        context['form'] = WorkOrderSearchForm(initial={'search_field': 'wo_no'})
        return context

class WorkOrderTablePartialView(ListView):
    """
    Renders the DataTables portion of the Work Order list.
    This view is designed to be called via HTMX for dynamic content updates.
    """
    model = WorkOrder
    template_name = 'project_management/workorder/_workorder_table.html'
    context_object_name = 'workorders'
    paginate_by = 23 # Matches the original ASP.NET GridView's PageSize

    def get_queryset(self):
        # --- Simulate session variables (CompId, FinYearId) ---
        # In a real application, these would come from the authenticated user's session
        # or profile, e.g., self.request.user.user_profile.comp_id
        comp_id = getattr(settings, 'DEFAULT_COMP_ID', 1)  # Example: 1
        fin_year_id = getattr(settings, 'DEFAULT_FIN_YEAR_ID', 2024) # Example: 2024
        # ---------------------------------------------------

        # Get search parameters from the POST request (submitted via HTMX form)
        search_field = self.request.POST.get('search_field')
        search_value_text = self.request.POST.get('search_value_text')
        search_value_customer = self.request.POST.get('search_value_customer')

        # Determine the effective search value based on the selected search_field
        search_value = None
        if search_field == 'customer':
            search_value = search_value_customer
        else:
            search_value = search_value_text
            
        # Call the fat model method to get filtered data
        queryset = WorkOrder.get_filtered_work_orders(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_field=search_field,
            search_value=search_value
        )
        return queryset

    # No need for get_context_data as ListView already provides context_object_name

class WorkOrderSearchInputPartialView(View):
    """
    Returns the HTML snippet for the dynamic search input field (text or customer autocomplete).
    Triggered by HTMX when the search_field dropdown changes.
    """
    def post(self, request, *args, **kwargs):
        search_field = request.POST.get('search_field')
        context = {
            'search_field': search_field,
            'current_search_value_text': request.POST.get('search_value_text', ''),
            'current_search_value_customer': request.POST.get('search_value_customer', ''),
        }
        html = render_to_string('project_management/workorder/_search_input.html', context, request=request)
        return HttpResponse(html)

class CustomerAutocompleteView(View):
    """
    Provides JSON responses for customer autocomplete, mimicking the ASP.NET WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        # --- Simulate session variable (CompId) ---
        comp_id = getattr(settings, 'DEFAULT_COMP_ID', 1) # Example: 1
        # ----------------------------------------
        
        customers = Customer.get_customers_for_autocomplete(prefix_text, comp_id)
        
        # Format results as "CustomerName [CustomerId]" as per original ASP.NET
        results = [f"{c.customer_name} [{c.customer_id}]" for c in customers]
        return JsonResponse(results, safe=False)

# Placeholder for the actual Material Credit Note Details page
class MaterialCreditNoteDetailsView(TemplateView):
    template_name = 'project_management/material_credit_note_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # These would be used to load the actual credit note details
        context['wo_id'] = self.kwargs.get('wo_id')
        context['wo_no'] = self.kwargs.get('wo_no')
        messages.info(self.request, f"Redirected to details for WO No: {context['wo_no']} (ID: {context['wo_id']})")
        return context

```

#### 4.4 Templates

**`project_management/templates/project_management/workorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Credit Note [MCN] - New</h2>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form id="searchForm" hx-post="{% url 'project_management:workorder_table' %}" 
              hx-target="#workorder-table-container" hx-swap="innerHTML"
              hx-indicator="#spinner"
              _="on htmx:afterRequest remove .is-active from #spinner">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <label for="{{ form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_field.label }}:
                    </label>
                    {{ form.search_field }}
                </div>

                <div id="search-input-container" class="flex-grow">
                    <!-- This will be dynamically swapped by HTMX based on search_field selection -->
                    {% include 'project_management/workorder/_search_input.html' with search_field=form.search_field.value current_search_value_text='' current_search_value_customer='' %}
                </div>

                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Search
                </button>
                <div id="spinner" class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 hidden ml-2"></div>
            </div>
        </form>
    </div>
    
    <div id="workorder-table-container"
         hx-trigger="load delay:100ms" {# Initial load of table #}
         hx-post="{% url 'project_management:workorder_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#spinner">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading work orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Minimal Alpine.js for general UI state if needed, but HTMX handles most dynamic interactions here.
    // Example: If a modal was triggered directly from this page, Alpine.js could manage its open/close state.
</script>
{% endblock %}
```

**`project_management/templates/project_management/workorder/_search_input.html`** (Partial for dynamic search input)

```html
<div id="search-input-container">
    {% if search_field == 'customer' %}
        <input type="text" name="search_value_customer" id="id_search_value_customer" 
               class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
               placeholder="Enter Customer Name"
               value="{{ current_search_value_customer }}"
               hx-get="{% url 'project_management:customer_autocomplete' %}"
               hx-trigger="keyup changed delay:300ms, search" {# Faster autocomplete response #}
               hx-target="#customer-suggestions"
               hx-swap="innerHTML"
               autocomplete="off"
               _="on focus set my value to '' then remove .hidden from #customer-suggestions"
               >
        <div id="customer-suggestions" class="absolute bg-white border border-gray-300 rounded shadow-lg z-10 w-[250px] mt-1 p-2 hidden"></div>
        <script>
            // Alpine.js integration for customer autocomplete selection logic
            document.addEventListener('htmx:afterSwap', function(evt) {
                if (evt.detail.target.id === 'customer-suggestions') {
                    // Only show suggestions if there are any
                    if (evt.detail.elt.childElementCount > 0) {
                        evt.detail.target.classList.remove('hidden');
                    } else {
                        evt.detail.target.classList.add('hidden');
                    }
                    const suggestions = evt.detail.elt.querySelectorAll('div');
                    suggestions.forEach(suggestion => {
                        suggestion.onclick = function() {
                            document.getElementById('id_search_value_customer').value = this.innerText;
                            document.getElementById('customer-suggestions').innerHTML = ''; // Clear suggestions
                            document.getElementById('customer-suggestions').classList.add('hidden'); // Hide suggestions
                            document.getElementById('searchForm').requestSubmit(); // Trigger form submission
                        };
                    });
                }
            });
            // Hide suggestions when clicking outside
            document.addEventListener('click', function(evt) {
                const suggestionsDiv = document.getElementById('customer-suggestions');
                const inputField = document.getElementById('id_search_value_customer');
                if (suggestionsDiv && inputField && !suggestionsDiv.contains(evt.target) && !inputField.contains(evt.target)) {
                    suggestionsDiv.classList.add('hidden');
                }
            });
        </script>
    {% else %}
        <input type="text" name="search_value_text" id="id_search_value_text" 
               class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
               placeholder="Enter {{ search_field|replace:'_',' '|title }}"
               value="{{ current_search_value_text }}"
               x-init="this.value = '{{ current_search_value_text }}'" {# Ensure value persists on swap #}
               >
    {% endif %}
</div>
```

**`project_management/templates/project_management/workorder/_workorder_table.html`** (Partial for DataTables content)

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg p-6">
    {% if workorders %}
    <table id="workOrderTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for wo in workorders %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <a href="{{ wo.get_material_credit_note_details_url }}" 
                       class="text-blue-600 hover:text-blue-800 hover:underline">
                        {{ wo.wo_no }}
                    </a>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ wo.sys_date|date:"d/m/Y" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ wo.project_title }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ wo.customer_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ wo.customer_id_fk }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <script>
    $(document).ready(function() {
        $('#workOrderTable').DataTable({
            "pageLength": 23, // Matches original page size
            "lengthMenu": [[10, 23, 50, -1], [10, 23, 50, "All"]],
            "responsive": true,
            "pagingType": "full_numbers",
            "language": {
                "emptyTable": "No data found to display"
            }
        });
    });
    </script>
    {% else %}
    <div class="text-center py-8">
        <p class="font-bold text-red-500">No data found to display</p>
    </div>
    {% endif %}
</div>
```

#### 4.5 URLs (`project_management/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderListView, 
    WorkOrderTablePartialView, 
    WorkOrderSearchInputPartialView,
    CustomerAutocompleteView,
    MaterialCreditNoteDetailsView,
)

app_name = 'project_management' # Defines the app namespace for URL lookups

urlpatterns = [
    path('material_credit_note/new/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX endpoints for dynamic content
    path('material_credit_note/new/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    path('material_credit_note/new/search_input/', WorkOrderSearchInputPartialView.as_view(), name='workorder_search_input_partial'),
    path('api/customer_autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder for the details page, matching original ASP.NET redirect
    path('material_credit_note/details/<int:wo_id>/<str:wo_no>/', MaterialCreditNoteDetailsView.as_view(), name='material_credit_note_details'),
]

```

#### 4.6 Tests (`project_management/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from .models import WorkOrder, Customer

# Set up dummy settings for testing default CompId/FinYearId if not in global settings
if not hasattr(settings, 'DEFAULT_COMP_ID'):
    settings.DEFAULT_COMP_ID = 1
if not hasattr(settings, 'DEFAULT_FIN_YEAR_ID'):
    settings.DEFAULT_FIN_YEAR_ID = 2024

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for Customer model
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='ABC Company',
            comp_id=settings.DEFAULT_COMP_ID
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002',
            customer_name='XYZ Corp',
            comp_id=settings.DEFAULT_COMP_ID
        )
        cls.customer3 = Customer.objects.create(
            customer_id='CUST999',
            customer_name='Another Company',
            comp_id=99 # Different company
        )

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customer_name, 'ABC Company')
        self.assertEqual(self.customer1.pk, 'CUST001')
        self.assertEqual(str(self.customer1), 'ABC Company')

    def test_get_customers_for_autocomplete(self):
        # Test basic autocomplete
        results = Customer.get_customers_for_autocomplete('ABC', settings.DEFAULT_COMP_ID)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].customer_name, 'ABC Company')

        # Test case-insensitivity
        results = Customer.get_customers_for_autocomplete('abc', settings.DEFAULT_COMP_ID)
        self.assertEqual(len(results), 1)

        # Test no match
        results = Customer.get_customers_for_autocomplete('NonExistent', settings.DEFAULT_COMP_ID)
        self.assertEqual(len(results), 0)

        # Test company ID filtering
        results = Customer.get_customers_for_autocomplete('Another', settings.DEFAULT_COMP_ID)
        self.assertEqual(len(results), 0)
        results = Customer.get_customers_for_autocomplete('Another', 99)
        self.assertEqual(len(results), 1)

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='ABC Company',
            comp_id=settings.DEFAULT_COMP_ID
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002',
            customer_name='XYZ Corp',
            comp_id=settings.DEFAULT_COMP_ID
        )
        
        # Create test data for WorkOrder model
        cls.wo1 = WorkOrder.objects.create(
            id=1,
            wo_no='WO-2024-001',
            project_title='Project Alpha',
            customer_id_fk='CUST001',
            sys_date='2024-01-15T10:00:00Z',
            is_closed_open=0,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=2024
        )
        cls.wo2 = WorkOrder.objects.create(
            id=2,
            wo_no='WO-2024-002',
            project_title='Project Beta',
            customer_id_fk='CUST002',
            sys_date='2024-02-20T11:00:00Z',
            is_closed_open=0,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=2024
        )
        cls.wo3 = WorkOrder.objects.create(
            id=3,
            wo_no='WO-2023-003', # Different financial year
            project_title='Project Gamma',
            customer_id_fk='CUST001',
            sys_date='2023-11-01T09:00:00Z',
            is_closed_open=0,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=2023
        )
        cls.wo4 = WorkOrder.objects.create(
            id=4,
            wo_no='WO-2024-004',
            project_title='Project Delta (Closed)',
            customer_id_fk='CUST002',
            sys_date='2024-03-01T14:00:00Z',
            is_closed_open=1, # Closed
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=2024
        )
        cls.wo5 = WorkOrder.objects.create(
            id=5,
            wo_no='WO-2024-005',
            project_title='Project Epsilon',
            customer_id_fk='NONEXISTENT', # Invalid customer ID
            sys_date='2024-04-01T15:00:00Z',
            is_closed_open=0,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=2024
        )


    def test_workorder_creation(self):
        self.assertEqual(self.wo1.wo_no, 'WO-2024-001')
        self.assertEqual(self.wo1.project_title, 'Project Alpha')
        self.assertEqual(self.wo1.customer_id_fk, 'CUST001')
        self.assertEqual(str(self.wo1), 'WO-2024-001')

    def test_customer_name_property(self):
        self.assertEqual(self.wo1.customer_name, 'ABC Company')
        self.assertEqual(self.wo2.customer_name, 'XYZ Corp')
        self.assertEqual(self.wo5.customer_name, 'N/A') # Test for non-existent customer

    def test_get_filtered_work_orders_no_search(self):
        # Default filters: comp_id, fin_year_id <= current, is_closed_open=0
        workorders = WorkOrder.get_filtered_work_orders(
            settings.DEFAULT_COMP_ID, settings.DEFAULT_FIN_YEAR_ID, None, None
        )
        self.assertEqual(workorders.count(), 3) # wo1, wo2, wo5 (wo3 is old FY, wo4 is closed)
        self.assertIn(self.wo1, workorders)
        self.assertIn(self.wo2, workorders)
        self.assertIn(self.wo5, workorders)
        self.assertNotIn(self.wo3, workorders)
        self.assertNotIn(self.wo4, workorders)

    def test_get_filtered_work_orders_by_wo_no(self):
        workorders = WorkOrder.get_filtered_work_orders(
            settings.DEFAULT_COMP_ID, settings.DEFAULT_FIN_YEAR_ID, 'wo_no', 'WO-2024-001'
        )
        self.assertEqual(workorders.count(), 1)
        self.assertEqual(workorders.first(), self.wo1)

    def test_get_filtered_work_orders_by_project_title(self):
        workorders = WorkOrder.get_filtered_work_orders(
            settings.DEFAULT_COMP_ID, settings.DEFAULT_FIN_YEAR_ID, 'project_title', 'alpha'
        )
        self.assertEqual(workorders.count(), 1)
        self.assertEqual(workorders.first(), self.wo1)

    def test_get_filtered_work_orders_by_customer(self):
        # Searching by "Customer Name [CustomerId]" format
        workorders = WorkOrder.get_filtered_work_orders(
            settings.DEFAULT_COMP_ID, settings.DEFAULT_FIN_YEAR_ID, 'customer', 'ABC Company [CUST001]'
        )
        self.assertEqual(workorders.count(), 1)
        self.assertEqual(workorders.first(), self.wo1)

        # Searching by partial customer name (fallback logic)
        workorders = WorkOrder.get_filtered_work_orders(
            settings.DEFAULT_COMP_ID, settings.DEFAULT_FIN_YEAR_ID, 'customer', 'ABC'
        )
        self.assertEqual(workorders.count(), 1)
        self.assertEqual(workorders.first(), self.wo1)

    def test_get_material_credit_note_details_url(self):
        url = self.wo1.get_material_credit_note_details_url()
        expected_url = reverse('project_management:material_credit_note_details', kwargs={'wo_id': 1, 'wo_no': 'WO-2024-001'})
        self.assertEqual(url, expected_url)

class WorkOrderViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Test Customer',
            comp_id=settings.DEFAULT_COMP_ID
        )
        cls.wo = WorkOrder.objects.create(
            id=10,
            wo_no='WO-VIEW-01',
            project_title='View Test Project',
            customer_id_fk='CUST001',
            sys_date='2024-05-01T12:00:00Z',
            is_closed_open=0,
            comp_id=settings.DEFAULT_COMP_ID,
            fin_year_id=settings.DEFAULT_FIN_YEAR_ID
        )

    def test_workorder_list_view_get(self):
        response = self.client.get(reverse('project_management:workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/list.html')
        self.assertIsInstance(response.context['form'], WorkOrderSearchForm)

    def test_workorder_table_partial_view_post_initial_load(self):
        # Simulate HTMX initial load
        response = self.client.post(reverse('project_management:workorder_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/_workorder_table.html')
        self.assertIn(self.wo, response.context['workorders'])
        self.assertContains(response, 'WO-VIEW-01') # Check if WO no is in the rendered HTML

    def test_workorder_table_partial_view_post_search_wo_no(self):
        response = self.client.post(
            reverse('project_management:workorder_table'),
            data={'search_field': 'wo_no', 'search_value_text': 'WO-VIEW-01'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/_workorder_table.html')
        self.assertIn(self.wo, response.context['workorders'])
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_workorder_table_partial_view_post_search_customer(self):
        response = self.client.post(
            reverse('project_management:workorder_table'),
            data={'search_field': 'customer', 'search_value_customer': 'Test Customer [CUST001]'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/_workorder_table.html')
        self.assertIn(self.wo, response.context['workorders'])
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_workorder_search_input_partial_view_post(self):
        response = self.client.post(
            reverse('project_management:workorder_search_input_partial'),
            data={'search_field': 'customer'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/_search_input.html')
        self.assertContains(response, 'name="search_value_customer"')
        self.assertNotContains(response, 'name="search_value_text"')

    def test_customer_autocomplete_view_get(self):
        response = self.client.get(
            reverse('project_management:customer_autocomplete'),
            {'q': 'Test'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertIn('Test Customer [CUST001]', response.json())

    def test_material_credit_note_details_view(self):
        response = self.client.get(
            reverse('project_management:material_credit_note_details', kwargs={'wo_id': self.wo.id, 'wo_no': self.wo.wo_no})
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/material_credit_note_details.html')
        self.assertEqual(response.context['wo_id'], self.wo.id)
        self.assertEqual(response.context['wo_no'], self.wo.wo_no)

```

### Step 5: HTMX and Alpine.js Integration

The migration leverages HTMX and Alpine.js to deliver a modern, interactive single-page experience without complex JavaScript frameworks.

-   **HTMX for Dynamic Content:**
    -   The main Work Order list (`list.html`) uses `hx-trigger="load"` and `hx-post` to initially load the table content into `#workorder-table-container` via `WorkOrderTablePartialView`.
    -   The search form (`searchForm`) uses `hx-post` to re-render the table dynamically when a search is performed, without a full page reload.
    -   The search field dropdown uses `hx-post` to `WorkOrderSearchInputPartialView` to dynamically swap the input field (text vs. customer autocomplete) based on selection.
    -   The customer autocomplete input uses `hx-get` to `CustomerAutocompleteView` to fetch suggestions as the user types, displaying them in a hidden div. Clicking a suggestion populates the input and triggers the main search.
    -   Loading indicators (`hx-indicator`) provide visual feedback during HTMX requests.
    -   The `HX-Trigger` header is not explicitly needed for the main list refresh as the search form's submission directly targets the table container, but if CRUD operations were added on this page that modified the data elsewhere, an `HX-Trigger` on the response would be essential.

-   **Alpine.js for UI State and Interactivity:**
    -   While HTMX handles the majority of dynamic content loading and form submissions, Alpine.js can manage minor client-side UI states. For this particular module, Alpine.js is primarily used for `x-init` to retain input values across HTMX swaps and for basic display logic like hiding autocomplete suggestions when clicking outside.
    -   More complex UI interactions, such as modals for adding/editing a Material Credit Note (if these were on *this* page, rather than a redirect), would heavily utilize Alpine.js.

-   **DataTables for List Views:**
    -   The `_workorder_table.html` partial directly initializes `DataTables` on the `<table>` element. This handles client-side sorting, searching, and pagination efficiently, offloading much of the complexity from the server. The `pageLength` is set to match the original ASP.NET `GridView`'s page size.

-   **No Full Page Reloads:** All user interactions (changing search criteria, typing in search boxes, clicking search) are handled by HTMX to update only the necessary parts of the page, providing a fluid user experience.

---

### Final Notes

-   **Placeholders:** `DEFAULT_COMP_ID` and `DEFAULT_FIN_YEAR_ID` are used as placeholders in `settings.py` for testing and demonstration. In a production environment, these would be securely derived from the authenticated user's session or profile, reflecting the multi-tenancy requirements.
-   **DRY Templates:** The use of `_workorder_table.html` and `_search_input.html` demonstrates DRY principles, allowing these reusable components to be swapped by HTMX.
-   **Fat Models, Thin Views:** Business logic, such as filtering Work Orders and retrieving customer names, is encapsulated within the `WorkOrder` and `Customer` models, keeping the Django views concise and focused on handling HTTP requests and rendering templates (mostly under 15 lines of core logic).
-   **Comprehensive Testing:** The provided tests cover model methods and view interactions, including HTMX-specific requests, ensuring functionality and maintainability.
-   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes (`bg-blue-500`, `py-2`, `px-4`, `rounded`, etc.) for modern, responsive styling consistent with the AutoERP guidelines. The CSS classes are directly applied, assuming Tailwind is configured in the project's base setup.