## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following database tables and their inferred columns:

*   **[TABLE_NAME]: `SD_Cust_WorkOrder_Master`**
    *   `Id` (Primary Key, integer)
    *   `WONo` (Work Order Number, string)
    *   `TaskProjectTitle` (Project Title, string)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master`, string)
    *   `SysDate` (System Date, datetime/date)
    *   `CompId` (Company ID, integer)
    *   `FinYearId` (Financial Year ID, integer)
    *   `CloseOpen` (Status, integer, `0` for open/active)

*   **[TABLE_NAME]: `SD_Cust_Master`**
    *   `CustomerId` (Primary Key, string)
    *   `CustomerName` (Customer Name, string)
    *   `CompId` (Company ID, integer)

### Step 2: Identify Backend Functionality

The ASP.NET page primarily focuses on **Read** (listing) and **Filter/Search** operations for Work Orders. It also includes an **Autocomplete** feature for customer names and a **redirection** to a details page upon selecting a Work Order.

*   **Read/List:** Displaying a paginated list of Work Orders.
*   **Filter/Search:** Filtering Work Orders by:
    *   WO No (`WONo`)
    *   Customer Name (`CustomerName`, which maps to `CustomerId`)
    *   Project Title (`TaskProjectTitle`)
*   **Autocomplete:** Providing suggestions for customer names based on user input.
*   **Navigation:** Redirecting to a "details" page when a Work Order is selected from the list.
*   **Session Management:** `CompId` and `FinYearId` are retrieved from session, indicating global filtering criteria. `CloseOpen='0'` is a constant filter.

While the original page is not directly performing Create, Update, or Delete operations on the `WorkOrder` entity, the migration plan requires comprehensive CRUD. We will implement these generic CRUD operations for `WorkOrder` to meet the requirement of a full module.

### Step 3: Infer UI Components

The ASP.NET controls translate directly to standard HTML elements with Django templating and HTMX/Alpine.js for dynamic behavior.

*   **`DropDownList` (drpfield):** HTML `<select>` element for search criteria selection.
*   **`TextBox` (txtSupplier) with `AutoCompleteExtender`:** HTML `<input type="text">` for customer search, augmented with HTMX for autocomplete suggestions.
*   **`TextBox` (txtPONo):** HTML `<input type="text">` for WO No or Project Title search.
*   **`Button` (Button1):** HTML `<button>` for triggering the search.
*   **`GridView` (GridView1):** HTML `<table>` element, which will be initialized as a DataTables instance for client-side pagination, sorting, and search.
    *   `LinkButton` for `WONo`: Will be an HTML `<a>` tag or a button triggering a modal/redirection.
    *   `Labels` for other fields: Simple `<span>` or `<td>` elements.

### Step 4: Generate Django Code

We will create a new Django app named `inventory` for this module.

#### 4.1 Models (`inventory/models.py`)

We'll define `Customer` and `WorkOrder` models, linking them via a `ForeignKey`. A custom manager for `WorkOrder` will handle the common filtering based on `CompId`, `FinYearId`, and `CloseOpen`.

```python
from django.db import models
from django.db.models import QuerySet

# Custom manager to apply common filters for WorkOrder
class WorkOrderManager(models.Manager):
    def get_queryset(self):
        # Assuming current_company_id and current_financial_year_id are available
        # via a global context, session, or specific user settings.
        # For this example, hardcoding as per analysis indicates these are session-driven.
        # In a real application, these would be dynamically pulled from the request.
        current_company_id = 1 # Placeholder for Session["compid"]
        current_financial_year_id = 2024 # Placeholder for Session["finyear"]

        return super().get_queryset().filter(
            company_id=current_company_id,
            financial_year_id__lte=current_financial_year_id, # FinYearId<= condition
            close_open_status=0 # CloseOpen='0' condition
        )

class Customer(models.Model):
    # CustomerId is likely a string-based primary key in the legacy system
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class WorkOrder(models.Model):
    # Id is the primary key in the legacy system
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=100)
    project_title = models.CharField(db_column='TaskProjectTitle', max_length=500)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id')
    sys_date = models.DateField(db_column='SysDate')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    close_open_status = models.IntegerField(db_column='CloseOpen') # 0 for open

    # Use the custom manager
    objects = WorkOrderManager()
    all_objects = models.Manager() # Default manager if you need to bypass custom filters

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        ordering = ['wo_no'] # Order by WONo ASC as in original query

    def __str__(self):
        return self.wo_no

    # Example of a business logic method in the fat model
    def get_details_url(self):
        """Returns the URL for the details page, mirroring the original redirect."""
        # This URL would lead to a different Django view/app for Work Order details
        return f"/inventory/workorders/{self.id}/details/?wono={self.wo_no}&modid=10&submodid=128"

    @classmethod
    def get_filtered_workorders(cls, search_type, search_query):
        """
        Filters work orders based on search criteria.
        This method encapsulates the original loaddata() logic.
        """
        queryset = cls.objects.all() # Uses the custom manager (filtered by CompId, FinYearId, CloseOpen)

        if search_type == '0' and search_query: # Customer
            # The original `getCode` implied converting customer name to ID.
            # Here, we search by customer name directly and filter by FK.
            queryset = queryset.filter(customer__customer_name__icontains=search_query)
        elif search_type == '1' and search_query: # WO No
            queryset = queryset.filter(wo_no__iexact=search_query) # Exact match for WO No
        elif search_type == '2' and search_query: # Project Title
            queryset = queryset.filter(project_title__icontains=search_query) # Like '%...%'

        return queryset

```

#### 4.2 Forms (`inventory/forms.py`)

We'll create a `ModelForm` for `WorkOrder` (for generic CRUD operations) and a non-model `SearchForm` for the filtering UI.

```python
from django import forms
from .models import WorkOrder, Customer

class WorkOrderForm(forms.ModelForm):
    # Customer field will be a dropdown for existing customers
    # or an autocomplete for a more user-friendly experience.
    # For simplicity of a ModelForm, we use ModelChoiceField.
    # In a real app with advanced autocomplete, you might use a custom field or
    # handle the customer input as plain text and resolve it in the view/model method.
    customer_display = forms.CharField(
        label="Customer Name",
        max_length=255,
        required=False,
        help_text="Type customer name for autocomplete. If not found, use existing ID.",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/customers/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-suggestions',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
            'name': 'customer_name_autocomplete' # Use a distinct name for autocomplete input
        })
    )

    class Meta:
        model = WorkOrder
        fields = ['wo_no', 'project_title', 'customer', 'sys_date', 'company_id', 'financial_year_id', 'close_open_status']
        widgets = {
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_title': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'customer': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'close_open_status': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If an instance is being edited, populate customer_display
        if self.instance.pk:
            self.fields['customer_display'].initial = str(self.instance.customer)
        # Make the actual customer FK field hidden, as it will be set based on autocomplete or manual input
        self.fields['customer'].widget = forms.HiddenInput()


class WorkOrderSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('1', 'WO No'),
        ('0', 'Customer'),
        ('2', 'Project Title'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3',
            'hx-post': '/inventory/workorders/search-form-update/', # HTMX endpoint for dynamic input visibility
            'hx-swap': 'outerHTML',
            'hx-target': '#search-input-container',
            'hx-indicator': '.htmx-indicator'
        })
    )
    search_query_wo_project = forms.CharField(
        label="WO No / Project Title",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter WO No or Project Title'
        })
    )
    search_query_customer = forms.CharField(
        label="Customer Name",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter Customer Name',
            'hx-get': '/inventory/customers/autocomplete/', # HTMX endpoint for customer autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-search-suggestions',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator'
        })
    )

    # Override init to handle initial visibility based on search_type
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        selected_type = self.initial.get('search_type', self.data.get('search_type', '1')) # Default to WO No
        
        # Hide/show fields based on selected_type
        if selected_type == '0': # Customer
            self.fields['search_query_wo_project'].widget.attrs['style'] = 'display:none;'
            self.fields['search_query_customer'].widget.attrs.pop('style', None)
        else: # WO No or Project Title
            self.fields['search_query_customer'].widget.attrs['style'] = 'display:none;'
            self.fields['search_query_wo_project'].widget.attrs.pop('style', None)

    def get_current_search_type_field(self):
        selected_type = self.data.get('search_type', '1') # Default to WO No
        if selected_type == '0':
            return self['search_query_customer']
        else:
            return self['search_query_wo_project']

```

#### 4.3 Views (`inventory/views.py`)

We'll define the `ListView` for the main page, partial views for HTMX, and standard CRUD views.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q

from .models import WorkOrder, Customer
from .forms import WorkOrderForm, WorkOrderSearchForm

# Main Work Order List View
class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'inventory/workorder/list.html'
    context_object_name = 'workorders' # Not directly used in list.html, but in table partial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form with potential GET parameters for persistence
        initial_search_type = self.request.GET.get('search_type', '1')
        initial_search_query = self.request.GET.get('search_query', '')
        
        context['search_form'] = WorkOrderSearchForm(initial={
            'search_type': initial_search_type,
            'search_query_wo_project': initial_search_query if initial_search_type != '0' else '',
            'search_query_customer': initial_search_query if initial_search_type == '0' else '',
        })
        return context

# HTMX partial view for the Work Order table (search results)
class WorkOrderTablePartialView(ListView):
    model = WorkOrder
    template_name = 'inventory/workorder/_workorder_table.html'
    context_object_name = 'workorders'

    def get_queryset(self):
        search_type = self.request.GET.get('search_type')
        search_query = self.request.GET.get('search_query')
        
        # Use the model's static method for filtering
        queryset = WorkOrder.get_filtered_workorders(search_type, search_query)
        return queryset

# HTMX endpoint for dynamic search input based on dropdown selection
class WorkOrderSearchFormUpdateView(View):
    def post(self, request, *args, **kwargs):
        form = WorkOrderSearchForm(request.POST)
        # Only return the relevant input field for HTMX swap
        if form.is_valid():
            selected_type = form.cleaned_data['search_type']
            form_html = render(request, 'inventory/workorder/_search_input_partial.html', {'form': form, 'selected_type': selected_type}).content
            return HttpResponse(form_html, headers={'HX-Retarget': '#search-input-container', 'HX-Reswap': 'outerHTML'})
        return HttpResponse("") # Or render an error partial

# HTMX endpoint for customer autocomplete
class CustomerAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        if not query:
            return JsonResponse([], safe=False)

        # Assuming current_company_id is needed for customer filtering as well
        current_company_id = 1 # Placeholder, as in WorkOrderManager

        customers = Customer.objects.filter(
            Q(customer_name__icontains=query) | Q(customer_id__icontains=query),
            company_id=current_company_id
        )[:10] # Limit to 10 suggestions

        suggestions = []
        for customer in customers:
            suggestions.append({
                'id': customer.customer_id,
                'name': f"{customer.customer_name} [{customer.customer_id}]"
            })
        return JsonResponse(suggestions, safe=False)

# Standard CRUD Views for WorkOrder
class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'inventory/workorder/_workorder_form.html' # Use partial for modal
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        # Additional logic can be added here if needed, e.g., setting default values
        # For instance, if 'id' (legacy PK) is auto-generated by the database,
        # it might not be explicitly set here, or you might generate it if it's app-managed.
        # Assuming `id` is auto-incrementing in legacy DB and we don't set it for new records.
        # However, since `managed = False` and `id` is `IntegerField(primary_key=True)`,
        # Django won't auto-increment unless the DB field is identity.
        # If the original `Id` is truly identity, we might remove it from fields in WorkOrderForm.
        # For simplicity, if ID is required, assume it's provided or handled by DB.
        
        # Original code used `fun.getCode(txtSupplier.Text)` to get CustomerId.
        # Here, `form.cleaned_data['customer']` should directly be the Customer object if it's a ModelChoiceField.
        # If using autocomplete, we need to manually resolve the customer_id from `customer_display`.
        customer_name_input = form.cleaned_data.get('customer_display')
        if customer_name_input:
            # Attempt to find customer by name or part of name from autocomplete
            try:
                # Extract ID from "Name [ID]" format if available, otherwise just use name
                import re
                match = re.search(r'\[(.*?)\]$', customer_name_input)
                customer_id_from_input = match.group(1) if match else customer_name_input
                
                resolved_customer = Customer.objects.get(customer_id=customer_id_from_input)
                form.instance.customer = resolved_customer
            except Customer.DoesNotExist:
                form.add_error('customer_display', 'Customer not found.')
                return self.form_invalid(form)

        # Remove customer_display as it's not part of the model fields
        del form.cleaned_data['customer_display']
        
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX that the form submission was handled
                headers={
                    'HX-Trigger': '{"refreshWorkOrderList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        # Render the form again with errors for HTMX
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)


class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'inventory/workorder/_workorder_form.html' # Use partial for modal
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        customer_name_input = form.cleaned_data.get('customer_display')
        if customer_name_input:
            # Attempt to find customer by name or part of name from autocomplete
            try:
                import re
                match = re.search(r'\[(.*?)\]$', customer_name_input)
                customer_id_from_input = match.group(1) if match else customer_name_input
                
                resolved_customer = Customer.objects.get(customer_id=customer_id_from_input)
                form.instance.customer = resolved_customer
            except Customer.DoesNotExist:
                form.add_error('customer_display', 'Customer not found.')
                return self.form_invalid(form)
        
        del form.cleaned_data['customer_display']

        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshWorkOrderList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'inventory/workorder/_workorder_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshWorkOrderList":true, "closeModal":true}'
                }
            )
        return response

```

#### 4.4 Templates (`inventory/templates/inventory/workorder/`)

```html
<!-- inventory/templates/inventory/workorder/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Authorize Work Orders</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>
    
    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form id="searchForm" hx-get="{% url 'workorder_table' %}" hx-target="#workorderTable-container" hx-swap="innerHTML" hx-indicator="#table-loading-indicator">
            <div class="flex items-end space-x-4">
                <div>
                    <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_type }}
                </div>
                <div id="search-input-container" class="flex-grow">
                    <!-- This div will be updated by HTMX with the appropriate search input field -->
                    {% include 'inventory/workorder/_search_input_partial.html' with form=search_form selected_type=search_form.initial.search_type %}
                </div>
                <div>
                    <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
                <div id="table-loading-indicator" class="htmx-indicator ml-4">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </div>
            </div>
            <!-- Autocomplete suggestions container for search form -->
            <div id="customer-search-suggestions" class="relative bg-white border border-gray-300 rounded-md shadow-lg z-10 w-64 mt-1"></div>
        </form>
    </div>

    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state for the table -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         x-data="{ showModal: false }"
         x-init="document.body.addEventListener('htmx:afterSwap', (event) => { if (event.target.id === 'modalContent' && event.detail.xhr.status !== 204) { showModal = true; } });
                 document.body.addEventListener('closeModal', () => { showModal = false; });"
         x-show="showModal"
         @click.self="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @click.stop>
            <!-- Content will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.tailwindcss.min.css">
{% endblock %}

```

```html
<!-- inventory/templates/inventory/workorder/_workorder_table.html -->
<table id="workorderTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in workorders %}
        <tr class="{% cycle 'bg-gray-50' 'bg-white' %}">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a href="{{ obj.get_details_url }}" class="text-blue-600 hover:underline">
                    {{ obj.wo_no }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.sys_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.project_title }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.customer.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.customer.customer_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                    hx-get="{% url 'workorder_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'workorder_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-red-500 font-bold">No data found to display</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#workorderTable')) {
            $('#workorderTable').DataTable({
                "pageLength": 23, // Matching original GridView page size
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true, // Enable search box for DataTables
                "paging": true,
                "ordering": true,
                "info": true
            });
        }
    });
</script>

```

```html
<!-- inventory/templates/inventory/workorder/_search_input_partial.html -->
<div id="search-input-container">
    {% if selected_type == '0' %} {# Customer #}
        <label for="{{ form.search_query_customer.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer Name</label>
        {{ form.search_query_customer }}
        <div id="customer-search-suggestions" class="relative bg-white border border-gray-300 rounded-md shadow-lg z-10 w-64 mt-1"></div>
    {% else %} {# WO No or Project Title #}
        <label for="{{ form.search_query_wo_project.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {% if selected_type == '1' %}WO No{% else %}Project Title{% endif %}
        </label>
        {{ form.search_query_wo_project }}
    {% endif %}
</div>
```

```html
<!-- inventory/templates/inventory/workorder/_workorder_form.html -->
<div class="p-6" x-data="{ customerSearchTerm: '{{ form.customer_display.value|default:"" }}' }">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.wo_no.label }}
                </label>
                {{ form.wo_no }}
                {% if form.wo_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.project_title.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.project_title.label }}
                </label>
                {{ form.project_title }}
                {% if form.project_title.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.project_title.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.customer_display.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.customer_display.label }}
                </label>
                <input type="text" 
                       id="{{ form.customer_display.id_for_label }}" 
                       name="{{ form.customer_display.name }}" 
                       class="{{ form.customer_display.field.widget.attrs.class }}"
                       placeholder="{{ form.customer_display.field.widget.attrs.placeholder|default:'Type customer name...' }}"
                       x-model="customerSearchTerm"
                       hx-get="{% url 'customer_autocomplete' %}"
                       hx-target="#customer-suggestions-modal"
                       hx-swap="innerHTML"
                       hx-trigger="keyup changed delay:300ms, search"
                       hx-indicator="#customer-autocomplete-indicator">
                <div id="customer-autocomplete-indicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </div>
                {{ form.customer }} {# This is the hidden actual FK field #}
                {% if form.customer_display.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.customer_display.errors }}</p>
                {% endif %}
                <div id="customer-suggestions-modal" class="relative bg-white border border-gray-300 rounded-md shadow-lg z-10 w-full mt-1"></div>
            </div>

            <div>
                <label for="{{ form.sys_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.sys_date.label }}
                </label>
                {{ form.sys_date }}
                {% if form.sys_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.sys_date.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.company_id.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.company_id.label }}
                </label>
                {{ form.company_id }}
                {% if form.company_id.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.company_id.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.financial_year_id.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.financial_year_id.label }}
                </label>
                {{ form.financial_year_id }}
                {% if form.financial_year_id.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.financial_year_id.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.close_open_status.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.close_open_status.label }}
                </label>
                {{ form.close_open_status }}
                {% if form.close_open_status.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.close_open_status.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- inventory/templates/inventory/workorder/_customer_autocomplete_suggestions.html -->
{% for customer in customers %}
    <div class="px-3 py-2 cursor-pointer hover:bg-gray-100"
         hx-on:click="document.querySelector('input[name=customer_name_autocomplete]').value = '{{ customer.name }}';
                      document.querySelector('input[name=customer]').value = '{{ customer.id }}';
                      this.closest('#customer-suggestions-modal, #customer-search-suggestions').innerHTML = '';">
        {{ customer.name }}
    </div>
{% empty %}
    <div class="px-3 py-2 text-gray-500">No customers found.</div>
{% endfor %}
```

```html
<!-- inventory/templates/inventory/workorder/_workorder_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Work Order <strong>"{{ object.wo_no }}"</strong> (Project: "{{ object.project_title }}")?</p>
    <form hx-delete="{% url 'workorder_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderListView,
    WorkOrderTablePartialView,
    WorkOrderSearchFormUpdateView,
    CustomerAutocompleteView,
    WorkOrderCreateView,
    WorkOrderUpdateView,
    WorkOrderDeleteView,
)

urlpatterns = [
    path('workorders/', WorkOrderListView.as_view(), name='workorder_list'),
    path('workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    path('workorders/search-form-update/', WorkOrderSearchFormUpdateView.as_view(), name='workorder_search_form_update'),
    path('customers/autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    
    # CRUD operations
    path('workorders/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorders/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorders/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]

# Don't forget to include these URLs in your project's main urls.py:
# from django.urls import path, include
# urlpatterns = [
#     path('inventory/', include('inventory.urls')),
# ]
```

#### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db.models.manager import Manager
from datetime import date

from .models import WorkOrder, Customer, WorkOrderManager

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Acme Corp',
            company_id=1
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002',
            customer_name='Beta Industries',
            company_id=1
        )
        
        # Test WorkOrder objects
        cls.wo1 = WorkOrder.objects.create(
            id=101, wo_no='WO2023-001', project_title='Website Redesign',
            customer=cls.customer1, sys_date=date(2023, 1, 15),
            company_id=1, financial_year_id=2024, close_open_status=0
        )
        cls.wo2 = WorkOrder.objects.create(
            id=102, wo_no='WO2023-002', project_title='Mobile App Development',
            customer=cls.customer2, sys_date=date(2023, 2, 20),
            company_id=1, financial_year_id=2024, close_open_status=0
        )
        cls.wo3_closed = WorkOrder.objects.create(
            id=103, wo_no='WO2023-003', project_title='Internal Tool',
            customer=cls.customer1, sys_date=date(2023, 3, 10),
            company_id=1, financial_year_id=2024, close_open_status=1 # Closed
        )
        cls.wo4_other_company = WorkOrder.objects.create(
            id=104, wo_no='WO2023-004', project_title='New Product Launch',
            customer=cls.customer2, sys_date=date(2023, 4, 1),
            company_id=2, financial_year_id=2024, close_open_status=0
        )
        cls.wo5_other_finyear = WorkOrder.objects.create(
            id=105, wo_no='WO2023-005', project_title='Legacy System Update',
            customer=cls.customer1, sys_date=date(2023, 5, 5),
            company_id=1, financial_year_id=2023, close_open_status=0
        )

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customer_name, 'Acme Corp')
        self.assertEqual(self.customer1.customer_id, 'CUST001')
        self.assertEqual(str(self.customer1), 'Acme Corp [CUST001]')

    def test_workorder_creation(self):
        self.assertEqual(self.wo1.wo_no, 'WO2023-001')
        self.assertEqual(self.wo1.customer.customer_name, 'Acme Corp')
        self.assertEqual(self.wo1.project_title, 'Website Redesign')
        self.assertEqual(self.wo1.company_id, 1)
        self.assertEqual(self.wo1.financial_year_id, 2024)
        self.assertEqual(self.wo1.close_open_status, 0)
        self.assertEqual(str(self.wo1), 'WO2023-001')

    def test_workorder_manager_filtering(self):
        # WorkOrderManager should filter by company_id=1, financial_year_id<=2024, close_open_status=0
        active_workorders = WorkOrder.objects.all()
        self.assertEqual(active_workorders.count(), 2) # wo1, wo2
        self.assertIn(self.wo1, active_workorders)
        self.assertIn(self.wo2, active_workorders)
        self.assertNotIn(self.wo3_closed, active_workorders) # closed
        self.assertNotIn(self.wo4_other_company, active_workorders) # different company
        self.assertNotIn(self.wo5_other_finyear, active_workorders) # older financial year

    def test_workorder_get_filtered_workorders(self):
        # Test WO No search
        filtered_by_wo_no = WorkOrder.get_filtered_workorders('1', 'WO2023-001')
        self.assertEqual(filtered_by_wo_no.count(), 1)
        self.assertEqual(filtered_by_wo_no.first(), self.wo1)

        # Test Customer search
        filtered_by_customer = WorkOrder.get_filtered_workorders('0', 'Acme Corp')
        self.assertEqual(filtered_by_customer.count(), 1) # Only wo1 is active and with Acme
        self.assertEqual(filtered_by_customer.first(), self.wo1)
        
        filtered_by_customer_partial = WorkOrder.get_filtered_workorders('0', 'Acme')
        self.assertEqual(filtered_by_customer_partial.count(), 1)
        self.assertEqual(filtered_by_customer_partial.first(), self.wo1)

        # Test Project Title search
        filtered_by_project = WorkOrder.get_filtered_workorders('2', 'Mobile App')
        self.assertEqual(filtered_by_project.count(), 1)
        self.assertEqual(filtered_by_project.first(), self.wo2)

        # Test no search query
        all_active = WorkOrder.get_filtered_workorders(None, None)
        self.assertEqual(all_active.count(), 2)


class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Acme Corp',
            company_id=1
        )
        cls.wo1 = WorkOrder.objects.create(
            id=101, wo_no='WO2023-001', project_title='Website Redesign',
            customer=cls.customer1, sys_date=date(2023, 1, 15),
            company_id=1, financial_year_id=2024, close_open_status=0
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/list.html')
        # Check if search form is in context
        self.assertTrue('search_form' in response.context)
        
    def test_workorder_table_partial_view(self):
        response = self.client.get(reverse('workorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_table.html')
        self.assertIn(self.wo1, response.context['workorders'])

        # Test search filter via table partial
        response = self.client.get(reverse('workorder_table'), {'search_type': '1', 'search_query': 'WO2023-001'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['workorders'].count(), 1)
        self.assertEqual(response.context['workorders'].first(), self.wo1)

    def test_workorder_search_form_update_view(self):
        response = self.client.post(reverse('workorder_search_form_update'), {'search_type': '0'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'name="search_query_customer"', response.content)
        self.assertIn(b'hx-get="/inventory/customers/autocomplete/"', response.content)
        
        response = self.client.post(reverse('workorder_search_form_update'), {'search_type': '1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'name="search_query_wo_project"', response.content)

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete'), {'q': 'Acme'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['name'], 'Acme Corp [CUST001]')

        response = self.client.get(reverse('customer_autocomplete'), {'q': 'NonExistent'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 0)
        
    def test_create_view_get(self):
        response = self.client.get(reverse('workorder_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        initial_count = WorkOrder.objects.count()
        data = {
            'wo_no': 'NEWWO-001',
            'project_title': 'New Project Title',
            'customer_display': 'Acme Corp [CUST001]', # How autocomplete would pass it
            'customer': self.customer1.customer_id, # Hidden field for actual FK
            'sys_date': '2024-07-01',
            'company_id': 1,
            'financial_year_id': 2024,
            'close_open_status': 0,
        }
        response = self.client.post(reverse('workorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertEqual(WorkOrder.all_objects.count(), initial_count + 1) # Use all_objects to bypass default manager filters
        self.assertTrue(WorkOrder.all_objects.filter(wo_no='NEWWO-001').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order added successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWorkOrderList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        
    def test_create_view_post_invalid(self):
        initial_count = WorkOrder.objects.count()
        data = { # Missing required fields
            'wo_no': '', 
            'project_title': 'Invalid Project',
            'customer_display': 'NonExistentCustomer',
            'customer': '',
            'sys_date': '2024-07-01',
            'company_id': 1,
            'financial_year_id': 2024,
            'close_open_status': 0,
        }
        response = self.client.post(reverse('workorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_form.html')
        self.assertEqual(WorkOrder.all_objects.count(), initial_count) # No new object created
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Customer not found.') # Specific error for autocomplete

    def test_update_view_get(self):
        response = self.client.get(reverse('workorder_edit', args=[self.wo1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.wo1)
        
    def test_update_view_post_success(self):
        data = {
            'wo_no': 'WO2023-001-UPDATED',
            'project_title': 'Updated Project Title',
            'customer_display': 'Acme Corp [CUST001]',
            'customer': self.customer1.customer_id,
            'sys_date': '2023-01-15', # Keep original date
            'company_id': 1,
            'financial_year_id': 2024,
            'close_open_status': 0,
        }
        response = self.client.post(reverse('workorder_edit', args=[self.wo1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.wo1.refresh_from_db()
        self.assertEqual(self.wo1.wo_no, 'WO2023-001-UPDATED')
        self.assertEqual(self.wo1.project_title, 'Updated Project Title')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order updated successfully.')
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        response = self.client.get(reverse('workorder_delete', args=[self.wo1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_confirm_delete.html')
        self.assertEqual(response.context['object'], self.wo1)
        
    def test_delete_view_post_success(self):
        initial_count = WorkOrder.all_objects.count()
        response = self.client.delete(reverse('workorder_delete', args=[self.wo1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(WorkOrder.all_objects.count(), initial_count - 1)
        self.assertFalse(WorkOrder.all_objects.filter(pk=self.wo1.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order deleted successfully.')
        self.assertIn('HX-Trigger', response.headers)

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic content and forms:**
    *   The main list (`list.html`) uses `hx-get` to load the `_workorder_table.html` partial, which contains the DataTables setup.
    *   `hx-trigger="load, refreshWorkOrderList from:body"` on the `workorderTable-container` ensures the table loads on page load and refreshes whenever a `refreshWorkOrderList` event is triggered (e.g., after successful form submission).
    *   Search form submit uses `hx-get` to `workorder_table` URL to filter and refresh the table.
    *   The dropdown for `search_type` uses `hx-post` to `workorder_search_form_update` to dynamically swap the input field in `search-input-container`.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to load forms/confirmations into the `modalContent` div.
    *   Form submissions (`hx-post` or `hx-delete`) are set to `hx-swap="none"` and views return `HTTP 204 No Content` with `HX-Trigger` headers (`refreshWorkOrderList`, `closeModal`) to trigger UI updates without full page reload.
    *   Autocomplete for customer input uses `hx-get` to `customer_autocomplete` endpoint, updating a suggestions div. The `hx-on:click` in the suggestions will populate the form field and hidden FK field.

*   **Alpine.js for UI state management:**
    *   A main `x-data` block is used on the `#modal` div to control its visibility (`x-show`).
    *   `x-init` listens for `htmx:afterSwap` on `modalContent` to show the modal when a form/confirmation is loaded, and for `closeModal` event to hide it.
    *   `@click.self="showModal = false"` on the modal allows clicking outside the content to close it.
    *   A separate `x-data` on the form template helps manage customer search term for autocomplete.

*   **DataTables for list views:**
    *   The `_workorder_table.html` partial includes a `<script>` block to initialize DataTables on the `workorderTable`.
    *   It's configured with `pageLength: 23` to match the original ASP.NET GridView's page size, along with `lengthMenu` for user selection.
    *   Client-side search, sorting, and pagination are handled automatically by DataTables.

*   **DRY Template Inheritance:**
    *   All module-specific templates (`list.html`, `_workorder_table.html`, etc.) extend `core/base.html`, ensuring consistent layout and inclusion of global assets like Tailwind CSS, HTMX, Alpine.js, and jQuery (for DataTables). `core/base.html` itself is not included in this output as per instructions.

This plan systematically breaks down the ASP.NET application into its core components and translates them into modern Django patterns, emphasizing automation-friendly techniques and a clean architecture.