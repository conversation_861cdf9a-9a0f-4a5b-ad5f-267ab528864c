The following Django modernization plan outlines the strategic transition of your ASP.NET application to a modern, efficient, and maintainable Django-based solution. Our focus is on leveraging AI-assisted automation to streamline this process, minimizing manual effort and ensuring a robust outcome. We prioritize business value by enhancing performance, improving user experience, and establishing a scalable architecture for future growth.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code queries several tables. For this modernization, we identify the following tables and their key fields. Note that `Id` columns are typically primary keys in ASP.NET, which we'll map to `id` or `pk` in Django with `db_column='Id'` for `managed=False` models.

-   **`SD_Cust_WorkOrder_Master` (Django Model: `WorkOrderMaster`)**:
    -   `Id` (PK)
    -   `WONo` (string)
    -   `TaskProjectTitle` (string)
    -   `CustomerId` (int)
    -   `CompId` (int)
    -   `FinYearId` (int)

-   **`SD_Cust_Master` (Django Model: `CustomerMaster`)**:
    -   `CustomerId` (PK)
    -   `CustomerName` (string)
    -   `CompId` (int)

-   **`tblPM_MaterialCreditNote_Master` (Django Model: `MaterialCreditNoteMaster`)**:
    -   `Id` (PK, `MCNId`)
    -   `MCNNo` (string)
    -   `SysDate` (date)
    -   `WONo` (string)
    -   `FinYearId` (int)
    -   `CompId` (int)

-   **`tblPM_MaterialCreditNote_Details` (Django Model: `MaterialCreditNoteDetail`)**:
    -   `Id` (PK, `MCNDId`)
    -   `MId` (FK to `MaterialCreditNoteMaster.Id`, `MCNId`)
    -   `PId` (int)
    -   `CId` (int)
    -   `MCNQty` (double)

-   **`tblDG_BOM_Master` (Django Model: `BOMMaster`)**:
    -   `Id` (PK, inferred)
    -   `WONo` (string)
    -   `FinYearId` (int)
    -   `CompId` (int)
    -   `PId` (int)
    -   `CId` (int)
    -   `ItemId` (int, FK to `ItemMaster.Id`)

-   **`tblDG_Item_Master` (Django Model: `ItemMaster`)**:
    -   `Id` (PK, `ItemId`)
    -   `ItemCode` (string)
    -   `PartNo` (string)
    -   `ManfDesc` (string, Description)
    -   `UOMBasic` (int, FK to `UnitMaster.Id`)
    -   `FileName` (string, for file download link)
    -   `AttName` (string, for spec sheet download link)
    -   `FileData` (binary data)
    -   `AttData` (binary data)
    -   `ContentType` (string)
    -   `AttContentType` (string)
    -   `StockQty` (double)
    -   `CompId` (int)

-   **`Unit_Master` (Django Model: `UnitMaster`)**:
    -   `Id` (PK)
    -   `Symbol` (string)

-   **`tblQc_AuthorizedMCN` (Django Model: `AuthorizedMCN`)**:
    -   `Id` (PK)
    -   `SysDate` (date)
    -   `SysTime` (time)
    -   `SessionId` (string, user ID)
    -   `CompId` (int)
    -   `FinYearId` (int)
    -   `MCNId` (int, FK to `MaterialCreditNoteMaster.Id`)
    -   `MCNDId` (int, FK to `MaterialCreditNoteDetail.Id`)
    -   `QAQty` (double)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations (read, update, validation) in the ASP.NET code.

**Instructions:**
The ASP.NET page primarily performs the following:

-   **Read:**
    -   Displays Work Order, Project, and Customer details based on URL parameters (`WOId`, `WONo`).
    -   Populates a grid (`GridView1`) with Material Credit Note (MCN) details, combining data from multiple tables (`tblPM_MaterialCreditNote_Master`, `tblPM_MaterialCreditNote_Details`, `tblDG_BOM_Master`, `tblDG_Item_Master`, `Unit_Master`, `tblQc_AuthorizedMCN`). This involves complex data aggregation and calculations (e.g., `TotQAQty`).
-   **Update/Create:**
    -   The `btnSubmit_Click` event processes selected rows from the grid. For each selected row, it inserts a new record into `tblQc_AuthorizedMCN` (effectively creating a QA authorization record) and updates the `StockQty` in `tblDG_Item_Master`.
-   **File Download:**
    -   `GridView1_RowCommand` handles "Download" and "DownloadSpec" commands, redirecting to a generic file download page to retrieve files from `tblDG_Item_Master`.
-   **Validation:**
    -   Input validation for `QA Qty` (`txtqty`) checks for numeric format, positive values, and ensures `QA Qty` does not exceed the remaining `MCN Qty` (`(MCNQty - TotQAQty - Qty) >= 0`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page utilizes:

-   **Labels (`<asp:Label>`):** To display read-only information such as `WONo`, `Project Name`, `Customer Name`, and various MCN item details (MCN No, Date, Item Code, Description, UOM, MCN Qty, Tot QA Qty). These will be rendered directly in Django templates.
-   **Grid View (`<asp:GridView>`):** This is the central display for MCN items, with columns for serial number, item details, quantities, checkboxes, and input fields. This will be replaced by a DataTables-powered HTML table, dynamically loaded via HTMX.
-   **Text Box (`<asp:TextBox>`):** For user input of `QA Qty`. This will be an HTML input field with Tailwind CSS styling, handled within the DataTables structure.
-   **Check Box (`<asp:CheckBox>`):** For selecting rows to be authorized. This will be an HTML checkbox.
-   **Link Buttons (`<asp:LinkButton>`):** For "Download" (Draw/Img) and "DownloadSpec" (Spec.sheet) actions. These will be standard HTML `<a>` tags or buttons with `hx-get` attributes for HTMX-driven download links.
-   **Buttons (`<asp:Button>`):** "Submit" and "Cancel" actions. These will be HTML buttons with HTMX attributes for submitting forms or triggering navigation. The "Submit" button will initiate a bulk update.
-   **Panel (`<asp:Panel>`):** A container with scrollbars. This will be an HTML `<div>` with appropriate CSS for scrolling.

Client-side JavaScript `loadingNotifier.js` and `PopUpMsg.js` suggest dynamic feedback and alerts, which will be handled by HTMX's `hx-indicator` and Django's `messages` framework combined with Alpine.js for UI state and modal management.

### Step 4: Generate Django Code

We will structure the Django application in an `inventory_transactions` module.

#### 4.1 Models (`inventory_transactions/models.py`)

This file will define all necessary Django models, mapping directly to your existing database tables. We set `managed = False` in the `Meta` class, as these models will connect to and interact with your existing database schema without Django managing table creation. We also define a custom manager for `MaterialCreditNoteDetail` to encapsulate the complex data retrieval logic (`loaddata` equivalent) from the original ASP.NET application.

```python
from django.db import models, transaction
from django.db.models import Sum, Q, F
from django.utils import timezone
from datetime import date, time

# Custom Manager for MaterialCreditNoteDetail to encapsulate complex query logic
class MaterialCreditNoteDetailManager(models.Manager):
    def get_authorized_mcn_details(self, wo_id, wo_no, comp_id, fin_year_id):
        """
        Mimics the loaddata() function from ASP.NET code-behind to fetch MCN details
        with related item, BOM, unit, and total QA quantities.
        Returns a list of dictionaries, each representing a row in the GridView.
        """
        # Step 1: Get base MCN details
        mcn_details_qs = self.filter(
            material_credit_note_master__wo_no=wo_no,
            material_credit_note_master__fin_year_id__lte=fin_year_id,
            material_credit_note_master__comp_id=comp_id
        ).select_related('material_credit_note_master')

        # Step 2: Annotate with BOM and Item details, and total QA quantities
        # We need to perform multiple lookups and aggregations.
        # This is a simplified approach, in a real system, you might prefetch and then process in Python
        # or use complex subqueries/CTE depending on DB performance characteristics.
        # For 'Fat Model' and readability, we'll iterate and build structured data,
        # which mirrors the ASP.NET DataTable creation.
        
        results = []
        for mcn_detail in mcn_details_qs:
            row_data = {
                'id': mcn_detail.id,
                'p_id': mcn_detail.p_id,
                'c_id': mcn_detail.c_id,
                'mcn_qty': mcn_detail.mcn_qty,
                'mcn_no': mcn_detail.material_credit_note_master.mcn_no,
                'mcn_date': mcn_detail.material_credit_note_master.sys_date.strftime('%d/%m/%Y') if mcn_detail.material_credit_note_master.sys_date else '',
                'mcn_id': mcn_detail.material_credit_note_master.id,
                'item_id': None,
                'item_code': '',
                'description': '',
                'uom': '',
                'download_draw_img': '',
                'download_spec_sheet': '',
                'tot_qa_qty': 0.0,
            }

            # Fetch BOM details
            try:
                bom_detail = BOMMaster.objects.get(
                    wo_no=wo_no,
                    fin_year_id__lte=fin_year_id,
                    comp_id=comp_id,
                    p_id=mcn_detail.p_id,
                    c_id=mcn_detail.c_id
                )
                row_data['item_id'] = bom_detail.item.id

                # Fetch Item Master details
                item_master = bom_detail.item
                if item_master:
                    row_data['item_code'] = item_master.item_code if item_master.c_id else item_master.part_no
                    row_data['description'] = item_master.manf_desc

                    # Fetch Unit Master details
                    if item_master.uom_basic:
                        unit_master = UnitMaster.objects.filter(id=item_master.uom_basic).first()
                        if unit_master:
                            row_data['uom'] = unit_master.symbol
                    
                    if item_master.file_name:
                        row_data['download_draw_img'] = 'View'
                    if item_master.att_name:
                        row_data['download_spec_sheet'] = 'View'

            except BOMMaster.DoesNotExist:
                pass # Or log error, or handle as needed

            # Calculate TotQAQty
            total_qa_qty = AuthorizedMCN.objects.filter(
                mcn_id=mcn_detail.material_credit_note_master.id,
                mcn_detail_id=mcn_detail.id,
                fin_year_id__lte=fin_year_id,
                comp_id=comp_id
            ).aggregate(total=Sum('qa_qty'))['total'] or 0.0
            row_data['tot_qa_qty'] = total_qa_qty

            results.append(row_data)
        
        return results

    @transaction.atomic
    def process_authorized_mcn_submission(self, data, session_id, comp_id, fin_year_id):
        """
        Handles the batch submission of authorized MCN quantities.
        Performs validation and updates stock.
        Returns (success: bool, message: str)
        """
        validated_entries = []
        has_invalid_input = False

        # First pass: Validate all selected entries
        for entry in data:
            mcn_detail_id = int(entry.get('mcnd_id'))
            mcn_id = int(entry.get('mcn_id'))
            qa_qty_str = entry.get('qa_qty', '0')
            is_checked = entry.get('is_checked') == 'on'

            if not is_checked:
                continue

            try:
                qa_qty = float(qa_qty_str)
                if qa_qty <= 0:
                    has_invalid_input = True
                    break # Invalid QA Qty
            except ValueError:
                has_invalid_input = True
                break # Not a valid number

            mcn_detail = self.get(id=mcn_detail_id, material_credit_note_master__id=mcn_id)
            
            # Recalculate current TotQAQty to avoid race conditions or stale data
            current_tot_qa_qty = AuthorizedMCN.objects.filter(
                mcn_id=mcn_detail.material_credit_note_master.id,
                mcn_detail_id=mcn_detail.id,
                fin_year_id__lte=fin_year_id,
                comp_id=comp_id
            ).aggregate(total=Sum('qa_qty'))['total'] or 0.0

            remaining_qty = mcn_detail.mcn_qty - current_tot_qa_qty

            if qa_qty > remaining_qty:
                has_invalid_input = True
                break # QA Qty exceeds remaining MCN Qty

            validated_entries.append({
                'mcn_detail': mcn_detail,
                'mcn_id': mcn_id,
                'qa_qty': qa_qty
            })
        
        if has_invalid_input:
            return False, "Invalid input data. Please check quantities."

        # Second pass: Process valid entries and perform database operations
        for entry in validated_entries:
            mcn_detail = entry['mcn_detail']
            mcn_id = entry['mcn_id']
            qa_qty = entry['qa_qty']

            # Insert into tblQc_AuthorizedMCN
            AuthorizedMCN.objects.create(
                sys_date=timezone.localdate(),
                sys_time=timezone.localtime().time(),
                session_id=session_id,
                comp_id=comp_id,
                fin_year_id=fin_year_id,
                mcn=mcn_detail.material_credit_note_master, # Use ForeignKey object
                mcn_detail=mcn_detail, # Use ForeignKey object
                qa_qty=qa_qty
            )

            # Update tblDG_Item_Master StockQty
            # Find ItemId via BOMMaster
            try:
                bom_detail = BOMMaster.objects.get(
                    wo_no=mcn_detail.material_credit_note_master.wo_no,
                    fin_year_id__lte=fin_year_id,
                    comp_id=comp_id,
                    p_id=mcn_detail.p_id,
                    c_id=mcn_detail.c_id
                )
                item_master = bom_detail.item
                if item_master:
                    # Use F() expression for atomic update to prevent race conditions
                    item_master.stock_qty = F('stock_qty') + qa_qty
                    item_master.save(update_fields=['stock_qty'])
                else:
                    return False, f"Item not found for MCN Detail ID {mcn_detail.id}. Stock not updated."
            except BOMMaster.DoesNotExist:
                return False, f"BOM entry not found for MCN Detail ID {mcn_detail.id}. Stock not updated."

        return True, "Material Credit Notes authorized successfully!"

# Define all models mapping to existing database tables
# For simplicity, we are not setting default values for nullable fields, assuming DB handles defaults.

class WorkOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=50)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255, blank=True, null=True)
    customer_id = models.IntegerField(db_column='CustomerId')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

    def get_customer_details(self):
        return CustomerMaster.objects.filter(customer_id=self.customer_id, comp_id=self.comp_id).first()


class CustomerMaster(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name


class MaterialCreditNoteMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mcn_no = models.CharField(db_column='MCNNo', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Master'
        verbose_name = 'Material Credit Note Master'
        verbose_name_plural = 'Material Credit Note Masters'

    def __str__(self):
        return self.mcn_no


class MaterialCreditNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Using 'material_credit_note_master' as related_name for clarity
    material_credit_note_master = models.ForeignKey(
        MaterialCreditNoteMaster,
        on_delete=models.DO_NOTHING, # Or models.CASCADE, depending on business rules
        db_column='MId',
        related_name='details'
    )
    mcn_qty = models.FloatField(db_column='MCNQty')
    p_id = models.IntegerField(db_column='PId') # Parent ID for BOM
    c_id = models.IntegerField(db_column='CId') # Child ID for BOM

    objects = MaterialCreditNoteDetailManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Details'
        verbose_name = 'Material Credit Note Detail'
        verbose_name_plural = 'Material Credit Note Details'

    def __str__(self):
        return f"MCN Detail {self.id} for MCN {self.material_credit_note_master.mcn_no}"


class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # FK to UnitMaster
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # Binary data for Draw/Img
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True) # Binary data for Spec.sheet
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', default=0.0)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or self.part_no or f"Item {self.id}"


class BOMMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id exists or composite PK
    wo_no = models.CharField(db_column='WONo', max_length=50)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    # Using 'item' as related_name for clarity
    item = models.ForeignKey(
        ItemMaster,
        on_delete=models.DO_NOTHING, # Or models.CASCADE
        db_column='ItemId',
        related_name='bom_entries'
    )

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for WO {self.wo_no}, Item {self.item.item_code or self.item.part_no}"


class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=20)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol


class AuthorizedMCN(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    mcn = models.ForeignKey(
        MaterialCreditNoteMaster,
        on_delete=models.DO_NOTHING, # Or models.CASCADE
        db_column='MCNId',
        related_name='authorized_entries'
    )
    mcn_detail = models.ForeignKey(
        MaterialCreditNoteDetail,
        on_delete=models.DO_NOTHING, # Or models.CASCADE
        db_column='MCNDId',
        related_name='authorized_entries'
    )
    qa_qty = models.FloatField(db_column='QAQty')

    class Meta:
        managed = False
        db_table = 'tblQc_AuthorizedMCN'
        verbose_name = 'Authorized MCN'
        verbose_name_plural = 'Authorized MCNs'

    def __str__(self):
        return f"Auth MCN {self.id} for MCN {self.mcn.mcn_no}"

```

#### 4.2 Forms (`inventory_transactions/forms.py`)

No traditional Django forms are needed for the main grid data input, as it's a dynamic table with specific input fields. The form validation and processing will be handled by the `MaterialCreditNoteDetailManager` method on the backend, directly parsing the POST data. This aligns with the original ASP.NET approach where `GridView1`'s rows were iterated.

If there were fields for adding/editing a single MCN Detail record outside the grid, a form would be defined here.

#### 4.3 Views (`inventory_transactions/views.py`)

We'll define class-based views (CBVs) for displaying the main page, rendering the DataTables partial, and handling the submission logic. Views are kept thin, delegating complex logic to the models.

```python
from django.shortcuts import render, get_object_or_404
from django.views import View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse
from django.contrib import messages
from django.db import transaction

from .models import (
    WorkOrderMaster, CustomerMaster, MaterialCreditNoteDetail,
    ItemMaster, AuthorizedMCN
)
# No forms.py needed for this specific page's interactions as logic is in model manager

class AuthorizedMCNListView(View):
    template_name = 'inventory_transactions/authorizedmcndetail/list.html'

    def get(self, request, *args, **kwargs):
        # Extract parameters from URL/Session, matching ASP.NET Page_Load
        wo_id = request.GET.get('WOId')
        wo_no = request.GET.get('WONo')

        # Dummy session data for demonstration. In a real app, this would come from authentication system.
        comp_id = request.session.get('compid', 1) # Example default
        fin_year_id = request.session.get('finyear', 1) # Example default
        session_id = request.session.get('username', 'admin') # Example default

        work_order_master = None
        customer_name = ""

        if wo_id and wo_no:
            try:
                work_order_master = get_object_or_404(
                    WorkOrderMaster,
                    id=wo_id,
                    wo_no=wo_no,
                    comp_id=comp_id,
                    fin_year_id__lte=fin_year_id
                )
                customer_obj = work_order_master.get_customer_details()
                if customer_obj:
                    customer_name = f"{customer_obj.customer_name} [ {customer_obj.customer_id} ]"
            except WorkOrderMaster.DoesNotExist:
                messages.error(request, "Work Order not found or unauthorized.")
                work_order_master = None # Ensure it's None if not found

        context = {
            'wo_id': wo_id,
            'wo_no': wo_no,
            'project_title': work_order_master.task_project_title if work_order_master else "",
            'customer_name': customer_name,
            'comp_id': comp_id,
            'fin_year_id': fin_year_id,
            'session_id': session_id,
        }
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        # Handles the "Submit" action for authorizing MCNs
        # Extract parameters from Session, matching ASP.NET btnSubmit_Click
        comp_id = request.session.get('compid', 1) # Example default
        fin_year_id = request.session.get('finyear', 1) # Example default
        session_id = request.session.get('username', 'admin') # Example default

        # The ASP.NET code iterates GridView rows and checks `CheckBox1.Checked`.
        # In Django, HTMX will send data from the entire form.
        # We need to parse request.POST to identify which rows were 'checked' and their QAQty.
        
        # Example POST data structure (sent from HTMX on submit):
        # [
        #    {'mcnd_id': '1', 'mcn_id': '101', 'mcn_qty': '10.0', 'tot_qa_qty': '5.0', 'qa_qty': '3.0', 'is_checked': 'on'},
        #    {'mcnd_id': '2', 'mcn_id': '102', 'mcn_qty': '20.0', 'tot_qa_qty': '20.0', 'qa_qty': '0.0', 'is_checked': 'off'},
        #    ...
        # ]
        # The frontend will structure this into a list of dictionaries for each row.
        
        # HTMX forms submit data as key-value pairs. If multiple rows are submitted,
        # it might look like:
        # mcnd_id_0: 1, mcn_id_0: 101, qa_qty_0: 3.0, is_checked_0: on
        # mcnd_id_1: 2, mcn_id_1: 102, qa_qty_1: 0.0, is_checked_1: off
        # We need to reconstruct this into a list of dictionaries.
        
        rows_data = []
        i = 0
        while f'mcnd_id_{i}' in request.POST:
            row = {
                'mcnd_id': request.POST.get(f'mcnd_id_{i}'),
                'mcn_id': request.POST.get(f'mcn_id_{i}'),
                'mcn_qty': request.POST.get(f'mcn_qty_{i}'),
                'tot_qa_qty': request.POST.get(f'tot_qa_qty_{i}'),
                'qa_qty': request.POST.get(f'qa_qty_{i}'),
                'is_checked': request.POST.get(f'is_checked_{i}'),
            }
            rows_data.append(row)
            i += 1
            
        success, msg = MaterialCreditNoteDetail.objects.process_authorized_mcn_submission(
            rows_data, session_id, comp_id, fin_year_id
        )

        if success:
            messages.success(request, msg)
            # Trigger HTMX to refresh the table and optionally clear forms
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshAuthorizedMCNList'}
            )
        else:
            messages.error(request, msg)
            # Re-render the form with errors or just display a message
            # For HTMX, we can return the updated table or a fragment with error messages
            # For simplicity, we just return a message and let HTMX trigger a refresh if needed
            return HttpResponse(
                status=400, # Bad Request, or 200 with content showing error
                headers={'HX-Retarget': '#messages-container', 'HX-Reswap': 'innerHTML'} # Target messages area
            )


class AuthorizedMCNTablePartialView(View):
    template_name = 'inventory_transactions/authorizedmcndetail/_table.html'

    def get(self, request, *args, **kwargs):
        wo_id = request.GET.get('WOId')
        wo_no = request.GET.get('WONo')
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 1)

        mcn_details = []
        if wo_id and wo_no:
            mcn_details = MaterialCreditNoteDetail.objects.get_authorized_mcn_details(
                wo_id, wo_no, comp_id, fin_year_id
            )
        
        context = {
            'mcn_details': mcn_details,
            'wo_id': wo_id, # Pass parameters needed for reload/submission
            'wo_no': wo_no,
            'comp_id': comp_id,
            'fin_year_id': fin_year_id,
        }
        return render(request, self.template_name, context)

class DownloadFileView(View):
    def get(self, request, *args, **kwargs):
        item_id = request.GET.get('Id')
        file_type = request.GET.get('FileType') # 'drawing' or 'spec'

        if not item_id or not file_type:
            return HttpResponse("File ID or type not provided.", status=400)

        item = get_object_or_404(ItemMaster, id=item_id)

        if file_type == 'drawing':
            file_data = item.file_data
            file_name = item.file_name
            content_type = item.content_type
        elif file_type == 'spec':
            file_data = item.att_data
            file_name = item.att_name
            content_type = item.att_content_type
        else:
            return HttpResponse("Invalid file type.", status=400)

        if file_data and file_name and content_type:
            response = HttpResponse(file_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            return response
        else:
            return HttpResponse("File not found or data missing.", status=404)

```

#### 4.4 Templates

Templates will be split for better organization and HTMX compatibility.

**`inventory_transactions/authorizedmcndetail/list.html`** (Main Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Authorize MCN</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700">
            <div>
                <strong>WO No:</strong> <span id="lblWono">{{ wo_no }}</span>
            </div>
            <div>
                <strong>Project Name:</strong> <span id="lblProjectTitle">{{ project_title }}</span>
            </div>
            <div>
                <strong>Customer Name:</strong> <span id="lblCustName">{{ customer_name }}</span>
            </div>
        </div>
    </div>

    <form hx-post="{% url 'inventory_transactions:authorizedmcndetail_submit' %}?WOId={{ wo_id }}&WONo={{ wo_no }}"
          hx-trigger="submit"
          hx-swap="none"
          id="mcnAuthForm">
        {% csrf_token %}
        <input type="hidden" name="wo_id" value="{{ wo_id }}">
        <input type="hidden" name="wo_no" value="{{ wo_no }}">
        <input type="hidden" name="comp_id" value="{{ comp_id }}">
        <input type="hidden" name="fin_year_id" value="{{ fin_year_id }}">

        <div id="mcnDetailTableContainer"
             hx-trigger="load, refreshAuthorizedMCNList from:body"
             hx-get="{% url 'inventory_transactions:authorizedmcndetail_table_partial' %}?WOId={{ wo_id }}&WONo={{ wo_no }}"
             hx-swap="innerHTML"
             class="bg-white shadow-md rounded-lg overflow-x-auto">
            <!-- DataTables content will be loaded here via HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Material Credit Note Details...</p>
            </div>
        </div>

        <div class="mt-6 text-center">
            <button type="submit"
                    id="btnSubmit"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-4">
                Submit
            </button>
            <a href="{% url 'inventory_transactions:authorizedmcn_cancel' %}"
               id="btnCancel"
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </a>
        </div>
    </form>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('mcnRow', (initialMCNQty, initialTotQAQty) => ({
            mcnQty: initialMCNQty,
            totQaQty: initialTotQAQty,
            qaQty: 0,
            isChecked: false,
            canEdit: false,
            
            init() {
                this.updateCanEdit();
            },

            updateCanEdit() {
                this.canEdit = (this.mcnQty - this.totQaQty) > 0;
                if (!this.canEdit) {
                    this.isChecked = false;
                    this.qaQty = 0;
                }
            },

            toggleCheck() {
                if (!this.canEdit) {
                    this.isChecked = false; // Prevent checking if not editable
                }
            },

            validateQaQty() {
                let remaining = this.mcnQty - this.totQaQty;
                if (this.qaQty < 0) {
                    this.qaQty = 0;
                }
                if (this.qaQty > remaining) {
                    this.qaQty = remaining;
                }
            }
        }));
    });
</script>
{% endblock %}
```

**`inventory_transactions/authorizedmcndetail/_table.html`** (DataTables Partial)

```html
<table id="authorizedMCNTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MCN No</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Draw/Img</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec.sheet</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">MCN Qty</th>
            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">QA Qty</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot QA Qty</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if mcn_details %}
            {% for detail in mcn_details %}
            <tr x-data="mcnRow({{ detail.mcn_qty|floatformat:3 }}, {{ detail.tot_qa_qty|floatformat:3 }})">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.mcn_no }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.mcn_date }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                    {% if detail.download_draw_img == 'View' %}
                        <a href="{% url 'inventory_transactions:download_file' %}?Id={{ detail.item_id }}&FileType=drawing"
                           target="_blank" class="text-blue-600 hover:text-blue-900">View</a>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                    {% if detail.download_spec_sheet == 'View' %}
                        <a href="{% url 'inventory_transactions:download_file' %}?Id={{ detail.item_id }}&FileType=spec"
                           target="_blank" class="text-blue-600 hover:text-blue-900">View</a>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.item_code }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.description }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.uom }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ detail.mcn_qty|floatformat:3 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                    <input type="hidden" name="mcnd_id_{{ forloop.counter0 }}" value="{{ detail.id }}">
                    <input type="hidden" name="mcn_id_{{ forloop.counter0 }}" value="{{ detail.mcn_id }}">
                    <input type="hidden" name="mcn_qty_{{ forloop.counter0 }}" value="{{ detail.mcn_qty }}">
                    <input type="hidden" name="tot_qa_qty_{{ forloop.counter0 }}" value="{{ detail.tot_qa_qty }}">

                    <input type="checkbox"
                           name="is_checked_{{ forloop.counter0 }}"
                           class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded"
                           x-model="isChecked"
                           x-bind:disabled="!canEdit"
                           @change="toggleCheck">
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <input type="number"
                           name="qa_qty_{{ forloop.counter0 }}"
                           step="0.001"
                           min="0"
                           x-model.number="qaQty"
                           x-bind:disabled="!isChecked"
                           @input="validateQaQty"
                           class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm text-right box3">
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ detail.tot_qa_qty|floatformat:3 }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="12" class="px-6 py-4 text-center text-sm text-red-500 font-bold">
                No data found to display
            </td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Initialize DataTables
    $(document).ready(function() {
        $('#authorizedMCNTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 3, 4, 9, 10] }, // Disable sorting for SN, Draw/Img, Spec.sheet, Select, QA Qty
                { "searchable": false, "targets": [0, 9, 10, 11] } // Disable searching for SN, Select, QA Qty, Tot QA Qty
            ]
        });
    });
</script>
```

**Note on `_table.html` and `form` submission:**
The `_table.html` is embedded inside the main `form` tag in `list.html`. When HTMX loads the table content, it replaces the `div#mcnDetailTableContainer`, effectively putting the table (and its input fields/checkboxes) *inside* the form. This allows all relevant input fields (`mcnd_id`, `mcn_id`, `qa_qty`, `is_checked`) to be submitted when the "Submit" button is clicked. Hidden inputs are used to pass necessary IDs and original quantities for backend validation. Alpine.js is used for client-side state management (checkbox enable/disable, QA Qty validation) ensuring a smooth user experience.

#### 4.5 URLs (`inventory_transactions/urls.py`)

This file defines the URL patterns for your Django application, linking URLs to the views.

```python
from django.urls import path
from .views import (
    AuthorizedMCNListView,
    AuthorizedMCNTablePartialView,
    DownloadFileView
)

app_name = 'inventory_transactions' # Namespace for URLs

urlpatterns = [
    # Main page displaying MCN authorization details
    path(
        'authorized_mcn_details/',
        AuthorizedMCNListView.as_view(),
        name='authorizedmcndetail_list'
    ),
    # HTMX endpoint to fetch the DataTables partial
    path(
        'authorized_mcn_details/table/',
        AuthorizedMCNTablePartialView.as_view(),
        name='authorizedmcndetail_table_partial'
    ),
    # HTMX endpoint for submitting QA quantities (POST request to main view)
    path(
        'authorized_mcn_details/submit/',
        AuthorizedMCNListView.as_view(), # POST request for submission handled by list view
        name='authorizedmcndetail_submit'
    ),
    # Endpoint for downloading files (Draw/Img, Spec.sheet)
    path(
        'download_file/',
        DownloadFileView.as_view(),
        name='download_file'
    ),
    # Placeholder for cancel redirection. This would typically go to a dashboard or previous page.
    path(
        'authorized_mcn_details/cancel/',
        lambda request: HttpResponse("Redirect to AuthorizedMCN.aspx?ModId=10&SubModId=128 - Implement actual redirect.", status=200),
        name='authorizedmcn_cancel'
    ),
]

```

#### 4.6 Tests (`inventory_transactions/tests.py`)

Comprehensive tests are crucial to ensure the correctness and robustness of the migrated application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.utils import timezone
from datetime import date, time
from unittest.mock import patch, MagicMock

from .models import (
    WorkOrderMaster, CustomerMaster, MaterialCreditNoteMaster,
    MaterialCreditNoteDetail, BOMMaster, ItemMaster, UnitMaster, AuthorizedMCN
)

# --- Model Unit Tests ---

class MaterialCreditNoteDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models to ensure relationships exist
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.wo_no = "WO-001"
        cls.wo_id = 101
        cls.customer_id = 501
        cls.mcn_master_id = 201
        cls.mcn_detail_id_1 = 301
        cls.mcn_detail_id_2 = 302
        cls.item_id_1 = 401
        cls.item_id_2 = 402
        cls.unit_id = 601
        cls.session_id = "testuser"

        cls.work_order = WorkOrderMaster.objects.create(
            id=cls.wo_id, WO_No=cls.wo_no, TaskProjectTitle="Test Project",
            CustomerId=cls.customer_id, CompId=cls.comp_id, FinYearId=cls.fin_year_id
        )
        cls.customer = CustomerMaster.objects.create(
            CustomerId=cls.customer_id, CustomerName="Test Customer", CompId=cls.comp_id
        )
        cls.mcn_master = MaterialCreditNoteMaster.objects.create(
            id=cls.mcn_master_id, MCNNo="MCN-001", SysDate=date.today(),
            WONo=cls.wo_no, FinYearId=cls.fin_year_id, CompId=cls.comp_id
        )
        cls.unit = UnitMaster.objects.create(id=cls.unit_id, Symbol="PCS")

        cls.item_1 = ItemMaster.objects.create(
            id=cls.item_id_1, ItemCode="ITEM001", ManfDesc="Item One Desc",
            UOMBasic=cls.unit_id, StockQty=100.0, CompId=cls.comp_id,
            FileName="drawing1.pdf", AttName="spec1.pdf"
        )
        cls.item_2 = ItemMaster.objects.create(
            id=cls.item_id_2, PartNo="PART002", ManfDesc="Item Two Desc",
            UOMBasic=cls.unit_id, StockQty=50.0, CompId=cls.comp_id
        )

        cls.bom_1 = BOMMaster.objects.create(
            id=1, WONo=cls.wo_no, FinYearId=cls.fin_year_id, CompId=cls.comp_id,
            PId=10, CId=1, ItemId=cls.item_id_1 # ItemId is FK
        )
        cls.bom_2 = BOMMaster.objects.create(
            id=2, WONo=cls.wo_no, FinYearId=cls.fin_year_id, CompId=cls.comp_id,
            PId=20, CId=2, ItemId=cls.item_id_2 # ItemId is FK
        )

        cls.mcn_detail_1 = MaterialCreditNoteDetail.objects.create(
            id=cls.mcn_detail_id_1, MId=cls.mcn_master_id, MCNQty=10.0, PId=10, CId=1 # MId is FK
        )
        cls.mcn_detail_2 = MaterialCreditNoteDetail.objects.create(
            id=cls.mcn_detail_id_2, MId=cls.mcn_master_id, MCNQty=5.0, PId=20, CId=2 # MId is FK
        )
        
        # Add some existing authorized quantities
        AuthorizedMCN.objects.create(
            id=1, SysDate=date.today(), SysTime=time(9,0,0), SessionId=cls.session_id,
            CompId=cls.comp_id, FinYearId=cls.fin_year_id,
            MCNId=cls.mcn_master_id, MCNDId=cls.mcn_detail_id_1, QAQty=3.0
        )
        AuthorizedMCN.objects.create(
            id=2, SysDate=date.today(), SysTime=time(9,30,0), SessionId=cls.session_id,
            CompId=cls.comp_id, FinYearId=cls.fin_year_id,
            MCNId=cls.mcn_master_id, MCNDId=cls.mcn_detail_id_1, QAQty=2.0
        )


    def test_get_authorized_mcn_details_method(self):
        details = MaterialCreditNoteDetail.objects.get_authorized_mcn_details(
            self.wo_id, self.wo_no, self.comp_id, self.fin_year_id
        )
        self.assertEqual(len(details), 2)

        # Test first detail row
        detail_1 = next(d for d in details if d['id'] == self.mcn_detail_id_1)
        self.assertIsNotNone(detail_1)
        self.assertEqual(detail_1['mcn_qty'], 10.0)
        self.assertEqual(detail_1['item_code'], self.item_1.item_code)
        self.assertEqual(detail_1['description'], self.item_1.manf_desc)
        self.assertEqual(detail_1['uom'], self.unit.symbol)
        self.assertEqual(detail_1['download_draw_img'], 'View')
        self.assertEqual(detail_1['download_spec_sheet'], 'View')
        self.assertEqual(detail_1['tot_qa_qty'], 5.0) # 3.0 + 2.0 from setUpTestData

        # Test second detail row
        detail_2 = next(d for d in details if d['id'] == self.mcn_detail_id_2)
        self.assertIsNotNone(detail_2)
        self.assertEqual(detail_2['mcn_qty'], 5.0)
        self.assertEqual(detail_2['item_code'], self.item_2.part_no) # Item 2 has PartNo, not ItemCode
        self.assertEqual(detail_2['description'], self.item_2.manf_desc)
        self.assertEqual(detail_2['uom'], self.unit.symbol)
        self.assertEqual(detail_2['download_draw_img'], '')
        self.assertEqual(detail_2['download_spec_sheet'], '')
        self.assertEqual(detail_2['tot_qa_qty'], 0.0)


    def test_process_authorized_mcn_submission_success(self):
        initial_stock_item1 = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        
        data = [
            {
                'mcnd_id': str(self.mcn_detail_id_1),
                'mcn_id': str(self.mcn_master_id),
                'mcn_qty': '10.0',
                'tot_qa_qty': '5.0',
                'qa_qty': '3.0',
                'is_checked': 'on'
            },
            {
                'mcnd_id': str(self.mcn_detail_id_2),
                'mcn_id': str(self.mcn_master_id),
                'mcn_qty': '5.0',
                'tot_qa_qty': '0.0',
                'qa_qty': '4.0',
                'is_checked': 'on'
            }
        ]
        
        success, msg = MaterialCreditNoteDetail.objects.process_authorized_mcn_submission(
            data, self.session_id, self.comp_id, self.fin_year_id
        )

        self.assertTrue(success)
        self.assertEqual(msg, "Material Credit Notes authorized successfully!")
        
        # Verify new AuthorizedMCN records
        self.assertEqual(AuthorizedMCN.objects.filter(mcn_detail_id=self.mcn_detail_id_1, qa_qty=3.0).count(), 1)
        self.assertEqual(AuthorizedMCN.objects.filter(mcn_detail_id=self.mcn_detail_id_2, qa_qty=4.0).count(), 1)
        
        # Verify stock updates
        item1_updated_stock = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        item2_updated_stock = ItemMaster.objects.get(id=self.item_id_2).stock_qty

        self.assertAlmostEqual(item1_updated_stock, initial_stock_item1 + 3.0)
        self.assertAlmostEqual(item2_updated_stock, self.item_2.stock_qty + 4.0)

        # Verify TotQAQty calculation is updated
        details = MaterialCreditNoteDetail.objects.get_authorized_mcn_details(
            self.wo_id, self.wo_no, self.comp_id, self.fin_year_id
        )
        detail_1_after_update = next(d for d in details if d['id'] == self.mcn_detail_id_1)
        detail_2_after_update = next(d for d in details if d['id'] == self.mcn_detail_id_2)
        self.assertAlmostEqual(detail_1_after_update['tot_qa_qty'], 5.0 + 3.0) # Original 5.0 + new 3.0
        self.assertAlmostEqual(detail_2_after_update['tot_qa_qty'], 0.0 + 4.0) # Original 0.0 + new 4.0


    def test_process_authorized_mcn_submission_invalid_qty(self):
        initial_stock_item1 = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        
        # Try to authorize more than remaining MCN Qty
        data = [
            {
                'mcnd_id': str(self.mcn_detail_id_1),
                'mcn_id': str(self.mcn_master_id),
                'mcn_qty': '10.0',
                'tot_qa_qty': '5.0',
                'qa_qty': '6.0', # Exceeds remaining (10-5=5)
                'is_checked': 'on'
            }
        ]
        
        success, msg = MaterialCreditNoteDetail.objects.process_authorized_mcn_submission(
            data, self.session_id, self.comp_id, self.fin_year_id
        )
        
        self.assertFalse(success)
        self.assertEqual(msg, "Invalid input data. Please check quantities.")
        
        # Verify no new AuthorizedMCN records
        self.assertEqual(AuthorizedMCN.objects.filter(mcn_detail_id=self.mcn_detail_id_1, qa_qty=6.0).count(), 0)
        
        # Verify stock not updated
        item1_updated_stock = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        self.assertAlmostEqual(item1_updated_stock, initial_stock_item1)

    def test_process_authorized_mcn_submission_negative_qty(self):
        data = [
            {
                'mcnd_id': str(self.mcn_detail_id_1),
                'mcn_id': str(self.mcn_master_id),
                'mcn_qty': '10.0',
                'tot_qa_qty': '5.0',
                'qa_qty': '-1.0',
                'is_checked': 'on'
            }
        ]
        success, msg = MaterialCreditNoteDetail.objects.process_authorized_mcn_submission(
            data, self.session_id, self.comp_id, self.fin_year_id
        )
        self.assertFalse(success)
        self.assertEqual(msg, "Invalid input data. Please check quantities.")

    def test_process_authorized_mcn_submission_non_numeric_qty(self):
        data = [
            {
                'mcnd_id': str(self.mcn_detail_id_1),
                'mcn_id': str(self.mcn_master_id),
                'mcn_qty': '10.0',
                'tot_qa_qty': '5.0',
                'qa_qty': 'abc',
                'is_checked': 'on'
            }
        ]
        success, msg = MaterialCreditNoteDetail.objects.process_authorized_mcn_submission(
            data, self.session_id, self.comp_id, self.fin_year_id
        )
        self.assertFalse(success)
        self.assertEqual(msg, "Invalid input data. Please check quantities.")

    def test_process_authorized_mcn_submission_no_checked_items(self):
        data = [
            {
                'mcnd_id': str(self.mcn_detail_id_1),
                'mcn_id': str(self.mcn_master_id),
                'mcn_qty': '10.0',
                'tot_qa_qty': '5.0',
                'qa_qty': '1.0',
                'is_checked': 'off' # Not checked
            }
        ]
        
        success, msg = MaterialCreditNoteDetail.objects.process_authorized_mcn_submission(
            data, self.session_id, self.comp_id, self.fin_year_id
        )
        # Should succeed because no items were actually selected for processing
        self.assertTrue(success) 
        self.assertEqual(msg, "Material Credit Notes authorized successfully!")
        self.assertEqual(AuthorizedMCN.objects.count(), 2) # No new entries added


class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.item = ItemMaster.objects.create(
            id=1, ItemCode="TEST-ITEM", ManfDesc="Test Description",
            UOMBasic=1, StockQty=100.0, CompId=1,
            FileName="test_drawing.pdf", AttName="test_spec.pdf",
            FileData=b"dummy drawing data", ContentType="application/pdf",
            AttData=b"dummy spec data", AttContentType="application/pdf"
        )
    
    def test_item_creation(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.item_code, "TEST-ITEM")
        self.assertEqual(item.stock_qty, 100.0)
        self.assertEqual(item.file_name, "test_drawing.pdf")
        self.assertEqual(item.file_data, b"dummy drawing data")


# --- View Integration Tests ---

class AuthorizedMCNViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Re-use setup data from model tests
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.wo_no = "WO-001"
        cls.wo_id = 101
        cls.customer_id = 501
        cls.mcn_master_id = 201
        cls.mcn_detail_id_1 = 301
        cls.mcn_detail_id_2 = 302
        cls.item_id_1 = 401
        cls.item_id_2 = 402
        cls.unit_id = 601
        cls.session_id = "testuser"

        cls.work_order = WorkOrderMaster.objects.create(
            id=cls.wo_id, WONo=cls.wo_no, TaskProjectTitle="Test Project",
            CustomerId=cls.customer_id, CompId=cls.comp_id, FinYearId=cls.fin_year_id
        )
        cls.customer = CustomerMaster.objects.create(
            CustomerId=cls.customer_id, CustomerName="Test Customer", CompId=cls.comp_id
        )
        cls.mcn_master = MaterialCreditNoteMaster.objects.create(
            id=cls.mcn_master_id, MCNNo="MCN-001", SysDate=date.today(),
            WONo=cls.wo_no, FinYearId=cls.fin_year_id, CompId=cls.comp_id
        )
        cls.unit = UnitMaster.objects.create(id=cls.unit_id, Symbol="PCS")

        cls.item_1 = ItemMaster.objects.create(
            id=cls.item_id_1, ItemCode="ITEM001", ManfDesc="Item One Desc",
            UOMBasic=cls.unit_id, StockQty=100.0, CompId=cls.comp_id,
            FileName="drawing1.pdf", AttName="spec1.pdf"
        )
        cls.item_2 = ItemMaster.objects.create(
            id=cls.item_id_2, PartNo="PART002", ManfDesc="Item Two Desc",
            UOMBasic=cls.unit_id, StockQty=50.0, CompId=cls.comp_id
        )

        cls.bom_1 = BOMMaster.objects.create(
            id=1, WONo=cls.wo_no, FinYearId=cls.fin_year_id, CompId=cls.comp_id,
            PId=10, CId=1, ItemId=cls.item_id_1
        )
        cls.bom_2 = BOMMaster.objects.create(
            id=2, WONo=cls.wo_no, FinYearId=cls.fin_year_id, CompId=cls.comp_id,
            PId=20, CId=2, ItemId=cls.item_id_2
        )

        cls.mcn_detail_1 = MaterialCreditNoteDetail.objects.create(
            id=cls.mcn_detail_id_1, MId=cls.mcn_master_id, MCNQty=10.0, PId=10, CId=1
        )
        cls.mcn_detail_2 = MaterialCreditNoteDetail.objects.create(
            id=cls.mcn_detail_id_2, MId=cls.mcn_master_id, MCNQty=5.0, PId=20, CId=2
        )
        
        AuthorizedMCN.objects.create(
            id=1, SysDate=date.today(), SysTime=time(9,0,0), SessionId=cls.session_id,
            CompId=cls.comp_id, FinYearId=cls.fin_year_id,
            MCNId=cls.mcn_master_id, MCNDId=cls.mcn_detail_id_1, QAQty=3.0
        )
        AuthorizedMCN.objects.create(
            id=2, SysDate=date.today(), SysTime=time(9,30,0), SessionId=cls.session_id,
            CompId=cls.comp_id, FinYearId=cls.fin_year_id,
            MCNId=cls.mcn_master_id, MCNDId=cls.mcn_detail_id_1, QAQty=2.0
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session['username'] = self.session_id
        session.save()

    def test_list_view_get_success(self):
        url = reverse('inventory_transactions:authorizedmcndetail_list')
        response = self.client.get(url, {'WOId': self.wo_id, 'WONo': self.wo_no})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/authorizedmcndetail/list.html')
        self.assertContains(response, self.wo_no)
        self.assertContains(response, self.work_order.task_project_title)
        self.assertContains(response, f"{self.customer.customer_name} [ {self.customer.customer_id} ]")

    def test_list_view_get_no_wo_params(self):
        url = reverse('inventory_transactions:authorizedmcndetail_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/authorizedmcndetail/list.html')
        self.assertContains(response, "WO No:") # Check labels exist but data empty
        self.assertContains(response, "Project Name:")
        self.assertContains(response, "Customer Name:")

    def test_table_partial_view_get_success(self):
        url = reverse('inventory_transactions:authorizedmcndetail_table_partial')
        response = self.client.get(url, {'WOId': self.wo_id, 'WONo': self.wo_no}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/authorizedmcndetail/_table.html')
        self.assertContains(response, self.mcn_detail_1.mcn_qty)
        self.assertContains(response, self.item_1.item_code)
        self.assertContains(response, "View") # For download links
        self.assertContains(response, f'{self.mcn_detail_1.mcn_qty|floatformat:3}') # Check MCN Qty
        self.assertContains(response, f'{5.0|floatformat:3}') # Check Tot QA Qty (3.0 + 2.0)

    def test_submit_view_post_success(self):
        # Initial stock of Item 1
        initial_stock = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        
        url = reverse('inventory_transactions:authorizedmcndetail_submit')
        post_data = {
            f'mcnd_id_0': str(self.mcn_detail_id_1),
            f'mcn_id_0': str(self.mcn_master_id),
            f'mcn_qty_0': '10.0',
            f'tot_qa_qty_0': '5.0',
            f'qa_qty_0': '2.0',
            f'is_checked_0': 'on',
            f'mcnd_id_1': str(self.mcn_detail_id_2),
            f'mcn_id_1': str(self.mcn_master_id),
            f'mcn_qty_1': '5.0',
            f'tot_qa_qty_1': '0.0',
            f'qa_qty_1': '1.0',
            f'is_checked_1': 'on',
        }
        response = self.client.post(url, post_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content, successful HTMX
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshAuthorizedMCNList')
        
        # Verify messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Material Credit Notes authorized successfully!")
        
        # Verify database changes
        self.assertEqual(AuthorizedMCN.objects.count(), 4) # 2 initial + 2 new
        item_1_new_stock = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        item_2_new_stock = ItemMaster.objects.get(id=self.item_id_2).stock_qty
        self.assertAlmostEqual(item_1_new_stock, initial_stock + 2.0)
        self.assertAlmostEqual(item_2_new_stock, self.item_2.stock_qty + 1.0) # From initial setup of 50.0

    def test_submit_view_post_invalid_qty(self):
        initial_stock = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        
        url = reverse('inventory_transactions:authorizedmcndetail_submit')
        post_data = {
            f'mcnd_id_0': str(self.mcn_detail_id_1),
            f'mcn_id_0': str(self.mcn_master_id),
            f'mcn_qty_0': '10.0',
            f'tot_qa_qty_0': '5.0',
            f'qa_qty_0': '6.0', # Invalid: 10 - 5 = 5 remaining, trying to authorize 6
            f'is_checked_0': 'on',
        }
        response = self.client.post(url, post_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 400) # Bad Request due to validation error
        self.assertIn('HX-Retarget', response.headers)
        self.assertIn('HX-Reswap', response.headers)
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Invalid input data. Please check quantities.")
        
        # Verify no database changes
        self.assertEqual(AuthorizedMCN.objects.count(), 2) # No new records added
        item_1_current_stock = ItemMaster.objects.get(id=self.item_id_1).stock_qty
        self.assertAlmostEqual(item_1_current_stock, initial_stock)

    def test_download_file_view_drawing(self):
        item_with_drawing = ItemMaster.objects.create(
            id=99, ItemCode="DRW-ITEM", CompId=1, StockQty=10,
            FileName="test_drawing.pdf", FileData=b"PDF_DATA", ContentType="application/pdf"
        )
        url = reverse('inventory_transactions:download_file') + f'?Id={item_with_drawing.id}&FileType=drawing'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], "application/pdf")
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_drawing.pdf"')
        self.assertEqual(response.content, b"PDF_DATA")

    def test_download_file_view_spec_sheet(self):
        item_with_spec = ItemMaster.objects.create(
            id=100, ItemCode="SPEC-ITEM", CompId=1, StockQty=10,
            AttName="test_spec.docx", AttData=b"DOCX_DATA", AttContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        url = reverse('inventory_transactions:download_file') + f'?Id={item_with_spec.id}&FileType=spec'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_spec.docx"')
        self.assertEqual(response.content, b"DOCX_DATA")

    def test_download_file_view_not_found(self):
        url = reverse('inventory_transactions:download_file') + f'?Id=99999&FileType=drawing' # Non-existent ID
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_download_file_view_missing_params(self):
        url = reverse('inventory_transactions:download_file') + f'?Id=1' # Missing FileType
        response = self.client.get(url)
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, "File ID or type not provided.", status_code=400)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for dynamic updates:**
    -   The main `list.html` page uses `hx-get` on `div#mcnDetailTableContainer` to load the `_table.html` content initially and on `refreshAuthorizedMCNList` custom event.
    -   The "Submit" button on `list.html` uses `hx-post` to submit the form data to the `authorizedmcndetail_submit` URL. On success (204 No Content), it triggers `refreshAuthorizedMCNList` to reload the table. On error (400 Bad Request), it uses `hx-retarget` and `hx-reswap` to display error messages in the `messages-container` (which should be part of the `base.html` template).
    -   "Download" links use standard `<a>` tags with `target="_blank"` as they are file downloads, not in-page dynamic updates.
-   **Alpine.js for UI state management:**
    -   An `x-data="mcnRow(...)"` component is defined for each row in `_table.html`. This component manages the `qaQty`, `isChecked`, and `canEdit` states for that row.
    -   `x-bind:disabled` on the checkbox and input field dynamically enables/disables them based on `canEdit` and `isChecked` states, mirroring the ASP.NET logic of disabling if `MCNQty` equals `TotQAQty`.
    -   `@change` and `@input` listeners on the checkbox and input fields trigger Alpine.js methods for client-side validation and state updates.
-   **DataTables for list views:**
    -   `_table.html` includes the `<table id="authorizedMCNTable" ...>` tag.
    -   A `script` block within `_table.html` initializes DataTables on `$(document).ready()`, ensuring that DataTables is applied to the dynamically loaded table content. This ensures client-side searching, sorting, and pagination.
-   **No full page reloads:** All interactions (table load, form submission, messages) are handled via HTMX, avoiding full page reloads and providing a snappy user experience.

## Final Notes

This comprehensive plan details the migration of your ASP.NET application to a modern Django solution.

-   **Placeholders:** `[APP_NAME]`, `[MODEL_NAME]`, etc., have been replaced with `inventory_transactions`, `MaterialCreditNoteDetail`, etc., and other inferred values from the analysis.
-   **DRY Principles:** Templates extend `core/base.html` and reusable components like the DataTables table are in partials (`_table.html`). Business logic is concentrated in model managers.
-   **Fat Models, Thin Views:** Complex data retrieval (`get_authorized_mcn_details`) and transaction processing (`process_authorized_mcn_submission`) are delegated to the `MaterialCreditNoteDetailManager`, keeping views concise and focused on request/response handling.
-   **Comprehensive Tests:** Unit tests cover model methods and their complex logic, while integration tests validate view interactions and database effects, ensuring high code quality and reliability.
-   **HTMX and Alpine.js:** These technologies are central to creating a dynamic, modern frontend experience without relying on large JavaScript frameworks, fully replacing the legacy ASP.NET postback model and client-side scripts.
-   **Database Interaction:** Models are configured with `managed=False` and `db_table` to seamlessly integrate with your existing SQL Server database.

This detailed blueprint allows for an automated and systematic transition, enabling your team to embrace modern web development practices with Django.