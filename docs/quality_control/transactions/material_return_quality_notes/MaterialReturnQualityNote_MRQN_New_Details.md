This modernization plan details the transition of your ASP.NET Material Return Quality Note (MRQN) New Details page to a modern Django-based solution. We'll leverage Django's "Fat Model, Thin View" approach, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation, all styled with Tailwind CSS.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with multiple database tables, either directly or via lookups. The `loadgrid` function fetches data primarily from `tblInv_MaterialReturn_Details` and enriches it with data from `tblDG_Item_Master`, `Unit_Master`, `tblHR_Departments`, `tblQc_MaterialReturnQuality_Details` (for `RecQty`), and `tblQC_Scrapregister` (for `Qty`). The `btnProceed_Click` inserts into `tblQc_MaterialReturnQuality_Master`, `tblQc_MaterialReturnQuality_Details`, and `tblQC_Scrapregister`, and updates `tblDG_Item_Master`.

**Identified Tables:**
*   `tblInv_MaterialReturn_Master` (Let's call it `MaterialReturnMaster`)
*   `tblInv_MaterialReturn_Details` (Let's call it `MaterialReturnDetail`)
*   `tblDG_Item_Master` (Let's call it `ItemMaster`)
*   `Unit_Master` (Let's call it `UnitMaster`)
*   `tblHR_Departments` (Let's call it `Department`)
*   `tblQc_MaterialReturnQuality_Master` (Let's call it `MaterialReturnQualityNoteMaster`)
*   `tblQc_MaterialReturnQuality_Details` (Let's call it `MaterialReturnQualityNoteDetail`)
*   `tblQC_Scrapregister` (Let's call it `ScrapRegister`)

**Key Columns (inferred and directly used):**

**`tblInv_MaterialReturn_Master` (MaterialReturnMaster)**
*   `Id` (PK)
*   `CompId`
*   `MRNNo`

**`tblInv_MaterialReturn_Details` (MaterialReturnDetail)**
*   `Id` (PK) - Referred to as `lblId`
*   `MId` (FK to `MaterialReturnMaster.Id`)
*   `ItemId` (FK to `ItemMaster.Id`) - Referred to as `lblitemid`
*   `DeptId` (FK to `Department.Id`)
*   `WONo` - Referred to as `lblWONO`
*   `RetQty` (Returned Quantity) - Referred to as `lblretqty`
*   `Remarks` - Referred to as `lblremrk`

**`tblDG_Item_Master` (ItemMaster)**
*   `Id` (PK)
*   `CompId`
*   `ItemCode`
*   `ManfDesc`
*   `UOMBasic` (FK to `UnitMaster.Id`)
*   `StockQty`

**`Unit_Master` (UnitMaster)**
*   `Id` (PK)
*   `Symbol` (Unit of Measure symbol)

**`tblHR_Departments` (Department)**
*   `Id` (PK)
*   `Symbol` (Department symbol)

**`tblQc_MaterialReturnQuality_Master` (MaterialReturnQualityNoteMaster)**
*   `Id` (PK)
*   `SysDate`
*   `SysTime`
*   `CompId`
*   `FinYearId`
*   `SessionId`
*   `MRQNNo` (Generated MRQN Number)
*   `MRNNo` (from `MaterialReturnMaster`)
*   `MRNId` (FK to `MaterialReturnMaster.Id`)

**`tblQc_MaterialReturnQuality_Details` (MaterialReturnQualityNoteDetail)**
*   `Id` (PK)
*   `MId` (FK to `MaterialReturnQualityNoteMaster.Id`)
*   `MRQNNo` (from `MaterialReturnQualityNoteMaster`)
*   `MRNId` (FK to `MaterialReturnDetail.Id`) - This is `tblInv_MaterialReturn_Details.Id`
*   `AcceptedQty` - Referred to as `lblAccQty` (existing) and `txtAccpQty` (new input)

**`tblQC_Scrapregister` (ScrapRegister)**
*   `Id` (PK)
*   `SysDate`
*   `SysTime`
*   `CompId`
*   `FinYearId`
*   `SessionId`
*   `MRQNId` (FK to `MaterialReturnQualityNoteMaster.Id`)
*   `MRQNDId` (FK to `MaterialReturnQualityNoteDetail.Id`)
*   `ItemId` (FK to `ItemMaster.Id`)
*   `ScrapNo`
*   `Qty` - Referred to as `lblScrap` (existing scrap quantity)

## Step 2: Identify Backend Functionality

**Read:**
*   `loadgrid()` populates the `GridView` by fetching data from `tblInv_MaterialReturn_Details` and performing multiple lookups/aggregations from `tblDG_Item_Master`, `Unit_Master`, `tblHR_Departments`, `tblQc_MaterialReturnQuality_Details` (sum of `AcceptedQty`), and `tblQC_Scrapregister` (sum of `Qty`).
*   This is a complex data retrieval for a list display with derived fields.

**Create/Update:**
*   The `btnProceed_Click` handles the main "Generate MRQN" functionality.
*   It generates new `MRQNNo` and `ScrapNo`.
*   It *inserts* a new record into `tblQc_MaterialReturnQuality_Master`.
*   For each checked row in the grid:
    *   It *inserts* a new record into `tblQc_MaterialReturnQuality_Details` (linking to the newly created `MRQNMaster` and the original `MaterialReturnDetail`).
    *   If the 'Type' (Dropdown) is 'Scrap' (`Value=1`), it *inserts* a record into `tblQC_Scrapregister`.
    *   It *updates* the `StockQty` in `tblDG_Item_Master`.
*   Validation: Checks if `txtAccpQty` is valid (numeric, `Qty >= AccpQty`).

**Delete:** No explicit delete functionality on this page.

**Validation Logic:**
*   `RequiredFieldValidator` and `RegularExpressionValidator` for `txtAccpQty` in the ASPX.
*   Server-side validation in `btnProceed_Click`:
    *   `fun.NumberValidationQty(AccpQty.ToString()) == true`
    *   `Qty >= AccpQty` where `Qty = RetQty - ExistingAccpQty`.
    *   Checks if all checked rows have valid inputs (`x == y && y > 0`).

## Step 3: Infer UI Components

*   **GridView3:** Displays a list of `MaterialReturnDetail` items with associated lookup data. It includes read-only labels for most fields and input controls (`TextBox` for `Accepted Qty`, `DropDownList` for `Type`, `CheckBox` for selection) within each row.
*   **TextBox (txtAccpQty):** Accepts numeric input for the accepted quantity.
*   **DropDownList (Drptype):** Selects between 'Hold' and 'Scrap'.
*   **CheckBox (ck):** Allows selection of rows for processing. Initially checked if `remaining_qty_for_processing > 0`.
*   **Buttons (btnProceed, btnCancel):** Trigger form submission and navigation.

## Step 4: Generate Django Code

### 4.1 Models (quality_control/models.py)

We define models for all identified database tables. Crucially, the `MaterialReturnDetail` model will have properties to fetch derived display data (e.g., `item_code`, `manf_desc`, `uom_basic`, `dept_symbol`, `current_accepted_qty`, `current_scrap_qty`, `remaining_qty_for_processing`). This aligns with the "Fat Model" principle.

```python
# quality_control/models.py
from django.db import models, transaction
from django.db.models import Sum
from datetime import date, time

# Assuming core app has utility functions like get_next_sequence_number
# and current user/company/financial year context (e.g., via middleware or user profile)

# Dummy utility functions for demonstration; replace with actual implementation
class clsFunctions:
    @staticmethod
    def getCurrDate():
        return date.today().strftime('%Y-%m-%d')

    @staticmethod
    def getCurrTime():
        return time.now().strftime('%H:%M:%S')

    @staticmethod
    def get_next_sequence_number(model_class, field_name, comp_id, fin_year_id):
        last_obj = model_class.objects.filter(
            CompId=comp_id,
            FinYearId=fin_year_id
        ).order_by(f'-{field_name}').first()
        if last_obj:
            try:
                next_num = int(getattr(last_obj, field_name)) + 1
            except ValueError: # Handle non-numeric sequence numbers
                next_num = 1
        else:
            next_num = 1
        return str(next_num).zfill(4) # Format as '0001'

    @staticmethod
    def NumberValidationQty(qty_str):
        try:
            qty = float(qty_str)
            # Add more specific validation if needed (e.g., positive, within range)
            return qty >= 0
        except ValueError:
            return False

# Base model for common fields (CompId, FinYearId, SysDate, SysTime, SessionId)
# This could be a mixin or abstract base class in a real ERP system
class AuditedModel(models.Model):
    sys_date = models.DateField(db_column='SysDate', default=date.today)
    sys_time = models.TimeField(db_column='SysTime', default=time.now)
    comp_id = models.IntegerField(db_column='CompId') # Company ID
    fin_year_id = models.IntegerField(db_column='FinYearId') # Financial Year ID
    session_id = models.CharField(db_column='SessionId', max_length=50) # User Session ID/Username

    class Meta:
        abstract = True
        managed = False # Assume tables exist and are managed externally

class MaterialReturnMaster(AuditedModel):
    id = models.AutoField(db_column='Id', primary_key=True)
    mrn_no = models.CharField(db_column='MRNNo', max_length=50)

    class Meta(AuditedModel.Meta):
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Master'
        verbose_name_plural = 'Material Return Masters'

    def __str__(self):
        return f"MRN {self.mrn_no} (ID: {self.id})"

class ItemMaster(AuditedModel):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey('UnitMaster', models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', default=0.0)

    class Meta(AuditedModel.Meta):
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

    @transaction.atomic
    def update_stock_qty(self, quantity_change):
        # Business logic for updating stock
        self.stock_qty = float(self.stock_qty) + float(quantity_change)
        self.save(update_fields=['stock_qty'])
        # Add logging or other side effects if necessary
        return True

class UnitMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class Department(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.symbol

class MaterialReturnQualityNoteMaster(AuditedModel):
    id = models.AutoField(db_column='Id', primary_key=True)
    mrqn_no = models.CharField(db_column='MRQNNo', max_length=50)
    mrn_no = models.CharField(db_column='MRNNo', max_length=50) # Redundant, but matching legacy schema
    mrn_master = models.ForeignKey(MaterialReturnMaster, models.DO_NOTHING, db_column='MRNId', blank=True, null=True, related_name='quality_notes')

    class Meta(AuditedModel.Meta):
        db_table = 'tblQc_MaterialReturnQuality_Master'
        verbose_name = 'Material Return Quality Note Master'
        verbose_name_plural = 'Material Return Quality Note Masters'

    def __str__(self):
        return f"MRQN {self.mrqn_no} (ID: {self.id})"

    @classmethod
    @transaction.atomic
    def process_grid_data(cls, master_mrn_id, company_id, financial_year_id, session_id, processed_details_data):
        """
        Processes the submitted grid data to create MRQN master and detail records,
        scrap records if applicable, and updates item stock.
        """
        if not processed_details_data:
            return False, "No data submitted for processing."

        # Get MRN Master for context
        mrn_master_obj = MaterialReturnMaster.objects.filter(id=master_mrn_id).first()
        if not mrn_master_obj:
            return False, "Material Return Master not found."

        # Generate MRQNNo
        mrqn_no = clsFunctions.get_next_sequence_number(cls, 'MRQNNo', company_id, financial_year_id)

        # Create MaterialReturnQualityNoteMaster entry (only once per batch)
        mrqn_master = cls.objects.create(
            mrn_master=mrn_master_obj,
            mrqn_no=mrqn_no,
            mrn_no=mrn_master_obj.mrn_no, # Copy MRNNo as per original
            comp_id=company_id,
            fin_year_id=financial_year_id,
            session_id=session_id
        )

        scrap_no = clsFunctions.get_next_sequence_number(ScrapRegister, 'ScrapNo', company_id, financial_year_id)

        processed_count = 0
        for detail_data in processed_details_data:
            mrn_detail_id = detail_data.get('id')
            accepted_qty_input = detail_data.get('accepted_qty_input')
            item_type = detail_data.get('type') # 0: Hold, 1: Scrap

            if not mrn_detail_id:
                raise ValueError(f"Missing MRN Detail ID in data: {detail_data}")
            
            mrn_detail_obj = MaterialReturnDetail.objects.filter(id=mrn_detail_id).first()
            if not mrn_detail_obj:
                continue # Skip if detail not found (shouldn't happen with proper validation)

            # Convert quantity, perform validation
            try:
                accp_qty = float(accepted_qty_input)
                if not clsFunctions.NumberValidationQty(str(accp_qty)) or accp_qty < 0:
                     raise ValueError("Invalid accepted quantity format or negative value.")
            except (ValueError, TypeError):
                # This should ideally be caught by form validation
                continue

            # Original logic: Qty = RetQty - (lblAccpQty + lblScrap)
            # This means the *new* accepted qty (txtAccpQty) must be <= remaining quantity
            remaining_qty = mrn_detail_obj.return_qty - (mrn_detail_obj.current_accepted_qty + mrn_detail_obj.current_scrap_qty)

            if accp_qty > 0 and accp_qty <= remaining_qty:
                # Create MaterialReturnQualityNoteDetail entry
                mrqn_detail = MaterialReturnQualityNoteDetail.objects.create(
                    mrqn_master=mrqn_master,
                    mrn_detail=mrn_detail_obj,
                    accepted_qty=accp_qty,
                    mrqn_no=mrqn_master.mrqn_no, # Redundant but matches legacy
                    comp_id=company_id,
                    fin_year_id=financial_year_id,
                    session_id=session_id
                )

                # If type is Scrap (1)
                if item_type == 1:
                    # Calculate scrap quantity
                    total_accepted_for_mrn_detail = MaterialReturnQualityNoteDetail.objects.filter(
                        mrn_detail=mrn_detail_obj
                    ).aggregate(total_accepted=Sum('accepted_qty'))['total_accepted'] or 0.0

                    actual_scrap_qty = mrn_detail_obj.return_qty - total_accepted_for_mrn_detail

                    if actual_scrap_qty > 0:
                        ScrapRegister.objects.create(
                            mrqn_master=mrqn_master,
                            mrqn_detail=mrqn_detail,
                            item_master=mrn_detail_obj.item,
                            scrap_no=scrap_no,
                            qty=actual_scrap_qty,
                            comp_id=company_id,
                            fin_year_id=financial_year_id,
                            session_id=session_id
                        )

                # Update ItemMaster stock quantity
                if mrn_detail_obj.item:
                    mrn_detail_obj.item.update_stock_qty(accp_qty)
                
                processed_count += 1
            elif accp_qty > 0: # Only if user intended to accept but quantity is invalid
                # Log or handle invalid quantity input for this row
                # This might be an error case to report back
                print(f"Validation failed for MRN Detail {mrn_detail_id}: Accepted Qty {accp_qty} exceeds remaining {remaining_qty}")
        
        if processed_count > 0:
            return True, "MRQN generated successfully."
        else:
            return False, "No valid items were processed or no items selected."


class MaterialReturnQualityNoteDetail(AuditedModel):
    id = models.AutoField(db_column='Id', primary_key=True)
    mrqn_master = models.ForeignKey(MaterialReturnQualityNoteMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    mrqn_no = models.CharField(db_column='MRQNNo', max_length=50) # Redundant, but matching legacy schema
    mrn_detail = models.ForeignKey('MaterialReturnDetail', models.DO_NOTHING, db_column='MRNId', related_name='quality_notes')
    accepted_qty = models.FloatField(db_column='AcceptedQty', default=0.0)

    class Meta(AuditedModel.Meta):
        db_table = 'tblQc_MaterialReturnQuality_Details'
        verbose_name = 'Material Return Quality Note Detail'
        verbose_name_plural = 'Material Return Quality Note Details'

    def __str__(self):
        return f"MRQN Detail for MRN {self.mrn_detail.id} (Accepted: {self.accepted_qty})"

class ScrapRegister(AuditedModel):
    id = models.AutoField(db_column='Id', primary_key=True)
    mrqn_master = models.ForeignKey(MaterialReturnQualityNoteMaster, models.DO_NOTHING, db_column='MRQNId', related_name='scrap_records')
    mrqn_detail = models.ForeignKey(MaterialReturnQualityNoteDetail, models.DO_NOTHING, db_column='MRQNDId', related_name='scrap_records_from_detail')
    item_master = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', related_name='scrap_records')
    scrap_no = models.CharField(db_column='ScrapNo', max_length=50)
    qty = models.FloatField(db_column='Qty', default=0.0)

    class Meta(AuditedModel.Meta):
        db_table = 'tblQC_Scrapregister'
        verbose_name = 'Scrap Register'
        verbose_name_plural = 'Scrap Registers'

    def __str__(self):
        return f"Scrap No: {self.scrap_no} (Qty: {self.qty})"


class MaterialReturnDetail(AuditedModel): # AuditedModel not strictly needed if it doesn't have sys_date, etc.
    id = models.AutoField(db_column='Id', primary_key=True)
    mrn_master = models.ForeignKey(MaterialReturnMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', related_name='material_return_details')
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    return_qty = models.FloatField(db_column='RetQty', default=0.0)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta(AuditedModel.Meta):
        db_table = 'tblInv_MaterialReturn_Details'
        verbose_name = 'Material Return Detail'
        verbose_name_plural = 'Material Return Details'
        # Remove managed = False if you decide to manage this model in Django

    def __str__(self):
        return f"MRN Detail {self.id} (Item: {self.item.item_code}, Ret Qty: {self.return_qty})"

    @property
    def item_code(self):
        return self.item.item_code if self.item else 'N/A'

    @property
    def manufacturer_description(self):
        return self.item.manf_desc if self.item else 'N/A'

    @property
    def uom_basic_symbol(self):
        return self.item.uom_basic.symbol if self.item and self.item.uom_basic else 'N/A'

    @property
    def department_symbol(self):
        return self.department.symbol if self.department else 'N/A'

    @property
    def current_accepted_qty(self):
        """Calculates sum of AcceptedQty for this MRN Detail from MaterialReturnQualityNoteDetail."""
        return self.quality_notes.aggregate(total=Sum('accepted_qty'))['total'] or 0.0

    @property
    def current_scrap_qty(self):
        """Calculates sum of Qty for this MRN Detail from ScrapRegister."""
        # Note: In original code, scrap qty was from ScrapRegister filtered by MRNId (tblInv_MaterialReturn_Details.Id)
        # and also by MRQNDId. This needs careful mapping.
        # Assuming tblQC_Scrapregister.MRQNDId links to tblQc_MaterialReturnQuality_Details.Id,
        # and tblQc_MaterialReturnQuality_Details.MRNId links to tblInv_MaterialReturn_Details.Id
        # So, we can filter through the quality_notes.
        return ScrapRegister.objects.filter(
            mrqn_detail__mrn_detail=self
        ).aggregate(total=Sum('qty'))['total'] or 0.0

    @property
    def remaining_qty_for_processing(self):
        """Calculates the quantity still available for quality processing."""
        return self.return_qty - (self.current_accepted_qty + self.current_scrap_qty)

    @property
    def is_eligible_for_processing(self):
        """Determines if this detail row should have its checkbox checked (like in original ASP.NET)."""
        return self.remaining_qty_for_processing > 0

```

### 4.2 Forms (quality_control/forms.py)

For a grid of inputs, a `FormSet` is the most idiomatic Django approach. We'll define a basic form for each row's editable fields and then use `formset_factory`.

```python
# quality_control/forms.py
from django import forms
from django.forms import formset_factory
from .models import MaterialReturnQualityNoteDetail
import re

class MaterialReturnDetailForm(forms.Form):
    """
    Form to capture input for a single MaterialReturnDetail row in the grid.
    This is not a ModelForm for MaterialReturnQualityNoteDetail directly,
    as it combines info from MaterialReturnDetail and user input for a NEW QualityNoteDetail.
    """
    mrn_detail_id = forms.IntegerField(widget=forms.HiddenInput())
    # Existing quantities are for display and server-side validation
    returned_qty = forms.FloatField(widget=forms.HiddenInput())
    existing_accepted_qty = forms.FloatField(widget=forms.HiddenInput())
    existing_scrap_qty = forms.FloatField(widget=forms.HiddenInput())
    
    # User input fields
    accepted_qty_input = forms.CharField(
        required=False, # Can be empty if not selected
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'type': 'number', # HTML5 number input
            'step': '0.001' # Allows decimal values
        })
    )
    TYPE_CHOICES = [
        (0, 'Hold'),
        (1, 'Scrap'),
    ]
    type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        initial=0
    )
    
    # Checkbox for selection (handled by the view based on accepted_qty_input)
    # The ASP.NET checkbox was based on remaining_qty, but actual processing depends on input.
    # We will implicitly check based on whether accepted_qty_input has a valid number.
    # For explicit checkbox control, add a boolean field and link it to JS.
    is_selected = forms.BooleanField(required=False, widget=forms.CheckboxInput(attrs={
        'class': 'form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'
    }))

    def clean_accepted_qty_input(self):
        qty_str = self.cleaned_data.get('accepted_qty_input')
        is_selected = self.cleaned_data.get('is_selected')

        if not is_selected and not qty_str: # If not selected and no quantity, it's fine
            return None
        
        if not qty_str:
            raise forms.ValidationError("Accepted quantity is required for selected items.")
        
        # Regex equivalent of ValidationExpression="^\d{1,15}(\.\d{0,3})?$"
        if not re.match(r"^\d{1,15}(\.\d{0,3})?$", qty_str):
            raise forms.ValidationError("Invalid quantity format. Max 15 digits before, 3 after decimal.")

        try:
            qty = float(qty_str)
            if qty < 0:
                raise forms.ValidationError("Quantity cannot be negative.")
            return qty
        except ValueError:
            raise forms.ValidationError("Invalid number format.")

    def clean(self):
        cleaned_data = super().clean()
        accepted_qty_input = cleaned_data.get('accepted_qty_input')
        returned_qty = cleaned_data.get('returned_qty')
        existing_accepted_qty = cleaned_data.get('existing_accepted_qty')
        existing_scrap_qty = cleaned_data.get('existing_scrap_qty')
        is_selected = cleaned_data.get('is_selected')

        if not is_selected and not accepted_qty_input:
            # If not selected and no quantity input, this row is skipped from processing
            return cleaned_data
            
        if is_selected and accepted_qty_input is None:
            # If selected but accepted_qty_input is empty (due to validation error), prevent further checks
            return cleaned_data

        if accepted_qty_input is not None:
            remaining_qty = returned_qty - (existing_accepted_qty + existing_scrap_qty)
            if accepted_qty_input > remaining_qty:
                self.add_error('accepted_qty_input', f"Accepted quantity ({accepted_qty_input:.3f}) cannot exceed remaining quantity ({remaining_qty:.3f}).")
        
        return cleaned_data

# Create a formset for multiple MaterialReturnDetailForm instances
MaterialReturnDetailFormSet = formset_factory(MaterialReturnDetailForm, extra=0)
```

### 4.3 Views (quality_control/views.py)

Given the complexity of handling a formset within a grid view, a `TemplateView` combined with manual formset handling or a `FormView` with custom `get_context_data` and `post` methods is suitable. We'll use a `TemplateView` to keep the logic clear.

```python
# quality_control/views.py
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from django.db import transaction

from .models import MaterialReturnMaster, MaterialReturnDetail, MaterialReturnQualityNoteMaster # Import all necessary models
from .forms import MaterialReturnDetailFormSet

class MaterialReturnQualityNoteNewDetailsView(TemplateView):
    template_name = 'quality_control/materialreturnqualitynote_new_details/list.html' # Main page template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve parameters from URL query string (equivalent to Request.QueryString)
        # In a real app, you might use self.request.user and other session data
        master_mrn_id = self.request.GET.get('Id') # MId
        fyid = self.request.GET.get('fyid')
        mrn_no = self.request.GET.get('mrnno')

        # Dummy session data (replace with actual session/user context in production)
        comp_id = int(self.request.session.get('compid', 1)) 
        session_id = self.request.session.get('username', 'system_user')
        fin_year_id = int(self.request.session.get('finyear', 1))

        context['master_mrn_id'] = master_mrn_id
        context['fyid'] = fyid
        context['mrn_no'] = mrn_no
        context['comp_id'] = comp_id
        context['session_id'] = session_id
        context['fin_year_id'] = fin_year_id

        # Fetch MaterialReturnMaster for context (e.g., to display MRNNo)
        mrn_master = get_object_or_404(MaterialReturnMaster, id=master_mrn_id)
        context['mrn_master'] = mrn_master

        # Prepare initial data for the formset (equivalent to loadgrid())
        # Filter MaterialReturnDetail objects related to the current MRN Master
        material_return_details = MaterialReturnDetail.objects.filter(mrn_master=mrn_master).order_by('id')
        
        # Build initial data for the formset
        initial_form_data = []
        for detail in material_return_details:
            initial_form_data.append({
                'mrn_detail_id': detail.id,
                'returned_qty': detail.return_qty,
                'existing_accepted_qty': detail.current_accepted_qty,
                'existing_scrap_qty': detail.current_scrap_qty,
                'is_selected': detail.is_eligible_for_processing, # Pre-check based on remaining qty
                'accepted_qty_input': detail.remaining_qty_for_processing if detail.is_eligible_for_processing else 0, # Pre-fill with remaining
                'type': 0, # Default to 'Hold'
            })
            
        formset = MaterialReturnDetailFormSet(initial=initial_form_data, prefix='mrqd')
        context['formset'] = formset
        
        return context

    def post(self, request, *args, **kwargs):
        master_mrn_id = request.GET.get('Id')
        comp_id = int(request.session.get('compid', 1)) 
        session_id = request.session.get('username', 'system_user')
        fin_year_id = int(request.session.get('finyear', 1))

        formset = MaterialReturnDetailFormSet(request.POST, prefix='mrqd')

        if formset.is_valid():
            processed_data = []
            for form in formset:
                if form.cleaned_data.get('is_selected') and form.cleaned_data.get('accepted_qty_input') is not None:
                    # Only include selected items with valid quantity input
                    processed_data.append({
                        'id': form.cleaned_data['mrn_detail_id'],
                        'accepted_qty_input': form.cleaned_data['accepted_qty_input'],
                        'type': form.cleaned_data['type'],
                    })
            
            try:
                success, message = MaterialReturnQualityNoteMaster.process_grid_data(
                    master_mrn_id, comp_id, fin_year_id, session_id, processed_data
                )
                if success:
                    messages.success(request, message)
                    # Redirect after success. Original navigated back to a list page.
                    return HttpResponseRedirect(reverse_lazy('quality_control:materialreturnqualitynote_list'))
                else:
                    messages.error(request, message)

            except Exception as e:
                # Catch general exceptions from model processing
                messages.error(request, f"An error occurred during processing: {e}")
                # Log the exception for debugging
                import logging
                logging.exception("Error processing MRQN grid data.")

        else:
            # Formset is not valid, display errors
            messages.error(request, "Please correct the errors in the form.")
            
        # If not successful or formset invalid, re-render the page with errors
        context = self.get_context_data(**kwargs) # Re-get context to pre-populate formset with POST data
        context['formset'] = formset # Pass the formset with errors
        return render(request, self.template_name, context)

# View for the HTMX-loaded table partial (if needed, but for this page, it's one large form)
# For this specific scenario, the whole grid is part of one form.
# If we were to have a separate view for *just* the table refreshing, it would be like this:
class MaterialReturnDetailTablePartialView(TemplateView):
    template_name = 'quality_control/materialreturnqualitynote_new_details/_table_partial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        master_mrn_id = self.request.GET.get('Id') # MId
        
        mrn_master = get_object_or_404(MaterialReturnMaster, id=master_mrn_id)
        material_return_details = MaterialReturnDetail.objects.filter(mrn_master=mrn_master).order_by('id')
        
        initial_form_data = []
        for detail in material_return_details:
            initial_form_data.append({
                'mrn_detail_id': detail.id,
                'returned_qty': detail.return_qty,
                'existing_accepted_qty': detail.current_accepted_qty,
                'existing_scrap_qty': detail.current_scrap_qty,
                'is_selected': detail.is_eligible_for_processing,
                'accepted_qty_input': detail.remaining_qty_for_processing if detail.is_eligible_for_processing else 0,
                'type': 0,
            })
        formset = MaterialReturnDetailFormSet(initial=initial_form_data, prefix='mrqd')
        context['formset'] = formset
        context['material_return_details'] = material_return_details # Pass details for display
        return context

```

### 4.4 Templates

We will create a main template for the page and a partial template for the table content that is dynamically loaded/refreshed. The main page will contain the overall form.

**`quality_control/materialreturnqualitynote_new_details/list.html`** (Main Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Return Quality Note [MRQN] - New</h2>
    </div>

    <!-- Message Display Area -->
    <div id="messages-container" hx-swap-oob="outerHTML:#messages-container">
        {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}" role="alert">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>

    <!-- MRN Master Info (from query params) -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Material Return Details</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div><strong>MRN No:</strong> {{ mrn_master.mrn_no }}</div>
            <div><strong>MRN ID:</strong> {{ mrn_master.id }}</div>
            <div><strong>Financial Year:</strong> {{ fyid }}</div>
            <div><strong>Company ID:</strong> {{ comp_id }}</div>
            <div><strong>User:</strong> {{ session_id }}</div>
        </div>
    </div>

    <form method="post" action="{% url 'quality_control:mrqn_new_details' %}?Id={{ master_mrn_id }}&fyid={{ fyid }}&mrnno={{ mrn_no }}"
          hx-post="{% url 'quality_control:mrqn_new_details' %}?Id={{ master_mrn_id }}&fyid={{ fyid }}&mrnno={{ mrn_no }}"
          hx-trigger="submit"
          hx-target="#messages-container"
          hx-swap="outerHTML"
          hx-indicator="#loadingIndicator"
          hx-on::after-request="if(event.detail.xhr.status == 204) window.location.href = '{% url 'quality_control:materialreturnqualitynote_list' %}'"
          hx-confirm="Are you sure you want to generate the MRQN?"
          id="mrqnForm">
        {% csrf_token %}

        <div id="mrqn-table-container"
             hx-trigger="load, refreshMRQNTable from:body"
             hx-get="{% url 'quality_control:mrqn_new_details_table_partial' %}?Id={{ master_mrn_id }}&fyid={{ fyid }}&mrnno={{ mrn_no }}"
             hx-swap="innerHTML">
            <!-- Loading indicator for table -->
            <div id="loadingIndicator" class="htmx-indicator flex items-center justify-center p-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="ml-2">Loading data...</p>
            </div>
        </div>

        <div class="flex justify-end space-x-4 mt-6">
            <button type="button" 
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                    onclick="window.location.href='{% url 'quality_control:materialreturnqualitynote_list' %}'">
                Cancel
            </button>
            <button type="submit"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out">
                Generate MRQN
            </button>
        </div>
    </form>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('mrqnDetailRow', (initialSelected, initialAccpQty) => ({
            isSelected: initialSelected,
            acceptedQty: initialAccpQty,
            init() {
                // Ensure the checkbox state matches initial load
                // Or update checkbox based on `acceptedQty` if it's auto-checked
                if (this.acceptedQty > 0) {
                    this.isSelected = true;
                }
            },
            toggleSelection() {
                this.isSelected = !this.isSelected;
            },
            updateAcceptedQty(event) {
                let val = parseFloat(event.target.value);
                this.acceptedQty = isNaN(val) ? 0 : val;
                // Auto-select if quantity is entered
                if (this.acceptedQty > 0) {
                    this.isSelected = true;
                }
            }
        }));
    });

    // Initialize DataTables after HTMX loads the table content
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.target.id === 'mrqn-table-container') {
            $('#mrqnDetailTable').DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "pagingType": "simple_numbers", // Smaller pagination
                "autoWidth": false, // Prevent DataTables from setting column widths automatically
                "columns": [ // Define column options for better rendering and sorting
                    { "orderable": false, "searchable": false }, // SN
                    { "orderable": false, "searchable": false }, // Checkbox
                    null, // Item Code
                    null, // Manf Desc
                    null, // UOM
                    null, // Dept
                    null, // WO No
                    null, // Ret Qty
                    null, // Accepted Qty (Existing)
                    null, // Accp Qty (New input)
                    null, // Type
                    null, // Scrap Qty (Existing)
                    null  // Remarks
                ]
            });
        }
    });

    // Client-side confirmation for Generate MRQN (alternative to hx-confirm if more complex)
    // function confirmationAdd() {
    //     return confirm("Are you sure you want to generate the MRQN?");
    // }
</script>
{% endblock %}
```

**`quality_control/materialreturnqualitynote_new_details/_table_partial.html`** (HTMX-loaded Table)

```html
<table id="mrqnDetailTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Select</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Manf Desc</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Dept</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Ret Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Accepted Qty (Prev)</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Accepted Qty (New)</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Scrap Qty (Prev)</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Remarks</th>
        </tr>
    </thead>
    <tbody>
        {% if formset.forms %}
            {% for form in formset %}
            <tr x-data="mrqnDetailRow('{{ form.initial.is_selected }}' === 'True', parseFloat('{{ form.initial.accepted_qty_input|default:0 }}'))" 
                :class="isSelected ? 'bg-blue-50/50' : ''">
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {{ form.is_selected }}
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-mrn_detail_id" value="{{ form.initial.mrn_detail_id }}">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-returned_qty" value="{{ form.initial.returned_qty }}">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-existing_accepted_qty" value="{{ form.initial.existing_accepted_qty }}">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-existing_scrap_qty" value="{{ form.initial.existing_scrap_qty }}">
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ material_return_details|get_item:forloop.counter0.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ material_return_details|get_item:forloop.counter0.manufacturer_description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-center">{{ material_return_details|get_item:forloop.counter0.uom_basic_symbol }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-center">{{ material_return_details|get_item:forloop.counter0.department_symbol }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-center">{{ material_return_details|get_item:forloop.counter0.wo_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-right">{{ material_return_details|get_item:forloop.counter0.return_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-right">{{ material_return_details|get_item:forloop.counter0.current_accepted_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm">
                    {{ form.accepted_qty_input }}
                    {% if form.accepted_qty_input.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.accepted_qty_input.errors|first }}</p>
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm">
                    {{ form.type }}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-right">{{ material_return_details|get_item:forloop.counter0.current_scrap_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ material_return_details|get_item:forloop.counter0.remarks }}</td>
            </tr>
            {% endfor %}
            {{ formset.management_form }} {# Required for formsets #}
        {% else %}
        <tr>
            <td colspan="13" class="py-4 text-center text-gray-500 font-medium">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

{% comment %}
    Custom filter to access object properties by index
    This is a workaround as formset initial data doesn't directly map to material_return_details in template iteration.
    Ideally, context for each form in formset should contain the original detail object.
    For production, consider passing the material_return_details list directly into the formset's initial data,
    or use a custom formset that attaches the instance to each form.
{% endcomment %}
{% load custom_filters %} {# Assuming a custom filter for accessing list items by index #}
```

**`quality_control/templatetags/custom_filters.py`** (Create this file and `__init__.py` in `quality_control/templatetags/`)

```python
from django import template

register = template.Library()

@register.filter(name='get_item')
def get_item(queryset, index):
    """
    Allows indexing into a queryset/list within a template.
    Example: {{ my_list|get_item:0 }}
    """
    if queryset and isinstance(index, int) and len(queryset) > index:
        return queryset[index]
    return '' # Or None, or raise an error depending on desired behavior
```
*Remember to add `{% load custom_filters %}` at the top of your template.*

### 4.5 URLs (quality_control/urls.py)

```python
# quality_control/urls.py
from django.urls import path
from .views import MaterialReturnQualityNoteNewDetailsView, MaterialReturnDetailTablePartialView

app_name = 'quality_control' # Define app_name for namespacing URLs

urlpatterns = [
    # Main page for Material Return Quality Note - New Details (GET for display, POST for processing)
    path('material_return_quality_note/new_details/', MaterialReturnQualityNoteNewDetailsView.as_view(), name='mrqn_new_details'),
    
    # HTMX endpoint for just the table content (if needed, e.g., after filter changes)
    # The main page uses this initially and for refresh.
    path('material_return_quality_note/new_details/table_partial/', MaterialReturnDetailTablePartialView.as_view(), name='mrqn_new_details_table_partial'),

    # Placeholder for the list page it redirects to
    path('material_return_quality_note/list/', MaterialReturnQualityNoteMaster.as_view(), name='materialreturnqualitynote_list'), # Replace with actual ListView if exists
]
```
**Note:** You'll need an actual `MaterialReturnQualityNoteMaster` ListView if you're redirecting to `quality_control:materialreturnqualitynote_list`. For demonstration purposes, I've just placed a placeholder.

### 4.6 Tests

**`quality_control/tests.py`**

```python
# quality_control/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db.models import Sum
from datetime import date, time
from unittest.mock import patch, MagicMock

from .models import (
    MaterialReturnMaster, MaterialReturnDetail, ItemMaster, UnitMaster, Department,
    MaterialReturnQualityNoteMaster, MaterialReturnQualityNoteDetail, ScrapRegister,
    clsFunctions # For mocking utility functions
)
from .forms import MaterialReturnDetailFormSet

class QualityControlModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.session_id = 'testuser'

        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.dept_prod = Department.objects.create(id=10, symbol='PROD')
        cls.item1 = ItemMaster.objects.create(
            id=101, item_code='ITEM001', manf_desc='Widget A', uom_basic=cls.unit_ea, stock_qty=100.0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.item2 = ItemMaster.objects.create(
            id=102, item_code='ITEM002', manf_desc='Gadget B', uom_basic=cls.unit_ea, stock_qty=50.0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.mrn_master = MaterialReturnMaster.objects.create(
            id=1, mrn_no='MRN0001',
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.mrn_detail1 = MaterialReturnDetail.objects.create(
            id=1001, mrn_master=cls.mrn_master, item=cls.item1, department=cls.dept_prod,
            wo_no='WO-ABC', return_qty=10.0, remarks='Some defect',
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.mrn_detail2 = MaterialReturnDetail.objects.create(
            id=1002, mrn_master=cls.mrn_master, item=cls.item2, department=cls.dept_prod,
            wo_no='WO-XYZ', return_qty=5.0, remarks='Minor damage',
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )

    def setUp(self):
        self.client = Client()
        # Set up mock session data for each test run
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id
        self.client.session['username'] = self.session_id
        self.url_params = f'Id={self.mrn_master.id}&fyid={self.fin_year_id}&mrnno={self.mrn_master.mrn_no}'
        self.list_url = reverse('quality_control:mrqn_new_details') + f'?{self.url_params}'

    def test_material_return_detail_properties(self):
        self.assertEqual(self.mrn_detail1.item_code, 'ITEM001')
        self.assertEqual(self.mrn_detail1.manufacturer_description, 'Widget A')
        self.assertEqual(self.mrn_detail1.uom_basic_symbol, 'EA')
        self.assertEqual(self.mrn_detail1.department_symbol, 'PROD')
        self.assertEqual(self.mrn_detail1.current_accepted_qty, 0.0)
        self.assertEqual(self.mrn_detail1.current_scrap_qty, 0.0)
        self.assertEqual(self.mrn_detail1.remaining_qty_for_processing, 10.0)
        self.assertTrue(self.mrn_detail1.is_eligible_for_processing)

        # Test with some existing quality notes
        mrqn_master_existing = MaterialReturnQualityNoteMaster.objects.create(
            mrn_master=self.mrn_master, mrqn_no='0001', mrn_no='MRN0001',
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id
        )
        MaterialReturnQualityNoteDetail.objects.create(
            mrqn_master=mrqn_master_existing, mrn_detail=self.mrn_detail1, accepted_qty=3.0, mrqn_no='0001',
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id
        )
        MaterialReturnQualityNoteDetail.objects.create(
            mrqn_master=mrqn_master_existing, mrn_detail=self.mrn_detail1, accepted_qty=2.0, mrqn_no='0001',
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id
        )
        
        # Re-fetch to get updated properties
        mrn_detail1_updated = MaterialReturnDetail.objects.get(id=self.mrn_detail1.id)
        self.assertEqual(mrn_detail1_updated.current_accepted_qty, 5.0)
        self.assertEqual(mrn_detail1_updated.remaining_qty_for_processing, 5.0)
        self.assertTrue(mrn_detail1_updated.is_eligible_for_processing)

        # Add scrap
        scrap_reg = ScrapRegister.objects.create(
            mrqn_master=mrqn_master_existing,
            mrqn_detail=MaterialReturnQualityNoteDetail.objects.filter(mrn_detail=self.mrn_detail1).first(), # Link to an existing detail
            item_master=self.item1, scrap_no='S001', qty=1.0,
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id
        )
        mrn_detail1_updated = MaterialReturnDetail.objects.get(id=self.mrn_detail1.id)
        self.assertEqual(mrn_detail1_updated.current_scrap_qty, 1.0)
        self.assertEqual(mrn_detail1_updated.remaining_qty_for_processing, 4.0)

    @patch('quality_control.models.clsFunctions.get_next_sequence_number')
    @patch('quality_control.models.clsFunctions.NumberValidationQty', return_value=True) # Mock validation to always pass
    def test_process_grid_data(self, mock_validation_qty, mock_get_next_sequence_number):
        mock_get_next_sequence_number.side_effect = ['0002', '0003'] # MRQNNo, ScrapNo

        processed_data = [
            {'id': self.mrn_detail1.id, 'accepted_qty_input': 4.0, 'type': 0, 'is_selected': True},
            {'id': self.mrn_detail2.id, 'accepted_qty_input': 2.0, 'type': 1, 'is_selected': True},
            # Unselected item should be ignored
            {'id': 9999, 'accepted_qty_input': 1.0, 'type': 0, 'is_selected': False} # Invalid ID, unselected
        ]

        success, message = MaterialReturnQualityNoteMaster.process_grid_data(
            self.mrn_master.id, self.comp_id, self.fin_year_id, self.session_id, processed_data
        )

        self.assertTrue(success)
        self.assertEqual(message, "MRQN generated successfully.")

        self.assertEqual(MaterialReturnQualityNoteMaster.objects.count(), 1)
        mrqn_master = MaterialReturnQualityNoteMaster.objects.first()
        self.assertEqual(mrqn_master.mrqn_no, '0002')

        self.assertEqual(MaterialReturnQualityNoteDetail.objects.count(), 2)
        mrqn_detail1 = MaterialReturnQualityNoteDetail.objects.get(mrn_detail=self.mrn_detail1)
        mrqn_detail2 = MaterialReturnQualityNoteDetail.objects.get(mrn_detail=self.mrn_detail2)
        self.assertEqual(mrqn_detail1.accepted_qty, 4.0)
        self.assertEqual(mrqn_detail2.accepted_qty, 2.0)

        # Check stock updates
        self.item1.refresh_from_db()
        self.item2.refresh_from_db()
        self.assertEqual(self.item1.stock_qty, 100.0 + 4.0) # Original + Accepted Qty
        self.assertEqual(self.item2.stock_qty, 50.0 + 2.0)

        # Check scrap register for item2 (type=1)
        self.assertEqual(ScrapRegister.objects.count(), 1)
        scrap_record = ScrapRegister.objects.first()
        self.assertEqual(scrap_record.item_master, self.item2)
        self.assertEqual(scrap_record.qty, self.mrn_detail2.return_qty - mrqn_detail2.accepted_qty) # 5.0 - 2.0 = 3.0
        self.assertEqual(scrap_record.scrap_no, '0003')

    def test_process_grid_data_no_valid_items(self):
        processed_data = [
            {'id': self.mrn_detail1.id, 'accepted_qty_input': 0.0, 'type': 0, 'is_selected': True}, # 0 quantity
            {'id': self.mrn_detail2.id, 'accepted_qty_input': 100.0, 'type': 0, 'is_selected': True}, # Exceeds return qty
        ]
        success, message = MaterialReturnQualityNoteMaster.process_grid_data(
            self.mrn_master.id, self.comp_id, self.fin_year_id, self.session_id, processed_data
        )
        self.assertFalse(success)
        self.assertEqual(message, "No valid items were processed or no items selected.")
        self.assertEqual(MaterialReturnQualityNoteMaster.objects.count(), 0) # No master created

class MaterialReturnQualityNoteNewDetailsViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.session_id = 'testuser'

        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.dept_prod = Department.objects.create(id=10, symbol='PROD')
        cls.item1 = ItemMaster.objects.create(
            id=101, item_code='ITEM001', manf_desc='Widget A', uom_basic=cls.unit_ea, stock_qty=100.0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.item2 = ItemMaster.objects.create(
            id=102, item_code='ITEM002', manf_desc='Gadget B', uom_basic=cls.unit_ea, stock_qty=50.0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.mrn_master = MaterialReturnMaster.objects.create(
            id=1, mrn_no='MRN0001',
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.mrn_detail1 = MaterialReturnDetail.objects.create(
            id=1001, mrn_master=cls.mrn_master, item=cls.item1, department=cls.dept_prod,
            wo_no='WO-ABC', return_qty=10.0, remarks='Some defect',
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )
        cls.mrn_detail2 = MaterialReturnDetail.objects.create(
            id=1002, mrn_master=cls.mrn_master, item=cls.item2, department=cls.dept_prod,
            wo_no='WO-XYZ', return_qty=5.0, remarks='Minor damage',
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id, session_id=cls.session_id
        )

    def setUp(self):
        self.client = Client()
        # Set up mock session data for each test run
        self.session = self.client.session
        self.session['compid'] = self.comp_id
        self.session['finyear'] = self.fin_year_id
        self.session['username'] = self.session_id
        self.session.save() # Crucial to save session before making requests

        self.url_params = f'Id={self.mrn_master.id}&fyid={self.fin_year_id}&mrnno={self.mrn_master.mrn_no}'
        self.view_url = reverse('quality_control:mrqn_new_details') + f'?{self.url_params}'
        self.redirect_url = reverse('quality_control:materialreturnqualitynote_list') # Assuming this exists

    def test_get_request_loads_formset_correctly(self):
        response = self.client.get(self.view_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialreturnqualitynote_new_details/list.html')
        self.assertIn('formset', response.context)
        
        formset = response.context['formset']
        self.assertEqual(len(formset), 2) # Should contain 2 forms for 2 details
        
        # Check initial values for first form
        form1 = formset[0]
        self.assertEqual(form1.initial['mrn_detail_id'], self.mrn_detail1.id)
        self.assertEqual(form1.initial['returned_qty'], self.mrn_detail1.return_qty)
        self.assertEqual(form1.initial['existing_accepted_qty'], 0.0)
        self.assertEqual(form1.initial['existing_scrap_qty'], 0.0)
        self.assertTrue(form1.initial['is_selected']) # Should be eligible by default
        self.assertEqual(form1.initial['accepted_qty_input'], 10.0) # Pre-filled with remaining qty

    @patch('quality_control.models.MaterialReturnQualityNoteMaster.process_grid_data')
    def test_post_request_valid_data_redirects(self, mock_process_grid_data):
        mock_process_grid_data.return_value = (True, "MRQN generated successfully.")
        
        formset_data = {
            'mrqd-TOTAL_FORMS': '2',
            'mrqd-INITIAL_FORMS': '2',
            'mrqd-MIN_NUM_FORMS': '0',
            'mrqd-MAX_NUM_FORMS': '',
            
            'mrqd-0-mrn_detail_id': str(self.mrn_detail1.id),
            'mrqd-0-returned_qty': str(self.mrn_detail1.return_qty),
            'mrqd-0-existing_accepted_qty': str(self.mrn_detail1.current_accepted_qty),
            'mrqd-0-existing_scrap_qty': str(self.mrn_detail1.current_scrap_qty),
            'mrqd-0-accepted_qty_input': '5.0',
            'mrqd-0-type': '0', # Hold
            'mrqd-0-is_selected': 'on', # Select this row

            'mrqd-1-mrn_detail_id': str(self.mrn_detail2.id),
            'mrqd-1-returned_qty': str(self.mrn_detail2.return_qty),
            'mrqd-1-existing_accepted_qty': str(self.mrn_detail2.current_accepted_qty),
            'mrqd-1-existing_scrap_qty': str(self.mrn_detail2.current_scrap_qty),
            'mrqd-1-accepted_qty_input': '2.0',
            'mrqd-1-type': '1', # Scrap
            'mrqd-1-is_selected': 'on', # Select this row
        }

        response = self.client.post(self.view_url, formset_data, HTTP_HX_REQUEST='true')
        
        # HTMX will return 204 No Content for successful operations + HX-Trigger header
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMRQNList') # Assuming this is triggered for redirection
        
        # Verify that process_grid_data was called with correct arguments
        expected_processed_data = [
            {'id': self.mrn_detail1.id, 'accepted_qty_input': 5.0, 'type': '0'},
            {'id': self.mrn_detail2.id, 'accepted_qty_input': 2.0, 'type': '1'},
        ]
        mock_process_grid_data.assert_called_once_with(
            str(self.mrn_master.id), self.comp_id, self.fin_year_id, self.session_id, expected_processed_data
        )

    def test_post_request_invalid_quantity_renders_with_errors(self):
        formset_data = {
            'mrqd-TOTAL_FORMS': '2',
            'mrqd-INITIAL_FORMS': '2',
            'mrqd-MIN_NUM_FORMS': '0',
            'mrqd-MAX_NUM_FORMS': '',
            
            'mrqd-0-mrn_detail_id': str(self.mrn_detail1.id),
            'mrqd-0-returned_qty': str(self.mrn_detail1.return_qty),
            'mrqd-0-existing_accepted_qty': str(self.mrn_detail1.current_accepted_qty),
            'mrqd-0-existing_scrap_qty': str(self.mrn_detail1.current_scrap_qty),
            'mrqd-0-accepted_qty_input': '15.0', # Invalid: > return_qty (10.0)
            'mrqd-0-type': '0',
            'mrqd-0-is_selected': 'on',

            'mrqd-1-mrn_detail_id': str(self.mrn_detail2.id),
            'mrqd-1-returned_qty': str(self.mrn_detail2.return_qty),
            'mrqd-1-existing_accepted_qty': str(self.mrn_detail2.current_accepted_qty),
            'mrqd-1-existing_scrap_qty': str(self.mrn_detail2.current_scrap_qty),
            'mrqd-1-accepted_qty_input': 'abc', # Invalid: non-numeric
            'mrqd-1-type': '1',
            'mrqd-1-is_selected': 'on',
        }

        response = self.client.post(self.view_url, formset_data)
        
        self.assertEqual(response.status_code, 200) # Should render the page again with errors
        self.assertTemplateUsed(response, 'quality_control/materialreturnqualitynote_new_details/list.html')
        self.assertContains(response, "Please correct the errors in the form.")
        self.assertContains(response, "Accepted quantity (15.000) cannot exceed remaining quantity (10.000).")
        self.assertContains(response, "Invalid number format.")

    def test_post_request_unselected_items_are_ignored(self):
        # No items selected, should result in a message about no valid items
        formset_data = {
            'mrqd-TOTAL_FORMS': '2',
            'mrqd-INITIAL_FORMS': '2',
            'mrqd-MIN_NUM_FORMS': '0',
            'mrqd-MAX_NUM_FORMS': '',
            
            'mrqd-0-mrn_detail_id': str(self.mrn_detail1.id),
            'mrqd-0-returned_qty': str(self.mrn_detail1.return_qty),
            'mrqd-0-existing_accepted_qty': str(self.mrn_detail1.current_accepted_qty),
            'mrqd-0-existing_scrap_qty': str(self.mrn_detail1.current_scrap_qty),
            'mrqd-0-accepted_qty_input': '0.0', # Not explicitly selected, 0 quantity
            'mrqd-0-type': '0',
            'mrqd-0-is_selected': '', # Unchecked checkbox

            'mrqd-1-mrn_detail_id': str(self.mrn_detail2.id),
            'mrqd-1-returned_qty': str(self.mrn_detail2.return_qty),
            'mrqd-1-existing_accepted_qty': str(self.mrn_detail2.current_accepted_qty),
            'mrqd-1-existing_scrap_qty': str(self.mrn_detail2.current_scrap_qty),
            'mrqd-1-accepted_qty_input': '0.0', # Not explicitly selected, 0 quantity
            'mrqd-1-type': '0',
            'mrqd-1-is_selected': '', # Unchecked checkbox
        }
        
        response = self.client.post(self.view_url, formset_data)
        
        self.assertEqual(response.status_code, 200) # Should render the page again with a message
        self.assertContains(response, "No valid items were processed or no items selected.")
        self.assertEqual(MaterialReturnQualityNoteMaster.objects.count(), 0)
        self.assertEqual(MaterialReturnQualityNoteDetail.objects.count(), 0)

    def test_table_partial_view(self):
        table_url = reverse('quality_control:mrqn_new_details_table_partial') + f'?{self.url_params}'
        response = self.client.get(table_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialreturnqualitynote_new_details/_table_partial.html')
        self.assertContains(response, '<table id="mrqnDetailTable"')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'Gadget B')
```

## Step 5: HTMX and Alpine.js Integration

*   **HTMX for Form Submission:** The main form submits using `hx-post`, targeting `#messages-container` for status updates, and using `hx-on::after-request` for client-side redirection after a successful 204 response.
*   **HTMX for Table Loading:** The `#mrqn-table-container` uses `hx-get` to initially load the table partial and `hx-trigger="load, refreshMRQNTable from:body"` to refresh it dynamically. `refreshMRQNTable` would be the event triggered after a successful submission.
*   **Alpine.js for UI State:** The `x-data="mrqnDetailRow(...)"` directive on each table row manages the `isSelected` state (though the primary selection is now based on `accepted_qty_input` having a value) and the `acceptedQty` for potential local validation or UI adjustments.
*   **DataTables:** The `$(document).ready` equivalent (or `htmx:afterSwap` listener) is used to initialize DataTables after the table HTML is loaded by HTMX.
*   **DRY Templates:** `_table_partial.html` is used as a reusable component for the table itself.
*   **No custom JavaScript:** All interactions are handled by HTMX and Alpine.js, fulfilling the requirement to avoid additional JavaScript. The `confirmationAdd()` has been replaced by `hx-confirm` on the submit button.

## Final Notes

*   **Session/Context Data:** In a full ERP system, `CompId`, `FinYearId`, and `SessionId` would likely come from user authentication context (e.g., `request.user.profile.company_id`) or custom middleware, not directly from `Session["compid"]`. For this migration, we've simulated it.
*   **Error Handling:** The `try-catch` blocks in ASP.NET are very broad. In Django, specific exceptions should be caught and handled, and `messages` used for user feedback. Logging is essential for server-side errors.
*   **`clsFunctions`:** The original `clsFunctions` class is a utility class. In Django, such functionalities are typically implemented as:
    *   Model methods (`@staticmethod` or regular methods).
    *   Helper functions in `utils.py` files within an app.
    *   Custom managers for query-related utilities.
    *   The `get_next_sequence_number` and `NumberValidationQty` are mocked in tests and implemented simply in `models.py` for demonstration.
*   **Database Schema Management:** The `managed = False` in models assumes you are connecting to an existing database. If you plan to manage the schema with Django migrations, you would set `managed = True` and define the fields precisely.
*   **`MaterialReturnQualityNoteMaster.process_grid_data`:** This method encapsulates the complex transactional logic from `btnProceed_Click`, making the view thin and adhering to the "Fat Model" principle.
*   **Formset Management:** The `MaterialReturnDetailFormSet` and `formset.management_form` are critical for Django to correctly parse and validate the multiple form instances submitted from the grid.
*   **`custom_filters.py`**: A simple custom filter `get_item` was added to assist in template rendering due to the way `formset`'s initial data maps compared to the `material_return_details` queryset. In a more robust solution, the `material_return_details` objects themselves would ideally be passed directly to each form in the formset, allowing direct access to their properties.