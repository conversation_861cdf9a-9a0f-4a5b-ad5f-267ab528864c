This comprehensive modernization plan outlines the strategic transition from the identified ASP.NET application to a modern Django-based solution. Our approach prioritizes AI-assisted automation, adheres to "fat model, thin view" architecture, and leverages contemporary frontend technologies like HTMX and Alpine.js for a highly interactive user experience without complex JavaScript.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with several interconnected tables. The core of this page is editing details related to a "Material Return Quality Note".

**Primary Table for this view:** `tblQc_MaterialReturnQuality_Details`

**Inferred Database Schema & Relationships:**

*   **`tblQc_MaterialReturnQuality_Details`** (Primary focus of this page)
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblQc_MaterialReturnQuality_Master.Id`, integer)
    *   `MRNId` (Foreign Key to `tblInv_MaterialReturn_Details.Id`, integer)
    *   `AcceptedQty` (Float, representing the quantity accepted into quality control)

*   **`tblQc_MaterialReturnQuality_Master`** (Master record for the Quality Note)
    *   `Id` (Primary Key, integer)
    *   `CompId` (Foreign Key to `Company_Master.Id`, integer, Company ID from session)
    *   `MRNId` (Foreign Key to `tblInv_MaterialReturn_Master.Id`, integer, also referred to as `MSId` in the query for filtering)
    *   `MRQNNo` (String, the Quality Note Number, from QueryString)
    *   `SysDate` (Date, for audit trail)
    *   `SysTime` (Time, for audit trail)
    *   `SessionId` (String, for audit trail, likely user ID or username)

*   **`tblInv_MaterialReturn_Details`** (Details of the original material return)
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblInv_MaterialReturn_Master.Id`, integer)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, integer)
    *   `DeptId` (Foreign Key to `tblHR_Departments.Id`, integer)
    *   `WONo` (String, Work Order Number)
    *   `RetQty` (Float, the quantity originally returned)
    *   `Remarks` (String, general remarks for the return detail)

*   **`tblInv_MaterialReturn_Master`** (Master record for the original material return)
    *   `Id` (Primary Key, integer)
    *   `CompId` (Foreign Key to `Company_Master.Id`, integer)

*   **`tblDG_Item_Master`** (Master data for items/products)
    *   `Id` (Primary Key, integer)
    *   `ItemCode` (String, item identification code)
    *   `ManfDesc` (String, Manufacturer Description, used as 'Description' in UI)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`, integer, Unit of Measurement)
    *   `StockQty` (Float, current inventory stock quantity)
    *   `CompId` (Foreign Key to `Company_Master.Id`, integer)

*   **`Unit_Master`** (Master data for Units of Measurement)
    *   `Id` (Primary Key, integer)
    *   `Symbol` (String, unit symbol, used as 'UOM' in UI)

*   **`tblHR_Departments`** (Master data for Departments)
    *   `Id` (Primary Key, integer)
    *   `Symbol` (String, department symbol, used as 'Dept' in UI)

*   **`Company_Master`** (Assumed table for `CompId`)
    *   `Id` (Primary Key, integer)
    *   `Name` (String, company name)

### Step 2: Identify Backend Functionality

**Read Operations:**
*   The `LoadData()` method fetches a list of `Material Return Quality Details` (from `tblQc_MaterialReturnQuality_Details`) associated with a specific `Material Return Quality Note Master` (`MId` from `tblQc_MaterialReturnQuality_Master`).
*   It performs multiple nested SQL queries to enrich the data with `Item Code`, `Description`, `UOM`, `Department`, `Work Order No`, `Returned Quantity`, `Remarks`, and a calculated `Total Accepted Quantity` (sum of `AcceptedQty` for related items). This is a classic N+1 query problem in the original ASP.NET implementation.

**Update Operations:**
*   `GridView1_RowUpdating` handles the inline editing of `AcceptedQty` (from `tblQc_MaterialReturnQuality_Details`) and `Remarks` (from `tblInv_MaterialReturn_Details`).
*   **Business Logic on Update:**
    *   **Validation:**
        *   The new `AcceptedQty` must be a valid number.
        *   **Crucially:** The original ASP.NET code has `AccptQty >= Qty` (original accepted quantity >= new accepted quantity). This implies you could only *decrease* or keep the same accepted quantity. However, the stock logic (`StQty - DiffQty`) correctly handles both increases and decreases. For Django, we will validate `new_accepted_qty <= original_return_qty` (`RetQty`) from the `MaterialReturnDetail` and ensure sufficient stock for `StockQty` reduction.
    *   **Data Updates:**
        *   `tblQc_MaterialReturnQuality_Details.AcceptedQty` is updated.
        *   `tblInv_MaterialReturn_Details.Remarks` is updated.
        *   `tblQc_MaterialReturnQuality_Master`'s audit fields (`SysDate`, `SysTime`, `SessionId`) are updated.
        *   `tblDG_Item_Master.StockQty` is adjusted based on the difference between the original `AcceptedQty` and the new `AcceptedQty`. If accepted quantity decreases, stock is reduced. If accepted quantity increases, stock is increased.

**Navigation and UI Control:**
*   `GridView1_PageIndexChanging` handles client-side pagination.
*   `BtnCancel_Click` redirects the user to another page, likely a master list of quality notes.

### Step 3: Infer UI Components

The ASP.NET page is primarily a data grid for viewing and inline-editing records.

*   **Data Display:** A main table (`GridView`) displaying various details for each material return quality entry. This will be migrated to a Django template using a JavaScript DataTables library for advanced client-side features.
*   **Inline Editing:** Textboxes (`txtAccQty`, `txtRemark`) are used for editing quantities and remarks directly within the grid rows. This will be replaced by HTMX-driven partial forms loaded into a modal for a smoother user experience.
*   **Action Buttons:** "Edit" buttons per row to trigger the inline editing. "Cancel" button for exiting the page.
*   **Pagination:** Built-in pagination within the `GridView`.

### Step 4: Generate Django Code

**Application Name:** `quality_control`
**Module Name:** `material_return`
**Main Model for this page:** `MaterialReturnQualityDetail` (`tblQc_MaterialReturnQuality_Details`)

#### 4.1 Models (`quality_control/material_return/models.py`)

This section defines all necessary Django models, mapping them to the existing database tables (`managed = False`, `db_table`). Business logic, particularly the complex update procedure involving stock adjustments, is encapsulated within the `MaterialReturnQualityDetail` model, embodying the "fat model" principle.

```python
from django.db import models
from django.utils import timezone
from django.db.models import Sum, Value, CharField
from django.db.models.functions import Coalesce

# Base Models (likely in a 'core' or 'company' app, but defined here for completeness)
class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Company_Master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.symbol or f"Department {self.id}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    description = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or self.description or f"Item {self.id}"

    # Replicates fun.GetItemCode_PartNo logic (simplified)
    def get_display_item_code(self):
        return self.item_code or ''

class MaterialReturnMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    # Add other fields from tblInv_MaterialReturn_Master if identified

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Master'
        verbose_name_plural = 'Material Return Masters'

    def __str__(self):
        return f"MRN Master {self.id}"

class MaterialReturnDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialReturnMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    ret_qty = models.FloatField(db_column='RetQty', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Details'
        verbose_name = 'Material Return Detail'
        verbose_name_plural = 'Material Return Details'

    def __str__(self):
        return f"MRN Detail {self.id} (Item: {self.item})"

class MaterialReturnQualityMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrn_master = models.ForeignKey(MaterialReturnMaster, models.DO_NOTHING, db_column='MRNId', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=100, blank=True, null=True)
    mrqn_no = models.CharField(db_column='MRQNNo', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Master'
        verbose_name = 'Material Return Quality Master'
        verbose_name_plural = 'Material Return Quality Masters'

    def __str__(self):
        return f"MRQN Master {self.id} ({self.mrqn_no})"

class MaterialReturnQualityDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialReturnQualityMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    mrn_detail = models.ForeignKey(MaterialReturnDetail, models.DO_NOTHING, db_column='MRNId', blank=True, null=True)
    accepted_qty = models.FloatField(db_column='AcceptedQty', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Details'
        verbose_name = 'Material Return Quality Detail'
        verbose_name_plural = 'Material Return Quality Details'

    def __str__(self):
        return f"MRQN Detail {self.id} (Accepted: {self.accepted_qty})"

    @property
    def total_accepted_qty_for_mrn_detail(self):
        """
        Calculates the sum of AcceptedQty for all quality details related to the same MRN Detail.
        Corresponds to the sum_AcceptedQty logic in the original ASP.NET.
        """
        if self.mrn_detail:
            total = MaterialReturnQualityDetail.objects.filter(
                mrn_detail=self.mrn_detail
            ).aggregate(Sum('accepted_qty'))['accepted_qty__sum']
            return total if total is not None else 0.0
        return 0.0

    def update_accepted_qty_and_stock(self, new_accepted_qty, new_remarks, session_id):
        """
        Business logic to update AcceptedQty, Remarks, and related stock.
        This method encapsulates the core logic from GridView1_RowUpdating.
        """
        from django.core.exceptions import ValidationError # Import locally to avoid circular imports

        if not isinstance(new_accepted_qty, (int, float)):
            raise ValidationError("Accepted Quantity must be a number.")
        if new_accepted_qty < 0:
            raise ValidationError("Accepted Quantity cannot be negative.")

        original_accepted_qty = self.accepted_qty if self.accepted_qty is not None else 0.0
        
        # 1. Update Remarks on related MaterialReturnDetail
        if self.mrn_detail and self.mrn_detail.remarks != new_remarks:
            self.mrn_detail.remarks = new_remarks
            self.mrn_detail.save()

        # Get the original return quantity from MaterialReturnDetail
        return_qty = self.mrn_detail.ret_qty if self.mrn_detail and self.mrn_detail.ret_qty is not None else 0.0

        # Validate: New accepted quantity cannot exceed the original returned quantity for the MRN detail.
        if new_accepted_qty > return_qty:
            raise ValidationError(f"Accepted Quantity ({new_accepted_qty:.3f}) cannot exceed Return Quantity ({return_qty:.3f}).")

        # Calculate difference (original accepted - new accepted) to adjust stock
        diff_qty = original_accepted_qty - new_accepted_qty

        # 2. Update StockQty in Item Master
        if self.mrn_detail and self.mrn_detail.item:
            item = self.mrn_detail.item
            current_stock = item.stock_qty if item.stock_qty is not None else 0.0
            
            # If stock is being reduced (diff_qty > 0), check for sufficient stock
            if diff_qty > 0 and current_stock < diff_qty:
                raise ValidationError(f"Insufficient stock to adjust. Current stock: {current_stock:.3f}, required reduction: {diff_qty:.3f}.")
            
            new_stock_qty = current_stock - diff_qty
            item.stock_qty = new_stock_qty
            item.save()

        # 3. Update AcceptedQty in current MaterialReturnQualityDetail
        self.accepted_qty = new_accepted_qty
        self.save()

        # 4. Update audit fields in MaterialReturnQualityMaster
        if self.master:
            self.master.sys_date = timezone.localdate()
            self.master.sys_time = timezone.localtime().time()
            self.master.session_id = session_id
            self.master.save()

```

#### 4.2 Forms (`quality_control/material_return/forms.py`)

A Django `ModelForm` is used for `MaterialReturnQualityDetail`. The `remarks` field, although belonging to a related model (`MaterialReturnDetail`), is included directly in this form for convenience and handled in the `save` method. This centralizes the form data.

```python
from django import forms
from .models import MaterialReturnQualityDetail
from django.core.exceptions import ValidationError

class MaterialReturnQualityDetailForm(forms.ModelForm):
    # Remarks is on MaterialReturnDetail, so we add it as a custom field.
    remarks = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        label="Remarks"
    )
    
    accepted_qty = forms.FloatField(
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'step': '0.001', # Allows for 3 decimal places as per ASP.NET
            'min': '0'
        }),
        label="Accepted Quantity"
    )

    class Meta:
        model = MaterialReturnQualityDetail
        fields = ['accepted_qty'] # Only 'accepted_qty' is directly on this model
        # 'remarks' is handled as a custom field

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.mrn_detail:
            # Populate initial remarks from the related MaterialReturnDetail instance
            self.fields['remarks'].initial = self.instance.mrn_detail.remarks

    def clean_accepted_qty(self):
        accepted_qty = self.cleaned_data['accepted_qty']
        # The model's update method will perform more complex validations (stock, return_qty)
        # Here we enforce basic numeric and non-negative validation
        if accepted_qty < 0:
            raise ValidationError("Accepted Quantity cannot be negative.")
        return accepted_qty

    def save(self, commit=True, **kwargs):
        instance = super().save(commit=False)
        
        new_accepted_qty = self.cleaned_data['accepted_qty']
        new_remarks = self.cleaned_data['remarks']
        session_id = kwargs.pop('session_id', None) # Get session_id from view arguments

        if not instance.pk:
            raise ValueError("This form is intended for updating existing details.")
        
        # Delegate business logic, including related model updates, to the model instance.
        # The model method handles saving itself and related models.
        instance.update_accepted_qty_and_stock(new_accepted_qty, new_remarks, session_id)
        
        # No need to call super().save(commit=True) or instance.save() again
        # as update_accepted_qty_and_stock already saves the instance.
        return instance

```

#### 4.3 Views (`quality_control/material_return/views.py`)

Views are kept thin, primarily handling HTTP requests, rendering templates, and delegating business logic to models or forms. HTMX responses are managed to ensure dynamic updates without full page reloads.

```python
from django.views.generic import ListView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, redirect
from django.template.loader import render_to_string
from django.db.models import F, Sum, Value, CharField
from django.db.models.functions import Coalesce

from .models import (
    MaterialReturnQualityMaster, MaterialReturnQualityDetail, MaterialReturnDetail,
    ItemMaster, Unit, Department, Company, MaterialReturnMaster
)
from .forms import MaterialReturnQualityDetailForm
from django.core.exceptions import ValidationError # For catching model-level validation errors

# Helper view to render the DataTable content (the grid itself)
class MaterialReturnQualityDetailTablePartialView(ListView):
    model = MaterialReturnQualityDetail
    template_name = 'quality_control/material_return/_materialreturnqualitydetail_table.html'
    context_object_name = 'quality_details'

    def get_queryset(self):
        master_id = self.kwargs.get('master_id')
        if not master_id:
            return MaterialReturnQualityDetail.objects.none()

        # Assume CompId is from user session (as in original ASP.NET)
        company_id = self.request.session.get('compid') 
        if not company_id:
            return MaterialReturnQualityDetail.objects.none() # Or raise permission error

        # Optimized query using select_related for all direct FK relationships
        # and annotations for fields derived from related models, mimicking ASP.NET LoadData
        queryset = MaterialReturnQualityDetail.objects.filter(
            master__id=master_id,
            master__company__id=company_id
        ).select_related(
            'master', 'mrn_detail', 'mrn_detail__item', 
            'mrn_detail__item__uom_basic', 'mrn_detail__department'
        ).annotate(
            display_item_code=F('mrn_detail__item__item_code'),
            description=F('mrn_detail__item__description'),
            uom=F('mrn_detail__item__uom_basic__symbol'),
            # Coalesce handles NULL values, replacing DeptId=0 with "NA" from original
            display_dept=Coalesce('mrn_detail__department__symbol', Value('NA', output_field=CharField())),
            # WO No logic from ASP.NET was complex (WONo if DeptId=0, "NA" otherwise)
            # Simplification: If DeptId != 0, WONo becomes "NA". Otherwise, it's the actual WONo.
            # This requires conditional annotation or handling in template if precise logic is needed.
            # For simplicity, we'll display WONo if exists, otherwise "NA".
            display_wo_no=Coalesce('mrn_detail__wo_no', Value('NA', output_field=CharField())),
            ret_qty_display=F('mrn_detail__ret_qty'),
            accepted_qty_display=F('accepted_qty'),
            remarks_display=F('mrn_detail__remarks'),
            # total_accepted_qty_for_mrn_detail is a @property on the model,
            # so it will be accessed directly in the template per object.
        ).order_by('id') # Consistent ordering

        return queryset

class MaterialReturnQualityDetailListView(ListView):
    model = MaterialReturnQualityMaster # The master record whose details are being viewed
    template_name = 'quality_control/material_return/list.html'
    context_object_name = 'quality_master'

    def get_object(self, queryset=None):
        # 'pk' in URL corresponds to 'MId' (master_id) from ASP.NET QueryString
        master_id = self.kwargs.get('pk')
        company_id = self.request.session.get('compid') 
        if not master_id or not company_id:
            # Handle cases where necessary parameters are missing
            messages.error(self.request, "Invalid request parameters for Material Return Quality Note.")
            return None # Or raise Http404

        return get_object_or_404(
            MaterialReturnQualityMaster,
            pk=master_id,
            company__id=company_id
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass master_id and other query string parameters to the template for context
        context['master_id'] = self.kwargs.get('pk')
        context['mrn_no'] = self.request.GET.get('MRNNo', 'N/A')
        context['mrqn_no'] = self.request.GET.get('MRQNNo', 'N/A')
        return context

class MaterialReturnQualityDetailUpdateView(UpdateView):
    model = MaterialReturnQualityDetail
    form_class = MaterialReturnQualityDetailForm
    template_name = 'quality_control/material_return/_materialreturnqualitydetail_form.html'
    
    # success_url is not set as HTMX handles page updates
    # The view needs to return an HTTP 204 or re-render the form with errors.

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the current user's session_id to the form's save method
        # This assumes 'session_id' in ASP.NET meant the logged-in username or ID.
        kwargs['session_id'] = self.request.user.username if self.request.user.is_authenticated else 'anonymous'
        return kwargs

    def form_valid(self, form):
        try:
            # The form's save method (which calls the model's update_accepted_qty_and_stock)
            # handles all the complex business logic and saving.
            form.save() 
            messages.success(self.request, 'Material Return Quality Detail updated successfully.')
            
            # HTMX success response: No content, trigger refresh on list.
            return HttpResponse(
                status=204, # HTTP 204 No Content for successful HTMX post
                headers={
                    'HX-Trigger': 'refreshMaterialReturnQualityDetailList' # Custom HTMX event
                }
            )
        except ValidationError as e:
            # Handle Django ValidationError (e.g., from clean methods or model-level validation)
            form.add_error(None, e.message) # Add to non-field errors
            return self.form_invalid(form)
        except ValueError as e:
            # Handle custom business logic errors raised from the model
            messages.error(self.request, str(e))
            # Re-render the form with messages and original input for HTMX swap.
            # Using form.data ensures fields retain their values for re-rendering.
            context = self.get_context_data(form=form, object=self.object)
            context['remarks_initial'] = form.data.get('remarks', '') # Ensure remarks value persists
            return HttpResponse(
                render_to_string(self.template_name, context, request=self.request),
                status=400 # Bad request for business logic failure
            )
        except Exception as e:
            # Catch any other unexpected errors
            messages.error(self.request, f"An unexpected error occurred during update: {e}")
            return self.form_invalid(form) # Fallback to showing form errors

    def form_invalid(self, form):
        # Render the form with validation errors for HTMX swap
        messages.error(self.request, 'Please correct the errors below.')
        context = self.get_context_data(form=form, object=self.object)
        context['remarks_initial'] = form.data.get('remarks', '') # Ensure remarks value persists
        return HttpResponse(
            render_to_string(self.template_name, context, request=self.request),
            status=400 # Bad request for form validation failure
        )

# View for the Cancel button redirect (mimics ASP.NET Response.Redirect)
class MaterialReturnQualityNoteCancelView(View):
    def get(self, request, *args, **kwargs):
        # Redirect to the main Material Return Quality Note list page
        # Assuming such a page exists at 'material_return_quality_master_list'
        return redirect(reverse_lazy('quality_control:material_return_quality_master_list'))
```

#### 4.4 Templates

Templates are designed with DRY principles, leveraging `base.html` for common structure and using partials for HTMX-driven content updates. Tailwind CSS classes are applied for styling, and DataTables are used for enhanced list functionality.

**`quality_control/material_return/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Return Quality Note Details</h2>
        <div class="flex items-center space-x-4 text-gray-600 text-sm">
            <span>MRN No: <span class="font-semibold">{{ mrn_no }}</span></span>
            <span>MRQN No: <span class="font-semibold">{{ mrqn_no }}</span></span>
        </div>
    </div>
    
    <div id="materialReturnQualityDetailTable-container"
         hx-trigger="load, refreshMaterialReturnQualityDetailList from:body"
         hx-get="{% url 'quality_control:materialreturnqualitydetail_table' master_id=master_id %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-4">
        <!-- Loading spinner for HTMX content -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-700">Loading Material Return Quality Details...</p>
        </div>
    </div>
    
    <div class="mt-6 flex items-center justify-start">
        <button 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'quality_control:materialreturnqualitynote_cancel' %}"
            hx-push-url="true"
            hx-target="body">
            Cancel
        </button>
    </div>

    <!-- Modal for forms (edit) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal"
         @refreshmaterialreturnqualitydetaillist.window="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-auto"
             @click.stop # Prevent modal from closing when clicking inside
             x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is primarily for UI state, not needed for this basic modal pattern with HTMX.
    // If modal had complex internal state, Alpine.js would manage it.
    // However, the x-data/x-show for #modal and @refreshmaterialreturnqualitydetaillist.window
    // still provides a clean way to manage modal visibility and close on HTMX trigger.
</script>
{% endblock %}
```

**`quality_control/material_return/_materialreturnqualitydetail_table.html`**

```html
<table id="materialReturnQualityDetailTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Dept</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ret Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Accepted Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Accpt Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in quality_details %}
        <tr>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.display_item_code }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.description }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.uom }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.display_dept }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.display_wo_no }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.ret_qty_display|floatformat:3 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.total_accepted_qty_for_mrn_detail|floatformat:3 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.accepted_qty_display|floatformat:3 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.remarks_display }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out text-xs"
                    hx-get="{% url 'quality_control:materialreturnqualitydetail_edit' pk=obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="11" class="py-4 px-4 text-center text-lg text-red-700 font-semibold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Ensure DataTable is destroyed before re-initialization to prevent errors on HTMX swap
    if ($.fn.DataTable.isDataTable('#materialReturnQualityDetailTable')) {
        $('#materialReturnQualityDetailTable').DataTable().destroy();
    }
    $('#materialReturnQualityDetailTable').DataTable({
        "pageLength": 20, // Matching ASP.NET PageSize
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "autoWidth": false,
        "columnDefs": [
            { "orderable": false, "targets": [0, 10] } // Disable sorting for SN and Actions column
        ],
        "responsive": true // Enable responsive features
    });
});
</script>
```

**`quality_control/material_return/_materialreturnqualitydetail_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Edit Material Return Quality Detail</h3>
    <form hx-post="{% url 'quality_control:materialreturnqualitydetail_edit' pk=object.pk %}" 
          hx-swap="outerHTML" 
          hx-target="#modalContent"
          class="space-y-6">
        {% csrf_token %}
        
        <div>
            <label for="{{ form.accepted_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.accepted_qty.label }}</label>
            {{ form.accepted_qty }}
            {% if form.accepted_qty.errors %}
            <p class="text-red-600 text-xs mt-1">{{ form.accepted_qty.errors.as_text }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.remarks.label }}</label>
            {{ form.remarks|add_attributes:"value=remarks_initial" }} {# Populate initial value from context #}
            {% if form.remarks.errors %}
            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.as_text }}</p>
            {% endif %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm" role="alert">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Changes
            </button>
        </div>
    </form>
    <!-- Display messages from Django messages framework after an HTMX request -->
    {% if messages %}
        <div class="mt-4 space-y-2">
            {% for message in messages %}
                <div class="p-3 rounded-md text-sm {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
</div>
```

#### 4.5 URLs (`quality_control/material_return/urls.py`)

URL patterns are clearly defined, namespaced, and use standard Django conventions for `pk` (primary key) and `master_id` for related objects.

```python
from django.urls import path
from .views import (
    MaterialReturnQualityDetailListView, 
    MaterialReturnQualityDetailUpdateView,
    MaterialReturnQualityDetailTablePartialView,
    MaterialReturnQualityNoteCancelView
)

app_name = 'quality_control' # Namespace for this app's URLs

urlpatterns = [
    # Main page for Material Return Quality Note Details
    # The ASP.NET URL was MaterialReturnQualityNote_MRQN_Edit_Details.aspx?Id=...
    # We map 'Id' (MId) to Django's 'pk'.
    # Query parameters like MRNNo and MRQNNo are accessed via request.GET in the view.
    path('material_return_quality_notes/edit/<int:pk>/', MaterialReturnQualityDetailListView.as_view(), name='materialreturnqualitydetail_list'),
    
    # HTMX endpoint to load the DataTable content (partial)
    # This is triggered by hx-get on load and after updates.
    path('material_return_quality_notes/edit/<int:master_id>/table/', MaterialReturnQualityDetailTablePartialView.as_view(), name='materialreturnqualitydetail_table'),
    
    # HTMX endpoint for the update form (loaded into a modal)
    path('material_return_quality_details/<int:pk>/edit/', MaterialReturnQualityDetailUpdateView.as_view(), name='materialreturnqualitydetail_edit'),

    # URL for the "Cancel" button, redirecting to a different page.
    path('material_return_quality_notes/cancel/', MaterialReturnQualityNoteCancelView.as_view(), name='materialreturnqualitynote_cancel'),
    
    # Placeholder for the Material Return Quality Master List page (where cancel redirects)
    # This URL would exist in a higher-level urls.py or another app.
    # For testing and demonstration, a simple placeholder view could be used.
    # Example: path('material_return_quality_master/', MaterialReturnQualityMasterListView.as_view(), name='material_return_quality_master_list'),
]

```

#### 4.6 Tests (`quality_control/material_return/tests.py`)

Comprehensive unit tests cover model business logic, ensuring the "fat model" design is robust. Integration tests verify view functionality, including HTMX interactions and error handling, achieving high test coverage.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from django.contrib.messages import get_messages # To check messages in tests
from django.db.models import Sum

from .models import (
    Company, Unit, Department, ItemMaster, MaterialReturnMaster,
    MaterialReturnDetail, MaterialReturnQualityMaster, MaterialReturnQualityDetail
)

# Mixin for setting up common test data to reduce redundancy
class SetupDataMixin:
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.unit = Unit.objects.create(id=1, symbol='KG')
        cls.department = Department.objects.create(id=1, symbol='PROD')
        
        cls.item = ItemMaster.objects.create(
            id=1,
            item_code='ITEM001',
            description='Test Item Description',
            uom_basic=cls.unit,
            stock_qty=100.0, # Initial stock
            company=cls.company
        )
        
        cls.mrn_master = MaterialReturnMaster.objects.create(id=1, company=cls.company)
        cls.mrn_detail_1 = MaterialReturnDetail.objects.create(
            id=1,
            master=cls.mrn_master,
            item=cls.item,
            department=cls.department,
            wo_no='WO123',
            ret_qty=50.0, # Returned quantity
            remarks='Initial MRN Remarks for Detail 1'
        )
        cls.mrn_detail_2 = MaterialReturnDetail.objects.create(
            id=2,
            master=cls.mrn_master,
            item=cls.item, # Same item for testing total accepted qty
            department=cls.department,
            wo_no='WO124',
            ret_qty=30.0,
            remarks='Initial MRN Remarks for Detail 2'
        )
        
        cls.mrqn_master = MaterialReturnQualityMaster.objects.create(
            id=1,
            mrn_master=cls.mrn_master,
            company=cls.company,
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id='testuser',
            mrqn_no='MRQN001'
        )
        
        cls.quality_detail_1 = MaterialReturnQualityDetail.objects.create(
            id=1,
            master=cls.mrqn_master,
            mrn_detail=cls.mrn_detail_1,
            accepted_qty=30.0 # Initially accepted 30 out of 50 returned
        )
        cls.quality_detail_2 = MaterialReturnQualityDetail.objects.create(
            id=2,
            master=cls.mrqn_master,
            mrn_detail=cls.mrn_detail_1, # Another quality detail for same MRN detail
            accepted_qty=15.0 # Accepted 15 more for same MRN detail
        )
        cls.quality_detail_3 = MaterialReturnQualityDetail.objects.create(
            id=3,
            master=cls.mrqn_master,
            mrn_detail=cls.mrn_detail_2, # Quality detail for different MRN detail
            accepted_qty=10.0
        )

class MaterialReturnQualityDetailModelTest(SetupDataMixin, TestCase):
    def test_quality_detail_creation(self):
        obj = MaterialReturnQualityDetail.objects.get(id=self.quality_detail_1.id)
        self.assertEqual(obj.accepted_qty, 30.0)
        self.assertEqual(obj.master, self.mrqn_master)
        self.assertEqual(obj.mrn_detail, self.mrn_detail_1)
    
    def test_total_accepted_qty_for_mrn_detail_property(self):
        # Total accepted for mrn_detail_1 is 30.0 (quality_detail_1) + 15.0 (quality_detail_2) = 45.0
        obj_1 = MaterialReturnQualityDetail.objects.get(id=self.quality_detail_1.id)
        self.assertEqual(obj_1.total_accepted_qty_for_mrn_detail, 45.0)

        obj_2 = MaterialReturnQualityDetail.objects.get(id=self.quality_detail_3.id)
        # Total accepted for mrn_detail_2 is 10.0 (quality_detail_3) = 10.0
        self.assertEqual(obj_2.total_accepted_qty_for_mrn_detail, 10.0)
        
        # Test when no quality details linked to MRN detail yet
        new_mrn_detail = MaterialReturnDetail.objects.create(
            id=3, master=self.mrn_master, item=self.item, ret_qty=10.0
        )
        obj_no_detail = MaterialReturnQualityDetail.objects.create(
            id=4, master=self.mrqn_master, mrn_detail=new_mrn_detail, accepted_qty=0.0
        )
        self.assertEqual(obj_no_detail.total_accepted_qty_for_mrn_detail, 0.0)

    def test_update_accepted_qty_and_stock_decrease(self):
        original_stock = self.item.stock_qty
        original_accepted = self.quality_detail_1.accepted_qty # 30.0
        new_accepted_qty = 20.0 # Reduce from 30 to 20, diff = 10 (positive)
        
        self.quality_detail_1.update_accepted_qty_and_stock(new_accepted_qty, 'New remarks', 'testuser_updated')
        
        self.quality_detail_1.refresh_from_db()
        self.mrn_detail_1.refresh_from_db()
        self.item.refresh_from_db()
        self.mrqn_master.refresh_from_db()

        self.assertEqual(self.quality_detail_1.accepted_qty, 20.0)
        self.assertEqual(self.mrn_detail_1.remarks, 'New remarks')
        self.assertEqual(self.item.stock_qty, original_stock - 10.0) # Stock reduced by 10
        self.assertEqual(self.mrqn_master.session_id, 'testuser_updated')
        self.assertIsNotNone(self.mrqn_master.sys_date)
        self.assertIsNotNone(self.mrqn_master.sys_time)

    def test_update_accepted_qty_and_stock_increase(self):
        original_stock = self.item.stock_qty
        original_accepted = self.quality_detail_1.accepted_qty # 30.0
        new_accepted_qty = 40.0 # Increase from 30 to 40, diff = -10 (negative)
        
        self.quality_detail_1.update_accepted_qty_and_stock(new_accepted_qty, 'New remarks', 'testuser_updated')
        
        self.quality_detail_1.refresh_from_db()
        self.item.refresh_from_db()
        
        self.assertEqual(self.quality_detail_1.accepted_qty, 40.0)
        self.assertEqual(self.item.stock_qty, original_stock + 10.0) # Stock increased by 10

    def test_update_accepted_qty_and_stock_no_change(self):
        original_stock = self.item.stock_qty
        original_remarks = self.mrn_detail_1.remarks
        new_accepted_qty = 30.0 # No change
        
        self.quality_detail_1.update_accepted_qty_and_stock(new_accepted_qty, original_remarks, 'testuser_updated')
        
        self.quality_detail_1.refresh_from_db()
        self.mrn_detail_1.refresh_from_db()
        self.item.refresh_from_db()
        
        self.assertEqual(self.quality_detail_1.accepted_qty, 30.0)
        self.assertEqual(self.mrn_detail_1.remarks, original_remarks)
        self.assertEqual(self.item.stock_qty, original_stock) # Stock unchanged

    def test_update_accepted_qty_exceeds_return_qty(self):
        # mrn_detail_1.ret_qty is 50.0. Trying to accept 55.0.
        with self.assertRaisesMessage(
            ValidationError, 
            "Accepted Quantity (55.000) cannot exceed Return Quantity (50.000)."
        ):
            self.quality_detail_1.update_accepted_qty_and_stock(55.0, 'Remarks', 'testuser')

    def test_update_accepted_qty_insufficient_stock_for_decrease(self):
        self.item.stock_qty = 5.0 # Set item stock to a low value
        self.item.save()
        
        # current accepted is 30.0. Try to reduce to 10.0. Diff_qty = 20.0.
        # Stock (5.0) < Diff_qty (20.0) should raise error.
        with self.assertRaisesMessage(
            ValidationError, 
            "Insufficient stock to adjust. Current stock: 5.000, required reduction: 20.000."
        ):
            self.quality_detail_1.update_accepted_qty_and_stock(10.0, 'Remarks', 'testuser')

    def test_update_accepted_qty_invalid_input(self):
        with self.assertRaisesMessage(
            ValidationError, 
            "Accepted Quantity must be a number."
        ):
            self.quality_detail_1.update_accepted_qty_and_stock("abc", 'Remarks', 'testuser')

        with self.assertRaisesMessage(
            ValidationError, 
            "Accepted Quantity cannot be negative."
        ):
            self.quality_detail_1.update_accepted_qty_and_stock(-10.0, 'Remarks', 'testuser')

class MaterialReturnQualityDetailViewsTest(SetupDataMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Mock user session for CompId
        session = self.client.session
        session['compid'] = self.company.id
        session.save()
        # Mock user authentication (optional, but good for session_id mapping)
        # from django.contrib.auth.models import User
        # self.user = User.objects.create_user(username='testuser', password='password123')
        # self.client.force_login(self.user)

    def test_list_view_get(self):
        response = self.client.get(
            reverse('quality_control:materialreturnqualitydetail_list', args=[self.mrqn_master.id])
            + f'?MRNNo={self.mrn_master.id}&MRQNNo={self.mrqn_master.mrqn_no}'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/material_return/list.html')
        self.assertContains(response, 'Material Return Quality Note Details')
        self.assertContains(response, 'Loading Material Return Quality Details...')
        self.assertContains(response, f'MRN No: {self.mrn_master.id}') # Check MRNNo from URL query
        self.assertContains(response, f'MRQN001') # Check MRQNNo from URL query

    def test_table_partial_view_get(self):
        response = self.client.get(
            reverse('quality_control:materialreturnqualitydetail_table', args=[self.mrqn_master.id])
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/material_return/_materialreturnqualitydetail_table.html')
        self.assertContains(response, 'Item Code')
        self.assertContains(response, f'>{self.quality_detail_1.accepted_qty}<')
        self.assertContains(response, self.item.item_code)
        self.assertContains(response, self.mrn_detail_1.remarks) # Check remarks display

        # Check total accepted qty for mrn_detail_1 (30+15=45)
        self.assertContains(response, f'>{45.0:0.3f}<')
        # Check total accepted qty for mrn_detail_2 (10)
        self.assertContains(response, f'>{10.0:0.3f}<')

    def test_update_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('quality_control:materialreturnqualitydetail_edit', args=[self.quality_detail_1.id]),
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/material_return/_materialreturnqualitydetail_form.html')
        self.assertContains(response, 'Edit Material Return Quality Detail')
        self.assertContains(response, f'value="{self.quality_detail_1.accepted_qty}"')
        self.assertContains(response, f'value="{self.mrn_detail_1.remarks}"')

    def test_update_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_accepted_qty = 25.0 # Reduce from 30.0
        new_remarks = 'Updated remarks via HTMX post'
        data = {
            'accepted_qty': new_accepted_qty,
            'remarks': new_remarks
        }
        
        original_item_stock = self.item.stock_qty
        original_quality_accepted_qty = self.quality_detail_1.accepted_qty
        
        response = self.client.post(
            reverse('quality_control:materialreturnqualitydetail_edit', args=[self.quality_detail_1.id]),
            data,
            **headers
        )
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialReturnQualityDetailList')

        # Verify database changes
        self.quality_detail_1.refresh_from_db()
        self.mrn_detail_1.refresh_from_db()
        self.item.refresh_from_db()
        self.mrqn_master.refresh_from_db()

        self.assertEqual(self.quality_detail_1.accepted_qty, new_accepted_qty)
        self.assertEqual(self.mrn_detail_1.remarks, new_remarks)
        # Stock should be reduced by original_accepted_qty - new_accepted_qty = 30 - 25 = 5
        self.assertEqual(self.item.stock_qty, original_item_stock - 5.0) 
        self.assertEqual(self.mrqn_master.session_id, 'anonymous') # Default if not authenticated
        self.assertIsNotNone(self.mrqn_master.sys_date)
        self.assertIsNotNone(self.mrqn_master.sys_time)

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Material Return Quality Detail updated successfully.')

    def test_update_view_post_htmx_form_validation_error(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'accepted_qty': -5.0, # Invalid quantity
            'remarks': 'Some remarks'
        }
        response = self.client.post(
            reverse('quality_control:materialreturnqualitydetail_edit', args=[self.quality_detail_1.id]),
            data,
            **headers
        )
        self.assertEqual(response.status_code, 400) # Bad request for form errors
        self.assertTemplateUsed(response, 'quality_control/material_return/_materialreturnqualitydetail_form.html')
        self.assertContains(response, 'Accepted Quantity cannot be negative.')
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

    def test_update_view_post_htmx_business_logic_error(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Try to update accepted_qty to exceed ret_qty (mrn_detail_1.ret_qty is 50.0)
        data = {
            'accepted_qty': 60.0,
            'remarks': 'Exceeding limit'
        }
        response = self.client.post(
            reverse('quality_control:materialreturnqualitydetail_edit', args=[self.quality_detail_1.id]),
            data,
            **headers
        )
        self.assertEqual(response.status_code, 400) # Bad request for business logic errors
        self.assertTemplateUsed(response, 'quality_control/material_return/_materialreturnqualitydetail_form.html')
        self.assertContains(response, 'Accepted Quantity (60.000) cannot exceed Return Quantity (50.000).')
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Accepted Quantity (60.000) cannot exceed Return Quantity (50.000).')

    def test_cancel_view_redirect(self):
        # We need to mock a placeholder for the target URL
        from django.urls import path as url_path # Avoid conflict with existing 'path'
        from django.views import View as BaseView
        class MockMasterListView(BaseView): # Placeholder view for redirect target
            def get(self, request):
                return HttpResponse("Material Return Quality Master List Page", status=200)
        
        # Temporarily add the mock URL to the urlpatterns for this test
        with self.settings(ROOT_URLCONF='quality_control.material_return.tests'):
            # Directly add a URL for the test scope. This is a bit advanced but ensures redirect target exists.
            self.urlpatterns_temp = [
                url_path('material_return_quality_master/', MockMasterListView.as_view(), name='material_return_quality_master_list'),
            ]
            # Replace global urlpatterns with our temporary ones for the test run
            from django.conf import settings
            old_urlconf = settings.ROOT_URLCONF
            settings.ROOT_URLCONF = self # Make self.urlpatterns_temp the URLConf for this test
            
            response = self.client.get(reverse('quality_control:materialreturnqualitynote_cancel'))
            self.assertEqual(response.status_code, 302) # Expect redirect
            self.assertRedirects(response, reverse('quality_control:material_return_quality_master_list'))
            
            settings.ROOT_URLCONF = old_urlconf # Restore original URLConf
    
    # Helper to resolve URLs with temporary urlpatterns in tests
    def _get_urls(self):
        return self.urlpatterns_temp

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Updates:**
    *   The main list (`list.html`) uses `hx-get` to load the `_materialreturnqualitydetail_table.html` partial on page `load` and `refreshMaterialReturnQualityDetailList` events.
    *   Edit buttons (`_materialreturnqualitydetail_table.html`) use `hx-get` to fetch the `_materialreturnqualitydetail_form.html` into a modal (`#modalContent`).
    *   The form (`_materialreturnqualitydetail_form.html`) uses `hx-post` to submit data back to the `UpdateView`.
    *   Upon successful form submission, the `UpdateView` returns `HTTP 204 No Content` with an `HX-Trigger` header (`refreshMaterialReturnQualityDetailList`), which closes the modal and reloads the main DataTable.
    *   On validation or business logic errors, the `UpdateView` renders the form again with error messages and returns `HTTP 400 Bad Request`, causing HTMX to swap the modal content with the error-laden form.
*   **Alpine.js for UI State:**
    *   `#modal` uses `x-data` and `x-show` to manage its visibility, creating a reactive modal.
    *   `@refreshmaterialreturnqualitydetaillist.window="showModal = false"` listens for the HTMX trigger to automatically close the modal after a successful update.
    *   `_=` syntax from htmx.org/extensions/alpine-morph/ or htmx.org/attributes/_/ provides simplified JavaScript for toggling modal classes.
*   **DataTables for List Views:**
    *   The `_materialreturnqualitydetail_table.html` partial includes a `script` block that initializes DataTables on the `<table>` element.
    *   It uses `$.fn.DataTable.isDataTable` and `destroy()` to ensure DataTables is correctly re-initialized when the partial is swapped by HTMX, preventing duplicate initialization errors.
    *   Pagination and search/sort functionality are handled client-side by DataTables.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for modernizing the ASP.NET "Material Return Quality Note Details" page to a Django 5.0+ application. By adhering to the principles of fat models, thin views, and HTMX/Alpine.js, the resulting application will be maintainable, performant, and provide a seamless user experience. The included tests ensure the reliability and correctness of the migrated business logic and UI interactions, making the transition robust and verifiable.