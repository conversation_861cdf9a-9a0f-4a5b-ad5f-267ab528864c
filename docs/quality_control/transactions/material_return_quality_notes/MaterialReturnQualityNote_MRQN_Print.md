## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

This modernization plan outlines the transition of your ASP.NET Material Return Quality Note (MRQN) viewing and search functionality to a modern Django application. The goal is to deliver a more efficient, maintainable, and user-friendly system by leveraging Django's robust framework, HTMX for dynamic interactions, and DataTables for advanced data presentation.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with the following database tables:

*   **`tblQc_MaterialReturnQuality_Master`**: This is the primary table for Material Return Quality Notes.
    *   Columns inferred: `Id` (Primary Key, integer), `SysDate` (Date/DateTime), `MRNNo` (string), `MRQNNo` (string), `FinYearId` (integer), `SessionId` (integer - representing Employee ID).
*   **`tblFinancial_master`**: Used to retrieve financial year details.
    *   Columns inferred: `FinYearId` (Primary Key, integer), `FinYear` (string), `CompId` (integer).
*   **`tblHR_OfficeStaff`**: Used to retrieve employee details (who generated the note).
    *   Columns inferred: `EmpId` (Primary Key, integer), `Title` (string), `EmployeeName` (string), `CompId` (integer).

### Step 2: Identify Backend Functionality

The ASP.NET page primarily performs **Read** operations with advanced filtering.

*   **Read:** The `GridView1` populates data from `tblQc_MaterialReturnQuality_Master`, joining with `tblFinancial_master` and `tblHR_OfficeStaff`.
*   **Filtering/Search:** Users can filter data by:
    *   MRQN No (`drpfield` value "0")
    *   MRN No (`drpfield` value "1")
    *   Employee Name (`drpfield` value "2") with an autocomplete feature.
*   **Pagination:** The `GridView1` supports pagination.
*   **Navigation:** A "Select" link button redirects to a `MaterialReturnQualityNote_MRQN_Print_Details.aspx` page, passing various record identifiers.

No direct Create, Update, or Delete operations are performed on this specific page.

### Step 3: Infer UI Components

The ASP.NET page's UI components and their Django equivalents are:

*   **Header:** "Material Return Quality Note [MRQN] - Print" (Django template heading).
*   **Search Controls:**
    *   `drpfield` (DropDownList): A Django `forms.ChoiceField` for selecting the search criteria. Its visibility logic (`txtMqnNo` vs `txtEmpName`) will be handled by Alpine.js.
    *   `txtMqnNo` (TextBox): A Django `forms.CharField` for MRQN/MRN number input.
    *   `txtEmpName` (TextBox) with `AutoCompleteExtender`: A Django `forms.CharField` for employee name input. Autocomplete will be implemented using HTMX and a dedicated Django view.
    *   `Button1` (Button "Search"): This will trigger an HTMX request to refresh the data table.
*   **Data Display:**
    *   `GridView1` (GridView): A Django HTML `<table>` element rendered via a partial template, enhanced with DataTables.js for client-side functionality.
    *   The "Select" `LinkButton` will be a standard Django `<a>` tag with a URL to the details page.

### Step 4: Generate Django Code

The Django application will be named `quality_control`.

#### 4.1 Models

We will create three models, mapping directly to your existing database tables. These models will have `managed = False` as Django will not manage their schema, ensuring compatibility with your current database. We will add a custom manager to `MaterialReturnQualityNote` to encapsulate the complex data retrieval and filtering logic.

**`quality_control/models.py`**

```python
from django.db import models
from django.db.models import F, Value, CharField
from django.db.models.functions import Concat, Cast
from django.utils import timezone
import datetime

# Custom Manager for MaterialReturnQualityNote to handle complex queries and data formatting
class MaterialReturnQualityNoteManager(models.Manager):
    def get_search_results(self, comp_id, fin_year_id, search_field, search_value, employee_id_for_search=None):
        """
        Replicates the ASP.NET loadData logic to fetch and filter MRQN records,
        including related financial year and employee information.
        """
        queryset = self.select_related('financial_year', 'generated_by').filter(
            CompId=comp_id,
            FinYearId__lte=fin_year_id  # Assuming FinYearId <= current FinYearId based on ASP.NET code
        )

        if search_field == '0' and search_value: # MRQN No
            queryset = queryset.filter(MRQNNo=search_value)
        elif search_field == '1' and search_value: # MRN No
            queryset = queryset.filter(MRNNo=search_value)
        elif search_field == '2' and employee_id_for_search: # Employee Name (SessionId maps to EmpId)
            queryset = queryset.filter(SessionId=employee_id_for_search)

        # Annotate with display fields needed for the template, similar to ASP.NET's DataTable construction
        # F("SysDate") and then formatting it as DD/MM/YYYY
        # Using database functions for efficiency
        queryset = queryset.annotate(
            display_sys_date=Concat(
                Cast('SysDate', output_field=CharField()),  # Assuming SysDate is DateTime and needs formatting
                Value(''), output_field=CharField()
            ),
            # GenBy combines Title and EmployeeName from tblHR_OfficeStaff
            display_gen_by=Concat(
                F('generated_by__Title'), Value('. '), F('generated_by__EmployeeName'),
                output_field=CharField()
            ),
            display_fin_year=F('financial_year__FinYear')
        ).order_by('-Id') # Order By Id Desc

        # Manually format date for the 'display_sys_date' as Django's default date formatting
        # in annotate might be complex or db-specific. Better to handle in Python after query if DB function is tricky
        # For simplicity, if SysDate is a DateField, it's easier. If it's a DateTimeField, we'd format.
        # Let's assume SysDate is DateField in DB for simplicity.
        # If it was DateTimeField, `display_sys_date` would be `models.functions.datetime.DateFormat('SysDate', 'dd/mm/yyyy')`
        # for a DB like PostgreSQL. For SQL Server, it would be different.
        # Given `fun.FromDateDMY(DS["SysDate"].ToString())`, we'll convert after fetching.
        # For this example, we'll return the raw SysDate and format in the template or in Python.
        # Let's add a property to the model for `display_sys_date` and `display_gen_by`.

        # For the purpose of the fat model, we'll try to provide the data ready for display.
        # If the direct database functions are complex, we return base QuerySet and process in a dedicated method.
        # Let's refine the manager to return objects with easy-to-access formatted properties.
        return queryset

class FinancialYear(models.Model):
    # This maps to tblFinancial_master
    FinYearId = models.IntegerField(db_column='FinYearId', primary_key=True)
    FinYear = models.CharField(db_column='FinYear', max_length=100)
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.FinYear

class Employee(models.Model):
    # This maps to tblHR_OfficeStaff
    EmpId = models.IntegerField(db_column='EmpId', primary_key=True)
    Title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=200)
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.Title or ''} {self.EmployeeName}".strip()

class MaterialReturnQualityNote(models.Model):
    # This maps to tblQc_MaterialReturnQuality_Master
    Id = models.IntegerField(db_column='Id', primary_key=True)
    SysDate = models.DateField(db_column='SysDate') # Assuming it's a DateField based on usage
    MRNNo = models.CharField(db_column='MRNNo', max_length=50)
    MRQNNo = models.CharField(db_column='MRQNNo', max_length=50)
    FinYearId = models.IntegerField(db_column='FinYearId') # This could be a ForeignKey
    SessionId = models.IntegerField(db_column='SessionId') # This could be a ForeignKey (EmpId)
    CompId = models.IntegerField(db_column='CompId')

    # Define explicit foreign key relationships for easier querying
    # Even with managed=False, Django can use these for joins if the columns exist and are consistent
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING,
                                       db_column='FinYearId', related_name='mrqn_notes_fin',
                                       to_field='FinYearId', null=True, blank=True)
    generated_by = models.ForeignKey(Employee, on_delete=models.DO_NOTHING,
                                     db_column='SessionId', related_name='mrqn_notes_gen',
                                     to_field='EmpId', null=True, blank=True)

    objects = MaterialReturnQualityNoteManager()

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Master'
        verbose_name = 'Material Return Quality Note'
        verbose_name_plural = 'Material Return Quality Notes'

    def __str__(self):
        return self.MRQNNo

    # Helper methods to format data for display, adhering to "Fat Model"
    @property
    def formatted_sys_date(self):
        """Returns SysDate in DD/MM/YYYY format."""
        return self.SysDate.strftime('%d/%m/%Y') if self.SysDate else ''

    @property
    def generator_name(self):
        """Returns 'Gen. By' as Title. EmployeeName."""
        if self.generated_by:
            return f"{self.generated_by.Title or ''}. {self.generated_by.EmployeeName}".strip()
        return "N/A"

    @property
    def financial_year_display(self):
        """Returns Financial Year as FinYear."""
        return self.financial_year.FinYear if self.financial_year else "N/A"

    # Business logic methods can be added here if any specific MRQN related calculations/validations are needed
```

#### 4.2 Forms

We'll define a simple form to handle the search inputs. Since this is just for filtering, a regular `Form` is sufficient, not necessarily a `ModelForm`.

**`quality_control/forms.py`**

```python
from django import forms
from .models import Employee

class MRQNPrintSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('0', 'MRQN No'),
        ('1', 'MRN No'),
        ('2', 'Employee Name'),
    ]

    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3', 'x-model': 'selectedSearchField'})
    )
    # Fields that will be conditionally visible based on search_field
    search_value_mrqn_mrn = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-64', 'placeholder': 'Enter MRQN/MRN No'})
    )
    search_value_employee_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-64',
            'placeholder': 'Enter Employee Name',
            'hx-get': '/quality_control/employees/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-model': 'employeeSearchText' # For Alpine.js
        })
    )
    # Hidden field to store employee ID from autocomplete for precise search
    employee_id_for_search = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput(attrs={'x-model': 'selectedEmployeeId'})
    )

    def clean(self):
        cleaned_data = super().clean()
        search_field = cleaned_data.get('search_field')
        search_value_mrqn_mrn = cleaned_data.get('search_value_mrqn_mrn')
        search_value_employee_name = cleaned_data.get('search_value_employee_name')
        employee_id_for_search = cleaned_data.get('employee_id_for_search')

        if search_field == '0' or search_field == '1': # MRQN No or MRN No
            if not search_value_mrqn_mrn:
                self.add_error('search_value_mrqn_mrn', "This field is required for the selected search type.")
        elif search_field == '2': # Employee Name
            if not employee_id_for_search and not search_value_employee_name:
                self.add_error('search_value_employee_name', "Please select an employee from the suggestions.")
            # If search_value_employee_name is present but employee_id_for_search is not, try to look up
            if search_value_employee_name and not employee_id_for_search:
                try:
                    # In a real scenario, this might need more robust matching or pre-validation client-side.
                    # For now, if the user types something but doesn't select, we try to match by name.
                    # This might not be exact if `fun.getCode` was more complex.
                    # Assuming a perfect match is needed for precise search.
                    employee = Employee.objects.get(EmployeeName=search_value_employee_name, CompId=self.initial.get('comp_id'))
                    cleaned_data['employee_id_for_search'] = employee.EmpId
                except Employee.DoesNotExist:
                    self.add_error('search_value_employee_name', "Selected employee not found or invalid.")
        return cleaned_data
```

#### 4.3 Views

We will use `ListView` for the main page and a custom `View` for the HTMX-driven table partial. An additional `View` will handle the employee autocomplete.

**`quality_control/views.py`**

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q
from .models import MaterialReturnQualityNote, Employee
from .forms import MRQNPrintSearchForm
import re # For extracting Employee ID from autocomplete string

class MRQNPrintListView(ListView):
    """
    Main view for Material Return Quality Note Print page.
    Displays search form and initial empty table container.
    """
    model = MaterialReturnQualityNote
    template_name = 'quality_control/materialreturnqualitynote/list.html'
    context_object_name = 'mrqn_notes'
    paginate_by = 20 # Django's built-in pagination (though DataTables will handle client-side)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the search form to the template
        context['search_form'] = MRQNPrintSearchForm(
            initial={'comp_id': self.request.session.get('compid', 0), # Pass comp_id for form validation
                     'fin_year_id': self.request.session.get('finyear', 0)
                     }
        )
        return context

class MRQNTablePartialView(View):
    """
    HTMX endpoint to render the MRQN table based on search parameters.
    This replaces the ASP.NET GridView data binding logic.
    """
    def get(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 0) # Example: Get from session
        fin_year_id = request.session.get('finyear', 0) # Example: Get from session

        search_form = MRQNPrintSearchForm(request.GET,
            initial={'comp_id': comp_id, 'fin_year_id': fin_year_id})

        mrqn_notes = []
        if search_form.is_valid():
            search_field = search_form.cleaned_data.get('search_field')
            search_value_mrqn_mrn = search_form.cleaned_data.get('search_value_mrqn_mrn')
            employee_id_for_search = search_form.cleaned_data.get('employee_id_for_search')

            # Use the custom manager to get filtered results
            mrqn_notes = MaterialReturnQualityNote.objects.get_search_results(
                comp_id, fin_year_id, search_field, search_value_mrqn_mrn, employee_id_for_search
            )
        else:
            # If form is not valid (e.g., missing required search value),
            # still render the table, possibly with an empty dataset and error message.
            messages.error(request, 'Please correct the errors in the search form.')

        context = {
            'mrqn_notes': mrqn_notes,
            'messages': messages.get_messages(request), # Pass messages for HTMX toast
        }
        # Render only the table partial
        html = render_to_string('quality_control/materialreturnqualitynote/_table.html', context, request)
        return HttpResponse(html)

class EmployeeAutocompleteView(View):
    """
    Web method equivalent for employee name autocomplete.
    Returns JSON list of employee names for HTMX.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '') # 'term' is common for autocomplete queries
        comp_id = request.session.get('compid', 0) # Example: Get from session

        if not prefix_text:
            return JsonResponse([])

        # Filter employees by name prefix and company ID
        employees = Employee.objects.filter(
            CompId=comp_id,
            EmployeeName__icontains=prefix_text # Use icontains for case-insensitive search
        ).order_by('EmployeeName')[:10] # Limit to 10 results, similar to ASP.NET CompletionSetCount

        # Format results as "EmployeeName [EmpId]"
        results = [
            f"{emp.EmployeeName} [{emp.EmpId}]" for emp in employees
        ]
        return JsonResponse(results, safe=False) # safe=False allows non-dict JSON response
```

#### 4.4 Templates

Templates will be structured to separate the main page from the HTMX-loaded data table.

**`quality_control/materialreturnqualitynote/list.html`**

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Material Return Quality Note [MRQN] - Print</h2>
    </div>

    <!-- Search Form Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{ selectedSearchField: '{{ search_form.search_field.value|default:'Select' }}', employeeSearchText: '', selectedEmployeeId: '' }">
        <form hx-get="{% url 'quality_control:mrqn_table' %}" hx-target="#mrqnTable-container" hx-trigger="submit, change from:#id_search_field">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By:</label>
                    {{ search_form.search_field }}
                </div>
                
                <div x-show="selectedSearchField === '0' || selectedSearchField === '1' || selectedSearchField === 'Select'" class="relative">
                    <label for="{{ search_form.search_value_mrqn_mrn.id_for_label }}" class="block text-sm font-medium text-gray-700">MRQN/MRN No:</label>
                    {{ search_form.search_value_mrqn_mrn }}
                    {% if search_form.search_value_mrqn_mrn.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.search_value_mrqn_mrn.errors }}</p>
                    {% endif %}
                </div>

                <div x-show="selectedSearchField === '2'" class="relative">
                    <label for="{{ search_form.search_value_employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name:</label>
                    {{ search_form.search_value_employee_name }}
                    <input type="hidden" name="{{ search_form.employee_id_for_search.name }}" x-model="selectedEmployeeId">
                    <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 w-full mt-1 rounded-md shadow-lg max-h-48 overflow-y-auto">
                        <!-- Autocomplete suggestions will be loaded here via HTMX -->
                    </div>
                    {% if search_form.search_value_employee_name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.search_value_employee_name.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="md:col-span-1 md:col-start-3 self-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm w-full md:w-auto">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Data Table Section -->
    <div id="mrqnTable-container"
         hx-trigger="load delay:100ms, refreshMRQNList from:body"
         hx-get="{% url 'quality_control:mrqn_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Return Quality Notes...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('mrqnPrintApp', () => ({
            selectedSearchField: '{{ search_form.search_field.value|default:'Select' }}',
            employeeSearchText: '',
            selectedEmployeeId: '',
            init() {
                // Initialize default visibility based on initial form value
                this.$watch('selectedSearchField', (value) => {
                    if (value === '2') {
                        this.employeeSearchText = ''; // Clear other fields on change
                        this.selectedEmployeeId = '';
                    } else {
                        this.employeeSearchText = '';
                        this.selectedEmployeeId = '';
                        document.getElementById('id_search_value_mrqn_mrn').value = '';
                    }
                });
                
                // Set initial employee search text if form has a value
                {% if search_form.search_value_employee_name.value %}
                this.employeeSearchText = '{{ search_form.search_value_employee_name.value }}';
                {% endif %}
                {% if search_form.employee_id_for_search.value %}
                this.selectedEmployeeId = '{{ search_form.employee_id_for_search.value }}';
                {% endif %}
            },
            selectEmployee(name, id) {
                this.employeeSearchText = name;
                this.selectedEmployeeId = id;
                document.getElementById('employee-suggestions').innerHTML = ''; // Clear suggestions
            }
        }));
    });

    // Handle HTMX Autocomplete response to populate suggestions
    document.addEventListener('htmx:afterOnLoad', function(event) {
        if (event.detail.target.id === 'employee-suggestions') {
            const data = JSON.parse(event.detail.xhr.responseText);
            let suggestionsHtml = '';
            if (data.length > 0) {
                data.forEach(item => {
                    const [name, id] = item.split(' [');
                    const empId = id.slice(0, -1); // Remove trailing ']'
                    suggestionsHtml += `
                        <div class="px-4 py-2 hover:bg-blue-100 cursor-pointer"
                             @click="$data.selectEmployee('${name.replace(/'/g, "\\'")}', '${empId}')">
                            ${item}
                        </div>
                    `;
                });
            } else {
                suggestionsHtml = '<div class="px-4 py-2 text-gray-500">No results</div>';
            }
            event.detail.target.innerHTML = suggestionsHtml;
        }
    });
</script>
{% endblock %}
```

**`quality_control/materialreturnqualitynote/_table.html`**

```html
{% load static %}

<!-- Display messages for HTMX requests -->
{% if messages %}
<div class="mb-4">
    {% for message in messages %}
    <div class="p-3 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}">
        {{ message }}
    </div>
    {% endfor %}
</div>
{% endif %}

<div class="overflow-x-auto">
    <table id="mrqnTable" class="min-w-full bg-white table-auto border-collapse border border-gray-200">
        <thead>
            <tr class="bg-gray-100">
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b">Action</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b">FinYear</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b">MRQN No</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b">Date</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b">MRN No</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b">Gen. By</th>
            </tr>
        </thead>
        <tbody>
            {% if mrqn_notes %}
                {% for note in mrqn_notes %}
                <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-center">
                        <a href="/materialreturnqualitynote/details/?Id={{ note.Id }}&ModId=10&SubModId=49&MRNNo={{ note.MRNNo }}&FYId={{ note.FinYearId }}&MRQNNo={{ note.MRQNNo }}&Key=RANDOMKEY"
                           class="text-blue-600 hover:text-blue-800 font-medium">
                           Select
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.financial_year_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.MRQNNo }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.formatted_sys_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.MRNNo }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.generator_name }}</td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-lg text-maroon-700 font-semibold">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#mrqnTable')) {
            $('#mrqnTable').DataTable().destroy();
        }
        $('#mrqnTable').DataTable({
            "pageLength": 10, // Default page length
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for page length
            "searching": true, // Enable search box provided by DataTables
            "ordering": true,  // Enable sorting
            "paging": true     // Enable pagination
        });
    });
</script>
```

#### 4.5 URLs

URL patterns for the `quality_control` application.

**`quality_control/urls.py`**

```python
from django.urls import path
from .views import MRQNPrintListView, MRQNTablePartialView, EmployeeAutocompleteView

app_name = 'quality_control' # Define app_name for namespacing

urlpatterns = [
    path('mrqn_print/', MRQNPrintListView.as_view(), name='mrqn_print_list'),
    path('mrqn_print/table/', MRQNTablePartialView.as_view(), name='mrqn_table'),
    path('employees/autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    # Note: The 'Select' link points to a hypothetical 'details' page,
    # which would need its own URL pattern (e.g., 'materialreturnqualitynote/details/')
    # This URL should be defined in its respective app. For this example, it's an external reference.
]
```
You would also need to include these URLs in your project's main `urls.py`:
```python
# In your project's urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('quality_control/', include('quality_control.urls', namespace='quality_control')),
    # Add other app URLs here
]
```

#### 4.6 Tests

Comprehensive tests for models and views to ensure functionality and data integrity.

**`quality_control/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialReturnQualityNote, FinancialYear, Employee
from .forms import MRQNPrintSearchForm
from django.contrib.messages import get_messages
from datetime import date

class FinancialYearModelTest(TestCase):
    def test_financial_year_creation(self):
        fy = FinancialYear.objects.create(FinYearId=2023, FinYear='2023-2024', CompId=1)
        self.assertEqual(fy.FinYear, '2023-2024')
        self.assertEqual(str(fy), '2023-2024')
        self.assertEqual(fy._meta.db_table, 'tblFinancial_master')

class EmployeeModelTest(TestCase):
    def test_employee_creation(self):
        emp = Employee.objects.create(EmpId=101, Title='Mr', EmployeeName='John Doe', CompId=1)
        self.assertEqual(emp.EmployeeName, 'John Doe')
        self.assertEqual(str(emp), 'Mr. John Doe')
        self.assertEqual(emp._meta.db_table, 'tblHR_OfficeStaff')

    def test_employee_creation_no_title(self):
        emp = Employee.objects.create(EmpId=102, EmployeeName='Jane Smith', CompId=1, Title=None)
        self.assertEqual(str(emp), 'Jane Smith')


class MaterialReturnQualityNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for related models first
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.fin_year_obj = FinancialYear.objects.create(FinYearId=cls.fin_year_id, FinYear='2023-2024', CompId=cls.comp_id)
        cls.employee_obj = Employee.objects.create(EmpId=1, Title='Mr', EmployeeName='Admin User', CompId=cls.comp_id)
        cls.employee_obj_2 = Employee.objects.create(EmpId=2, Title='Ms', EmployeeName='Jane Smith', CompId=cls.comp_id)

        # Create test MRQN data
        MaterialReturnQualityNote.objects.create(
            Id=1, SysDate='2023-01-15', MRNNo='MRN001', MRQNNo='MRQN001',
            FinYearId=cls.fin_year_id, SessionId=cls.employee_obj.EmpId, CompId=cls.comp_id
        )
        MaterialReturnQualityNote.objects.create(
            Id=2, SysDate='2023-02-20', MRNNo='MRN002', MRQNNo='MRQN002',
            FinYearId=cls.fin_year_id, SessionId=cls.employee_obj_2.EmpId, CompId=cls.comp_id
        )
        MaterialReturnQualityNote.objects.create(
            Id=3, SysDate='2022-11-01', MRNNo='MRN003', MRQNNo='MRQN003',
            FinYearId=2022, SessionId=cls.employee_obj.EmpId, CompId=cls.comp_id
        )

    def test_mrqn_creation(self):
        note = MaterialReturnQualityNote.objects.get(Id=1)
        self.assertEqual(note.MRQNNo, 'MRQN001')
        self.assertEqual(note.SysDate, date(2023, 1, 15))
        self.assertEqual(note.financial_year.FinYear, '2023-2024')
        self.assertEqual(note.generated_by.EmployeeName, 'Admin User')

    def test_formatted_properties(self):
        note = MaterialReturnQualityNote.objects.get(Id=1)
        self.assertEqual(note.formatted_sys_date, '15/01/2023')
        self.assertEqual(note.generator_name, 'Mr. Admin User')
        self.assertEqual(note.financial_year_display, '2023-2024')

    def test_get_search_results_no_filter(self):
        # Should return all notes for the current comp_id and fin_year_id <= current
        results = MaterialReturnQualityNote.objects.get_search_results(
            self.comp_id, self.fin_year_id, 'Select', None, None
        )
        self.assertEqual(results.count(), 3) # MRQN001, MRQN002, MRQN003 (as 2022 <= 2023)

    def test_get_search_results_mrqn_no(self):
        results = MaterialReturnQualityNote.objects.get_search_results(
            self.comp_id, self.fin_year_id, '0', 'MRQN001', None
        )
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first().MRQNNo, 'MRQN001')

    def test_get_search_results_mrn_no(self):
        results = MaterialReturnQualityNote.objects.get_search_results(
            self.comp_id, self.fin_year_id, '1', 'MRN002', None
        )
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first().MRNNo, 'MRN002')

    def test_get_search_results_employee_id(self):
        results = MaterialReturnQualityNote.objects.get_search_results(
            self.comp_id, self.fin_year_id, '2', None, self.employee_obj.EmpId
        )
        self.assertEqual(results.count(), 2) # MRQN001 and MRQN003 (both by Admin User)
        self.assertTrue(all(r.generator_name == 'Mr. Admin User' for r in results))

    def test_get_search_results_no_match(self):
        results = MaterialReturnQualityNote.objects.get_search_results(
            self.comp_id, self.fin_year_id, '0', 'NONEXISTENT', None
        )
        self.assertEqual(results.count(), 0)


class MRQNPrintViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.fin_year_obj = FinancialYear.objects.create(FinYearId=cls.fin_year_id, FinYear='2023-2024', CompId=cls.comp_id)
        cls.employee_obj = Employee.objects.create(EmpId=1, Title='Mr', EmployeeName='Admin User', CompId=cls.comp_id)
        cls.employee_obj_2 = Employee.objects.create(EmpId=2, Title='Ms', EmployeeName='Jane Smith', CompId=cls.comp_id)

        MaterialReturnQualityNote.objects.create(
            Id=1, SysDate='2023-01-15', MRNNo='MRN001', MRQNNo='MRQN001',
            FinYearId=cls.fin_year_id, SessionId=cls.employee_obj.EmpId, CompId=cls.comp_id
        )
        MaterialReturnQualityNote.objects.create(
            Id=2, SysDate='2023-02-20', MRNNo='MRN002', MRQNNo='MRQN002',
            FinYearId=cls.fin_year_id, SessionId=cls.employee_obj_2.EmpId, CompId=cls.comp_id
        )
        MaterialReturnQualityNote.objects.create(
            Id=3, SysDate='2022-11-01', MRNNo='MRN003', MRQNNo='MRQN003',
            FinYearId=2022, SessionId=cls.employee_obj.EmpId, CompId=cls.comp_id
        )

    def setUp(self):
        self.client = Client()
        # Simulate session variables (CompId, FinYearId)
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('quality_control:mrqn_print_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialreturnqualitynote/list.html')
        self.assertIn('search_form', response.context)

    def test_mrqn_table_partial_view_no_filter(self):
        response = self.client.get(reverse('quality_control:mrqn_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialreturnqualitynote/_table.html')
        # Check if all relevant notes are present (3 notes from setup)
        self.assertContains(response, 'MRQN001')
        self.assertContains(response, 'MRQN002')
        self.assertContains(response, 'MRQN003')

    def test_mrqn_table_partial_view_filter_mrqn_no(self):
        response = self.client.get(reverse('quality_control:mrqn_table'), {'search_field': '0', 'search_value_mrqn_mrn': 'MRQN001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRQN001')
        self.assertNotContains(response, 'MRQN002')

    def test_mrqn_table_partial_view_filter_mrn_no(self):
        response = self.client.get(reverse('quality_control:mrqn_table'), {'search_field': '1', 'search_value_mrqn_mrn': 'MRN002'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRN002')
        self.assertNotContains(response, 'MRN001')

    def test_mrqn_table_partial_view_filter_employee_name_with_id(self):
        response = self.client.get(reverse('quality_control:mrqn_table'), {
            'search_field': '2',
            'search_value_employee_name': 'Admin User', # Should be ignored if employee_id_for_search is set
            'employee_id_for_search': self.employee_obj.EmpId
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRQN001') # By Admin User
        self.assertContains(response, 'MRQN003') # By Admin User
        self.assertNotContains(response, 'MRQN002') # By Jane Smith

    def test_mrqn_table_partial_view_filter_employee_name_missing_id_invalid(self):
        # Simulates a user typing but not selecting a valid employee from autocomplete
        response = self.client.get(reverse('quality_control:mrqn_table'), {
            'search_field': '2',
            'search_value_employee_name': 'NonExistent Employee',
            'employee_id_for_search': '' # Missing or invalid ID
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display')
        messages = list(get_messages(response))
        self.assertIn('Please correct the errors in the search form.', str(messages[0]))
        self.assertIn('Selected employee not found or invalid.', str(messages[1]))

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('quality_control:employee_autocomplete'), {'term': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertJSONEqual(str(response.content, encoding='utf8'), []) # No 'john' in setup

        response = self.client.get(reverse('quality_control:employee_autocomplete'), {'term': 'Admin'})
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(str(response.content, encoding='utf8'), [f"{self.employee_obj.EmployeeName} [{self.employee_obj.EmpId}]"])

        response = self.client.get(reverse('quality_control:employee_autocomplete'), {'term': 'jane'})
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(str(response.content, encoding='utf8'), [f"{self.employee_obj_2.EmployeeName} [{self.employee_obj_2.EmpId}]"])
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:**
    *   The search form uses `hx-get` to `{% url 'quality_control:mrqn_table' %}` to load the table partial. The `hx-trigger="submit, change from:#id_search_field"` ensures the table reloads when the search button is clicked or the dropdown selection changes.
    *   The `mrqnTable-container` div uses `hx-trigger="load, refreshMRQNList from:body"` to load the table on page load and to refresh it if a custom HTMX event `refreshMRQNList` is triggered (though not used in this specific scenario, useful for related CRUD operations elsewhere).
    *   Employee autocomplete uses `hx-get` on `id_search_value_employee_name` to fetch suggestions from `employee_autocomplete` endpoint, targeting `#employee-suggestions`.
    *   Error/Success messages are passed via Django's messages framework and rendered in the partial, leveraging HTMX's `hx-swap="innerHTML"` to refresh.
*   **Alpine.js:**
    *   `x-data` on the search form div manages the `selectedSearchField` and `employeeSearchText`/`selectedEmployeeId` state.
    *   `x-show` directives conditionally display `search_value_mrqn_mrn` or `search_value_employee_name` based on `selectedSearchField`.
    *   An `init()` function and `x-watch` observe changes to `selectedSearchField` to clear irrelevant input fields, mirroring the ASP.NET `drpfield_SelectedIndexChanged` behavior.
    *   A `@click` handler on autocomplete suggestions updates `employeeSearchText` and `selectedEmployeeId` when an option is selected.
*   **DataTables:**
    *   The `_table.html` partial includes the `<table>` element with `id="mrqnTable"`.
    *   A JavaScript block within this partial (or included via `extra_js` from `base.html`) initializes DataTables on this table using `$('#mrqnTable').DataTable()`. This handles client-side pagination, sorting, and search.
    *   It's crucial to `destroy()` any existing DataTables instance before re-initializing it when the partial is reloaded by HTMX to prevent errors.

This approach provides a dynamic and responsive user experience without full page reloads, aligning with modern web development practices and significantly improving performance compared to the original ASP.NET WebForms postback model.

## Final Notes

*   **Placeholders:** Replace `RANDOMKEY` in the "Select" link with actual dynamic data if needed by the details page. If `fun.GetRandomAlphaNumeric()` was for security/cache busting, Django's CSRF tokens and standard URL patterns offer security, and browser caching is managed differently.
*   **Company/Financial Year ID:** The `CompId` and `FinYearId` are assumed to be retrieved from `request.session` (as in ASP.NET). In a production Django app, these might come from a user's profile, a URL parameter, or a multi-tenancy solution.
*   **Error Handling:** The original ASP.NET code had empty `catch (Exception ex) { }` blocks. The Django implementation includes basic form validation and messages for user feedback, but robust error logging and user-friendly error pages would be implemented at a project level.
*   **Styling:** Tailwind CSS classes (`box3`, `redbox`, `fontcsswhite` etc. from original CSS are replaced with Tailwind utility classes like `bg-blue-600`, `py-2`, `px-4`, `rounded-md`, `shadow-md`, `w-full`, etc.) are used for a modern, responsive UI.
*   **DataTables CDN:** Assumed to be included in `core/base.html` or explicitly in `list.html` `extra_js` block (as shown).