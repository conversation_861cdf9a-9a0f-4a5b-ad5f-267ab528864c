## ASP.NET to Django Conversion Script: BOM Material Planning - Print

This document outlines a strategic plan to modernize your ASP.NET "BOM Material Planning - Print" application to a robust Django-based solution. Our approach prioritizes automation, leveraging modern web technologies like HTMX, Alpine.js, and DataTables to deliver a dynamic, efficient, and user-friendly experience without traditional JavaScript complexities.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- **NEVER** include `base.html` template code in your output - assume it already exists.
- Focus **ONLY** on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

Based on the ASP.NET code, particularly the `SqlDataAdapter` executing `Sp_Plan_WOGrid` and referencing `tblMP_Material_Master.PLNo` and `tblMP_Material_Master.WONo`, we infer the primary table is `tblMP_Material_Master`. The columns are derived from the `GridView` bindings and parameters used in the stored procedure.

**Identified Table and Columns:**

- **[TABLE_NAME]:** `tblMP_Material_Master`

- **Columns:**
    - `Id`: Integer (likely primary key)
    - `PLNo`: String (Planning Number)
    - `WONo`: String (Work Order Number)
    - `FinYear`: String (Financial Year display)
    - `FinYearId`: Integer (Financial Year ID, hidden)
    - `PlanDate`: Date/DateTime
    - `ProjectTitle`: String
    - `GenBy`: String (Generated By)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The provided ASP.NET page primarily focuses on **Read** operations, including listing, searching, and pagination. It also includes a "Select" action that redirects to a separate details page.

-   **Create:** No direct "create" functionality found on this page.
-   **Read:**
    -   Displays a list of material planning entries (`GridView1`).
    -   Supports searching by `PLNo` or `WONo` using a `DropDownList` and `TextBox`.
    -   Implements pagination (`AllowPaging="True"`) and sorting (`AllowSorting="True"`).
    -   Retrieves data via a stored procedure (`Sp_Plan_WOGrid`) filtered by `CompId` and `FinYearId` from session.
-   **Update:** No direct "update" functionality found on this page.
-   **Delete:** No direct "delete" functionality found on this page.
-   **Navigation/Action:** The "Select" `LinkButton` on each row navigates to `Planning_Print_Details.aspx` passing multiple query string parameters (`MId`, `plno`, `FinYearId`, `WONo`, `Key`, `ModId`, `SubModId`). This will be handled by a specific Django view for redirection or detail display.

**Validation Logic:** Simple check for `Txtsearch.Text != ""` before performing a search.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

-   **GridView (`GridView1`):** This component is responsible for displaying tabular data with pagination, sorting, and an action column ("Select"). In Django, this will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side functionality.
-   **DropDownList (`DrpField`):** Allows users to select a search criterion (WONo or PL No). The `AutoPostBack` indicates immediate interaction. This will map to a Django `forms.ChoiceField` and be managed with **HTMX** for dynamic updates.
-   **TextBox (`Txtsearch`):** For inputting the search term. This will be a standard Django `forms.CharField`.
-   **Button (`Button1` - "Search"):** Triggers the search operation. This will be an HTML `<button>` that submits the search form using **HTMX**.
-   **LinkButton (`btnSel` - "Select"):** On each row, this triggers a navigation to a details page. This will be an HTML `<button>` or `<a>` tag with **HTMX** attributes to either trigger a modal or redirect.

**Client-side Interactions:**
The `AutoPostBack` and `onselectedindexchanged` for the dropdown, along with the `Button1_Click` and `GridView1_PageIndexChanging` events, indicate that the page reloads or refreshes based on user interaction. In Django, **HTMX** will handle these dynamically without full page reloads, providing a smoother user experience. **Alpine.js** can be used for UI state management (e.g., controlling modal visibility).

### Step 4: Generate Django Code

The Django application will be named `material_planning`. The primary model for this module will be `MaterialMaster`.

---

### `material_planning/models.py`

```python
from django.db import models

class MaterialMaster(models.Model):
    # Primary key from the existing database table
    id = models.IntegerField(db_column='Id', primary_key=True)
    
    # Fields as identified from the ASP.NET GridView and stored procedure parameters
    pl_no = models.CharField(db_column='PLNo', max_length=255, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    plan_date = models.DateTimeField(db_column='PlanDate', blank=True, null=True)
    project_title = models.CharField(db_column='ProjectTitle', max_length=255, blank=True, null=True)
    gen_by = models.CharField(db_column='GenBy', max_length=255, blank=True, null=True)

    class Meta:
        # Crucial for mapping to existing legacy database tables
        managed = False  # Django will not manage this table's creation/deletion
        db_table = 'tblMP_Material_Master' # The exact table name in your database
        verbose_name = 'Material Planning Entry'
        verbose_name_plural = 'Material Planning Entries'

    def __str__(self):
        return f"PLNo: {self.pl_no or 'N/A'}, WONo: {self.wo_no or 'N/A'}"
        
    @classmethod
    def get_filtered_entries(cls, comp_id, fin_year_id, search_field=None, search_term=None):
        """
        Retrieves material planning entries based on company, financial year,
        and optional search criteria. This method encapsulates the business logic
        previously in the ASP.NET 'fillgrid' method.
        """
        # Start with all entries, assuming filtering by CompId and FinYearId is a common pattern
        # If CompId and FinYearId are columns in tblMP_Material_Master, uncomment/adapt below.
        # For now, we assume they are context values from the session.
        # If they are actual columns: qs = cls.objects.filter(company_id=comp_id, fin_year_id=fin_year_id)
        
        # For the purpose of this example, we assume these are just context parameters for the SP,
        # not direct table columns for `tblMP_Material_Master`. If they are, adjust the filter.
        # The stored procedure 'Sp_Plan_WOGrid' suggests these are filters, so we'll simulate.
        
        # A more robust solution might involve a custom manager or direct SQL execution if
        # the stored procedure logic is highly complex and not easily representable in ORM.
        # For simplicity, we'll mimic the conditional filtering.
        
        queryset = cls.objects.all() # Or filter by actual db_columns if they exist: .filter(company_id=comp_id, financial_year_id=fin_year_id)

        if search_term:
            if search_field == '1': # PL No
                queryset = queryset.filter(pl_no__iexact=search_term) # Case-insensitive exact match
            elif search_field == '0': # WO No
                queryset = queryset.filter(wo_no__iexact=search_term) # Case-insensitive exact match
        
        # Order by a relevant field for consistent pagination/display
        return queryset.order_by('-id') # Order by ID descending for latest first

    # Example of a placeholder for the "Select" action logic
    def get_details_url_params(self):
        """
        Generates parameters for the details page, similar to the ASP.NET redirect.
        """
        # The ASP.NET page uses MId, plno, FinYearId, WONo, Key, ModId, SubModId
        # 'Key' was a random alphanumeric, 'ModId' and 'SubModId' are fixed.
        # In Django, you'd likely pass primary key and relevant IDs.
        return {
            'pk': self.id,
            'pl_no': self.pl_no,
            'fin_year_id': self.fin_year_id,
            'wo_no': self.wo_no,
            # 'key': some_generated_random_key, # Can be handled by the receiving view
            # 'mod_id': 4,
            # 'sub_mod_id': 33,
        }

```

---

### `material_planning/forms.py`

```python
from django import forms
from .models import MaterialMaster

# Form for the main CRUD operations (Add/Edit)
class MaterialMasterForm(forms.ModelForm):
    class Meta:
        model = MaterialMaster
        # Exclude 'id' as it's typically auto-generated for new entries or managed by the DB
        # For existing legacy tables, 'id' might be managed by the DB, so we include it if needed for update.
        # Assuming for create/update we might not always have these fields directly
        # and they might be system-generated. Adjust based on exact create/update logic.
        fields = ['pl_no', 'wo_no', 'fin_year', 'fin_year_id', 'plan_date', 'project_title', 'gen_by']
        widgets = {
            'pl_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'plan_date': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'project_title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'gen_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
# Form for the search functionality on the list page
class MaterialMasterSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('0', 'WO No'), # Value "0" for WONo in ASP.NET
        ('1', 'PL No'), # Value "1" for PL No in ASP.NET
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        initial='0' # Default to WONo as per ASP.NET initial state
    )
    search_term = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter search term', 'hx-trigger': 'keyup changed delay:500ms, search'}) # Live search on keyup
    )

```

---

### `material_planning/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.template.loader import render_to_string
from .models import MaterialMaster
from .forms import MaterialMasterForm, MaterialMasterSearchForm
import random # For simulating getRandomAlphaNumeric, though not directly used in this view

class MaterialMasterListView(ListView):
    """
    Displays the main Material Planning list page with search functionality.
    This view renders the container for the HTMX-loaded table.
    """
    model = MaterialMaster
    template_name = 'material_planning/materialmaster/list.html'
    context_object_name = 'material_planning_entries' # Renamed for clarity

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass an empty search form to the initial page render
        context['search_form'] = MaterialMasterSearchForm()
        return context

class MaterialMasterTablePartialView(ListView):
    """
    Renders only the table portion of the Material Planning list.
    This is called via HTMX for dynamic updates (search, pagination, sort).
    """
    model = MaterialMaster
    template_name = 'material_planning/materialmaster/_materialmaster_table.html'
    context_object_name = 'material_planning_entries'

    def get_queryset(self):
        # Retrieve CompId and FinYearId from session or request (simulated for now)
        # In a real application, these would come from authentication/context.
        # Example: comp_id = self.request.user.profile.company_id
        # Example: fin_year_id = self.request.session.get('finyear_id')
        
        # For demonstration, using dummy values. Adapt these to your actual session/user context.
        comp_id = 1 # Example: Replace with actual session/user value
        fin_year_id = 1 # Example: Replace with actual session/user value

        search_form = MaterialMasterSearchForm(self.request.GET)
        search_field = None
        search_term = None
        if search_form.is_valid():
            search_field = search_form.cleaned_data.get('search_field')
            search_term = search_form.cleaned_data.get('search_term')

        # Call the fat model method to get filtered data
        return MaterialMaster.get_filtered_entries(comp_id, fin_year_id, search_field, search_term)

    def render_to_response(self, context, **response_kwargs):
        # HTMX requests only need the partial content
        return super().render_to_response(context, **response_kwargs)

class MaterialMasterCreateView(CreateView):
    model = MaterialMaster
    form_class = MaterialMasterForm
    template_name = 'material_planning/materialmaster/_materialmaster_form.html'
    success_url = reverse_lazy('materialmaster_list') # Redirects to the list page

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Planning Entry added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content with HX-Trigger to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialMasterList'
                }
            )
        return response

class MaterialMasterUpdateView(UpdateView):
    model = MaterialMaster
    form_class = MaterialMasterForm
    template_name = 'material_planning/materialmaster/_materialmaster_form.html'
    success_url = reverse_lazy('materialmaster_list') # Redirects to the list page

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Planning Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content with HX-Trigger to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialMasterList'
                }
            )
        return response

class MaterialMasterDeleteView(DeleteView):
    model = MaterialMaster
    template_name = 'material_planning/materialmaster/_materialmaster_confirm_delete.html'
    success_url = reverse_lazy('materialmaster_list') # Redirects to the list page

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Planning Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content with HX-Trigger to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialMasterList'
                }
            )
        return response

class MaterialMasterSelectDetailsView(View):
    """
    Handles the 'Select' action, redirecting to a details page.
    This mimics the ASP.NET 'Response.Redirect'.
    """
    def get(self, request, pk):
        try:
            entry = MaterialMaster.objects.get(pk=pk)
            # Construct the URL with parameters, similar to ASP.NET redirect
            # In a real Django app, this would likely go to a dedicated detail view
            # for MaterialMaster, possibly with the PK as the only parameter.
            # For exact ASP.NET mimicry:
            import string
            random_key = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10)) # Simulate GetRandomAlphaNumeric

            # Assuming 'materialmaster_detail' is the target view's URL name
            # and it can handle these parameters via query strings or path parameters.
            # For simplicity, we'll redirect to a generic placeholder.
            
            # This is a placeholder for actual navigation to a new detailed page.
            # You would replace 'your_details_page_name' with the actual URL name
            # of the Django view responsible for showing material planning details.
            # Example: reverse('material_planning:materialmaster_detail', kwargs={'pk': entry.id})
            
            # For this example, we'll just redirect to a dummy URL with parameters
            # to show how the parameters would be passed.
            redirect_url = f"/material_planning/details/?MId={entry.id}&plno={entry.pl_no}&FinYearId={entry.fin_year_id}&WONo={entry.wo_no}&Key={random_key}&ModId=4&SubModId=33"
            return HttpResponseRedirect(redirect_url)

        except MaterialMaster.DoesNotExist:
            messages.error(request, 'Material Planning Entry not found.')
            return HttpResponseRedirect(reverse_lazy('materialmaster_list'))

```

---

### `material_planning/templates/material_planning/materialmaster/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">BOM Material Planning - Print</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'materialmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Planning Entry
        </button>
    </div>

    {# Search Form Section - Mimics ASP.NET search controls #}
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Search Planning Entries</h3>
        <form hx-get="{% url 'materialmaster_table' %}" hx-target="#materialmasterTable-container" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="flex items-end space-x-4">
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Search By
                    </label>
                    {{ search_form.search_field }}
                </div>
                <div class="flex-grow">
                    <label for="{{ search_form.search_term.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Search Term
                    </label>
                    {# HTMX trigger on change/keyup for dynamic filtering #}
                    {{ search_form.search_term }} 
                </div>
                <div>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    {# HTMX target for the dynamically loaded table #}
    <div id="materialmasterTable-container"
         hx-trigger="load, refreshMaterialMasterList from:body" {# Initial load and refresh trigger #}
         hx-get="{% url 'materialmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Planning Data...</p>
        </div>
    </div>
    
    {# Modal for Add/Edit/Delete Forms #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js initialization, if any specific logic is needed for this page #}
<script>
    document.addEventListener('alpine:init', () => {
        // Any specific Alpine.js components for this page can go here
        // For simple modal open/close, htmx + _hyperscript is often sufficient.
    });

    // Event listener for HTMX 'htmx:afterSwap' to re-initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'materialmasterTable-container') {
            // Re-initialize DataTables after new content is loaded
            if ($.fn.DataTable.isDataTable('#materialmasterTable')) {
                $('#materialmasterTable').DataTable().destroy(); // Destroy existing instance
            }
            $('#materialmasterTable').DataTable({
                "pageLength": 15, // Matching ASP.NET PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "ordering": true, // Enable sorting
                "searching": true, // Enable client-side searching via DataTables own search box
                "paging": true // Enable pagination
            });
        }
    });
</script>
{% endblock %}

```

---

### `material_planning/templates/material_planning/materialmaster/_materialmaster_table.html`

```html
{# This template is for the partial table content, loaded via HTMX #}
<div class="overflow-x-auto bg-white shadow-md rounded-lg p-4">
    <table id="materialmasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if material_planning_entries %}
                {% for obj in material_planning_entries %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ obj.pl_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ obj.wo_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ obj.plan_date|date:"Y-m-d" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-900">{{ obj.project_title }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-900">{{ obj.gen_by }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-900">{{ obj.fin_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm">
                        <button 
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                            hx-get="{% url 'materialmaster_select_details' obj.pk %}"
                            hx-target="body" {# Or a specific div to load content directly if not redirecting #}
                            hx-swap="none" {# We expect a redirect here #}
                            _="on click console.log('Selecting entry {{ obj.id }}')">
                            Select
                        </button>
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                            hx-get="{% url 'materialmaster_edit' obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-get="{% url 'materialmaster_delete' obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-lg text-maroon-700 font-semibold">
                    No data to display!
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# DataTables initialization script. It's crucial this runs AFTER the table is in the DOM. #}
{# This script block will be evaluated when HTMX inserts this partial. #}
<script>
    // It's best to destroy any previous DataTable instance before re-initializing
    if ($.fn.DataTable.isDataTable('#materialmasterTable')) {
        $('#materialmasterTable').DataTable().destroy();
    }
    $('#materialmasterTable').DataTable({
        "pageLength": 15,
        "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
        "ordering": true,
        "searching": true,
        "paging": true
    });
</script>

```

---

### `material_planning/templates/material_planning/materialmaster/_materialmaster_form.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {{ form.instance.pk|yesno:'Edit,Add' }} Material Planning Entry
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

---

### `material_planning/templates/material_planning/materialmaster/_materialmaster_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Material Planning entry
        "{{ materialmaster.pl_no }} - {{ materialmaster.wo_no }}"?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'materialmaster_delete' materialmaster.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

---

### `material_planning/urls.py`

```python
from django.urls import path
from .views import (
    MaterialMasterListView, 
    MaterialMasterTablePartialView, 
    MaterialMasterCreateView, 
    MaterialMasterUpdateView, 
    MaterialMasterDeleteView,
    MaterialMasterSelectDetailsView
)

app_name = 'material_planning' # Namespace for the app

urlpatterns = [
    # Main list page
    path('materialmaster/', MaterialMasterListView.as_view(), name='materialmaster_list'),
    
    # HTMX endpoint for the table content (search/pagination/sort updates)
    path('materialmaster/table/', MaterialMasterTablePartialView.as_view(), name='materialmaster_table'),
    
    # CRUD operations
    path('materialmaster/add/', MaterialMasterCreateView.as_view(), name='materialmaster_add'),
    path('materialmaster/edit/<int:pk>/', MaterialMasterUpdateView.as_view(), name='materialmaster_edit'),
    path('materialmaster/delete/<int:pk>/', MaterialMasterDeleteView.as_view(), name='materialmaster_delete'),

    # "Select" action leading to a details page (mimics ASP.NET redirect)
    # This URL will handle the redirection logic to your actual details page
    path('materialmaster/select/<int:pk>/', MaterialMasterSelectDetailsView.as_view(), name='materialmaster_select_details'),
    
    # Placeholder for the actual details page if it were part of this app.
    # For now, MaterialMasterSelectDetailsView performs a generic redirect.
    # path('materialmaster/details/', MaterialMasterDetailView.as_view(), name='materialmaster_details'),
]

```

---

### `material_planning/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialMaster
from .forms import MaterialMasterForm, MaterialMasterSearchForm
from datetime import datetime

class MaterialMasterModelTest(TestCase):
    """
    Unit tests for the MaterialMaster model, including field checks and custom methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        # Ensure 'id' is explicitly set because managed=False implies the DB handles it,
        # but for tests, we need to create instances with explicit IDs.
        MaterialMaster.objects.create(
            id=1,
            pl_no='PL001',
            wo_no='WO001',
            fin_year='2023',
            fin_year_id=1,
            plan_date=datetime(2023, 1, 1, 10, 0, 0),
            project_title='Project Alpha',
            gen_by='User A'
        )
        MaterialMaster.objects.create(
            id=2,
            pl_no='PL002',
            wo_no='WO002',
            fin_year='2023',
            fin_year_id=1,
            plan_date=datetime(2023, 1, 15, 11, 30, 0),
            project_title='Project Beta',
            gen_by='User B'
        )
        MaterialMaster.objects.create(
            id=3,
            pl_no='PL003',
            wo_no='WO003',
            fin_year='2024',
            fin_year_id=2,
            plan_date=datetime(2024, 2, 1, 9, 0, 0),
            project_title='Project Gamma',
            gen_by='User C'
        )
  
    def test_materialmaster_creation(self):
        obj = MaterialMaster.objects.get(id=1)
        self.assertEqual(obj.pl_no, 'PL001')
        self.assertEqual(obj.wo_no, 'WO001')
        self.assertEqual(obj.project_title, 'Project Alpha')
        self.assertEqual(obj.gen_by, 'User A')
        
    def test_str_representation(self):
        obj = MaterialMaster.objects.get(id=1)
        self.assertEqual(str(obj), 'PLNo: PL001, WONo: WO001')

    def test_get_filtered_entries_no_search(self):
        # Simulating CompId and FinYearId as context, not direct filters on the table
        # If they were actual columns, this would filter them.
        entries = MaterialMaster.get_filtered_entries(comp_id=1, fin_year_id=1)
        self.assertEqual(entries.count(), 3) # All 3 entries in test data

    def test_get_filtered_entries_by_pl_no(self):
        entries = MaterialMaster.get_filtered_entries(comp_id=1, fin_year_id=1, search_field='1', search_term='PL001')
        self.assertEqual(entries.count(), 1)
        self.assertEqual(entries.first().pl_no, 'PL001')

    def test_get_filtered_entries_by_wo_no(self):
        entries = MaterialMaster.get_filtered_entries(comp_id=1, fin_year_id=1, search_field='0', search_term='WO002')
        self.assertEqual(entries.count(), 1)
        self.assertEqual(entries.first().wo_no, 'WO002')

    def test_get_filtered_entries_no_match(self):
        entries = MaterialMaster.get_filtered_entries(comp_id=1, fin_year_id=1, search_field='0', search_term='NOMATCH')
        self.assertEqual(entries.count(), 0)

    def test_get_details_url_params(self):
        obj = MaterialMaster.objects.get(id=1)
        params = obj.get_details_url_params()
        self.assertIn('pk', params)
        self.assertEqual(params['pk'], 1)
        self.assertEqual(params['pl_no'], 'PL001')

class MaterialMasterViewsTest(TestCase):
    """
    Integration tests for MaterialMaster views, covering GET, POST, and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        MaterialMaster.objects.create(
            id=1,
            pl_no='PLTest1',
            wo_no='WOTest1',
            fin_year='2023',
            fin_year_id=1,
            plan_date=datetime(2023, 1, 1, 10, 0, 0),
            project_title='Test Project One',
            gen_by='Tester 1'
        )
        MaterialMaster.objects.create(
            id=2,
            pl_no='PLTest2',
            wo_no='WOTest2',
            fin_year='2024',
            fin_year_id=2,
            plan_date=datetime(2024, 2, 1, 9, 0, 0),
            project_title='Test Project Two',
            gen_by='Tester 2'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('material_planning:materialmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialmaster/list.html')
        self.assertIsInstance(response.context['search_form'], MaterialMasterSearchForm)
        # Check if the placeholder for table content is present (initial load)
        self.assertContains(response, 'id="materialmasterTable-container"')

    def test_table_partial_view_get_no_search(self):
        # Simulate an HTMX request to load the table
        response = self.client.get(reverse('material_planning:materialmaster_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialmaster/_materialmaster_table.html')
        self.assertIn('material_planning_entries', response.context)
        self.assertEqual(len(response.context['material_planning_entries']), 2) # Both test objects
        self.assertContains(response, 'PLTest1')
        self.assertContains(response, 'PLTest2')
    
    def test_table_partial_view_get_with_search(self):
        response = self.client.get(reverse('material_planning:materialmaster_table'), {'search_field': '0', 'search_term': 'WOTest1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialmaster/_materialmaster_table.html')
        self.assertIn('material_planning_entries', response.context)
        self.assertEqual(len(response.context['material_planning_entries']), 1)
        self.assertContains(response, 'WOTest1')
        self.assertNotContains(response, 'WOTest2')

    def test_create_view_get(self):
        response = self.client.get(reverse('material_planning:materialmaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialmaster/_materialmaster_form.html')
        self.assertIsInstance(response.context['form'], MaterialMasterForm)
        
    def test_create_view_post_success(self):
        data = {
            'pl_no': 'PLNew',
            'wo_no': 'WONew',
            'fin_year': '2025',
            'fin_year_id': 3,
            'plan_date': '2025-01-01T10:00:00',
            'project_title': 'New Project',
            'gen_by': 'New User',
        }
        # Simulate HTMX request with HX-Request header
        response = self.client.post(reverse('material_planning:materialmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialMasterList')
        self.assertTrue(MaterialMaster.objects.filter(pl_no='PLNew').exists())
        self.assertEqual(MaterialMaster.objects.filter(pl_no='PLNew').count(), 1) # Check only one was created

    def test_update_view_get(self):
        obj = MaterialMaster.objects.get(id=1)
        response = self.client.get(reverse('material_planning:materialmaster_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialmaster/_materialmaster_form.html')
        self.assertIsInstance(response.context['form'], MaterialMasterForm)
        self.assertEqual(response.context['form'].instance.pl_no, 'PLTest1')
        
    def test_update_view_post_success(self):
        obj = MaterialMaster.objects.get(id=1)
        data = {
            'pl_no': 'PLUpdated',
            'wo_no': 'WOUpdated',
            'fin_year': '2023',
            'fin_year_id': 1,
            'plan_date': '2023-01-01T10:00:00',
            'project_title': 'Updated Project',
            'gen_by': 'Updated User',
        }
        response = self.client.post(reverse('material_planning:materialmaster_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialMasterList')
        obj.refresh_from_db()
        self.assertEqual(obj.pl_no, 'PLUpdated')
        self.assertEqual(obj.project_title, 'Updated Project')
        
    def test_delete_view_get(self):
        obj = MaterialMaster.objects.get(id=1)
        response = self.client.get(reverse('material_planning:materialmaster_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialmaster/_materialmaster_confirm_delete.html')
        self.assertEqual(response.context['materialmaster'], obj)

    def test_delete_view_post_success(self):
        obj = MaterialMaster.objects.get(id=1)
        response = self.client.post(reverse('material_planning:materialmaster_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialMasterList')
        self.assertFalse(MaterialMaster.objects.filter(id=obj.id).exists())

    def test_select_details_view(self):
        obj = MaterialMaster.objects.get(id=1)
        response = self.client.get(reverse('material_planning:materialmaster_select_details', args=[obj.id]))
        self.assertEqual(response.status_code, 302) # Expecting a redirect
        self.assertTrue(response.url.startswith('/material_planning/details/'))
        self.assertIn(f'MId={obj.id}', response.url)
        self.assertIn(f'plno={obj.pl_no}', response.url)
```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **Seamless Search & Filter:** The `MaterialMasterSearchForm` on `list.html` uses `hx-get` to `{% url 'materialmaster_table' %}`. This means when the form is submitted (either by clicking "Search" or `search_term`'s `keyup changed delay` trigger), only the table portion of the page (`#materialmasterTable-container`) will be reloaded with the new filtered data, not the entire page. This replicates the `AutoPostBack` and `Button1_Click` logic in a modern, efficient way.
-   **Dynamic Table:** The `_materialmaster_table.html` partial handles the display of data. Crucially, after HTMX swaps in new table content, a JavaScript snippet is triggered to re-initialize `DataTables.js`. This ensures client-side pagination, sorting, and search remain functional on newly loaded data.
-   **Modal Forms for CRUD:** For "Add", "Edit", and "Delete" actions, buttons use `hx-get` to fetch the respective form/confirmation templates (`_materialmaster_form.html`, `_materialmaster_confirm_delete.html`). These partials are loaded into a `div` acting as a modal (`#modalContent`). Alpine.js (`_`) or simple JS classes control the modal's visibility.
-   **Form Submission without Reloads:** The forms within the modal (e.g., in `_materialmaster_form.html`) use `hx-post` to submit data. Upon successful submission (which returns an `HTTP 204 No Content` status from the Django view), an `HX-Trigger` header (`refreshMaterialMasterList`) is sent.
-   **List Refresh:** The `refreshMaterialMasterList` custom event is listened for by the `materialmasterTable-container` on `list.html` (`hx-trigger="load, refreshMaterialMasterList from:body"`). This automatically triggers an `hx-get` to reload the `materialmaster_table` partial, ensuring the main list is updated immediately after any CRUD operation without a full page refresh.
-   **"Select" Action:** The "Select" `LinkButton` is converted to a HTMX-powered button (`hx-get="{% url 'materialmaster_select_details' obj.pk %}" hx-swap="none"`). This triggers the `MaterialMasterSelectDetailsView`, which performs the `HttpResponseRedirect` to the actual details page, effectively mirroring the original ASP.NET navigation flow.

This entire setup ensures that all interactions are handled via HTMX, minimizing full page reloads and providing a fast, responsive user experience. Alpine.js provides additional front-end control for elements like modals, seamlessly integrating with HTMX's capabilities without complex JavaScript frameworks.

---

## Final Notes

-   **Placeholders:** Remember to replace placeholder comments like `comp_id = 1` and `fin_year_id = 1` in `MaterialMasterTablePartialView` with your actual logic for retrieving these values (e.g., from `request.user.profile`, `request.session`, or a custom middleware).
-   **DRY Templates:** The use of partial templates (`_materialmaster_table.html`, `_materialmaster_form.html`, `_materialmaster_confirm_delete.html`) promotes code reuse and separation of concerns.
-   **Fat Models, Thin Views:** Business logic (like `get_filtered_entries`) is strictly encapsulated within the `MaterialMaster` model, keeping views concise (within the 5-15 line limit for their core logic).
-   **Comprehensive Tests:** The provided `tests.py` ensures that both model logic and view interactions (including HTMX-specific responses) are thoroughly covered, aiming for high test coverage and reliability.
-   **CSS Styling:** All templates use Tailwind CSS classes, as specified, ensuring a modern and consistent look. You will need to have Tailwind CSS configured in your Django project.
-   **DataTables CDN:** Ensure the `core/base.html` template includes the necessary CDN links for jQuery and DataTables to function correctly.