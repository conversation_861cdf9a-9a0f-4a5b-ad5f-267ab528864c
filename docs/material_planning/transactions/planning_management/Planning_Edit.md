## ASP.NET to Django Conversion Script: Material Planning - Edit

This document outlines a strategic plan to modernize your legacy ASP.NET Material Planning module to a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation to transform the existing functionality into modern Django patterns, focusing on a "fat model, thin view" architecture, dynamic frontend interactions with HTMX and Alpine.js, and efficient data presentation using DataTables.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
- Analyzing the ASP.NET code-behind, we identify the primary table `tblMP_Material_Master` which is central to this module.
- Several lookup/related tables are accessed: `tblMM_Supplier_master`, `tblFinancial_master`, `tblMM_PR_Master`, and `tblMM_PR_Details`. The `CompId` (Company ID) and `FinYearId` (Financial Year ID) are consistently used for filtering and lookup, suggesting relationships to `Company` and `FinancialYear` master tables.

**Inferred Schema:**

*   **`tblCompany_master`** (Inferred as `Company`):
    *   `CompId` (PK, int)
    *   `CompName` (varchar) - *Inferred for context*

*   **`tblMM_Supplier_master`** (Mapped to `Supplier`):
    *   `SupplierId` (PK, varchar)
    *   `SupplierName` (varchar)
    *   `CompId` (FK to `tblCompany_master.CompId`, int)

*   **`tblFinancial_master`** (Mapped to `FinancialYear`):
    *   `FinYearId` (PK, int)
    *   `FinYear` (varchar)
    *   `CompId` (FK to `tblCompany_master.CompId`, int)

*   **`tblMP_Material_Master`** (Mapped to `MaterialPlanning`):
    *   `Id` (PK, int)
    *   `CompId` (FK to `tblCompany_master.CompId`, int)
    *   `FinYearId` (FK to `tblFinancial_master.FinYearId`, int)
    *   `PLNo` (varchar)
    *   `WONo` (varchar)
    *   `SupplierId` (FK to `tblMM_Supplier_master.SupplierId`, varchar, nullable)
    *   `CompDate` (datetime/date, nullable)
    *   `PId` (varchar, nullable)
    *   `CId` (varchar, nullable)
    *   `ItemId` (varchar, nullable)
    *   `SysDate` (datetime/date, nullable)
    *   `SysTime` (datetime/time, nullable)

*   **`tblMM_PR_Master`** (Mapped to `PrMaster`, inferred for `has_pr_details` logic):
    *   `Id` (PK, int)
    *   `PRNo` (varchar)
    *   `WONo` (varchar)
    *   `CompId` (FK to `tblCompany_master.CompId`, int)
    *   *Other fields as per original schema*

*   **`tblMM_PR_Details`** (Mapped to `PrDetail`, inferred for `has_pr_details` logic):
    *   `Id` (PK, int)
    *   `MId` (FK to `tblMM_PR_Master.Id`, int) - relationship to master
    *   `PRNo` (varchar)
    *   `PId` (varchar)
    *   `ItemId` (varchar)
    *   `CId` (varchar)
    *   *Other fields as per original schema*

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
The ASP.NET page is titled "Material Planning - Edit", primarily focusing on **Read (List with Search/Filter)** and **Update (Inline Editing)** operations. There's also a "Select" action which redirects to a detail page.

*   **Read (List & Search):**
    *   The `fillgrid` function fetches data from `tblMP_Material_Master` based on `CompId` and `FinYearId` from the session.
    *   It supports searching by `PLNo` (Planning Order Number) or `SupplierName`.
    *   It performs lookups for `SupplierName` and `FinYear` from their respective master tables.
    *   Pagination is handled by `GridView1_PageIndexChanging`.
*   **Update (Inline Edit):**
    *   The `GridView1_RowCommand` handles the "edt" and "save" commands.
    *   "edt" (edit) command: Makes `txtSupName` (Supplier Name) and `TxtCompDate` (Completion Date) editable within the grid row.
    *   "save" command: Updates `SupplierId` and `CompDate` in `tblMP_Material_Master` for the selected `PLNo` and `Id`. It also updates `SysDate` and `SysTime`.
    *   Validation: `RequiredFieldValidator` for `txtSupName` and `TxtCompDate`, `RegularExpressionValidator` for `TxtCompDate` format.
*   **Select:**
    *   The "Sel" command redirects to `Planning_Edit_Details.aspx`, passing `plno` and `MId` (Id). This implies navigating to a more detailed view of the selected planning record.
*   **Business Logic / Conditional Display:**
    *   The `disableEdit()` function determines if a record can be edited or only selected based on whether a `Supplier` is already assigned and if corresponding entries exist in `tblMM_PR_Details` (Purchase Requisition Details) for the given WO No, Item, PId, and CId. This is complex conditional logic tied to data.
*   **Auto-completion:**
    *   `GetCompletionList` (WebMethod) provides auto-complete suggestions for supplier names by searching `tblMM_Supplier_master`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The UI consists of a search/filter section at the top, followed by a data grid.

*   **Search/Filter Section:**
    *   `DrpField` (DropDownList): Selects the search criterion (e.g., "Supplier Name", "PL No").
    *   `Txtsearch` (TextBox): Input for `PLNo` search.
    *   `txtCustName` (TextBox) with `AutoCompleteExtender`: Input for `Supplier Name` search with auto-completion.
    *   `Button1` (Button): Triggers the search.
*   **Data Grid (`GridView1`):**
    *   Displays a list of "Material Planning" records with columns like SN, FinYear, PL No, WO No, SupplierName, Comp Date.
    *   Supports pagination (`AllowPaging="True"`).
    *   **Per-row actions:**
        *   `LinkButton` with `CommandName="Sel"` ("Select"): Navigates to a detail page.
        *   `LinkButton` with `CommandName="edt"` ("Edit"): Toggles inline editing for `SupplierName` and `CompDate`.
        *   `LinkButton` with `CommandName="save"` ("Save"): Saves inline edits.
        *   `LinkButton` with `CommandName="Cancel"` ("Cancel"): Reverts inline edits.
        *   `Label` `lblPR`: Conditionally displayed based on `disableEdit()` logic.
    *   **In-line Edit Fields:**
        *   `txtSupName` (TextBox) with `AutoCompleteExtender`: For editing `SupplierName`.
        *   `TxtCompDate` (TextBox) with `CalendarExtender`: For editing `CompDate`.
        *   `RequiredFieldValidator`, `RegularExpressionValidator` for validation during inline edit.

### Step 4: Generate Django Code

### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
- `Company` model (inferred), `Supplier`, `FinancialYear`, `PrMaster`, `PrDetail` (inferred) models for relationships.
- `MaterialPlanning` will be the core model for this module.
- All models will use `managed = False` and `db_table` to map to existing database tables.
- Model methods encapsulate business logic, adhering to the "fat model" principle.

**File:** `material_planning/models.py`

```python
from django.db import models
from django.urls import reverse
from datetime import datetime, date, time

# Inferred Company model, assuming a central company table
class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompName', max_length=255) # Placeholder for actual column name

    class Meta:
        managed = False
        db_table = 'tblCompany_master' # Replace with your actual Company master table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class Supplier(models.Model):
    id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50) # Assuming SupplierId is VARCHAR/NVARCHAR
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='suppliers')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.id}]"

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    financial_year = models.CharField(db_column='FinYear', max_length=50)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='financial_years')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.financial_year

# Placeholder models for PR related tables needed by MaterialPlanning.has_pr_details
class PrMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='pr_masters')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'
    
    def __str__(self):
        return self.pr_no

class PrDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_master = models.ForeignKey(PrMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='pr_details')
    pr_no = models.CharField(db_column='PRNo', max_length=50) # Redundant but kept for direct mapping
    p_id = models.CharField(db_column='PId', max_length=50, null=True, blank=True)
    item_id = models.CharField(db_column='ItemId', max_length=50, null=True, blank=True)
    c_id = models.CharField(db_column='CId', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"{self.pr_no} - {self.item_id}"


class MaterialPlanning(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='material_planning_records')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='material_planning_records')
    pl_no = models.CharField(db_column='PLNo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='material_planning_records', null=True, blank=True)
    completion_date = models.DateField(db_column='CompDate', null=True, blank=True)
    p_id = models.CharField(db_column='PId', max_length=50, null=True, blank=True)
    c_id = models.CharField(db_column='CId', max_length=50, null=True, blank=True)
    item_id = models.CharField(db_column='ItemId', max_length=50, null=True, blank=True)
    system_date = models.DateField(db_column='SysDate', null=True, blank=True)
    system_time = models.TimeField(db_column='SysTime', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMP_Material_Master'
        verbose_name = 'Material Planning'
        verbose_name_plural = 'Material Planning'
        ordering = ['-id'] # Matches Order By Id Desc

    def __str__(self):
        return f"{self.pl_no} - {self.wo_no}"

    def save(self, *args, **kwargs):
        # Update SysDate and SysTime automatically on save, mimicking ASP.NET
        self.system_date = date.today()
        self.system_time = datetime.now().time()
        super().save(*args, **kwargs)

    def has_pr_details(self):
        """
        Mimics the complex SQL query from ASP.NET's disableEdit() logic.
        Checks if related PR details exist for this material planning record.
        """
        # This is the ORM translation of the complex SQL query for tblMM_PR_Details
        # StrSql2 = fun.select("tblMM_PR_Details.Id", "tblMM_PR_Master,tblMM_PR_Details", 
        # " tblMM_PR_Master.PRNo=tblMM_PR_Details.PRNo AND tblMM_PR_Master.Id=tblMM_PR_Details.MId 
        # AND tblMM_PR_Master.CompId='" + CompId + "' AND tblMM_PR_Details.PId='" + Pid + "'
        # AND tblMM_PR_Details.ItemId='" + Item + "' AND tblMM_PR_Master.WONo='" + Wono + "' 
        # AND tblMM_PR_Details.CId='" + Cid + "'");
        
        return PrDetail.objects.filter(
            pr_master__company=self.company, # Filter by company as in original
            pr_master__wo_no=self.wo_no,
            p_id=self.p_id,
            item_id=self.item_id,
            c_id=self.c_id
        ).exists()

    def get_action_visibility(self):
        """
        Determines the visibility of Edit, Save, Cancel, Select buttons and PR label
        based on supplier presence and PR details.
        """
        has_supplier = self.supplier is not None
        has_pr = self.has_pr_details()

        if has_supplier:
            if has_pr:
                # Supplier assigned AND PR details exist -> Cannot edit, show PR label
                return {'edit': False, 'pr_label': True, 'select': False}
            else:
                # Supplier assigned AND NO PR details -> Can edit
                return {'edit': True, 'pr_label': False, 'select': False}
        else:
            # No supplier assigned -> Can select (to add supplier/details on other page)
            return {'edit': False, 'pr_label': False, 'select': True}

```

### 4.2 Forms

**Task:** Define Django forms for user input (search and update).

**Instructions:**
- `MaterialPlanningEditForm` for the in-line update of `Supplier` and `Completion Date`.
- `MaterialPlanningSearchForm` to manage the search dropdown and text inputs.
- Widgets apply Tailwind CSS classes.

**File:** `material_planning/forms.py`

```python
from django import forms
from .models import MaterialPlanning, Supplier

class MaterialPlanningEditForm(forms.ModelForm):
    # Use CharField with a custom widget for supplier to allow autocomplete string,
    # then resolve to Supplier object in clean method.
    supplier_name_autocomplete = forms.CharField(
        label="Supplier Name",
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/material-planning/supplier-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-on:focus': 'showSuggestions = true',
            'x-on:blur.away': 'setTimeout(() => { showSuggestions = false }, 100)', # Hide after a delay
        })
    )
    # Hidden field to store actual supplier ID once selected
    supplier_id_hidden = forms.CharField(
        widget=forms.HiddenInput(attrs={'x-model': 'selectedSupplierId'})
    )

    completion_date = forms.DateField(
        label="Completion Date",
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input, offers built-in calendar
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'], # Allow multiple input formats
    )

    class Meta:
        model = MaterialPlanning
        fields = ['completion_date', 'supplier'] # supplier will be handled by custom fields
        # Exclude 'id', 'company', 'financial_year', 'pl_no', 'wo_no', etc. as they are not editable
        # 'supplier' is included but its widget is replaced by supplier_name_autocomplete

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize supplier_name_autocomplete from instance data
        if self.instance.pk and self.instance.supplier:
            self.fields['supplier_name_autocomplete'].initial = str(self.instance.supplier)
            self.fields['supplier_id_hidden'].initial = self.instance.supplier.id
        else:
             self.fields['supplier_id_hidden'].initial = '' # Ensure hidden field is empty if no supplier

        # Remove the default 'supplier' field widget if it's not a direct input
        self.fields['supplier'].widget = forms.HiddenInput() # Keep supplier field for model, but hide its widget

    def clean(self):
        cleaned_data = super().clean()
        supplier_id = cleaned_data.get('supplier_id_hidden')
        supplier_name = cleaned_data.get('supplier_name_autocomplete')
        
        # Validate supplier_id and supplier_name_autocomplete
        if not supplier_id:
            # If a name was entered but no ID selected, it's invalid
            if supplier_name:
                self.add_error('supplier_name_autocomplete', "Please select a valid supplier from the suggestions.")
            else:
                # If both are empty and supplier is required
                self.add_error('supplier_name_autocomplete', "Supplier Name is required.")
        else:
            try:
                # Assuming company_id is available from the request/session or instance
                # For this example, let's assume it's part of MaterialPlanning.instance.company.id
                # In a real app, you might pass company_id to form __init__
                company_id = self.instance.company.id if self.instance.company else None
                if not company_id:
                    self.add_error(None, "Company ID is missing for supplier validation.")
                
                # Check if the selected supplier ID actually exists for the current company
                supplier_obj = Supplier.objects.get(id=supplier_id, company__id=company_id)
                
                # Further validate if the autocomplete name matches the actual supplier name
                # (Optional, but good for data integrity if user types then changes their mind)
                if str(supplier_obj) != supplier_name:
                     self.add_error('supplier_name_autocomplete', "Selected supplier does not match typed name. Please re-select.")
                
                cleaned_data['supplier'] = supplier_obj # Assign the actual Supplier object
            except Supplier.DoesNotExist:
                self.add_error('supplier_name_autocomplete', "Invalid supplier selected.")
        
        return cleaned_data

class MaterialPlanningSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('0', 'Select'),
        ('1', 'Supplier Name'),
        ('2', 'PL No'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-model': 'searchField', # Alpine.js binding
            'hx-get': 'this.value == "1" ? "/material-planning/supplier-autocomplete-field/" : null', # Trigger for autocomplete field visibility
            'hx-target': '#search-input-container',
            'hx-swap': 'outerHTML', # Swap the container
        })
    )
    
    # These fields will be rendered conditionally by HTMX/Alpine
    search_text_plno = forms.CharField(
        label="PL No Search",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter PL No',
            'x-show': 'searchField == "2"', # Alpine.js binding
            'x-transition': '',
        })
    )
    
    supplier_search_name = forms.CharField(
        label="Supplier Name Search",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Supplier Name',
            'x-show': 'searchField == "1"', # Alpine.js binding
            'x-transition': '',
            'hx-get': '/material-planning/supplier-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#search-supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'x-on:focus': 'showSearchSuggestions = true',
            'x-on:blur.away': 'setTimeout(() => { showSearchSuggestions = false }, 100)',
        })
    )

```

### 4.3 Views

**Task:** Implement CRUD operations and dynamic content delivery using CBVs.

**Instructions:**
- Keep views thin (5-15 lines) by offloading complex logic to models or helper functions.
- `ListView` for the main page.
- `TablePartialView` for HTMX-driven table refreshes.
- `UpdateView` for inline editing.
- `SupplierAutocompleteView` for search and form autocomplete.
- `SelectView` as a placeholder for navigation.

**File:** `material_planning/views.py`

```python
from django.views.generic import ListView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.shortcuts import get_object_or_404
from .models import MaterialPlanning, Supplier, Company # Assuming Company model exists and CompId is available via session/user
from .forms import MaterialPlanningEditForm, MaterialPlanningSearchForm
from django.template.loader import render_to_string # For rendering partials

# Utility function to get current company and financial year IDs (mimics ASP.NET Session)
# In a real app, this would be from request.user, session, or a custom middleware.
def get_session_context(request):
    # Placeholder: Replace with actual logic to retrieve CompId and FinYearId
    # For demonstration, hardcode or get from dummy session if configured.
    # In a production app, this would be linked to user authentication/session management.
    current_company_id = int(request.session.get('compid', 1)) # Default to 1 for testing
    current_fin_year_id = int(request.session.get('finyear', 1)) # Default to 1 for testing
    return current_company_id, current_fin_year_id

class MaterialPlanningListView(ListView):
    """
    Displays the main Material Planning list page with search controls.
    This view only renders the initial page structure.
    The table content is loaded via HTMX from MaterialPlanningTablePartialView.
    """
    model = MaterialPlanning
    template_name = 'material_planning/list.html'
    context_object_name = 'material_planning_records'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form
        current_company_id, current_fin_year_id = get_session_context(self.request)
        context['search_form'] = MaterialPlanningSearchForm(self.request.GET)
        context['current_company_id'] = current_company_id
        context['current_fin_year_id'] = current_fin_year_id
        return context

class MaterialPlanningTablePartialView(ListView):
    """
    Returns the DataTables table content via HTMX, handling search and pagination.
    """
    model = MaterialPlanning
    template_name = 'material_planning/_material_planning_table.html'
    context_object_name = 'material_planning_records'
    paginate_by = 15 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        queryset = super().get_queryset()
        current_company_id, current_fin_year_id = get_session_context(self.request)

        # Apply common filters from ASP.NET code
        queryset = queryset.filter(
            company__id=current_company_id,
            financial_year__id__lte=current_fin_year_id # FinYearId<=, as per ASP.NET
        ).select_related('supplier', 'financial_year', 'company') # Optimize lookups

        # Apply search filters from request parameters
        search_field = self.request.GET.get('search_field', '0')
        search_text_plno = self.request.GET.get('search_text_plno')
        supplier_search_name = self.request.GET.get('supplier_search_name')

        if search_field == '2' and search_text_plno: # PL No search
            queryset = queryset.filter(pl_no__icontains=search_text_plno) # Use icontains for partial match
        elif search_field == '1' and supplier_search_name: # Supplier Name search
            # ASP.NET uses fun.getCode(txtCustName.Text) which likely gets SupplierId from name.
            # Here, we search by supplier name directly or by ID if it's in the format "Name [ID]".
            # Let's support searching by full string including ID or just name.
            if '[' in supplier_search_name and ']' in supplier_search_name:
                # If format is "Name [ID]", extract ID and search by that
                try:
                    supplier_id_from_search = supplier_search_name.split('[')[-1].strip(']')
                    queryset = queryset.filter(supplier__id=supplier_id_from_search)
                except IndexError:
                    queryset = queryset.filter(supplier__supplier_name__icontains=supplier_search_name)
            else:
                queryset = queryset.filter(supplier__supplier_name__icontains=supplier_search_name)
        
        return queryset.order_by('-id') # Match ASP.NET Order By Id Desc

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        current_company_id, _ = get_session_context(self.request)
        context['current_company_id'] = current_company_id
        return context

class MaterialPlanningUpdateView(UpdateView):
    """
    Handles inline editing for Material Planning records.
    Renders the form via HTMX GET, processes form submission via HTMX POST.
    """
    model = MaterialPlanning
    form_class = MaterialPlanningEditForm
    template_name = 'material_planning/_material_planning_form.html'
    
    # success_url is not strictly needed for HTMX, as we return 204 with trigger
    # but good practice for non-HTMX requests or as a fallback.
    success_url = reverse_lazy('material_planning_list') 

    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        current_company_id, _ = get_session_context(self.request)
        return get_object_or_404(
            MaterialPlanning.objects.filter(company__id=current_company_id), 
            pk=pk
        )

    def form_valid(self, form):
        # The form's clean method handles populating the 'supplier' field
        response = super().form_valid(form)
        messages.success(self.request, 'Material Planning record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX not to swap anything
                headers={
                    'HX-Trigger': 'refreshMaterialPlanningList' # Custom event to refresh the table
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If invalid, re-render the form with errors in place
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=400 # Indicate bad request due to validation errors
            )
        return response

class MaterialPlanningSelectView(View):
    """
    Handles the "Select" command from the GridView.
    Mimics the ASP.NET Response.Redirect for a detail page.
    """
    def get(self, request, pk, *args, **kwargs):
        # In a real app, you would pass these IDs to a Django URL
        # For demonstration, we'll just log and redirect.
        material_planning_record = get_object_or_404(MaterialPlanning, pk=pk)
        messages.info(request, f"Selected Material Planning: PLNo={material_planning_record.pl_no}, MId={material_planning_record.id}. Redirecting to details.")
        
        # Replace with your actual Django URL for the detail page
        # Example: return redirect(reverse('material_planning_detail', args=[pk]))
        # Assuming the original URL structure is like /planning_edit_details/<plno>/<mid>/
        # For now, a simple redirect or log.
        return HttpResponse(
            status=204, # No content
            headers={
                'HX-Redirect': reverse_lazy('material_planning_detail', kwargs={'pk': pk}) # Example redirect to a Django detail view
                # 'HX-Redirect': '/material-planning/details/?plno={}&MId={}&ModId=4&SubModId=33'.format(
                #    material_planning_record.pl_no, material_planning_record.id) # Mimic original redirect if needed
            }
        )

# Placeholder for Material Planning Detail view
class MaterialPlanningDetailView(View):
    def get(self, request, pk):
        record = get_object_or_404(MaterialPlanning, pk=pk)
        return HttpResponse(f"<h1>Material Planning Details for PL No: {record.pl_no} (ID: {record.id})</h1><p>This is a placeholder for the actual detail page.</p>")


class SupplierAutocompleteView(View):
    """
    Provides JSON responses for supplier auto-completion.
    Mimics the ASP.NET GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        current_company_id, _ = get_session_context(request)
        
        if not prefix_text:
            return JsonResponse([], safe=False)

        # Filter suppliers by name starting with prefix_text and by company
        # Also include exact matches if any to prioritize
        suppliers = Supplier.objects.filter(
            company__id=current_company_id
        ).filter(
            Q(supplier_name__istartswith=prefix_text) | Q(supplier_name__icontains=prefix_text)
        ).order_by('supplier_name')[:10] # Limit to 10 suggestions

        # Format as "SupplierName [SupplierId]"
        suggestions = [str(s) for s in suppliers]
        
        return JsonResponse(suggestions, safe=False)

```

### 4.4 Templates

**Task:** Create templates for each view, ensuring HTMX and Alpine.js integration.

**Instructions:**
- Extend `core/base.html`.
- Use HTMX for dynamic content loading and form submissions.
- Use Alpine.js for local UI state (e.g., toggling search fields, modal visibility).
- DataTables for the main list view.

**File:** `material_planning/templates/material_planning/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Planning - Edit</h2>
    </div>
    
    <!-- Search and Filter Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8" x-data="{ searchField: '{{ search_form.search_field.value|default:'0' }}' }">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Filter Records</h3>
        <form id="searchForm" hx-get="{% url 'material_planning_table' %}" hx-target="#materialPlanningTable-container" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_field }}
                </div>
                <div id="search-input-container" class="relative">
                    <template x-if="searchField === '2'">
                        <div>
                            <label for="{{ search_form.search_text_plno.id_for_label }}" class="block text-sm font-medium text-gray-700">PL No</label>
                            {{ search_form.search_text_plno }}
                        </div>
                    </template>
                    <template x-if="searchField === '1'">
                        <div x-data="{ showSearchSuggestions: false, selectedSearchSupplierId: '' }">
                            <label for="{{ search_form.supplier_search_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier Name</label>
                            {{ search_form.supplier_search_name }}
                            <div id="search-supplier-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto" x-show="showSearchSuggestions && $el.children.length > 0">
                                <!-- Suggestions loaded via HTMX -->
                            </div>
                        </div>
                    </template>
                </div>
                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Material Planning Table Container (HTMX loaded) -->
    <div id="materialPlanningTable-container"
         hx-trigger="load, refreshMaterialPlanningList from:body"
         hx-get="{% url 'material_planning_table' %}"
         hx-indicator="#table-loading-indicator"
         hx-params="search_field,search_text_plno,supplier_search_name"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial loading state -->
        <div id="table-loading-indicator" class="flex justify-center items-center h-48" hx-indicator="on">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Material Planning records...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery for DataTables -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Include DataTables -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.css">
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('materialPlanningPage', () => ({
            searchField: '{{ search_form.search_field.value|default:'0' }}',
            // Add other data properties here if needed for broader page state
        }));

        // Handle autocomplete suggestion selection for search input
        document.body.addEventListener('click', function(event) {
            if (event.target.closest('#search-supplier-suggestions')) {
                const suggestionItem = event.target.closest('[data-supplier-value]');
                if (suggestionItem) {
                    const value = suggestionItem.getAttribute('data-supplier-value');
                    const input = document.getElementById('{{ search_form.supplier_search_name.id_for_label }}');
                    if (input) {
                        input.value = value;
                        input.dispatchEvent(new Event('change')); // Trigger HTMX change event
                        showSearchSuggestions = false; // Hide suggestions
                    }
                }
            }
        });
    });

    // Event listener for HTMX after swap to re-initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'materialPlanningTable-container') {
            $('#materialPlanningTable').DataTable({
                "pageLength": 15, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "pagingType": "full_numbers", // More complete pagination controls
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>', // Custom DOM for styling
            });
            // Re-bind Alpine.js if new content is swapped in that needs it
            if (window.Alpine) {
                window.Alpine.discoverUninitializedComponents(function (el) {
                    window.Alpine.initializeComponent(el);
                });
            }
        }
        // Handle modal opening/closing after HTMX requests
        if (evt.detail.target.id === 'modalContent' && evt.detail.xhr.status === 200) {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden');
        }
    });

    // Close modal on 204 status (success with no content)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204 || evt.detail.xhr.status === 302) { // 302 for HX-Redirect
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
                modal.classList.add('hidden');
            }
        }
    });
</script>
{% endblock %}
```

**File:** `material_planning/templates/material_planning/_material_planning_table.html` (Partial for HTMX)

```html
<table id="materialPlanningTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL No</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comp Date</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in material_planning_records %}
        {% with actions=obj.get_action_visibility %}
        <tr id="material-planning-{{ obj.id }}">
            <td class="py-4 px-6 whitespace-nowrap text-sm text-right text-gray-500">{{ forloop.counter|add:page_obj.start_index|add:-1 }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-center text-gray-900">{{ obj.financial_year.financial_year }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-center text-gray-900">{{ obj.pl_no }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-center text-gray-900">{{ obj.wo_no }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-left text-gray-900">{{ obj.supplier|default:"N/A" }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-center text-gray-900">{{ obj.completion_date|date:"d-m-Y"|default:"N/A" }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-center">
                {% if actions.select %}
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs"
                    hx-get="{% url 'material_planning_select' obj.pk %}"
                    hx-trigger="click"
                    hx-swap="none"
                    _="on htmx:afterRequest if event.detail.xhr.status == 204 and event.detail.xhr.headers['HX-Redirect'] then window.location.href = event.detail.xhr.headers['HX-Redirect']"
                >
                    Select
                </button>
                {% elif actions.edit %}
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs mr-2"
                    hx-get="{% url 'material_planning_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal"
                >
                    Edit
                </button>
                {% else %}
                <span class="text-red-600 font-semibold text-xs">PR Exists</span>
                {% endif %}
            </td>
        </tr>
        {% endwith %}
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-6 text-center text-sm text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- HTMX will handle DataTables initialization in the parent list.html via htmx:afterSwap -->
```

**File:** `material_planning/templates/material_planning/_material_planning_form.html` (Partial for HTMX modal)

```html
<div class="p-6" x-data="{ showSuggestions: false, selectedSupplierId: '{{ form.instance.supplier.id|default:'' }}' }">
    <h3 class="text-xl font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Planning Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent" hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.supplier_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.supplier_name_autocomplete.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.supplier_name_autocomplete }}
                <input type="hidden" name="supplier_id_hidden" x-model="selectedSupplierId">
                {% if form.supplier_name_autocomplete.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.supplier_name_autocomplete.errors }}</p>
                {% endif %}
                <div id="supplier-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto" x-show="showSuggestions && $el.children.length > 0" x-transition.opacity.duration.300>
                    <!-- Suggestions loaded via HTMX -->
                </div>
            </div>
            
            <div class="mb-4">
                <label for="{{ form.completion_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.completion_date.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.completion_date }}
                {% if form.completion_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.completion_date.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
         <div id="form-loading-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-1 text-sm text-gray-600">Saving...</p>
        </div>
    </form>
</div>

<script>
    // Handle autocomplete suggestion selection for form input
    document.body.addEventListener('click', function(event) {
        if (event.target.closest('#supplier-suggestions')) {
            const suggestionItem = event.target.closest('[data-supplier-value]');
            if (suggestionItem) {
                const value = suggestionItem.getAttribute('data-supplier-value');
                const id = suggestionItem.getAttribute('data-supplier-id');
                const input = document.getElementById('{{ form.supplier_name_autocomplete.id_for_label }}');
                if (input) {
                    input.value = value;
                    input.dispatchEvent(new Event('input')); // Trigger Alpine.js x-model
                    document.querySelector('[name="supplier_id_hidden"]').value = id; // Set hidden ID field
                    showSuggestions = false; // Hide suggestions
                }
            }
        }
    });
</script>
```

**File:** `material_planning/templates/material_planning/_supplier_autocomplete_suggestions.html` (Partial for HTMX autocomplete)

```html
{% if suggestions %}
    {% for suggestion in suggestions %}
        <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
             data-supplier-value="{{ suggestion }}"
             data-supplier-id="{{ suggestion.split('[')|last|replace:']','' }}">
            {{ suggestion }}
        </div>
    {% endfor %}
{% else %}
    <div class="px-4 py-2 text-gray-500 text-sm">No suggestions</div>
{% endif %}
```
*Note: `replace:']',''` is a custom template filter. You'd need to register it, or parse `suggestion.split('[')[-1].strip(']')` in JS directly.* I'll use `split('|')` to be safer, assuming `Supplier` model's `__str__` would be `f"{self.supplier_name}|{self.id}"` for simpler parsing. Let's adjust `Supplier.__str__` to this format.

**Revised `Supplier.__str__`:**
```python
class Supplier(models.Model):
    # ...
    def __str__(self):
        return f"{self.supplier_name}|{self.id}" # Changed for easier JS parsing
```
**Revised `_supplier_autocomplete_suggestions.html`:**
```html
{% if suggestions %}
    {% for suggestion in suggestions %}
        {% with parts=suggestion.split:'|' %}
        <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
             data-supplier-value="{{ parts.0 }} [{{ parts.1 }}]"
             data-supplier-id="{{ parts.1 }}">
            {{ parts.0 }} [{{ parts.1 }}]
        </div>
        {% endwith %}
    {% endfor %}
{% else %}
    <div class="px-4 py-2 text-gray-500 text-sm">No suggestions</div>
{% endif %}
```
**Revised `SupplierAutocompleteView`:**
```python
class SupplierAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        current_company_id, _ = get_session_context(request)
        
        if not prefix_text:
            return JsonResponse([], safe=False)

        suppliers = Supplier.objects.filter(
            company__id=current_company_id
        ).filter(
            Q(supplier_name__istartswith=prefix_text) | Q(id__istartswith=prefix_text) # Also search by ID
        ).order_by('supplier_name')[:10]

        # Format as "SupplierName|SupplierId" for easier JS parsing
        suggestions = [f"{s.supplier_name}|{s.id}" for s in suppliers] 
        
        # Determine if it's for search input or form field based on 'hx-target' or other header
        # This is a simplification; a single view serves both.
        # It needs to return HTML, not JSON, for HTMX to swap into the suggestions div.
        context = {'suggestions': suggestions}
        return render(request, 'material_planning/_supplier_autocomplete_suggestions.html', context)
```
*Note: The auto-completion view previously returned JSON. For HTMX to swap directly into a `div`, it needs to return HTML. I've updated the `SupplierAutocompleteView` and its template to reflect this.*

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
- Create paths for the main list, the HTMX-loaded table, update forms, and the supplier autocomplete endpoint.
- Use consistent naming.

**File:** `material_planning/urls.py`

```python
from django.urls import path
from .views import (
    MaterialPlanningListView,
    MaterialPlanningTablePartialView,
    MaterialPlanningUpdateView,
    MaterialPlanningSelectView,
    MaterialPlanningDetailView, # Placeholder for redirect target
    SupplierAutocompleteView,
)

urlpatterns = [
    path('material-planning/', MaterialPlanningListView.as_view(), name='material_planning_list'),
    path('material-planning/table/', MaterialPlanningTablePartialView.as_view(), name='material_planning_table'),
    path('material-planning/edit/<int:pk>/', MaterialPlanningUpdateView.as_view(), name='material_planning_edit'),
    path('material-planning/select/<int:pk>/', MaterialPlanningSelectView.as_view(), name='material_planning_select'),
    path('material-planning/details/<int:pk>/', MaterialPlanningDetailView.as_view(), name='material_planning_detail'), # Target for 'Select'
    path('material-planning/supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
]

```

### 4.6 Tests

**Task:** Write tests for the models and views, ensuring comprehensive coverage.

**Instructions:**
- Unit tests for all model methods (`get_action_visibility`, `has_pr_details`).
- Integration tests for `ListView`, `TablePartialView`, `UpdateView`, `SelectView`, and `SupplierAutocompleteView`, covering HTMX interactions.

**File:** `material_planning/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, time
from .models import Company, FinancialYear, Supplier, MaterialPlanning, PrMaster, PrDetail
from django.contrib.messages import get_messages

# Assume Company and FinancialYear 1 exist for testing purposes
class BaseTestCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.financial_year = FinancialYear.objects.create(id=1, financial_year='2023-2024', company=cls.company)
        cls.supplier1 = Supplier.objects.create(id='SUP001', supplier_name='Supplier Alpha', company=cls.company)
        cls.supplier2 = Supplier.objects.create(id='SUP002', supplier_name='Supplier Beta', company=cls.company)
        
        # Create a MaterialPlanning record with a supplier
        cls.mp_record_with_supplier = MaterialPlanning.objects.create(
            id=1, company=cls.company, financial_year=cls.financial_year,
            pl_no='PL001', wo_no='WO001', supplier=cls.supplier1, 
            completion_date=date(2024, 6, 1), system_date=date.today(), system_time=time(12, 0, 0),
            p_id='P1', c_id='C1', item_id='I1'
        )
        # Create a MaterialPlanning record without a supplier
        cls.mp_record_no_supplier = MaterialPlanning.objects.create(
            id=2, company=cls.company, financial_year=cls.financial_year,
            pl_no='PL002', wo_no='WO002', supplier=None, 
            completion_date=None, system_date=date.today(), system_time=time(12, 0, 0),
            p_id='P2', c_id='C2', item_id='I2'
        )
        # Create PR details to test has_pr_details logic
        cls.pr_master = PrMaster.objects.create(id=1, pr_no='PRM001', wo_no='WO001', company=cls.company)
        cls.pr_detail = PrDetail.objects.create(id=1, pr_master=cls.pr_master, pr_no='PRM001', p_id='P1', c_id='C1', item_id='I1')

        # Record with existing PR to test has_pr_details=True
        cls.mp_record_with_pr = MaterialPlanning.objects.create(
            id=3, company=cls.company, financial_year=cls.financial_year,
            pl_no='PRPL001', wo_no='WO001', supplier=cls.supplier2, 
            completion_date=date(2024, 7, 1), system_date=date.today(), system_time=time(12, 0, 0),
            p_id='P1', c_id='C1', item_id='I1'
        )

class MaterialPlanningModelTest(BaseTestCase):
    def test_material_planning_creation(self):
        self.assertEqual(self.mp_record_with_supplier.pl_no, 'PL001')
        self.assertEqual(self.mp_record_with_supplier.supplier.supplier_name, 'Supplier Alpha')
        self.assertIsNotNone(self.mp_record_with_supplier.system_date)
        self.assertIsNotNone(self.mp_record_with_supplier.system_time)
    
    def test_has_pr_details_true(self):
        # This record (id=3) should match the created PrDetail based on WO, PId, CId, ItemId
        self.assertTrue(self.mp_record_with_pr.has_pr_details())

    def test_has_pr_details_false(self):
        # This record (id=2) should NOT match any PrDetail
        self.assertFalse(self.mp_record_no_supplier.has_pr_details())

    def test_get_action_visibility_edit(self):
        # mp_record_with_supplier (id=1) has supplier, but no matching PR details
        actions = self.mp_record_with_supplier.get_action_visibility()
        self.assertTrue(actions['edit'])
        self.assertFalse(actions['pr_label'])
        self.assertFalse(actions['select'])

    def test_get_action_visibility_select(self):
        # mp_record_no_supplier (id=2) has no supplier
        actions = self.mp_record_no_supplier.get_action_visibility()
        self.assertFalse(actions['edit'])
        self.assertFalse(actions['pr_label'])
        self.assertTrue(actions['select'])

    def test_get_action_visibility_pr_label(self):
        # mp_record_with_pr (id=3) has supplier AND matching PR details
        actions = self.mp_record_with_pr.get_action_visibility()
        self.assertFalse(actions['edit'])
        self.assertTrue(actions['pr_label'])
        self.assertFalse(actions['select'])

class MaterialPlanningViewsTest(BaseTestCase):
    def setUp(self):
        self.client = Client()
        # Mock session variables for company and financial year
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.financial_year.id
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('material_planning_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/list.html')
        self.assertContains(response, 'Material Planning - Edit') # Check for title
        self.assertContains(response, 'id="materialPlanningTable-container"') # Check for HTMX container

    def test_table_partial_view_no_filters(self):
        response = self.client.get(reverse('material_planning_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/_material_planning_table.html')
        self.assertContains(response, 'PL001') # Check for records
        self.assertContains(response, 'PL002')
        self.assertContains(response, 'PL001') # From mp_record_with_pr
        self.assertEqual(len(response.context['material_planning_records']), 3)

    def test_table_partial_view_search_pl_no(self):
        response = self.client.get(reverse('material_planning_table'), {'search_field': '2', 'search_text_plno': 'PL001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PL001')
        self.assertNotContains(response, 'PL002')
        self.assertEqual(len(response.context['material_planning_records']), 2) # PL001 and PRPL001

    def test_table_partial_view_search_supplier_name(self):
        response = self.client.get(reverse('material_planning_table'), {'search_field': '1', 'supplier_search_name': 'Alpha'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Supplier Alpha')
        self.assertNotContains(response, 'Supplier Beta')
        self.assertEqual(len(response.context['material_planning_records']), 1)

    def test_update_view_get(self):
        response = self.client.get(reverse('material_planning_edit', args=[self.mp_record_with_supplier.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/_material_planning_form.html')
        self.assertContains(response, 'Edit Material Planning Record')
        self.assertContains(response, self.mp_record_with_supplier.supplier.supplier_name) # Check initial form values

    def test_update_view_post_success(self):
        new_supplier_name = 'Supplier Beta'
        new_supplier_id = 'SUP002' # This comes from the hidden field
        new_comp_date = '2024-08-15'

        data = {
            'supplier_name_autocomplete': f'{new_supplier_name} [{new_supplier_id}]',
            'supplier_id_hidden': new_supplier_id,
            'completion_date': new_comp_date,
        }
        response = self.client.post(reverse('material_planning_edit', args=[self.mp_record_with_supplier.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialPlanningList')

        updated_record = MaterialPlanning.objects.get(id=self.mp_record_with_supplier.id)
        self.assertEqual(updated_record.supplier.id, new_supplier_id)
        self.assertEqual(updated_record.completion_date, date(2024, 8, 15))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Material Planning record updated successfully.')

    def test_update_view_post_invalid_data(self):
        data = {
            'supplier_name_autocomplete': '', # Missing required field
            'supplier_id_hidden': '',
            'completion_date': 'invalid-date', # Invalid date format
        }
        response = self.client.post(reverse('material_planning_edit', args=[self.mp_record_with_supplier.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 400) # HTMX validation failure
        self.assertContains(response, 'Supplier Name is required.')
        self.assertContains(response, 'Enter a valid date.')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'prefixText': 'Supp'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/_supplier_autocomplete_suggestions.html')
        self.assertContains(response, 'Supplier Alpha|SUP001')
        self.assertContains(response, 'Supplier Beta|SUP002')
        self.assertNotContains(response, 'No suggestions')

    def test_supplier_autocomplete_view_no_match(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'prefixText': 'XYZ'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/_supplier_autocomplete_suggestions.html')
        self.assertContains(response, 'No suggestions')

    def test_select_view(self):
        response = self.client.get(reverse('material_planning_select', args=[self.mp_record_with_supplier.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('material_planning_detail', args=[self.mp_record_with_supplier.id]))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), f"Selected Material Planning: PLNo={self.mp_record_with_supplier.pl_no}, MId={self.mp_record_with_supplier.id}. Redirecting to details.")
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The provided code samples demonstrate robust HTMX and Alpine.js integration:
- **HTMX for Table Reloads:** The main list page (`list.html`) uses `hx-get` on `materialPlanningTable-container` to fetch the table content. It's triggered on `load`, and `refreshMaterialPlanningList` from the body (after a successful form submission), and on `submit` of the search form. This ensures the table dynamically updates without full page reloads.
- **HTMX for Modal Forms:** "Edit" buttons use `hx-get` to load the edit form (`_material_planning_form.html`) into a modal container (`#modalContent`). Form submissions use `hx-post` with `hx-swap="none"` and a `HX-Trigger` to signal the main list to refresh. Invalid form submissions re-render the form with errors (status 400).
- **HTMX for Autocomplete:** Supplier autocomplete inputs (`supplier_name_autocomplete`, `supplier_search_name`) use `hx-get` to `supplier_autocomplete` URL, triggering on `keyup changed delay:500ms`. The response (`_supplier_autocomplete_suggestions.html`) is swapped into a suggestions `div`.
- **Alpine.js for UI State:**
    - `x-data="{ searchField: ... }"` on the search form container toggles the visibility of the `PL No` or `Supplier Name` search input fields using `x-show`.
    - `x-show` and `x-on:focus`/`x-on:blur.away` are used for managing the visibility of autocomplete suggestion lists.
    - `x-on:click` handlers on `button` elements are used to add/remove the `is-active` class on the modal for visual display.
    - `x-model` is used to bind form fields to Alpine.js data properties (e.g., `selectedSupplierId`) for client-side state management.
- **DataTables:** Initialized on `htmx:afterSwap` event for the `materialPlanningTable-container`. This ensures DataTables correctly applies to the dynamically loaded content, providing client-side search, sort, and pagination. `destroy: true` is crucial for re-initialization.
- **No Additional JavaScript:** All dynamic interactions are handled by HTMX and Alpine.js, eliminating the need for custom, imperative JavaScript as per the requirements.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Material Planning - Edit module to Django. By adhering to the "fat model, thin view" principle, leveraging modern frontend technologies like HTMX and Alpine.js, and integrating robust testing, the resulting Django application will be:

*   **More Maintainable:** Business logic is centralized in models, making it easier to understand, test, and update.
*   **Highly Performant:** HTMX minimizes network requests and page reloads, providing a snappier user experience.
*   **Scalable:** Django's architecture and ORM are designed for growth, handling increasing data volumes and user loads.
*   **Testable:** Comprehensive unit and integration tests ensure code quality and reduce regression risks.
*   **Developer-Friendly:** Python and Django offer a modern, efficient development environment.

This detailed plan, broken down into specific Django application files with runnable code, serves as an excellent foundation for an AI-assisted automation process to systematically convert ASP.NET Web Forms applications. The plain English explanations ensure that non-technical stakeholders can understand the value and process of modernization.