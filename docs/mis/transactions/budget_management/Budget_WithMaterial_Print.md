This comprehensive modernization plan will transition the ASP.NET Crystal Reports page for "Budget With Material Print" to a modern Django-based solution. Our approach focuses on re-architecting the application to adhere to Django 5.0+ best practices, utilizing a fat model/thin view structure, and leveraging HTMX + Alpine.js for dynamic frontend interactions, ensuring a smooth, responsive user experience without heavy JavaScript frameworks. The core tables involved, `AccHead` and `tblACC_Budget_Transactions`, will be managed with full CRUD capabilities.

The original ASP.NET page was solely a report viewer. In our Django modernization, we'll provide the underlying data management (CRUD for Account Heads and Budget Transactions) and then integrate a specific "print/report" view that mimics the original page's data display.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Analyzing the SQL query from the ASP.NET code-behind:

`Select tblACC_Budget_Transactions.Id, REPLACE(CONVERT (varchar, CONVERT (datetime, SUBSTRING(tblACC_Budget_Transactions.SysDate , CHARINDEX('-',tblACC_Budget_Transactions.SysDate ) + 1, 2) + '-' + LEFT (tblACC_Budget_Transactions.SysDate , CHARINDEX('-', tblACC_Budget_Transactions.SysDate ) - 1) + '-' + RIGHT (tblACC_Budget_Transactions.SysDate , CHARINDEX('-', REVERSE(tblACC_Budget_Transactions.SysDate )) - 1)), 103), '/', '-') AS SysDate,tblACC_Budget_Transactions.SysTime,tblACC_Budget_Transactions.BudgetCode, AccHead.Description+'-'+ AccHead.Symbol AS Description,tblACC_Budget_Transactions.Amount from AccHead ,tblACC_Budget_Transactions where tblACC_Budget_Transactions.BudgetCode=AccHead.Id and AccHead.Id='` + c + `'`

We identify two key database tables:

1.  **`tblACC_Budget_Transactions`**
    *   **Purpose:** Stores individual budget transaction records.
    *   **Columns:**
        *   `Id`: Unique identifier for each transaction (Primary Key).
        *   `SysDate`: Date of the transaction (stored as `DD-MM-YYYY` string).
        *   `SysTime`: Time of the transaction (stored as `HH:MM:SS` string).
        *   `BudgetCode`: Foreign key linking to `AccHead.Id`, indicating the associated account head.
        *   `Amount`: The monetary value of the transaction.

2.  **`AccHead`**
    *   **Purpose:** Stores definitions for various account heads or budget categories.
    *   **Columns:**
        *   `Id`: Unique identifier for each account head (Primary Key).
        *   `Description`: A descriptive name for the account head.
        *   `Symbol`: A short symbol or code for the account head.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET `.aspx` and `.cs` files indicate a **Read** (or **Retrieve**) functionality:
*   **Read:** The primary function is to fetch specific budget transaction data and its associated account head details based on an `Id` passed via the URL query string. This data is then used to generate and display a Crystal Report. The code explicitly retrieves a single record by indexing `ds.Tables[0].Rows[0]`, implying that for reporting purposes, it expects or only utilizes one relevant budget transaction per account head ID.

**Inferred Functionality for Modernization:** While the ASP.NET code only shows a "read for report" operation, a complete modern application would require full data management. Therefore, for the Django solution, we will implement full **Create, Read (List and Detail), Update, and Delete (CRUD)** operations for both `AccountHead` and `BudgetTransaction` entities, along with a specialized "Print View" for `BudgetTransaction` that mirrors the original report's intent.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET page is focused on displaying a report, so its UI components are minimal:
*   **`CR:CrystalReportViewer`**: This control is used to render and display the Crystal Report output directly on the web page. Its role is purely presentational, showing the prepared report.
*   **`CR:CrystalReportSource`**: This control defines the path to the `.rpt` file and acts as the data source for the `CrystalReportViewer`.

There are no interactive UI elements for user input or data manipulation (like text boxes, buttons for adding/editing records, or data grids for lists) on *this specific page*. The data for the report is determined by a query string parameter, not by user interaction on the page itself.

For the Django migration, these presentation components will be replaced by:
*   **HTML Templates:** Custom Django templates will display the data in a user-friendly and printable format, replacing the Crystal Report.
*   **DataTables:** For list views of `AccountHead` and `BudgetTransaction`, DataTables will provide client-side sorting, searching, and pagination.
*   **HTMX and Alpine.js:** These will handle all dynamic interactions, such as opening forms in modals for Add/Edit/Delete operations without full page reloads.

## Step 4: Generate Django Code

We will structure the Django application as an app named `accounts`.

### 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

The `models.py` file will define the structure of our data, mapping directly to the existing database tables. We'll add methods for business logic (fat model approach) and data conversion specific to the legacy `SysDate` and `SysTime` string fields.

```python
# accounts/models.py
from django.db import models
from datetime import datetime, date, time

class AccountHead(models.Model):
    # Django automatically creates an 'id' primary key field.
    # If the legacy database column is explicitly named 'Id' and is a non-auto-incrementing int,
    # you might need to specify: id = models.IntegerField(db_column='Id', primary_key=True)
    # However, for most legacy systems, Django's default 'id' PK maps perfectly.
    description = models.CharField(db_column='Description', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False  # Important: Tells Django not to manage table creation/deletion
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        """Returns a user-friendly string representation of the account head."""
        return f"{self.description} - {self.symbol}"

    def get_full_description(self):
        """Combines description and symbol, mirroring original report output."""
        return f"{self.description}-{self.symbol}"

class BudgetTransaction(models.Model):
    # Django's default 'id' primary key field will be used.
    sys_date_str = models.CharField(db_column='SysDate', max_length=10, help_text="Date in DD-MM-YYYY format.")
    sys_time_str = models.CharField(db_column='SysTime', max_length=8, help_text="Time in HH:MM:SS format.")
    
    # Foreign key to AccountHead using the legacy 'BudgetCode' column name.
    # on_delete=models.DO_NOTHING is used to reflect legacy database behavior
    # where referential integrity might not be strictly enforced by the ORM,
    # or if deleting an AccountHead should not cascade delete transactions.
    budget_code = models.ForeignKey(
        AccountHead,
        on_delete=models.DO_NOTHING,
        db_column='BudgetCode',
        related_name='budget_transactions',
        verbose_name='Account Head'
    )
    
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblACC_Budget_Transactions'
        verbose_name = 'Budget Transaction'
        verbose_name_plural = 'Budget Transactions'

    def __str__(self):
        """Returns a user-friendly string representation of the transaction."""
        return f"Transaction {self.pk} for {self.budget_code.description} on {self.sys_date_str}"

    def get_parsed_sys_date(self) -> date | None:
        """Parses the SysDate string (DD-MM-YYYY) into a date object."""
        try:
            return datetime.strptime(self.sys_date_str, '%d-%m-%Y').date()
        except (ValueError, TypeError):
            # Log error or handle gracefully if date format is inconsistent
            return None

    def get_parsed_sys_time(self) -> time | None:
        """Parses the SysTime string (HH:MM:SS) into a time object."""
        try:
            return datetime.strptime(self.sys_time_str, '%H:%M:%S').time()
        except (ValueError, TypeError):
            # Log error or handle gracefully
            return None

    def get_formatted_sys_date_for_report(self) -> str:
        """Returns date formatted as DD-MM-YYYY for report display."""
        parsed_date = self.get_parsed_sys_date()
        return parsed_date.strftime('%d-%m-%Y') if parsed_date else 'N/A'

    @classmethod
    def get_transaction_for_report(cls, acc_head_id: int):
        """
        Retrieves a single BudgetTransaction related to a specific AccountHead ID for reporting.
        This mimics the original ASP.NET behavior of taking the first found record.
        In a real scenario, you might add logic to select the latest or a specific transaction.
        """
        return cls.objects.filter(budget_code__id=acc_head_id).first()

```

### 4.2 Forms

Task: Define Django forms for user input.

## Instructions:

These forms will handle data input and validation for `AccountHead` and `BudgetTransaction` records. They are built using Django's `ModelForm` and styled with Tailwind CSS classes.

```python
# accounts/forms.py
from django import forms
from .models import AccountHead, BudgetTransaction
from datetime import datetime

class AccountHeadForm(forms.ModelForm):
    class Meta:
        model = AccountHead
        fields = ['description', 'symbol']
        widgets = {
            'description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'e.g., Office Supplies'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'e.g., OFF-SUP'}),
        }
        
    def clean_description(self):
        description = self.cleaned_data['description']
        if not description.strip():
            raise forms.ValidationError("Description cannot be empty.")
        return description.strip()

    def clean_symbol(self):
        symbol = self.cleaned_data['symbol']
        if not symbol.strip():
            raise forms.ValidationError("Symbol cannot be empty.")
        return symbol.strip().upper() # Convert to uppercase for consistency

class BudgetTransactionForm(forms.ModelForm):
    class Meta:
        model = BudgetTransaction
        fields = ['sys_date_str', 'sys_time_str', 'budget_code', 'amount']
        widgets = {
            'sys_date_str': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'}),
            'sys_time_str': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'HH:MM:SS'}),
            'budget_code': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'step': '0.01'}),
        }
        
    def clean_sys_date_str(self):
        sys_date_str = self.cleaned_data['sys_date_str']
        try:
            datetime.strptime(sys_date_str, '%d-%m-%Y')
        except ValueError:
            raise forms.ValidationError("Date must be in DD-MM-YYYY format.")
        return sys_date_str

    def clean_sys_time_str(self):
        sys_time_str = self.cleaned_data['sys_time_str']
        try:
            datetime.strptime(sys_time_str, '%H:%M:%S')
        except ValueError:
            raise forms.ValidationError("Time must be in HH:MM:SS format.")
        return sys_time_str

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive value.")
        return amount

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We will define standard CRUD views for `AccountHead` and `BudgetTransaction`, along with a specific `BudgetTransactionPrintView` that replaces the Crystal Report functionality. Views are kept thin (5-15 lines) by offloading business logic to models.

```python
# accounts/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import AccountHead, BudgetTransaction
from .forms import AccountHeadForm, BudgetTransactionForm

# --- AccountHead Views ---
class AccountHeadListView(ListView):
    model = AccountHead
    template_name = 'accounts/accounthead/list.html'
    context_object_name = 'account_heads'

class AccountHeadCreateView(CreateView):
    model = AccountHead
    form_class = AccountHeadForm
    template_name = 'accounts/accounthead/form.html'
    success_url = reverse_lazy('account_head_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Account Head added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a no-content response with a trigger header
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshAccountHeadList'})
        return response

class AccountHeadUpdateView(UpdateView):
    model = AccountHead
    form_class = AccountHeadForm
    template_name = 'accounts/accounthead/form.html'
    success_url = reverse_lazy('account_head_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Account Head updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshAccountHeadList'})
        return response

class AccountHeadDeleteView(DeleteView):
    model = AccountHead
    template_name = 'accounts/accounthead/confirm_delete.html'
    success_url = reverse_lazy('account_head_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Account Head deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshAccountHeadList'})
        return response

# Partial view for AccountHead list table (for HTMX refresh)
class AccountHeadTablePartialView(ListView):
    model = AccountHead
    template_name = 'accounts/accounthead/_accounthead_table.html'
    context_object_name = 'account_heads'


# --- BudgetTransaction Views ---
class BudgetTransactionListView(ListView):
    model = BudgetTransaction
    template_name = 'accounts/budgettransaction/list.html'
    context_object_name = 'budget_transactions'

class BudgetTransactionCreateView(CreateView):
    model = BudgetTransaction
    form_class = BudgetTransactionForm
    template_name = 'accounts/budgettransaction/form.html'
    success_url = reverse_lazy('budget_transaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Transaction added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetTransactionList'})
        return response

class BudgetTransactionUpdateView(UpdateView):
    model = BudgetTransaction
    form_class = BudgetTransactionForm
    template_name = 'accounts/budgettransaction/form.html'
    success_url = reverse_lazy('budget_transaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Transaction updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetTransactionList'})
        return response

class BudgetTransactionDeleteView(DeleteView):
    model = BudgetTransaction
    template_name = 'accounts/budgettransaction/confirm_delete.html'
    success_url = reverse_lazy('budget_transaction_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Budget Transaction deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetTransactionList'})
        return response

# Partial view for BudgetTransaction list table (for HTMX refresh)
class BudgetTransactionTablePartialView(ListView):
    model = BudgetTransaction
    template_name = 'accounts/budgettransaction/_budgettransaction_table.html'
    context_object_name = 'budget_transactions'


# --- Budget Print/Report View ---
class BudgetTransactionPrintView(TemplateView):
    template_name = 'accounts/budgettransaction/print_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        acc_head_id = self.kwargs.get('acc_head_id') # From URL: /print/<int:acc_head_id>/
        
        # Mimic ASP.NET's logic: get the first transaction linked to this AccountHead.
        budget_transaction = BudgetTransaction.get_transaction_for_report(acc_head_id)

        if not budget_transaction:
            raise Http404("Budget Transaction or Account Head not found for this ID.")

        context['transaction'] = budget_transaction
        # Prepare context data as seen in Crystal Reports parameters
        context['budget_code'] = budget_transaction.budget_code.id
        context['sys_date'] = budget_transaction.get_formatted_sys_date_for_report()
        context['sys_time'] = budget_transaction.sys_time_str
        context['description'] = budget_transaction.budget_code.get_full_description()
        context['amount'] = budget_transaction.amount

        return context

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will utilize DRY principles, extending `core/base.html` and using partial templates for HTMX-driven dynamic content. Styling will be done with Tailwind CSS, and DataTables will manage list view presentation.

**1. `accounts/accounthead/list.html`** (AccountHead List View)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Account Heads</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'account_head_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Account Head
        </button>
    </div>
    
    <div id="accountheadTable-container"
         hx-trigger="load, refreshAccountHeadList from:body"
         hx-get="{% url 'account_head_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center text-gray-600">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg">Loading Account Heads...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 ease-out"
             _="on load add .scale-100 .opacity-100 to me">
             <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js can be used for more complex UI state if needed, but HTMX covers most here -->
<script>
    // No specific Alpine.js needed for this simple modal logic,
    // as HTMX and _hyperscript directly manage the modal state.
</script>
{% endblock %}

```

**2. `accounts/accounthead/_accounthead_table.html`** (AccountHead Table Partial)

```html
<div class="p-4">
    <table id="accountheadTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in account_heads %}
            <tr class="hover:bg-gray-50 transition duration-150 ease-in-out">
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.symbol }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md mr-2 text-xs shadow-sm"
                        hx-get="{% url 'account_head_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-md text-xs shadow-sm"
                        hx-get="{% url 'account_head_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-4 px-6 text-center text-gray-500">No account heads found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors on HTMX swap
    if ($.fn.DataTable.isDataTable('#accountheadTable')) {
        $('#accountheadTable').DataTable().destroy();
    }
    $('#accountheadTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers",
        "responsive": true
    });
});
</script>
```

**3. `accounts/accounthead/form.html`** (AccountHead Form Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Account Head</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <ul class="text-red-600 text-sm">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-md">
                <i class="fas fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

**4. `accounts/accounthead/confirm_delete.html`** (AccountHead Delete Confirmation Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <p class="text-gray-700 mb-6">Are you sure you want to delete the Account Head: <strong>{{ object.description }} - {{ object.symbol }}</strong>?</p>
        <p class="text-red-600 font-medium mb-6">This action cannot be undone.</p>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-md">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

---

**5. `accounts/budgettransaction/list.html`** (BudgetTransaction List View)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Budget Transactions</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'budget_transaction_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Transaction
        </button>
    </div>
    
    <div id="budgettransactionTable-container"
         hx-trigger="load, refreshBudgetTransactionList from:body"
         hx-get="{% url 'budget_transaction_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center text-gray-600">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg">Loading Budget Transactions...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 ease-out"
             _="on load add .scale-100 .opacity-100 to me">
             <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js can be used for more complex UI state if needed -->
<script>
    // No specific Alpine.js needed here, _hyperscript handles modal
</script>
{% endblock %}
```

**6. `accounts/budgettransaction/_budgettransaction_table.html`** (BudgetTransaction Table Partial)

```html
<div class="p-4">
    <table id="budgettransactionTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Head</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in budget_transactions %}
            <tr class="hover:bg-gray-50 transition duration-150 ease-in-out">
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.sys_date_str }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.sys_time_str }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.budget_code.get_full_description }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.amount|floatformat:2 }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md mr-2 text-xs shadow-sm"
                        hx-get="{% url 'budget_transaction_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-md text-xs shadow-sm"
                        hx-get="{% url 'budget_transaction_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                    <a href="{% url 'budget_transaction_print' obj.budget_code.pk %}" target="_blank"
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1.5 px-3 rounded-md text-xs shadow-sm inline-block">
                        <i class="fas fa-print"></i> Print
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-gray-500">No budget transactions found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#budgettransactionTable')) {
        $('#budgettransactionTable').DataTable().destroy();
    }
    $('#budgettransactionTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers",
        "responsive": true
    });
});
</script>
```

**7. `accounts/budgettransaction/form.html`** (BudgetTransaction Form Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Budget Transaction</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <ul class="text-red-600 text-sm">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-md">
                <i class="fas fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

**8. `accounts/budgettransaction/confirm_delete.html`** (BudgetTransaction Delete Confirmation Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <p class="text-gray-700 mb-6">Are you sure you want to delete the Budget Transaction for <strong>{{ object.budget_code.get_full_description }}</strong> on <strong>{{ object.sys_date_str }}</strong> with amount <strong>{{ object.amount|floatformat:2 }}</strong>?</p>
        <p class="text-red-600 font-medium mb-6">This action cannot be undone.</p>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 ease-in-out shadow-md">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

**9. `accounts/budgettransaction/print_report.html`** (Budget Transaction Print View - replaces Crystal Report)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-3xl bg-white shadow-xl rounded-lg my-8">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-extrabold text-blue-800 mb-2">Budget Transaction Report</h1>
        <p class="text-gray-600 text-lg">Detailed View for Printing</p>
    </div>

    <div class="border border-gray-300 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b">Transaction Details</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700 text-lg">
            <div class="flex flex-col">
                <span class="font-semibold text-gray-600">Account Head:</span>
                <span class="mt-1 text-gray-900">{{ description }}</span>
            </div>
            <div class="flex flex-col">
                <span class="font-semibold text-gray-600">Budget Code:</span>
                <span class="mt-1 text-gray-900">{{ budget_code }}</span>
            </div>
            <div class="flex flex-col">
                <span class="font-semibold text-gray-600">Date:</span>
                <span class="mt-1 text-gray-900">{{ sys_date }}</span>
            </div>
            <div class="flex flex-col">
                <span class="font-semibold text-gray-600">Time:</span>
                <span class="mt-1 text-gray-900">{{ sys_time }}</span>
            </div>
            <div class="flex flex-col md:col-span-2">
                <span class="font-semibold text-gray-600">Amount:</span>
                <span class="mt-1 text-gray-900 text-2xl font-bold text-green-700">${{ amount|floatformat:2 }}</span>
            </div>
        </div>
    </div>

    <div class="text-center mt-8">
        <button onclick="window.print()" class="bg-gray-700 hover:bg-gray-800 text-white font-bold py-2 px-6 rounded-lg shadow-md print:hidden transition duration-300 ease-in-out">
            <i class="fas fa-print mr-2"></i> Print Report
        </button>
        <a href="javascript:history.back()" class="ml-4 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-lg shadow-md print:hidden transition duration-300 ease-in-out">
            <i class="fas fa-arrow-left mr-2"></i> Back
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<style>
    @media print {
        body {
            background-color: #fff;
            margin: 0;
            padding: 0;
        }
        .container {
            box-shadow: none !important;
            border: none !important;
            margin: 0 !important;
            max-width: 100% !important;
            padding: 0 !important;
        }
        .print\\:hidden {
            display: none !important;
        }
    }
</style>
{% endblock %}
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

The `accounts/urls.py` file will map URL paths to the respective Django views. This provides a clean, maintainable structure for navigation.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    AccountHeadListView, AccountHeadCreateView, AccountHeadUpdateView, AccountHeadDeleteView, AccountHeadTablePartialView,
    BudgetTransactionListView, BudgetTransactionCreateView, BudgetTransactionUpdateView, BudgetTransactionDeleteView, BudgetTransactionTablePartialView,
    BudgetTransactionPrintView
)

urlpatterns = [
    # Account Head URLs
    path('accountheads/', AccountHeadListView.as_view(), name='account_head_list'),
    path('accountheads/add/', AccountHeadCreateView.as_view(), name='account_head_add'),
    path('accountheads/edit/<int:pk>/', AccountHeadUpdateView.as_view(), name='account_head_edit'),
    path('accountheads/delete/<int:pk>/', AccountHeadDeleteView.as_view(), name='account_head_delete'),
    path('accountheads/table/', AccountHeadTablePartialView.as_view(), name='account_head_table'), # HTMX partial

    # Budget Transaction URLs
    path('budgettransactions/', BudgetTransactionListView.as_view(), name='budget_transaction_list'),
    path('budgettransactions/add/', BudgetTransactionCreateView.as_view(), name='budget_transaction_add'),
    path('budgettransactions/edit/<int:pk>/', BudgetTransactionUpdateView.as_view(), name='budget_transaction_edit'),
    path('budgettransactions/delete/<int:pk>/', BudgetTransactionDeleteView.as_view(), name='budget_transaction_delete'),
    path('budgettransactions/table/', BudgetTransactionTablePartialView.as_view(), name='budget_transaction_table'), # HTMX partial
    
    # Budget Print/Report URL (replaces original ASP.NET page)
    # The 'Id' in the original query string maps to acc_head_id here.
    path('budgettransactions/print/<int:acc_head_id>/', BudgetTransactionPrintView.as_view(), name='budget_transaction_print'),
]

```

You would then include these URLs in your project's main `urls.py`:
```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('accounts.urls')), # Include the accounts app URLs
    # Add other project-level URLs
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for model methods and integration tests for all views are critical for ensuring the migrated application works correctly and robustly. We aim for at least 80% test coverage.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import AccountHead, BudgetTransaction
from datetime import date, time

class AccountHeadModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.account_head1 = AccountHead.objects.create(
            description='Office Supplies',
            symbol='OFSUP'
        )
        cls.account_head2 = AccountHead.objects.create(
            description='Travel Expenses',
            symbol='TRAVEL'
        )
  
    def test_account_head_creation(self):
        """Test that an AccountHead object is created correctly."""
        self.assertEqual(self.account_head1.description, 'Office Supplies')
        self.assertEqual(self.account_head1.symbol, 'OFSUP')
        self.assertTrue(isinstance(self.account_head1, AccountHead))

    def test_str_method(self):
        """Test the __str__ method returns the expected string."""
        self.assertEqual(str(self.account_head1), 'Office Supplies - OFSUP')

    def test_get_full_description_method(self):
        """Test the get_full_description method."""
        self.assertEqual(self.account_head1.get_full_description(), 'Office Supplies-OFSUP')

class BudgetTransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create an AccountHead first as BudgetTransaction has a FK to it
        cls.account_head = AccountHead.objects.create(
            description='Electricity Bill',
            symbol='ELEC'
        )
        # Create test BudgetTransaction data
        cls.transaction1 = BudgetTransaction.objects.create(
            sys_date_str='15-03-2023',
            sys_time_str='10:30:00',
            budget_code=cls.account_head,
            amount=150.75
        )
        cls.transaction2 = BudgetTransaction.objects.create(
            sys_date_str='20-03-2023',
            sys_time_str='14:00:00',
            budget_code=cls.account_head,
            amount=200.00
        )

    def test_budget_transaction_creation(self):
        """Test that a BudgetTransaction object is created correctly."""
        self.assertEqual(self.transaction1.sys_date_str, '15-03-2023')
        self.assertEqual(self.transaction1.amount, 150.75)
        self.assertEqual(self.transaction1.budget_code, self.account_head)
        self.assertTrue(isinstance(self.transaction1, BudgetTransaction))

    def test_get_parsed_sys_date(self):
        """Test parsing of SysDate string to date object."""
        self.assertEqual(self.transaction1.get_parsed_sys_date(), date(2023, 3, 15))
        # Test with invalid date string
        self.transaction1.sys_date_str = 'invalid-date'
        self.assertIsNone(self.transaction1.get_parsed_sys_date())

    def test_get_parsed_sys_time(self):
        """Test parsing of SysTime string to time object."""
        self.assertEqual(self.transaction1.get_parsed_sys_time(), time(10, 30, 0))
        # Test with invalid time string
        self.transaction1.sys_time_str = 'invalid-time'
        self.assertIsNone(self.transaction1.get_parsed_sys_time())

    def test_get_formatted_sys_date_for_report(self):
        """Test date formatting for report."""
        self.assertEqual(self.transaction1.get_formatted_sys_date_for_report(), '15-03-2023')
        self.transaction1.sys_date_str = '01-01-2000'
        self.assertEqual(self.transaction1.get_formatted_sys_date_for_report(), '01-01-2000')

    def test_get_transaction_for_report(self):
        """Test the class method to get transaction for report."""
        # It should return the first transaction for the given account head ID
        retrieved_transaction = BudgetTransaction.get_transaction_for_report(self.account_head.pk)
        # Since transaction1 was created first for this account_head, it should be retrieved
        self.assertEqual(retrieved_transaction, self.transaction1) 

        # Test with a non-existent account head ID
        self.assertIsNone(BudgetTransaction.get_transaction_for_report(999))

class AccountHeadViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.account_head = AccountHead.objects.create(
            description='Test Head',
            symbol='TH'
        )

    def test_list_view(self):
        """Test the AccountHead list view."""
        response = self.client.get(reverse('account_head_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/accounthead/list.html')
        self.assertContains(response, 'Account Heads')
        self.assertContains(response, self.account_head.description)

    def test_list_view_htmx_partial(self):
        """Test the HTMX partial for AccountHead list table."""
        response = self.client.get(reverse('account_head_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/accounthead/_accounthead_table.html')
        self.assertContains(response, self.account_head.description)
        self.assertContains(response, 'id="accountheadTable"')

    def test_create_view_get(self):
        """Test GET request to AccountHead create view."""
        response = self.client.get(reverse('account_head_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/accounthead/form.html')
        self.assertContains(response, 'Add Account Head')

    def test_create_view_post_success(self):
        """Test successful POST request to AccountHead create view."""
        data = {'description': 'New Head', 'symbol': 'NH'}
        response = self.client.post(reverse('account_head_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(AccountHead.objects.filter(description='New Head').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshAccountHeadList')

    def test_create_view_post_invalid(self):
        """Test invalid POST request to AccountHead create view."""
        data = {'description': '', 'symbol': 'INVALID'} # Empty description
        response = self.client.post(reverse('account_head_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Description cannot be empty.')
        self.assertFalse(AccountHead.objects.filter(symbol='INVALID').exists())

    def test_update_view_get(self):
        """Test GET request to AccountHead update view."""
        response = self.client.get(reverse('account_head_edit', args=[self.account_head.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/accounthead/form.html')
        self.assertContains(response, 'Edit Account Head')
        self.assertContains(response, self.account_head.description)

    def test_update_view_post_success(self):
        """Test successful POST request to AccountHead update view."""
        data = {'description': 'Updated Head', 'symbol': 'UH'}
        response = self.client.post(reverse('account_head_edit', args=[self.account_head.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.account_head.refresh_from_db()
        self.assertEqual(self.account_head.description, 'Updated Head')
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        """Test GET request to AccountHead delete view."""
        response = self.client.get(reverse('account_head_delete', args=[self.account_head.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/accounthead/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.account_head.description)

    def test_delete_view_post_success(self):
        """Test successful POST request to AccountHead delete view."""
        response = self.client.post(reverse('account_head_delete', args=[self.account_head.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(AccountHead.objects.filter(pk=self.account_head.pk).exists())
        self.assertIn('HX-Trigger', response.headers)

class BudgetTransactionViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.account_head = AccountHead.objects.create(
            description='Utility Bill',
            symbol='UTIL'
        )
        self.transaction = BudgetTransaction.objects.create(
            sys_date_str='01-01-2023',
            sys_time_str='09:00:00',
            budget_code=self.account_head,
            amount=500.00
        )

    def test_list_view(self):
        """Test the BudgetTransaction list view."""
        response = self.client.get(reverse('budget_transaction_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgettransaction/list.html')
        self.assertContains(response, 'Budget Transactions')
        self.assertContains(response, str(self.transaction.amount))

    def test_list_view_htmx_partial(self):
        """Test the HTMX partial for BudgetTransaction list table."""
        response = self.client.get(reverse('budget_transaction_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgettransaction/_budgettransaction_table.html')
        self.assertContains(response, str(self.transaction.amount))
        self.assertContains(response, 'id="budgettransactionTable"')

    def test_create_view_get(self):
        """Test GET request to BudgetTransaction create view."""
        response = self.client.get(reverse('budget_transaction_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgettransaction/form.html')
        self.assertContains(response, 'Add Budget Transaction')

    def test_create_view_post_success(self):
        """Test successful POST request to BudgetTransaction create view."""
        data = {
            'sys_date_str': '02-02-2023',
            'sys_time_str': '11:00:00',
            'budget_code': self.account_head.pk,
            'amount': 250.50
        }
        response = self.client.post(reverse('budget_transaction_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue(BudgetTransaction.objects.filter(amount=250.50).exists())
        self.assertIn('HX-Trigger', response.headers)

    def test_update_view_get(self):
        """Test GET request to BudgetTransaction update view."""
        response = self.client.get(reverse('budget_transaction_edit', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgettransaction/form.html')
        self.assertContains(response, 'Edit Budget Transaction')
        self.assertContains(response, str(self.transaction.amount))

    def test_update_view_post_success(self):
        """Test successful POST request to BudgetTransaction update view."""
        data = {
            'sys_date_str': '01-01-2023',
            'sys_time_str': '09:00:00',
            'budget_code': self.account_head.pk,
            'amount': 550.00 # Updated amount
        }
        response = self.client.post(reverse('budget_transaction_edit', args=[self.transaction.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.amount, 550.00)
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        """Test GET request to BudgetTransaction delete view."""
        response = self.client.get(reverse('budget_transaction_delete', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgettransaction/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, str(self.transaction.amount))

    def test_delete_view_post_success(self):
        """Test successful POST request to BudgetTransaction delete view."""
        response = self.client.post(reverse('budget_transaction_delete', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BudgetTransaction.objects.filter(pk=self.transaction.pk).exists())
        self.assertIn('HX-Trigger', response.headers)

    def test_print_view_success(self):
        """Test the BudgetTransactionPrintView for valid ID."""
        response = self.client.get(reverse('budget_transaction_print', args=[self.account_head.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgettransaction/print_report.html')
        self.assertContains(response, 'Budget Transaction Report')
        self.assertContains(response, self.account_head.get_full_description())
        self.assertContains(response, str(self.transaction.amount))
        self.assertContains(response, self.transaction.sys_date_str)

    def test_print_view_no_transaction(self):
        """Test the BudgetTransactionPrintView for an account head with no transactions."""
        account_head_no_trans = AccountHead.objects.create(description='Empty', symbol='EMPTY')
        response = self.client.get(reverse('budget_transaction_print', args=[account_head_no_trans.pk]))
        self.assertEqual(response.status_code, 404) # Should raise Http404

    def test_print_view_invalid_id(self):
        """Test the BudgetTransactionPrintView for a non-existent account head ID."""
        response = self.client.get(reverse('budget_transaction_print', args=[99999]))
        self.assertEqual(response.status_code, 404) # Should raise Http404

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for Modals and List Refresh:** All Add, Edit, and Delete buttons in the list views (`list.html` and `_table.html` partials) use `hx-get` to fetch the form or confirmation partial into `#modalContent` within the main `#modal` div.
*   **HTMX for Form Submission:** The forms (`form.html` and `confirm_delete.html` partials) use `hx-post` to submit data back to the same URL. On successful submission (HTTP 204 No Content), an `HX-Trigger` header (`refreshAccountHeadList` or `refreshBudgetTransactionList`) is sent.
*   **List Refresh:** The `_table.html` partials are wrapped in a container that listens for `load` and `refresh[ModelName]List from:body` events (`hx-trigger="load, refreshAccountHeadList from:body"`). This ensures the DataTable is reloaded and reinitialized after any CRUD operation.
*   **Alpine.js (Optional but recommended for complex UI):** While `_hyperscript` is used directly in the templates for simple modal toggle (`on click add .is-active to #modal`), Alpine.js could be initialized in `extra_js` block if more complex frontend state management or reactive components were needed. For this specific scenario, `_hyperscript` suffices.
*   **DataTables Initialization:** The `_table.html` partials include a `<script>` block to initialize DataTables on the loaded table. It's crucial to destroy any existing DataTable instance before re-initializing to prevent errors with HTMX partial reloads.
*   **No Full Page Reloads:** All CRUD interactions occur within modals, and the list tables update dynamically, providing a Single Page Application (SPA)-like experience without complex JavaScript frameworks.
*   **Print View:** The `print_report.html` template includes a `window.print()` button for user-initiated printing and a `print:hidden` Tailwind CSS class to hide controls when printing.

## Final Notes

*   **Placeholders:** Replace placeholders like `[APP_NAME]`, `[MODEL_NAME_LOWER]`, etc., with your actual app and model names. In this detailed plan, we've used `accounts`, `accounthead`, and `budgettransaction`.
*   **Database Connection:** Ensure your Django `settings.py` is configured to connect to your legacy SQL Server database using `django-pyodbc-azure` or a similar backend that supports `managed = False`.
*   **Static/Media Files:** Ensure Django's static files configuration is set up correctly (e.g., `STATIC_URL`, `STATIC_ROOT`, `STATICFILES_DIRS`).
*   **Security:** Implement Django's authentication and authorization as needed. The current plan does not include this, but it is a critical step in a production application.
*   **Error Handling:** The current views use Django's default error handling. For production, consider more robust error logging and user-friendly error pages.
*   **Data Consistency:** Given `SysDate` and `SysTime` are stored as strings in the legacy database, rigorous validation (as implemented in forms) and proper parsing (in models) are essential. Data cleanup might be necessary if existing data is not consistently formatted.
*   **Tailwind CSS:** Ensure Tailwind CSS is set up and compiled in your Django project to render the styled components correctly.
*   **Font Awesome:** The templates use Font Awesome icons (`<i>` tags with `fas` classes). Ensure Font Awesome is linked in your `base.html`.