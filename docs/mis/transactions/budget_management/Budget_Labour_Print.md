## ASP.NET to Django Conversion Script:

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET application, specifically the `Budget_Labour_Print.aspx` page, to a modern Django 5.0+ solution. The core functionality of this ASP.NET page is to display a Crystal Report based on specific budget transaction data. In Django, we will replace this proprietary reporting mechanism with an interactive web-based data display using standard HTML, enhanced by HTMX, Alpine.js, and DataTables for a rich user experience without full page reloads.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code executes a SQL query joining `tblACC_Budget_Transactions` and `AccHead` tables.
The query filters `AccHead.Id` using a query string parameter `Id`.
It extracts `Id`, `SysDate`, `SysTime`, `BudgetCode`, `Amount` from `tblACC_Budget_Transactions` and `Description`, `Symbol` from `AccHead`.

**Inferred Schema:**

*   **Table:** `tblACC_Budget_Transactions`
    *   **Columns:**
        *   `Id` (Primary Key, integer)
        *   `SysDate` (String, appears to be `DD-MM-YYYY` format, represents a date)
        *   `SysTime` (String, represents a time)
        *   `BudgetCode` (Foreign Key to `AccHead.Id`, integer)
        *   `Amount` (Decimal/Numeric)

*   **Table:** `AccHead`
    *   **Columns:**
        *   `Id` (Primary Key, integer)
        *   `Description` (String)
        *   `Symbol` (String)

*   **Relationship:** `tblACC_Budget_Transactions.BudgetCode` refers to `AccHead.Id`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The provided ASP.NET code is exclusively for **Read** operations, specifically to display a detailed report (via Crystal Reports) of a single budget transaction identified by an `AccHead.Id` (passed as `Id` in the query string). It retrieves data, sets report parameters, and renders the report. There are no direct `Create`, `Update`, or `Delete` operations in this specific file.

**Modernization Scope Extension:**
Although the original ASP.NET file is "read-only", for a comprehensive modernization to a Django application, it's essential to provide full CRUD capabilities for the `BudgetTransaction` entity. The original "print" functionality will be transformed into a detailed view.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The primary UI component is `CrystalReportViewer1`, which displays the generated Crystal Report. The page doesn't contain interactive forms or data grids.
The `Request.QueryString["Id"]` implies a parameter-driven report display.

**Django Equivalent UI Components:**
*   **List View:** A page (using DataTables) to list all `BudgetTransaction` records, allowing users to browse and select a transaction for detail viewing or CRUD operations.
*   **Detail View:** A dedicated page to display the details of a single `BudgetTransaction` and its associated `AccountHead` information, serving as the replacement for the Crystal Report.
*   **Form (Modal):** Modals for adding, editing, and confirming deletion of `BudgetTransaction` records, leveraging HTMX for dynamic content loading and form submission.

### Step 4: Generate Django Code

We will create a new Django app named `budget_app` for this module.

#### 4.1 Models

We need two models: `AccountHead` for the account descriptions and `BudgetTransaction` for the budget entries.

**File: `budget_app/models.py`**

```python
from django.db import models
from django.utils.translation import gettext_lazy as _

class AccountHead(models.Model):
    """
    Maps to the 'AccHead' table in the legacy database.
    Represents an account head with a description and symbol.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to create/manage this table
        db_table = 'AccHead'
        verbose_name = _('Account Head')
        verbose_name_plural = _('Account Heads')

    def __str__(self):
        return f"{self.description} - {self.symbol}" if self.description and self.symbol else f"Account Head {self.id}"

    def get_full_description(self):
        """
        Replicates the 'Description' + '-' + 'Symbol' logic from the original query.
        """
        return f"{self.description}-{self.symbol}" if self.description and self.symbol else ""

class BudgetTransaction(models.Model):
    """
    Maps to the 'tblACC_Budget_Transactions' table in the legacy database.
    Represents a specific budget transaction entry.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # SysDate is stored as DD-MM-YYYY string in the original database.
    # We will store it as a DateField and handle display formatting in templates.
    sys_date = models.DateField(db_column='SysDate', null=True, blank=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, null=True, blank=True) # Stored as string in original DB
    budget_code = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='BudgetCode', related_name='budget_transactions', null=True, blank=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, null=True, blank=True)

    class Meta:
        managed = False  # Tells Django not to create/manage this table
        db_table = 'tblACC_Budget_Transactions'
        verbose_name = _('Budget Transaction')
        verbose_name_plural = _('Budget Transactions')
        ordering = ['-sys_date', '-sys_time'] # Default ordering

    def __str__(self):
        return f"Budget {self.id} for {self.budget_code.get_full_description() if self.budget_code else 'N/A'}"

    def get_formatted_sys_date(self):
        """
        Returns the system date formatted as DD-MM-YYYY to match original output style.
        """
        if self.sys_date:
            return self.sys_date.strftime('%d-%m-%Y')
        return ""

    def get_display_description(self):
        """
        Combines AccountHead description and symbol for display,
        similar to the original report's 'Description' parameter.
        """
        if self.budget_code:
            return self.budget_code.get_full_description()
        return "N/A"

```

#### 4.2 Forms

A form for `BudgetTransaction` for Create and Update operations.

**File: `budget_app/forms.py`**

```python
from django import forms
from .models import BudgetTransaction, AccountHead
import datetime

class BudgetTransactionForm(forms.ModelForm):
    """
    Form for creating and updating BudgetTransaction records.
    """
    # Use CharField for sys_date to handle DD-MM-YYYY input, then convert in clean method
    sys_date = forms.CharField(
        label="System Date (DD-MM-YYYY)",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'DD-MM-YYYY'
        })
    )

    class Meta:
        model = BudgetTransaction
        fields = ['sys_date', 'sys_time', 'budget_code', 'amount']
        widgets = {
            'sys_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'HH:MM:SS'}),
            'budget_code': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
        }
        labels = {
            'budget_code': 'Account Head',
        }

    def clean_sys_date(self):
        sys_date_str = self.cleaned_data['sys_date']
        try:
            # Attempt to parse DD-MM-YYYY format
            return datetime.datetime.strptime(sys_date_str, '%d-%m-%Y').date()
        except ValueError:
            raise forms.ValidationError("Invalid date format. Please use DD-MM-YYYY.")

```

#### 4.3 Views

The views for listing, creating, updating, deleting, and displaying details of `BudgetTransaction` records.

**File: `budget_app/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import BudgetTransaction, AccountHead
from .forms import BudgetTransactionForm
from django.shortcuts import get_object_or_404

# Keep views thin (5-15 lines) - business logic moved to models or forms where applicable.

class BudgetTransactionListView(ListView):
    """
    Displays a list of all BudgetTransaction records.
    The table content is loaded via HTMX.
    """
    model = BudgetTransaction
    template_name = 'budget_app/budgettransaction/list.html'
    context_object_name = 'budget_transactions'

class BudgetTransactionTablePartialView(TemplateView):
    """
    Renders the DataTables partial for BudgetTransaction list,
    to be loaded dynamically via HTMX.
    """
    template_name = 'budget_app/budgettransaction/_budgettransaction_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetch all budget transactions, optimized for display
        context['budget_transactions'] = BudgetTransaction.objects.select_related('budget_code').all()
        return context

class BudgetTransactionCreateView(CreateView):
    """
    Handles creation of new BudgetTransaction records via a modal form.
    """
    model = BudgetTransaction
    form_class = BudgetTransactionForm
    template_name = 'budget_app/budgettransaction/_budgettransaction_form.html'
    success_url = reverse_lazy('budgettransaction_list') # Not directly used with HX-Swap: none

    def form_valid(self, form):
        # Business logic for form saving handled by ModelForm.save()
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Transaction added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetTransactionList'
                }
            )
        return response

class BudgetTransactionUpdateView(UpdateView):
    """
    Handles updating existing BudgetTransaction records via a modal form.
    """
    model = BudgetTransaction
    form_class = BudgetTransactionForm
    template_name = 'budget_app/budgettransaction/_budgettransaction_form.html'
    context_object_name = 'budget_transaction'
    success_url = reverse_lazy('budgettransaction_list') # Not directly used with HX-Swap: none

    def form_valid(self, form):
        # Business logic for form saving handled by ModelForm.save()
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Transaction updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetTransactionList'
                }
            )
        return response

class BudgetTransactionDeleteView(DeleteView):
    """
    Handles deletion of BudgetTransaction records via a confirmation modal.
    """
    model = BudgetTransaction
    template_name = 'budget_app/budgettransaction/confirm_delete.html'
    context_object_name = 'budget_transaction'
    success_url = reverse_lazy('budgettransaction_list') # Not directly used with HX-Swap: none

    def delete(self, request, *args, **kwargs):
        # Business logic for deletion handled by DeleteView.delete()
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Budget Transaction deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetTransactionList'
                }
            )
        return response

class BudgetTransactionDetailView(DetailView):
    """
    Displays the details of a single BudgetTransaction,
    replicating the data presented by the original Crystal Report.
    This view uses 'pk' which corresponds to AccountHead.Id,
    and fetches the *first* BudgetTransaction linked to it,
    as per the original ASP.NET code's behavior (ds.Tables[0].Rows[0]).
    """
    model = BudgetTransaction
    template_name = 'budget_app/budgettransaction/detail.html'
    context_object_name = 'transaction_detail'

    def get_object(self, queryset=None):
        """
        Overrides get_object to find a BudgetTransaction based on AccountHead.Id (self.kwargs['pk']).
        This directly replaces the original SQL logic filtering by AccHead.Id.
        """
        account_head_id = self.kwargs.get('pk')
        if not account_head_id:
            raise Http404(_("Account Head ID not provided"))

        # Find the AccountHead first
        account_head = get_object_or_404(AccountHead, id=account_head_id)

        # Retrieve the first BudgetTransaction associated with this AccountHead.
        # The original code used ds.Tables[0].Rows[0], implying only the first
        # matching record was used if multiple existed.
        budget_transaction = account_head.budget_transactions.first()

        if not budget_transaction:
            raise Http404(_("No Budget Transaction found for this Account Head ID."))

        return budget_transaction

```

#### 4.4 Templates

Templates for the list view, partials for the table and forms, and a detail view.

**File: `budget_app/templates/budget_app/budgettransaction/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Budget Transactions</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'budgettransaction_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Budget Transaction
        </button>
    </div>

    <div id="budgettransactionTable-container"
         hx-trigger="load, refreshBudgetTransactionList from:body"
         hx-get="{% url 'budgettransaction_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-700">Loading Budget Transactions...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if specific UI state management is needed
        // For example, a global modal state if not using htmx's _ syntax
    });
</script>
{% endblock %}
```

**File: `budget_app/templates/budget_app/budgettransaction/_budgettransaction_table.html`**

```html
<div class="overflow-x-auto rounded-lg shadow">
    <table id="budgettransactionTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Head</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in budget_transactions %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.get_formatted_sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.sys_time }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.budget_code.get_full_description|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap">
                    <button
                        class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                        hx-get="{% url 'budgettransaction_detail' obj.budget_code.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        View Details
                    </button>
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                        hx-get="{% url 'budgettransaction_edit' obj.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'budgettransaction_delete' obj.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Only initialize DataTable if it hasn't been initialized
    if (!$.fn.DataTable.isDataTable('#budgettransactionTable')) {
        $('#budgettransactionTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    }
});
</script>
```

**File: `budget_app/templates/budget_app/budgettransaction/_budgettransaction_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Budget Transaction</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="body">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `budget_app/templates/budget_app/budgettransaction/confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the budget transaction:</p>
    <p class="font-bold text-lg text-center mb-6">{{ budget_transaction.budget_code.get_full_description|default:"N/A" }} - Amount: {{ budget_transaction.amount }} on {{ budget_transaction.get_formatted_sys_date }}?</p>
    <form hx-post="{% url 'budgettransaction_delete' budget_transaction.pk %}" hx-swap="none" hx-target="body">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `budget_app/templates/budget_app/budgettransaction/detail.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Budget Transaction Details (Report View)</h3>

    <div class="bg-gray-50 p-4 rounded-lg shadow-inner">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm font-medium text-gray-500">Budget Code:</p>
                <p class="text-lg font-bold text-gray-900">{{ transaction_detail.budget_code.id }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-500">System Date:</p>
                <p class="text-lg font-bold text-gray-900">{{ transaction_detail.get_formatted_sys_date }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-500">System Time:</p>
                <p class="text-lg font-bold text-gray-900">{{ transaction_detail.sys_time }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-500">Description:</p>
                <p class="text-lg font-bold text-gray-900">{{ transaction_detail.get_display_description }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-500">Amount:</p>
                <p class="text-lg font-bold text-gray-900">{{ transaction_detail.amount|floatformat:2 }}</p>
            </div>
        </div>
    </div>

    <div class="mt-6 flex items-center justify-end">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Close
        </button>
    </div>
</div>
```

#### 4.5 URLs

URL patterns for the `budget_app`.

**File: `budget_app/urls.py`**

```python
from django.urls import path
from .views import (
    BudgetTransactionListView,
    BudgetTransactionCreateView,
    BudgetTransactionUpdateView,
    BudgetTransactionDeleteView,
    BudgetTransactionDetailView,
    BudgetTransactionTablePartialView,
)

urlpatterns = [
    path('budgettransactions/', BudgetTransactionListView.as_view(), name='budgettransaction_list'),
    path('budgettransactions/table/', BudgetTransactionTablePartialView.as_view(), name='budgettransaction_table'),
    path('budgettransactions/add/', BudgetTransactionCreateView.as_view(), name='budgettransaction_add'),
    path('budgettransactions/edit/<int:pk>/', BudgetTransactionUpdateView.as_view(), name='budgettransaction_edit'),
    path('budgettransactions/delete/<int:pk>/', BudgetTransactionDeleteView.as_view(), name='budgettransaction_delete'),
    # This detail view replaces the original Crystal Report functionality
    # It takes AccountHead.Id as pk, similar to original QueryString["Id"]
    path('budgettransactions/detail/<int:pk>/', BudgetTransactionDetailView.as_view(), name='budgettransaction_detail'),
]
```

#### 4.6 Tests

Comprehensive unit tests for models and integration tests for views.

**File: `budget_app/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import AccountHead, BudgetTransaction
import datetime

class AccountHeadModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        AccountHead.objects.create(id=1, description='Labour Budget', symbol='LB')
        AccountHead.objects.create(id=2, description='Material Budget', symbol='MB')

    def test_account_head_creation(self):
        acc_head = AccountHead.objects.get(id=1)
        self.assertEqual(acc_head.description, 'Labour Budget')
        self.assertEqual(acc_head.symbol, 'LB')

    def test_get_full_description_method(self):
        acc_head = AccountHead.objects.get(id=1)
        self.assertEqual(acc_head.get_full_description(), 'Labour Budget-LB')

    def test_str_method(self):
        acc_head = AccountHead.objects.get(id=1)
        self.assertEqual(str(acc_head), 'Labour Budget - LB')
        acc_head_no_desc_sym = AccountHead.objects.create(id=3, description=None, symbol=None)
        self.assertEqual(str(acc_head_no_desc_sym), 'Account Head 3')


class BudgetTransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent AccountHead first
        acc_head = AccountHead.objects.create(id=100, description='Project A', symbol='PA')
        # Create test data for BudgetTransaction
        BudgetTransaction.objects.create(
            id=1,
            sys_date=datetime.date(2023, 10, 26),
            sys_time='10:30:00',
            budget_code=acc_head,
            amount=1500.75
        )

    def test_budget_transaction_creation(self):
        bt = BudgetTransaction.objects.get(id=1)
        self.assertEqual(bt.sys_date, datetime.date(2023, 10, 26))
        self.assertEqual(bt.sys_time, '10:30:00')
        self.assertEqual(bt.budget_code.description, 'Project A')
        self.assertEqual(str(bt.amount), '1500.75') # DecimalField stores as Decimal

    def test_get_formatted_sys_date_method(self):
        bt = BudgetTransaction.objects.get(id=1)
        self.assertEqual(bt.get_formatted_sys_date(), '26-10-2023')

    def test_get_display_description_method(self):
        bt = BudgetTransaction.objects.get(id=1)
        self.assertEqual(bt.get_display_description(), 'Project A-PA')

    def test_str_method(self):
        bt = BudgetTransaction.objects.get(id=1)
        self.assertEqual(str(bt), 'Budget 1 for Project A-PA')


class BudgetTransactionViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary linked data
        cls.acc_head_1 = AccountHead.objects.create(id=200, description='Test Project', symbol='TP')
        cls.acc_head_2 = AccountHead.objects.create(id=201, description='Another Project', symbol='AP')

        # Create test data for views
        cls.bt1 = BudgetTransaction.objects.create(
            id=10,
            sys_date=datetime.date(2023, 11, 1),
            sys_time='09:00:00',
            budget_code=cls.acc_head_1,
            amount=100.00
        )
        cls.bt2 = BudgetTransaction.objects.create(
            id=11,
            sys_date=datetime.date(2023, 11, 2),
            sys_time='14:00:00',
            budget_code=cls.acc_head_2,
            amount=250.50
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('budgettransaction_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgettransaction/list.html')
        # Check that the list view itself doesn't contain the objects, but prepares for HTMX
        self.assertNotIn('budget_transactions', response.context) # Table is loaded via HTMX

    def test_table_partial_view(self):
        response = self.client.get(reverse('budgettransaction_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgettransaction/_budgettransaction_table.html')
        self.assertIn('budget_transactions', response.context)
        self.assertContains(response, 'Test Project-TP')
        self.assertContains(response, 'Another Project-AP')
        self.assertContains(response, '100.00')

    def test_create_view_get(self):
        response = self.client.get(reverse('budgettransaction_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgettransaction/_budgettransaction_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        data = {
            'sys_date': '05-12-2023', # DD-MM-YYYY format
            'sys_time': '12:00:00',
            'budget_code': self.acc_head_1.id,
            'amount': '300.00',
        }
        response = self.client.post(reverse('budgettransaction_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX successful POST
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBudgetTransactionList', response.headers['HX-Trigger'])
        self.assertTrue(BudgetTransaction.objects.filter(amount=300.00).exists())
        self.assertEqual(BudgetTransaction.objects.get(amount=300.00).sys_date, datetime.date(2023, 12, 5))

    def test_create_view_post_invalid(self):
        data = {
            'sys_date': 'invalid-date', # Invalid format
            'sys_time': '12:00:00',
            'budget_code': self.acc_head_1.id,
            'amount': '300.00',
        }
        response = self.client.post(reverse('budgettransaction_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Invalid date format. Please use DD-MM-YYYY.')
        self.assertFalse(BudgetTransaction.objects.filter(amount=300.00).exists())


    def test_update_view_get(self):
        response = self.client.get(reverse('budgettransaction_edit', args=[self.bt1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgettransaction/_budgettransaction_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.bt1)

    def test_update_view_post_success(self):
        updated_data = {
            'sys_date': '01-01-2024',
            'sys_time': '11:11:11',
            'budget_code': self.acc_head_2.id,
            'amount': '999.99',
        }
        response = self.client.post(reverse('budgettransaction_edit', args=[self.bt1.id]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBudgetTransactionList', response.headers['HX-Trigger'])
        self.bt1.refresh_from_db()
        self.assertEqual(self.bt1.amount, 999.99)
        self.assertEqual(self.bt1.sys_date, datetime.date(2024, 1, 1))
        self.assertEqual(self.bt1.budget_code, self.acc_head_2)

    def test_delete_view_get(self):
        response = self.client.get(reverse('budgettransaction_delete', args=[self.bt1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgettransaction/confirm_delete.html')
        self.assertTrue('budget_transaction' in response.context)
        self.assertEqual(response.context['budget_transaction'], self.bt1)

    def test_delete_view_post_success(self):
        count_before = BudgetTransaction.objects.count()
        response = self.client.post(reverse('budgettransaction_delete', args=[self.bt1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBudgetTransactionList', response.headers['HX-Trigger'])
        self.assertEqual(BudgetTransaction.objects.count(), count_before - 1)
        self.assertFalse(BudgetTransaction.objects.filter(id=self.bt1.id).exists())

    def test_detail_view_success(self):
        # We need a BudgetTransaction linked to acc_head_1
        response = self.client.get(reverse('budgettransaction_detail', args=[self.acc_head_1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgettransaction/detail.html')
        self.assertTrue('transaction_detail' in response.context)
        self.assertEqual(response.context['transaction_detail'].id, self.bt1.id)
        self.assertContains(response, 'Budget Transaction Details (Report View)')
        self.assertContains(response, f'Budget Code: {self.acc_head_1.id}')
        self.assertContains(response, f'Description: {self.acc_head_1.get_full_description()}')
        self.assertContains(response, f'Amount: {self.bt1.amount}')

    def test_detail_view_no_budget_transaction(self):
        # Create an account head with no linked budget transactions
        acc_head_no_bt = AccountHead.objects.create(id=202, description='No Budget', symbol='NB')
        response = self.client.get(reverse('budgettransaction_detail', args=[acc_head_no_bt.id]))
        self.assertEqual(response.status_code, 404)

    def test_detail_view_invalid_account_head_id(self):
        response = self.client.get(reverse('budgettransaction_detail', args=[99999])) # Non-existent ID
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic updates:**
    *   The `budgettransaction_list.html` uses `hx-get` to load the `_budgettransaction_table.html` partial, which contains the DataTables.
    *   CRUD operations (Add, Edit, Delete) are triggered by buttons with `hx-get` that fetch the respective form/confirmation partial into a modal (`#modalContent`).
    *   Form submissions within the modal use `hx-post` with `hx-swap="none"`.
    *   Upon successful form submission (Create/Update/Delete), the views return `HttpResponse(status=204)` with an `HX-Trigger` header set to `refreshBudgetTransactionList`.
    *   The main list view's table container (`#budgettransactionTable-container`) listens for `refreshBudgetTransactionList` to reload the table, ensuring the DataTables content is always up-to-date.

*   **Alpine.js for UI state management:**
    *   The modal (`#modal`) uses `_` (Hyperscript, often used with HTMX and Alpine.js) to manage its `hidden` class.
    *   `_="on click add .is-active to #modal"` shows the modal.
    *   `_="on click if event.target.id == 'modal' remove .is-active from me"` hides the modal when clicking outside of `modalContent`.
    *   `_="on click remove .is-active from #modal"` hides the modal on cancel/close buttons.
    *   This provides a seamless, JavaScript-driven modal experience with minimal explicit JavaScript.

*   **DataTables for list views:**
    *   The `_budgettransaction_table.html` partial includes the JavaScript for DataTables initialization: `$('#budgettransactionTable').DataTable({...});`.
    *   This ensures client-side searching, sorting, and pagination for the list of budget transactions.

*   **No full page reloads:** All CRUD operations and data display (list and detail) are handled dynamically using HTMX, avoiding traditional page reloads and providing a fast, responsive user experience.

*   **DRY Template Inheritance:** All templates `{% extends 'core/base.html' %}` (assuming `core/base.html` contains common elements and CDN links for DataTables, HTMX, Alpine.js, Tailwind CSS, and jQuery).

## Final Notes

*   This plan fully replaces the Crystal Reports functionality with a modern, web-native detail view while providing a complete CRUD interface for managing Budget Transactions.
*   The `SysDate` parsing logic from the original ASP.NET was complex. In Django, by mapping it to `DateField`, we rely on Django's robust date handling, using `datetime.strptime` in the form's `clean_sys_date` to parse the `DD-MM-YYYY` input. Display formatting is handled by the model's `get_formatted_sys_date` method and template filters.
*   The `pk` in `BudgetTransactionDetailView`'s URL pattern is mapped to `AccountHead.Id` to match the original page's behavior of taking `QueryString["Id"]` which was `AccHead.Id`.
*   Remember to configure your Django project's `settings.py` to include `budget_app` in `INSTALLED_APPS` and update your root `urls.py` to include `budget_app.urls`.
*   Ensure your database connection settings in Django are correctly configured to connect to your existing SQL Server database.
*   The `managed = False` in model Meta classes is crucial for Django to not attempt to create or alter your legacy database tables.