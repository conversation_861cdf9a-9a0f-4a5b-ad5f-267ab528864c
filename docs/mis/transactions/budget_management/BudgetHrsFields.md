## ASP.NET to Django Conversion Script: BudgetHrsFields

This document outlines a strategic plan to modernize your existing ASP.NET `BudgetHrsFields.aspx` application to a robust, scalable, and maintainable Django-based solution. Our approach emphasizes automated conversion, leverages modern web technologies like HTMX and Alpine.js, and adheres to Django's best practices, including the "Fat Model, Thin View" architecture.

### Business Value Proposition

Migrating this ASP.NET application to Django offers several key business benefits:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms, which are complex to maintain and scale, to a modern, actively supported framework.
2.  **Improved Performance and User Experience:** HTMX and Alpine.js ensure dynamic updates without full page reloads, providing a snappier and more intuitive user experience. DataTables will enable efficient handling of large datasets, improving usability for data-intensive tasks.
3.  **Enhanced Maintainability and Scalability:** Django's structured MVC (or MTV) architecture, coupled with the "Fat Model, Thin View" approach, centralizes business logic, making the codebase easier to understand, debug, and extend. This modularity reduces development time for new features and simplifies long-term maintenance.
4.  **Cost Efficiency:** Automating large portions of the migration process significantly reduces manual coding effort, leading to faster deployment and lower development costs. Python's versatility and Django's ecosystem also provide a cost-effective platform for future development.
5.  **Future-Proofing:** Adopting a popular, open-source framework like Django ensures access to a vibrant community, continuous updates, and a wealth of libraries, safeguarding your investment against technological obsolescence.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include base.html template code in your output - assume it already exists and is properly linked.
*   Focus ONLY on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the `SqlDataSource` definitions, we identify two primary tables:

*   **`tblMIS_BudgetHrs_Field_Category`**: This table stores the main categories.
    *   Columns:
        *   `Id` (Primary Key, Integer)
        *   `Category` (String)
        *   `Symbol` (String)

*   **`tblMIS_BudgetHrs_Field_SubCategory`**: This table stores sub-categories, linked to the main categories.
    *   Columns:
        *   `Id` (Primary Key, Integer)
        *   `MId` (Foreign Key to `tblMIS_BudgetHrs_Field_Category.Id`, Integer)
        *   `SubCategory` (String)
        *   `Symbol` (String)

### Step 2: Identify Backend Functionality

The ASP.NET code implements comprehensive CRUD (Create, Read, Update, Delete) operations for both categories and sub-categories:

*   **For `tblMIS_BudgetHrs_Field_Category` (Field Category):**
    *   **Create:** Handled by `GridView1_RowCommand` for `Add` and `Add1` commands (inserting from footer or empty template).
    *   **Read:** Data bound to `GridView1` via `LocalSqlServer`'s `SelectCommand`. Note the `Id!=1` filter, which suggests 'Id=1' is a special category that shouldn't be listed in the main table.
    *   **Update:** Handled by `GridView1_RowUpdating`.
    *   **Delete:** Handled by `GridView1_RowDeleted`.
    *   **Validation:** Required fields for `Category` and `Symbol`.

*   **For `tblMIS_BudgetHrs_Field_SubCategory` (Field Sub-Category):**
    *   **Create:** Handled by `GridView2_RowCommand` for `Add_sb` and `Add_sb1` commands.
    *   **Read:** Data bound to `GridView2` via `SqlDataSource11`'s `SelectCommand`, which joins with the category table to display the category name.
    *   **Update:** Handled by `GridView2_RowUpdating`.
    *   **Delete:** Handled by `GridView2_RowDeleted`.
    *   **Validation:** Required fields for `Category` (MId), `SubCategory`, `Symbol`. Additionally, there's a business rule `MId != "1"` enforced client-side, preventing selection of category ID 1 for sub-categories.

### Step 3: Infer UI Components

The ASP.NET page features two `GridView` controls, each representing a data table with integrated CRUD forms.

*   **`GridView1` (Field Category):**
    *   Displays a list of categories with 'Edit' and 'Delete' actions.
    *   Includes an inline 'Insert' form in the footer or an 'EmptyDataTemplate'.
    *   Fields: SN (row number), Category (text), Symbol (text).
    *   Validation: Client-side required field validators.
*   **`GridView2` (Field Sub-Category):**
    *   Displays a list of sub-categories with 'Edit' and 'Delete' actions.
    *   Includes an inline 'Insert' form in the footer or an 'EmptyDataTemplate'.
    *   Fields: SN (row number), Category (dropdown tied to `tblMIS_BudgetHrs_Field_Category`), Sub-Category (text), Symbol (text).
    *   Validation: Client-side required field validators and an alert for `MId=1`.

The conversion will replace these `GridView`s with HTMX-powered DataTables, and the inline forms with HTMX-loaded modals, ensuring a modern, interactive user experience without full page reloads.

### Step 4: Generate Django Code

We will create a Django application named `mis_transaction`.

#### 4.1 Models (`mis_transaction/models.py`)

This file defines the Django models that map to your existing database tables. We'll use `managed = False` to tell Django not to create or modify these tables, as they already exist.

```python
from django.db import models
from django.core.exceptions import ValidationError

class BudgetHrsCategory(models.Model):
    """
    Maps to tblMIS_BudgetHrs_Field_Category.
    Represents the main field categories for budget hours.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=255, unique=True, null=False, blank=False)
    symbol = models.CharField(db_column='Symbol', max_length=50, unique=True, null=False, blank=False)

    class Meta:
        managed = False  # Tells Django not to manage this table's creation/deletion
        db_table = 'tblMIS_BudgetHrs_Field_Category'
        verbose_name = 'Budget Hours Category'
        verbose_name_plural = 'Budget Hours Categories'
        ordering = ['-id'] # Matches original ASP.NET ordering 'order by [Id] desc'

    def __str__(self):
        return self.category

    def save(self, *args, **kwargs):
        self.full_clean()  # Run model's clean methods before saving
        super().save(*args, **kwargs)

    @classmethod
    def get_all_categories(cls, exclude_id=None):
        """
        Retrieves all categories, optionally excluding a specific ID (like 1).
        Matches the SELECT * FROM [tblMIS_BudgetHrs_Field_Category] where Id!=1.
        """
        queryset = cls.objects.all()
        if exclude_id is not None:
            queryset = queryset.exclude(id=exclude_id)
        return queryset

    @classmethod
    def create_category(cls, category, symbol):
        """Business logic to create a new category."""
        return cls.objects.create(category=category, symbol=symbol)

    def update_category(self, category=None, symbol=None):
        """Business logic to update an existing category."""
        if category is not None:
            self.category = category
        if symbol is not None:
            self.symbol = symbol
        self.save()

    def delete_category(self):
        """Business logic to delete a category."""
        self.delete()


class BudgetHrsSubCategory(models.Model):
    """
    Maps to tblMIS_BudgetHrs_Field_SubCategory.
    Represents the sub-categories linked to main categories.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    category = models.ForeignKey(
        BudgetHrsCategory,
        on_delete=models.CASCADE,
        db_column='MId',  # Correctly maps to MId in the database
        related_name='subcategories'
    )
    subcategory = models.CharField(db_column='SubCategory', max_length=255, unique=True, null=False, blank=False)
    symbol = models.CharField(db_column='Symbol', max_length=50, unique=True, null=False, blank=False)

    class Meta:
        managed = False  # Tells Django not to manage this table's creation/deletion
        db_table = 'tblMIS_BudgetHrs_Field_SubCategory'
        verbose_name = 'Budget Hours Sub-Category'
        verbose_name_plural = 'Budget Hours Sub-Categories'
        # Matches original ASP.NET ordering
        ordering = ['category__id', 'id'] 

    def __str__(self):
        return f"{self.category.category} - {self.subcategory}"

    def clean(self):
        """Custom validation for the model."""
        super().clean()
        if self.category_id == 1: # MId = 1 check
            raise ValidationError('Category "Id=1" cannot be selected for sub-categories.')

    def save(self, *args, **kwargs):
        self.full_clean()  # Run model's clean methods before saving
        super().save(*args, **kwargs)

    @classmethod
    def get_all_subcategories_with_category_names(cls):
        """
        Retrieves all sub-categories, joining with category names.
        Matches SqlDataSource11's select command logic.
        """
        return cls.objects.select_related('category').all()

    @classmethod
    def create_subcategory(cls, mid, subcategory, symbol):
        """Business logic to create a new sub-category."""
        category_obj = BudgetHrsCategory.objects.get(id=mid)
        new_sub = cls(category=category_obj, subcategory=subcategory, symbol=symbol)
        new_sub.save() # This will trigger the clean method for MId=1 validation
        return new_sub

    def update_subcategory(self, mid=None, subcategory=None, symbol=None):
        """Business logic to update an existing sub-category."""
        if mid is not None:
            self.category = BudgetHrsCategory.objects.get(id=mid)
        if subcategory is not None:
            self.subcategory = subcategory
        if symbol is not None:
            self.symbol = symbol
        self.save() # This will trigger the clean method for MId=1 validation

    def delete_subcategory(self):
        """Business logic to delete a sub-category."""
        self.delete()

```

#### 4.2 Forms (`mis_transaction/forms.py`)

These forms handle data validation and clean user input before saving to the database.

```python
from django import forms
from .models import BudgetHrsCategory, BudgetHrsSubCategory

class BudgetHrsCategoryForm(forms.ModelForm):
    """
    Form for creating and updating BudgetHrsCategory instances.
    """
    class Meta:
        model = BudgetHrsCategory
        fields = ['category', 'symbol']
        widgets = {
            'category': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter category name'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter symbol'}),
        }
        labels = {
            'category': 'Category Name',
            'symbol': 'Symbol Code',
        }
    
    # Custom validation can be added here if needed, beyond model's full_clean

class BudgetHrsSubCategoryForm(forms.ModelForm):
    """
    Form for creating and updating BudgetHrsSubCategory instances.
    """
    category = forms.ModelChoiceField(
        queryset=BudgetHrsCategory.objects.all(), # SqlDataSource2 selects all categories
        empty_label="-- Select Category --", # ASP.NET had a "1" check, this is more user-friendly
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )

    class Meta:
        model = BudgetHrsSubCategory
        fields = ['category', 'subcategory', 'symbol']
        widgets = {
            'subcategory': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter sub-category name'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter symbol'}),
        }
        labels = {
            'category': 'Main Category',
            'subcategory': 'Sub-Category Name',
            'symbol': 'Symbol Code',
        }

    def clean_category(self):
        """
        Custom validation for the category field. 
        Replicates the ASP.NET 'MId != 1' logic, ensuring category ID 1 is not selected.
        """
        category = self.cleaned_data['category']
        if category and category.id == 1:
            raise forms.ValidationError('Category "Id=1" cannot be selected for sub-categories.')
        return category

```

#### 4.3 Views (`mis_transaction/views.py`)

Views are kept thin, delegating business logic to the models. HTMX triggers are used for dynamic updates.

```python
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.db import IntegrityError # For handling unique constraints

from .models import BudgetHrsCategory, BudgetHrsSubCategory
from .forms import BudgetHrsCategoryForm, BudgetHrsSubCategoryForm

# --- Main Page View (combines both sections) ---
class BudgetHrsFieldsIndexView(TemplateView):
    """
    Main view to display both BudgetHrsCategory and BudgetHrsSubCategory tables.
    Mimics the dual-table layout of the original ASP.NET page.
    """
    template_name = 'mis_transaction/budgethrsfields/index.html'

# --- BudgetHrsCategory CRUD Views ---

class BudgetHrsCategoryTablePartialView(ListView):
    """
    Returns the HTML partial for the BudgetHrsCategory table, for HTMX updates.
    """
    model = BudgetHrsCategory
    template_name = 'mis_transaction/budgethrscategory/_budgethrscategory_table.html'
    context_object_name = 'categories'
    # The original ASP.NET had 'where Id!=1' in its main select.
    # We will include ID 1 in the list for editing/deleting if it exists,
    # but the primary creation form should respect the original intent if possible.
    # For simplicity of display in DataTables, we will show all for now.
    # If category with Id=1 needs to be completely hidden, filter in queryset.
    queryset = BudgetHrsCategory.objects.all().order_by('-id')


class BudgetHrsCategoryCreateView(CreateView):
    """
    Handles creation of new BudgetHrsCategory instances via HTMX modal.
    """
    model = BudgetHrsCategory
    form_class = BudgetHrsCategoryForm
    template_name = 'mis_transaction/budgethrscategory/_budgethrscategory_form.html'
    
    def form_valid(self, form):
        try:
            # Business logic moved to model
            form.instance = BudgetHrsCategory.create_category(
                category=form.cleaned_data['category'],
                symbol=form.cleaned_data['symbol']
            )
            messages.success(self.request, 'Budget Hours Category added successfully.')
            # HTMX response to close modal and refresh list
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsCategoryList'})
        except IntegrityError:
            form.add_error(None, "A category with this name or symbol already exists.")
            return self.form_invalid(form) # Re-render form with errors
        except ValidationError as e:
            form.add_error(None, e.messages[0])
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For HTMX, return the form with errors for re-rendering in modal
        return self.render_to_response(self.get_context_data(form=form))


class BudgetHrsCategoryUpdateView(UpdateView):
    """
    Handles updating existing BudgetHrsCategory instances via HTMX modal.
    """
    model = BudgetHrsCategory
    form_class = BudgetHrsCategoryForm
    template_name = 'mis_transaction/budgethrscategory/_budgethrscategory_form.html'
    context_object_name = 'category'

    def form_valid(self, form):
        try:
            # Business logic moved to model
            form.instance.update_category(
                category=form.cleaned_data['category'],
                symbol=form.cleaned_data['symbol']
            )
            messages.success(self.request, 'Budget Hours Category updated successfully.')
            # HTMX response to close modal and refresh list
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsCategoryList'})
        except IntegrityError:
            form.add_error(None, "A category with this name or symbol already exists.")
            return self.form_invalid(form) # Re-render form with errors
        except ValidationError as e:
            form.add_error(None, e.messages[0])
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For HTMX, return the form with errors for re-rendering in modal
        return self.render_to_response(self.get_context_data(form=form))


class BudgetHrsCategoryDeleteView(DeleteView):
    """
    Handles deletion of BudgetHrsCategory instances via HTMX modal.
    """
    model = BudgetHrsCategory
    template_name = 'mis_transaction/budgethrscategory/_budgethrscategory_confirm_delete.html'
    context_object_name = 'category'

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        try:
            # Business logic moved to model
            obj.delete_category()
            messages.success(self.request, 'Budget Hours Category deleted successfully.')
            # HTMX response to close modal and refresh list
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsCategoryList'})
        except Exception as e:
            messages.error(self.request, f'Error deleting category: {e}')
            # If deletion fails, perhaps re-render the modal with an error
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsCategoryList'}) # Still try to refresh list


# --- BudgetHrsSubCategory CRUD Views ---

class BudgetHrsSubCategoryTablePartialView(ListView):
    """
    Returns the HTML partial for the BudgetHrsSubCategory table, for HTMX updates.
    """
    model = BudgetHrsSubCategory
    template_name = 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_table.html'
    context_object_name = 'subcategories'
    queryset = BudgetHrsSubCategory.get_all_subcategories_with_category_names() # Use model manager method


class BudgetHrsSubCategoryCreateView(CreateView):
    """
    Handles creation of new BudgetHrsSubCategory instances via HTMX modal.
    """
    model = BudgetHrsSubCategory
    form_class = BudgetHrsSubCategoryForm
    template_name = 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_form.html'
    
    def form_valid(self, form):
        try:
            # Business logic moved to model
            form.instance = BudgetHrsSubCategory.create_subcategory(
                mid=form.cleaned_data['category'].id, # Access ID from selected category object
                subcategory=form.cleaned_data['subcategory'],
                symbol=form.cleaned_data['symbol']
            )
            messages.success(self.request, 'Budget Hours Sub-Category added successfully.')
            # HTMX response to close modal and refresh list
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsSubCategoryList'})
        except IntegrityError:
            form.add_error(None, "A sub-category with this name or symbol already exists within this category.")
            return self.form_invalid(form)
        except ValidationError as e:
            form.add_error(None, e.messages[0]) # From model's clean method
            return self.form_invalid(form)


    def form_invalid(self, form):
        # For HTMX, return the form with errors for re-rendering in modal
        return self.render_to_response(self.get_context_data(form=form))


class BudgetHrsSubCategoryUpdateView(UpdateView):
    """
    Handles updating existing BudgetHrsSubCategory instances via HTMX modal.
    """
    model = BudgetHrsSubCategory
    form_class = BudgetHrsSubCategoryForm
    template_name = 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_form.html'
    context_object_name = 'subcategory'

    def form_valid(self, form):
        try:
            # Business logic moved to model
            form.instance.update_subcategory(
                mid=form.cleaned_data['category'].id,
                subcategory=form.cleaned_data['subcategory'],
                symbol=form.cleaned_data['symbol']
            )
            messages.success(self.request, 'Budget Hours Sub-Category updated successfully.')
            # HTMX response to close modal and refresh list
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsSubCategoryList'})
        except IntegrityError:
            form.add_error(None, "A sub-category with this name or symbol already exists within this category.")
            return self.form_invalid(form)
        except ValidationError as e:
            form.add_error(None, e.messages[0])
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For HTMX, return the form with errors for re-rendering in modal
        return self.render_to_response(self.get_context_data(form=form))


class BudgetHrsSubCategoryDeleteView(DeleteView):
    """
    Handles deletion of BudgetHrsSubCategory instances via HTMX modal.
    """
    model = BudgetHrsSubCategory
    template_name = 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_confirm_delete.html'
    context_object_name = 'subcategory'

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        try:
            # Business logic moved to model
            obj.delete_subcategory()
            messages.success(self.request, 'Budget Hours Sub-Category deleted successfully.')
            # HTMX response to close modal and refresh list
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsSubCategoryList'})
        except Exception as e:
            messages.error(self.request, f'Error deleting sub-category: {e}')
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetHrsSubCategoryList'})

```

#### 4.4 Templates

Templates are organized within `mis_transaction/` directory.

**`mis_transaction/budgethrsfields/index.html`** (Main page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Field Category Section -->
        <div>
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold text-gray-800">Field Category</h2>
                <button 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'budgethrscategory_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Category
                </button>
            </div>
            
            <div id="budgethrscategoryTable-container"
                 hx-trigger="load, refreshBudgetHrsCategoryList from:body"
                 hx-get="{% url 'budgethrscategory_table' %}"
                 hx-swap="innerHTML"
                 class="bg-white rounded-lg shadow overflow-hidden">
                <!-- Data Table will be loaded here via HTMX -->
                <div class="p-6 text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Categories...</p>
                </div>
            </div>
            <p id="categoryMessage" class="text-red-500 text-sm mt-2"></p>
        </div>

        <!-- Field Sub-Category Section -->
        <div>
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold text-gray-800">Field Sub-Category</h2>
                <button 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'budgethrssubcategory_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Sub-Category
                </button>
            </div>
            
            <div id="budgethrssubcategoryTable-container"
                 hx-trigger="load, refreshBudgetHrsSubCategoryList from:body"
                 hx-get="{% url 'budgethrssubcategory_table' %}"
                 hx-swap="innerHTML"
                 class="bg-white rounded-lg shadow overflow-hidden">
                <!-- Data Table will be loaded here via HTMX -->
                <div class="p-6 text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Sub-Categories...</p>
                </div>
            </div>
            <p id="subcategoryMessage" class="text-red-500 text-sm mt-2"></p>
        </div>
    </div>
    
    <!-- Modals for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-auto"
             @htmx:after-request="if (event.detail.successful) { show = true }"
             @htmx:before-swap="if (!event.detail.successful) { show = false; alert('Error loading form.'); event.detail.shouldSwap = false; }">
            <!-- Form/Delete Confirmation Partial will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js for modal management
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });

    // Handle messages (e.g., from Django's messages framework)
    document.body.addEventListener('htmx:afterSwap', function(event) {
        const messagesDiv = document.getElementById('messages');
        if (messagesDiv) {
            messagesDiv.innerHTML = ''; // Clear existing messages
            // You might need a custom HTMX header for Django messages to be injected here
            // or fetch them with another HTMX request if necessary.
            // For now, assuming they are rendered in base.html and cleared on full page loads.
        }
    });

    // Global event listener for HTMX triggers to control modal visibility
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent' && evt.detail.successful) {
            // After form/delete content is loaded into modalContent
            document.getElementById('modal').classList.add('is-active');
            Alpine.store('modal').open();
        }
    });

    // Close modal on successful form submission (handled by HX-Trigger returning 204)
    document.body.addEventListener('refreshBudgetHrsCategoryList', function() {
        document.getElementById('modal').classList.remove('is-active');
        Alpine.store('modal').close();
    });
    document.body.addEventListener('refreshBudgetHrsSubCategoryList', function() {
        document.getElementById('modal').classList.remove('is-active');
        Alpine.store('modal').close();
    });

    // Optionally, handle form errors to keep modal open
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target.closest('#modalContent') && event.detail.xhr.status !== 204) {
            // If the request was for a modal form and it was not a success (204 No Content),
            // it means form validation failed, and the form with errors was swapped back.
            // So, keep the modal open.
            document.getElementById('modal').classList.add('is-active');
            Alpine.store('modal').open();
        }
    });

</script>
{% endblock %}
```

**`mis_transaction/budgethrscategory/_budgethrscategory_table.html`** (Category Table Partial)

```html
<div class="overflow-x-auto">
    <table id="budgethrscategoryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol Code</th>
                <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in categories %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.category }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.symbol }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3 inline-flex items-center"
                        hx-get="{% url 'budgethrscategory_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 inline-flex items-center"
                        hx-get="{% url 'budgethrscategory_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-3 px-6 text-center text-sm text-gray-500">No categories found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#budgethrscategoryTable')) {
            $('#budgethrscategoryTable').DataTable().destroy();
        }
        $('#budgethrscategoryTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false, // Disable auto-width to allow Tailwind to control sizing
            "columnDefs": [
                { "orderable": false, "targets": [0, 3] } // SN and Actions columns not sortable
            ],
        });
    });
</script>
```

**`mis_transaction/budgethrscategory/_budgethrscategory_form.html`** (Category Form Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Budget Hours Category</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                <div class="mt-1">
                    {{ field }}
                </div>
                {% if field.errors %}
                <p class="mt-2 text-sm text-red-600">{{ field.errors.as_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="mt-2 text-sm text-red-600">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Category
            </button>
        </div>
    </form>
</div>
```

**`mis_transaction/budgethrscategory/_budgethrscategory_confirm_delete.html`** (Category Delete Confirmation Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the category: <strong>"{{ category.category }}"</strong>?</p>
    <form hx-post="{% url 'budgethrscategory_delete' category.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`mis_transaction/budgethrssubcategory/_budgethrssubcategory_table.html`** (Sub-Category Table Partial)

```html
<div class="overflow-x-auto">
    <table id="budgethrssubcategoryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub-Category</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in subcategories %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.category.category }}</td> {# Access category name via FK #}
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.subcategory }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.symbol }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3 inline-flex items-center"
                        hx-get="{% url 'budgethrssubcategory_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 inline-flex items-center"
                        hx-get="{% url 'budgethrssubcategory_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-3 px-6 text-center text-sm text-gray-500">No sub-categories found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#budgethrssubcategoryTable')) {
            $('#budgethrssubcategoryTable').DataTable().destroy();
        }
        $('#budgethrssubcategoryTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false, // Disable auto-width to allow Tailwind to control sizing
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // SN and Actions columns not sortable
            ],
        });
    });
</script>
```

**`mis_transaction/budgethrssubcategory/_budgethrssubcategory_form.html`** (Sub-Category Form Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Budget Hours Sub-Category</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                <div class="mt-1">
                    {{ field }}
                </div>
                {% if field.errors %}
                <p class="mt-2 text-sm text-red-600">{{ field.errors.as_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="mt-2 text-sm text-red-600">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Sub-Category
            </button>
        </div>
    </form>
</div>
```

**`mis_transaction/budgethrssubcategory/_budgethrssubcategory_confirm_delete.html`** (Sub-Category Delete Confirmation Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the sub-category: <strong>"{{ subcategory.subcategory }}"</strong> (Category: {{ subcategory.category.category }})?</p>
    <form hx-post="{% url 'budgethrssubcategory_delete' subcategory.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`mis_transaction/urls.py`)

This file defines the URL patterns for your Django application.

```python
from django.urls import path
from .views import (
    BudgetHrsFieldsIndexView,
    BudgetHrsCategoryTablePartialView,
    BudgetHrsCategoryCreateView,
    BudgetHrsCategoryUpdateView,
    BudgetHrsCategoryDeleteView,
    BudgetHrsSubCategoryTablePartialView,
    BudgetHrsSubCategoryCreateView,
    BudgetHrsSubCategoryUpdateView,
    BudgetHrsSubCategoryDeleteView,
)

urlpatterns = [
    # Main entry point mirroring the original ASP.NET page
    path('budgethrsfields/', BudgetHrsFieldsIndexView.as_view(), name='budgethrsfields_index'),

    # HTMX endpoints for BudgetHrsCategory
    path('budgethrsfields/category/table/', BudgetHrsCategoryTablePartialView.as_view(), name='budgethrscategory_table'),
    path('budgethrsfields/category/add/', BudgetHrsCategoryCreateView.as_view(), name='budgethrscategory_add'),
    path('budgethrsfields/category/edit/<int:pk>/', BudgetHrsCategoryUpdateView.as_view(), name='budgethrscategory_edit'),
    path('budgethrsfields/category/delete/<int:pk>/', BudgetHrsCategoryDeleteView.as_view(), name='budgethrscategory_delete'),

    # HTMX endpoints for BudgetHrsSubCategory
    path('budgethrsfields/subcategory/table/', BudgetHrsSubCategoryTablePartialView.as_view(), name='budgethrssubcategory_table'),
    path('budgethrsfields/subcategory/add/', BudgetHrsSubCategoryCreateView.as_view(), name='budgethrssubcategory_add'),
    path('budgethrsfields/subcategory/edit/<int:pk>/', BudgetHrsSubCategoryUpdateView.as_view(), name='budgethrssubcategory_edit'),
    path('budgethrsfields/subcategory/delete/<int:pk>/', BudgetHrsSubCategoryDeleteView.as_view(), name='budgethrssubcategory_delete'),
]

```

#### 4.6 Tests (`mis_transaction/tests.py`)

Comprehensive tests cover both model business logic and view interactions, including HTMX-specific responses.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
from django.core.exceptions import ValidationError

from .models import BudgetHrsCategory, BudgetHrsSubCategory

class BudgetHrsCategoryModelTest(TestCase):
    """
    Unit tests for the BudgetHrsCategory model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.category1 = BudgetHrsCategory.objects.create(category='Test Category 1', symbol='TC1')
        cls.category2 = BudgetHrsCategory.objects.create(category='Test Category 2', symbol='TC2')
        # Create a special category with ID 1 if it doesn't exist naturally, for subcategory testing
        try:
            cls.category_id_1 = BudgetHrsCategory.objects.create(id=1, category='Special Category', symbol='SPCL')
        except IntegrityError:
            # If ID 1 already exists (e.g., from previous test data), just get it
            cls.category_id_1 = BudgetHrsCategory.objects.get(id=1)

    def test_category_creation(self):
        """Tests that a category can be created successfully."""
        category_count = BudgetHrsCategory.objects.count()
        new_category = BudgetHrsCategory.create_category(category='New Category', symbol='NC')
        self.assertEqual(BudgetHrsCategory.objects.count(), category_count + 1)
        self.assertEqual(new_category.category, 'New Category')
        self.assertEqual(new_category.symbol, 'NC')

    def test_category_update(self):
        """Tests updating an existing category."""
        self.category1.update_category(category='Updated Category 1', symbol='UTC1')
        self.category1.refresh_from_db()
        self.assertEqual(self.category1.category, 'Updated Category 1')
        self.assertEqual(self.category1.symbol, 'UTC1')

    def test_category_delete(self):
        """Tests deleting a category."""
        category_count = BudgetHrsCategory.objects.count()
        self.category2.delete_category()
        self.assertEqual(BudgetHrsCategory.objects.count(), category_count - 1)
        self.assertFalse(BudgetHrsCategory.objects.filter(id=self.category2.id).exists())

    def test_duplicate_category_creation(self):
        """Tests that creating a category with duplicate name or symbol raises an error."""
        with self.assertRaises(IntegrityError):
            BudgetHrsCategory.create_category(category='Test Category 1', symbol='Unique') # Duplicate name
        with self.assertRaises(IntegrityError):
            BudgetHrsCategory.create_category(category='Unique Category', symbol='TC1') # Duplicate symbol
    
    def test_get_all_categories_excluding_id(self):
        """Tests the manager method to exclude a specific ID."""
        categories = BudgetHrsCategory.get_all_categories(exclude_id=1)
        self.assertFalse(categories.filter(id=1).exists())
        self.assertTrue(categories.filter(id=self.category1.id).exists())

class BudgetHrsSubCategoryModelTest(TestCase):
    """
    Unit tests for the BudgetHrsSubCategory model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create categories for subcategory FKs
        cls.category_main = BudgetHrsCategory.objects.create(category='Main Category', symbol='MC')
        cls.category_other = BudgetHrsCategory.objects.create(category='Other Category', symbol='OC')
        try:
            cls.category_id_1 = BudgetHrsCategory.objects.create(id=1, category='Special Category', symbol='SPCL')
        except IntegrityError:
            cls.category_id_1 = BudgetHrsCategory.objects.get(id=1)

        cls.subcategory1 = BudgetHrsSubCategory.objects.create(
            category=cls.category_main, subcategory='SubCat A', symbol='SCA'
        )
        cls.subcategory2 = BudgetHrsSubCategory.objects.create(
            category=cls.category_main, subcategory='SubCat B', symbol='SCB'
        )

    def test_subcategory_creation(self):
        """Tests that a subcategory can be created successfully."""
        sub_count = BudgetHrsSubCategory.objects.count()
        new_sub = BudgetHrsSubCategory.create_subcategory(
            mid=self.category_other.id, subcategory='New Sub', symbol='NS'
        )
        self.assertEqual(BudgetHrsSubCategory.objects.count(), sub_count + 1)
        self.assertEqual(new_sub.subcategory, 'New Sub')
        self.assertEqual(new_sub.category, self.category_other)

    def test_subcategory_update(self):
        """Tests updating an existing subcategory."""
        self.subcategory1.update_subcategory(
            subcategory='Updated SubCat A', symbol='USCA', mid=self.category_other.id
        )
        self.subcategory1.refresh_from_db()
        self.assertEqual(self.subcategory1.subcategory, 'Updated SubCat A')
        self.assertEqual(self.subcategory1.symbol, 'USCA')
        self.assertEqual(self.subcategory1.category, self.category_other)

    def test_subcategory_delete(self):
        """Tests deleting a subcategory."""
        sub_count = BudgetHrsSubCategory.objects.count()
        self.subcategory2.delete_subcategory()
        self.assertEqual(BudgetHrsSubCategory.objects.count(), sub_count - 1)
        self.assertFalse(BudgetHrsSubCategory.objects.filter(id=self.subcategory2.id).exists())

    def test_create_subcategory_with_restricted_category_id(self):
        """Tests validation for creating subcategory with category ID 1."""
        with self.assertRaises(ValidationError) as cm:
            BudgetHrsSubCategory.create_subcategory(
                mid=self.category_id_1.id, subcategory='Restricted Sub', symbol='RS'
            )
        self.assertIn('Category "Id=1" cannot be selected for sub-categories.', cm.exception.messages)

    def test_update_subcategory_to_restricted_category_id(self):
        """Tests validation for updating subcategory to category ID 1."""
        temp_sub = BudgetHrsSubCategory.objects.create(category=self.category_main, subcategory='Temp Sub', symbol='TS')
        with self.assertRaises(ValidationError) as cm:
            temp_sub.update_subcategory(mid=self.category_id_1.id)
        self.assertIn('Category "Id=1" cannot be selected for sub-categories.', cm.exception.messages)
        temp_sub.delete() # Clean up

    def test_duplicate_subcategory_creation(self):
        """Tests that creating a subcategory with duplicate name or symbol raises an error."""
        with self.assertRaises(IntegrityError):
            BudgetHrsSubCategory.create_subcategory(
                mid=self.category_main.id, subcategory='SubCat A', symbol='Unique'
            ) # Duplicate subcategory name within same category
        with self.assertRaises(IntegrityError):
            BudgetHrsSubCategory.create_subcategory(
                mid=self.category_main.id, subcategory='Unique Sub', symbol='SCA'
            ) # Duplicate symbol within same category

    def test_get_all_subcategories_with_category_names(self):
        """Tests the manager method to retrieve subcategories with linked category names."""
        subcategories = BudgetHrsSubCategory.get_all_subcategories_with_category_names()
        self.assertGreaterEqual(len(subcategories), 2)
        # Ensure category object is pre-fetched
        self.assertIsInstance(subcategories[0].category, BudgetHrsCategory)
        self.assertEqual(subcategories[0].category.category, self.category_main.category)


class BudgetHrsViewsTest(TestCase):
    """
    Integration tests for BudgetHrsCategory and BudgetHrsSubCategory views.
    """
    def setUp(self):
        self.client = Client()
        self.category1 = BudgetHrsCategory.objects.create(category='Test Cat A', symbol='TCA')
        self.category2 = BudgetHrsCategory.objects.create(category='Test Cat B', symbol='TCB')
        try:
            self.category_id_1 = BudgetHrsCategory.objects.create(id=1, category='Special Category For Tests', symbol='SPCLT')
        except IntegrityError:
            self.category_id_1 = BudgetHrsCategory.objects.get(id=1)
            # Ensure it's not ID 1 if already exists, as we need a real test category
            if self.category_id_1.category != 'Special Category For Tests':
                self.category_id_1 = BudgetHrsCategory.objects.create(category='Special Category For Tests', symbol='SPCLT') # Create a new one if ID 1 taken by non-test data

        self.subcategory1 = BudgetHrsSubCategory.objects.create(
            category=self.category1, subcategory='Test Sub A', symbol='TSA'
        )
        self.subcategory2 = BudgetHrsSubCategory.objects.create(
            category=self.category2, subcategory='Test Sub B', symbol='TSB'
        )

    # --- BudgetHrsFieldsIndexView Test ---
    def test_index_view_get(self):
        """Tests the main index page loads correctly."""
        response = self.client.get(reverse('budgethrsfields_index'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrsfields/index.html')
        self.assertContains(response, 'Field Category')
        self.assertContains(response, 'Field Sub-Category')


    # --- BudgetHrsCategory Views Tests ---
    def test_category_table_partial_view_get(self):
        """Tests that the category table partial loads correctly via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrscategory_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrscategory/_budgethrscategory_table.html')
        self.assertContains(response, self.category1.category) # Check if category is in table

    def test_category_create_view_get(self):
        """Tests GET request for category creation form."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrscategory_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrscategory/_budgethrscategory_form.html')
        self.assertContains(response, 'Add Budget Hours Category') # Check form title

    def test_category_create_view_post_success(self):
        """Tests successful POST request for category creation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'category': 'New Category From Form', 'symbol': 'NCFF'}
        response = self.client.post(reverse('budgethrscategory_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertTrue(BudgetHrsCategory.objects.filter(category='New Category From Form').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetHrsCategoryList')

    def test_category_create_view_post_failure(self):
        """Tests failed POST request for category creation (validation error)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'category': self.category1.category, 'symbol': 'Unique'} # Duplicate category name
        response = self.client.post(reverse('budgethrscategory_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'mis_transaction/budgethrscategory/_budgethrscategory_form.html')
        self.assertContains(response, 'A category with this name or symbol already exists.') # Check for error message
        self.assertFalse(response.has_header('HX-Trigger')) # No trigger on validation failure

    def test_category_update_view_get(self):
        """Tests GET request for category update form."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrscategory_edit', args=[self.category1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrscategory/_budgethrscategory_form.html')
        self.assertContains(response, 'Edit Budget Hours Category')
        self.assertContains(response, self.category1.category) # Form should contain existing data

    def test_category_update_view_post_success(self):
        """Tests successful POST request for category update."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'category': 'Updated Category Name', 'symbol': self.category1.symbol} # Update name, keep symbol
        response = self.client.post(reverse('budgethrscategory_edit', args=[self.category1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.category1.refresh_from_db()
        self.assertEqual(self.category1.category, 'Updated Category Name')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetHrsCategoryList')

    def test_category_delete_view_get(self):
        """Tests GET request for category delete confirmation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrscategory_delete', args=[self.category1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrscategory/_budgethrscategory_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.category1.category)

    def test_category_delete_view_post_success(self):
        """Tests successful POST request for category deletion."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        category_pk = self.category1.pk
        response = self.client.post(reverse('budgethrscategory_delete', args=[category_pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BudgetHrsCategory.objects.filter(pk=category_pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetHrsCategoryList')


    # --- BudgetHrsSubCategory Views Tests ---
    def test_subcategory_table_partial_view_get(self):
        """Tests that the subcategory table partial loads correctly via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrssubcategory_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_table.html')
        self.assertContains(response, self.subcategory1.subcategory) # Check if subcategory is in table

    def test_subcategory_create_view_get(self):
        """Tests GET request for subcategory creation form."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrssubcategory_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_form.html')
        self.assertContains(response, 'Add Budget Hours Sub-Category')

    def test_subcategory_create_view_post_success(self):
        """Tests successful POST request for subcategory creation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'category': self.category2.pk,
            'subcategory': 'New SubCat From Form',
            'symbol': 'NSCFF'
        }
        response = self.client.post(reverse('budgethrssubcategory_add'), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue(BudgetHrsSubCategory.objects.filter(subcategory='New SubCat From Form').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetHrsSubCategoryList')

    def test_subcategory_create_view_post_restricted_category_failure(self):
        """Tests failed POST request for subcategory creation with restricted category ID 1."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'category': self.category_id_1.pk, # Category with ID 1
            'subcategory': 'SubCat With ID 1',
            'symbol': 'SW1'
        }
        response = self.client.post(reverse('budgethrssubcategory_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_form.html')
        self.assertContains(response, 'Category "Id=1" cannot be selected for sub-categories.')
        self.assertFalse(response.has_header('HX-Trigger'))

    def test_subcategory_update_view_post_restricted_category_failure(self):
        """Tests failed POST request for subcategory update to restricted category ID 1."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'category': self.category_id_1.pk, # Try to update to category ID 1
            'subcategory': self.subcategory1.subcategory,
            'symbol': self.subcategory1.symbol
        }
        response = self.client.post(reverse('budgethrssubcategory_edit', args=[self.subcategory1.pk]), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_form.html')
        self.assertContains(response, 'Category "Id=1" cannot be selected for sub-categories.')
        self.assertFalse(response.has_header('HX-Trigger'))

    def test_subcategory_update_view_get(self):
        """Tests GET request for subcategory update form."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrssubcategory_edit', args=[self.subcategory1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_form.html')
        self.assertContains(response, 'Edit Budget Hours Sub-Category')
        self.assertContains(response, self.subcategory1.subcategory)

    def test_subcategory_update_view_post_success(self):
        """Tests successful POST request for subcategory update."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'category': self.category1.pk, # Keep same category or change to a valid one
            'subcategory': 'Updated SubCat Name',
            'symbol': 'USCN'
        }
        response = self.client.post(reverse('budgethrssubcategory_edit', args=[self.subcategory1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.subcategory1.refresh_from_db()
        self.assertEqual(self.subcategory1.subcategory, 'Updated SubCat Name')
        self.assertEqual(self.subcategory1.symbol, 'USCN')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetHrsSubCategoryList')

    def test_subcategory_delete_view_get(self):
        """Tests GET request for subcategory delete confirmation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budgethrssubcategory_delete', args=[self.subcategory1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_transaction/budgethrssubcategory/_budgethrssubcategory_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.subcategory1.subcategory)

    def test_subcategory_delete_view_post_success(self):
        """Tests successful POST request for subcategory deletion."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        subcategory_pk = self.subcategory1.pk
        response = self.client.post(reverse('budgethrssubcategory_delete', args=[subcategory_pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BudgetHrsSubCategory.objects.filter(pk=subcategory_pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetHrsSubCategoryList')
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic updates:**
    *   The main `index.html` uses `hx-get` to initially load and periodically refresh the category and sub-category tables (`_budgethrscategory_table.html`, `_budgethrssubcategory_table.html`) into their respective `div` containers.
    *   Add/Edit/Delete buttons within the tables use `hx-get` to fetch the relevant form or confirmation partial into the `#modalContent` div.
    *   Form submissions (`hx-post`) on the modal forms trigger the corresponding Django `CreateView` or `UpdateView`. On successful submission, these views return a `204 No Content` status with an `HX-Trigger` header (e.g., `refreshBudgetHrsCategoryList`). This event is listened for in the main template to close the modal and re-trigger the table `hx-get`, thus refreshing the data.
    *   Error handling for forms returns the form partial with errors, allowing HTMX to swap it back into the modal for user correction.

*   **Alpine.js for UI state management:**
    *   A simple Alpine.js component manages the visibility of the modal (`x-data="{ show: false }"` and `x-show="show"`).
    *   `hx-trigger="click _=on click add .is-active to #modal"` combines HTMX and Alpine.js to open the modal.
    *   The `hx-post` responses (e.g., `status=204` with `HX-Trigger`) close the modal and refresh the table.
    *   The `on click if event.target.id == 'modal' remove .is-active from me` directive allows clicking outside the modal content to close it.

*   **DataTables for List Views:**
    *   Each table partial (`_budgethrscategory_table.html`, `_budgethrssubcategory_table.html`) includes the JavaScript for DataTables initialization.
    *   The `$(document).ready()` block ensures DataTables is initialized every time the partial is loaded and swapped into the DOM by HTMX. It also includes a `destroy()` call to prevent re-initialization errors if the table was previously rendered.
    *   Tailwind CSS is used for basic table styling.

*   **DRY Templates:**
    *   `core/base.html` (assumed to exist) handles all global CDN links for DataTables, HTMX, Alpine.js, and Tailwind CSS.
    *   Partial templates (`_*.html`) are used for reusable components like forms and tables, loaded dynamically via HTMX.

This comprehensive plan ensures a smooth, automated, and maintainable transition from your legacy ASP.NET application to a modern Django solution, providing significant long-term business value.