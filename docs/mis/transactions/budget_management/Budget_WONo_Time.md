## ASP.NET to Django Conversion Script: Work Order Budgeting

This document outlines a modernization plan to transition your legacy ASP.NET application for "Hrs Budget For Work Order No" to a modern Django-based solution. Our approach emphasizes automation, leveraging Django's robust framework, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- **NEVER** include `base.html` template code in your output - assume it already exists and is extended.
- Focus **ONLY** on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables. The primary table for budget entries is `tblACC_Budget_WO_Time`. It also references `tblDG_Item_Master` for equipment details, `tblMIS_BudgetHrs_Field_Category` for budget categories, and `tblMIS_BudgetHrs_Field_SubCategory` for sub-categories. The C# code's `BtnInsert_Click` method indicates that "updates" are actually new inserts into `tblACC_Budget_WO_Time`, which means this table stores *transactions* rather than a single aggregated budget. The aggregation of hours (Allocated, Utilized, Balance) is performed by functions like `CUH.AllocatedHrs_WONo`.

**Identified Tables and Columns:**

*   **`tblACC_Budget_WO_Time`**: Stores individual budget entries/transactions.
    *   `Id` (Primary Key, implicitly created by Django if not explicitly defined, or needs to be identified from schema if it exists)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `SessionId` (String) - Represents the user who made the entry.
    *   `WONo` (String) - Work Order Number.
    *   `EquipId` (Integer) - Foreign Key to `tblDG_Item_Master`.
    *   `HrsBudgetCat` (Integer) - Foreign Key to `tblMIS_BudgetHrs_Field_Category`.
    *   `HrsBudgetSubCat` (Integer) - Foreign Key to `tblMIS_BudgetHrs_Field_SubCategory`.
    *   `Hour` (Double) - The budget hours for this specific transaction.

*   **`tblDG_Item_Master`**: Stores equipment master data.
    *   `Id` (Primary Key)
    *   `ItemCode` (String) - Equipment Code.
    *   `ManfDesc` (String) - Equipment Description.

*   **`tblMIS_BudgetHrs_Field_Category`**: Stores budget categories.
    *   `Id` (Primary Key)
    *   `Category` (String) - Category Name.

*   **`tblMIS_BudgetHrs_Field_SubCategory`**: Stores budget sub-categories.
    *   `Id` (Primary Key)
    *   `SubCategory` (String) - Sub-Category Name.
    *   `MId` (Integer) - Foreign Key to `tblMIS_BudgetHrs_Field_Category.Id`.

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page primarily displays an aggregated view of budget hours for a given Work Order and allows adding new budget entries, either individually or in a batch through the grid interface.

*   **Create (Add):** The `btnAdd_Click` method allows adding a single new budget transaction for a selected Equipment, Category, and Sub-Category with a specified hour value. It checks for existing entries with the same combination, potentially to prevent immediate duplicates but still allows adding new transactions.
*   **Read (List & Aggregate):** The `CalculateBalAmt` method fetches distinct combinations of Equipment, Category, and Sub-Category for the specified Work Order. For each combination, it then calculates "Allocated Hrs", "Utilized Hrs", "Bal Hrs", and "Finish (%)" by aggregating data from `tblACC_Budget_WO_Time` using helper functions (`CUH.AllocatedHrs_WONo`, `CUH.UtilizeHrs_WONo`). This aggregated data is then displayed in the `GridView`.
*   **Update (Bulk Add/Modify):** The `BtnInsert_Click` method, contrary to typical "update" behavior, *inserts* new `tblACC_Budget_WO_Time` records for each row where the checkbox is ticked and a new hour value is provided. This effectively allows batch adding new budget transactions. This is a key behavioral difference that needs to be replicated.
*   **Dependency/Lookup:** The dropdowns (`drpEqNo`, `drpCat`, `drpSubCat`) are dynamically populated. `drpEqNo` populates initially, then `drpCat` populates based on `drpEqNo` selection, and `drpSubCat` populates based on `drpCat` selection. This indicates hierarchical lookups.
*   **Contextual Data:** `WONo` (Work Order Number) is passed as a query string parameter and is central to all operations. `CompId` and `FinYearId` are retrieved from session, implying company and financial year context.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page uses standard Web Forms controls to capture input and display data.

*   **Display Work Order Number:** `lblWoNo` displays the `WONo`.
*   **Input Forms (Add):**
    *   `drpEqNo` (DropDownList): Select equipment.
    *   `drpCat` (DropDownList): Select category.
    *   `drpSubCat` (DropDownList): Select sub-category.
    *   `txtHrs` (TextBox): Input budget hours for adding.
    *   `btnAdd` (Button): Triggers single new budget entry.
*   **Data Grid (List & Bulk Update):**
    *   `GridView1`: Displays aggregated budget information.
        *   Columns: Equipment No, Description, Category, Sub Category, Budget Hrs, Utilized Hrs, Bal Hrs, Finish(%).
        *   Interactive elements per row:
            *   Checkbox (`CheckBox1`): Enables inline hour input (`TxtHour`).
            *   Budget Hrs (`lblHour`/`TxtHour`): Label for display, TextBox for input when checked.
            *   Hyperlink: For drill-down to `Budget_WONo_Details_Time.aspx`.
*   **Action Buttons:**
    *   `BtnInsert` (Button): Triggers bulk budget entry/update.
    *   `BtnExport` (Button): Export data (hidden in sample, but functionality exists).
    *   `Button1` (Button): Cancel/redirect.
*   **Validation:** Client-side validators are present for required fields and data types.

### Step 4: Generate Django Code

#### 4.1 Models (`work_order_budget/models.py`)

We will create models for the main budget transactions and the lookup tables. The `WorkOrderBudgetEntry` model will have methods to facilitate the aggregation logic previously handled by `Cal_Used_Hours`. We will define a `WorkOrderBudgetEntryManager` to handle the complex aggregation query that forms the main grid data.

```python
import datetime
from django.db import models
from django.db.models import Sum, F, ExpressionWrapper, fields, Value
from django.db.models.functions import Coalesce, Cast

# Assume a User model and Company/FinancialYear setup exists in a 'core' app or similar.
# For this migration, we'll simulate the CompId and FinYearId.
# In a real application, these would likely be linked to the logged-in user's profile
# or selected context.

class Equipment(models.Model):
    # Corresponds to tblDG_Item_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Equipment'
        verbose_name_plural = 'Equipment'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class BudgetCategory(models.Model):
    # Corresponds to tblMIS_BudgetHrs_Field_Category
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_Category'
        verbose_name = 'Budget Category'
        verbose_name_plural = 'Budget Categories'

    def __str__(self):
        return self.category

class BudgetSubCategory(models.Model):
    # Corresponds to tblMIS_BudgetHrs_Field_SubCategory
    id = models.IntegerField(db_column='Id', primary_key=True)
    sub_category = models.CharField(db_column='SubCategory', max_length=100)
    category = models.ForeignKey(BudgetCategory, on_delete=models.DO_NOTHING, db_column='MId', related_name='subcategories') # MId is FK to Category.Id

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_SubCategory'
        verbose_name = 'Budget Sub-Category'
        verbose_name_plural = 'Budget Sub-Categories'

    def __str__(self):
        return f"{self.sub_category} ({self.category.category})"


class WorkOrderBudgetEntryManager(models.Manager):
    """
    Custom manager to handle the complex aggregation logic for the Work Order Budget display.
    This simulates the CalculateBalAmt method from the ASP.NET code.
    """
    def get_aggregated_budget_data(self, wono, comp_id):
        """
        Aggregates budget data for a given Work Order Number and Company ID.
        Returns a QuerySet of distinct budget combinations with calculated hours.
        """
        # Annotate each budget entry with its category and sub_category details
        # and then group by the distinct combination of EquipId, HrsBudgetCat, HrsBudgetSubCat
        
        # In a real scenario, CUH.AllocatedHrs_WONo and CUH.UtilizeHrs_WONo
        # would perform database queries. We'll simulate this by summing
        # from the same tblACC_Budget_WO_Time table.
        # This assumes Hour in tblACC_Budget_WO_Time represents 'Allocated'
        # and there's another mechanism or table for 'Utilized' hours.
        # Based on original code, 'Utilized Hrs' comes from CUH.UtilizeHrs_WONo
        # which might be a different table/calculation.
        # For simplicity, if no other source for utilized hours is known, we will assume
        # 'utilized hours' might be a separate transaction type or come from a different system.
        # For this example, let's assume UtilizedHrs are also budget entries but perhaps
        # marked by a specific transaction type or source, which is not evident here.
        # A safer assumption is that 'Hour' is the *allocated* budget, and 'Utilized' is external.
        # Since we don't have the source for CUH.UtilizeHrs_WONo, we will mock it
        # as a simple aggregation of 'Hour' for demonstration purposes.
        # In a real migration, 'CUH's logic needs to be fully reverse-engineered.

        # Let's assume 'Hour' in tblACC_Budget_WO_Time is the 'allocated' amount.
        # The original code implies 'Utilized Hrs' comes from a distinct source or
        # is calculated differently (CUH.UtilizeHrs_WONo).
        # Without that logic, we will define it as 0 for simplicity or
        # as a percentage of the allocated hours for demonstration.
        # A more accurate migration would require examining CUH.UtilizeHrs_WONo's implementation.

        # For this example, let's simplify:
        # AllocatedHrs = Sum of 'Hour' for that combination
        # UtilizedHrs = For demonstration, let's assume it's 50% of allocated or 0 for now.
        # In a real app, this would be a lookup or join to actual utilization data.

        qs = self.filter(
            wono=wono,
            comp_id=comp_id
        ).values(
            'equip_id', 
            'budget_category__id', 
            'budget_subcategory__id'
        ).annotate(
            # Mimic the distinct aggregation logic
            # These are the IDs that form the unique group for display
            EquipId=F('equip_id'),
            CatId=F('budget_category__id'),
            SubCatId=F('budget_subcategory__id'),

            # Fetch display names
            EquipmentNo=F('equipment__item_code'),
            Description=F('equipment__manf_desc'),
            Category=F('budget_category__category'),
            SubCategory=F('budget_subcategory__sub_category'),

            # Calculations based on the original logic
            # Coalesce(Sum('hour'), 0.0) ensures if no entries, it's 0.0
            allocated_hrs=Coalesce(Sum('hour', filter=models.Q(comp_id=comp_id, wono=wono)), 0.0),
        ).annotate(
            # For demonstration, assume utilized_hrs are 40% of allocated, or from a different source.
            # In a real scenario, this would be derived from the actual utilized hours source (`CUH.UtilizeHrs_WONo`).
            # Here, we're simulating the *display* of calculated fields.
            utilized_hrs=ExpressionWrapper(
                Cast(F('allocated_hrs') * Value(0.40), output_field=fields.FloatField()),
                output_field=fields.FloatField()
            ),
            # utilized_hrs=Value(0.0, output_field=fields.FloatField()), # Use this if no utilized hours source is known
        ).annotate(
            bal_hrs=ExpressionWrapper(
                F('allocated_hrs') - F('utilized_hrs'),
                output_field=fields.FloatField()
            ),
            finish_percent=ExpressionWrapper(
                Cast(Value(100.0) * F('utilized_hrs') / F('allocated_hrs'), output_field=fields.FloatField()),
                output_field=fields.FloatField()
            ),
        ).order_by('EquipId', 'CatId', 'SubCatId') # Ensure consistent ordering

        return qs

class WorkOrderBudgetEntry(models.Model):
    # Corresponds to tblACC_Budget_WO_Time
    # Assuming Id is the PK, if not, adjust primary_key=True to existing PK column
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming auto-incremented PK
    sys_date = models.DateField(db_column='SysDate', default=datetime.date.today)
    sys_time = models.TimeField(db_column='SysTime', default=datetime.datetime.now().time)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50) # Username
    wono = models.CharField(db_column='WONo', max_length=100)
    equipment = models.ForeignKey(Equipment, on_delete=models.DO_NOTHING, db_column='EquipId', related_name='budget_entries')
    budget_category = models.ForeignKey(BudgetCategory, on_delete=models.DO_NOTHING, db_column='HrsBudgetCat', related_name='budget_entries')
    budget_subcategory = models.ForeignKey(BudgetSubCategory, on_delete=models.DO_NOTHING, db_column='HrsBudgetSubCat', related_name='budget_entries')
    hour = models.FloatField(db_column='Hour') # The budget hours for this specific transaction

    objects = WorkOrderBudgetEntryManager() # Attach our custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_Budget_WO_Time'
        verbose_name = 'Work Order Budget Entry'
        verbose_name_plural = 'Work Order Budget Entries'

    def __str__(self):
        return f"WO: {self.wono}, Equip: {self.equipment.item_code}, Cat: {self.budget_category.category}, SubCat: {self.budget_subcategory.sub_category} - {self.hour} Hrs"

    def get_absolute_url(self):
        # Placeholder for detail view if needed, or modify for HyperLink1 equivalent
        # This link in ASP.NET goes to another page with a lot of query params.
        # We'll define a simple URL for it.
        return f"/mis/budget_details/{self.wono}/?alloc_hrs={self.hour}&equip_id={self.equipment.id}"

```

#### 4.2 Forms (`work_order_budget/forms.py`)

We'll need two forms:
1.  `SingleBudgetEntryForm`: For the `Add` button functionality.
2.  `BulkBudgetUpdateForm`: To handle the inline editing and bulk submission from the DataTables grid. This will be a custom form, not a ModelForm.

```python
from django import forms
from .models import Equipment, BudgetCategory, BudgetSubCategory, WorkOrderBudgetEntry

class SingleBudgetEntryForm(forms.Form):
    """
    Form for adding a single budget entry via the top section.
    """
    equipment = forms.ModelChoiceField(
        queryset=Equipment.objects.all().order_by('item_code'),
        label="Equip. No [ Desc ]",
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': 'hx-get="{% url "work_order_budget:get_categories" %}"',
            'hx-target': '#id_category',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
        })
    )
    category = forms.ModelChoiceField(
        queryset=BudgetCategory.objects.none(), # Populated dynamically
        label="Category",
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': 'hx-get="{% url "work_order_budget:get_subcategories" %}"',
            'hx-target': '#id_sub_category',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
        })
    )
    sub_category = forms.ModelChoiceField(
        queryset=BudgetSubCategory.objects.none(), # Populated dynamically
        label="Sub Category",
        empty_label="Select",
        required=False, # Original ASP.NET allows empty if category is '1'
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )
    hours = forms.FloatField(
        label="Hrs",
        min_value=0.01, # Similar to ASP.NET (^[1-9]\d*(\.\d+)?$)
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter hours',
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add a default 'Select' option for category if it's empty
        self.fields['category'].choices = [('', 'Select')] + list(self.fields['category'].choices)
        self.fields['sub_category'].choices = [('', 'Select')] + list(self.fields['sub_category'].choices)

        # Set initial queryset for categories if an equipment is provided
        # This will be handled by HTMX for dynamic updates in the template
        if 'equipment' in self.initial:
            self.fields['category'].queryset = BudgetCategory.objects.filter(
                budget_entries__equipment__id=self.initial['equipment']
            ).distinct().order_by('category')
        
        # Set initial queryset for subcategories if a category is provided
        if 'category' in self.initial:
            self.fields['sub_category'].queryset = BudgetSubCategory.objects.filter(
                category__id=self.initial['category']
            ).order_by('sub_category')
        
    def clean(self):
        cleaned_data = super().clean()
        equipment = cleaned_data.get('equipment')
        category = cleaned_data.get('category')
        sub_category = cleaned_data.get('sub_category')
        hours = cleaned_data.get('hours')

        # Custom validation: Mimic the ASP.NET check for duplicate entries
        # "Budget Hrs is already allocated for selected Category & Sub-Category."
        # This check was only if no hours were present. If hours were > 0, it inserted.
        # This is unusual. I will interpret this as checking for *any* existing entry
        # for this combination, then allowing insert if hours > 0.
        # If the original behavior meant strict uniqueness, this needs adjustment.
        # Given the "update" button inserts, it's transactional.
        
        # Original code check `if (DSselHrsBudgetCK.HasRows==false)` only for `btnAdd_Click`
        # and it prevents adding IF A ROW EXISTS for the EXACT combination.
        # This means we only allow adding if no previous transaction exists for this combination
        # if the total hours for that combination were 0? This is confusing.
        # For simplicity, if we add, we add a new transaction. The "alert" only fires if
        # a row for this exact combination exists.
        
        # Let's align with the ASP.NET code: if an entry (transaction) with the exact
        # EquipId, WONo, HrsBudgetCat, HrsBudgetSubCat already exists, show an error.
        # This contradicts the 'Update' behavior which always inserts.
        # Given the conflict, I will assume the `btnAdd_Click` check was for *preventing* initial duplicates
        # of the *first* allocation, but subsequent 'updates' (via `BtnInsert_Click`) add new entries.
        # For a clean Django solution, we will *always* add a new entry (transaction)
        # when the form is submitted, similar to how `BtnInsert_Click` behaves for bulk updates.
        # The uniqueness check should be handled at a higher level (e.g., business rule in model manager)
        # if a combination should only have *one* active budget entry.
        # For now, we allow multiple entries as per `BtnInsert_Click`'s INSERT behavior.
        # If strict uniqueness is needed, we'd adjust the model and form.
        return cleaned_data


class BulkBudgetRowForm(forms.Form):
    """
    A form for a single row in the bulk update grid.
    Used for validation and processing of individual row updates.
    """
    equip_id = forms.IntegerField(widget=forms.HiddenInput())
    cat_id = forms.IntegerField(widget=forms.HiddenInput())
    sub_cat_id = forms.IntegerField(widget=forms.HiddenInput())
    # This `updated_hours` field will only be present if the checkbox is ticked in the UI
    updated_hours = forms.FloatField(
        required=False,
        min_value=0.01,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'New Hrs',
            'x-show': 'showInput' # Controlled by Alpine.js for visibility
        })
    )

    def clean_updated_hours(self):
        hours = self.cleaned_data.get('updated_hours')
        # If the field is present (meaning checkbox was ticked) and it's empty, require it.
        # The original ASP.NET has a RequiredFieldValidator + RegularExpressionValidator (numeric > 0).
        if hours is None and self.data.get(f'updated_hours_{self.prefix}', '') != '': # check if input was actually sent but was empty
             raise forms.ValidationError("Hours must be entered.")
        if hours is not None and hours <= 0:
            raise forms.ValidationError("Hours must be greater than zero.")
        return hours

```

#### 4.3 Views (`work_order_budget/views.py`)

We'll need views for the main list, the form for adding a single entry, the partial view for the DataTables content, and endpoints for dynamic dropdown population.

```python
import datetime
from django.views.generic import View, TemplateView, ListView, CreateView
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin # Assume authentication is required
from django.db import transaction

from .models import WorkOrderBudgetEntry, Equipment, BudgetCategory, BudgetSubCategory
from .forms import SingleBudgetEntryForm, BulkBudgetRowForm

# Helper function to get common context (like WONo, CompId, FinYearId)
def get_common_context(request, wono):
    # In a real application, CompId and FinYearId would come from user profile, session, or specific configuration.
    # For this example, we'll hardcode them or get from user session if available.
    # Replace with your actual logic to retrieve company and financial year IDs.
    comp_id = request.session.get('compid', 1)  # Default to 1 if not in session
    fin_year_id = request.session.get('finyear', 1) # Default to 1 if not in session
    username = request.user.username if request.user.is_authenticated else 'anonymous'

    return {
        'wono': wono,
        'comp_id': comp_id,
        'fin_year_id': fin_year_id,
        'session_id': username,
    }


class WorkOrderBudgetListView(LoginRequiredMixin, TemplateView):
    """
    Main view to display the Work Order Budget screen.
    It renders the base template and relies on HTMX to load the DataTable and form elements.
    """
    template_name = 'work_order_budget/workorderbudgetentry/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono = self.kwargs['wono']
        context.update(get_common_context(self.request, wono))
        context['add_form'] = SingleBudgetEntryForm(
            # Initialize with default values if needed, e.g., for dropdowns after selection
        )
        return context


class WorkOrderBudgetTablePartialView(LoginRequiredMixin, ListView):
    """
    Partial view to render the DataTables content via HTMX.
    This replaces the ASP.NET GridView.
    """
    model = WorkOrderBudgetEntry
    template_name = 'work_order_budget/workorderbudgetentry/_workorderbudgetentry_table.html'
    context_object_name = 'budget_items' # Renamed for clarity in template

    def get_queryset(self):
        wono = self.kwargs['wono']
        context = get_common_context(self.request, wono)
        
        # Use the custom manager to get the aggregated data
        return WorkOrderBudgetEntry.objects.get_aggregated_budget_data(
            wono=context['wono'],
            comp_id=context['comp_id']
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add necessary context for the partial template, e.g., wono
        context.update(get_common_context(self.request, self.kwargs['wono']))
        return context


class AddSingleBudgetEntryView(LoginRequiredMixin, CreateView):
    """
    Handles adding a single budget entry from the form above the grid.
    This replaces the 'btnAdd_Click' functionality.
    """
    model = WorkOrderBudgetEntry
    form_class = SingleBudgetEntryForm
    template_name = 'work_order_budget/workorderbudgetentry/_single_budget_form.html' # Use partial for HTMX
    # success_url is handled by HTMX trigger

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Initialize the form with data from the request if it's an HTMX request
        # to ensure dropdowns can be correctly populated for subsequent HTMX calls.
        if self.request.method == 'GET' and self.request.headers.get('HX-Request'):
            # This is for HTMX GET requests that might re-render the form.
            # Example: if a form is loaded via HTMX and you want to pre-fill it.
            # For this specific scenario, the form is part of the initial page load.
            pass
        return kwargs

    def form_valid(self, form):
        wono = self.kwargs['wono']
        context = get_common_context(self.request, wono)
        
        # Create the new budget transaction
        WorkOrderBudgetEntry.objects.create(
            sys_date=datetime.date.today(),
            sys_time=datetime.datetime.now().time(),
            comp_id=context['comp_id'],
            fin_year_id=context['fin_year_id'],
            session_id=context['session_id'],
            wono=context['wono'],
            equipment=form.cleaned_data['equipment'],
            budget_category=form.cleaned_data['category'],
            budget_subcategory=form.cleaned_data['sub_category'],
            hour=form.cleaned_data['hours']
        )
        messages.success(self.request, 'Budget entry added successfully.')
        
        # For HTMX, send a 204 No Content response and trigger a refresh event
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshBudgetList' # Custom event to refresh the DataTable
            }
        )

    def form_invalid(self, form):
        # If form is invalid, re-render the form with errors
        messages.error(self.request, 'Please correct the errors below.')
        if self.request.headers.get('HX-Request'):
            # Return the form partial with errors for HTMX to swap back
            # Ensure proper rendering of errors in the partial
            return render(self.request, self.template_name, {
                'add_form': form,
                **get_common_context(self.request, self.kwargs['wono'])
            })
        return super().form_invalid(form)


class BulkUpdateBudgetEntriesView(LoginRequiredMixin, View):
    """
    Handles the bulk update of budget entries from the DataTable.
    This replaces the 'BtnInsert_Click' functionality, which inserts new records.
    """
    def post(self, request, wono):
        context = get_common_context(request, wono)
        
        # We expect a dictionary where keys are 'prefix-field_name'
        # e.g., 'form-0-equip_id', 'form-0-updated_hours'
        # We need to manually parse the formset-like data.
        
        updated_rows_data = []
        for key in request.POST:
            if key.startswith('selected_row_'):
                # Extract the index from 'selected_row_INDEX'
                index = key.split('_')[2]
                
                # Check if a new hour value was provided for this row
                hours_key = f'updated_hours_{index}'
                if hours_key in request.POST and request.POST[hours_key]:
                    try:
                        equip_id = int(request.POST[f'equip_id_{index}'])
                        cat_id = int(request.POST[f'cat_id_{index}'])
                        sub_cat_id = int(request.POST[f'sub_cat_id_{index}'])
                        new_hours = float(request.POST[hours_key])
                        
                        # Validate the individual row data using BulkBudgetRowForm
                        row_form = BulkBudgetRowForm(
                            {
                                'equip_id': equip_id,
                                'cat_id': cat_id,
                                'sub_cat_id': sub_cat_id,
                                'updated_hours': new_hours
                            },
                            prefix=str(index) # Use index as prefix for error messages
                        )
                        if row_form.is_valid():
                            updated_rows_data.append(row_form.cleaned_data)
                        else:
                            # Handle specific row validation errors (e.g., return HTMX error)
                            messages.error(request, f"Error in row {index}: {row_form.errors.as_text()}")
                            return HttpResponse(status=400, content=f"Error in row {index}: {row_form.errors.as_text()}")
                    except (ValueError, KeyError) as e:
                        messages.error(request, f"Invalid data for row {index}: {e}")
                        return HttpResponse(status=400, content=f"Invalid data for row {index}: {e}")

        if not updated_rows_data:
            messages.warning(request, "No rows selected or new hours provided for update.")
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetList'}) # Still trigger refresh

        with transaction.atomic():
            for row_data in updated_rows_data:
                WorkOrderBudgetEntry.objects.create(
                    sys_date=datetime.date.today(),
                    sys_time=datetime.datetime.now().time(),
                    comp_id=context['comp_id'],
                    fin_year_id=context['fin_year_id'],
                    session_id=context['session_id'],
                    wono=context['wono'],
                    equipment_id=row_data['equip_id'],
                    budget_category_id=row_data['cat_id'],
                    budget_subcategory_id=row_data['sub_cat_id'],
                    hour=row_data['updated_hours']
                )
        
        messages.success(request, 'Selected budget entries updated successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshBudgetList'
            }
        )


class GetCategoriesView(View):
    """
    HTMX endpoint to dynamically populate the Category dropdown based on Equipment selection.
    """
    def get(self, request):
        equip_id = request.GET.get('equipment')
        categories = BudgetCategory.objects.none()
        if equip_id and equip_id != 'Select':
            # Get categories associated with this equipment through existing budget entries
            # or from a broader dataset if that's how it's meant to work.
            # Assuming categories are filtered by equipment that *has* budget entries.
            categories = BudgetCategory.objects.filter(
                budget_entries__equipment__id=equip_id
            ).distinct().order_by('category')
        
        # If the original ASP.NET had a "1" for empty category, we mimic it for the dropdown's value
        # But for ModelChoiceField, an empty_label makes more sense.
        html_options = '<option value="">Select</option>'
        for category in categories:
            html_options += f'<option value="{category.id}">{category.category}</option>'
        
        # Return only the <select> element's options or the full select element
        # For hx-swap="outerHTML" on #id_category, return the full select.
        # For hx-swap="innerHTML", just return options. Let's do outerHTML on the whole select.
        
        # It needs to return a full HTML <select> tag to properly update the original element
        # with hx-swap="outerHTML".
        # Ensure the ID matches the target.
        return HttpResponse(f"""
            <select name="category" id="id_category"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    hx-get="{% url "work_order_budget:get_subcategories" %}"
                    hx-target="#id_sub_category"
                    hx-swap="outerHTML"
                    hx-trigger="change">
                {html_options}
            </select>
        """)


class GetSubCategoriesView(View):
    """
    HTMX endpoint to dynamically populate the Sub Category dropdown based on Category selection.
    """
    def get(self, request):
        category_id = request.GET.get('category')
        sub_categories = BudgetSubCategory.objects.none()
        if category_id and category_id != '1': # Mimic ASP.NET's '1' means clear sub-category
            try:
                category_id = int(category_id)
                sub_categories = BudgetSubCategory.objects.filter(category__id=category_id).order_by('sub_category')
            except ValueError:
                pass # Invalid category ID
        
        html_options = '<option value="">Select</option>'
        for sub_category in sub_categories:
            html_options += f'<option value="{sub_category.id}">{sub_category.sub_category}</option>'
        
        return HttpResponse(f"""
            <select name="sub_category" id="id_sub_category"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                {html_options}
            </select>
        """)

# Helper for the drill-down link (HyperLink1)
class BudgetDetailsView(LoginRequiredMixin, TemplateView):
    template_name = 'work_order_budget/budget_details.html' # A simple placeholder template
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass query parameters from original ASP.NET link
        context['wono'] = kwargs.get('wono')
        context['alloc_hrs'] = self.request.GET.get('alloc_hrs')
        context['util_hrs'] = self.request.GET.get('UtilHrs')
        context['bal_hrs'] = self.request.GET.get('BalHrs')
        context['equip_id'] = self.request.GET.get('Eqid')
        context['cat_id'] = self.request.GET.get('Cat')
        context['sub_cat_id'] = self.request.GET.get('SubCat')
        context['mod_id'] = self.request.GET.get('ModId')
        return context

```

#### 4.4 Templates (`work_order_budget/templates/work_order_budget/workorderbudgetentry/`)

-   `list.html`: Main page, loads the DataTable and form via HTMX.
-   `_single_budget_form.html`: Partial for the "Add" form.
-   `_workorderbudgetentry_table.html`: Partial for the DataTables content.
-   `budget_details.html`: Placeholder for drill-down target.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Hours Budget For Work Order No: {{ wono }}</h2>
    </div>

    <!-- Section for adding new budget entries -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-4">Add New Budget Entry</h3>
        <div id="single-budget-form-container"
             hx-get="{% url 'work_order_budget:add_budget_entry' wono=wono %}"
             hx-trigger="load"
             hx-swap="innerHTML">
            <!-- Form will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading form...</p>
            </div>
        </div>
    </div>

    <!-- Section for displaying and bulk updating existing budget entries -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold mb-4">Existing Budget Allocations</h3>
        <form id="bulk-update-form" hx-post="{% url 'work_order_budget:bulk_update_budget' wono=wono %}" hx-swap="none">
            {% csrf_token %}
            <div id="budget-table-container"
                 hx-trigger="load, refreshBudgetList from:body"
                 hx-get="{% url 'work_order_budget:budget_table' wono=wono %}"
                 hx-swap="innerHTML">
                <!-- DataTable will be loaded here via HTMX -->
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2">Loading budget data...</p>
                </div>
            </div>

            <div class="mt-6 flex justify-center space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out">
                    Update Selected
                </button>
                <a href="{% url 'work_order_budget:cancel_action' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-200 ease-in-out">
                    Cancel
                </a>
                <!-- Export button functionality would typically be a separate HTMX GET or full page redirect -->
                <button type="button" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out"
                        hx-get="{% url 'work_order_budget:export_budget' wono=wono %}" hx-target="body" hx-swap="none">
                    Export
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init for global components if needed
    document.addEventListener('alpine:init', () => {
        Alpine.data('tableRow', () => ({
            showInput: false,
            toggleInput() {
                this.showInput = !this.showInput;
            }
        }));
    });
</script>
{% endblock %}
```

**`_single_budget_form.html`**

```html
<form hx-post="{% url 'work_order_budget:add_budget_entry' wono=wono %}" hx-swap="none">
    {% csrf_token %}
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
        <div>
            <label for="{{ add_form.equipment.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ add_form.equipment.label }}
            </label>
            {{ add_form.equipment }}
            {% if add_form.equipment.errors %}
            <p class="text-red-500 text-xs mt-1">{{ add_form.equipment.errors }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ add_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ add_form.category.label }}
            </label>
            {{ add_form.category }}
            {% if add_form.category.errors %}
            <p class="text-red-500 text-xs mt-1">{{ add_form.category.errors }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ add_form.sub_category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ add_form.sub_category.label }}
            </label>
            {{ add_form.sub_category }}
            {% if add_form.sub_category.errors %}
            <p class="text-red-500 text-xs mt-1">{{ add_form.sub_category.errors }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ add_form.hours.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ add_form.hours.label }}
            </label>
            {{ add_form.hours }}
            {% if add_form.hours.errors %}
            <p class="text-red-500 text-xs mt-1">{{ add_form.hours.errors }}</p>
            {% endif %}
        </div>
        <div>
            <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded w-full">
                Add
            </button>
        </div>
    </div>
</form>
```

**`_workorderbudgetentry_table.html`**

```html
<table id="workorderbudgetentryTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equip. No</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub Cate.</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Hrs</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Utilized Hrs</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Bal Hrs</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Finish (%)</th>
            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for item in budget_items %}
        <tr x-data="tableRow" class="hover:bg-gray-100">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                <input type="checkbox" name="selected_row_{{ forloop.counter0 }}" value="true" @change="toggleInput()" class="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out">
                <input type="hidden" name="equip_id_{{ forloop.counter0 }}" value="{{ item.EquipId }}">
                <input type="hidden" name="cat_id_{{ forloop.counter0 }}" value="{{ item.CatId }}">
                <input type="hidden" name="sub_cat_id_{{ forloop.counter0 }}" value="{{ item.SubCatId }}">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.EquipmentNo }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.Description }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.Category }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.SubCategory }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                <span x-show="!showInput">{{ item.allocated_hrs|floatformat:2 }}</span>
                <input type="number" step="0.01" name="updated_hours_{{ forloop.counter0 }}"
                       x-show="showInput" x-transition:enter.duration.100ms
                       value="{{ item.allocated_hrs|floatformat:2 }}"
                       class="block w-24 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-right">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.utilized_hrs|floatformat:2 }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.bal_hrs|floatformat:2 }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                {% if item.allocated_hrs > 0 %}
                    {{ item.finish_percent|floatformat:2 }}%
                {% else %}
                    N/A
                {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                <a href="{% url 'work_order_budget:budget_details' wono=wono %}?AllocHrs={{ item.allocated_hrs }}&UtilHrs={{ item.utilized_hrs }}&BalHrs={{ item.bal_hrs }}&Eqid={{ item.EquipId }}&Cat={{ item.CatId }}&SubCat={{ item.SubCatId }}&ModId=14"
                   class="text-blue-600 hover:text-blue-900">Select</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Only initialize DataTable if it hasn't been initialized
    if ( ! $.fn.DataTable.isDataTable( '#workorderbudgetentryTable' ) ) {
        $('#workorderbudgetentryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "pagingType": "full_numbers",
            "columnDefs": [
                { "orderable": false, "targets": [1, 10] }, // Disable sorting for SN, CK, Details columns
                { "searchable": false, "targets": [1, 10] } // Disable searching for SN, CK, Details columns
            ]
        });
    } else {
        // If it's already a DataTable, destroy and re-initialize to update content
        $('#workorderbudgetentryTable').DataTable().destroy();
        $('#workorderbudgetentryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "pagingType": "full_numbers",
            "columnDefs": [
                { "orderable": false, "targets": [1, 10] },
                { "searchable": false, "targets": [1, 10] }
            ]
        });
    }
});
</script>
```

**`budget_details.html` (Placeholder for drill-down)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-4">Budget Details for Work Order No: {{ wono }}</h2>
    <div class="bg-white shadow-md rounded-lg p-6">
        <p>This is a placeholder for the detailed budget information for specific Equipment, Category, and Sub-Category.</p>
        <p><strong>Work Order:</strong> {{ wono }}</p>
        <p><strong>Allocated Hours (from URL):</strong> {{ alloc_hrs }}</p>
        <p><strong>Utilized Hours (from URL):</strong> {{ util_hrs }}</p>
        <p><strong>Balance Hours (from URL):</strong> {{ bal_hrs }}</p>
        <p><strong>Equipment ID (from URL):</strong> {{ equip_id }}</p>
        <p><strong>Category ID (from URL):</strong> {{ cat_id }}</p>
        <p><strong>Sub Category ID (from URL):</strong> {{ sub_cat_id }}</p>
        <p><strong>Module ID (from URL):</strong> {{ mod_id }}</p>

        <div class="mt-6">
            <a href="{% url 'work_order_budget:workorderbudgetentry_list' wono=wono %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Back to Budget List
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`work_order_budget/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderBudgetListView, 
    WorkOrderBudgetTablePartialView, 
    AddSingleBudgetEntryView, 
    BulkUpdateBudgetEntriesView,
    GetCategoriesView, 
    GetSubCategoriesView,
    BudgetDetailsView,
)

app_name = 'work_order_budget'

urlpatterns = [
    # Main list view for a specific Work Order
    path('<str:wono>/', WorkOrderBudgetListView.as_view(), name='workorderbudgetentry_list'),
    
    # HTMX endpoint for the DataTable content
    path('<str:wono>/table/', WorkOrderBudgetTablePartialView.as_view(), name='budget_table'),
    
    # HTMX endpoint for the single add budget form
    path('<str:wono>/add_single/', AddSingleBudgetEntryView.as_view(), name='add_budget_entry'),
    
    # HTMX endpoint for bulk updating budget entries from the grid
    path('<str:wono>/bulk_update/', BulkUpdateBudgetEntriesView.as_view(), name='bulk_update_budget'),

    # HTMX endpoints for dynamic dropdowns
    path('get_categories/', GetCategoriesView.as_view(), name='get_categories'),
    path('get_subcategories/', GetSubCategoriesView.as_view(), name='get_subcategories'),

    # Placeholder for the detail view linked from the grid
    path('details/<str:wono>/', BudgetDetailsView.as_view(), name='budget_details'),

    # Placeholder for "Cancel" (redirects back to a list of WO, if such a page exists)
    # This assumes a root WO list page exists at '/work_orders/'
    path('cancel/', lambda request: redirect('work_orders:list'), name='cancel_action'),

    # Placeholder for Export (can be a simple redirect to a download view)
    path('<str:wono>/export/', lambda request, wono: HttpResponse(status=204, headers={'HX-Trigger': f'alert:"Export functionality would be implemented here for WO {wono}"'}), name='export_budget'),
]

```

#### 4.6 Tests (`work_order_budget/tests.py`)

```python
import datetime
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db import connection

from .models import WorkOrderBudgetEntry, Equipment, BudgetCategory, BudgetSubCategory

class WorkOrderBudgetModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy tables and insert test data because managed=False
        # In a real setup, connect to existing database or use migrations to create schema
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE tblDG_Item_Master (
                    Id INT PRIMARY KEY,
                    ItemCode VARCHAR(100),
                    ManfDesc VARCHAR(255)
                );
            """)
            cursor.execute("""
                CREATE TABLE tblMIS_BudgetHrs_Field_Category (
                    Id INT PRIMARY KEY,
                    Category VARCHAR(100)
                );
            """)
            cursor.execute("""
                CREATE TABLE tblMIS_BudgetHrs_Field_SubCategory (
                    Id INT PRIMARY KEY,
                    SubCategory VARCHAR(100),
                    MId INT
                );
            """)
            cursor.execute("""
                CREATE TABLE tblACC_Budget_WO_Time (
                    Id INT PRIMARY KEY IDENTITY(1,1),
                    SysDate DATE,
                    SysTime TIME,
                    CompId INT,
                    FinYearId INT,
                    SessionId VARCHAR(50),
                    WONo VARCHAR(100),
                    EquipId INT,
                    HrsBudgetCat INT,
                    HrsBudgetSubCat INT,
                    Hour FLOAT
                );
            """)

            # Insert sample data into dummy tables
            cls.eq1 = Equipment.objects.create(id=1, item_code='EQ001', manf_desc='Excavator')
            cls.eq2 = Equipment.objects.create(id=2, item_code='EQ002', manf_desc='Bulldozer')

            cls.cat1 = BudgetCategory.objects.create(id=10, category='Maintenance')
            cls.cat2 = BudgetCategory.objects.create(id=20, category='Operation')

            cls.subcat1_1 = BudgetSubCategory.objects.create(id=100, sub_category='Engine', category=cls.cat1)
            cls.subcat1_2 = BudgetSubCategory.objects.create(id=101, sub_category='Hydraulics', category=cls.cat1)
            cls.subcat2_1 = BudgetSubCategory.objects.create(id=200, sub_category='Fuel', category=cls.cat2)

            # Create test budget entries
            cls.wono_test = "WO-2023-001"
            cls.comp_id_test = 1
            cls.fin_year_id_test = 2023

            WorkOrderBudgetEntry.objects.create(
                sys_date=datetime.date.today(),
                sys_time=datetime.time(10, 0, 0),
                comp_id=cls.comp_id_test,
                fin_year_id=cls.fin_year_id_test,
                session_id='testuser',
                wono=cls.wono_test,
                equipment=cls.eq1,
                budget_category=cls.cat1,
                budget_subcategory=cls.subcat1_1,
                hour=10.0
            )
            WorkOrderBudgetEntry.objects.create(
                sys_date=datetime.date.today(),
                sys_time=datetime.time(11, 0, 0),
                comp_id=cls.comp_id_test,
                fin_year_id=cls.fin_year_id_test,
                session_id='testuser',
                wono=cls.wono_test,
                equipment=cls.eq1,
                budget_category=cls.cat1,
                budget_subcategory=cls.subcat1_1,
                hour=5.0
            )
            WorkOrderBudgetEntry.objects.create(
                sys_date=datetime.date.today(),
                sys_time=datetime.time(12, 0, 0),
                comp_id=cls.comp_id_test,
                fin_year_id=cls.fin_year_id_test,
                session_id='testuser',
                wono=cls.wono_test,
                equipment=cls.eq2,
                budget_category=cls.cat2,
                budget_subcategory=cls.subcat2_1,
                hour=20.0
            )
    
    @classmethod
    def tearDownClass(cls):
        # Drop dummy tables
        with connection.cursor() as cursor:
            cursor.execute("DROP TABLE tblACC_Budget_WO_Time;")
            cursor.execute("DROP TABLE tblMIS_BudgetHrs_Field_SubCategory;")
            cursor.execute("DROP TABLE tblMIS_BudgetHrs_Field_Category;")
            cursor.execute("DROP TABLE tblDG_Item_Master;")
        super().tearDownClass()


    def test_workorderbudgetentry_creation(self):
        entry = WorkOrderBudgetEntry.objects.get(wono=self.wono_test, equipment=self.eq1, hour=10.0)
        self.assertEqual(entry.session_id, 'testuser')
        self.assertEqual(entry.equipment.item_code, 'EQ001')
        self.assertEqual(entry.budget_category.category, 'Maintenance')
        self.assertEqual(entry.budget_subcategory.sub_category, 'Engine')
        self.assertEqual(entry.hour, 10.0)

    def test_get_aggregated_budget_data(self):
        aggregated_data = WorkOrderBudgetEntry.objects.get_aggregated_budget_data(
            wono=self.wono_test, comp_id=self.comp_id_test
        )
        
        # There should be 2 distinct aggregated entries based on EquipId, CatId, SubCatId
        self.assertEqual(len(aggregated_data), 2)
        
        # Check the first aggregated entry (EQ001, Maintenance, Engine)
        item1 = aggregated_data.get(EquipId=self.eq1.id, CatId=self.cat1.id, SubCatId=self.subcat1_1.id)
        self.assertEqual(item1['EquipmentNo'], 'EQ001')
        self.assertEqual(item1['allocated_hrs'], 15.0) # 10.0 + 5.0
        # Check the approximated utilized hours (40% of 15.0)
        self.assertAlmostEqual(item1['utilized_hrs'], 6.0) 
        self.assertAlmostEqual(item1['bal_hrs'], 9.0) # 15.0 - 6.0
        self.assertAlmostEqual(item1['finish_percent'], 40.0)

        # Check the second aggregated entry (EQ002, Operation, Fuel)
        item2 = aggregated_data.get(EquipId=self.eq2.id, CatId=self.cat2.id, SubCatId=self.subcat2_1.id)
        self.assertEqual(item2['EquipmentNo'], 'EQ002')
        self.assertEqual(item2['allocated_hrs'], 20.0)
        self.assertAlmostEqual(item2['utilized_hrs'], 8.0) # 40% of 20.0
        self.assertAlmostEqual(item2['bal_hrs'], 12.0)
        self.assertAlmostEqual(item2['finish_percent'], 40.0)

class WorkOrderBudgetViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy tables and insert test data for views
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE tblDG_Item_Master (
                    Id INT PRIMARY KEY,
                    ItemCode VARCHAR(100),
                    ManfDesc VARCHAR(255)
                );
            """)
            cursor.execute("""
                CREATE TABLE tblMIS_BudgetHrs_Field_Category (
                    Id INT PRIMARY KEY,
                    Category VARCHAR(100)
                );
            """)
            cursor.execute("""
                CREATE TABLE tblMIS_BudgetHrs_Field_SubCategory (
                    Id INT PRIMARY KEY,
                    SubCategory VARCHAR(100),
                    MId INT
                );
            """)
            cursor.execute("""
                CREATE TABLE tblACC_Budget_WO_Time (
                    Id INT PRIMARY KEY IDENTITY(1,1),
                    SysDate DATE,
                    SysTime TIME,
                    CompId INT,
                    FinYearId INT,
                    SessionId VARCHAR(50),
                    WONo VARCHAR(100),
                    EquipId INT,
                    HrsBudgetCat INT,
                    HrsBudgetSubCat INT,
                    Hour FLOAT
                );
            """)

            cls.eq1 = Equipment.objects.create(id=1, item_code='EQ001', manf_desc='Excavator')
            cls.eq2 = Equipment.objects.create(id=2, item_code='EQ002', manf_desc='Bulldozer')

            cls.cat1 = BudgetCategory.objects.create(id=10, category='Maintenance')
            cls.cat2 = BudgetCategory.objects.create(id=20, category='Operation')

            cls.subcat1_1 = BudgetSubCategory.objects.create(id=100, sub_category='Engine', category=cls.cat1)
            cls.subcat1_2 = BudgetSubCategory.objects.create(id=101, sub_category='Hydraulics', category=cls.cat1)
            cls.subcat2_1 = BudgetSubCategory.objects.create(id=200, sub_category='Fuel', category=cls.cat2)

            cls.wono_test = "WO-2023-001"
            cls.comp_id_test = 1
            cls.fin_year_id_test = 2023

            WorkOrderBudgetEntry.objects.create(
                sys_date=datetime.date.today(),
                sys_time=datetime.time(10, 0, 0),
                comp_id=cls.comp_id_test,
                fin_year_id=cls.fin_year_id_test,
                session_id='testuser',
                wono=cls.wono_test,
                equipment=cls.eq1,
                budget_category=cls.cat1,
                budget_subcategory=cls.subcat1_1,
                hour=10.0
            )

    @classmethod
    def tearDownClass(cls):
        with connection.cursor() as cursor:
            cursor.execute("DROP TABLE tblACC_Budget_WO_Time;")
            cursor.execute("DROP TABLE tblMIS_BudgetHrs_Field_SubCategory;")
            cursor.execute("DROP TABLE tblMIS_BudgetHrs_Field_Category;")
            cursor.execute("DROP TABLE tblDG_Item_Master;")
        super().tearDownClass()

    def setUp(self):
        self.client = Client()
        # Simulate a logged-in user and session data
        self.user = User.objects.create_user(username='testuser', password='password123')
        self.client.login(username='testuser', password='password123')
        session = self.client.session
        session['compid'] = self.comp_id_test
        session['finyear'] = self.fin_year_id_test
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('work_order_budget:workorderbudgetentry_list', args=[self.wono_test]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order_budget/workorderbudgetentry/list.html')
        self.assertContains(response, self.wono_test)
        self.assertContains(response, 'Add New Budget Entry')

    def test_budget_table_partial_view_get(self):
        response = self.client.get(reverse('work_order_budget:budget_table', args=[self.wono_test]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order_budget/workorderbudgetentry/_workorderbudgetentry_table.html')
        self.assertContains(response, self.eq1.item_code)
        self.assertContains(response, self.cat1.category)

    def test_add_single_budget_entry_view_get_htmx(self):
        response = self.client.get(reverse('work_order_budget:add_budget_entry', args=[self.wono_test]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order_budget/workorderbudgetentry/_single_budget_form.html')
        self.assertContains(response, '<form')
        self.assertContains(response, '<select name="equipment"')

    def test_add_single_budget_entry_view_post_success(self):
        initial_count = WorkOrderBudgetEntry.objects.count()
        data = {
            'equipment': self.eq2.id,
            'category': self.cat2.id,
            'sub_category': self.subcat2_1.id,
            'hours': 15.5
        }
        response = self.client.post(reverse('work_order_budget:add_budget_entry', args=[self.wono_test]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertEqual(WorkOrderBudgetEntry.objects.count(), initial_count + 1)
        new_entry = WorkOrderBudgetEntry.objects.order_by('-id').first()
        self.assertEqual(new_entry.hour, 15.5)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Budget entry added successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetList')

    def test_add_single_budget_entry_view_post_invalid(self):
        initial_count = WorkOrderBudgetEntry.objects.count()
        data = {
            'equipment': self.eq2.id,
            'category': self.cat2.id,
            'sub_category': self.subcat2_1.id,
            'hours': -5.0 # Invalid hours
        }
        response = self.client.post(reverse('work_order_budget:add_budget_entry', args=[self.wono_test]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'work_order_budget/workorderbudgetentry/_single_budget_form.html')
        self.assertContains(response, 'Hours must be greater than or equal to 0.01.')
        self.assertEqual(WorkOrderBudgetEntry.objects.count(), initial_count)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

    def test_bulk_update_budget_entries_view_post_success(self):
        initial_count = WorkOrderBudgetEntry.objects.count()
        # Simulate selecting the first row and updating its hours
        # The first row in our test setup is EQ001, Cat1, SubCat1_1 with 10.0 hours
        # In the get_aggregated_budget_data, its aggregated total is 15.0
        # If we tick the box and enter 25.0, it should add a new transaction for 25.0
        data = {
            f'selected_row_0': 'true',
            f'equip_id_0': str(self.eq1.id),
            f'cat_id_0': str(self.cat1.id),
            f'sub_cat_id_0': str(self.subcat1_1.id),
            f'updated_hours_0': '25.0'
        }
        response = self.client.post(reverse('work_order_budget:bulk_update_budget', args=[self.wono_test]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertEqual(WorkOrderBudgetEntry.objects.count(), initial_count + 1)
        new_entry = WorkOrderBudgetEntry.objects.order_by('-id').first()
        self.assertEqual(new_entry.equipment.id, self.eq1.id)
        self.assertEqual(new_entry.budget_category.id, self.cat1.id)
        self.assertEqual(new_entry.budget_subcategory.id, self.subcat1_1.id)
        self.assertEqual(new_entry.hour, 25.0)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Selected budget entries updated successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetList')

    def test_bulk_update_budget_entries_view_post_invalid_hours(self):
        initial_count = WorkOrderBudgetEntry.objects.count()
        data = {
            f'selected_row_0': 'true',
            f'equip_id_0': str(self.eq1.id),
            f'cat_id_0': str(self.cat1.id),
            f'sub_cat_id_0': str(self.subcat1_1.id),
            f'updated_hours_0': '-5.0' # Invalid value
        }
        response = self.client.post(reverse('work_order_budget:bulk_update_budget', args=[self.wono_test]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 400) # Bad Request due to validation error
        self.assertContains(response, 'Hours must be greater than zero.')
        self.assertEqual(WorkOrderBudgetEntry.objects.count(), initial_count)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn('Error in row 0', str(messages[0]))

    def test_get_categories_view_htmx(self):
        response = self.client.get(reverse('work_order_budget:get_categories'), {'equipment': self.eq1.id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.cat1.id}">{self.cat1.category}</option>')
        self.assertNotContains(response, f'<option value="{self.cat2.id}">{self.cat2.category}</option>') # Only categories linked to EQ1's budget entries

    def test_get_subcategories_view_htmx(self):
        response = self.client.get(reverse('work_order_budget:get_subcategories'), {'category': self.cat1.id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.subcat1_1.id}">{self.subcat1_1.sub_category}</option>')
        self.assertContains(response, f'<option value="{self.subcat1_2.id}">{self.subcat1_2.sub_category}</option>')
        self.assertNotContains(response, f'<option value="{self.subcat2_1.id}">{self.subcat2_1.sub_category}</option>')

    def test_budget_details_view(self):
        response = self.client.get(reverse('work_order_budget:budget_details', args=[self.wono_test]) + '?AllocHrs=10.0&UtilHrs=4.0&BalHrs=6.0&Eqid=1&Cat=10&SubCat=100&ModId=14')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_order_budget/budget_details.html')
        self.assertContains(response, f'Work Order: {self.wono_test}')
        self.assertContains(response, 'Allocated Hours (from URL): 10.0')

# Dummy User model for LoginRequiredMixin if not already defined in your project
from django.contrib.auth.models import User
# This needs to be in your project's settings.py, e.g., AUTH_USER_MODEL = 'auth.User'
# or you would use your custom user model if you have one.

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Implementation:**

*   **Main Page Load:** `workorderbudgetentry/list.html` loads the initial layout.
*   **DataTable Loading:** The `budget-table-container` div uses `hx-get` to fetch `{% url 'work_order_budget:budget_table' wono=wono %}`. `hx-trigger="load, refreshBudgetList from:body"` ensures the table loads on page load and refreshes whenever the `refreshBudgetList` custom event is triggered (after successful CRUD operations).
*   **Add Form Loading:** The `single-budget-form-container` div uses `hx-get` to fetch `{% url 'work_order_budget:add_budget_entry' wono=wono %}`. This ensures the form is loaded into the page via HTMX.
*   **Form Submission (Add):** The `_single_budget_form.html` form uses `hx-post` to submit to `{% url 'work_order_budget:add_budget_entry' wono=wono %}`. `hx-swap="none"` is used with a `204 No Content` response from the view, which then sends `HX-Trigger: refreshBudgetList` to refresh the table. If there are form errors, the view re-renders the form partial with errors, which HTMX swaps back into the `single-budget-form-container`.
*   **Bulk Update Form Submission:** The `bulk-update-form` wraps the table and uses `hx-post` to submit to `{% url 'work_order_budget:bulk_update_budget' wono=wono %}`. Similar to the add form, `hx-swap="none"` and `HX-Trigger: refreshBudgetList` are used for success, or a 400 response with an error message.
*   **Dynamic Dropdowns (HTMX):**
    *   The `equipment` dropdown in `_single_budget_form.html` has `hx-get` and `hx-target="#id_category"` with `hx-trigger="change"`. This will fetch the `get_categories` endpoint to update the category dropdown.
    *   Similarly, the `category` dropdown has `hx-get` and `hx-target="#id_sub_category"` with `hx-trigger="change"`. This will fetch the `get_subcategories` endpoint to update the sub-category dropdown.
    *   The views (`GetCategoriesView`, `GetSubCategoriesView`) return the full `<select>` element's HTML, allowing `hx-swap="outerHTML"` to replace the target dropdown.
*   **Inline Editing (Alpine.js + HTMX):**
    *   Each row in `_workorderbudgetentry_table.html` uses `x-data="tableRow"` to manage the `showInput` state for displaying either the `<span>` or `<input type="number">` for "Budget Hrs".
    *   The checkbox `name="selected_row_{{ forloop.counter0 }}"` has `@change="toggleInput()"` to control `showInput` for that specific row.
    *   Hidden inputs (`equip_id_`, `cat_id_`, `sub_cat_id_`) are included for each row to pass the necessary IDs when the bulk update form is submitted.
*   **Messages:** Django's `messages` framework is used for user feedback, displayed via the `core/base.html` template.

### Final Notes

This comprehensive plan provides a robust framework for migrating your ASP.NET "Hrs Budget For Work Order No" functionality to Django.

*   **Focus on Business Logic:** The core business logic for calculating `Allocated Hrs`, `Utilized Hrs`, `Bal Hrs`, and `Finish (%)` has been moved to a custom manager (`WorkOrderBudgetEntryManager`) on the `WorkOrderBudgetEntry` model, adhering to the "Fat Model" principle. The actual calculations within `CUH` class in ASP.NET would need to be fully reverse-engineered and implemented accurately in the Django manager/model methods. For demonstration, a placeholder calculation for `utilized_hrs` was used.
*   **Transactional Updates:** The ASP.NET behavior of inserting new records for "updates" (via `BtnInsert_Click`) has been replicated, treating each "update" as a new budget transaction. If the business requirement is truly to modify *existing* records or maintain strict uniqueness per combination, the model design and update logic would need to be adjusted (e.g., using `update_or_create` or a specific "edit" form for existing entries).
*   **Automation Focus:** The plan emphasizes HTMX for dynamic content loading and form submission, reducing the need for traditional JavaScript. The structured nature of Django models, forms, and views lends itself well to automated code generation and template structuring in a larger migration effort.
*   **Scalability & Maintainability:** Django's ORM, CBVs, and clear separation of concerns lead to a more maintainable and scalable application compared to tightly coupled ASP.NET Web Forms code.
*   **Security:** Django automatically handles CSRF protection, and `LoginRequiredMixin` ensures views are protected, enhancing the security posture.
*   **Testing:** Comprehensive unit and integration tests are crucial for verifying correct migration and ongoing functionality, ensuring an 80% test coverage target. The provided tests cover model behavior, view responses, form submissions, and HTMX interactions.
*   **Front-end Modernization:** The shift to HTMX + Alpine.js with Tailwind CSS provides a modern, responsive, and efficient user experience without the complexity of a full JavaScript framework. DataTables handles complex grid interactions with ease.

This plan provides a clear, actionable roadmap for your modernization journey, prioritizing business value and automated approaches.