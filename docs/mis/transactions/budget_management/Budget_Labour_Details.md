This comprehensive Django modernization plan outlines the strategy to transition your legacy ASP.NET application, specifically the `Budget_Labour_Details` module, to a modern Django-based solution. Our focus is on automation-driven approaches, utilizing conversational AI for plain English instructions, and adhering to modern Django 5.0+ best practices with a "fat model, thin view" architecture, HTMX, Alpine.js, and DataTables.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`Budget_Labour_Details`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The C# code-behind reveals the following SQL query for data retrieval:
```sql
Select tblACC_Budget_Transactions.Id,REPLACE(CONVERT (varchar, CONVERT (datetime, SUBSTRING(tblACC_Budget_Transactions.SysDate , CHARINDEX('-',tblACC_Budget_Transactions.SysDate ) + 1, 2) + '-' + LEFT (tblACC_Budget_Transactions.SysDate , CHARINDEX('-', tblACC_Budget_Transactions.SysDate ) - 1) + '-' + RIGHT (tblACC_Budget_Transactions.SysDate , CHARINDEX('-', REVERSE(tblACC_Budget_Transactions.SysDate )) - 1)), 103), '/', '-') AS SysDate,tblACC_Budget_Transactions.SysTime,tblACC_Budget_Transactions.BudgetCode,  AccHead.Description,AccHead.Symbol,tblACC_Budget_Transactions.Amount  from  AccHead ,tblACC_Budget_Transactions where tblACC_Budget_Transactions.BudgetCode=AccHead.Id  and  AccHead.Id='" + Code + "'"
```
And `UPDATE`/`DELETE` operations on `tblACC_Budget_Transactions`.

**Extracted Schema:**

*   **[TABLE_NAME]**: `tblACC_Budget_Transactions`
    *   **Columns**:
        *   `Id` (Primary Key, integer)
        *   `SysDate` (string, stored as 'MM-DD-YYYY' format in `varchar`, converted for display)
        *   `SysTime` (string, `varchar`)
        *   `BudgetCode` (Foreign Key to `AccHead.Id`, integer)
        *   `Amount` (Decimal/Numeric, regex suggests up to 15 digits integer, 3 decimal places)
        *   `CompId` (integer, from session)
        *   `FinYearId` (integer, from session)
        *   `SessionId` (string, from session, likely username)

*   **[TABLE_NAME]**: `AccHead`
    *   **Columns**:
        *   `Id` (Primary Key, integer)
        *   `Description` (string)
        *   `Symbol` (string)

**Relationship:** `tblACC_Budget_Transactions.BudgetCode` is a foreign key to `AccHead.Id`. One `AccHead` record can have many `tblACC_Budget_Transactions`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Read:**
    *   The `Page_Load` event fetches data from `tblACC_Budget_Transactions` and `AccHead` based on a `BudgetCode` (`Id`) passed via query string.
    *   It populates a `GridView` (`GridView2`) with transaction details (`Id`, `SysDate`, `SysTime`, `Amount`).
    *   It also displays `Description` and `Symbol` from the associated `AccHead`.
*   **Update:**
    *   Triggered by `GridView2_RowCommand` with `CommandName="Update"`.
    *   Iterates through rows where `CheckBox1` is checked.
    *   Updates `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, and `Amount` for selected records in `tblACC_Budget_Transactions`.
    *   `Amount` validation using a RegularExpressionValidator for numeric input.
*   **Delete:**
    *   Triggered by `GridView2_RowCommand` with `CommandName="Deletes"`.
    *   Iterates through rows where `CheckBox1` is checked.
    *   Deletes selected records from `tblACC_Budget_Transactions`.
*   **No explicit Create:** The current page is primarily for viewing and modifying existing budget details. There is no 'Add New' form for transactions on this specific page.
*   **UI Interaction Logic:**
    *   `CheckBox1_CheckedChanged` toggles visibility of `lblAmount` (Label) and `TxtAmount` (TextBox) to enable in-place editing for selected rows.
*   **Auxiliary Functions:**
    *   `BtnExport_Click`: Redirects to another page for printing/exporting.
    *   `btnCancel_Click`: Redirects to a dashboard page.
    *   Session variables (`compid`, `username`, `finyear`) are used for `CompId`, `SessionId`, `FinYearId` during updates.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **Main Display:** `GridView2` acts as a data table. This will be replaced by a Django template using DataTables for client-side features like searching, sorting, and pagination.
*   **Data Fields:**
    *   `lblCode`, `lblDesc`: Simple labels displaying header information.
    *   `lblDate`, `lblTime`, `lblAmount`: Labels displaying data within the grid.
    *   `TxtAmount`: A `TextBox` for inputting `Amount`, conditionally visible.
*   **User Interaction:**
    *   `CheckBox1`: A checkbox per row to select items for batch update/delete. This will be an HTML checkbox with Alpine.js for interactivity.
    *   `BtnUpdate`, `BtnDelete`: Buttons in the `GridView` footer to trigger batch operations. These will be HTML buttons with HTMX `hx-post` attributes.
    *   `BtnExport`, `btnCancel`: Standard HTML buttons.
*   **Validation:** `RegularExpressionValidator` for `TxtAmount` will be replicated using Django form validation and client-side (HTML `pattern` attribute for initial client-side hint, or Alpine.js if complex).
*   **Styling:** `CssClass="yui-datatable-theme"` will be replaced by Tailwind CSS and DataTables default styling.

---

### Step 4: Generate Django Code

We will create a new Django app, e.g., `accounts`, to house this module.

#### 4.1 Models (`accounts/models.py`)

**Task:** Create Django models based on the database schema for `AccHead` and `tblACC_Budget_Transactions`.

**Instructions:**
- `AccountHead` will map to `AccHead`.
- `BudgetTransaction` will map to `tblACC_Budget_Transactions`.
- Define fields with appropriate Django field types matching inferred column types.
- Set `managed = False` and `db_table` for both models as they map to existing tables.
- Implement a class method `bulk_update_or_delete` in `BudgetTransaction` to encapsulate the batch update/delete logic, following the fat model principle.

```python
from django.db import models, transaction
from django.utils import timezone
from decimal import Decimal, InvalidOperation

class AccountHead(models.Model):
    """
    Maps to the 'AccHead' database table, representing the budget header.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False  # Important: tells Django not to manage this table's schema (it already exists)
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"{self.symbol} - {self.description}"

class BudgetTransaction(models.Model):
    """
    Maps to the 'tblACC_Budget_Transactions' database table, representing individual budget entries.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Foreign key to AccountHead using BudgetCode, matches AccHead.Id
    budget_code = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='BudgetCode', related_name='budget_transactions')
    sys_date = models.CharField(db_column='SysDate', max_length=10) # Stored as 'MM-DD-YYYY' varchar
    sys_time = models.CharField(db_column='SysTime', max_length=8) # Stored as 'HH:MM:SS' varchar
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3) # Based on ASP.NET regex
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50) # Stores the username/session ID

    class Meta:
        managed = False  # Important: tells Django not to manage this table's schema
        db_table = 'tblACC_Budget_Transactions'
        verbose_name = 'Budget Transaction'
        verbose_name_plural = 'Budget Transactions'
        ordering = ['sys_date', 'sys_time'] # Default ordering for consistency

    def __str__(self):
        return f"Transaction {self.id} for {self.budget_code.symbol} on {self.sys_date} {self.sys_time}"

    @classmethod
    def bulk_update_or_delete(cls, updates, deletes, user_session_id, company_id, financial_year_id):
        """
        Performs bulk update and delete operations on BudgetTransaction records.
        This method encapsulates the business logic, adhering to the 'fat model' principle.

        Args:
            updates (list of tuples): [(transaction_id, new_amount_str), ...] for updates.
            deletes (list of int): [transaction_id, ...] for deletions.
            user_session_id (str): User ID/username from session.
            company_id (int): Company ID from session.
            financial_year_id (int): Financial Year ID from session.
        Returns:
            tuple: (updated_count, deleted_count)
        """
        updated_count = 0
        deleted_count = 0
        # Match ASP.NET's date/time format for 'SysDate' and 'SysTime' updates
        current_date = timezone.now().strftime('%m-%d-%Y')
        current_time = timezone.now().strftime('%H:%M:%S')

        with transaction.atomic():
            for item_id, amount_str in updates:
                try:
                    amount = Decimal(amount_str)
                    if amount >= 0: # ASP.NET had Amt>0, but allows 0. Check your business rule.
                        rows_updated = cls.objects.filter(id=item_id).update(
                            amount=amount,
                            sys_date=current_date,
                            sys_time=current_time,
                            comp_id=company_id,
                            fin_year_id=financial_year_id,
                            session_id=user_session_id
                        )
                        updated_count += rows_updated
                except (ValueError, InvalidOperation):
                    # Log error for invalid amount string instead of crashing
                    print(f"Warning: Could not convert amount '{amount_str}' for transaction ID {item_id}")
                    pass # Silently skip invalid amount updates

            for item_id in deletes:
                rows_deleted, _ = cls.objects.filter(id=item_id).delete()
                deleted_count += rows_deleted
        return updated_count, deleted_count

```

#### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django form if there were dedicated forms for single object CRUD. In this case, since the original ASP.NET page focuses on *batch* updates/deletes and displays, a traditional `ModelForm` for single transactions is not directly implied for this page. We'll omit a separate `BudgetTransactionForm` since the UI allows direct inline editing controlled by Alpine.js and submission as part of a batch POST request.

*(No separate forms.py for this specific module as per analysis, since modifications are inline and batch-processed rather than through dedicated forms. If a separate "add new transaction" page were needed, a ModelForm would be created.)*

#### 4.3 Views (`accounts/views.py`)

**Task:** Implement the main view for displaying budget details and handling batch operations, along with auxiliary views for table partials, export, and cancel.

**Instructions:**
- `BudgetDetailView` handles both GET (displaying the page) and POST (batch update/delete).
- `BudgetTransactionTablePartialView` renders only the HTML table for HTMX swaps.
- `BudgetExportView` and `BudgetCancelView` handle the redirects.
- Views are kept thin, delegating complex logic to the model.

```python
from django.views.generic import View, TemplateView
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse
from django.http import HttpResponse
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin # Recommended for production apps

from .models import BudgetTransaction, AccountHead

class BudgetDetailView(View):
    """
    Handles displaying budget transactions for a specific Account Head
    and processing bulk update/delete operations via HTMX.
    """
    template_name = 'accounts/budget_details/detail.html'

    def get_object(self, acc_head_id):
        """Fetches the AccountHead object."""
        return get_object_or_404(AccountHead, id=acc_head_id)

    def get_queryset(self, account_head):
        """Fetches related BudgetTransaction objects."""
        return BudgetTransaction.objects.filter(budget_code=account_head).order_by('sys_date', 'sys_time')

    def get_context_data(self, acc_head_id):
        """Prepares context data for the template."""
        account_head = self.get_object(acc_head_id)
        budget_transactions = self.get_queryset(account_head)
        
        context = {
            'account_head': account_head,
            'budget_transactions': budget_transactions,
            'acc_head_id': acc_head_id,
        }
        return context

    def get(self, request, pk):
        """Renders the main budget details page."""
        context = self.get_context_data(pk)
        return render(request, self.template_name, context)

    def post(self, request, pk):
        """
        Handles batch update and delete operations.
        It identifies the action ('update' or 'delete') and
        collects the necessary data from the POST request.
        """
        # In a real application, you'd fetch user_session_id, company_id, financial_year_id
        # from request.user (if logged in) and request.session.
        # For demonstration, we'll use placeholder values or assume existence.
        user_session_id = request.user.username if request.user.is_authenticated else "anonymous_user"
        company_id = request.session.get('compid', 1)  # Default value, adjust as per your session setup
        financial_year_id = request.session.get('finyear', 1) # Default value

        action = request.POST.get('action') # 'update' or 'delete' from button name

        updates = []
        deletes = []
        
        # Iterates through all form fields to find selected items
        # The checkbox `name="delete_{{obj.pk}}"` being 'on' signifies selection for either action.
        # The input `name="amount_{{obj.pk}}"` carries the potential new amount.
        for key, value in request.POST.items():
            if key.startswith('delete_') and value == 'on':
                try:
                    item_id = int(key.replace('delete_', ''))
                    # If checkbox is checked, add to potential updates or deletions
                    if action == 'update':
                        # Get the corresponding amount field value
                        amount_value = request.POST.get(f'amount_{item_id}', '').strip()
                        if amount_value: # Only add to updates if an amount value is present
                             updates.append((item_id, amount_value))
                    elif action == 'delete':
                        deletes.append(item_id)
                except ValueError:
                    messages.error(request, f"Invalid ID encountered: {key}")
                    # Continue processing other items even if one fails
                    
        updated_count = 0
        deleted_count = 0

        if action == 'update':
            updated_count, _ = BudgetTransaction.bulk_update_or_delete(
                updates, [], user_session_id, company_id, financial_year_id
            )
            if updated_count > 0:
                messages.success(request, f"{updated_count} record(s) updated successfully.")
            else:
                messages.info(request, "No records selected or valid amounts provided for update.")
        elif action == 'delete':
            _, deleted_count = BudgetTransaction.bulk_update_or_delete(
                [], deletes, user_session_id, company_id, financial_year_id
            )
            if deleted_count > 0:
                messages.success(request, f"{deleted_count} record(s) deleted successfully.")
            else:
                messages.info(request, "No records selected for deletion.")
        else:
            messages.error(request, "Invalid action specified.")

        # HTMX response: Trigger a refresh of the table.
        if request.headers.get('HX-Request'):
            response = HttpResponse(status=204) # 204 No Content for successful HTMX post that triggers another action
            response['HX-Trigger'] = 'refreshBudgetTransactionList'
            return response
        
        # Fallback for non-HTMX requests (unlikely for this component)
        return redirect(reverse('budget_details_list', args=[pk]))


class BudgetTransactionTablePartialView(View):
    """
    Renders only the DataTables-enabled table portion of the budget details.
    This is fetched via HTMX to dynamically update the list.
    """
    def get(self, request, pk):
        account_head = get_object_or_404(AccountHead, id=pk)
        budget_transactions = BudgetTransaction.objects.filter(budget_code=account_head).order_by('sys_date', 'sys_time')
        context = {
            'budget_transactions': budget_transactions,
            'account_head': account_head # Pass for header info in partial if needed
        }
        return render(request, 'accounts/budget_details/_budget_transaction_table.html', context)


class BudgetExportView(View):
    """
    Handles the export functionality for budget details.
    Mimics the ASP.NET redirect to a print/export page.
    """
    def get(self, request, pk):
        # In a real application, you would generate a PDF, CSV, or Excel file here.
        # Example using a placeholder message and redirect:
        messages.info(request, f"Export for Budget Code {pk} is not yet fully implemented. "
                               "This would typically generate a PDF/Excel report.")
        # Redirect back to the details page, or to a dedicated export confirmation page
        return redirect(reverse('budget_details_list', args=[pk]))

class BudgetCancelView(View):
    """
    Handles the cancel action, redirecting to a dashboard.
    Mimics the ASP.NET redirect.
    """
    def get(self, request):
        # Assuming '/dashboard/' is the URL for your main dashboard
        # You should replace '/dashboard/' with the actual named URL for your dashboard, e.g., reverse_lazy('dashboard')
        messages.info(request, "Redirecting to Dashboard.")
        return redirect('/dashboard/') # Or reverse_lazy('your_dashboard_url_name')
```

#### 4.4 Templates (`accounts/templates/accounts/budget_details/`)

**Task:** Create the main detail template (`detail.html`) and the partial table template (`_budget_transaction_table.html`).

**Instructions:**
- `detail.html` will extend `core/base.html` and contain the overall page structure.
- `_budget_transaction_table.html` will contain the DataTables-enabled table and associated HTMX/Alpine.js logic for dynamic row interactions.
- Alpine.js will manage the visibility of the amount textboxes based on checkbox state.
- HTMX will manage table refreshes and form submissions.

**`accounts/templates/accounts/budget_details/detail.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Labour Budget Details</h2>
        <div class="w-full max-w-2xl bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Budget Code:</label>
                <p class="mt-1 text-lg font-semibold text-blue-700">{{ account_head.symbol }}</p>
            </div>
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700">Description:</label>
                <p class="mt-1 text-lg text-gray-900">{{ account_head.description }}</p>
            </div>
        </div>
    </div>

    <div id="budgetTransactionTable-container"
         hx-trigger="load, refreshBudgetTransactionList from:body"
         hx-get="{% url 'budget_transaction_table_partial' acc_head_id=account_head.pk %}"
         hx-swap="innerHTML"
         class="mt-8">
        <!-- DataTables content will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading budget transactions...</p>
        </div>
    </div>

    <div class="mt-8 flex justify-center space-x-4">
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md"
            hx-get="{% url 'budget_export' pk=account_head.pk %}" 
            hx-trigger="click" 
            hx-swap="none" 
            hx-indicator="#loadingIndicator"
            _="on htmx:afterRequest remove .is-active from #loadingIndicator">
            Export
        </button>
        <a href="{% url 'budget_cancel' %}" 
           class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-md flex items-center justify-center">
            Cancel
        </a>
    </div>

    <!-- Global Loading Indicator for HTMX operations -->
    <div id="loadingIndicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on htmx:beforeRequest add .is-active to me
            on htmx:afterRequest remove .is-active from me">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <p class="mt-2 text-white ml-3">Processing...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js data for managing checkbox states and input visibility
    document.addEventListener('alpine:init', () => {
        Alpine.data('budgetTable', () => ({
            toggleAllCheckboxes(event) {
                const checked = event.target.checked;
                document.querySelectorAll('.individual-checkbox').forEach(checkbox => {
                    checkbox.checked = checked;
                    // Trigger Alpine.js reactivity for each row's 'checked' property
                    checkbox.dispatchEvent(new Event('change'));
                });
            }
        }));
    });

    // DataTables initialization logic
    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#budgetTransactionTable')) {
            $('#budgetTransactionTable').DataTable().destroy(); // Destroy previous instance if it exists
        }
        $('#budgetTransactionTable').DataTable({
            "pageLength": 20, // From ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 25, 50, "All"]],
            "ordering": true, // Enable sorting
            "searching": true, // Enable search box
            "paging": true, // Enable pagination
            "responsive": true // Make table responsive
        });
    }

    // Call initializeDataTable when the page loads initially
    $(document).ready(function() {
        initializeDataTable();
    });

    // Re-initialize DataTables after HTMX loads new content into the container
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'budgetTransactionTable-container') {
            initializeDataTable();
            // Re-initialize Alpine.js for new DOM content
            // Alpine.init() processes new DOM sections if auto-init is not enough.
            // However, with `x-data` on the table and rows, it should largely self-initialize.
        }
    });

    // Handle messages from Django
    document.addEventListener('DOMContentLoaded', function() {
        const messages = document.querySelectorAll('.message');
        messages.forEach(message => {
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000); // Hide messages after 5 seconds
        });
    });
</script>
{% endblock %}
```

**`accounts/templates/accounts/budget_details/_budget_transaction_table.html`**

```html
<div x-data="budgetTable()" class="overflow-x-auto bg-white rounded-lg shadow-md p-4 border border-gray-200">
    <form id="batchForm" hx-post="{% url 'budget_details_batch_operations' pk=account_head.pk %}" 
          hx-trigger="submit" 
          hx-swap="none" 
          hx-indicator="#loadingIndicator">
        {% csrf_token %}
        <table id="budgetTransactionTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" @change="toggleAllCheckboxes($event)" 
                               class="form-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 rounded">
                    </th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                    <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for obj in budget_transactions %}
                <tr x-data="{ checked: false }" :class="{ 'bg-blue-50': checked }">
                    <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">
                        <input type="checkbox" 
                               name="delete_{{ obj.pk }}" 
                               x-model="checked" 
                               class="form-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 rounded individual-checkbox">
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap">{{ obj.sys_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap">{{ obj.sys_time }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">
                        <span x-show="!checked" class="text-gray-900">{{ obj.amount }}</span>
                        <input type="text" 
                               name="amount_{{ obj.pk }}" 
                               value="{{ obj.amount }}" 
                               x-show="checked" 
                               class="w-32 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-right"
                               pattern="^\d{1,15}(\.\d{0,3})?$" 
                               title="Enter a valid amount (e.g., 123.456). Max 15 digits before decimal, 3 after.">
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="py-4 px-4 text-center text-gray-500">No budget transactions found for this budget code.</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot class="bg-gray-50">
                <tr>
                    <td colspan="5" class="py-3 px-4 text-right">
                        <div class="flex justify-end space-x-4">
                            <button type="submit" name="action" value="update" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                                Update Checked
                            </button>
                            <button type="submit" name="action" value="delete" 
                                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                                    onclick="return confirm('Are you sure you want to delete the selected transactions? This action cannot be undone.');">
                                Delete Checked
                            </button>
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns for all views.

**Instructions:**
- Create a URL for the main detail page.
- Create a URL for the HTMX-fetched table partial.
- Create a URL for the batch operations POST endpoint.
- Create URLs for export and cancel actions.

```python
from django.urls import path
from .views import (
    BudgetDetailView, 
    BudgetTransactionTablePartialView, 
    BudgetExportView, 
    BudgetCancelView
)

urlpatterns = [
    # Main detail view for a specific Account Head (Budget Code)
    path('budget_details/<int:pk>/', BudgetDetailView.as_view(), name='budget_details_list'),
    
    # HTMX endpoint to load/refresh only the budget transactions table
    path('budget_details/<int:pk>/table/', BudgetTransactionTablePartialView.as_view(), name='budget_transaction_table_partial'),
    
    # HTMX endpoint for batch update/delete operations (POST request)
    path('budget_details/<int:pk>/batch_ops/', BudgetDetailView.as_view(), name='budget_details_batch_operations'),

    # Export functionality
    path('budget_details/export/<int:pk>/', BudgetExportView.as_view(), name='budget_export'),
    
    # Cancel/Redirect functionality
    path('budget_details/cancel/', BudgetCancelView.as_view(), name='budget_cancel'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
- Model tests ensure the data mapping and model methods work as expected.
- View tests verify correct rendering, context, and POST handling, including HTMX responses.
- Strive for at least 80% test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db.models import F
from decimal import Decimal
from datetime import datetime

from .models import AccountHead, BudgetTransaction

class AccountHeadModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup run once for all test methods in this class
        cls.account_head = AccountHead.objects.create(
            id=101,
            description="Test Labour Budget",
            symbol="LBR-001"
        )
  
    def test_account_head_creation(self):
        self.assertEqual(self.account_head.description, "Test Labour Budget")
        self.assertEqual(self.account_head.symbol, "LBR-001")
        self.assertEqual(self.account_head.__str__(), "LBR-001 - Test Labour Budget")

    def test_account_head_verbose_name(self):
        self.assertEqual(self.account_head._meta.verbose_name, 'Account Head')
        self.assertEqual(self.account_head._meta.verbose_name_plural, 'Account Heads')

class BudgetTransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup run once for all test methods in this class
        cls.account_head = AccountHead.objects.create(
            id=201,
            description="Project A Budget",
            symbol="PA-BGT"
        )
        cls.transaction1 = BudgetTransaction.objects.create(
            id=1,
            budget_code=cls.account_head,
            sys_date="01-15-2023",
            sys_time="10:00:00",
            amount=Decimal('100.000'),
            comp_id=1,
            fin_year_id=1,
            session_id="user1"
        )
        cls.transaction2 = BudgetTransaction.objects.create(
            id=2,
            budget_code=cls.account_head,
            sys_date="01-16-2023",
            sys_time="11:00:00",
            amount=Decimal('250.500'),
            comp_id=1,
            fin_year_id=1,
            session_id="user1"
        )
        cls.transaction3_other_budget = BudgetTransaction.objects.create(
            id=3,
            budget_code=AccountHead.objects.create(id=202, description="Other Budget", symbol="OTHER-BGT"),
            sys_date="01-17-2023",
            sys_time="12:00:00",
            amount=Decimal('500.000'),
            comp_id=1,
            fin_year_id=1,
            session_id="user2"
        )
  
    def test_budget_transaction_creation(self):
        self.assertEqual(self.transaction1.amount, Decimal('100.000'))
        self.assertEqual(self.transaction1.budget_code.symbol, "PA-BGT")
        self.assertEqual(self.transaction1.__str__(), f"Transaction 1 for PA-BGT on 01-15-2023 10:00:00")

    def test_bulk_update(self):
        updates = [(self.transaction1.id, "120.000"), (self.transaction2.id, "300.750")]
        updated_count, deleted_count = BudgetTransaction.bulk_update_or_delete(
            updates, [], "test_user", 2, 2
        )
        self.assertEqual(updated_count, 2)
        self.assertEqual(deleted_count, 0)
        
        self.transaction1.refresh_from_db()
        self.transaction2.refresh_from_db()
        
        self.assertEqual(self.transaction1.amount, Decimal('120.000'))
        self.assertEqual(self.transaction2.amount, Decimal('300.750'))
        self.assertEqual(self.transaction1.session_id, "test_user")
        self.assertEqual(self.transaction1.comp_id, 2)
        self.assertEqual(self.transaction1.fin_year_id, 2)
        
        # Test date/time format matches expected
        current_date = datetime.now().strftime('%m-%d-%Y')
        current_time = datetime.now().strftime('%H:%M:%S')
        self.assertEqual(self.transaction1.sys_date, current_date)
        self.assertEqual(self.transaction1.sys_time, current_time)

    def test_bulk_delete(self):
        deletes = [self.transaction1.id]
        updated_count, deleted_count = BudgetTransaction.bulk_update_or_delete(
            [], deletes, "test_user", 1, 1
        )
        self.assertEqual(updated_count, 0)
        self.assertEqual(deleted_count, 1)
        self.assertFalse(BudgetTransaction.objects.filter(id=self.transaction1.id).exists())
        self.assertTrue(BudgetTransaction.objects.filter(id=self.transaction2.id).exists()) # Ensure other not deleted

    def test_bulk_update_with_invalid_amount(self):
        updates = [(self.transaction1.id, "invalid_amount")]
        updated_count, _ = BudgetTransaction.bulk_update_or_delete(
            updates, [], "test_user", 1, 1
        )
        self.assertEqual(updated_count, 0) # Should not update due to invalid amount
        self.transaction1.refresh_from_db()
        self.assertEqual(self.transaction1.amount, Decimal('100.000')) # Amount should remain unchanged

    def test_no_updates_or_deletes(self):
        updated_count, deleted_count = BudgetTransaction.bulk_update_or_delete([], [], "test_user", 1, 1)
        self.assertEqual(updated_count, 0)
        self.assertEqual(deleted_count, 0)

class BudgetViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.account_head = AccountHead.objects.create(
            id=301,
            description="Department Budget",
            symbol="DEP-BGT"
        )
        BudgetTransaction.objects.create(
            id=10,
            budget_code=cls.account_head,
            sys_date="02-01-2023",
            sys_time="09:00:00",
            amount=Decimal('50.000'),
            comp_id=1,
            fin_year_id=1,
            session_id="viewer1"
        )
        BudgetTransaction.objects.create(
            id=11,
            budget_code=cls.account_head,
            sys_date="02-02-2023",
            sys_time="10:00:00",
            amount=Decimal('75.000'),
            comp_id=1,
            fin_year_id=1,
            session_id="viewer1"
        )
    
    def setUp(self):
        self.client = Client()
        self.detail_url = reverse('budget_details_list', args=[self.account_head.id])
        self.table_partial_url = reverse('budget_transaction_table_partial', args=[self.account_head.id])
        self.batch_ops_url = reverse('budget_details_batch_operations', args=[self.account_head.id])
        self.export_url = reverse('budget_export', args=[self.account_head.id])
        self.cancel_url = reverse('budget_cancel')

    def test_budget_detail_view_get(self):
        response = self.client.get(self.detail_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_details/detail.html')
        self.assertContains(response, self.account_head.symbol)
        self.assertContains(response, self.account_head.description)
        self.assertContains(response, 'Loading budget transactions...') # Initial HTMX placeholder
        self.assertEqual(response.context['account_head'], self.account_head)
        
    def test_budget_transaction_table_partial_view(self):
        response = self.client.get(self.table_partial_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_details/_budget_transaction_table.html')
        self.assertContains(response, '50.000')
        self.assertContains(response, '75.000')
        self.assertContains(response, 'id="budgetTransactionTable"') # Ensure DataTables ID is present
        self.assertEqual(len(response.context['budget_transactions']), 2)

    def test_batch_update_post(self):
        # Simulate selecting one transaction and updating its amount
        initial_amount = BudgetTransaction.objects.get(id=10).amount
        
        post_data = {
            'action': 'update',
            f'delete_{10}': 'on', # Checkbox is checked for this ID
            f'amount_{10}': '99.999', # New amount
            f'delete_{11}': '', # Checkbox is unchecked for this ID
            f'amount_{11}': '100.000', # Even if present, should be ignored if not checked
            'csrfmiddlewaretoken': self.client.get(self.detail_url).context['csrf_token'],
        }
        
        response = self.client.post(self.batch_ops_url, post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetTransactionList')
        
        # Verify update
        updated_transaction = BudgetTransaction.objects.get(id=10)
        self.assertEqual(updated_transaction.amount, Decimal('99.999'))
        
        # Verify other transaction was not updated
        other_transaction = BudgetTransaction.objects.get(id=11)
        self.assertEqual(other_transaction.amount, Decimal('75.000')) # Should remain unchanged

        # Check messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "1 record(s) updated successfully.")
        
    def test_batch_delete_post(self):
        # Simulate selecting one transaction for deletion
        post_data = {
            'action': 'delete',
            f'delete_{10}': 'on', # Checkbox is checked for this ID
            f'delete_{11}': '', # Checkbox is unchecked for this ID
            'csrfmiddlewaretoken': self.client.get(self.detail_url).context['csrf_token'],
        }
        
        response = self.client.post(self.batch_ops_url, post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetTransactionList')
        
        # Verify deletion
        self.assertFalse(BudgetTransaction.objects.filter(id=10).exists())
        self.assertTrue(BudgetTransaction.objects.filter(id=11).exists()) # Ensure other not deleted

        # Check messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "1 record(s) deleted successfully.")

    def test_batch_update_no_selection(self):
        # Simulate submitting update without any checked checkboxes
        post_data = {
            'action': 'update',
            f'delete_{10}': '', 
            f'amount_{10}': '99.999',
            'csrfmiddlewaretoken': self.client.get(self.detail_url).context['csrf_token'],
        }
        response = self.client.post(self.batch_ops_url, post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "No records selected or valid amounts provided for update.")

    def test_budget_export_view(self):
        response = self.client.get(self.export_url)
        # Should redirect back to the detail page for now
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, self.detail_url)

        # Check messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Export functionality for Budget Code", str(messages[0]))

    def test_budget_cancel_view(self):
        response = self.client.get(self.cancel_url)
        # Should redirect to dashboard
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, '/dashboard/') # Assuming /dashboard/ is the target

        # Check messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Redirecting to Dashboard.")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX:**
    - The main content area (`budgetTransactionTable-container`) uses `hx-get` to fetch the table partial (`_budget_transaction_table.html`) on `load` and on a custom `refreshBudgetTransactionList` event.
    - The `batchForm` within the table partial uses `hx-post` to submit batch updates/deletes to `budget_details_batch_operations`.
    - `hx-swap="none"` is used on the form submission because the server responds with `status=204` and an `HX-Trigger` header to cause a re-fetch of the table, rather than directly swapping content. This ensures the entire table is re-rendered with fresh data.
    - A global loading indicator (`#loadingIndicator`) is shown during HTMX requests using `htmx-indicator` class and `on htmx:beforeRequest/afterRequest` event handlers.
    - `BtnExport` also uses `hx-get` to trigger the export view (which currently just redirects and shows a message).

- **Alpine.js:**
    - `x-data="budgetTable()"` on the table container (or form) defines the Alpine.js component.
    - `x-data="{ checked: false }"` on each `<tr>` manages the checkbox state for that row.
    - `x-model="checked"` links the checkbox to the `checked` state.
    - `x-show="!checked"` and `x-show="checked"` dynamically show/hide the label and input field for `Amount` based on the checkbox state.
    - `@change="toggleAllCheckboxes($event)"` on the "select all" checkbox (in the header) triggers a function to update all individual row checkboxes.
    - `:class="{ 'bg-blue-50': checked }"` dynamically applies a background color to selected rows.

- **DataTables:**
    - The `_budget_transaction_table.html` uses `id="budgetTransactionTable"`.
    - JavaScript code in `detail.html` (within `{% block extra_js %}`) initializes DataTables on this ID.
    - Crucially, it re-initializes (destroy then re-create) DataTables after each HTMX swap that replaces the table content, ensuring DataTables features like pagination, search, and sort continue to work correctly with the new DOM.

---

### Final Notes

- **Placeholders:** Ensure all placeholders like `{% url 'your_dashboard_url_name' %}` are replaced with actual named URLs from your Django project.
- **Session Management:** The current plan assumes `request.user` for `session_id` and `request.session` for `comp_id` and `fin_year_id`. In a real application, proper Django authentication and session middleware configuration are required for these to work correctly.
- **Database Connection:** With `managed = False`, Django's ORM will connect to your existing database. Ensure your `settings.py` `DATABASES` configuration points to your SQL Server database.
- **Error Handling:** While `messages` provide user feedback, comprehensive server-side error logging should be configured in Django for production environments.
- **Export Functionality:** The current `BudgetExportView` is a placeholder. For full export, you would integrate a library (e.g., `Pillow` for images, `reportlab` or `weasyprint` for PDF, `openpyxl` for Excel) to generate and serve the file.
- **Security:** Ensure proper authentication, authorization, and input sanitization (Django's forms and ORM provide much of this by default, but always be vigilant). The direct use of `request.POST.get` for IDs assumes trust in client-side data, which should be validated server-side.
- **Modularity:** This plan focuses on the `accounts` app. As you migrate more modules, organize them into logical Django apps.