## ASP.NET to Django Conversion Script:

This modernization plan outlines the conversion of a typical ASP.NET page and its code-behind into a modern Django application. Given the provided ASP.NET code consists only of master page references and an empty `Page_Load` method, we will *assume* a common scenario for a "Budget Code Allocation" module. This allows us to demonstrate the automated conversion principles and the target Django architecture effectively, even without concrete source code details.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code is an empty page. In a real scenario, an automated tool would scan `.aspx` files for `SqlDataSource` controls, `GridView` column bindings, or `AD.NET` calls in the code-behind (`.cs` file) to infer the database schema.

**Assumed Database Schema:**
-   **Table Name:** `tbl_budget_code_allocation` (typical for ERP systems)
-   **Columns:**
    -   `id` (Primary Key, auto-incremented by database)
    -   `budget_code` (e.g., `VARCHAR(50)`, unique identifier for the budget code)
    -   `description` (e.g., `NVARCHAR(255)`, description of the budget code)
    -   `allocated_amount` (e.g., `DECIMAL(18, 2)`, the monetary amount allocated)
    -   `allocation_date` (e.g., `DATE`, the date of allocation)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
With the empty ASP.NET code, we assume standard CRUD operations for "Budget Code Allocation" are intended, mirroring common ERP module functionality.
-   **Create:** Adding new budget code allocations.
-   **Read:** Displaying a list of all existing budget code allocations.
-   **Update:** Modifying details of an existing budget code allocation.
-   **Delete:** Removing a budget code allocation.
-   **Validation Logic:** We assume basic validation for required fields (e.g., `budget_code`, `description`, `allocated_amount`, `allocation_date`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Based on the assumed CRUD functionality, a typical ASP.NET page would use:
-   **GridView:** To display a list of `Budget Code Allocations`. This will be replaced by a Django template using DataTables.
-   **TextBoxes:** For `budget_code`, `description`, `allocated_amount`, `allocation_date` input. These will map to Django Form fields with appropriate widgets.
-   **Buttons/LinkButtons:** For "Add New", "Edit", "Delete", "Save", "Cancel" actions. These will be implemented using HTMX attributes for dynamic interactions and modals.
-   **MasterPage:** The `MasterPage.master` implies a shared layout. This translates to Django's `core/base.html` template inheritance.
-   **JavaScript:** `loadingNotifier.js` suggests client-side feedback during async operations. This will be handled by HTMX's built-in indicators and potentially simple Alpine.js for UI state.

---

## Step 4: Generate Django Code

We will structure the Django application under an `accounts` app (inferred from `Module_Accounts_Transactions_BudgetCode_Allocation`) within your Django project.

### 4.1 Models (`accounts/models.py`)

**Task:** Create a Django model based on the inferred database schema.

```python
from django.db import models

class BudgetCodeAllocation(models.Model):
    # Unique identifier for the budget code, e.g., BC-001
    budget_code = models.CharField(
        db_column='budget_code',
        max_length=50,
        unique=True,
        verbose_name='Budget Code'
    )
    # Description of the budget code allocation
    description = models.CharField(
        db_column='description',
        max_length=255,
        verbose_name='Description'
    )
    # The monetary amount allocated
    allocated_amount = models.DecimalField(
        db_column='allocated_amount',
        max_digits=18,
        decimal_places=2,
        verbose_name='Allocated Amount'
    )
    # The date when the allocation was made
    allocation_date = models.DateField(
        db_column='allocation_date',
        verbose_name='Allocation Date'
    )

    class Meta:
        managed = False  # Tells Django not to manage this table's creation/deletion
        db_table = 'tbl_budget_code_allocation' # Maps to the existing database table
        verbose_name = 'Budget Code Allocation'
        verbose_name_plural = 'Budget Code Allocations'
        ordering = ['-allocation_date', 'budget_code'] # Default ordering

    def __str__(self):
        """Returns a human-readable string representation of the object."""
        return f"{self.budget_code} - {self.description}"

    def get_display_amount(self):
        """Business logic: Format amount for display."""
        return f"${self.allocated_amount:,.2f}"

    def can_be_deleted(self):
        """Business logic: Determine if an allocation can be deleted."""
        # Example: perhaps allocations older than a year cannot be deleted
        # from django.utils import timezone
        # return (timezone.now().date() - self.allocation_date).days < 365
        return True # Placeholder: always true for now

```

### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django form for user input, including Tailwind CSS classes.

```python
from django import forms
from .models import BudgetCodeAllocation

class BudgetCodeAllocationForm(forms.ModelForm):
    class Meta:
        model = BudgetCodeAllocation
        fields = ['budget_code', 'description', 'allocated_amount', 'allocation_date']
        widgets = {
            'budget_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'allocated_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'allocation_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
        labels = {
            'budget_code': 'Budget Code',
            'description': 'Description',
            'allocated_amount': 'Allocated Amount',
            'allocation_date': 'Allocation Date',
        }
    
    def clean_budget_code(self):
        """Custom validation for budget_code."""
        budget_code = self.cleaned_data['budget_code']
        # Example validation: ensure code format
        if not budget_code.startswith('BC-'):
            raise forms.ValidationError("Budget Code must start with 'BC-'.")
        return budget_code

    def clean_allocated_amount(self):
        """Custom validation: amount must be positive."""
        amount = self.cleaned_data['allocated_amount']
        if amount <= 0:
            raise forms.ValidationError("Allocated amount must be greater than zero.")
        return amount

```

### 4.3 Views (`accounts/views.py`)

**Task:** Implement CRUD operations using CBVs, keeping them thin.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import BudgetCodeAllocation
from .forms import BudgetCodeAllocationForm

class BudgetCodeAllocationListView(ListView):
    """
    Displays the main page for Budget Code Allocations.
    The actual table content is loaded via HTMX.
    """
    model = BudgetCodeAllocation
    template_name = 'accounts/budgetcodeallocation/list.html'
    context_object_name = 'budgetcodeallocations' # Not directly used in list view, but good practice

class BudgetCodeAllocationTablePartialView(TemplateView):
    """
    Renders only the DataTables table for Budget Code Allocations,
    designed to be loaded via HTMX into the main list view.
    """
    template_name = 'accounts/budgetcodeallocation/_budgetcodeallocation_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['budgetcodeallocations'] = BudgetCodeAllocation.objects.all()
        return context

class BudgetCodeAllocationCreateView(CreateView):
    """Handles creation of new Budget Code Allocations."""
    model = BudgetCodeAllocation
    form_class = BudgetCodeAllocationForm
    template_name = 'accounts/budgetcodeallocation/_budgetcodeallocation_form.html' # Partial for modal
    success_url = reverse_lazy('budgetcodeallocation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Code Allocation added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content response for HTMX
                headers={
                    'HX-Trigger': 'refreshBudgetCodeAllocationList' # Trigger refresh on client
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request, return the form with errors
            return response
        return response


class BudgetCodeAllocationUpdateView(UpdateView):
    """Handles updating existing Budget Code Allocations."""
    model = BudgetCodeAllocation
    form_class = BudgetCodeAllocationForm
    template_name = 'accounts/budgetcodeallocation/_budgetcodeallocation_form.html' # Partial for modal
    success_url = reverse_lazy('budgetcodeallocation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Code Allocation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content response for HTMX
                headers={
                    'HX-Trigger': 'refreshBudgetCodeAllocationList' # Trigger refresh on client
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request, return the form with errors
            return response
        return response


class BudgetCodeAllocationDeleteView(DeleteView):
    """Handles deletion of Budget Code Allocations."""
    model = BudgetCodeAllocation
    template_name = 'accounts/budgetcodeallocation/_budgetcodeallocation_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('budgetcodeallocation_list')

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        if not self.object.can_be_deleted():
            messages.error(self.request, "This budget code allocation cannot be deleted.")
            return HttpResponse(
                status=403, # Forbidden
                headers={
                    'HX-Trigger': 'closeModal' # Trigger to close the modal or display error
                }
            )
        
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Budget Code Allocation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content response for HTMX
                headers={
                    'HX-Trigger': 'refreshBudgetCodeAllocationList' # Trigger refresh on client
                }
            )
        return response

```

### 4.4 Templates (`accounts/templates/accounts/budgetcodeallocation/`)

**Task:** Create templates for each view, ensuring HTMX and DataTables integration.

#### `list.html`
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Budget Code Allocations</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'budgetcodeallocation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Allocation
        </button>
    </div>
    
    <div id="budgetcodeallocationTable-container"
         hx-trigger="load, refreshBudgetCodeAllocationList from:body"
         hx-get="{% url 'budgetcodeallocation_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-700">Loading Budget Code Allocations...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
        // Example: x-data="{ open: false }" on a parent element for managing modal visibility
    });

    // Global listener for closing modal (e.g., triggered by HX-Trigger 'closeModal')
    document.body.addEventListener('closeModal', function() {
        document.getElementById('modal').classList.remove('is-active');
        document.getElementById('modalContent').innerHTML = ''; // Clear content
    });
</script>
{% endblock %}
```

#### `_budgetcodeallocation_table.html` (Partial Template)
```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative" style="max-height: 70vh;">
    <table id="budgetcodeallocationTable" class="min-w-full bg-white text-sm">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Allocated Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Allocation Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in budgetcodeallocations %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.budget_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.get_display_amount }}</td> {# Using fat model method #}
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.allocation_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-xs"
                        hx-get="{% url 'budgetcodeallocation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'budgetcodeallocation_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No budget code allocations found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Ensure jQuery and DataTables scripts are loaded in core/base.html or via CDN
$(document).ready(function() {
    $('#budgetcodeallocationTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers", // For full pagination controls
        "responsive": true, // Make table responsive
        "autoWidth": false, // Disable auto width to allow custom sizing
        "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>' // Custom DOM for DataTables layout
    });
});
</script>
```

#### `_budgetcodeallocation_form.html` (Partial Template)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Budget Code Allocation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) document.getElementById('modal').classList.remove('is-active');">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `_budgetcodeallocation_confirm_delete.html` (Partial Template)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Budget Code Allocation "<span class="font-semibold">{{ object.budget_code }} - {{ object.description }}</span>"? This action cannot be undone.</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) document.getElementById('modal').classList.remove('is-active');">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import (
    BudgetCodeAllocationListView,
    BudgetCodeAllocationCreateView,
    BudgetCodeAllocationUpdateView,
    BudgetCodeAllocationDeleteView,
    BudgetCodeAllocationTablePartialView
)

urlpatterns = [
    path('budgetcodeallocations/', BudgetCodeAllocationListView.as_view(), name='budgetcodeallocation_list'),
    path('budgetcodeallocations/add/', BudgetCodeAllocationCreateView.as_view(), name='budgetcodeallocation_add'),
    path('budgetcodeallocations/edit/<int:pk>/', BudgetCodeAllocationUpdateView.as_view(), name='budgetcodeallocation_edit'),
    path('budgetcodeallocations/delete/<int:pk>/', BudgetCodeAllocationDeleteView.as_view(), name='budgetcodeallocation_delete'),
    # HTMX-specific endpoint for refreshing the DataTables content
    path('budgetcodeallocations/table/', BudgetCodeAllocationTablePartialView.as_view(), name='budgetcodeallocation_table'),
]
```

Remember to include these URLs in your project's main `urls.py`:
```python
# project_name/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('accounts.urls')), # Link to your new app
    # ... other project urls
]
```

### 4.6 Tests (`accounts/tests.py`)

**Task:** Write comprehensive tests for the model and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import BudgetCodeAllocation
from datetime import date

class BudgetCodeAllocationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.bca1 = BudgetCodeAllocation.objects.create(
            budget_code='BC-PROJ-001',
            description='Project Alpha Allocation',
            allocated_amount=15000.00,
            allocation_date=date(2023, 1, 15)
        )
        cls.bca2 = BudgetCodeAllocation.objects.create(
            budget_code='BC-DEPT-HR',
            description='HR Department Budget',
            allocated_amount=50000.00,
            allocation_date=date(2023, 2, 20)
        )
  
    def test_budget_code_allocation_creation(self):
        obj = BudgetCodeAllocation.objects.get(budget_code='BC-PROJ-001')
        self.assertEqual(obj.description, 'Project Alpha Allocation')
        self.assertEqual(obj.allocated_amount, 15000.00)
        self.assertEqual(obj.allocation_date, date(2023, 1, 15))
        
    def test_budget_code_label(self):
        obj = BudgetCodeAllocation.objects.get(budget_code='BC-PROJ-001')
        field_label = obj._meta.get_field('budget_code').verbose_name
        self.assertEqual(field_label, 'Budget Code')

    def test_str_method(self):
        obj = BudgetCodeAllocation.objects.get(budget_code='BC-PROJ-001')
        self.assertEqual(str(obj), 'BC-PROJ-001 - Project Alpha Allocation')
        
    def test_get_display_amount_method(self):
        obj = BudgetCodeAllocation.objects.get(budget_code='BC-PROJ-001')
        self.assertEqual(obj.get_display_amount(), '$15,000.00')

    def test_can_be_deleted_method(self):
        obj = BudgetCodeAllocation.objects.get(budget_code='BC-PROJ-001')
        self.assertTrue(obj.can_be_deleted())

class BudgetCodeAllocationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.bca = BudgetCodeAllocation.objects.create(
            budget_code='BC-TEST-001',
            description='Test Allocation',
            allocated_amount=1000.00,
            allocation_date=date(2023, 3, 1)
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('budgetcodeallocation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgetcodeallocation/list.html')
        # The list view doesn't directly contain context, the table partial does

    def test_table_partial_view(self):
        response = self.client.get(reverse('budgetcodeallocation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgetcodeallocation/_budgetcodeallocation_table.html')
        self.assertIn('budgetcodeallocations', response.context)
        self.assertEqual(len(response.context['budgetcodeallocations']), BudgetCodeAllocation.objects.count())
        self.assertContains(response, 'BC-TEST-001') # Check if object is in rendered HTML

    def test_create_view_get(self):
        response = self.client.get(reverse('budgetcodeallocation_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgetcodeallocation/_budgetcodeallocation_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        initial_count = BudgetCodeAllocation.objects.count()
        data = {
            'budget_code': 'BC-NEW-001',
            'description': 'New Allocation',
            'allocated_amount': 2500.00,
            'allocation_date': '2023-04-01',
        }
        response = self.client.post(reverse('budgetcodeallocation_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful creation
        self.assertEqual(BudgetCodeAllocation.objects.count(), initial_count + 1)
        self.assertTrue(BudgetCodeAllocation.objects.filter(budget_code='BC-NEW-001').exists())
        self.assertRedirects(response, reverse('budgetcodeallocation_list'))

    def test_create_view_post_invalid(self):
        initial_count = BudgetCodeAllocation.objects.count()
        data = { # Missing required fields
            'budget_code': 'INVALID', # Doesn't start with BC-
            'allocated_amount': -100.00, # Negative amount
            'allocation_date': '2023-04-01',
        }
        response = self.client.post(reverse('budgetcodeallocation_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'accounts/budgetcodeallocation/_budgetcodeallocation_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(BudgetCodeAllocation.objects.filter(budget_code='INVALID').exists())
        self.assertEqual(BudgetCodeAllocation.objects.count(), initial_count)
        self.assertContains(response, "Budget Code must start with 'BC-'.")
        self.assertContains(response, "Allocated amount must be greater than zero.")
        self.assertContains(response, "This field is required.") # for description

    def test_update_view_get(self):
        obj = BudgetCodeAllocation.objects.get(pk=self.bca.pk)
        response = self.client.get(reverse('budgetcodeallocation_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgetcodeallocation/_budgetcodeallocation_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        obj = BudgetCodeAllocation.objects.get(pk=self.bca.pk)
        data = {
            'budget_code': 'BC-UPDATED-001',
            'description': 'Updated Description',
            'allocated_amount': 1200.00,
            'allocation_date': '2023-03-02',
        }
        response = self.client.post(reverse('budgetcodeallocation_edit', args=[obj.pk]), data)
        self.assertEqual(response.status_code, 302)
        obj.refresh_from_db()
        self.assertEqual(obj.budget_code, 'BC-UPDATED-001')
        self.assertRedirects(response, reverse('budgetcodeallocation_list'))

    def test_delete_view_get(self):
        obj = BudgetCodeAllocation.objects.get(pk=self.bca.pk)
        response = self.client.get(reverse('budgetcodeallocation_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgetcodeallocation/_budgetcodeallocation_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_success(self):
        obj = BudgetCodeAllocation.objects.get(pk=self.bca.pk)
        initial_count = BudgetCodeAllocation.objects.count()
        response = self.client.post(reverse('budgetcodeallocation_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertEqual(BudgetCodeAllocation.objects.count(), initial_count - 1)
        self.assertFalse(BudgetCodeAllocation.objects.filter(pk=obj.pk).exists())
        self.assertRedirects(response, reverse('budgetcodeallocation_list'))

    # HTMX-specific tests
    def test_create_view_htmx_post(self):
        initial_count = BudgetCodeAllocation.objects.count()
        data = {
            'budget_code': 'BC-HX-001',
            'description': 'HTMX New Allocation',
            'allocated_amount': 3000.00,
            'allocation_date': '2023-05-01',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('budgetcodeallocation_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetCodeAllocationList')
        self.assertTrue(BudgetCodeAllocation.objects.filter(budget_code='BC-HX-001').exists())
        self.assertEqual(BudgetCodeAllocation.objects.count(), initial_count + 1)

    def test_update_view_htmx_post(self):
        obj = BudgetCodeAllocation.objects.get(pk=self.bca.pk)
        data = {
            'budget_code': obj.budget_code, # Keep same code
            'description': 'HTMX Updated Description',
            'allocated_amount': 1500.00,
            'allocation_date': '2023-03-01',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('budgetcodeallocation_edit', args=[obj.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetCodeAllocationList')
        obj.refresh_from_db()
        self.assertEqual(obj.description, 'HTMX Updated Description')

    def test_delete_view_htmx_post(self):
        obj_to_delete = BudgetCodeAllocation.objects.create(
            budget_code='BC-TEMP-DELETE',
            description='Temporary for Delete Test',
            allocated_amount=100.00,
            allocation_date=date(2023, 6, 1)
        )
        initial_count = BudgetCodeAllocation.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('budgetcodeallocation_delete', args=[obj_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetCodeAllocationList')
        self.assertEqual(BudgetCodeAllocation.objects.count(), initial_count - 1)
        self.assertFalse(BudgetCodeAllocation.objects.filter(pk=obj_to_delete.pk).exists())
```

## Step 5: HTMX and Alpine.js Integration

The generated code fully embraces HTMX and Alpine.js for a modern, dynamic user experience:

-   **HTMX for dynamic updates:**
    -   The main `list.html` uses `hx-trigger="load, refreshBudgetCodeAllocationList from:body"` and `hx-get="{% url 'budgetcodeallocation_table' %}"` to load the DataTables content dynamically on page load and when the `refreshBudgetCodeAllocationList` custom event is triggered.
    -   "Add New", "Edit", and "Delete" buttons use `hx-get` to load forms or confirmation dialogs into a modal (`#modalContent`) without full page reloads.
    -   Form submissions use `hx-post` with `hx-swap="none"` and `hx-on::after-request` to handle successful responses (status 204 No Content) by closing the modal and triggering the `refreshBudgetCodeAllocationList` event on the client, which in turn causes the table to reload via HTMX.
    -   Error handling in forms will re-render the partial form with validation messages.

-   **Alpine.js for UI state management:**
    -   The modal (`#modal`) uses Alpine.js's `_` attribute (`_="on click add .is-active to #modal"`) to show/hide the modal based on button clicks. It also handles closing the modal when clicking outside of it.
    -   A global `closeModal` event listener is set up in `list.html` to allow views (e.g., delete with failure condition) to programmatically close the modal via an `HX-Trigger`.

-   **DataTables for list views:**
    -   The `_budgetcodeallocation_table.html` partial template contains the `<table>` element, which is then initialized as a DataTable using JavaScript in `$(document).ready()`. This provides client-side searching, sorting, and pagination.
    -   The `dom` option in DataTables initialization provides a modern layout for search, length menu, table, and pagination.

-   **No custom JavaScript requirements (beyond HTMX/Alpine/DataTables):**
    -   All dynamic interactions are declarative using HTMX attributes.
    -   UI state (like modal visibility) is managed by Alpine.js.
    -   Data presentation features (sorting, filtering) are handled by DataTables.

## Final Notes

This comprehensive plan provides a robust and modern Django solution for the "Budget Code Allocation" module. The emphasis on HTMX, Alpine.js, and DataTables ensures a rich, interactive user experience without the complexity of traditional JavaScript frameworks. The fat model/thin view architecture promotes maintainability and scalability, while the automated testing ensures high code quality and reliability. This structure is easily extendable for future features and fits perfectly within a larger ERP system modernization effort.