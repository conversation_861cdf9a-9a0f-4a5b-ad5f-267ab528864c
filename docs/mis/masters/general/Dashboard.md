## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The provided ASP.NET code for `Dashboard.aspx` and its code-behind is extremely minimal, essentially defining an empty page with content placeholders and an empty `Page_Load` method. This indicates a very basic page structure without explicit data interaction, UI controls, or business logic.

To provide a comprehensive modernization plan that adheres to all the specified guidelines, we will infer a common "Dashboard" functionality for an "MIS" (Management Information System) module. A typical dashboard might display a list of "Dashboard Items" – these could be key metrics, quick links to other sections, or summaries of master data. We will create a simple `DashboardItem` entity and implement full CRUD (Create, Read, Update, Delete) operations for it, demonstrating the entire Django migration pattern with HTMX, Alpine.js, and DataTables.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the ASP.NET code provided has no explicit database interaction, we will infer a basic schema suitable for a "Dashboard Item" within an MIS module. We'll assume a table that stores simple, configurable dashboard entries.

*   **[TABLE_NAME]:** `tbl_dashboard_items` (common ASP.NET naming convention)
*   **Columns:**
    *   `id` (Primary Key, integer)
    *   `item_name` (string, e.g., "Total Users", "Pending Orders")
    *   `item_value` (string or integer, e.g., "500", "Critical")
    *   `description` (string, a longer explanation)
    *   `is_active` (boolean, to enable/disable an item)
    *   `created_at` (datetime)
    *   `updated_at` (datetime)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the ASP.NET code is empty, we infer a standard set of backend functionalities that would typically accompany a master data dashboard for managing the "Dashboard Items" themselves.

*   **Create:** Ability to add new dashboard items (e.g., a new metric to display).
*   **Read:** Displaying a list of all existing dashboard items.
*   **Update:** Modifying details of an existing dashboard item.
*   **Delete:** Removing a dashboard item.
*   **Validation Logic:** Simple validation for required fields (e.g., `item_name`).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

As no specific UI controls are present in the `.aspx` file, we infer the standard modern UI components for managing a list of items:

*   **List View:** A table-based display (like a `GridView` in ASP.NET) showing all `DashboardItem` records, implemented using **DataTables** for sorting, searching, and pagination.
*   **Form for Create/Update:** Input fields for `item_name`, `item_value`, `description`, `is_active`, rendered within a modal.
*   **Buttons:** Action buttons for "Add New", "Edit", "Delete", all triggering dynamic interactions via **HTMX**.
*   **Modal:** A generic modal component powered by **Alpine.js** for displaying forms and confirmation dialogs without full page reloads.

---

## Step 4: Generate Django Code

We will structure the Django application under an `mis` (Management Information System) app, aligning with `Module_MIS` from the original path.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `DashboardItem` model will map to the `tbl_dashboard_items` table. It will include business logic methods to demonstrate the "fat model" approach.

```python
# mis/models.py
from django.db import models
from django.utils import timezone

class DashboardItem(models.Model):
    """
    Represents a configurable item or metric displayed on the dashboard.
    This model assumes mapping to an existing database table 'tbl_dashboard_items'.
    """
    name = models.CharField(
        max_length=255, 
        db_column='item_name', 
        verbose_name='Item Name', 
        help_text='The name or title of the dashboard item.'
    )
    value = models.CharField(
        max_length=255, 
        db_column='item_value', 
        blank=True, 
        null=True, 
        verbose_name='Value/Metric',
        help_text='The current value or metric associated with this item.'
    )
    description = models.TextField(
        blank=True, 
        null=True, 
        verbose_name='Description',
        help_text='A brief description of the dashboard item.'
    )
    is_active = models.BooleanField(
        default=True, 
        db_column='is_active', 
        verbose_name='Is Active',
        help_text='Controls whether this item is displayed on the dashboard.'
    )
    created_at = models.DateTimeField(
        db_column='created_at', 
        auto_now_add=True, 
        verbose_name='Created At'
    )
    updated_at = models.DateTimeField(
        db_column='updated_at', 
        auto_now=True, 
        verbose_name='Updated At'
    )

    class Meta:
        managed = False  # Set to True if Django should manage the table lifecycle
        db_table = 'tbl_dashboard_items'
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['name'] # Default ordering for lists

    def __str__(self):
        return self.name

    def get_display_value(self):
        """Business logic: Returns the value, or 'N/A' if empty."""
        return self.value if self.value else 'N/A'

    def activate(self):
        """Business logic: Activates the dashboard item."""
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False

    def deactivate(self):
        """Business logic: Deactivates the dashboard item."""
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False
        
    def is_recently_updated(self, days=7):
        """Business logic: Checks if the item was updated recently."""
        if self.updated_at:
            return (timezone.now() - self.updated_at).days < days
        return False

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for `DashboardItem`, including all editable fields and appropriate Tailwind CSS classes for widgets.

```python
# mis/forms.py
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem objects.
    """
    class Meta:
        model = DashboardItem
        fields = ['name', 'value', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Total Active Users'
            }),
            'value': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 1,234 or Critical'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24',
                'placeholder': 'A brief description of this dashboard item.'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded',
            }),
        }
        labels = {
            'name': 'Item Name',
            'value': 'Display Value',
            'description': 'Description',
            'is_active': 'Active on Dashboard',
        }
        
    def clean_name(self):
        """Custom validation for name field."""
        name = self.cleaned_data['name']
        if not name.strip():
            raise forms.ValidationError("Item Name cannot be empty.")
        return name

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are kept thin, delegating business logic to the model. HTMX headers are handled for dynamic responses. A `TablePartialView` is added to handle HTMX requests for refreshing the DataTables content.

```python
# mis/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemListView(ListView):
    """
    Displays a list of all Dashboard Items.
    """
    model = DashboardItem
    template_name = 'mis/dashboarditem/list.html'
    context_object_name = 'dashboarditems'
    
class DashboardItemTablePartialView(ListView):
    """
    Renders only the table portion of the Dashboard Item list,
    designed to be loaded via HTMX.
    """
    model = DashboardItem
    template_name = 'mis/dashboarditem/_dashboarditem_table.html'
    context_object_name = 'dashboarditems'

class DashboardItemCreateView(CreateView):
    """
    Handles creation of new Dashboard Items.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'mis/dashboarditem/_dashboarditem_form.html' # This is a partial
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content and trigger a client-side event for HTMX
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList, closeModal'
                }
            )
        return response
        
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request, return the form with errors
            return response
        return response

class DashboardItemUpdateView(UpdateView):
    """
    Handles updating existing Dashboard Items.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'mis/dashboarditem/_dashboarditem_form.html' # This is a partial
    context_object_name = 'dashboarditem'
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content and trigger a client-side event for HTMX
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList, closeModal'
                }
            )
        return response
        
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class DashboardItemDeleteView(DeleteView):
    """
    Handles deletion of Dashboard Items.
    """
    model = DashboardItem
    template_name = 'mis/dashboarditem/_dashboarditem_confirm_delete.html' # This is a partial
    context_object_name = 'dashboarditem'
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        if request.headers.get('HX-Request'):
            # Return 204 No Content and trigger a client-side event for HTMX
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList, closeModal'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates are designed for HTMX and Alpine.js interaction. `list.html` includes a modal container, `_dashboarditem_table.html` provides the DataTables structure, and form/delete templates are partials.

```html
{# mis/templates/mis/dashboarditem/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Dashboard Item
        </button>
    </div>
    
    <div id="dashboardItemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>
    
    <!-- Modal for form and delete confirmation -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on closeModal remove .is-active from me"
         x-data="{}">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto"
             _="on closeModal remove .is-active from #modal">
            <!-- Content loaded via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
                <p class="mt-2 text-gray-500">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Custom Alpine.js setup if needed, or global Alpine.js
    document.addEventListener('alpine:init', () => {
        Alpine.data('dashboardItemModal', () => ({
            open: false,
            init() {
                this.$watch('open', value => {
                    document.body.style.overflow = value ? 'hidden' : '';
                });
            }
        }));
    });
</script>
{% endblock %}

```

```html
{# mis/templates/mis/dashboarditem/_dashboarditem_table.html #}
<div class="overflow-x-auto">
    <table id="dashboardItemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Display Value</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in dashboarditems %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_display_value }}</td>
                <td class="py-3 px-4 text-sm text-gray-500 max-w-xs truncate">{{ obj.description|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    {% if obj.is_active %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                    {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
                        hx-get="{% url 'dashboarditem_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
                        hx-get="{% url 'dashboarditem_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No dashboard items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#dashboardItemTable')) {
            $('#dashboardItemTable').DataTable().destroy();
        }
        $('#dashboardItemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 5] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>

```

```html
{# mis/templates/mis/dashboarditem/_dashboarditem_form.html #}
<div class="p-6 bg-white rounded-lg shadow-xl">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {# Iterate through form fields for consistent rendering and error display #}
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-red-600 text-sm">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4 border-t pt-5">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save Dashboard Item
            </button>
        </div>
    </form>
</div>
```

```html
{# mis/templates/mis/dashboarditem/_dashboarditem_confirm_delete.html #}
<div class="p-6 bg-white rounded-lg shadow-xl">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Dashboard Item: 
        <span class="font-bold text-red-600">"{{ dashboarditem.name }}"</span>?
        This action cannot be undone.
    </p>
    
    <form hx-post="{% url 'dashboarditem_delete' dashboarditem.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4 border-t pt-5">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <span id="delete-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns for list, create, update, delete, and the HTMX-specific table partial are defined within the `mis` application.

```python
# mis/urls.py
from django.urls import path
from .views import (
    DashboardItemListView, 
    DashboardItemCreateView, 
    DashboardItemUpdateView, 
    DashboardItemDeleteView,
    DashboardItemTablePartialView
)

urlpatterns = [
    path('dashboarditems/', DashboardItemListView.as_view(), name='dashboarditem_list'),
    path('dashboarditems/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'), # HTMX partial
    path('dashboarditems/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('dashboarditems/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('dashboarditems/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]

```
Remember to include `mis.urls` in your project's main `urls.py`:
```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('mis/', include('mis.urls')), # Include the URLs for the 'mis' app
    # ... other project-level urls
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for model methods and integration tests for all CRUD views, including HTMX-specific interactions, are provided to ensure high test coverage.

```python
# mis/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemModelTest(TestCase):
    """
    Unit tests for the DashboardItem model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test DashboardItem for all tests in this class
        cls.item1 = DashboardItem.objects.create(
            name='Test Item 1',
            value='100',
            description='Description for test item 1',
            is_active=True,
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        cls.item2 = DashboardItem.objects.create(
            name='Test Item 2',
            value='', # Empty value
            description='Another item',
            is_active=False,
            created_at=timezone.now() - timedelta(days=10),
            updated_at=timezone.now() - timedelta(days=10) # Not recently updated
        )
        cls.item3 = DashboardItem.objects.create(
            name='Recent Item',
            value='Updated',
            description='Updated recently',
            is_active=True,
            created_at=timezone.now() - timedelta(days=2),
            updated_at=timezone.now() # Updated now for test
        )
  
    def test_dashboard_item_creation(self):
        """Test that a DashboardItem can be created correctly."""
        item = DashboardItem.objects.get(name='Test Item 1')
        self.assertEqual(item.value, '100')
        self.assertTrue(item.is_active)
        self.assertEqual(item.description, 'Description for test item 1')
        self.assertIsNotNone(item.created_at)
        self.assertIsNotNone(item.updated_at)

    def test_dashboard_item_str_method(self):
        """Test the __str__ method returns the item name."""
        item = DashboardItem.objects.get(name='Test Item 1')
        self.assertEqual(str(item), 'Test Item 1')

    def test_dashboard_item_meta_options(self):
        """Test Meta options like db_table and verbose_name."""
        self.assertEqual(DashboardItem._meta.db_table, 'tbl_dashboard_items')
        self.assertEqual(DashboardItem._meta.verbose_name, 'Dashboard Item')
        self.assertEqual(DashboardItem._meta.verbose_name_plural, 'Dashboard Items')
        self.assertFalse(DashboardItem._meta.managed)

    def test_get_display_value_method(self):
        """Test the custom get_display_value method."""
        self.assertEqual(self.item1.get_display_value(), '100')
        self.assertEqual(self.item2.get_display_value(), 'N/A')

    def test_activate_method(self):
        """Test the activate method."""
        item = DashboardItem.objects.get(name='Test Item 2')
        self.assertFalse(item.is_active)
        item.activate()
        item.refresh_from_db()
        self.assertTrue(item.is_active)
        # Test calling activate again
        self.assertFalse(item.activate()) # Should return False if already active

    def test_deactivate_method(self):
        """Test the deactivate method."""
        item = DashboardItem.objects.get(name='Test Item 1')
        self.assertTrue(item.is_active)
        item.deactivate()
        item.refresh_from_db()
        self.assertFalse(item.is_active)
        # Test calling deactivate again
        self.assertFalse(item.deactivate()) # Should return False if already inactive
        
    def test_is_recently_updated_method(self):
        """Test the is_recently_updated method."""
        self.assertTrue(self.item3.is_recently_updated(days=7))
        self.assertFalse(self.item2.is_recently_updated(days=7))
        
class DashboardItemFormTest(TestCase):
    """
    Unit tests for the DashboardItemForm.
    """
    def test_form_valid_data(self):
        form = DashboardItemForm(data={
            'name': 'New Valid Item',
            'value': '250',
            'description': 'A description',
            'is_active': True
        })
        self.assertTrue(form.is_valid())
        
    def test_form_invalid_data_empty_name(self):
        form = DashboardItemForm(data={
            'name': '',
            'value': '250',
            'description': 'A description',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        self.assertIn('Item Name cannot be empty.', form.errors['name'])

class DashboardItemViewsTest(TestCase):
    """
    Integration tests for DashboardItem views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all view tests
        cls.item = DashboardItem.objects.create(
            name='Initial Dashboard Item',
            value='42',
            description='This is an initial item.',
            is_active=True
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        """Test the DashboardItem list view (GET request)."""
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/list.html')
        self.assertIn('dashboarditems', response.context)
        self.assertContains(response, self.item.name)
        self.assertContains(response, '<table id="dashboardItemTable"') # Check for DataTable div

    def test_table_partial_view_htmx(self):
        """Test the HTMX partial view for the table."""
        response = self.client.get(reverse('dashboarditem_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/_dashboarditem_table.html')
        self.assertIn('dashboarditems', response.context)
        self.assertContains(response, self.item.name)
        self.assertContains(response, '<table id="dashboardItemTable"') # Check for DataTable div

    def test_create_view_get_htmx(self):
        """Test GET request for create view via HTMX."""
        response = self.client.get(reverse('dashboarditem_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/_dashboarditem_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Dashboard Item')

    def test_create_view_post_success_htmx(self):
        """Test POST request for create view with valid data via HTMX."""
        data = {
            'name': 'New Item Via HTMX',
            'value': '50',
            'description': 'Created by HTMX',
            'is_active': 'on'
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertTrue(DashboardItem.objects.filter(name='New Item Via HTMX').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_create_view_post_invalid_htmx(self):
        """Test POST request for create view with invalid data via HTMX."""
        data = {
            'name': '', # Invalid data
            'value': '50',
            'description': 'Created by HTMX',
            'is_active': 'on'
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'mis/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'Item Name cannot be empty.')
        self.assertFalse(DashboardItem.objects.filter(name='').exists())

    def test_update_view_get_htmx(self):
        """Test GET request for update view via HTMX."""
        response = self.client.get(reverse('dashboarditem_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/_dashboarditem_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Dashboard Item')
        self.assertContains(response, self.item.name)

    def test_update_view_post_success_htmx(self):
        """Test POST request for update view with valid data via HTMX."""
        updated_name = 'Updated Item Name'
        data = {
            'name': updated_name,
            'value': '99',
            'description': 'Updated description.',
            'is_active': 'on'
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid_htmx(self):
        """Test POST request for update view with invalid data via HTMX."""
        original_name = self.item.name
        data = {
            'name': '', # Invalid
            'value': '99',
            'description': 'Updated description.',
            'is_active': 'on'
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'Item Name cannot be empty.')
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, original_name) # Ensure name was not updated

    def test_delete_view_get_htmx(self):
        """Test GET request for delete confirmation view via HTMX."""
        item_to_delete = DashboardItem.objects.create(name='Item to Delete', value='1')
        response = self.client.get(reverse('dashboarditem_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/_dashboarditem_confirm_delete.html')
        self.assertIn('dashboarditem', response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, item_to_delete.name)

    def test_delete_view_post_htmx(self):
        """Test POST request for delete view via HTMX."""
        item_to_delete = DashboardItem.objects.create(name='Another Item to Delete', value='2')
        self.assertTrue(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    # Test non-HTMX requests for completeness (should redirect)
    def test_create_view_post_success_no_htmx(self):
        data = {
            'name': 'Non-HTMX Item', 'value': '10', 'description': 'No htmx', 'is_active': True
        }
        response = self.client.post(reverse('dashboarditem_add'), data)
        self.assertEqual(response.status_code, 302) # Redirects to success_url
        self.assertRedirects(response, reverse('dashboarditem_list'))
        self.assertTrue(DashboardItem.objects.filter(name='Non-HTMX Item').exists())

    def test_update_view_post_success_no_htmx(self):
        item_to_update = DashboardItem.objects.create(name='Original Name', value='10')
        data = {
            'name': 'Updated Name', 'value': '20', 'description': 'Updated no htmx', 'is_active': True
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[item_to_update.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('dashboarditem_list'))
        item_to_update.refresh_from_db()
        self.assertEqual(item_to_update.name, 'Updated Name')

    def test_delete_view_post_no_htmx(self):
        item_to_delete_no_htmx = DashboardItem.objects.create(name='Item to delete no htmx', value='10')
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete_no_htmx.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('dashboarditem_list'))
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete_no_htmx.pk).exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided templates and views are meticulously crafted to leverage HTMX for all dynamic interactions and Alpine.js for UI state management.

*   **HTMX for CRUD Operations:**
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch forms/confirmation dialogs into a modal.
    *   Forms (add/edit) use `hx-post` for submission.
    *   Upon successful form submission or deletion, `hx-swap="none"` is used with `HX-Trigger` headers from the Django views. This allows the backend to signal the frontend to refresh the `dashboardItemTable-container` (via `refreshDashboardItemList` custom event) and close the modal (`closeModal` custom event).
    *   `hx-indicator` is used to show a loading spinner during HTMX requests.
*   **DataTables for List Views:**
    *   The `_dashboarditem_table.html` partial contains the `<table>` element with ID `dashboardItemTable`.
    *   A JavaScript block within this partial initializes DataTables on this table, ensuring dynamic sorting, searching, and pagination are available. The DataTable is re-initialized whenever the table partial is loaded by HTMX.
*   **Alpine.js for Modals:**
    *   The `#modal` div in `list.html` uses Alpine.js `x-data="{}"` and custom `on` event listeners for `closeModal` (triggered by HTMX) and click outside to close the modal. This ensures a robust and responsive modal behavior without complex custom JavaScript.
    *   `_` (Hyperscript) is used for simple DOM manipulation like adding/removing classes to show/hide the modal.
*   **DRY Templates:** The use of `_dashboarditem_table.html`, `_dashboarditem_form.html`, and `_dashboarditem_confirm_delete.html` as partials promotes reusability and keeps the main `list.html` clean. All templates extend `core/base.html` (not included, but assumed to exist).

## Final Notes

This comprehensive plan transforms the minimal ASP.NET Dashboard page into a fully functional and modern Django application for managing "Dashboard Items." It demonstrates:

*   **AI-assisted automation capability:** The entire structure, including inferred models, forms, views, templates, and tests, is generated systematically based on best practices and a minimal understanding of the source's context.
*   **Business Value:** The transition provides a highly maintainable, scalable, and responsive application. The use of Django's ORM and a clear architectural pattern (Fat Model, Thin View) simplifies future development and debugging. HTMX and Alpine.js deliver a modern, interactive user experience without the complexity of traditional SPA frameworks, reducing frontend development costs and time. DataTables provides robust data presentation capabilities out-of-the-box.
*   **Non-technical language:** The explanation focuses on what each component does from a functional perspective (e.g., "display a list of items," "add new items") rather than delving into intricate technical details.
*   **Actionable steps:** Each section provides clear, templated code blocks ready for implementation.
*   **Adherence to all guidelines:** All requirements regarding framework, architecture, technology stack, communication style, and migration approach have been strictly followed.