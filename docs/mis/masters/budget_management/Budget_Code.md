## ASP.NET to Django Conversion Script:

This document outlines a clear, automated modernization plan to transition your legacy ASP.NET application, specifically the "Budget Code" module, to a robust and modern Django 5.0+ solution. Our approach prioritizes automation, leveraging AI-assisted tools to streamline the conversion process, minimize manual effort, and ensure a high-quality, maintainable outcome. We will focus on key business outcomes such as improved performance, enhanced user experience, and a future-proof architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include base.html template code in your output - assume it already exists and is extended.
*   Focus ONLY on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the `SqlDataSource1` definition:

*   **Table Name:** `tblMIS_BudgetCode`
*   **Columns:**
    *   `Id` (Primary Key, integer) - Identified from `DataKeyNames="Id"` and `DeleteCommand`.
    *   `Description` (String) - Identified from `InsertCommand`, `UpdateCommand`, `TemplateField HeaderText="Description"`.
    *   `Symbol` (String, MaxLength=2) - Identified from `InsertCommand`, `UpdateCommand`, `TemplateField HeaderText="Symbol"`, and `MaxLength="2"` attribute.

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

*   **Create:**
    *   Triggered by `btnInsert` in `GridView1`'s `FooterTemplate` (CommandName "Add") and `EmptyDataTemplate` (CommandName "Add1").
    *   C# `GridView1_RowCommand` handles insertion by setting `SqlDataSource1.InsertParameters` and calling `SqlDataSource1.Insert()`.
    *   Validation: `RequiredFieldValidator` for `Description` and `Symbol`. Uniqueness check for `Symbol` before insertion.
*   **Read:**
    *   `GridView1` is populated using `DataSourceID="SqlDataSource1"`.
    *   `SqlDataSource1.SelectCommand="SELECT * FROM [tblMIS_BudgetCode] order by [Id] desc"` retrieves all records.
*   **Update:**
    *   Triggered by the `ShowEditButton` in `GridView1`'s `CommandField`.
    *   C# `GridView1_RowUpdating` handles the update logic by setting `SqlDataSource1.UpdateParameters` and calling `SqlDataSource1.Update()`.
    *   Validation: `RequiredFieldValidator` for `Description` (`txtTerms1`).
*   **Delete:**
    *   Triggered by the `ShowDeleteButton` in `GridView1`'s `CommandField`.
    *   C# `GridView1_RowDeleted` simply updates a message, the actual deletion is handled by `SqlDataSource1.DeleteCommand`.
*   **Validation Logic:**
    *   `RequiredFieldValidator` for `Description` and `Symbol` fields during insert and update.
    *   Custom C# logic (`GridView1_RowCommand`) checks for duplicate `Symbol` values before inserting. This will be handled by Django's `unique=True` field constraint.
    *   Client-side confirmation dialogs (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) are present.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **GridView1:** This is the primary data display and interaction component. It acts as a table for listing, with in-row editing, deleting, and a footer row/empty data template for adding new records. This will be replaced by a Django template using DataTables, with HTMX for dynamic CRUD operations.
*   **TextBox (`txtTerms1`, `txtTerms2`, `txtTerms3`, `txtValue1`, `txtValue2`, `txtValue3`):** Used for inputting `Description` and `Symbol`. These map directly to Django `forms.TextInput` widgets.
*   **Button/LinkButton (`btnInsert`, `btnCancel`, `Edit`, `Delete` links):** Trigger actions. These will be replaced by standard HTML buttons with HTMX attributes.
*   **Label (`lblTerms`, `lblvalue`, `lblMessage`):** Display data or messages. `lblMessage` will be replaced by Django's messages framework and HTMX `HX-Trigger` to notify the client.
*   **RequiredFieldValidator:** Client-side and server-side validation. This will be replaced by Django's form validation and model field constraints.
*   **`yui-datatable.css`:** Indicates existing table styling, which will be migrated to DataTables with Tailwind CSS.
*   **Client-side JS (`PopUpMsg.js`, `loadingNotifier.js`, `confirmationAdd()`, etc.):** These functionalities will be replaced by HTMX for AJAX requests and Alpine.js for UI state management (like modals) and simplified confirmations.

### Step 4: Generate Django Code

We will create a Django application named `mis` (Module Information System) to house the Budget Code module.

#### 4.1 Models (`mis/models.py`)

This model will map directly to your existing `tblMIS_BudgetCode` table. The `Symbol` field will be set to `unique=True` to enforce the same business rule as in the ASP.NET code.

```python
from django.db import models

class BudgetCode(models.Model):
    # 'Id' is the primary key and will be implicitly handled by Django if not specified,
    # or explicitly if your existing table's 'Id' is not an auto-incrementing integer.
    # Assuming it's an auto-incrementing PK, Django handles it.
    
    description = models.CharField(
        db_column='Description', 
        max_length=255, # Assuming a reasonable max length for description
        verbose_name='Description'
    )
    symbol = models.CharField(
        db_column='Symbol', 
        max_length=2, 
        unique=True, # Enforces uniqueness for the symbol, replicating ASP.NET's check
        verbose_name='Symbol'
    )

    class Meta:
        managed = False  # Django will not manage this table's creation/alteration
        db_table = 'tblMIS_BudgetCode' # Map to your existing table name
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'
        ordering = ['-id'] # Replicates 'order by [Id] desc' from SELECT command

    def __str__(self):
        return f"{self.description} ({self.symbol})"

    # Business logic methods, if any more complex validation or operations are needed.
    # For example, if 'Symbol' had complex generation logic.
    def get_display_name(self):
        """Returns the description and symbol for display."""
        return self.__str__()
```

#### 4.2 Forms (`mis/forms.py`)

This form will handle the data input and validation for `BudgetCode` objects.

```python
from django import forms
from .models import BudgetCode

class BudgetCodeForm(forms.ModelForm):
    class Meta:
        model = BudgetCode
        fields = ['description', 'symbol']
        widgets = {
            'description': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter description'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'maxlength': '2',
                'placeholder': 'Enter symbol (max 2 chars)'
            }),
        }
        
    def clean_symbol(self):
        # The 'unique=True' on the model field already handles most uniqueness validation.
        # This method can be used for additional custom symbol validation if needed.
        symbol = self.cleaned_data['symbol']
        return symbol.upper() # Consistent with ASP.NET's .ToUpper() for symbol
```

#### 4.3 Views (`mis/views.py`)

These Class-Based Views will manage your CRUD operations. A dedicated partial view is created for the DataTable content to enable efficient HTMX updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import BudgetCode
from .forms import BudgetCodeForm

# View for the main Budget Code list page
class BudgetCodeListView(ListView):
    model = BudgetCode
    template_name = 'mis/budgetcode/list.html'
    context_object_name = 'budget_codes' # Renamed for clarity in templates

# View for HTMX-loaded DataTable partial
class BudgetCodeTablePartialView(ListView):
    model = BudgetCode
    template_name = 'mis/budgetcode/_budgetcode_table.html'
    context_object_name = 'budget_codes'

# View for adding a new Budget Code
class BudgetCodeCreateView(CreateView):
    model = BudgetCode
    form_class = BudgetCodeForm
    template_name = 'mis/budgetcode/_budgetcode_form.html' # Use partial for modal
    success_url = reverse_lazy('budgetcode_list')

    def form_valid(self, form):
        # The .clean_symbol() in the form already converts to upper.
        # Super call handles saving the instance.
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Code added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetCodeList' # Custom event to refresh the table
                }
            )
        return response # For non-HTMX requests (unlikely in this setup)

# View for updating an existing Budget Code
class BudgetCodeUpdateView(UpdateView):
    model = BudgetCode
    form_class = BudgetCodeForm
    template_name = 'mis/budgetcode/_budgetcode_form.html' # Use partial for modal
    success_url = reverse_lazy('budgetcode_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Code updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetCodeList'
                }
            )
        return response

# View for deleting a Budget Code
class BudgetCodeDeleteView(DeleteView):
    model = BudgetCode
    template_name = 'mis/budgetcode/_budgetcode_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('budgetcode_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Budget Code deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetCodeList'
                }
            )
        return response
```

#### 4.4 Templates

These templates will provide the user interface, heavily relying on HTMX and Alpine.js for dynamic interactions. Create a `templates/mis/budgetcode/` directory in your `mis` app.

**`mis/templates/mis/budgetcode/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Budget Codes</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200"
            hx-get="{% url 'budgetcode_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Budget Code
        </button>
    </div>
    
    <div id="budgetcodeTable-container"
         hx-trigger="load, refreshBudgetCodeList from:body"
         hx-get="{% url 'budgetcode_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Budget Codes...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 ease-out"
             _="on load transition transform scale-100 opacity-100 duration-300 ease-out">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is primarily for UI state, like showing/hiding modals.
        // HTMX handles data fetching and DOM manipulation.
    });
</script>
{% endblock %}
```

**`mis/templates/mis/budgetcode/_budgetcode_table.html`**

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="budgetcodeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in budget_codes %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.description }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center font-mono">{{ obj.symbol }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white py-1.5 px-3 rounded-md text-xs font-semibold mr-2 transition duration-150"
                        hx-get="{% url 'budgetcode_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white py-1.5 px-3 rounded-md text-xs font-semibold transition duration-150"
                        hx-get="{% url 'budgetcode_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-6 px-4 text-center text-sm text-gray-500">
                    No Budget Codes found. Click "Add New Budget Code" to get started!
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script -->
<script>
    $(document).ready(function() {
        $('#budgetcodeTable').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            // Customize DataTables to work well with Tailwind CSS (if using
            // a custom DataTables-Tailwind integration or a plugin)
        });
    });
</script>
```

**`mis/templates/mis/budgetcode/_budgetcode_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Budget Code</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal } else { alert('An error occurred. Please check your input.'); }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 space-y-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}

            {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <ul class="block sm:inline">
                    {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200">
                Save Budget Code
            </button>
        </div>
    </form>
</div>
```

**`mis/templates/mis/budgetcode/_budgetcode_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Budget Code: 
        <span class="font-bold">{{ object.description }} ({{ object.symbol }})</span>? This action cannot be undone.</p>
    
    <form hx-post="{% url 'budgetcode_delete' object.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal } else { alert('An error occurred during deletion.'); }">
        {% csrf_token %}
        <input type="hidden" name="confirm" value="true">
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200">
                Delete Budget Code
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`mis/urls.py`)

These URL patterns will link your views to specific routes.

```python
from django.urls import path
from .views import (
    BudgetCodeListView, 
    BudgetCodeTablePartialView,
    BudgetCodeCreateView, 
    BudgetCodeUpdateView, 
    BudgetCodeDeleteView
)

urlpatterns = [
    path('budgetcodes/', BudgetCodeListView.as_view(), name='budgetcode_list'),
    path('budgetcodes/table/', BudgetCodeTablePartialView.as_view(), name='budgetcode_table'), # HTMX partial load
    path('budgetcodes/add/', BudgetCodeCreateView.as_view(), name='budgetcode_add'),
    path('budgetcodes/edit/<int:pk>/', BudgetCodeUpdateView.as_view(), name='budgetcode_edit'),
    path('budgetcodes/delete/<int:pk>/', BudgetCodeDeleteView.as_view(), name='budgetcode_delete'),
]
```

Remember to include these app URLs in your project's main `urls.py`:

```python
# In your_project_name/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('mis/', include('mis.urls')), # Include your new app's URLs
]
```

#### 4.6 Tests (`mis/tests.py`)

Comprehensive tests are crucial for verifying the correctness of your migration. This includes unit tests for the model and integration tests for all views, including HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import BudgetCode
from .forms import BudgetCodeForm
import json

class BudgetCodeModelTest(TestCase):
    """
    Unit tests for the BudgetCode model, verifying its structure and behavior.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for all tests
        BudgetCode.objects.create(
            description='Marketing Budget', 
            symbol='MK'
        )
        BudgetCode.objects.create(
            description='Development Budget', 
            symbol='DV'
        )
  
    def test_budget_code_creation(self):
        """Test that a BudgetCode object can be created successfully."""
        obj = BudgetCode.objects.get(id=1)
        self.assertEqual(obj.description, 'Marketing Budget')
        self.assertEqual(obj.symbol, 'MK')
        self.assertTrue(isinstance(obj, BudgetCode))

    def test_description_label(self):
        """Test the verbose name for the 'description' field."""
        obj = BudgetCode.objects.get(id=1)
        field_label = obj._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Description')
        
    def test_symbol_label(self):
        """Test the verbose name for the 'symbol' field."""
        obj = BudgetCode.objects.get(id=1)
        field_label = obj._meta.get_field('symbol').verbose_name
        self.assertEqual(field_label, 'Symbol')

    def test_symbol_max_length(self):
        """Test the maximum length constraint for the 'symbol' field."""
        obj = BudgetCode.objects.get(id=1)
        max_length = obj._meta.get_field('symbol').max_length
        self.assertEqual(max_length, 2)
        
    def test_symbol_uniqueness(self):
        """Test that the 'symbol' field enforces uniqueness."""
        with self.assertRaises(Exception): # Expecting an IntegrityError or ValidationError
            BudgetCode.objects.create(
                description='Duplicate Symbol', 
                symbol='MK' # This should fail due to unique=True
            )
        
    def test_object_name_is_description_and_symbol(self):
        """Test the __str__ method of the BudgetCode model."""
        obj = BudgetCode.objects.get(id=1)
        expected_object_name = f"{obj.description} ({obj.symbol})"
        self.assertEqual(str(obj), expected_object_name)

    def test_ordering(self):
        """Test that the default ordering is by ID descending."""
        codes = BudgetCode.objects.all()
        self.assertEqual(codes[0].symbol, 'DV') # ID 2
        self.assertEqual(codes[1].symbol, 'MK') # ID 1


class BudgetCodeViewsTest(TestCase):
    """
    Integration tests for the BudgetCode views, covering CRUD operations and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create data accessible to all test methods
        cls.budget_code1 = BudgetCode.objects.create(
            description='Test Budget 1', 
            symbol='T1'
        )
        cls.budget_code2 = BudgetCode.objects.create(
            description='Test Budget 2', 
            symbol='T2'
        )
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolation
        self.client = Client()
    
    def test_list_view_get(self):
        """Test the BudgetCode list view (GET request)."""
        response = self.client.get(reverse('budgetcode_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/budgetcode/list.html')
        self.assertTrue('budget_codes' in response.context)
        self.assertIn(self.budget_code1, response.context['budget_codes'])
        self.assertIn(self.budget_code2, response.context['budget_codes'])

    def test_table_partial_view_get(self):
        """Test the HTMX-loaded table partial view (GET request)."""
        response = self.client.get(reverse('budgetcode_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/budgetcode/_budgetcode_table.html')
        self.assertTrue('budget_codes' in response.context)
        self.assertContains(response, 'Test Budget 1')
        self.assertContains(response, 'Test Budget 2')
        # Check for DataTables script
        self.assertContains(response, '$("#budgetcodeTable").DataTable(')

    def test_create_view_get(self):
        """Test the BudgetCode create form view (GET request for modal)."""
        response = self.client.get(reverse('budgetcode_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/budgetcode/_budgetcode_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], BudgetCodeForm)

    def test_create_view_post_success_htmx(self):
        """Test successful creation via HTMX POST request."""
        initial_count = BudgetCode.objects.count()
        data = {
            'description': 'New Budget Code',
            'symbol': 'NC',
        }
        response = self.client.post(reverse('budgetcode_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX success response: 204 No Content with HX-Trigger
        self.assertEqual(response.status_code, 204)
        self.assertTrue(BudgetCode.objects.filter(description='New Budget Code').exists())
        self.assertEqual(BudgetCode.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetCodeList')
        
        # Verify message framework usage (requires test client setup to capture messages)
        messages = list(response.context['messages']) if 'messages' in response.context else []
        # messages for 204 are typically handled client-side or implicitly.
        # This check is more relevant for full page redirects.

    def test_create_view_post_invalid_htmx(self):
        """Test invalid creation data via HTMX POST request (duplicate symbol)."""
        initial_count = BudgetCode.objects.count()
        data = {
            'description': 'Duplicate Symbol Attempt',
            'symbol': 'T1', # T1 already exists
        }
        response = self.client.post(reverse('budgetcode_add'), data, HTTP_HX_REQUEST='true')

        # For invalid forms, HTMX usually returns 200 with the rendered form containing errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/budgetcode/_budgetcode_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Symbol with this Symbol already exists.', response.context['form'].errors['symbol'])
        self.assertEqual(BudgetCode.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test the BudgetCode update form view (GET request for modal)."""
        obj = self.budget_code1
        response = self.client.get(reverse('budgetcode_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/budgetcode/_budgetcode_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success_htmx(self):
        """Test successful update via HTMX POST request."""
        obj = self.budget_code1
        data = {
            'description': 'Updated Description',
            'symbol': obj.symbol, # Symbol remains same or valid unique
        }
        response = self.client.post(reverse('budgetcode_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db() # Reload object to get updated data
        self.assertEqual(obj.description, 'Updated Description')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetCodeList')

    def test_update_view_post_invalid_htmx(self):
        """Test invalid update data via HTMX POST request (duplicate symbol)."""
        obj_to_update = self.budget_code1
        # Try to update budget_code1's symbol to budget_code2's symbol
        data = {
            'description': obj_to_update.description,
            'symbol': self.budget_code2.symbol, # This symbol already exists
        }
        response = self.client.post(reverse('budgetcode_edit', args=[obj_to_update.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/budgetcode/_budgetcode_form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Symbol with this Symbol already exists.', response.context['form'].errors['symbol'])
        obj_to_update.refresh_from_db()
        self.assertNotEqual(obj_to_update.symbol, self.budget_code2.symbol) # Symbol should not have changed

    def test_delete_view_get(self):
        """Test the BudgetCode delete confirmation view (GET request for modal)."""
        obj = self.budget_code1
        response = self.client.get(reverse('budgetcode_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/budgetcode/_budgetcode_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post_success_htmx(self):
        """Test successful deletion via HTMX POST request."""
        obj_to_delete = self.budget_code1
        initial_count = BudgetCode.objects.count()
        response = self.client.post(reverse('budgetcode_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertEqual(BudgetCode.objects.count(), initial_count - 1)
        self.assertFalse(BudgetCode.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetCodeList')
```

### Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for all dynamic updates and form submissions:**
    *   The `list.html` uses `hx-get` to load the table partial `_budgetcode_table.html` on `load` and `refreshBudgetCodeList` events.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to load their respective forms/confirmations into the `#modalContent` div.
    *   Form submissions (`_budgetcode_form.html`, `_budgetcode_confirm_delete.html`) use `hx-post` with `hx-swap="none"` and `hx-on::after-request` to handle success (close modal, trigger refresh) or failure (display errors).
    *   `HX-Trigger: 'refreshBudgetCodeList'` is sent back from successful `Create`, `Update`, `Delete` views to prompt the table to reload.
*   **Alpine.js for client-side reactivity and modals:**
    *   The modal (`id="modal"`) uses Alpine.js's `_` attribute (`on click add .is-active to #modal`) to control its visibility. This simple approach avoids complex JavaScript.
    *   The `on load transition` within `modalContent` provides a smooth visual transition when the modal appears.
*   **DataTables for all list views with sorting and filtering:**
    *   The `_budgetcode_table.html` partial includes a `<script>` block that initializes `$('#budgetcodeTable').DataTable()`. This enables client-side pagination, searching, and sorting automatically.
    *   Ensure DataTables CSS and JS are included in your `core/base.html` (e.g., from CDN).
*   **All interactions work without full page reloads:** This is the core benefit of the HTMX approach. All CRUD operations are performed via AJAX, updating only necessary parts of the page.
*   **Ensure proper `HX-Trigger` responses for list refreshes after CRUD operations:** This is handled by returning `HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetCodeList'})` from the `form_valid` and `delete` methods of the `Create`, `Update`, and `Delete` views.

### Final Notes

*   **Database Setup:** Ensure your Django `settings.py` is configured to connect to your existing `LocalSqlServer` database using the appropriate database backend (e.g., `django-mssql-backend` for SQL Server).
*   **Tailwind CSS:** Integrate Tailwind CSS into your Django project (typically by including its CDN link in `core/base.html` or compiling it) so that the provided CSS classes apply correctly.
*   **Dependencies:** Make sure `django-htmx` is installed and added to `INSTALLED_APPS` in `settings.py`, and the HTMX and Alpine.js CDN links are in your `core/base.html`.
*   **Error Handling:** The `hx-on::after-request` in the form templates provides basic client-side error notification. More sophisticated error handling (e.g., displaying server-side validation errors in a non-field error section) can be implemented.
*   **Security:** Always remember to protect your views with proper authentication and authorization (e.g., Django's `LoginRequiredMixin`, `PermissionRequiredMixin`) as needed. This plan focuses on structural conversion.