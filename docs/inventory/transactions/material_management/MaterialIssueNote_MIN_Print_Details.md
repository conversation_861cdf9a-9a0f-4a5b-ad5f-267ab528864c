## ASP.NET to Django Conversion Script: Material Issue Note Print Details

This modernization plan outlines the strategic transition of your ASP.NET Material Issue Note (MIN) Print Details page to a robust, modern Django application. Our approach leverages AI-assisted automation to systematically convert legacy code into a maintainable, high-performance Django solution. We prioritize interactive web interfaces using HTMX and Alpine.js, ensuring a seamless user experience.

### Business Benefits of Django Modernization:
- **Reduced Technical Debt:** Moves away from outdated frameworks, improving code quality and maintainability.
- **Enhanced Performance:** Django's optimized ORM and Caching capabilities deliver faster response times.
- **Improved User Experience:** Interactive frontends with HTMX and Alpine.js provide a snappier, more modern feel without full page reloads.
- **Scalability:** Django's architecture is designed to scale with your business needs.
- **Cost Efficiency:** Python's ecosystem and Django's rapid development features can lead to lower development and maintenance costs.
- **Future-Proofing:** Adopts widely supported, open-source technologies, mitigating vendor lock-in.
- **Automated Migration:** Our methodology focuses on repeatable, AI-guided processes to minimize manual effort and human error.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`MaterialIssueNote`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Aim for at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define corresponding Django models.

**Instructions:**
From the C# code-behind, the primary entity is the Material Issue Note (MIN), which involves `tblInv_MaterialIssue_Master` and `tblInv_MaterialIssue_Details`. The report also pulls data from several related lookup tables.

**Main Entities:**
- **`tblInv_MaterialIssue_Master`**: Represents the header information of a Material Issue Note.
    - `Id` (Primary Key, integer)
    - `MINNo` (Material Issue Note Number, string)
    - `MRSNo` (Material Requisition System Number, string)
    - `SysDate` (System Date / MIN Date, datetime)
    - `SessionId` (ID of the employee who generated it, integer - FK to `tblHR_OfficeStaff`)
    - `CompId` (Company ID, integer - FK to Company Master, assumed for user context)

- **`tblInv_MaterialIssue_Details`**: Represents the line items of a Material Issue Note.
    - `Id` (Primary Key, integer)
    - `MId` (Foreign Key to `tblInv_MaterialIssue_Master.Id`, integer)
    - `MRSId` (Foreign Key to `tblInv_MaterialRequisition_Details.Id`, integer - this links to a specific requisition *detail* line)
    - `IssueQty` (Quantity Issued, double)

**Related Lookup Tables (inferred from joins):**
- **`tblInv_MaterialRequisition_Master`**: Material Requisition Master (linked by `tblInv_MaterialIssue_Master.MRSId`)
    - `Id` (Primary Key)
- **`tblInv_MaterialRequisition_Details`**: Material Requisition Details (linked by `tblInv_MaterialIssue_Details.MRSId`)
    - `Id` (Primary Key)
    - `MId` (FK to `tblInv_MaterialRequisition_Master.Id`)
    - `ItemId` (FK to `tblDG_Item_Master.Id`)
    - `DeptId` (FK to `BusinessGroup.Id`)
    - `WONo` (Work Order Number, string)
    - `ReqQty` (Requested Quantity, double)
    - `Remarks` (Remarks, string)
- **`tblDG_Item_Master`**: Item Master
    - `Id` (Primary Key)
    - `ItemCode` (Item Code, string)
    - `ManfDesc` (Manufacturer Description / Item Description, string)
    - `UOMBasic` (Unit of Measure Basic ID, integer - FK to `Unit_Master`)
- **`Unit_Master`**: Unit of Measure Master
    - `Id` (Primary Key)
    - `Symbol` (UOM Symbol, string)
- **`BusinessGroup`**: Business Group / Department Master
    - `Id` (Primary Key)
    - `Symbol` (Department Symbol, string)
- **`tblHR_OfficeStaff`**: HR Office Staff (Employee Master)
    - `EmpId` (Primary Key)
    - `Title` (Title, string)
    - `EmployeeName` (Employee Name, string)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and data processing logic in the ASP.NET code.

**Instructions:**
The ASP.NET page is a **Read** operation, specifically for printing details of a Material Issue Note. It aggregates data from multiple tables to generate a report.

-   **Read (Detail View):** The `Page_Init` method fetches a specific `MaterialIssueNoteMaster` entry using `MId` from the query string. It then iterates through its `MaterialIssueDetail` entries, and for each detail, it queries `MaterialRequisitionDetail`, `ItemMaster`, `UnitMaster`, `BusinessGroup`, and `OfficeStaff` to build a comprehensive data set. This data is then bound to a Crystal Report Viewer. This complex data retrieval process will be translated into optimized Django ORM queries within the models or a dedicated `MaterialIssueNoteManager`.
-   **No direct Create, Update, Delete:** The provided ASP.NET code does not include explicit C/U/D operations for Material Issue Notes. However, in a full application modernization, these functionalities are standard. We will generate the necessary Django views and forms for these operations as part of the comprehensive plan, adhering to the requirement for `CreateView`, `UpdateView`, and `DeleteView`.
-   **Data Manipulation/Business Logic:** The C# code includes logic for:
    -   Formatting `ReqQty` and `IssueQty` to 3 decimal places.
    -   Handling `DeptId` and `WONo` to display "NA" if certain conditions are met (e.g., DeptId = 1 means "NA" for Dept, WONo is displayed, otherwise Dept symbol is displayed and WONo is "NA"). This will be encapsulated in model methods.
    -   Converting `SysDate` to `DD/MM/YYYY` format (`fun.FromDateDMY`).
    -   Concatenating `Title` and `EmployeeName` for "Gen By".
    -   Retrieving company address (`fun.CompAdd`). These helper functions will be translated to model methods or custom Django template filters/tags.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to determine Django template equivalents.

**Instructions:**
-   **`CrystalReportViewer`**: This component renders a pre-designed report. In Django, for web display, this will be replaced by an HTML table, enhanced with **DataTables** for interactivity (searching, sorting, pagination). For actual "printing," a dedicated "Print" button or a "Generate PDF" link can trigger a backend PDF generation service (e.g., using WeasyPrint or ReportLab), which is beyond the scope of a direct HTML conversion but represents the full modernization.
-   **`asp:Panel`**: A simple container. This translates to an HTML `div`.
-   **`asp:Button` (`BtnCancel`)**: A navigation button. This will be an HTML `button` or `<a>` tag in Django, using `reverse_lazy` for URL redirection. For HTMX, it might trigger a modal close or a `hx-redirect`.
-   **Commented `asp:GridView`**: This explicitly confirms the need for a tabular data display. The DataTables approach is a direct, modernized replacement for this.

---

### Step 4: Generate Django Code

We will create a Django application named `inventory` to house these components.

#### 4.1 Models (`inventory/models.py`)

**Task:** Create Django models based on the identified database schema. We'll define `MaterialIssueNoteMaster` as the primary model and `MaterialIssueNoteDetail` as its related detail, along with necessary lookup models, using `managed = False` for existing tables. We'll include methods for the business logic found in the C# code.

```python
from django.db import models

# Assuming an existing 'Company' model handles CompId
# and 'User' model handles SessionId (Employee) authentication
# For simplicity, we define basic lookup models here

class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Add other company fields if necessary, e.g., Name, Address
    name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany' # Assuming a company table exists
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

    @classmethod
    def get_company_address(cls, comp_id):
        """Retrieves company address based on CompId."""
        try:
            return cls.objects.get(id=comp_id).address
        except cls.DoesNotExist:
            return "Company Address Not Found"

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name or ''}".strip()

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or f"Business Group {self.id}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True) # Assuming ItemMaster is company specific

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.manf_desc or f"Item {self.id}"

class MaterialRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Add other fields like MRSNo, SysDate if needed
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Master'
        verbose_name_plural = 'Material Requisition Masters'

    def __str__(self):
        return f"MRS Master {self.id}"

class MaterialRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialRequisitionMaster, models.DO_NOTHING, db_column='MId')
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    req_qty = models.FloatField(db_column='ReqQty', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=1000, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

    def __str__(self):
        return f"MRS Detail {self.id} for {self.item}"

class MaterialIssueNoteMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    min_no = models.CharField(db_column='MINNo', max_length=255, blank=True, null=True)
    mrs_master = models.ForeignKey(MaterialRequisitionMaster, models.DO_NOTHING, db_column='MRSId', related_name='issue_notes_master', blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    generated_by = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='SessionId', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Note'
        verbose_name_plural = 'Material Issue Notes'

    def __str__(self):
        return self.min_no or f"MIN {self.id}"

    def get_min_date_formatted(self):
        """Returns the MIN date in DD/MM/YYYY format."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

    def get_generated_by_name(self):
        """Returns the formatted name of the person who generated the MIN."""
        return str(self.generated_by) if self.generated_by else ''

    def get_company_address(self):
        """Returns the address of the associated company."""
        return self.company.address if self.company else 'N/A'

class MaterialIssueNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialIssueNoteMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    mrs_detail = models.ForeignKey(MaterialRequisitionDetail, models.DO_NOTHING, db_column='MRSId', related_name='issue_note_details')
    issue_qty = models.FloatField(db_column='IssueQty', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Details'
        verbose_name = 'Material Issue Note Detail'
        verbose_name_plural = 'Material Issue Note Details'

    def __str__(self):
        return f"MIN Detail {self.id} (Issue Qty: {self.issue_qty or 0})"

    def get_display_data(self):
        """
        Aggregates and formats all relevant data for display, mirroring the Crystal Report logic.
        This method replaces the complex data construction logic in the C# code-behind.
        """
        data = {
            'id': self.id,
            'item_code': '',
            'description': '',
            'uom': '',
            'dept': '',
            'wo_no': '',
            'req_qty': None,
            'issue_qty': None,
            'remarks': '',
            'comp_id': self.master.company.id if self.master and self.master.company else None,
        }

        if self.mrs_detail:
            data['req_qty'] = f"{self.mrs_detail.req_qty:.3f}" if self.mrs_detail.req_qty is not None else ''
            data['remarks'] = self.mrs_detail.remarks or ''

            if self.mrs_detail.item:
                data['item_code'] = self.mrs_detail.item.item_code or ItemMaster.objects.get_item_code_part_no(self.master.company.id, self.mrs_detail.item.id) # Assuming this method exists
                data['description'] = self.mrs_detail.item.manf_desc or ''
                if self.mrs_detail.item.uom_basic:
                    data['uom'] = self.mrs_detail.item.uom_basic.symbol or ''

            if self.mrs_detail.dept and self.mrs_detail.dept.id == 1: # Assuming '1' means 'NA' for department
                data['dept'] = 'NA'
                data['wo_no'] = self.mrs_detail.wo_no or ''
            elif self.mrs_detail.dept:
                data['dept'] = self.mrs_detail.dept.symbol or ''
                data['wo_no'] = 'NA'
            else: # If dept is null, then use WO No if available, otherwise NA
                data['wo_no'] = self.mrs_detail.wo_no or ''
                data['dept'] = 'NA'

        data['issue_qty'] = f"{self.issue_qty:.3f}" if self.issue_qty is not None else ''

        return data

# Placeholder for custom item code retrieval if needed from `fun.GetItemCode_PartNo`
# In a real scenario, this would be either a field on ItemMaster or a manager method
# For now, we'll assume item_code is directly available on ItemMaster
ItemMaster.add_to_class('get_item_code_part_no', lambda cls, comp_id, item_id: ItemMaster.objects.get(id=item_id, comp_id=comp_id).item_code)

```

#### 4.2 Forms (`inventory/forms.py`)

**Task:** Define a Django form for `MaterialIssueNoteMaster`. Although the original page is print-only, this form demonstrates how add/edit functionality would be implemented.

```python
from django import forms
from .models import MaterialIssueNoteMaster

class MaterialIssueNoteMasterForm(forms.ModelForm):
    class Meta:
        model = MaterialIssueNoteMaster
        fields = ['min_no', 'mrs_master', 'sys_date', 'generated_by', 'company']
        widgets = {
            'min_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mrs_master': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'generated_by': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    def clean_min_no(self):
        min_no = self.cleaned_data.get('min_no')
        if not min_no:
            raise forms.ValidationError("Material Issue Note number is required.")
        return min_no

```

#### 4.3 Views (`inventory/views.py`)

**Task:** Implement Django Class-Based Views for listing, creating, updating, deleting, and displaying the details of Material Issue Notes. A dedicated detail view will represent the "print details" functionality.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialIssueNoteMaster, MaterialIssueNoteDetail
from .forms import MaterialIssueNoteMasterForm
from django.shortcuts import get_object_or_404
from django.db.models import Prefetch

class MaterialIssueNoteListView(ListView):
    model = MaterialIssueNoteMaster
    template_name = 'inventory/materialissuenote/list.html'
    context_object_name = 'material_issue_notes'

    # Ensure related data is prefetched for efficiency in the list view if needed
    def get_queryset(self):
        return super().get_queryset().select_related('generated_by', 'company')

class MaterialIssueNoteTablePartialView(ListView):
    model = MaterialIssueNoteMaster
    template_name = 'inventory/materialissuenote/_materialissuenote_table.html'
    context_object_name = 'material_issue_notes'

    def get_queryset(self):
        # Prefetch details and related requisition details for efficient display
        return super().get_queryset().select_related(
            'generated_by', 'company', 'mrs_master'
        ).prefetch_related(
            Prefetch('details', queryset=MaterialIssueNoteDetail.objects.select_related(
                'mrs_detail__item__uom_basic',
                'mrs_detail__dept'
            ))
        )

class MaterialIssueNoteDetailView(DetailView):
    model = MaterialIssueNoteMaster
    template_name = 'inventory/materialissuenote/detail.html'
    context_object_name = 'material_issue_note'

    def get_queryset(self):
        # Optimized query to fetch all necessary related data for the report
        return super().get_queryset().select_related(
            'generated_by', 'company', 'mrs_master'
        ).prefetch_related(
            Prefetch('details', queryset=MaterialIssueNoteDetail.objects.select_related(
                'mrs_detail__master', # MRS master might be useful for MIN master report data
                'mrs_detail__item__uom_basic', # Item UOM
                'mrs_detail__dept' # Department
            ))
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Prepare aggregated data for display, mirroring the Crystal Report's line items
        min_details_data = []
        for detail in self.object.details.all():
            min_details_data.append(detail.get_display_data())
        context['min_details_data'] = min_details_data
        # Context for report header-like info
        context['min_no'] = self.object.min_no
        context['mrs_no'] = self.object.mrs_master.id if self.object.mrs_master else 'N/A' # MRSNo from master
        context['min_date'] = self.object.get_min_date_formatted()
        context['generated_by'] = self.object.get_generated_by_name()
        context['company_address'] = self.object.get_company_address()
        return context


class MaterialIssueNoteCreateView(CreateView):
    model = MaterialIssueNoteMaster
    form_class = MaterialIssueNoteMasterForm
    template_name = 'inventory/materialissuenote/form.html'
    success_url = reverse_lazy('materialissuenote_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Issue Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response

class MaterialIssueNoteUpdateView(UpdateView):
    model = MaterialIssueNoteMaster
    form_class = MaterialIssueNoteMasterForm
    template_name = 'inventory/materialissuenote/form.html'
    success_url = reverse_lazy('materialissuenote_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Issue Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response

class MaterialIssueNoteDeleteView(DeleteView):
    model = MaterialIssueNoteMaster
    template_name = 'inventory/materialissuenote/confirm_delete.html'
    success_url = reverse_lazy('materialissuenote_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Issue Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create HTML templates for each view, integrating HTMX, Alpine.js, and DataTables.

**List Template (`inventory/materialissuenote/list.html`):**
This template provides the main entry point to view all Material Issue Notes and trigger CRUD operations.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Issue Notes</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'materialissuenote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New MIN
        </button>
    </div>

    <div id="materialissuenoteTable-container"
         hx-trigger="load, refreshMaterialIssueNoteList from:body"
         hx-get="{% url 'materialissuenote_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Material Issue Notes...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management
    });
</script>
{% endblock %}
```

**Table Partial Template (`inventory/materialissuenote/_materialissuenote_table.html`):**
This partial template is loaded dynamically by HTMX to display the DataTables table.

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="materialissuenoteTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="py-3 px-6 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MIN No.</th>
                <th class="py-3 px-6 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No.</th>
                <th class="py-3 px-6 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                <th class="py-3 px-6 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for min_obj in material_issue_notes %}
            <tr class="hover:bg-gray-100">
                <td class="py-3 px-6 border-b border-gray-200 text-sm">{{ forloop.counter }}</td>
                <td class="py-3 px-6 border-b border-gray-200 text-sm">{{ min_obj.min_no }}</td>
                <td class="py-3 px-6 border-b border-gray-200 text-sm">{{ min_obj.mrs_master.id|default:'N/A' }}</td> {# Assuming MRSId from master is shown #}
                <td class="py-3 px-6 border-b border-gray-200 text-sm">{{ min_obj.get_min_date_formatted }}</td>
                <td class="py-3 px-6 border-b border-gray-200 text-sm">{{ min_obj.get_generated_by_name }}</td>
                <td class="py-3 px-6 border-b border-gray-200 text-sm flex justify-center space-x-2">
                    <a
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                        href="{% url 'materialissuenote_detail' min_obj.pk %}">
                        View Details
                    </a>
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'materialissuenote_edit' min_obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'materialissuenote_delete' min_obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#materialissuenoteTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**Detail Template (`inventory/materialissuenote/detail.html`):**
This template displays the comprehensive details for a single Material Issue Note, mirroring the Crystal Report's data.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Issue Note Details - {{ material_issue_note.min_no }}</h2>
        <a href="{% url 'materialissuenote_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Back to List
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold mb-4 border-b pb-2">MIN Header Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div><strong class="text-gray-700">MIN No.:</strong> {{ min_no }}</div>
            <div><strong class="text-gray-700">MRS No.:</strong> {{ mrs_no }}</div>
            <div><strong class="text-gray-700">MIN Date:</strong> {{ min_date }}</div>
            <div><strong class="text-gray-700">Generated By:</strong> {{ generated_by }}</div>
            <div class="md:col-span-2 lg:col-span-3"><strong class="text-gray-700">Company Address:</strong> {{ company_address }}</div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 border-b pb-2">MIN Line Items</h3>
        <div class="overflow-x-auto">
            <table id="minDetailsTable" class="min-w-full leading-normal">
                <thead>
                    <tr>
                        <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                        <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                        <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept</th>
                        <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                        <th class="py-3 px-4 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                        <th class="py-3 px-4 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Qty</th>
                        <th class="py-3 px-4 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item_data in min_details_data %}
                    <tr class="hover:bg-gray-100">
                        <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ item_data.item_code }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ item_data.description }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ item_data.uom }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ item_data.dept }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ item_data.wo_no }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm text-right">{{ item_data.req_qty }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm text-right">{{ item_data.issue_qty }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ item_data.remarks }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="py-4 px-4 text-center text-sm text-gray-500">No details available for this Material Issue Note.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="mt-6 flex justify-end">
             <button onclick="window.print()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded print:hidden">
                Print Report
            </button>
            {# Optionally, add a button here to generate a PDF report via an HTMX call if PDF generation is implemented #}
            {# <button hx-post="{% url 'generate_min_pdf' material_issue_note.pk %}" hx-swap="none" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded ml-2">Generate PDF</button> #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#minDetailsTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true, // Enable search for details table
            "responsive": true
        });
    });
</script>
{% endblock %}
```

**Form Partial Template (`inventory/materialissuenote/_materialissuenote_form.html`):**
Used for both Add and Edit operations, loaded dynamically into a modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Issue Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**Confirm Delete Partial Template (`inventory/materialissuenote/_materialissuenote_confirm_delete.html`):**
Used for delete confirmation, loaded dynamically into a modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4 text-gray-700">Are you sure you want to delete Material Issue Note "{{ object.min_no }}"?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

**Task:** Define URL patterns for all views within the `inventory` app.

```python
from django.urls import path
from .views import (
    MaterialIssueNoteListView,
    MaterialIssueNoteTablePartialView,
    MaterialIssueNoteDetailView,
    MaterialIssueNoteCreateView,
    MaterialIssueNoteUpdateView,
    MaterialIssueNoteDeleteView
)

urlpatterns = [
    path('materialissuenotes/', MaterialIssueNoteListView.as_view(), name='materialissuenote_list'),
    path('materialissuenotes/table/', MaterialIssueNoteTablePartialView.as_view(), name='materialissuenote_table'),
    path('materialissuenotes/<int:pk>/', MaterialIssueNoteDetailView.as_view(), name='materialissuenote_detail'),
    path('materialissuenotes/add/', MaterialIssueNoteCreateView.as_view(), name='materialissuenote_add'),
    path('materialissuenotes/edit/<int:pk>/', MaterialIssueNoteUpdateView.as_view(), name='materialissuenote_edit'),
    path('materialissuenotes/delete/<int:pk>/', MaterialIssueNoteDeleteView.as_view(), name='materialissuenote_delete'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views, ensuring high test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from .models import (
    Company, OfficeStaff, UnitMaster, BusinessGroup, ItemMaster,
    MaterialRequisitionMaster, MaterialRequisitionDetail,
    MaterialIssueNoteMaster, MaterialIssueNoteDetail
)

class MaterialIssueNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St')
        cls.staff = OfficeStaff.objects.create(emp_id=101, title='Mr', employee_name='John Doe')
        cls.unit = UnitMaster.objects.create(id=1, symbol='Pcs')
        cls.dept_na = BusinessGroup.objects.create(id=1, symbol='NA_Dept') # DeptId=1 means 'NA' for department
        cls.dept_prod = BusinessGroup.objects.create(id=2, symbol='Production')
        cls.item = ItemMaster.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Description', uom_basic=cls.unit, comp_id=cls.company)
        cls.mrs_master = MaterialRequisitionMaster.objects.create(id=1, comp_id=cls.company)
        cls.mrs_detail_wo = MaterialRequisitionDetail.objects.create(
            id=1, master=cls.mrs_master, item=cls.item, dept=cls.dept_na, wo_no='WO123', req_qty=100.500, remarks='Test WO Remarks'
        )
        cls.mrs_detail_dept = MaterialRequisitionDetail.objects.create(
            id=2, master=cls.mrs_master, item=cls.item, dept=cls.dept_prod, wo_no=None, req_qty=50.250, remarks='Test Dept Remarks'
        )

        cls.min_master = MaterialIssueNoteMaster.objects.create(
            id=1,
            min_no='MIN/2023/001',
            mrs_master=cls.mrs_master,
            sys_date=datetime(2023, 10, 26, 10, 0, 0),
            generated_by=cls.staff,
            company=cls.company
        )
        MaterialIssueNoteDetail.objects.create(
            id=1, master=cls.min_master, mrs_detail=cls.mrs_detail_wo, issue_qty=90.750
        )
        MaterialIssueNoteDetail.objects.create(
            id=2, master=cls.min_master, mrs_detail=cls.mrs_detail_dept, issue_qty=45.123
        )

    def test_min_master_creation(self):
        min_obj = MaterialIssueNoteMaster.objects.get(id=1)
        self.assertEqual(min_obj.min_no, 'MIN/2023/001')
        self.assertEqual(min_obj.mrs_master.id, self.mrs_master.id)
        self.assertEqual(min_obj.generated_by, self.staff)
        self.assertEqual(min_obj.company, self.company)

    def test_min_detail_creation(self):
        min_detail_obj = MaterialIssueNoteDetail.objects.get(id=1)
        self.assertEqual(min_detail_obj.master, self.min_master)
        self.assertEqual(min_detail_obj.mrs_detail, self.mrs_detail_wo)
        self.assertEqual(min_detail_obj.issue_qty, 90.750)

    def test_get_min_date_formatted(self):
        min_obj = MaterialIssueNoteMaster.objects.get(id=1)
        self.assertEqual(min_obj.get_min_date_formatted(), '26/10/2023')

    def test_get_generated_by_name(self):
        min_obj = MaterialIssueNoteMaster.objects.get(id=1)
        self.assertEqual(min_obj.get_generated_by_name(), 'Mr. John Doe')

    def test_get_company_address(self):
        min_obj = MaterialIssueNoteMaster.objects.get(id=1)
        self.assertEqual(min_obj.get_company_address(), '123 Test St')

    def test_min_detail_get_display_data_wo(self):
        detail = MaterialIssueNoteDetail.objects.get(id=1)
        data = detail.get_display_data()
        self.assertEqual(data['item_code'], 'ITEM001')
        self.assertEqual(data['description'], 'Test Item Description')
        self.assertEqual(data['uom'], 'Pcs')
        self.assertEqual(data['dept'], 'NA')
        self.assertEqual(data['wo_no'], 'WO123')
        self.assertEqual(data['req_qty'], '100.500')
        self.assertEqual(data['issue_qty'], '90.750')
        self.assertEqual(data['remarks'], 'Test WO Remarks')

    def test_min_detail_get_display_data_dept(self):
        detail = MaterialIssueNoteDetail.objects.get(id=2)
        data = detail.get_display_data()
        self.assertEqual(data['item_code'], 'ITEM001')
        self.assertEqual(data['description'], 'Test Item Description')
        self.assertEqual(data['uom'], 'Pcs')
        self.assertEqual(data['dept'], 'Production')
        self.assertEqual(data['wo_no'], 'NA') # Should be 'NA' as DeptId is not 1 and WONo is null
        self.assertEqual(data['req_qty'], '50.250')
        self.assertEqual(data['issue_qty'], '45.123')
        self.assertEqual(data['remarks'], 'Test Dept Remarks')


class MaterialIssueNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all views
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St')
        cls.staff = OfficeStaff.objects.create(emp_id=101, title='Mr', employee_name='John Doe')
        cls.unit = UnitMaster.objects.create(id=1, symbol='Pcs')
        cls.dept_na = BusinessGroup.objects.create(id=1, symbol='NA_Dept')
        cls.item = ItemMaster.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Description', uom_basic=cls.unit, comp_id=cls.company)
        cls.mrs_master = MaterialRequisitionMaster.objects.create(id=1, comp_id=cls.company)
        cls.mrs_detail = MaterialRequisitionDetail.objects.create(
            id=1, master=cls.mrs_master, item=cls.item, dept=cls.dept_na, wo_no='WO123', req_qty=100.500, remarks='Test WO Remarks'
        )

        cls.min1 = MaterialIssueNoteMaster.objects.create(
            id=1, min_no='MIN/2023/001', mrs_master=cls.mrs_master, sys_date=datetime(2023, 1, 1), generated_by=cls.staff, company=cls.company
        )
        MaterialIssueNoteDetail.objects.create(id=1, master=cls.min1, mrs_detail=cls.mrs_detail, issue_qty=90.0)

        cls.min2 = MaterialIssueNoteMaster.objects.create(
            id=2, min_no='MIN/2023/002', mrs_master=cls.mrs_master, sys_date=datetime(2023, 1, 2), generated_by=cls.staff, company=cls.company
        )
        MaterialIssueNoteDetail.objects.create(id=2, master=cls.min2, mrs_detail=cls.mrs_detail, issue_qty=80.0)

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('materialissuenote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/list.html')
        self.assertContains(response, 'Material Issue Notes') # Check for title

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('materialissuenote_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertContains(response, 'MIN/2023/001')
        self.assertContains(response, 'MIN/2023/002')
        self.assertContains(response, 'John Doe')

    def test_detail_view_get(self):
        response = self.client.get(reverse('materialissuenote_detail', args=[self.min1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/detail.html')
        self.assertContains(response, 'MIN/2023/001')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'Test Item Description')
        self.assertContains(response, '90.000') # Check formatted issue quantity
        self.assertContains(response, '100.500') # Check formatted requested quantity

    def test_create_view_get(self):
        response = self.client.get(reverse('materialissuenote_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/form.html')
        self.assertContains(response, 'Add Material Issue Note')

    def test_create_view_post_success(self):
        new_min_data = {
            'min_no': 'MIN/2023/003',
            'mrs_master': self.mrs_master.id,
            'sys_date': '2023-11-01',
            'generated_by': self.staff.emp_id,
            'company': self.company.id,
        }
        response = self.client.post(reverse('materialissuenote_add'), new_min_data)
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertTrue(MaterialIssueNoteMaster.objects.filter(min_no='MIN/2023/003').exists())
        # Test HX-Request behavior
        hx_response = self.client.post(reverse('materialissuenote_add'), new_min_data, HTTP_HX_REQUEST='true')
        self.assertEqual(hx_response.status_code, 204) # No content for HTMX trigger
        self.assertEqual(hx_response.headers.get('HX-Trigger'), 'refreshMaterialIssueNoteList')

    def test_update_view_get(self):
        response = self.client.get(reverse('materialissuenote_edit', args=[self.min1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/form.html')
        self.assertContains(response, 'Edit Material Issue Note')
        self.assertContains(response, 'MIN/2023/001')

    def test_update_view_post_success(self):
        updated_data = {
            'min_no': 'MIN/2023/001-UPDATED',
            'mrs_master': self.mrs_master.id,
            'sys_date': '2023-01-01',
            'generated_by': self.staff.emp_id,
            'company': self.company.id,
        }
        response = self.client.post(reverse('materialissuenote_edit', args=[self.min1.pk]), updated_data)
        self.assertEqual(response.status_code, 302)
        self.min1.refresh_from_db()
        self.assertEqual(self.min1.min_no, 'MIN/2023/001-UPDATED')

        # Test HX-Request behavior
        hx_response = self.client.post(reverse('materialissuenote_edit', args=[self.min1.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(hx_response.status_code, 204)
        self.assertEqual(hx_response.headers.get('HX-Trigger'), 'refreshMaterialIssueNoteList')


    def test_delete_view_get(self):
        response = self.client.get(reverse('materialissuenote_delete', args=[self.min1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, 'Are you sure you want to delete Material Issue Note "MIN/2023/001"?')

    def test_delete_view_post_success(self):
        initial_count = MaterialIssueNoteMaster.objects.count()
        response = self.client.post(reverse('materialissuenote_delete', args=[self.min1.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertEqual(MaterialIssueNoteMaster.objects.count(), initial_count - 1)
        self.assertFalse(MaterialIssueNoteMaster.objects.filter(pk=self.min1.pk).exists())

        # Test HX-Request behavior
        new_min_to_delete = MaterialIssueNoteMaster.objects.create(
            id=3, min_no='MIN/2023/00X', mrs_master=self.mrs_master, sys_date=datetime(2023, 1, 3), generated_by=self.staff, company=self.company
        )
        hx_response = self.client.post(reverse('materialissuenote_delete', args=[new_min_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(hx_response.status_code, 204)
        self.assertEqual(hx_response.headers.get('HX-Trigger'), 'refreshMaterialIssueNoteList')
        self.assertFalse(MaterialIssueNoteMaster.objects.filter(pk=new_min_to_delete.pk).exists())
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for dynamic content:**
    - The `materialissuenote_list.html` uses `hx-get="{% url 'materialissuenote_table' %}"` to load the initial table content and `hx-trigger="load, refreshMaterialIssueNoteList from:body"` to ensure it reloads the table dynamically after any CRUD operation.
    - Add/Edit/Delete buttons use `hx-get` to fetch partial forms (`_materialissuenote_form.html`, `_materialissuenote_confirm_delete.html`) into a modal.
    - Form submissions (`hx-post`) in the partials use `hx-swap="none"` and backend `HX-Trigger` headers (`refreshMaterialIssueNoteList`) to signal the main list view to refresh the table without a full page reload.
- **Alpine.js for UI state management:**
    - The modal (`#modal`) uses Alpine.js (via `_`) to toggle its visibility (`add .is-active to #modal`, `remove .is-active from me`). This handles opening and closing the modal without custom JavaScript.
- **DataTables for list views:**
    - The `_materialissuenote_table.html` and `detail.html` include `<script>` tags to initialize DataTables on the `<table>` elements with client-side searching, sorting, and pagination enabled. The script is placed within the partial to ensure it runs after the content is loaded by HTMX.
- **DRY Templates:**
    - All templates extend `core/base.html` for consistent layout and CDN inclusion (DataTables, HTMX, Alpine.js, Tailwind CSS).
    - Partial templates (`_materialissuenote_table.html`, `_materialissuenote_form.html`, `_materialissuenote_confirm_delete.html`) are used for reusable components that are loaded via HTMX.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the Material Issue Note print functionality to a modern Django application. By leveraging AI-assisted automation, the process can be streamlined, reducing manual effort and potential errors. The focus on fat models, thin views, HTMX, Alpine.js, and DataTables ensures a performant, maintainable, and user-friendly solution that aligns with contemporary web development best practices. Further automation can be applied to generating model fields directly from database schemas, inferring relationships, and even drafting initial test cases, significantly accelerating the modernization lifecycle.