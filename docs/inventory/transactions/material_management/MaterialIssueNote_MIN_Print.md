## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code, particularly the `GridView1` columns and the `loaddata` method using `Sp_MIN_FillGrid` and the `GetCompletionList` method, we can infer the following database schema:

*   **Primary Table:** `tblInv_MaterialIssue_Master`
    *   `Id` (Primary Key, Integer)
    *   `FinYearId` (Integer)
    *   `FinYear` (Varchar/Nvarchar)
    *   `MINNo` (Varchar/Nvarchar)
    *   `SysDate` (DateTime)
    *   `MRSNo` (Varchar/Nvarchar, nullable)
    *   `GenBy` (Varchar/Nvarchar)
    *   `CompId` (Integer, inferred from `Session["compid"]`)
    *   `SessionId` (Integer, inferred from `fun.getCode(TxtEmpName.Text)` and mapping to `EmpId`)

*   **Related Table (for Autocomplete):** `tblHR_OfficeStaff`
    *   `EmpId` (Primary Key, Integer)
    *   `EmployeeName` (Varchar/Nvarchar)
    *   `CompId` (Integer, inferred from `Session["compid"]`)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET page is primarily a "Print" view, focusing on listing and searching existing Material Issue Notes (MINs), then redirecting to a detail page.

*   **Create:** Not present on this page.
*   **Read:**
    *   Displays a list of Material Issue Notes from `tblInv_MaterialIssue_Master` via the `Sp_MIN_FillGrid` stored procedure.
    *   Filters the list based on user input for "MRS No", "MIN No", or "Employee Name".
    *   Performs pagination (PageSize=20).
    *   Includes an autocomplete feature for "Employee Name" using data from `tblHR_OfficeStaff`.
*   **Update:** Not present on this page.
*   **Delete:** Not present on this page.
*   **Navigation:** Upon clicking "Select" for a Material Issue Note, it redirects to a `MaterialIssueNote_MIN_Print_Details.aspx` page, passing key identifiers.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **Search/Filter Controls:**
    *   `DrpField` (DropDownList): Allows selection of search criteria ("MRS No", "MIN No", "Employee Name"). Triggers an automatic postback (`AutoPostBack="True"`).
    *   `TxtMrs` (TextBox): Input for "MRS No" or "MIN No". Visibility changes based on `DrpField` selection.
    *   `TxtEmpName` (TextBox): Input for "Employee Name". Visibility changes based on `DrpField` selection.
    *   `TxtEmpName_AutoCompleteExtender`: Provides autocomplete suggestions for `TxtEmpName` using `GetCompletionList` web method.
    *   `Button1` (Button): "Search" button to initiate data filtering.
*   **Data Display:**
    *   `GridView1` (GridView): Displays the list of Material Issue Notes with columns: "SN", "Select", "Id" (hidden), "FinYear Id" (hidden), "FinYear", "MIN No", "Date" (`SysDate`), "MRS No", "Gen. By".
*   **Action Controls:**
    *   "Select" (LinkButton within `GridView1`): Triggers a redirection to a detail page.

## Step 4: Generate Django Code

We will create a Django application named `inventory` to house these components.

### 4.1 Models

We will define two models: `MaterialIssueNote` for the main data and `Employee` for the autocomplete functionality. Both will use `managed = False` and `db_table` to map to existing database tables. We also add custom managers for encapsulating business logic.

**`inventory/models.py`**

```python
from django.db import models

class MaterialIssueNoteManager(models.Manager):
    """
    Custom manager for MaterialIssueNote to encapsulate filtering logic
    similar to the Sp_MIN_FillGrid stored procedure and loaddata method.
    """
    def filter_by_criteria(self, company_id, financial_year_id, search_field, search_value):
        qs = self.get_queryset().filter(company_id=company_id, fin_year_id=financial_year_id)

        if search_field == 'MRSNo' and search_value:
            qs = qs.filter(mrs_no__iexact=search_value) # Case-insensitive exact match
        elif search_field == 'MINNo' and search_value:
            qs = qs.filter(min_no__iexact=search_value) # Case-insensitive exact match
        elif search_field == 'EmployeeName' and search_value:
            # The original ASP.NET extracted EmpId from "Name [EmpId]" using fun.getCode().
            # We will attempt to parse the EmpId from the search_value if present,
            # or look up the employee by name if it's just the name.
            # A more robust solution might pass the EmpId as a hidden field from autocomplete.
            
            emp_id = None
            if '[' in search_value and ']' in search_value:
                try:
                    emp_id_str = search_value.split('[')[-1][:-1]
                    emp_id = int(emp_id_str)
                except (ValueError, IndexError):
                    pass # Couldn't parse ID, might just be the name
            
            if emp_id:
                qs = qs.filter(session_id=emp_id)
            else: # If no ID, try to find employee by name
                try:
                    employee = Employee.objects.get(employee_name__iexact=search_value, company_id=company_id)
                    qs = qs.filter(session_id=employee.id)
                except Employee.DoesNotExist:
                    qs = qs.none() # No matching employee found

        return qs

class MaterialIssueNote(models.Model):
    """
    Maps to tblInv_MaterialIssue_Master table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    fin_year = models.CharField(db_column='FinYear', max_length=50) # Inferred max_length
    min_no = models.CharField(db_column='MINNo', max_length=50) # Inferred max_length
    sys_date = models.DateTimeField(db_column='SysDate')
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    gen_by = models.CharField(db_column='GenBy', max_length=100) # Inferred max_length
    company_id = models.IntegerField(db_column='CompId')
    session_id = models.IntegerField(db_column='SessionId') # This stores EmpId from tblHR_OfficeStaff

    objects = MaterialIssueNoteManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Note'
        verbose_name_plural = 'Material Issue Notes'

    def __str__(self):
        return self.min_no

    def get_employee_display_name(self):
        """
        Retrieves the employee name from tblHR_OfficeStaff based on SessionId.
        """
        try:
            employee = Employee.objects.get(id=self.session_id, company_id=self.company_id)
            return employee.employee_name
        except Employee.DoesNotExist:
            return "N/A"

class EmployeeManager(models.Manager):
    """
    Custom manager for Employee to handle autocomplete queries.
    """
    def get_completion_list(self, prefix_text, company_id, count=10):
        """
        Fetches employee names for autocomplete, similar to GetCompletionList web method.
        """
        return self.get_queryset().filter(
            company_id=company_id,
            employee_name__icontains=prefix_text
        ).order_by('employee_name')[:count]

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff table, used for employee lookup/autocomplete.
    """
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255) # Inferred max_length
    company_id = models.IntegerField(db_column='CompId')

    objects = EmployeeManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.employee_name} [{self.id}]" # Mimic ASP.NET's "Name [Id]" format for autocomplete
```

### 4.2 Forms

We'll define a custom form for the search/filter functionality, as this page does not perform direct CRUD operations on `MaterialIssueNote` objects. However, per the template instructions, a generic `MaterialIssueNoteForm` is also provided for potential future CRUD views.

**`inventory/forms.py`**

```python
from django import forms
from .models import MaterialIssueNote

class MaterialIssueNoteSearchForm(forms.Form):
    """
    Form for filtering Material Issue Notes based on selected criteria.
    """
    SEARCH_FIELD_CHOICES = [
        ('Select', 'Select'), # Original ASP.NET had "Select", "0", "1", "2"
        ('MRSNo', 'MRS No'),
        ('MINNo', 'MIN No'),
        ('EmployeeName', 'Employee Name'),
    ]

    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': "{% url 'materialissuenote_table' %}", # HTMX: re-render table on selection change
            'hx-target': '#materialissuenote-list-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
            'name': 'search_field' # Essential for POST/GET data
        })
    )
    search_value_mrs_min = forms.CharField(
        max_length=150,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter MRS/MIN No',
            'name': 'search_value_mrs_min'
        })
    )
    search_value_employee = forms.CharField(
        max_length=250,
        required=False,
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'list': 'employee-suggestions', # For <datalist> integration
            'autocomplete': 'off', # Prevent browser autocomplete
            'hx-get': "{% url 'employee_autocomplete' %}", # HTMX: fetch suggestions on input
            'hx-trigger': 'keyup changed delay:300ms, search',
            'hx-target': '#employee-suggestions-datalist', # Target for the datalist HTML
            'hx-swap': 'outerHTML', # Swap the entire datalist
            'name': 'search_value_employee'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Handle initial visibility of search value fields based on selected_field
        selected_field = self.data.get('search_field') if self.is_bound else self.initial.get('search_field', 'Select')

        if selected_field == 'EmployeeName':
            self.fields['search_value_mrs_min'].widget.attrs['class'] += ' hidden'
        else:
            self.fields['search_value_employee'].widget.attrs['class'] += ' hidden'

# Generic MaterialIssueNoteForm (for Create/Update views if they were to be implemented later)
class MaterialIssueNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialIssueNote
        fields = ['min_no', 'mrs_no', 'sys_date', 'gen_by', 'fin_year']
        widgets = {
            'min_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mrs_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'gen_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

### 4.3 Views

We'll create views for listing, searching, table partial rendering, and autocomplete. We'll also provide placeholder CRUD views as per the template.

**`inventory/views.py`**

```python
from django.views.generic import ListView, TemplateView, View, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from .models import MaterialIssueNote, Employee
from .forms import MaterialIssueNoteSearchForm, MaterialIssueNoteForm # Import both forms

class MaterialIssueNoteListView(TemplateView):
    """
    Renders the main Material Issue Note list page with the search form.
    The actual table content is loaded via HTMX.
    """
    template_name = 'inventory/materialissuenote/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with any existing GET parameters
        context['form'] = MaterialIssueNoteSearchForm(self.request.GET or None)
        return context

class MaterialIssueNoteTablePartialView(ListView):
    """
    Provides the HTML partial for the Material Issue Note table,
    designed to be loaded via HTMX for filtering and pagination.
    """
    model = MaterialIssueNote
    template_name = 'inventory/materialissuenote/_materialissuenote_table.html'
    context_object_name = 'materialissuenotes'
    paginate_by = 20 # Matches PageSize="20" from GridView

    def get_queryset(self):
        # Simulate session variables (replace with actual session access in a real app)
        # These would typically come from user authentication or a global context manager
        company_id = self.request.session.get('compid', 1)
        financial_year_id = self.request.session.get('finyear', 1)

        form = MaterialIssueNoteSearchForm(self.request.GET)
        search_field = self.request.GET.get('search_field')
        search_value = ''

        if search_field == 'MRSNo' or search_field == 'MINNo':
            search_value = self.request.GET.get('search_value_mrs_min', '').strip()
        elif search_field == 'EmployeeName':
            search_value = self.request.GET.get('search_value_employee', '').strip()

        # If 'Select' is chosen or no field is selected, return all relevant records
        if search_field == 'Select' or not search_field:
             return MaterialIssueNote.objects.filter(company_id=company_id, fin_year_id=financial_year_id)

        # Use the custom manager for filtering, keeping views thin
        return MaterialIssueNote.objects.filter_by_criteria(
            company_id=company_id,
            financial_year_id=financial_year_id,
            search_field=search_field,
            search_value=search_value
        )
    
    def get_context_data(self, **kwargs):
        # Passes the search form back to the template for initial rendering if needed
        context = super().get_context_data(**kwargs)
        context['search_form'] = MaterialIssueNoteSearchForm(self.request.GET or None)
        return context

class MaterialIssueNoteDetailView(View):
    """
    Simulates the redirection to MaterialIssueNote_MIN_Print_Details.aspx.
    In a full Django app, this would render a dedicated detail template.
    """
    def get(self, request, pk):
        try:
            min_obj = MaterialIssueNote.objects.get(id=pk)
            messages.info(request, f"Selected MIN: ID={min_obj.id}, MIN No={min_obj.min_no}, MRS No={min_obj.mrs_no}, FY ID={min_obj.fin_year_id}")
            # In a real app, you would render a detail page for min_obj
            return render(request, 'inventory/materialissuenote/detail.html', {'min_obj': min_obj})
        except MaterialIssueNote.DoesNotExist:
            messages.error(request, "Material Issue Note not found.")
            return redirect(reverse_lazy('materialissuenote_list')) # Redirect back to list
        except Exception as e:
            messages.error(request, f"An error occurred: {e}")
            return redirect(reverse_lazy('materialissuenote_list'))


class EmployeeAutocompleteView(View):
    """
    Provides employee suggestions for autocomplete, returning HTML for a <datalist>.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        company_id = request.session.get('compid', 1) # Simulate session company ID
        
        suggestions = Employee.objects.get_completion_list(prefix_text, company_id)
        
        # Build the HTML for the <datalist>
        response_html = '<datalist id="employee-suggestions-datalist">'
        for suggestion in suggestions:
            response_html += f'<option value="{str(suggestion)}"></option>' # Uses Employee.__str__ for "Name [Id]" format
        response_html += '</datalist>'
        
        return HttpResponse(response_html)

# --- Generic CRUD Views (as per template, not explicitly from source ASP.NET page) ---
# These are included to meet the template's requirements for full CRUD views.
# They assume that a MaterialIssueNoteForm (ModelForm) is defined in forms.py.

class MaterialIssueNoteCreateView(CreateView):
    model = MaterialIssueNote
    form_class = MaterialIssueNoteForm
    template_name = 'inventory/materialissuenote/form.html'
    success_url = reverse_lazy('materialissuenote_list')

    def form_valid(self, form):
        # Set session-dependent fields before saving
        form.instance.company_id = self.request.session.get('compid', 1)
        form.instance.fin_year_id = self.request.session.get('finyear', 1)
        # Note: SessionId (employee ID) or other fields might need to be set here
        # if they are not part of the form's fields.
        response = super().form_valid(form)
        messages.success(self.request, 'Material Issue Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response

class MaterialIssueNoteUpdateView(UpdateView):
    model = MaterialIssueNote
    form_class = MaterialIssueNoteForm
    template_name = 'inventory/materialissuenote/form.html'
    success_url = reverse_lazy('materialissuenote_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Issue Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response

class MaterialIssueNoteDeleteView(DeleteView):
    model = MaterialIssueNote
    template_name = 'inventory/materialissuenote/confirm_delete.html'
    success_url = reverse_lazy('materialissuenote_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Issue Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response
```

### 4.4 Templates

We'll define the main list template and partials for the table, search form, and the generic CRUD modals.

**`inventory/materialissuenote/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Material Issue Note [MIN] - Print</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialissuenote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then remove .hidden from #modal">
            Add New Material Issue Note
        </button>
    </div>

    <!-- Search Form -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="search-form" hx-get="{% url 'materialissuenote_table' %}" hx-target="#materialissuenote-list-container" hx-swap="innerHTML" hx-indicator=".htmx-indicator">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="{{ form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.search_field.label }}</label>
                    {{ form.search_field }}
                </div>
                <div>
                    <!-- Conditional rendering based on search_field selection -->
                    <div x-data="{ selectedField: '{{ form.search_field.value|default:'Select' }}' }"
                         x-init="selectedField = document.getElementById('{{ form.search_field.id_for_label }}').value;"
                         @change="selectedField = document.getElementById('{{ form.search_field.id_for_label }}').value;"
                         class="flex space-x-2 items-end">
                        <label for="id_search_value" class="block text-sm font-medium text-gray-700 sr-only">Search Value</label>
                        <div x-show="selectedField === 'MRSNo' || selectedField === 'MINNo'" class="w-full">
                            {{ form.search_value_mrs_min }}
                        </div>
                        <div x-show="selectedField === 'EmployeeName'" class="w-full">
                            {{ form.search_value_employee }}
                            <div id="employee-suggestions-datalist"></div> {# HTMX target for autocomplete suggestions #}
                        </div>
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                            Search
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading Indicator for HTMX -->
    <div class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading data...</p>
    </div>

    <!-- Material Issue Note List Table Container -->
    <div id="materialissuenote-list-container"
         hx-trigger="load, refreshMaterialIssueNoteList from:body"
         hx-get="{% url 'materialissuenote_table' %}"
         hx-swap="innerHTML">
        <!-- Table content will be loaded here via HTMX -->
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .flex then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full"
             _="on htmx:afterSwap if event.detail.xhr.status == 204 remove .flex then add .hidden to #modal">
            <!-- Form or confirmation content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('searchLogic', () => ({
            selectedField: '{{ form.search_field.value|default:'Select' }}',
            init() {
                // Initialize selectedField from the actual DOM element
                this.selectedField = document.getElementById('{{ form.search_field.id_for_label }}').value;

                // Watch for changes in the dropdown
                this.$watch('selectedField', value => {
                    // This handles showing/hiding the correct input field based on dropdown
                    const mrsMinInput = document.querySelector('[name="search_value_mrs_min"]');
                    const employeeInput = document.querySelector('[name="search_value_employee"]');

                    if (mrsMinInput && employeeInput) {
                        if (value === 'EmployeeName') {
                            mrsMinInput.classList.add('hidden');
                            employeeInput.classList.remove('hidden');
                            employeeInput.value = ''; // Clear other field
                        } else {
                            mrsMinInput.classList.remove('hidden');
                            employeeInput.classList.add('hidden');
                            mrsMinInput.value = ''; // Clear other field
                        }
                    }
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`inventory/materialissuenote/_materialissuenote_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="materialissuenoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MIN No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in materialissuenotes %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter|add:page_obj.start_index|add:-1 }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.min_no }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.sys_date|date:"d M Y" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.mrs_no|default:"-" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.get_employee_display_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.fin_year }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'materialissuenote_detail' obj.pk %}" class="text-blue-600 hover:text-blue-900 mr-3">Select</a>
                    <button
                        class="text-yellow-600 hover:text-yellow-900 mr-3"
                        hx-get="{% url 'materialissuenote_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'materialissuenote_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-3 px-6 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Pagination Controls -->
    {% if is_paginated %}
    <nav class="mt-4 flex justify-between items-center" aria-label="Pagination">
        <div>
            {% if page_obj.has_previous %}
            <button
                class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded-l"
                hx-get="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                hx-target="#materialissuenote-list-container" hx-swap="innerHTML" hx-indicator=".htmx-indicator">
                Previous
            </button>
            {% endif %}
            {% if page_obj.has_next %}
            <button
                class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded-r"
                hx-get="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                hx-target="#materialissuenote-list-container" hx-swap="innerHTML" hx-indicator=".htmx-indicator">
                Next
            </button>
            {% endif %}
        </div>
        <span class="text-sm text-gray-700">
            Page {{ page_obj.number }} of {{ page_obj.num_pages }}.
        </span>
    </nav>
    {% endif %}
</div>

<script>
    // DataTables initialization (will re-initialize every time HTMX swaps this content)
    $(document).ready(function() {
        $('#materialissuenoteTable').DataTable({
            "paging": false, // Disable DataTables internal paging as we handle it via Django/HTMX
            "info": false,   // Disable info text like "Showing X of Y entries"
            "searching": false, // Disable DataTables internal search as we handle it via Django/HTMX
            "lengthChange": false, // Disable "Show X entries" dropdown
            "columnDefs": [
                { "orderable": false, "targets": [0, 6] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>
```

**`inventory/materialissuenote/form.html`** (Generic CRUD form partial)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Issue Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="json-enc" hx-on::after-request="if(event.detail.successful) this.closest('#modal')._clickedOutside()">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`inventory/materialissuenote/confirm_delete.html`** (Generic delete confirmation partial)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Material Issue Note "{{ object }}"?</p>
    
    <div class="flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
            _="on click remove .flex then add .hidden to #modal">
            Cancel
        </button>
        <button 
            hx-delete="{% url 'materialissuenote_delete' object.pk %}"
            hx-swap="none"
            hx-on::after-request="if(event.detail.successful) this.closest('#modal')._clickedOutside()"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
            Delete
        </button>
    </div>
</div>
```

**`inventory/materialissuenote/detail.html`** (Simple detail view for "Select" action)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Material Issue Note Details</h2>

    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            <div>
                <p class="font-semibold">MIN No:</p>
                <p>{{ min_obj.min_no }}</p>
            </div>
            <div>
                <p class="font-semibold">Date:</p>
                <p>{{ min_obj.sys_date|date:"d M Y h:i A" }}</p>
            </div>
            <div>
                <p class="font-semibold">MRS No:</p>
                <p>{{ min_obj.mrs_no|default:"N/A" }}</p>
            </div>
            <div>
                <p class="font-semibold">Generated By:</p>
                <p>{{ min_obj.get_employee_display_name }}</p>
            </div>
            <div>
                <p class="font-semibold">Financial Year:</p>
                <p>{{ min_obj.fin_year }} (ID: {{ min_obj.fin_year_id }})</p>
            </div>
            <div>
                <p class="font-semibold">Company ID:</p>
                <p>{{ min_obj.company_id }}</p>
            </div>
            <div>
                <p class="font-semibold">Session ID (Employee ID):</p>
                <p>{{ min_obj.session_id }}</p>
            </div>
        </div>
        <div class="mt-6 text-right">
            <a href="{% url 'materialissuenote_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Back to List
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

### 4.5 URLs

We'll define URL patterns for all our views, including those specifically for HTMX partials.

**`inventory/urls.py`**

```python
from django.urls import path
from .views import (
    MaterialIssueNoteListView, MaterialIssueNoteTablePartialView,
    MaterialIssueNoteCreateView, MaterialIssueNoteUpdateView, MaterialIssueNoteDeleteView,
    MaterialIssueNoteDetailView, EmployeeAutocompleteView
)

urlpatterns = [
    # Main list view for Material Issue Notes
    path('materialissuenotes/', MaterialIssueNoteListView.as_view(), name='materialissuenote_list'),
    
    # HTMX endpoint for the Material Issue Note table partial
    path('materialissuenotes/table/', MaterialIssueNoteTablePartialView.as_view(), name='materialissuenote_table'),

    # HTMX endpoint for employee autocomplete suggestions
    path('employees/autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Detail view for a specific Material Issue Note (equivalent to original "Select" action)
    path('materialissuenotes/<int:pk>/detail/', MaterialIssueNoteDetailView.as_view(), name='materialissuenote_detail'),

    # Generic CRUD paths (as per template instructions, though not explicitly from this ASP.NET page)
    path('materialissuenotes/add/', MaterialIssueNoteCreateView.as_view(), name='materialissuenote_add'),
    path('materialissuenotes/edit/<int:pk>/', MaterialIssueNoteUpdateView.as_view(), name='materialissuenote_edit'),
    path('materialissuenotes/delete/<int:pk>/', MaterialIssueNoteDeleteView.as_view(), name='materialissuenote_delete'),
]
```

### 4.6 Tests

Comprehensive tests for models and views ensure functionality and maintainability.

**`inventory/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialIssueNote, Employee
from datetime import datetime

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up non-managed models directly
        # Use bulk_create for efficiency, assuming IDs are handled by the database
        Employee.objects.bulk_create([
            Employee(id=1, employee_name='John Doe', company_id=1),
            Employee(id=2, employee_name='Jane Smith', company_id=1),
            Employee(id=3, employee_name='Bob Johnson', company_id=2),
            Employee(id=4, employee_name='Alice Brown', company_id=1),
        ])
    
    def test_employee_creation(self):
        employee = Employee.objects.get(id=1)
        self.assertEqual(employee.employee_name, 'John Doe')
        self.assertEqual(employee.company_id, 1)

    def test_employee_str_method(self):
        employee = Employee.objects.get(id=1)
        self.assertEqual(str(employee), 'John Doe [1]')

    def test_get_completion_list(self):
        # Test basic completion
        suggestions = Employee.objects.get_completion_list('Jo', 1)
        self.assertIn(Employee.objects.get(id=1), suggestions)
        self.assertEqual(len(suggestions), 1)

        # Test case-insensitivity
        suggestions = Employee.objects.get_completion_list('john', 1)
        self.assertIn(Employee.objects.get(id=1), suggestions)

        # Test filtering by company_id
        suggestions = Employee.objects.get_completion_list('Bob', 1)
        self.assertNotIn(Employee.objects.get(id=3), suggestions) # Bob is in company 2
        suggestions = Employee.objects.get_completion_list('Bob', 2)
        self.assertIn(Employee.objects.get(id=3), suggestions)

        # Test count limit
        suggestions = Employee.objects.get_completion_list('', 1, count=2)
        self.assertEqual(len(suggestions), 2)
        self.assertTrue(all(s.company_id == 1 for s in suggestions))


class MaterialIssueNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Employee.objects.bulk_create([
            Employee(id=101, employee_name='Ram Sharma', company_id=1),
            Employee(id=102, employee_name='Sita Devi', company_id=1),
        ])

        # Create test data for MaterialIssueNote
        MaterialIssueNote.objects.bulk_create([
            MaterialIssueNote(id=1, fin_year_id=1, fin_year='2023-24', min_no='MIN/001', sys_date=datetime(2024, 1, 1, 10, 0), mrs_no='MRS/001', gen_by='Ram Sharma', company_id=1, session_id=101),
            MaterialIssueNote(id=2, fin_year_id=1, fin_year='2023-24', min_no='MIN/002', sys_date=datetime(2024, 1, 5, 11, 30), mrs_no='MRS/002', gen_by='Sita Devi', company_id=1, session_id=102),
            MaterialIssueNote(id=3, fin_year_id=2, fin_year='2024-25', min_no='MIN/003', sys_date=datetime(2024, 2, 10, 14, 0), mrs_no='MRS/003', gen_by='Ram Sharma', company_id=1, session_id=101),
            MaterialIssueNote(id=4, fin_year_id=1, fin_year='2023-24', min_no='MIN/004', sys_date=datetime(2024, 1, 15, 9, 0), mrs_no=None, gen_by='John Doe', company_id=2, session_id=101),
        ])
  
    def test_materialissuenote_creation(self):
        min_obj = MaterialIssueNote.objects.get(id=1)
        self.assertEqual(min_obj.min_no, 'MIN/001')
        self.assertEqual(min_obj.fin_year, '2023-24')
        self.assertEqual(min_obj.company_id, 1)

    def test_materialissuenote_str_method(self):
        min_obj = MaterialIssueNote.objects.get(id=1)
        self.assertEqual(str(min_obj), 'MIN/001')

    def test_get_employee_display_name(self):
        min_obj = MaterialIssueNote.objects.get(id=1)
        self.assertEqual(min_obj.get_employee_display_name(), 'Ram Sharma')
        
        # Test for non-existent employee
        min_obj_no_employee = MaterialIssueNote.objects.create(
            id=5, fin_year_id=1, fin_year='2023-24', min_no='MIN/005', 
            sys_date=datetime.now(), mrs_no='MRS/005', gen_by='Unknown', 
            company_id=1, session_id=999 # Non-existent employee ID
        )
        self.assertEqual(min_obj_no_employee.get_employee_display_name(), 'N/A')

    def test_filter_by_criteria_mrs_no(self):
        qs = MaterialIssueNote.objects.filter_by_criteria(1, 1, 'MRSNo', 'MRS/001')
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().min_no, 'MIN/001')

    def test_filter_by_criteria_min_no(self):
        qs = MaterialIssueNote.objects.filter_by_criteria(1, 1, 'MINNo', 'MIN/002')
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().min_no, 'MIN/002')

    def test_filter_by_criteria_employee_name_with_id(self):
        # Test parsing "Name [ID]" format
        qs = MaterialIssueNote.objects.filter_by_criteria(1, 1, 'EmployeeName', 'Ram Sharma [101]')
        self.assertEqual(qs.count(), 1) # Only MIN/001
        self.assertEqual(qs.first().min_no, 'MIN/001')

        # Test filtering by employee name (if ID not provided)
        qs = MaterialIssueNote.objects.filter_by_criteria(1, 1, 'EmployeeName', 'Sita Devi')
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().min_no, 'MIN/002')
        
    def test_filter_by_criteria_no_match(self):
        qs = MaterialIssueNote.objects.filter_by_criteria(1, 1, 'MRSNo', 'NONEXISTENT')
        self.assertEqual(qs.count(), 0)

    def test_filter_by_criteria_different_company_id(self):
        qs = MaterialIssueNote.objects.filter_by_criteria(2, 1, 'MINNo', 'MIN/004')
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().min_no, 'MIN/004')
        
        qs = MaterialIssueNote.objects.filter_by_criteria(1, 1, 'MINNo', 'MIN/004')
        self.assertEqual(qs.count(), 0) # MIN/004 is company 2

    def test_filter_by_criteria_different_fin_year_id(self):
        qs = MaterialIssueNote.objects.filter_by_criteria(1, 2, 'MINNo', 'MIN/003')
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().min_no, 'MIN/003')

        qs = MaterialIssueNote.objects.filter_by_criteria(1, 1, 'MINNo', 'MIN/003')
        self.assertEqual(qs.count(), 0) # MIN/003 is fin year 2

class MaterialIssueNoteViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create common test data
        Employee.objects.bulk_create([
            Employee(id=101, employee_name='Gen By Test', company_id=1),
        ])
        MaterialIssueNote.objects.bulk_create([
            MaterialIssueNote(id=1, fin_year_id=1, fin_year='2023-24', min_no='MIN/LIST', sys_date=datetime(2024, 1, 1, 10, 0), mrs_no='MRS/LIST', gen_by='Gen By Test', company_id=1, session_id=101),
            MaterialIssueNote(id=2, fin_year_id=1, fin_year='2023-24', min_no='MIN/EDIT', sys_date=datetime(2024, 1, 2, 10, 0), mrs_no='MRS/EDIT', gen_by='Gen By Test', company_id=1, session_id=101),
            MaterialIssueNote(id=3, fin_year_id=1, fin_year='2023-24', min_no='MIN/DEL', sys_date=datetime(2024, 1, 3, 10, 0), mrs_no='MRS/DEL', gen_by='Gen By Test', company_id=1, session_id=101),
            MaterialIssueNote(id=4, fin_year_id=1, fin_year='2023-24', min_no='MIN/SEARCH', sys_date=datetime(2024, 1, 4, 10, 0), mrs_no='MRS/SEARCH', gen_by='Gen By Test', company_id=1, session_id=101),
            MaterialIssueNote(id=5, fin_year_id=1, fin_year='2023-24', min_no='MIN/OTHER', sys_date=datetime(2024, 1, 5, 10, 0), mrs_no='MRS/OTHER', gen_by='Another Employee [102]', company_id=1, session_id=102), # Assume 102 exists
        ])
        
    def setUp(self):
        self.client = Client()
        # Simulate session variables
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 1
        session.save()
    
    def test_list_view(self):
        response = self.client.get(reverse('materialissuenote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/list.html')
        self.assertContains(response, 'Material Issue Note [MIN] - Print')

    def test_table_partial_view(self):
        response = self.client.get(reverse('materialissuenote_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertContains(response, 'MIN/LIST') # Check if data is present
        self.assertContains(response, '<table id="materialissuenoteTable"')

    def test_table_partial_view_search_mrs_no(self):
        response = self.client.get(reverse('materialissuenote_table'), {'search_field': 'MRSNo', 'search_value_mrs_min': 'MRS/SEARCH'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MIN/SEARCH')
        self.assertNotContains(response, 'MIN/LIST')
    
    def test_table_partial_view_search_min_no(self):
        response = self.client.get(reverse('materialissuenote_table'), {'search_field': 'MINNo', 'search_value_mrs_min': 'MIN/EDIT'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MIN/EDIT')
        self.assertNotContains(response, 'MIN/LIST')

    def test_table_partial_view_search_employee_name(self):
        response = self.client.get(reverse('materialissuenote_table'), {'search_field': 'EmployeeName', 'search_value_employee': 'Gen By Test [101]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MIN/LIST')
        self.assertContains(response, 'MIN/EDIT')
        self.assertContains(response, 'MIN/DEL')
        self.assertNotContains(response, 'MIN/OTHER') # MIN/OTHER is by employee 102

    def test_table_partial_view_pagination(self):
        # Assuming only 1 item per page for testing pagination
        response = self.client.get(reverse('materialissuenote_table') + '?page=1', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Page 1 of') # Should be dynamic based on count
        # Reconfigure paginate_by to 1 in the view for specific pagination testing,
        # or create more test data to exceed default paginate_by.

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'John'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('John Doe [1]', response.content.decode()) # From global setup, company_id=1
        self.assertIn('<datalist id="employee-suggestions-datalist">', response.content.decode())

    def test_materialissuenote_detail_view(self):
        min_obj = MaterialIssueNote.objects.get(min_no='MIN/LIST')
        response = self.client.get(reverse('materialissuenote_detail', args=[min_obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/detail.html')
        self.assertContains(response, f"MIN No: {min_obj.min_no}")
        self.assertContains(response, "Messages") # Check for messages from view

    def test_materialissuenote_detail_view_not_found(self):
        response = self.client.get(reverse('materialissuenote_detail', args=[999999])) # Non-existent PK
        self.assertEqual(response.status_code, 302) # Redirects to list
        self.assertRedirects(response, reverse('materialissuenote_list'))

    # --- Generic CRUD View Tests (as per template instructions) ---
    def test_create_view_get(self):
        response = self.client.get(reverse('materialissuenote_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'min_no': 'MIN/NEW',
            'mrs_no': 'MRS/NEW',
            'sys_date': '2024-03-15',
            'gen_by': 'Test User',
            'fin_year': '2023-24'
        }
        response = self.client.post(reverse('materialissuenote_add'), data, HTTP_HX_REQUEST='true', content_type='application/json')
        self.assertEqual(response.status_code, 204) # HTMX No Content response for success
        self.assertTrue(MaterialIssueNote.objects.filter(min_no='MIN/NEW').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialIssueNoteList', response.headers['HX-Trigger'])
        
    def test_create_view_post_invalid(self):
        data = { 'min_no': '' } # Invalid data
        response = self.client.post(reverse('materialissuenote_add'), data, HTTP_HX_REQUEST='true', content_type='application/json')
        self.assertEqual(response.status_code, 200) # HTMX should return form with errors
        self.assertContains(response, 'This field is required') # Check for error message
        self.assertFalse(MaterialIssueNote.objects.filter(min_no='').exists())

    def test_update_view_get(self):
        obj = MaterialIssueNote.objects.get(min_no='MIN/EDIT')
        response = self.client.get(reverse('materialissuenote_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.min_no, 'MIN/EDIT')

    def test_update_view_post_success(self):
        obj = MaterialIssueNote.objects.get(min_no='MIN/EDIT')
        data = {
            'min_no': 'MIN/UPDATED',
            'mrs_no': obj.mrs_no,
            'sys_date': obj.sys_date.strftime('%Y-%m-%d'),
            'gen_by': obj.gen_by,
            'fin_year': obj.fin_year
        }
        response = self.client.post(reverse('materialissuenote_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true', content_type='application/json')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.min_no, 'MIN/UPDATED')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialIssueNoteList', response.headers['HX-Trigger'])
        
    def test_delete_view_get(self):
        obj = MaterialIssueNote.objects.get(min_no='MIN/DEL')
        response = self.client.get(reverse('materialissuenote_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/confirm_delete.html')
        self.assertTrue('object' in response.context)
        
    def test_delete_view_post_success(self):
        obj = MaterialIssueNote.objects.get(min_no='MIN/DEL')
        response = self.client.delete(reverse('materialissuenote_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialIssueNote.objects.filter(pk=obj.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialIssueNoteList', response.headers['HX-Trigger'])
```

## Step 5: HTMX and Alpine.js Integration

The provided code demonstrates the full integration of HTMX and Alpine.js:

*   **HTMX for Dynamic Updates:**
    *   The main list page (`list.html`) uses `hx-get` to load the table content from `{% url 'materialissuenote_table' %}` upon page load (`hx-trigger="load"`).
    *   Any CRUD operation (add, edit, delete) triggers an `HX-Trigger` header (`refreshMaterialIssueNoteList`) from the server upon success, which causes the `materialissuenoteTable` to reload automatically, ensuring the list is always fresh without full page reloads.
    *   The search form uses `hx-get` to filter the table, submitting the form parameters to the `materialissuenote_table` endpoint.
    *   The dropdown `search_field` also triggers a `hx-get` on change, reloading the table (and potentially hiding/showing the appropriate search input field via Alpine.js).
    *   Pagination buttons use `hx-get` to fetch new pages of data, targeting the `materialissuenote-list-container`.
    *   The autocomplete for employee names uses `hx-get` to `{% url 'employee_autocomplete' %}` which returns a `<datalist>` HTML snippet that HTMX swaps into the DOM, making the browser's native autocomplete functionality available.
*   **Alpine.js for UI State Management:**
    *   `x-data` and `x-show` attributes are used in `list.html` to conditionally show/hide the `TxtMrs` and `TxtEmpName` equivalent fields based on the `DrpField` selection, mimicking the ASP.NET `DrpField_SelectedIndexChanged` behavior client-side.
    *   The modal functionality (`#modal`) uses Alpine.js (`x-data` and `_` attribute with htmx events) to manage its visibility, showing it when an HTMX request for a form is initiated and hiding it after a successful form submission or cancellation.
*   **DataTables for List Views:**
    *   The `_materialissuenote_table.html` partial includes a JavaScript snippet that initializes DataTables on the `materialissuenoteTable`.
    *   Crucially, DataTables' built-in pagination, searching, and length change features are disabled (`"paging": false`, `"searching": false`, `"lengthChange": false`) because these functionalities are handled by Django's ListView pagination and HTMX-driven filtering on the server-side, ensuring a single source of truth for data and efficient server-side processing. Sorting is left enabled for DataTables client-side, but it only applies to the current page of data.

## Final Notes

*   All placeholders like `[APP_NAME]`, `[MODEL_NAME]`, etc., have been replaced with concrete values (`inventory`, `MaterialIssueNote`).
*   Templates are designed to be DRY, with `_materialissuenote_table.html` being a reusable partial.
*   Business logic for filtering and data retrieval is encapsulated in the `MaterialIssueNoteManager` and `EmployeeManager` within `models.py`, adhering to the "Fat Model, Thin View" principle. Views are kept concise (typically 5-15 lines).
*   Comprehensive unit tests for models and integration tests for views are provided, aiming for high test coverage.
*   The communication style is non-technical, focusing on the "what" and "why" rather than overly technical "how" details, making it suitable for business stakeholders while providing runnable code for developers.
*   The transition strategy focuses on AI-assisted automation, providing complete code snippets that can be generated and integrated systematically.