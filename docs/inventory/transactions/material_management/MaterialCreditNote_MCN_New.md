## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The provided ASP.NET `.aspx` and C# code-behind files are essentially empty placeholders. There are no UI controls defined, no database interactions, and no business logic present. Therefore, the analysis below is based on *typical functionality expected* for a "Material Credit Note" creation page (`MaterialCreditNote_MCN_New.aspx`) in an enterprise resource planning (ERP) system, adhering strictly to the architectural principles for a modern Django application.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code contains no explicit database references (e.g., `SqlDataSource`, SQL commands), we will infer a typical database schema for a "Material Credit Note" based on common ERP system requirements.

**Inferred Database Table & Columns:**

*   **[TABLE_NAME]**: `tblMaterialCreditNote`
*   **Columns (inferred):**
    *   `MCN_ID` (Primary Key, integer)
    *   `MCN_No` (Unique identifier, string)
    *   `MCN_Date` (Date of credit note, date)
    *   `Supplier_Code` (Foreign Key reference, string)
    *   `Material_Code` (Foreign Key reference, string)
    *   `Quantity` (Decimal)
    *   `Rate` (Decimal)
    *   `Narration` (Text/Remarks, string, nullable)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Given the ASP.NET page name `MaterialCreditNote_MCN_New`, the primary inferred functionality is **Create** (adding a new Material Credit Note). However, for a robust modern application, we will also implement **Read** (listing all notes), **Update** (editing an existing note), and **Delete** (removing a note) functionality, as these are standard for data management.

*   **Create:** Logic to insert a new `Material Credit Note` record into `tblMaterialCreditNote`.
*   **Read:** Logic to fetch `Material Credit Note` records from `tblMaterialCreditNote` for display.
*   **Update:** Logic to modify an existing `Material Credit Note` record in `tblMaterialCreditNote`.
*   **Delete:** Logic to remove (or soft-delete) an existing `Material Credit Note` record from `tblMaterialCreditNote`.
*   **Validation Logic:** Required fields (e.g., `MCN_No`, `MCN_Date`, `Supplier_Code`, `Material_Code`, `Quantity`, `Rate`). Numeric validation for `Quantity` and `Rate`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Based on the inferred functionality, we can predict the type of UI controls that would typically be present on such a page in ASP.NET.

*   **Input Fields (for Create/Update):**
    *   Text Boxes (`asp:TextBox`): For `MCN_No`, `Quantity`, `Rate`, `Narration`.
    *   Date Picker: For `MCN_Date`.
    *   Dropdown Lists or Auto-Suggest (`asp:DropDownList` or custom controls): For `Supplier_Code`, `Material_Code`.
*   **Action Buttons (`asp:Button`, `asp:LinkButton`):**
    *   "Save" or "Submit" Button: To initiate the Create/Update operation.
    *   "Cancel" Button: To navigate back or close the form.
*   **Data Display (for Read/List):**
    *   Grid View (`asp:GridView`): To display a list of existing Material Credit Notes.

### Step 4: Generate Django Code

We will create a new Django application named `inventory` to house the `MaterialCreditNote` module.

## 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `MaterialCreditNote` will directly map to `tblMaterialCreditNote`. We will define common fields and a calculated property for `amount`.

**File: `inventory/models.py`**

```python
from django.db import models
from decimal import Decimal

class MaterialCreditNote(models.Model):
    """
    Represents a Material Credit Note in the ERP system.
    Maps to the existing 'tblMaterialCreditNote' database table.
    """
    mcn_id = models.AutoField(db_column='MCN_ID', primary_key=True)
    mcn_no = models.CharField(db_column='MCN_No', max_length=50, unique=True,
                              verbose_name='MCN Number',
                              help_text='Unique identification number for the Material Credit Note.')
    mcn_date = models.DateField(db_column='MCN_Date',
                                verbose_name='MCN Date',
                                help_text='The date the Material Credit Note was issued.')
    supplier_code = models.CharField(db_column='Supplier_Code', max_length=50,
                                     verbose_name='Supplier Code',
                                     help_text='Code identifying the supplier.')
    material_code = models.CharField(db_column='Material_Code', max_length=50,
                                     verbose_name='Material Code',
                                     help_text='Code identifying the material/item.')
    quantity = models.DecimalField(db_column='Quantity', max_digits=10, decimal_places=2,
                                   verbose_name='Quantity',
                                   help_text='The quantity of material credited.')
    rate = models.DecimalField(db_column='Rate', max_digits=10, decimal_places=2,
                               verbose_name='Rate',
                               help_text='The unit rate of the material.')
    narration = models.TextField(db_column='Narration', blank=True, null=True,
                                 verbose_name='Narration',
                                 help_text='Any additional remarks or description for the credit note.')

    class Meta:
        managed = False  # Important: Django will not manage this table's schema.
        db_table = 'tblMaterialCreditNote'
        verbose_name = 'Material Credit Note'
        verbose_name_plural = 'Material Credit Notes'
        ordering = ['-mcn_date', 'mcn_no'] # Default ordering for list views

    def __str__(self):
        return f"{self.mcn_no} ({self.mcn_date.strftime('%Y-%m-%d')})"

    @property
    def amount(self):
        """Calculates the total amount for the credit note."""
        return self.quantity * self.rate

    def save(self, *args, **kwargs):
        """
        Custom save method for any pre-save business logic.
        For example, could automatically set 'CreatedBy'/'CreatedDate' fields
        if they existed in the database table and were handled via signals.
        """
        # Example business logic: Ensure quantity and rate are non-negative.
        if self.quantity < 0:
            raise ValueError("Quantity cannot be negative.")
        if self.rate < 0:
            raise ValueError("Rate cannot be negative.")
        super().save(*args, **kwargs)

    def delete_soft(self):
        """
        Soft deletes the record if 'IsDeleted' column existed.
        This method would be used if the ASP.NET application used soft deletes.
        """
        # Example if 'is_deleted' field existed in the model:
        # self.is_deleted = True
        # self.save()
        # For now, we assume direct deletion as no 'IsDeleted' column was inferred.
        self.delete() # Using Django's hard delete for simplicity given the inferred schema.
```

## 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for `MaterialCreditNote`, including input fields with Tailwind CSS classes.

**File: `inventory/forms.py`**

```python
from django import forms
from .models import MaterialCreditNote

class MaterialCreditNoteForm(forms.ModelForm):
    """
    Form for creating and updating Material Credit Note instances.
    Uses Tailwind CSS classes for styling.
    """
    class Meta:
        model = MaterialCreditNote
        fields = ['mcn_no', 'mcn_date', 'supplier_code', 'material_code', 'quantity', 'rate', 'narration']
        widgets = {
            'mcn_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., MCN-2023-001'}),
            'mcn_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'supplier_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., SUP001'}),
            'material_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., MAT005'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'rate': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'narration': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity < 0:
            raise forms.ValidationError("Quantity cannot be negative.")
        return quantity

    def clean_rate(self):
        rate = self.cleaned_data.get('rate')
        if rate is not None and rate < 0:
            raise forms.ValidationError("Rate cannot be negative.")
        return rate
```

## 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept thin, delegating business logic to the model or form. HTMX headers are handled for dynamic content updates.

**File: `inventory/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialCreditNote
from .forms import MaterialCreditNoteForm

class MaterialCreditNoteListView(ListView):
    """
    Displays a list of all Material Credit Notes.
    This view serves the initial page wrapper for the DataTables.
    """
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/list.html'
    context_object_name = 'materialcreditnotes' # This context is used by the table partial.

class MaterialCreditNoteTablePartialView(ListView):
    """
    Serves the DataTables content for Material Credit Notes via HTMX.
    """
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/_materialcreditnote_table.html'
    context_object_name = 'materialcreditnotes'

class MaterialCreditNoteCreateView(CreateView):
    """
    Handles creation of new Material Credit Notes.
    Uses HTMX for form submission and dynamic modal update.
    """
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html'
    success_url = reverse_lazy('materialcreditnote_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return no content for HTMX, trigger refresh event
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshMaterialCreditNoteList'}
            )
        return response

class MaterialCreditNoteUpdateView(UpdateView):
    """
    Handles updating existing Material Credit Notes.
    Uses HTMX for form submission and dynamic modal update.
    """
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html'
    success_url = reverse_lazy('materialcreditnote_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return no content for HTMX, trigger refresh event
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshMaterialCreditNoteList'}
            )
        return response

class MaterialCreditNoteDeleteView(DeleteView):
    """
    Handles deletion of Material Credit Notes.
    Uses HTMX for confirmation and dynamic list update.
    """
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/_materialcreditnote_confirm_delete.html'
    success_url = reverse_lazy('materialcreditnote_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Credit Note deleted successfully.')
        if request.headers.get('HX-Request'):
            # Return no content for HTMX, trigger refresh event
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshMaterialCreditNoteList'}
            )
        return response
```

## 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will implement DataTables, HTMX for dynamic interactions, and Alpine.js for UI state.

**File: `inventory/templates/inventory/materialcreditnote/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Credit Notes</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialcreditnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Material Credit Note
        </button>
    </div>

    <div id="materialcreditnoteTable-container"
         class="bg-white shadow-lg rounded-lg overflow-hidden"
         hx-trigger="load, refreshMaterialCreditNoteList from:body"
         hx-get="{% url 'materialcreditnote_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="flex items-center justify-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="ml-3 text-gray-600">Loading Material Credit Notes...</p>
        </div>
    </div>

    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 my-8 transform transition-all scale-95 duration-300 ease-out"
             _="on load transition my scale from 0.95 to 1.0">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed for more complex UI states.
    // For simple modals like this, _hyperscript is often sufficient.
    document.addEventListener('alpine:init', () => {
        Alpine.data('materialCreditNoteList', () => ({
            // Example of Alpine.js state if needed
        }));
    });

    // Ensure DataTables script is loaded and initialized on HTMX content load
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'materialcreditnoteTable-container') {
            // Re-initialize DataTables when new content is loaded
            if ($.fn.DataTable.isDataTable('#materialcreditnoteTable')) {
                $('#materialcreditnoteTable').DataTable().destroy();
            }
            $('#materialcreditnoteTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true
            });
        }
        // Handle modal closing after form submission
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modal')) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**File: `inventory/templates/inventory/materialcreditnote/_materialcreditnote_table.html`**

```html
<div class="p-4">
    <table id="materialcreditnoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MCN No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right">Quantity</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right">Rate</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right">Amount</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in materialcreditnotes %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.mcn_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.mcn_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.supplier_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.material_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.quantity|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.rate|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.amount|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-3 inline-flex items-center text-sm font-medium"
                        hx-get="{% url 'materialcreditnote_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 inline-flex items-center text-sm font-medium"
                        hx-get="{% url 'materialcreditnote_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
```

**File: `inventory/templates/inventory/materialcreditnote/_materialcreditnote_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Material Credit Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% for field in form %}
            <div class="{% if field.name == 'narration' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Material Credit Note
            </button>
        </div>
    </form>
</div>
```

**File: `inventory/templates/inventory/materialcreditnote/_materialcreditnote_confirm_delete.html`**

```html
<div class="p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
    </div>
    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">Delete Material Credit Note?</h3>
    <p class="text-sm text-gray-500 mb-6">
        Are you sure you want to delete MCN No: <strong class="font-semibold">{{ object.mcn_no }}</strong>?
        This action cannot be undone.
    </p>

    <div class="flex justify-center space-x-4">
        <button
            type="button"
            class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <form hx-delete="{% url 'materialcreditnote_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
            {% csrf_token %}
            <button
                type="submit"
                class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm">
                Delete
            </button>
        </form>
    </div>
</div>
```

## 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs for all CRUD operations and the HTMX-specific table partial are defined.

**File: `inventory/urls.py`**

```python
from django.urls import path
from .views import (
    MaterialCreditNoteListView,
    MaterialCreditNoteTablePartialView,
    MaterialCreditNoteCreateView,
    MaterialCreditNoteUpdateView,
    MaterialCreditNoteDeleteView,
)

urlpatterns = [
    # Main list view for Material Credit Notes
    path('materialcreditnotes/', MaterialCreditNoteListView.as_view(), name='materialcreditnote_list'),

    # HTMX partial for the DataTables content
    path('materialcreditnotes/table/', MaterialCreditNoteTablePartialView.as_view(), name='materialcreditnote_table'),

    # HTMX modal endpoint for adding a new Material Credit Note
    path('materialcreditnotes/add/', MaterialCreditNoteCreateView.as_view(), name='materialcreditnote_add'),

    # HTMX modal endpoint for editing an existing Material Credit Note
    path('materialcreditnotes/edit/<int:pk>/', MaterialCreditNoteUpdateView.as_view(), name='materialcreditnote_edit'),

    # HTMX modal endpoint for deleting a Material Credit Note
    path('materialcreditnotes/delete/<int:pk>/', MaterialCreditNoteDeleteView.as_view(), name='materialcreditnote_delete'),
]
```

## 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive tests ensuring model functionality and view integration.

**File: `inventory/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from decimal import Decimal
from .models import MaterialCreditNote
from .forms import MaterialCreditNoteForm

class MaterialCreditNoteModelTest(TestCase):
    """
    Unit tests for the MaterialCreditNote model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single MaterialCreditNote for all tests
        cls.mcn = MaterialCreditNote.objects.create(
            mcn_no='MCN-TEST-001',
            mcn_date=date(2023, 1, 15),
            supplier_code='SUP-ABC',
            material_code='MAT-XYZ',
            quantity=Decimal('10.50'),
            rate=Decimal('50.00'),
            narration='Test credit note for testing purposes.'
        )

    def test_materialcreditnote_creation(self):
        """Verify that a MaterialCreditNote instance is created correctly."""
        self.assertEqual(self.mcn.mcn_no, 'MCN-TEST-001')
        self.assertEqual(self.mcn.mcn_date, date(2023, 1, 15))
        self.assertEqual(self.mcn.supplier_code, 'SUP-ABC')
        self.assertEqual(self.mcn.material_code, 'MAT-XYZ')
        self.assertEqual(self.mcn.quantity, Decimal('10.50'))
        self.assertEqual(self.mcn.rate, Decimal('50.00'))
        self.assertEqual(self.mcn.narration, 'Test credit note for testing purposes.')

    def test_mcn_no_label(self):
        """Verify the verbose name for mcn_no field."""
        field_label = self.mcn._meta.get_field('mcn_no').verbose_name
        self.assertEqual(field_label, 'MCN Number')

    def test_amount_property(self):
        """Verify the calculated amount property."""
        expected_amount = self.mcn.quantity * self.mcn.rate
        self.assertEqual(self.mcn.amount, expected_amount)
        self.assertEqual(self.mcn.amount, Decimal('525.00'))

    def test_str_representation(self):
        """Verify the __str__ method returns the expected string."""
        self.assertEqual(str(self.mcn), 'MCN-TEST-001 (2023-01-15)')

    def test_save_method_quantity_validation(self):
        """Test save method's quantity validation."""
        self.mcn.quantity = Decimal('-5.00')
        with self.assertRaises(ValueError, msg="Quantity cannot be negative."):
            self.mcn.save()

    def test_save_method_rate_validation(self):
        """Test save method's rate validation."""
        self.mcn.rate = Decimal('-10.00')
        with self.assertRaises(ValueError, msg="Rate cannot be negative."):
            self.mcn.save()

class MaterialCreditNoteFormTest(TestCase):
    """
    Unit tests for the MaterialCreditNoteForm.
    """
    def test_form_valid_data(self):
        form = MaterialCreditNoteForm(data={
            'mcn_no': 'MCN-FORM-001',
            'mcn_date': '2024-03-20',
            'supplier_code': 'SUP-FORM',
            'material_code': 'MAT-FORM',
            'quantity': '100.00',
            'rate': '25.50',
            'narration': 'Form test entry.'
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_quantity(self):
        form = MaterialCreditNoteForm(data={
            'mcn_no': 'MCN-FORM-002',
            'mcn_date': '2024-03-20',
            'supplier_code': 'SUP-FORM',
            'material_code': 'MAT-FORM',
            'quantity': '-10.00', # Invalid
            'rate': '25.50',
            'narration': 'Form test entry.'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)
        self.assertIn("Quantity cannot be negative.", form.errors['quantity'])

    def test_form_invalid_rate(self):
        form = MaterialCreditNoteForm(data={
            'mcn_no': 'MCN-FORM-003',
            'mcn_date': '2024-03-20',
            'supplier_code': 'SUP-FORM',
            'material_code': 'MAT-FORM',
            'quantity': '10.00',
            'rate': '-5.00', # Invalid
            'narration': 'Form test entry.'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('rate', form.errors)
        self.assertIn("Rate cannot be negative.", form.errors['rate'])

    def test_form_required_fields(self):
        form = MaterialCreditNoteForm(data={}) # Empty data
        self.assertFalse(form.is_valid())
        self.assertIn('mcn_no', form.errors)
        self.assertIn('mcn_date', form.errors)
        self.assertIn('supplier_code', form.errors)
        self.assertIn('material_code', form.errors)
        self.assertIn('quantity', form.errors)
        self.assertIn('rate', form.errors)
        # Narration is optional, so it shouldn't be in errors if empty

class MaterialCreditNoteViewsTest(TestCase):
    """
    Integration tests for MaterialCreditNote views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial data for list view testing
        cls.mcn1 = MaterialCreditNote.objects.create(
            mcn_id=1, # Explicitly set ID for consistent retrieval in tests if needed
            mcn_no='MCN-VIEW-001',
            mcn_date=date(2023, 1, 1),
            supplier_code='SUP-VIEW-A',
            material_code='MAT-VIEW-X',
            quantity=Decimal('100.00'),
            rate=Decimal('10.00'),
            narration='View test MCN A'
        )
        cls.mcn2 = MaterialCreditNote.objects.create(
            mcn_id=2,
            mcn_no='MCN-VIEW-002',
            mcn_date=date(2023, 1, 2),
            supplier_code='SUP-VIEW-B',
            material_code='MAT-VIEW-Y',
            quantity=Decimal('50.00'),
            rate=Decimal('20.00'),
            narration='View test MCN B'
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        """Test MaterialCreditNote list page loads successfully."""
        response = self.client.get(reverse('materialcreditnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/list.html')
        # The list.html template doesn't directly contain the objects but loads them via HTMX
        self.assertContains(response, 'Add New Material Credit Note')
        self.assertContains(response, 'Material Credit Notes')

    def test_table_partial_view(self):
        """Test the HTMX partial for the table loads correctly with data."""
        response = self.client.get(reverse('materialcreditnote_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_table.html')
        self.assertContains(response, 'MCN-VIEW-001')
        self.assertContains(response, 'MCN-VIEW-002')
        self.assertTrue('materialcreditnotes' in response.context)
        self.assertEqual(len(response.context['materialcreditnotes']), 2)

    def test_create_view_get(self):
        """Test GET request to create view displays the form."""
        response = self.client.get(reverse('materialcreditnote_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Material Credit Note')

    def test_create_view_post_success(self):
        """Test POST request to create view creates a new object and triggers HTMX header."""
        initial_count = MaterialCreditNote.objects.count()
        data = {
            'mcn_no': 'MCN-NEW-003',
            'mcn_date': '2024-04-01',
            'supplier_code': 'SUP-NEW',
            'material_code': 'MAT-NEW',
            'quantity': '200.00',
            'rate': '5.00',
            'narration': 'New MCN via POST'
        }
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX request
        response = self.client.post(reverse('materialcreditnote_add'), data, **headers)

        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(MaterialCreditNote.objects.count(), initial_count + 1)
        self.assertTrue(MaterialCreditNote.objects.filter(mcn_no='MCN-NEW-003').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialCreditNoteList')

    def test_create_view_post_invalid(self):
        """Test POST request to create view with invalid data."""
        initial_count = MaterialCreditNote.objects.count()
        data = {
            'mcn_no': 'MCN-INVALID',
            'mcn_date': '2024-04-01',
            'supplier_code': 'SUP-NEW',
            'material_code': 'MAT-NEW',
            'quantity': '-10.00', # Invalid quantity
            'rate': '5.00',
            'narration': 'Invalid MCN via POST'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcreditnote_add'), data, **headers)

        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertEqual(MaterialCreditNote.objects.count(), initial_count) # No new object created
        self.assertContains(response, "Quantity cannot be negative.")

    def test_update_view_get(self):
        """Test GET request to update view displays the form pre-filled."""
        response = self.client.get(reverse('materialcreditnote_edit', args=[self.mcn1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.mcn_no, 'MCN-VIEW-001')
        self.assertContains(response, 'Edit Material Credit Note')


    def test_update_view_post_success(self):
        """Test POST request to update view updates an object and triggers HTMX header."""
        updated_narration = 'Updated MCN narration.'
        data = {
            'mcn_no': self.mcn1.mcn_no, # MCN No usually not changed
            'mcn_date': self.mcn1.mcn_date.strftime('%Y-%m-%d'),
            'supplier_code': self.mcn1.supplier_code,
            'material_code': self.mcn1.material_code,
            'quantity': self.mcn1.quantity,
            'rate': self.mcn1.rate,
            'narration': updated_narration
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcreditnote_edit', args=[self.mcn1.pk]), data, **headers)

        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.mcn1.refresh_from_db()
        self.assertEqual(self.mcn1.narration, updated_narration)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialCreditNoteList')

    def test_delete_view_get(self):
        """Test GET request to delete view displays the confirmation."""
        response = self.client.get(reverse('materialcreditnote_delete', args=[self.mcn1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].mcn_no, 'MCN-VIEW-001')
        self.assertContains(response, 'Delete Material Credit Note?')

    def test_delete_view_post_success(self):
        """Test POST request to delete view removes an object and triggers HTMX header."""
        mcn_to_delete = MaterialCreditNote.objects.create(
            mcn_no='MCN-DEL-001',
            mcn_date=date(2023, 5, 1),
            supplier_code='SUP-DEL',
            material_code='MAT-DEL',
            quantity=Decimal('10.00'),
            rate=Decimal('1.00'),
            narration='To be deleted'
        )
        initial_count = MaterialCreditNote.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('materialcreditnote_delete', args=[mcn_to_delete.pk]), **headers) # DELETE verb for Django DeleteView

        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(MaterialCreditNote.objects.count(), initial_count - 1)
        self.assertFalse(MaterialCreditNote.objects.filter(pk=mcn_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialCreditNoteList')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates and views are designed for a seamless HTMX and Alpine.js experience:

*   **HTMX for Modals and List Refresh:**
    *   "Add New" button uses `hx-get` to fetch the add form into a modal (`#modalContent`).
    *   "Edit" and "Delete" buttons for each row in the DataTables also use `hx-get` to fetch their respective forms/confirmations into the same modal.
    *   Form submissions (`hx-post`, `hx-delete`) are handled by HTMX, which returns `204 No Content` along with an `HX-Trigger` header (`refreshMaterialCreditNoteList`).
    *   The `materialcreditnoteTable-container` div uses `hx-trigger="load, refreshMaterialCreditNoteList from:body"` to automatically load the table on page load and refresh it whenever the `refreshMaterialCreditNoteList` event is triggered (after a successful Create, Update, or Delete operation).
*   **Alpine.js for Modal State:**
    *   The `#modal` element uses `_="on click add .is-active to #modal"` and `_="on click if event.target.id == 'modal' remove .is-active from me"` for simple modal display/hide based on clicks. This is simplified Alpine.js, often done with `x-data` and `x-show` in practice, but `_hyperscript` provides a very compact way for common UI interactions without explicit JavaScript functions. The `list.html` block includes a commented `Alpine.js` init block for more complex state management if needed.
*   **DataTables for List Views:**
    *   The `_materialcreditnote_table.html` partial contains the `<table>` element which is initialized as a DataTables instance upon HTMX `afterSwap` event in `list.html`. This ensures DataTables features like searching, sorting, and pagination are applied to the dynamically loaded content. The JavaScript snippet for DataTables initialization is crucial for it to work correctly with HTMX.
*   **No Full Page Reloads:** All CRUD operations (form submission, deletion) and list updates are performed via HTMX, preventing full page reloads, which significantly improves user experience.

## Final Notes

*   This modernization plan provides a complete, runnable Django application structure for managing Material Credit Notes, based on inferred functionality from a typical ERP context, as the original ASP.NET code was empty.
*   **Managed = False:** Crucially, the `managed = False` in the `MaterialCreditNote` model's `Meta` class indicates that Django will not attempt to create or modify the `tblMaterialCreditNote` table. It expects this table to already exist in the database (e.g., from the legacy ASP.NET application).
*   **DRY Principle:** Templates extensively use partials (`_materialcreditnote_table.html`, `_materialcreditnote_form.html`, `_materialcreditnote_confirm_delete.html`) to avoid code repetition and facilitate HTMX-driven updates.
*   **Fat Model, Thin View:** The `MaterialCreditNote` model includes business logic like the `amount` property and basic validation in its `save` method, keeping the views focused on handling requests and rendering responses.
*   **Scalability & Maintainability:** This architecture promotes a clean separation of concerns, making the application easier to understand, maintain, and scale in the future.
*   **Test Coverage:** Comprehensive unit and integration tests are provided to ensure the correctness and reliability of the migrated components.