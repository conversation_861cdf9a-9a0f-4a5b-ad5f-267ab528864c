## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The ASP.NET code provided primarily performs a cleanup operation on a temporary table (`tblinv_MaterialReturn_Temp`) when the "Dashboard" page loads. It removes records associated with the current user's session and company ID. A "Dashboard" typically implies displaying and managing data. Therefore, this modernization plan will focus on creating a robust Django dashboard for `MaterialReturnNote` entities, and separately demonstrate how the temporary table cleanup can be handled in a Django-appropriate way.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

From the C# code, we explicitly see:
- `fun.delete("tblinv_MaterialReturn_Temp", "CompId='" + CompId + "'And SessionId='" + sId + "'");`
This indicates a temporary table named `tblinv_MaterialReturn_Temp` with columns `CompId` and `SessionId`. To make it a functional temporary record for a material return note, we infer additional fields.

For the actual "Material Return Note Dashboard" functionality, we infer a primary persistent table where finalized material return notes are stored. Let's call it `tblinv_MaterialReturn`.

**Inferred Database Schemas:**

1.  **Temporary Table:** `tblinv_MaterialReturn_Temp`
    *   **Purpose:** Stores transient, in-progress material return note data for a specific user session and company.
    *   **Columns:**
        *   `TempNoteId` (Primary Key, Integer, auto-increment) - *Inferred*
        *   `CompId` (Integer) - *Explicit from code*
        *   `SessionId` (String/VARCHAR) - *Explicit from code*
        *   `TempReturnDate` (Date) - *Inferred*
        *   `TempMaterialName` (String/VARCHAR) - *Inferred*
        *   `TempQuantity` (Decimal/Float) - *Inferred*
        *   `TempRemarks` (Text) - *Inferred*

2.  **Main Table (Inferred for Dashboard Functionality):** `tblinv_MaterialReturn`
    *   **Purpose:** Stores finalized, persistent material return notes. This will be the focus of the Django dashboard.
    *   **Columns:**
        *   `MaterialReturnNoteId` (Primary Key, Integer, auto-increment) - *Inferred*
        *   `CompId` (Integer) - *Typically a Foreign Key to a Company table*
        *   `ReturnDate` (Date)
        *   `MaterialName` (String/VARCHAR)
        *   `Quantity` (Decimal/Float)
        *   `Unit` (String/VARCHAR, e.g., KG, PCS) - *Inferred for realism*
        *   `Reason` (Text)
        *   `ReturnedBy` (String/VARCHAR) - *Maps to `Session["username"]` for historical tracking*
        *   `CreatedAt` (DateTime) - *Audit field*
        *   `UpdatedAt` (DateTime) - *Audit field*

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code (`Page_Load` in `MaterialReturnNote_Dashboard.aspx.cs`) explicitly performs only a **Delete** operation on `tblinv_MaterialReturn_Temp` based on `CompId` and `SessionId`.

*   **Create:** Not explicitly shown in the provided code. In a full system, temporary records would be created before reaching this dashboard, and finalized notes would be created/saved from a different entry point.
*   **Read:** Not explicitly shown. A dashboard implies reading (displaying) data, which will be the primary functionality of the Django `ListView`.
*   **Update:** Not explicitly shown.
*   **Delete:** Explicitly shown for `tblinv_MaterialReturn_Temp`. We will implement this as a utility function. For the main `MaterialReturnNote` entities, standard Django `DeleteView` will be provided.

**Validation Logic:** No explicit validation logic is visible in the provided code. We will implement standard Django form validation.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The `.aspx` file is almost entirely empty, relying on master page content and the code-behind for logic. This implies the "dashboard" might have been heavily dynamic or simply a landing page for cleanup.
Given the target architecture (Django + HTMX + Alpine.js + DataTables), we will infer standard UI components for a modern dashboard:
*   A **DataTable** for listing `MaterialReturnNote` entries, offering search, sort, and pagination.
*   **Buttons** for "Add New", "Edit", and "Delete" actions.
*   **Modals** (using HTMX for content loading and Alpine.js for state management) for "Add", "Edit", and "Confirm Delete" forms.
*   Form fields (Text inputs, Date pickers, etc.) for `MaterialReturnNote` creation and editing.

## Step 4: Generate Django Code

We will create a Django application named `inventory_transactions`.

### 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

We'll create two models:
1.  `MaterialReturnNote`: The primary model for the dashboard.
2.  `MaterialReturnNoteTemp`: To directly reflect the temporary table from the ASP.NET code and encapsulate its cleanup logic.

**File:** `inventory_transactions/models.py`

```python
from django.db import models
from django.utils import timezone

class MaterialReturnNoteManager(models.Manager):
    """Manager for MaterialReturnNote with custom business logic."""
    def get_active_notes(self, company_id=None):
        """Retrieve notes that are currently active (e.g., not yet processed)."""
        # Example business logic: filter notes less than 30 days old
        filter_kwargs = {'return_date__gte': timezone.now().date() - timezone.timedelta(days=30)}
        if company_id:
            filter_kwargs['company_id'] = company_id
        return self.filter(**filter_kwargs).order_by('-return_date')

class MaterialReturnNote(models.Model):
    """
    Represents a finalized Material Return Note.
    Maps to the inferred 'tblinv_MaterialReturn' table.
    """
    objects = MaterialReturnNoteManager() # Attach the custom manager

    company_id = models.IntegerField(
        db_column='CompId',
        verbose_name='Company ID',
        help_text='The ID of the company associated with this return.'
    )
    return_date = models.DateField(
        db_column='ReturnDate',
        verbose_name='Return Date',
        help_text='The date the material was returned.'
    )
    material_name = models.CharField(
        db_column='MaterialName',
        max_length=255,
        verbose_name='Material Name',
        help_text='Name of the material being returned.'
    )
    quantity = models.DecimalField(
        db_column='Quantity',
        max_digits=10,
        decimal_places=2,
        verbose_name='Quantity',
        help_text='The quantity of the material returned.'
    )
    unit = models.CharField(
        db_column='Unit',
        max_length=50,
        blank=True,
        null=True,
        verbose_name='Unit',
        help_text='Unit of measurement (e.g., KG, PCS, Liters).'
    )
    reason = models.TextField(
        db_column='Reason',
        blank=True,
        null=True,
        verbose_name='Reason for Return',
        help_text='Detailed reason for returning the material.'
    )
    returned_by = models.CharField(
        db_column='ReturnedBy',
        max_length=255,
        verbose_name='Returned By',
        help_text='User who initiated the material return.'
    )
    created_at = models.DateTimeField(
        db_column='CreatedAt',
        auto_now_add=True,
        verbose_name='Created At'
    )
    updated_at = models.DateTimeField(
        db_column='UpdatedAt',
        auto_now=True,
        verbose_name='Updated At'
    )

    class Meta:
        managed = False  # Tells Django not to manage table creation/modification
        db_table = 'tblinv_MaterialReturn'  # The actual table name in the database
        verbose_name = 'Material Return Note'
        verbose_name_plural = 'Material Return Notes'
        ordering = ['-return_date', '-created_at'] # Default ordering for display

    def __str__(self):
        return f"MRN #{self.pk} - {self.material_name} ({self.return_date})"

    # Example Business logic methods (Fat Model principle)
    def is_recent(self):
        """Checks if the note was created within the last 7 days."""
        return (timezone.now().date() - self.return_date).days < 7

    def get_formatted_quantity(self):
        """Returns quantity with unit."""
        return f"{self.quantity} {self.unit or ''}".strip()

class MaterialReturnNoteTempManager(models.Manager):
    """Manager for MaterialReturnNoteTemp with cleanup logic."""
    def cleanup_temp_notes(self, company_id, session_id):
        """
        Deletes temporary material return notes for a specific company and session ID.
        This directly translates the ASP.NET Page_Load cleanup logic.
        """
        self.filter(company_id=company_id, session_id=session_id).delete()

class MaterialReturnNoteTemp(models.Model):
    """
    Represents a temporary, in-progress Material Return Note.
    Maps directly to 'tblinv_MaterialReturn_Temp' as found in ASP.NET code.
    """
    objects = MaterialReturnNoteTempManager() # Attach the custom manager

    company_id = models.IntegerField(
        db_column='CompId',
        verbose_name='Company ID',
        help_text='The company ID associated with this temporary record.'
    )
    session_id = models.CharField(
        db_column='SessionId',
        max_length=255,
        verbose_name='Session ID',
        help_text='The user session ID that created this temporary record.'
    )
    temp_return_date = models.DateField(
        db_column='TempReturnDate',
        blank=True,
        null=True,
        verbose_name='Temp Return Date'
    )
    temp_material_name = models.CharField(
        db_column='TempMaterialName',
        max_length=255,
        blank=True,
        null=True,
        verbose_name='Temp Material Name'
    )
    temp_quantity = models.DecimalField(
        db_column='TempQuantity',
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='Temp Quantity'
    )
    temp_remarks = models.TextField(
        db_column='TempRemarks',
        blank=True,
        null=True,
        verbose_name='Temp Remarks'
    )

    class Meta:
        managed = False  # Tells Django not to manage table creation/modification
        db_table = 'tblinv_MaterialReturn_Temp'  # The actual table name in the database
        verbose_name = 'Temporary Material Return Note'
        verbose_name_plural = 'Temporary Material Return Notes'

    def __str__(self):
        return f"Temp MRN for Session: {self.session_id}, Company: {self.company_id}"

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

We'll create a ModelForm for `MaterialReturnNote`. The temporary model (`MaterialReturnNoteTemp`) is typically managed internally and doesn't usually require a user-facing form for direct input.

**File:** `inventory_transactions/forms.py`

```python
from django import forms
from .models import MaterialReturnNote

class MaterialReturnNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialReturnNote
        fields = ['company_id', 'return_date', 'material_name', 'quantity', 'unit', 'reason', 'returned_by']
        widgets = {
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'return_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'step': '0.01', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'unit': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'reason': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'returned_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'company_id': 'Company ID',
            'return_date': 'Return Date',
            'material_name': 'Material Name',
            'quantity': 'Quantity',
            'unit': 'Unit',
            'reason': 'Reason for Return',
            'returned_by': 'Returned By',
        }

    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive value.")
        return quantity

```

### 4.3 Views

Task: Implement CRUD operations using CBVs for `MaterialReturnNote`, and a utility view for `MaterialReturnNoteTemp` cleanup.

## Instructions:

Keep views thin (5-15 lines) and move business logic to models.

**File:** `inventory_transactions/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from .models import MaterialReturnNote, MaterialReturnNoteTemp
from .forms import MaterialReturnNoteForm

# MaterialReturnNote CRUD Views
class MaterialReturnNoteListView(ListView):
    """
    Displays a list of Material Return Notes.
    The actual table content is loaded via HTMX to support DataTables.
    """
    model = MaterialReturnNote
    template_name = 'inventory_transactions/materialreturnnote/list.html'
    context_object_name = 'material_return_notes' # Renamed for clarity

    def get_queryset(self):
        # Example: Filter notes for a specific company or user if applicable
        # For simplicity, returning all or using the custom manager's active notes
        # In a real app, CompId and SessionId would come from request.user or session
        # Simulating CompId = 1 for demonstration if not from authenticated user
        user_company_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        return MaterialReturnNote.objects.get_active_notes(company_id=user_company_id)


class MaterialReturnNoteTablePartialView(ListView):
    """
    Renders the DataTables table content only, for HTMX requests.
    """
    model = MaterialReturnNote
    template_name = 'inventory_transactions/materialreturnnote/_materialreturnnote_table.html'
    context_object_name = 'material_return_notes'

    def get_queryset(self):
        user_company_id = self.request.session.get('compid', 1)
        return MaterialReturnNote.objects.get_active_notes(company_id=user_company_id)

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return super().render_to_response(context, **response_kwargs)
        # If not an HTMX request, redirect to the full list view
        return HttpResponse(status=204, headers={'HX-Location': reverse_lazy('materialreturnnote_list')})

class MaterialReturnNoteCreateView(CreateView):
    """Handles creation of new Material Return Notes."""
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'inventory_transactions/materialreturnnote/_materialreturnnote_form.html' # Partial for modal
    success_url = reverse_lazy('materialreturnnote_list') # Redundant for HTMX but good practice

    def get_initial(self):
        initial = super().get_initial()
        # Pre-fill returned_by with username from session (mimics ASP.NET Session["username"])
        initial['returned_by'] = self.request.session.get('username', 'Anonymous')
        initial['company_id'] = self.request.session.get('compid', 1) # Default to 1
        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Note added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response to signal success and trigger refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnNoteList'
                }
            )
        return response # Fallback for non-HTMX
    
    def form_invalid(self, form):
        # Render the form again with errors for HTMX modal
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, self.request))
        return super().form_invalid(form)


class MaterialReturnNoteUpdateView(UpdateView):
    """Handles updating existing Material Return Notes."""
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'inventory_transactions/materialreturnnote/_materialreturnnote_form.html' # Partial for modal
    success_url = reverse_lazy('materialreturnnote_list') # Redundant for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnNoteList'
                }
            )
        return response
    
    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, self.request))
        return super().form_invalid(form)


class MaterialReturnNoteDeleteView(DeleteView):
    """Handles deletion of Material Return Notes."""
    model = MaterialReturnNote
    template_name = 'inventory_transactions/materialreturnnote/_materialreturnnote_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('materialreturnnote_list') # Redundant for HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Return Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnNoteList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure the object is available in context for confirmation message
        context['material_return_note'] = self.get_object()
        return context

# Utility View for MaterialReturnNoteTemp Cleanup
class CleanupTempNotesView(View):
    """
    A utility view to demonstrate the cleanup of temporary material return notes.
    In a real application, this might be triggered by an HTMX request on login
    or as part of a periodic background task, NOT on every page load.
    """
    @method_decorator(csrf_exempt) # For demonstration, allow POST without CSRF if needed.
                                 # In production, use @method_decorator(login_required)
    def post(self, request, *args, **kwargs):
        # Simulate getting CompId and SessionId from session, as in ASP.NET
        # In a real Django app, use request.user.profile.company_id etc.
        company_id = request.session.get('compid', 1) # Defaulting for example
        session_id = request.session.session_key or 'no_session' # Use Django's session key

        # Perform the cleanup using the model manager
        MaterialReturnNoteTemp.objects.cleanup_temp_notes(company_id, session_id)
        messages.info(request, f"Temporary notes for session {session_id} and company {company_id} have been cleaned up.")

        # Return a 204 No Content response for HTMX, indicating success
        return HttpResponse(status=204)

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

All templates extend `core/base.html`. They are designed for HTMX partial loading.

**File:** `inventory_transactions/templates/inventory_transactions/materialreturnnote/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Return Notes Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialreturnnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Note
        </button>
    </div>
    
    <div id="materialreturnnoteTable-container"
         hx-trigger="load, refreshMaterialReturnNoteList from:body"
         hx-get="{% url 'materialreturnnote_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Return Notes...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 transition-transform duration-300">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed (e.g., for complex UI state)
    document.addEventListener('alpine:init', () => {
        Alpine.data('materialReturnNoteDashboard', () => ({
            // Example: A simple state variable for modal visibility if not fully HTMX controlled
            isModalOpen: false,
            openModal() {
                this.isModalOpen = true;
                document.getElementById('modal').classList.add('is-active');
            },
            closeModal() {
                this.isModalOpen = false;
                document.getElementById('modal').classList.remove('is-active');
            }
        }));
    });

    // Event listener for HTMX afterSwap to initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'materialreturnnoteTable-container') {
            // Destroy existing DataTable instance if it exists
            if ($.fn.DataTable.isDataTable('#materialreturnnoteTable')) {
                $('#materialreturnnoteTable').DataTable().destroy();
            }
            // Initialize new DataTable instance
            $('#materialreturnnoteTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true,
                "autoWidth": false
            });
        }
    });

    // Optional: Trigger cleanup on initial page load if needed (e.g., via HTMX post)
    // This is to demonstrate the ASP.NET Page_Load cleanup logic.
    // In a real Django app, consider background tasks or logout hooks for this.
    // document.addEventListener('DOMContentLoaded', () => {
    //     htmx.trigger(document.body, 'cleanupTempNotes');
    // });
    // This would require a hidden element like:
    // <div hx-post="{% url 'cleanup_temp_notes' %}" hx-trigger="cleanupTempNotes from:body" hx-swap="none"></div>
</script>
{% endblock %}

```

**File:** `inventory_transactions/templates/inventory_transactions/materialreturnnote/_materialreturnnote_table.html` (Partial for HTMX)

```html
<table id="materialreturnnoteTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Return Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Material Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Returned By</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for note in material_return_notes %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ note.return_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ note.material_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ note.get_formatted_quantity }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ note.returned_by }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'materialreturnnote_edit' note.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'materialreturnnote_delete' note.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No material return notes found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

```

**File:** `inventory_transactions/templates/inventory_transactions/materialreturnnote/_materialreturnnote_form.html` (Partial for HTMX modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {% if form.instance.pk %}Edit{% else %}Add{% endif %} Material Return Note
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out">
                <span id="form-indicator" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save
            </button>
        </div>
    </form>
</div>
```

**File:** `inventory_transactions/templates/inventory_transactions/materialreturnnote/_materialreturnnote_confirm_delete.html` (Partial for HTMX modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Material Return Note for 
        <span class="font-semibold text-blue-700">{{ material_return_note.material_name }}</span> 
        on <span class="font-semibold text-blue-700">{{ material_return_note.return_date|date:"Y-m-d" }}</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'materialreturnnote_delete' material_return_note.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create paths for list, create, update, delete, and the HTMX table partial, plus the cleanup utility.

**File:** `inventory_transactions/urls.py`

```python
from django.urls import path
from .views import (
    MaterialReturnNoteListView, 
    MaterialReturnNoteTablePartialView,
    MaterialReturnNoteCreateView, 
    MaterialReturnNoteUpdateView, 
    MaterialReturnNoteDeleteView,
    CleanupTempNotesView,
)

urlpatterns = [
    path('material-return-notes/', MaterialReturnNoteListView.as_view(), name='materialreturnnote_list'),
    path('material-return-notes/table/', MaterialReturnNoteTablePartialView.as_view(), name='materialreturnnote_table'),
    path('material-return-notes/add/', MaterialReturnNoteCreateView.as_view(), name='materialreturnnote_add'),
    path('material-return-notes/edit/<int:pk>/', MaterialReturnNoteUpdateView.as_view(), name='materialreturnnote_edit'),
    path('material-return-notes/delete/<int:pk>/', MaterialReturnNoteDeleteView.as_view(), name='materialreturnnote_delete'),
    
    # Utility URL for cleaning up temporary notes (mimics ASP.NET Page_Load cleanup)
    # This might be called via an HTMX POST request on dashboard load or via a button.
    path('material-return-notes/cleanup-temp/', CleanupTempNotesView.as_view(), name='cleanup_temp_notes'),
]
```

### 4.6 Tests

Task: Write tests for the models and views.

## Instructions:

Include comprehensive unit tests for model methods and properties.
Add integration tests for all views (list, create, update, delete) and HTMX interactions.

**File:** `inventory_transactions/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import MaterialReturnNote, MaterialReturnNoteTemp
from .forms import MaterialReturnNoteForm

class MaterialReturnNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects used by all test methods
        cls.company_id_1 = 101
        cls.session_id_1 = 'test_session_abc'
        cls.note1 = MaterialReturnNote.objects.create(
            company_id=cls.company_id_1,
            return_date=timezone.now().date(),
            material_name='Test Material A',
            quantity=10.50,
            unit='KG',
            reason='Damaged during transit',
            returned_by='testuser1',
        )
        cls.note2_old = MaterialReturnNote.objects.create(
            company_id=cls.company_id_1,
            return_date=timezone.now().date() - timedelta(days=45), # Older note
            material_name='Old Material B',
            quantity=5.00,
            unit='PCS',
            reason='Expired',
            returned_by='testuser2',
        )
        MaterialReturnNoteTemp.objects.create(
            company_id=cls.company_id_1,
            session_id=cls.session_id_1,
            temp_return_date=timezone.now().date(),
            temp_material_name='Temp Item 1',
            temp_quantity=20.0,
            temp_remarks='Temp record for user 1'
        )
        MaterialReturnNoteTemp.objects.create(
            company_id=cls.company_id_1,
            session_id='another_session_xyz',
            temp_return_date=timezone.now().date(),
            temp_material_name='Temp Item 2',
            temp_quantity=5.0,
            temp_remarks='Temp record for user 2'
        )

    def test_material_return_note_creation(self):
        self.assertEqual(self.note1.material_name, 'Test Material A')
        self.assertEqual(self.note1.quantity, 10.50)
        self.assertTrue(self.note1.created_at)
        self.assertTrue(self.note1.updated_at)
        self.assertAlmostEqual(self.note1.quantity, 10.50) # Use assertAlmostEqual for DecimalFields

    def test_material_name_label(self):
        field_label = self.note1._meta.get_field('material_name').verbose_name
        self.assertEqual(field_label, 'Material Name')

    def test_get_formatted_quantity_method(self):
        self.assertEqual(self.note1.get_formatted_quantity(), '10.50 KG')
        note_no_unit = MaterialReturnNote.objects.create(
            company_id=1, return_date=timezone.now().date(), material_name='No Unit', quantity=10, returned_by='test'
        )
        self.assertEqual(note_no_unit.get_formatted_quantity(), '10.00')

    def test_is_recent_method(self):
        self.assertTrue(self.note1.is_recent())
        self.assertFalse(self.note2_old.is_recent())

    def test_material_return_note_temp_creation(self):
        temp_note = MaterialReturnNoteTemp.objects.get(session_id=self.session_id_1)
        self.assertEqual(temp_note.temp_material_name, 'Temp Item 1')
        self.assertEqual(temp_note.company_id, self.company_id_1)

    def test_cleanup_temp_notes_manager_method(self):
        # Create a temp note to be deleted
        MaterialReturnNoteTemp.objects.create(
            company_id=self.company_id_1,
            session_id='session_to_delete',
            temp_material_name='Delete Me',
            temp_quantity=1.0,
            temp_remarks='Should be gone'
        )
        initial_count = MaterialReturnNoteTemp.objects.filter(session_id='session_to_delete').count()
        self.assertEqual(initial_count, 1)

        MaterialReturnNoteTemp.objects.cleanup_temp_notes(self.company_id_1, 'session_to_delete')
        
        final_count = MaterialReturnNoteTemp.objects.filter(session_id='session_to_delete').count()
        self.assertEqual(final_count, 0)
        # Ensure other session's notes are not affected
        self.assertEqual(MaterialReturnNoteTemp.objects.filter(session_id='another_session_xyz').count(), 1)


class MaterialReturnNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = 101
        cls.test_note = MaterialReturnNote.objects.create(
            company_id=cls.company_id,
            return_date=timezone.now().date(),
            material_name='Test View Material',
            quantity=100.00,
            unit='Units',
            reason='Testing view updates',
            returned_by='viewtester',
        )
    
    def setUp(self):
        # Set up client for each test method
        self.client = Client()
        # Mock session data similar to ASP.NET environment
        session = self.client.session
        session['username'] = 'testuser_session'
        session['compid'] = self.company_id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('materialreturnnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/materialreturnnote/list.html')
        self.assertIn('material_return_notes', response.context)
        self.assertContains(response, 'Material Return Notes Dashboard')
        # Check that the test_note is included in the context's queryset
        self.assertIn(self.test_note, response.context['material_return_notes'])

    def test_table_partial_view_get_htmx(self):
        # Test that HTMX request gets the partial template
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialreturnnote_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/materialreturnnote/_materialreturnnote_table.html')
        self.assertIn('material_return_notes', response.context)
        self.assertContains(response, 'Test View Material')
        
    def test_table_partial_view_get_non_htmx_redirects(self):
        # Test that non-HTMX request redirects to the full list view
        response = self.client.get(reverse('materialreturnnote_table'))
        self.assertEqual(response.status_code, 204) # HX-Location causes 204
        self.assertIn('HX-Location', response.headers)
        self.assertEqual(response.headers['HX-Location'], reverse('materialreturnnote_list'))


    def test_create_view_get(self):
        response = self.client.get(reverse('materialreturnnote_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/materialreturnnote/_materialreturnnote_form.html')
        self.assertTrue('form' in response.context)
        # Check initial values are set from session
        self.assertEqual(response.context['form'].initial['returned_by'], 'testuser_session')
        self.assertEqual(response.context['form'].initial['company_id'], self.company_id)

    def test_create_view_post_valid(self):
        data = {
            'company_id': self.company_id,
            'return_date': '2023-01-15',
            'material_name': 'New Material X',
            'quantity': '10.00',
            'unit': 'Liters',
            'reason': 'Excess stock',
            'returned_by': 'postuser',
        }
        response = self.client.post(reverse('materialreturnnote_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success response (No Content)
        self.assertTrue(MaterialReturnNote.objects.filter(material_name='New Material X').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialReturnNoteList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        data = {
            'company_id': self.company_id,
            'return_date': '2023-01-15',
            'material_name': '', # Invalid: required field
            'quantity': '0', # Invalid: quantity must be positive
            'unit': 'Liters',
            'reason': 'Excess stock',
            'returned_by': 'postuser',
        }
        response = self.client.post(reverse('materialreturnnote_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'inventory_transactions/materialreturnnote/_materialreturnnote_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Quantity must be a positive value.')
        self.assertFalse(MaterialReturnNote.objects.filter(material_name='').exists())


    def test_update_view_get(self):
        response = self.client.get(reverse('materialreturnnote_edit', args=[self.test_note.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/materialreturnnote/_materialreturnnote_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.test_note)
        self.assertContains(response, 'Edit Material Return Note')

    def test_update_view_post_valid(self):
        updated_data = {
            'company_id': self.test_note.company_id,
            'return_date': self.test_note.return_date,
            'material_name': 'Updated Material Name',
            'quantity': '123.45',
            'unit': 'Units',
            'reason': 'Updated reason.',
            'returned_by': self.test_note.returned_by,
        }
        response = self.client.post(reverse('materialreturnnote_edit', args=[self.test_note.pk]), updated_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.test_note.refresh_from_db()
        self.assertEqual(self.test_note.material_name, 'Updated Material Name')
        self.assertEqual(self.test_note.quantity, 123.45)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialReturnNoteList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(reverse('materialreturnnote_delete', args=[self.test_note.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/materialreturnnote/_materialreturnnote_confirm_delete.html')
        self.assertTrue('material_return_note' in response.context)
        self.assertEqual(response.context['material_return_note'], self.test_note)
        self.assertContains(response, f"delete the Material Return Note for {self.test_note.material_name}")

    def test_delete_view_post(self):
        # Create another note to delete as test_note is used for other tests
        note_to_delete = MaterialReturnNote.objects.create(
            company_id=self.company_id,
            return_date=timezone.now().date(),
            material_name='To Be Deleted',
            quantity=50.0,
            returned_by='deleter',
        )
        self.assertTrue(MaterialReturnNote.objects.filter(pk=note_to_delete.pk).exists())
        
        response = self.client.post(reverse('materialreturnnote_delete', args=[note_to_delete.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialReturnNote.objects.filter(pk=note_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialReturnNoteList', response.headers['HX-Trigger'])

    def test_cleanup_temp_notes_view(self):
        # Ensure temporary notes exist for cleanup
        MaterialReturnNoteTemp.objects.create(
            company_id=self.company_id,
            session_id=self.client.session.session_key,
            temp_material_name='Ephemeral Item',
            temp_quantity=1.0,
            temp_remarks='Will be deleted by cleanup view'
        )
        self.assertTrue(MaterialReturnNoteTemp.objects.filter(session_id=self.client.session.session_key).exists())

        response = self.client.post(reverse('cleanup_temp_notes'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content
        self.assertFalse(MaterialReturnNoteTemp.objects.filter(session_id=self.client.session.session_key).exists())
        # Check that a message is added (though not directly returned in 204)
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), f"Temporary notes for session {self.client.session.session_key} and company {self.company_id} have been cleaned up.")

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for dynamic updates:**
    *   The `list.html` uses `hx-get` on `materialreturnnoteTable-container` to load the table content dynamically from `{% url 'materialreturnnote_table' %}`. This ensures DataTables can be re-initialized after content swap.
    *   `hx-trigger="load, refreshMaterialReturnNoteList from:body"` ensures the table loads on page load and refreshes whenever a `refreshMaterialReturnNoteList` custom event is triggered (e.g., after successful CRUD operations).
    *   "Add", "Edit", and "Delete" buttons use `hx-get` to load forms/confirmation dialogs into a modal via `hx-target="#modalContent"`.
    *   Form submissions (`hx-post`) on `_materialreturnnote_form.html` and `_materialreturnnote_confirm_delete.html` swap "none" and trigger `HX-Trigger` to refresh the table.
    *   `hx-indicator` is used to show loading state during form submissions.
*   **Alpine.js for UI state management:**
    *   A basic Alpine.js component `x-data="materialReturnNoteDashboard"` is shown in `list.html` as an example to manage modal visibility, though for simple HTMX modals, CSS classes (`hidden`, `is-active`) with `_` (hyperscript) can often suffice. The `_` commands in `list.html` handle basic modal show/hide.
*   **DataTables for list views:**
    *   The `_materialreturnnote_table.html` partial contains the `<table>` element with `id="materialreturnnoteTable"`.
    *   JavaScript in `list.html` listens for `htmx:afterSwap` event on the table container. When the table content is swapped, it destroys any existing DataTable instance and re-initializes a new one, ensuring proper functionality (pagination, search, sort).
*   **No full page reloads:** All CRUD operations and table refreshes happen via HTMX without full page navigation.
*   **`HX-Trigger` responses:** `CreateView`, `UpdateView`, and `DeleteView` return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshMaterialReturnNoteList'})` upon successful form submission or deletion. This signals to the client-side HTMX to refresh the table container, adhering to the `hx-trigger="... refreshMaterialReturnNoteList from:body"` attribute.

## Final Notes

This modernization plan provides a comprehensive Django solution for the "Material Return Note Dashboard."

*   **Placeholders replaced:** All `[PLACEHOLDER]` values have been replaced with concrete names derived from the ASP.NET analysis and standard Django conventions.
*   **DRY templates:** Templates (`_materialreturnnote_table.html`, `_materialreturnnote_form.html`, `_materialreturnnote_confirm_delete.html`) are partials designed for HTMX, promoting reusability and keeping the main `list.html` clean.
*   **Business logic in models:** Examples like `MaterialReturnNote.objects.get_active_notes()` and `note.is_recent()` demonstrate the "Fat Model" principle, keeping views concise. The `MaterialReturnNoteTempManager.cleanup_temp_notes()` directly encapsulates the ASP.NET Page_Load logic.
*   **Comprehensive tests:** Detailed `TestCase` classes cover model attributes/methods and view interactions, including HTMX-specific response checks.
*   **HTMX/Alpine.js integration:** The solution fully leverages HTMX for dynamic content updates and modal interactions, and DataTables for advanced table features, minimizing custom JavaScript.

This modernized Django application delivers a significantly improved user experience and maintainability compared to the legacy ASP.NET solution, leveraging modern web development best practices.