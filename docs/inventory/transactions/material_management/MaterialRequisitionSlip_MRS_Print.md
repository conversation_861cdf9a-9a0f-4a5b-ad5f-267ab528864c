## ASP.NET to Django Conversion Script: Material Requisition Slip (MRS) Print Module

This document outlines a strategic plan to modernize the existing ASP.NET Material Requisition Slip (MRS) Print module into a robust, scalable Django application. Our approach leverages AI-assisted automation, focusing on systematic conversion to minimize manual coding and accelerate deployment.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination, with server-side processing for efficiency.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

This conversion focuses on a "print" or "view" module, which implies a search/list functionality leading to a detail view. There are no direct Create/Update/Delete operations on this page, only data retrieval and redirection.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we identify two primary database interactions:
1.  **Main Data Retrieval:** The `loadData()` method calls the stored procedure `Sp_MRS_FillGrid`. This procedure returns fields like `Id`, `FinYearId`, `FinYear`, `MRSNo`, `SysDate`, and `GenBy`. This strongly suggests a table storing Material Requisition Slip header information.
    *   **Inferred Table Name:** `TblMRSHeader` (a common naming convention for such a module).
    *   **Inferred Columns:**
        *   `Id` (int, Primary Key)
        *   `FinYearId` (int)
        *   `FinYear` (string/int)
        *   `MRSNo` (string)
        *   `SysDate` (datetime)
        *   `GenBy` (string - likely employee name/ID)
        *   `CompId` (int) - passed as parameter to `Sp_MRS_FillGrid`, implying it's a filtering column.
2.  **Autocomplete Data:** The `GetCompletionList` web method queries `tblHR_OfficeStaff` for employee names.
    *   **Inferred Table Name:** `tblHR_OfficeStaff`
    *   **Inferred Columns:**
        *   `EmpId` (int, Primary Key)
        *   `EmployeeName` (string)
        *   `CompId` (int)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data flows in the ASP.NET code.

**Instructions:**

*   **Read (List & Filter):** The primary function is to display a list of Material Requisition Slips (`GridView1`) based on filters.
    *   Filtering is done by `Employee Name` (`txtEmpName`) or `MRS No` (`txtMrsNo`), controlled by a `DropDownList` (`drpfield`).
    *   The `loadData()` method fetches data using a stored procedure (`Sp_MRS_FillGrid`) with dynamic `WHERE` clauses based on search input.
    *   Pagination is handled server-side (`GridView1_PageIndexChanging`).
*   **Autocomplete Search:** The `GetCompletionList` web method provides suggestions for `Employee Name` as the user types.
*   **Detail View Redirection:** When a user clicks "Select" on a MRS record (`GridView1_RowCommand`), the application redirects to `MaterialRequisitionSlip_MRS_Print_Details.aspx` with specific query parameters (`Id`, `MRSNo`, `FyId`, `Key`, `ModId`, `SubModId`). This is akin to navigating to a detailed view of the selected MRS.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to modern web components.

**Instructions:**

*   **Search/Filter Section:**
    *   `DropDownList ID="drpfield"`: Will be a standard HTML `<select>` element. Its `onselectedindexchanged` and `AutoPostBack` behavior will be replicated using Alpine.js for visibility toggling and HTMX for submitting the search form.
    *   `TextBox ID="txtEmpName"` with `AutoCompleteExtender`: Will be a standard HTML `<input type="text">` combined with HTMX for triggering API requests for autocomplete suggestions, and Alpine.js for managing the autocomplete UI.
    *   `TextBox ID="txtMrsNo"`: A standard HTML `<input type="text">`.
    *   `Button ID="Button1" Text="Search"`: A standard HTML `<button>` which triggers an HTMX request to refresh the data table.
*   **Data Display:**
    *   `GridView ID="GridView1"`: This will be replaced by a `<table>` element, styled with Tailwind CSS, and initialized as a DataTables instance. DataTables will handle client-side display, but for server-side processing of filtering, sorting, and pagination (as indicated by the ASP.NET code), it will make AJAX calls to a Django view.
    *   `LinkButton ID="btnlnk" CommandName="Sel" Text="Select"`: This action will be an HTML `<button>` or `<a>` tag that triggers an HTMX request to a Django view, which then performs the necessary redirection to the detail page.

### Step 4: Generate Django Code

We will create a new Django application, `inventory_mrs`, to house this module.

#### 4.1 Models (`inventory_mrs/models.py`)

**Task:** Create Django models based on the identified database schema.

**Instructions:**
-   We'll define `MaterialRequisitionSlip` and `Employee` models.
-   Both models will have `managed = False` and `db_table` set to map to the existing database tables.
-   We'll add a custom manager for `MaterialRequisitionSlip` to encapsulate the complex filtering logic from `Sp_MRS_FillGrid`.

```python
from django.db import models
from django.db.models import F, Value
from django.db.models.functions import Concat
from django.urls import reverse

class MaterialRequisitionSlipManager(models.Manager):
    """
    Custom manager for MaterialRequisitionSlip to handle complex queries
    like the original Sp_MRS_FillGrid stored procedure.
    """
    def get_filtered_mrs_data(self, company_id, financial_year_id, search_type, search_value):
        """
        Mimics the Sp_MRS_FillGrid stored procedure logic for filtering.
        This method retrieves MRS header data based on company, financial year,
        and search criteria (MRS No or Employee Name).
        """
        qs = self.get_queryset().filter(
            comp_id=company_id,
            fin_year_id=financial_year_id
        )

        if search_type == 'mrs_no' and search_value:
            qs = qs.filter(mrs_no__icontains=search_value)
        elif search_type == 'employee_name' and search_value:
            # Assuming 'GenBy' field stores the employee name or can be joined/resolved
            # For this example, we assume GenBy is the employee's name for search.
            # If GenBy is an ID, a join to Employee model would be required.
            qs = qs.filter(generated_by__icontains=search_value)
        
        # Select specific fields as per original GridView columns
        # Note: 'SysDate' is stored as datetime, but formatted for display.
        # We'll use F() for direct column mapping where needed.
        return qs.values(
            'id', 
            'fin_year_id', 
            'fin_year', 
            'mrs_no', 
            'sys_date', 
            'generated_by'
        ).order_by('-sys_date', '-mrs_no') # Default ordering

class MaterialRequisitionSlip(models.Model):
    """
    Django model for TblMRSHeader, representing Material Requisition Slips.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)  # Primary key as integer field
    fin_year_id = models.IntegerField(db_column='FinYearId')
    fin_year = models.CharField(db_column='FinYear', max_length=50) # Assuming FinYear is string like "2023-24"
    mrs_no = models.CharField(db_column='MRSNo', max_length=100)
    sys_date = models.DateTimeField(db_column='SysDate')
    generated_by = models.CharField(db_column='GenBy', max_length=250) # Assuming this is employee name/ID
    comp_id = models.IntegerField(db_column='CompId', default=0) # Company ID for filtering

    objects = MaterialRequisitionSlipManager() # Attach custom manager

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'TblMRSHeader'  # Map to the existing ASP.NET database table
        verbose_name = 'Material Requisition Slip'
        verbose_name_plural = 'Material Requisition Slips'

    def __str__(self):
        return f"{self.mrs_no} ({self.sys_date.strftime('%Y-%m-%d')})"

    def get_absolute_url(self):
        """
        Returns the URL to the detail view for this MRS, mimicking the ASP.NET redirect.
        """
        # This URL should point to the actual detail page, not just a redirector.
        # Assuming the detail page will be named 'mrs_detail' and expects pk, mrs_no, fy_id
        return reverse('inventory_mrs:mrs_detail_redirect', kwargs={
            'pk': self.pk, 
            'mrs_no': self.mrs_no, 
            'fy_id': self.fin_year_id
        })


class Employee(models.Model):
    """
    Django model for tblHR_OfficeStaff, used for employee autocomplete.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=250)
    comp_id = models.IntegerField(db_column='CompId', default=0)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

```

#### 4.2 Forms (`inventory_mrs/forms.py`)

**Task:** Define a Django form for the search criteria.

**Instructions:**
-   This will be a regular `forms.Form` (not a `ModelForm`) as it's for search parameters, not for directly creating/updating a model instance.
-   Include `search_type`, `search_value`, and potentially hidden fields for company/financial year if they were part of the form.

```python
from django import forms
from .models import Employee

class MaterialRequisitionSlipSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('employee_name', 'Employee Name'),
        ('mrs_no', 'MRS No'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        initial='employee_name'
    )
    
    # Text field for employee name search, will be auto-completed
    employee_name_text = forms.CharField(
        label='Employee Name',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[350px]', # Apply original width and class
            'placeholder': 'Enter Employee Name',
            'hx-get': '/inventory_mrs/autocomplete/employees/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:300ms, search',
            'hx-target': '#autocomplete-results', # Target to display results
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-model': 'employeeNameSearch', # Alpine.js binding
            'x-on:input.debounce.300ms': 'fetchAutocompleteResults()', # Trigger Alpine method
            'x-on:click': 'showAutocomplete = true',
            'x-on:focus': 'showAutocomplete = true',
            'x-on:blur.outside': 'showAutocomplete = false'
        })
    )

    # Hidden field to store selected Employee ID if needed, or directly use employee_name_text
    # For now, search is based on employee_name_text
    
    mrs_no_text = forms.CharField(
        label='MRS No',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter MRS No',
            'x-model': 'mrsNoSearch' # Alpine.js binding
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        employee_name_text = cleaned_data.get('employee_name_text')
        mrs_no_text = cleaned_data.get('mrs_no_text')

        if search_type == 'employee_name' and not employee_name_text:
            pass # No error if blank, means all employees or initial load
        elif search_type == 'mrs_no' and not mrs_no_text:
            pass # No error if blank, means all MRS No or initial load
        
        return cleaned_data
```

#### 4.3 Views (`inventory_mrs/views.py`)

**Task:** Implement views for listing, searching, and handling specific actions.

**Instructions:**
-   `MaterialRequisitionSlipListView`: Renders the initial page with the search form.
-   `MaterialRequisitionSlipTableHTMXView`: An API endpoint for DataTables server-side processing, returning JSON. This will be a simple `View` or `TemplateView` with a `post` method.
-   `EmployeeAutocompleteView`: An API endpoint for the employee name autocomplete, returning JSON.
-   `MaterialRequisitionSlipDetailRedirectView`: Handles the "Select" action, constructing the redirect URL.

```python
import json
from django.views.generic import TemplateView, View
from django.http import JsonResponse, HttpResponseRedirect, HttpResponseBadRequest, HttpResponse
from django.urls import reverse_lazy
from django.template.loader import render_to_string
from django.db.models import F
from django.conf import settings # For accessing session keys if needed
import random
import string

from .models import MaterialRequisitionSlip, Employee
from .forms import MaterialRequisitionSlipSearchForm

# Placeholder for session data (replace with actual session access)
# In a real app, you'd get these from request.session or user profile
def get_session_context(request):
    """Mocks fetching CompId and FinYearId from session."""
    # Assuming these are stored in session after user login/selection
    company_id = request.session.get('compid', 1) # Default to 1 for example
    financial_year_id = request.session.get('finyear', 1) # Default to 1 for example
    return company_id, financial_year_id

class MaterialRequisitionSlipListView(TemplateView):
    """
    Renders the main Material Requisition Slip search and list page.
    """
    template_name = 'inventory_mrs/materialrequisitionslip/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with initial values if any
        context['form'] = MaterialRequisitionSlipSearchForm()
        return context

class MaterialRequisitionSlipTableHTMXView(View):
    """
    Handles DataTables server-side processing requests via HTMX/AJAX.
    Returns JSON data formatted for DataTables.
    """
    def get(self, request, *args, **kwargs):
        # This view primarily serves POST requests from DataTables,
        # but could serve an initial GET if data is pre-loaded without DataTables JS.
        # For server-side processing, DataTables sends POST requests.
        return JsonResponse({"error": "GET not supported for DataTables server-side processing"}, status=405)

    def post(self, request, *args, **kwargs):
        # DataTables sends parameters as form-encoded data or JSON.
        # Assuming form-encoded for simplicity from DataTables POST.
        draw = int(request.POST.get('draw', 1))
        start = int(request.POST.get('start', 0))
        length = int(request.POST.get('length', 10))
        search_value = request.POST.get('search[value]', '')
        order_column_index = int(request.POST.get('order[0][column]', 0))
        order_dir = request.POST.get('order[0][dir]', 'asc')
        
        # Get search type and value from the main search form,
        # which might be passed via HTMX parameters or implicit via form fields.
        search_type_from_form = request.POST.get('search_type', 'employee_name')
        employee_name_text_from_form = request.POST.get('employee_name_text', '')
        mrs_no_text_from_form = request.POST.get('mrs_no_text', '')

        # Determine the actual search value and type to pass to the manager
        actual_search_value = ''
        if search_type_from_form == 'mrs_no':
            actual_search_value = mrs_no_text_from_form
        elif search_type_from_form == 'employee_name':
            actual_search_value = employee_name_text_from_form

        company_id, financial_year_id = get_session_context(request)

        # Retrieve and filter data using the custom manager
        # The manager method applies base filters (CompId, FinYearId) and search
        queryset = MaterialRequisitionSlip.objects.get_filtered_mrs_data(
            company_id, 
            financial_year_id, 
            search_type_from_form, 
            actual_search_value
        )
        
        # Total records before pagination
        records_total = queryset.count()
        
        # Apply ordering based on DataTables request
        column_names = ['id', 'fin_year_id', 'fin_year', 'mrs_no', 'sys_date', 'generated_by'] # Must match DataTables column order
        order_by_column = column_names[order_column_index]
        if order_dir == 'desc':
            order_by_column = '-' + order_by_column
        
        queryset = queryset.order_by(order_by_column)

        # Apply pagination
        paginated_data = queryset[start:start + length]

        # Format data for DataTables
        data = []
        for i, item in enumerate(paginated_data):
            # Format date as it was in ASP.NET GridView (e.g., "MM/DD/YYYY" or "YYYY-MM-DD")
            formatted_date = item['sys_date'].strftime('%d/%m/%Y') if item['sys_date'] else ''
            
            data.append([
                start + i + 1, # SN (Serial Number)
                item['fin_year'],
                item['mrs_no'],
                formatted_date,
                item['generated_by'],
                item['id'], # Hidden but needed for 'Select' command
                item['fin_year_id'] # Hidden but needed for 'Select' command
            ])

        response_data = {
            'draw': draw,
            'recordsTotal': records_total,
            'recordsFiltered': records_total, # Assuming filtering is done by queryset directly
            'data': data,
        }
        return JsonResponse(response_data)


class EmployeeAutocompleteView(View):
    """
    Provides autocomplete suggestions for employee names.
    Returns JSON list of employee names and IDs.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('term', '') # 'term' is common for jQuery UI Autocomplete
        company_id, _ = get_session_context(request) # Get company_id from session

        if not query:
            return JsonResponse([], safe=False)

        # Filter employees by name and company
        employees = Employee.objects.filter(
            employee_name__icontains=query,
            comp_id=company_id
        ).values('emp_id', 'employee_name')[:10] # Limit to 10 suggestions

        results = []
        for emp in employees:
            results.append({
                'label': f"{emp['employee_name']} [{emp['emp_id']}]", # Format as per original
                'value': emp['employee_name'], # Value to put in the text box
                'id': emp['emp_id'] # Actual ID for backend lookup if needed
            })
        return JsonResponse(results, safe=False)

class MaterialRequisitionSlipDetailRedirectView(View):
    """
    Handles the 'Select' action from the MRS list,
    redirecting to the external MaterialRequisitionSlip_MRS_Print_Details.aspx.
    """
    def get(self, request, pk, mrs_no, fy_id, *args, **kwargs):
        # Generate random key as per original ASP.NET code
        get_random_key = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10)) # Example length 10

        # Construct the target URL.
        # This assumes the ASP.NET details page still exists and is accessible.
        # In a full migration, this would be a Django DetailView.
        # For modernization, we'd aim to convert MaterialRequisitionSlip_MRS_Print_Details.aspx too.
        # Hardcoding old ASP.NET URL for demonstration purposes.
        target_url = f"/Module_Inventory_Transactions_MaterialRequisitionSlip_MRS_Print_Details.aspx?Id={pk}&MRSNo={mrs_no}&FyId={fy_id}&Key={get_random_key}&ModId=9&SubModId=40"
        
        # Perform the redirect
        return HttpResponseRedirect(target_url)

```

#### 4.4 Templates (`inventory_mrs/templates/inventory_mrs/materialrequisitionslip/`)

**Task:** Create templates for the list view and the partial table.

**Instructions:**
-   `list.html`: The main page, including the search form and a container for the DataTables. HTMX will load the table into this container. Alpine.js will manage the visibility of search fields.
-   `_table.html`: A partial template containing the DataTables structure, loaded dynamically by HTMX.

##### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Material Requisition Slip [MRS] - Print</h2>
        
        <form id="mrs-search-form" 
              hx-post="{% url 'inventory_mrs:mrs_table_data' %}"
              hx-trigger="submit, drpFieldChange from:body"
              hx-target="#mrsTable-container"
              hx-swap="innerHTML"
              class="space-y-4"
              x-data="{ 
                searchType: '{{ form.search_type.value|default:'employee_name' }}', 
                employeeNameSearch: '{{ form.employee_name_text.value|default:'' }}', 
                mrsNoSearch: '{{ form.mrs_no_text.value|default:'' }}',
                showAutocomplete: false,
                autocompleteResults: [],
                selectAutocomplete: function(label, value, id) {
                    this.employeeNameSearch = value;
                    this.showAutocomplete = false;
                    // Optionally set a hidden field for employee ID if needed
                },
                fetchAutocompleteResults: async function() {
                    if (this.employeeNameSearch.length < 1) {
                        this.autocompleteResults = [];
                        return;
                    }
                    try {
                        const response = await fetch(`{% url 'inventory_mrs:employee_autocomplete' %}?term=${encodeURIComponent(this.employeeNameSearch)}`);
                        this.autocompleteResults = await response.json();
                        this.showAutocomplete = true;
                    } catch (error) {
                        console.error('Error fetching autocomplete results:', error);
                    }
                }
              }">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <label for="{{ form.search_type.id_for_label }}" class="sr-only">Search By:</label>
                    <select id="{{ form.search_type.id_for_label }}" 
                            name="{{ form.search_type.html_name }}" 
                            class="{{ form.search_type.field.widget.attrs.class }} p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            x-model="searchType"
                            hx-trigger="change"
                            hx-post="{% url 'inventory_mrs:mrs_table_data' %}"
                            hx-target="#mrsTable-container"
                            hx-swap="innerHTML">
                        {% for value, label in form.search_type.field.choices %}
                            <option value="{{ value }}" {% if value == form.search_type.value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex-grow relative">
                    <div x-show="searchType === 'employee_name'" x-cloak>
                        <label for="{{ form.employee_name_text.id_for_label }}" class="sr-only">{{ form.employee_name_text.label }}</label>
                        <input type="text" id="{{ form.employee_name_text.id_for_label }}" 
                               name="{{ form.employee_name_text.html_name }}" 
                               class="{{ form.employee_name_text.field.widget.attrs.class }} p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                               x-model="employeeNameSearch"
                               x-on:keyup.debounce.300ms="fetchAutocompleteResults()"
                               placeholder="Enter Employee Name">
                        
                        <div x-show="showAutocomplete && autocompleteResults.length > 0" 
                             class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto"
                             x-cloak>
                            <template x-for="result in autocompleteResults" :key="result.id">
                                <div x-text="result.label" 
                                     @click="selectAutocomplete(result.label, result.value, result.id)"
                                     class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-800"></div>
                            </template>
                        </div>
                    </div>
                    
                    <div x-show="searchType === 'mrs_no'" x-cloak>
                        <label for="{{ form.mrs_no_text.id_for_label }}" class="sr-only">{{ form.mrs_no_text.label }}</label>
                        <input type="text" id="{{ form.mrs_no_text.id_for_label }}" 
                               name="{{ form.mrs_no_text.html_name }}" 
                               class="{{ form.mrs_no_text.field.widget.attrs.class }} p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                               x-model="mrsNoSearch"
                               placeholder="Enter MRS No">
                    </div>
                </div>
                
                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- DataTables container -->
    <div id="mrsTable-container"
         hx-trigger="load delay:100ms"
         hx-post="{% url 'inventory_mrs:mrs_table_data' %}"
         hx-swap="innerHTML">
        <!-- Loading spinner while content loads -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Requisition Slips...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script type="text/javascript" src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>

<script>
    // Initialize Alpine.js for interactive elements
    document.addEventListener('alpine:init', () => {
        Alpine.data('mrsPrint', () => ({
            // This component handles the search form and autocomplete interaction
        }));
    });

    // Function to initialize DataTables after HTMX loads content
    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#mrsTable')) {
            $('#mrsTable').DataTable().destroy();
        }
        $('#mrsTable').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "{% url 'inventory_mrs:mrs_table_data' %}",
                "type": "POST",
                "data": function (d) {
                    // Include CSRF token
                    d.csrfmiddlewaretoken = $('input[name="csrfmiddlewaretoken"]').val();
                    // Include search parameters from the form
                    d.search_type = $('[name="search_type"]').val();
                    d.employee_name_text = $('[name="employee_name_text"]').val();
                    d.mrs_no_text = $('[name="mrs_no_text"]').val();
                    return d;
                }
            },
            "columns": [
                { "data": 0, "orderable": false }, // SN
                { "data": 1 }, // Fin Year
                { "data": 2 }, // MRS No
                { "data": 3 }, // Date
                { "data": 4 }, // Gen By
                { "data": null, "orderable": false, "render": function (data, type, row) {
                    // Data is an array, row[5] is Id, row[6] is FinYearId, row[2] is MRSNo
                    // The 'Select' button redirects using HTMX to the Django redirector view
                    return `<button class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded" 
                                    hx-get="/inventory_mrs/${row[5]}/${row[2]}/${row[6]}/redirect/" 
                                    hx-swap="outerHTML" 
                                    hx-indicator="#mrsTable-loading-indicator"
                                    hx-confirm="Are you sure you want to view details for MRS No. ${row[2]}?">
                                    Select
                            </button>`;
                }},
            ],
            "pagingType": "full_numbers",
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "emptyTable": "No data to display !",
                "loadingRecords": "Loading...",
                "processing": "<div class='inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500'></div> Processing..."
            }
        });
    }

    // HTMX loads data. Once content is loaded, initialize DataTable.
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.target.id === 'mrsTable-container') {
            initializeDataTable();
        }
    });

    // Initial load of the table data
    document.addEventListener('DOMContentLoaded', function() {
        // HTMX will trigger the initial load via hx-trigger="load" on the container
        // No explicit JS call needed here for the first load.
    });

</script>
{% endblock %}
```

##### `_table.html` (Partial for DataTables)

```html
<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    <div id="mrsTable-loading-indicator" class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-20" style="display:none;">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="ml-2 text-gray-700">Loading data...</p>
    </div>
    <table id="mrsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            <!-- DataTables will populate this tbody dynamically via AJAX -->
        </tbody>
    </table>
</div>

```

#### 4.5 URLs (`inventory_mrs/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
-   Set up paths for the main list page, the DataTables AJAX endpoint, the autocomplete endpoint, and the detail redirect.

```python
from django.urls import path
from .views import (
    MaterialRequisitionSlipListView,
    MaterialRequisitionSlipTableHTMXView,
    EmployeeAutocompleteView,
    MaterialRequisitionSlipDetailRedirectView
)

app_name = 'inventory_mrs' # Namespace for URLs

urlpatterns = [
    path('mrs-print/', MaterialRequisitionSlipListView.as_view(), name='mrs_list'),
    path('mrs-print/table-data/', MaterialRequisitionSlipTableHTMXView.as_view(), name='mrs_table_data'),
    path('autocomplete/employees/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    # URL for redirecting to the external ASP.NET detail page
    path('<int:pk>/<str:mrs_no>/<int:fy_id>/redirect/', MaterialRequisitionSlipDetailRedirectView.as_view(), name='mrs_detail_redirect'),
]
```

#### 4.6 Tests (`inventory_mrs/tests.py`)

**Task:** Write tests for the models and views to ensure functionality and coverage.

**Instructions:**
-   Include unit tests for model properties and manager methods.
-   Add integration tests for all views, specifically checking HTMX interactions and server-side DataTables responses.
-   Mock `request.session` for `CompId` and `FinYearId`.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
import json
from datetime import datetime

from .models import MaterialRequisitionSlip, Employee

class MaterialRequisitionSlipModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        MaterialRequisitionSlip.objects.create(
            id=1,
            fin_year_id=1,
            fin_year='2023-24',
            mrs_no='MRS-001',
            sys_date=datetime(2023, 1, 10, 10, 0, 0),
            generated_by='John Doe',
            comp_id=1
        )
        MaterialRequisitionSlip.objects.create(
            id=2,
            fin_year_id=1,
            fin_year='2023-24',
            mrs_no='MRS-002',
            sys_date=datetime(2023, 1, 11, 11, 0, 0),
            generated_by='Jane Smith',
            comp_id=1
        )
        MaterialRequisitionSlip.objects.create(
            id=3,
            fin_year_id=2,
            fin_year='2024-25',
            mrs_no='MRS-003',
            sys_date=datetime(2024, 2, 1, 9, 0, 0),
            generated_by='John Doe',
            comp_id=1
        )
        Employee.objects.create(
            emp_id=101,
            employee_name='John Doe',
            comp_id=1
        )
        Employee.objects.create(
            emp_id=102,
            employee_name='Jane Smith',
            comp_id=1
        )

    def test_mrs_creation(self):
        mrs = MaterialRequisitionSlip.objects.get(id=1)
        self.assertEqual(mrs.mrs_no, 'MRS-001')
        self.assertEqual(mrs.generated_by, 'John Doe')
        self.assertEqual(mrs.sys_date.year, 2023)

    def test_employee_creation(self):
        employee = Employee.objects.get(emp_id=101)
        self.assertEqual(employee.employee_name, 'John Doe')

    def test_mrs_manager_filtering_no_search(self):
        # Test filtering with company and financial year, no specific search
        queryset = MaterialRequisitionSlip.objects.get_filtered_mrs_data(1, 1, '', '')
        self.assertEqual(queryset.count(), 2)
        self.assertIn('MRS-001', [item['mrs_no'] for item in queryset])
        self.assertIn('MRS-002', [item['mrs_no'] for item in queryset])

    def test_mrs_manager_filtering_by_mrs_no(self):
        queryset = MaterialRequisitionSlip.objects.get_filtered_mrs_data(1, 1, 'mrs_no', '001')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['mrs_no'], 'MRS-001')

    def test_mrs_manager_filtering_by_employee_name(self):
        queryset = MaterialRequisitionSlip.objects.get_filtered_mrs_data(1, 1, 'employee_name', 'John Doe')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['mrs_no'], 'MRS-001') # In current data, only MRS-001 by John Doe for FinYear 1

    def test_mrs_manager_filtering_by_employee_name_case_insensitive(self):
        queryset = MaterialRequisitionSlip.objects.get_filtered_mrs_data(1, 1, 'employee_name', 'john doe')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['mrs_no'], 'MRS-001')

    def test_mrs_manager_filtering_by_company_and_fin_year(self):
        queryset = MaterialRequisitionSlip.objects.get_filtered_mrs_data(1, 2, '', '')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['mrs_no'], 'MRS-003')

class MaterialRequisitionSlipViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        MaterialRequisitionSlip.objects.create(
            id=1, fin_year_id=1, fin_year='2023-24', mrs_no='MRS-001',
            sys_date=datetime(2023, 1, 10, 10, 0, 0), generated_by='Test Employee A', comp_id=1
        )
        MaterialRequisitionSlip.objects.create(
            id=2, fin_year_id=1, fin_year='2023-24', mrs_no='MRS-002',
            sys_date=datetime(2023, 1, 11, 11, 0, 0), generated_by='Test Employee B', comp_id=1
        )
        Employee.objects.create(emp_id=101, employee_name='Test Employee A', comp_id=1)
        Employee.objects.create(emp_id=102, employee_name='Test Employee B', comp_id=1)
        
    def setUp(self):
        self.client = Client()
        # Mock session data for company and financial year
        self.session_patch = patch('inventory_mrs.views.get_session_context')
        self.mock_get_session_context = self.session_patch.start()
        self.mock_get_session_context.return_value = (1, 1) # comp_id=1, fin_year_id=1

    def tearDown(self):
        self.session_patch.stop()

    def test_mrs_list_view_get(self):
        response = self.client.get(reverse('inventory_mrs:mrs_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_mrs/materialrequisitionslip/list.html')
        self.assertContains(response, 'Material Requisition Slip [MRS] - Print')
        self.assertIsInstance(response.context['form'], MaterialRequisitionSlipSearchForm)

    def test_mrs_table_htmx_view_post_initial_load(self):
        response = self.client.post(
            reverse('inventory_mrs:mrs_table_data'),
            {
                'draw': 1, 'start': 0, 'length': 10,
                'search[value]': '', 'order[0][column]': 0, 'order[0][dir]': 'asc',
                'search_type': 'employee_name', 'employee_name_text': '', 'mrs_no_text': '',
                'csrfmiddlewaretoken': self.client.cookies['csrftoken'].value # Must provide CSRF token
            },
            HTTP_X_REQUESTED_WITH='XMLHttpRequest', # Indicate AJAX request for DataTables
            content_type='application/x-www-form-urlencoded' # Ensure proper content type
        )
        self.assertEqual(response.status_code, 200)
        json_response = json.loads(response.content)
        self.assertEqual(json_response['recordsTotal'], 2)
        self.assertEqual(json_response['recordsFiltered'], 2)
        self.assertEqual(len(json_response['data']), 2)
        self.assertEqual(json_response['data'][0][2], 'MRS-001') # MRS No. of first record
        self.assertEqual(json_response['data'][1][2], 'MRS-002') # MRS No. of second record

    def test_mrs_table_htmx_view_post_filter_by_mrs_no(self):
        response = self.client.post(
            reverse('inventory_mrs:mrs_table_data'),
            {
                'draw': 1, 'start': 0, 'length': 10,
                'search[value]': '', 'order[0][column]': 0, 'order[0][dir]': 'asc',
                'search_type': 'mrs_no', 'employee_name_text': '', 'mrs_no_text': '001',
                'csrfmiddlewaretoken': self.client.cookies['csrftoken'].value
            },
            HTTP_X_REQUESTED_WITH='XMLHttpRequest',
            content_type='application/x-www-form-urlencoded'
        )
        self.assertEqual(response.status_code, 200)
        json_response = json.loads(response.content)
        self.assertEqual(json_response['recordsTotal'], 1)
        self.assertEqual(json_response['data'][0][2], 'MRS-001')

    def test_mrs_table_htmx_view_post_filter_by_employee_name(self):
        response = self.client.post(
            reverse('inventory_mrs:mrs_table_data'),
            {
                'draw': 1, 'start': 0, 'length': 10,
                'search[value]': '', 'order[0][column]': 0, 'order[0][dir]': 'asc',
                'search_type': 'employee_name', 'employee_name_text': 'Employee A', 'mrs_no_text': '',
                'csrfmiddlewaretoken': self.client.cookies['csrftoken'].value
            },
            HTTP_X_REQUESTED_WITH='XMLHttpRequest',
            content_type='application/x-www-form-urlencoded'
        )
        self.assertEqual(response.status_code, 200)
        json_response = json.loads(response.content)
        self.assertEqual(json_response['recordsTotal'], 1)
        self.assertEqual(json_response['data'][0][2], 'MRS-001') # Assuming 'Test Employee A' only for MRS-001

    def test_employee_autocomplete_view(self):
        response = self.client.get(
            reverse('inventory_mrs:employee_autocomplete'),
            {'term': 'Test'},
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        self.assertEqual(response.status_code, 200)
        json_response = json.loads(response.content)
        self.assertEqual(len(json_response), 2)
        self.assertEqual(json_response[0]['label'], 'Test Employee A [101]')
        self.assertEqual(json_response[1]['label'], 'Test Employee B [102]')

    def test_material_requisition_slip_detail_redirect_view(self):
        mrs_id = 1
        mrs_no = 'MRS-001'
        fy_id = 1
        response = self.client.get(
            reverse('inventory_mrs:mrs_detail_redirect', kwargs={'pk': mrs_id, 'mrs_no': mrs_no, 'fy_id': fy_id})
        )
        self.assertEqual(response.status_code, 302) # Expecting a redirect
        self.assertIn("Module_Inventory_Transactions_MaterialRequisitionSlip_MRS_Print_Details.aspx", response.url)
        self.assertIn(f"Id={mrs_id}", response.url)
        self.assertIn(f"MRSNo={mrs_no}", response.url)
        self.assertIn(f"FyId={fy_id}", response.url)
        self.assertIn("ModId=9", response.url)
        self.assertIn("SubModId=40", response.url)
        self.assertIn("Key=", response.url) # Key should be present

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Search & Table Refresh:**
    *   The main search form (`#mrs-search-form`) uses `hx-post` to submit search criteria to `{% url 'inventory_mrs:mrs_table_data' %}`.
    *   `hx-trigger="submit, drpFieldChange from:body"` ensures the form submits on button click and when the dropdown changes (Alpine.js will dispatch a custom event `drpFieldChange`).
    *   `hx-target="#mrsTable-container"` and `hx-swap="innerHTML"` ensure only the table content is updated, not the entire page.
    *   DataTables AJAX configuration uses `hx-post` for server-side processing, ensuring search, sort, and pagination also trigger HTMX requests.
    *   The "Select" button in the table directly uses `hx-get` to trigger the redirector view.
*   **Alpine.js for UI State Management:**
    *   `x-data` on the form manages `searchType` to dynamically show/hide `txtEmpName` or `txtMrsNo` input fields based on dropdown selection (`x-show`).
    *   Alpine.js handles the autocomplete results display (`x-show`, `x-for`) and selection logic.
    *   `x-model` binds input values to Alpine.js data properties.
*   **DataTables for List Views:**
    *   The `_table.html` partial serves as the target for HTMX.
    *   The JavaScript within `list.html` initializes DataTables with `serverSide: true` and `ajax.url` pointing to `{% url 'inventory_mrs:mrs_table_data' %}`.
    *   DataTables `ajax.data` function collects current form inputs (including the CSRF token) and sends them with each DataTables AJAX request (for search, sort, paginate).
    *   The "Actions" column in DataTables is custom rendered to include HTMX-enabled "Select" buttons.
*   **No Full Page Reloads:** All user interactions (search, filter, pagination, select) are handled via HTMX, providing a seamless single-page application feel without heavy JavaScript frameworks.
*   **DRY Template Inheritance:** `list.html` extends `core/base.html`, ensuring consistent header, footer, and CDN links (jQuery, DataTables, Alpine.js, HTMX, Tailwind CSS) are inherited without duplication.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for modernizing the Material Requisition Slip Print module from ASP.NET to Django. By strictly adhering to the "Fat Model, Thin View" principle, leveraging HTMX and Alpine.js for dynamic frontend interactions, and implementing DataTables for efficient data presentation, the new Django application will be:

*   **Performant:** Server-side DataTables processing minimizes data transfer and ensures quick responses for large datasets.
*   **Maintainable:** Clean separation of concerns (models for business logic, thin views, component-based templates) simplifies future development and debugging.
*   **User-Friendly:** HTMX and Alpine.js provide a smooth, responsive user experience without full page reloads.
*   **Scalable:** Django's robust architecture supports growth and integration with other systems.
*   **Testable:** Comprehensive unit and integration tests ensure code quality and prevent regressions, fostering confidence in the migration.

This approach minimizes manual coding through systematic pattern conversion, accelerating the modernization process and delivering tangible business value.