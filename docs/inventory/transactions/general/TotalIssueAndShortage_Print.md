This modernization plan details the transformation of an ASP.NET application, specifically a Crystal Reports print page, into a modern Django-based solution. Our focus is on leveraging AI-assisted automation to streamline the migration process, ensuring a clean, efficient, and maintainable Django application that delivers significant business value.

The original ASP.NET page `TotalIssueAndShortage_Print.aspx` with its C# code-behind is primarily a report generation and display utility. It dynamically fetches data from multiple database tables, performs complex calculations, and renders a Crystal Report. Our Django modernization reimagines this as a dynamic, interactive web report using HTMX and DataTables, with all business logic strictly confined to Django models.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The C# code heavily interacts with several tables to compile the "Total Issue and Shortage" report. While the report itself is a derived dataset, the underlying data originates from these tables. We infer the primary tables and their approximate columns from the SQL queries:

-   **`tblDG_BOM_Master`**: Represents Bill of Materials data. Columns inferred: `CId` (Child ID), `PId` (Parent ID), `WONo` (Work Order Number), `CompId` (Company ID), `ItemId`.
-   **`tblDG_Item_Master`**: Item master data. Columns inferred: `Id` (Item ID), `ManfDesc` (Manufacturer Description), `UOMBasic` (Unit of Measure Basic).
-   **`Unit_Master`**: Unit of Measure definitions. Columns inferred: `Id` (Unit ID), `Symbol` (Unit Symbol).
-   **`tblInv_WIS_Details`**: Inventory Work In Progress (WIP) Issue Details. Columns inferred: `IssuedQty` (Issued Quantity), `MId` (Master ID), `PId`, `CId`.
-   **`tblInv_WIS_Master`**: Inventory WIP Issue Master. Columns inferred: `Id` (Master ID), `WONO` (Work Order Number), `CompId` (Company ID), `FinYearId` (Financial Year ID).
-   **`SD_Cust_WorkOrder_Master`**: Customer Work Order Master. Columns inferred: `WONo` (Work Order Number), `CompId` (Company ID), `TaskProjectLeader`, `TaskTargetTryOut_FDate`, `TaskTargetTryOut_TDate`, `TaskTargetDespach_FDate`, `TaskTargetDespach_TDate`.
-   **`tblMM_Rate_Register`**: Material Management Rate Register. Columns inferred: `CompId` (Company ID), `ItemId` (Item ID), `Rate`, `Discount`.

**Derived Report Columns (for `TotalIssueShortageEntry`):**
The final report, represented by `dt` in the C# code, contains the following columns: `ItemCode`, `ManfDesc`, `UOMBasic`, `BOMQty`, `IssuedQty`, `ShortageQty`, `AC`, `Rate`, `Amount`. This will be our `TotalIssueShortageEntry` model.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and data processing logic.

**Instructions:**
The ASP.NET page `TotalIssueAndShortage_Print.aspx` is exclusively a **Read** (report display) operation. There are no direct Create, Update, or Delete operations on the report itself. The page:

-   **Reads:** Gathers parameters (`WONo`, `CompId`, `FinYearId`, `Status`) from query strings and session. It then executes complex, recursive SQL queries (via `fun.select` and `getPrintnode`) to fetch and calculate `BOMQty`, `IssuedQty`, `ShortageQty`, `Rate`, and `Amount` by joining multiple tables (`tblDG_BOM_Master`, `tblDG_Item_Master`, `Unit_Master`, `tblInv_WIS_Details`, `tblInv_WIS_Master`, `SD_Cust_WorkOrder_Master`, `tblMM_Rate_Register`).
-   **Calculates:** Performs aggregations (e.g., `sum(IssuedQty)`), quantity calculations (`BOMRecurQty`), and rate/amount calculations.
-   **Presents:** Populates a `DataTable` and binds it to a Crystal Report Viewer.
-   **Navigation:** The "Cancel" button (`Button1_Click`) handles redirection based on a `Status` parameter.

For the Django migration, the core challenge is encapsulating this complex data retrieval and calculation logic into Django models and custom managers, following the "Fat Model" principle. Although this particular page is only for "Read" (reporting), we will provide the boilerplate for Create, Update, Delete for a hypothetical `TotalIssueShortageEntry` to adhere to the prompt's template requirements, clarifying that in a real-world scenario, CRUD would apply to the *underlying* data records (BOM, Inventory) rather than the derived report entries.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django template mapping.

**Instructions:**
The ASP.NET page is quite minimalistic in terms of interactive UI components, focusing on report rendering:

-   **`CR:CrystalReportViewer`**: This is the main display component for the generated report. In Django, this will be replaced by a standard HTML `<table>` element, dynamically enhanced with DataTables for interactivity.
-   **`asp:Panel` (`Panel1`)**: A container for the report viewer, often used for layout and scrollability. In Django, this will be a `<div>` with appropriate Tailwind CSS classes for styling and potentially `overflow-y-auto` for scrollability.
-   **`asp:Button` (`Button2`)**: A "Cancel" button. In Django, this will be a standard HTML `<button>` element, possibly with HTMX attributes for dynamic interaction or a simple `href` for redirection.

There are no complex client-side JavaScript interactions explicitly detailed beyond a `loadingNotifier.js`, which can be handled by HTMX's built-in loading indicators. Alpine.js will manage simple UI state for modals (add/edit/delete forms) as required by the template.

### Step 4: Generate Django Code

We will create a Django app named `inventory_reports`.

#### 4.1 Models (`inventory_reports/models.py`)

**Task:** Create Django models based on the database schema and a derived report model.

**Instructions:**
We define `managed = False` for all models as they map to existing legacy database tables. For `TotalIssueShortageEntry`, which represents a derived report row, we also set `managed = False` but omit `db_table`, indicating it's not directly mapped to a persistent table. Its fields correspond to the output columns of the report. A custom manager `TotalIssueShortageEntryManager` will contain the complex report generation logic.

```python
from django.db import models
from django.db.models import Sum, F, ExpressionWrapper, fields
from django.db.models.functions import Coalesce
from django.core.exceptions import ObjectDoesNotExist

# Placeholder for utility functions from clsFunctions
# In a real migration, these would be implemented as proper Django ORM queries
# or integrated directly into model managers.
class ClsFunctionsStub:
    """
    A stub for ASP.NET's clsFunctions. In a real migration,
    these methods would be re-implemented using Django ORM
    or by querying the legacy database directly if not migrated.
    """
    def Connection(self):
        # Django manages connections, so this is illustrative
        return "Not applicable in Django ORM context"

    def select(self, columns, table, where_clause):
        # This is simplified; real implementation would use Django ORM or raw SQL
        print(f"DEBUG: Simulating SELECT {columns} FROM {table} WHERE {where_clause}")
        # In a real app, you'd translate this to ORM calls
        pass

    def getCompany(self, comp_id):
        # Example: Fetch company name from Company model
        try:
            return Company.objects.get(id=comp_id).company_name
        except ObjectDoesNotExist:
            return f"Company {comp_id}"

    def CompAdd(self, comp_id):
        # Example: Fetch company address
        try:
            return Company.objects.get(id=comp_id).address
        except ObjectDoesNotExist:
            return f"Address for {comp_id}"

    def getProjectTitle(self, wono):
        # Example: Fetch project title from WorkOrderMaster
        try:
            return WorkOrderMaster.objects.get(wono=wono).project_title # Assuming project_title exists
        except ObjectDoesNotExist:
            return f"Project Title for WO: {wono}"

    def FromDateDMY(self, date_str):
        # Converts date string to DD-MM-YYYY format
        if not date_str:
            return ""
        try:
            # Assuming date_str is in a format parseable by datetime
            from datetime import datetime
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S').strftime('%d-%m-%Y')
        except ValueError:
            return date_str # Return as is if parsing fails

    def GetItemCode_PartNo(self, comp_id, item_id):
        # Example: Fetch item code from ItemMaster
        try:
            return ItemMaster.objects.get(id=item_id, comp_id=comp_id).item_code # Assuming item_code exists
        except ObjectDoesNotExist:
            return f"ItemCode {item_id}"

    # This is a highly complex recursive function from ASP.NET.
    # Its precise logic needs detailed re-engineering in Django.
    # This stub provides a conceptual implementation.
    def BOMRecurQty(self, wono, p_id, c_id, multiplier, comp_id, fin_year_id):
        """
        Simulates recursive BOM quantity calculation.
        This is a complex business logic function that needs careful re-implementation.
        For demonstration, it returns a placeholder value.
        """
        # In a real scenario, this would involve complex ORM queries
        # potentially using Common Table Expressions (CTEs) or recursive queries
        # if the database supports it, or iterative Python logic.
        print(f"DEBUG: Calculating BOMRecurQty for WONo={wono}, PId={p_id}, CId={c_id}")
        # Placeholder for actual calculation
        return 1.0 # Or based on a simplified lookup for testing

# Instantiate the stub
fun = ClsFunctionsStub()

# --- Underlying Database Models (Managed=False) ---

class Company(models.Model):
    id = models.IntegerField(primary_key=True, db_column='CId') # Assuming CId is primary key
    company_name = models.CharField(max_length=255, db_column='CompanyName') # Inferred
    address = models.CharField(max_length=500, db_column='Address') # Inferred

    class Meta:
        managed = False
        db_table = 'CompanyMaster' # Placeholder table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

class UnitMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

class ItemMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    manf_desc = models.CharField(max_length=255, db_column='ManfDesc')
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items', null=True)
    item_code = models.CharField(max_length=100, db_column='ItemCode') # Inferred from fun.GetItemCode_PartNo
    comp_id = models.IntegerField(db_column='CompId') # Inferred

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

class BOMMaster(models.Model):
    # Assuming a composite primary key or a unique ID column in the original table
    # For managed=False, Django doesn't enforce primary key constraints for the table
    # but having 'id' helps with ORM operations.
    id = models.AutoField(primary_key=True) # Assuming an auto-incrementing ID for Django's use
    parent_id = models.IntegerField(db_column='PId')
    child_id = models.IntegerField(db_column='CId')
    wo_no = models.CharField(max_length=100, db_column='WONo')
    comp_id = models.IntegerField(db_column='CompId')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Entry'
        verbose_name_plural = 'BOM Entries'
        # Consider unique_together if (parent_id, child_id, wo_no, comp_id) is unique

class WISMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    wo_no = models.CharField(max_length=100, db_column='WONO')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Master'
        verbose_name = 'WIS Master'
        verbose_name_plural = 'WIS Masters'

class WISDetail(models.Model):
    id = models.AutoField(primary_key=True) # Assuming an ID for Django's use
    master = models.ForeignKey(WISMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', null=True)
    parent_id = models.IntegerField(db_column='PId')
    child_id = models.IntegerField(db_column='CId')
    issued_qty = models.FloatField(db_column='IssuedQty')

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Details'
        verbose_name = 'WIS Detail'
        verbose_name_plural = 'WIS Details'

class WorkOrderMaster(models.Model):
    id = models.AutoField(primary_key=True) # Assuming an ID for Django's use
    wo_no = models.CharField(max_length=100, db_column='WONo')
    comp_id = models.IntegerField(db_column='CompId')
    task_project_leader = models.CharField(max_length=255, db_column='TaskProjectLeader')
    task_target_try_out_fdate = models.DateTimeField(db_column='TaskTargetTryOut_FDate', null=True, blank=True)
    task_target_try_out_tdate = models.DateTimeField(db_column='TaskTargetTryOut_TDate', null=True, blank=True)
    task_target_despach_fdate = models.DateTimeField(db_column='TaskTargetDespach_FDate', null=True, blank=True)
    task_target_despach_tdate = models.DateTimeField(db_column='TaskTargetDespach_TDate', null=True, blank=True)
    project_title = models.CharField(max_length=255, db_column='ProjectTitle', null=True, blank=True) # Inferred

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

class RateRegister(models.Model):
    id = models.AutoField(primary_key=True) # Assuming an ID for Django's use
    comp_id = models.IntegerField(db_column='CompId')
    item_id = models.IntegerField(db_column='ItemId')
    rate = models.FloatField(db_column='Rate')
    discount = models.FloatField(db_column='Discount', default=0.0)

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'
        # Consider unique_together for (comp_id, item_id) if rates are per item per company

# --- Derived Report Model and Manager ---

class TotalIssueShortageEntryManager(models.Manager):
    """
    Custom manager to encapsulate the complex report generation logic
    from the ASP.NET code-behind's Page_Init and getPrintnode methods.
    This generates "virtual" TotalIssueShortageEntry instances.
    """
    def generate_report(self, wono, comp_id, fin_year_id):
        """
        Re-implements the logic of getPrintnode and Page_Init for report data.
        This method will query various underlying models to build the report.
        """
        report_data = []
        # In a real scenario, 'fun' calls would be replaced by direct ORM queries
        # and Python logic for recursion and calculations.

        # 1. Get initial BOM CIds (from Page_Init: fun.select("CId", "tblDG_BOM_Master", "WONo='" + WONo + "' AND PId='0'"))
        root_bom_entries = BOMMaster.objects.filter(wo_no=wono, parent_id=0, comp_id=comp_id).values_list('child_id', flat=True)

        processed_nodes = set() # To prevent infinite recursion and duplicate entries

        def _process_node_recursive(node_id):
            if node_id in processed_nodes:
                return
            processed_nodes.add(node_id)

            # Logic from getPrintnode for a given node (CId)
            try:
                # Select current node details (from getparent2 in C#)
                current_bom_entry = BOMMaster.objects.select_related('item__uom_basic').get(
                    child_id=node_id, wo_no=wono, comp_id=comp_id
                )
                item_master = current_bom_entry.item
                if not item_master:
                    return # Skip if item not found

                item_code = fun.GetItemCode_PartNo(comp_id, item_master.id)
                manf_desc = item_master.manf_desc
                uom_symbol = item_master.uom_basic.symbol if item_master.uom_basic else ''

                # Calculate BOMQty (from fun.BOMRecurQty)
                # This is a placeholder; actual recursion needs to be implemented
                bom_qty = fun.BOMRecurQty(wono, current_bom_entry.parent_id, node_id, 1, comp_id, fin_year_id)

                # Calculate IssuedQty (from sql in C#)
                issued_qty_sum = WISDetail.objects.filter(
                    master__wo_no=wono,
                    master__comp_id=comp_id,
                    master__fin_year_id__lte=fin_year_id, # C# had AND tblInv_WIS_Master.FinYearId<='"+FinYearId+"'
                    parent_id=current_bom_entry.parent_id,
                    child_id=node_id
                ).aggregate(total_issued=Coalesce(Sum('issued_qty'), 0.0))['total_issued']
                
                issue_qty = issued_qty_sum

                shortage_qty = round(bom_qty - issue_qty, 3)

                # Get Rate and calculate Amount
                rate_register = RateRegister.objects.filter(
                    comp_id=comp_id,
                    item_id=item_master.id
                ).annotate(
                    effective_rate=ExpressionWrapper(
                        F('rate') - (F('rate') * F('discount') / 100.0),
                        output_field=fields.FloatField()
                    )
                ).order_by('-id').first() # Assuming latest rate is max(rate) if no date field

                rate = round(rate_register.effective_rate, 2) if rate_register else 0.0
                amount = round(issue_qty * rate, 2)

                # Add row if IssueQty > 0 (as per C# logic)
                if issue_qty > 0:
                    report_data.append(TotalIssueShortageEntry(
                        item_code=item_code,
                        manf_desc=manf_desc,
                        uom_basic=uom_symbol,
                        bom_qty=bom_qty,
                        issued_qty=issue_qty,
                        shortage_qty=shortage_qty,
                        ac="A", # "A" or "C" from C# logic, simplified here
                        rate=rate,
                        amount=amount,
                        # A unique ID for the report row for client-side operations (like DataTables)
                        # In a real app, this could be a hash of the key fields or a simple counter.
                        pk=len(report_data) + 1 # Dummy PK for demonstration
                    ))

                # Recursively process children (from getparent in C#)
                child_bom_entries = BOMMaster.objects.filter(
                    parent_id=node_id, wo_no=wono, comp_id=comp_id
                ).values_list('child_id', flat=True)

                for child_node_id in child_bom_entries:
                    _process_node_recursive(child_node_id)

            except ObjectDoesNotExist:
                pass # Node not found, skip or log
            except Exception as e:
                print(f"Error processing node {node_id}: {e}") # Log errors

        # Start recursion from root BOM entries (PId='0')
        for root_child_id in root_bom_entries:
            _process_node_recursive(root_child_id)

        # Sort the report data by ItemCode as in C# (dv.Sort = "ItemCode")
        report_data.sort(key=lambda x: x.item_code)

        # Retrieve additional report parameters
        try:
            work_order_info = WorkOrderMaster.objects.get(wo_no=wono, comp_id=comp_id)
            report_parameters = {
                "Company": fun.getCompany(comp_id),
                "Address": fun.CompAdd(comp_id),
                "Title": fun.getProjectTitle(wono),
                "WONo": wono,
                "TryOut_FDate": fun.FromDateDMY(str(work_order_info.task_target_try_out_fdate)),
                "TryOut_TDate": fun.FromDateDMY(str(work_order_info.task_target_try_out_tdate)),
                "Despach_FDate": fun.FromDateDMY(str(work_order_info.task_target_despach_fdate)),
                "Despach_TDate": fun.FromDateDMY(str(work_order_info.task_target_despach_tdate)),
                "ProjectLeader": work_order_info.task_project_leader,
            }
        except ObjectDoesNotExist:
            report_parameters = {
                "Company": "N/A", "Address": "N/A", "Title": "N/A", "WONo": wono,
                "TryOut_FDate": "N/A", "TryOut_TDate": "N/A", "Despach_FDate": "N/A",
                "Despach_TDate": "N/A", "ProjectLeader": "N/A",
            }
        
        # Return a tuple of (report_data, report_parameters)
        return report_data, report_parameters

class TotalIssueShortageEntry(models.Model):
    """
    Represents a single row of the 'Total Issue and Shortage' report.
    This is a conceptual model for derived report data, not directly
    persisted to a single database table. It uses a custom manager
    to generate its instances from underlying data.
    """
    # Define primary key for Django's internal use, even if not DB-mapped
    pk = models.IntegerField(primary_key=True)

    item_code = models.CharField(max_length=100)
    manf_desc = models.CharField(max_length=255)
    uom_basic = models.CharField(max_length=50) # Storing symbol directly
    bom_qty = models.FloatField()
    issued_qty = models.FloatField()
    shortage_qty = models.FloatField()
    ac = models.CharField(max_length=10) # 'A' or 'C' in original C#
    rate = models.FloatField()
    amount = models.FloatField()

    # Custom manager to generate report data
    objects = TotalIssueShortageEntryManager()

    class Meta:
        managed = False  # This model is not managed by Django migrations
        # No db_table as it's a derived, not persisted, entity

    def __str__(self):
        return f"Report Entry: {self.item_code} - {self.manf_desc}"

    # Business logic methods for report entry (if any, e.g., formatting)
    def is_shortage_critical(self):
        return self.shortage_qty > 0 and self.issued_qty == 0

    def get_display_rate(self):
        return f"{self.rate:,.2f}"

    def get_display_amount(self):
        return f"{self.amount:,.2f}"

```

#### 4.2 Forms (`inventory_reports/forms.py`)

**Task:** Define a Django form for `TotalIssueShortageEntry`.

**Instructions:**
Since `TotalIssueShortageEntry` is a derived model, a `ModelForm` can technically be used but it won't perform actual database operations. For demonstration and adherence to the template, we create a `ModelForm` that would represent the fields of a report entry. In a real scenario, if a report entry were "editable," these edits would need to propagate to the *underlying* database tables. For this read-only report page, this form is largely conceptual for CRUD operations on the *report entry itself*.

```python
from django import forms
from .models import TotalIssueShortageEntry

class TotalIssueShortageEntryForm(forms.ModelForm):
    class Meta:
        model = TotalIssueShortageEntry
        fields = [
            'item_code', 'manf_desc', 'uom_basic', 'bom_qty', 
            'issued_qty', 'shortage_qty', 'ac', 'rate', 'amount'
        ]
        widgets = {
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manf_desc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'uom_basic': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bom_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'issued_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'shortage_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ac': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'rate': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    # Add custom validation methods here if needed, for instance, to ensure quantities are positive.
```

#### 4.3 Views (`inventory_reports/views.py`)

**Task:** Implement Read (List) operation for the report and conceptual CRUD operations.

**Instructions:**
The `TotalIssueShortageEntryListView` will be the primary view, responsible for displaying the report. It overrides `get_queryset` to call our custom manager's `generate_report` method. The `CreateView`, `UpdateView`, and `DeleteView` are provided as structural adherence to the prompt, but for this report page, they are conceptual; actual CRUD would modify underlying BOM/Inventory records, not the report rows. We simulate successful operations for runnable code.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from .models import TotalIssueShortageEntry, fun
from .forms import TotalIssueShortageEntryForm

# This will serve as the main report view.
# It leverages the TotalIssueShortageEntry model and its manager to fetch report data.
class TotalIssueShortageEntryListView(ListView):
    model = TotalIssueShortageEntry
    template_name = 'inventory_reports/totalissueshortageentry/list.html'
    context_object_name = 'totalissueshortageentries'

    def get_queryset(self):
        # Retrieve parameters from URL, mimicking ASP.NET's Request.QueryString and Session
        # In a real app, CompId and FinYearId would likely come from user session/profile
        wono = self.request.GET.get('wono')
        status = self.request.GET.get('status') # Not directly used for report data, but for redirect
        
        # Dummy session values for testing if not available in real session
        # In a real app, these would come from actual user session management
        comp_id = self.request.session.get('compid', 1) # Default to 1 for demonstration
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 for demonstration

        if wono:
            report_entries, report_params = self.model.objects.generate_report(wono, comp_id, fin_year_id)
            self.report_params = report_params # Store for context_object
            return report_entries
        return [] # Return empty list if no WONo provided

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add report parameters to the context
        context['report_params'] = getattr(self, 'report_params', {})
        return context


# This view renders just the table content, for HTMX partial updates
class TotalIssueShortageEntryTablePartialView(TotalIssueShortageEntryListView):
    template_name = 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_table.html'


# Conceptual CRUD views for TotalIssueShortageEntry (as per prompt requirement)
# In a real report scenario, these would typically apply to underlying data (BOM, WIS entries)
# and not the derived report entries themselves.
class TotalIssueShortageEntryCreateView(CreateView):
    model = TotalIssueShortageEntry
    form_class = TotalIssueShortageEntryForm
    template_name = 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_form.html'
    success_url = reverse_lazy('totalissueshortageentry_list')

    def form_valid(self, form):
        # For a derived report entry, creation is conceptual.
        # In a real app, this would involve creating underlying BOM or WIS records.
        # We'll just simulate success for demonstration.
        messages.success(self.request, 'Total Issue Shortage Entry conceptually added.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing but use headers
                headers={
                    'HX-Trigger': 'refreshTotalIssueShortageEntryList' # Trigger refresh of the list
                }
            )
        return HttpResponseRedirect(self.get_success_url())

    def get_object(self, queryset=None):
        # Override to prevent database lookup since this model is not persisted
        return None


class TotalIssueShortageEntryUpdateView(UpdateView):
    model = TotalIssueShortageEntry
    form_class = TotalIssueShortageEntryForm
    template_name = 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_form.html'
    success_url = reverse_lazy('totalissueshortageentry_list')

    def get_object(self, queryset=None):
        # Since TotalIssueShortageEntry is derived, we need a way to "find" it.
        # For demonstration, we'll assume a dummy ID is passed and create a dummy object.
        # In a real application, this would imply finding the underlying record
        # that corresponds to this report entry.
        pk = self.kwargs.get(self.pk_url_kwarg)
        # Create a dummy instance for form population
        dummy_obj = TotalIssueShortageEntry(pk=pk, item_code=f'ITEM-{pk}', manf_desc='Dummy Item',
                                              uom_basic='PCS', bom_qty=10, issued_qty=5,
                                              shortage_qty=5, ac='A', rate=100, amount=500)
        return dummy_obj

    def form_valid(self, form):
        # For a derived report entry, update is conceptual.
        # In a real app, this would involve updating underlying BOM or WIS records.
        messages.success(self.request, 'Total Issue Shortage Entry conceptually updated.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTotalIssueShortageEntryList'
                }
            )
        return HttpResponseRedirect(self.get_success_url())


class TotalIssueShortageEntryDeleteView(DeleteView):
    model = TotalIssueShortageEntry
    template_name = 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_confirm_delete.html'
    success_url = reverse_lazy('totalissueshortageentry_list')

    def get_object(self, queryset=None):
        # Similar to update, we'll create a dummy object for deletion confirmation.
        pk = self.kwargs.get(self.pk_url_kwarg)
        dummy_obj = TotalIssueShortageEntry(pk=pk, item_code=f'ITEM-{pk}', manf_desc='Dummy Item')
        return dummy_obj

    def delete(self, request, *args, **kwargs):
        # For a derived report entry, deletion is conceptual.
        # In a real app, this would involve deleting underlying BOM or WIS records.
        messages.success(self.request, 'Total Issue Shortage Entry conceptually deleted.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTotalIssueShortageEntryList'
                }
            )
        return HttpResponseRedirect(self.get_success_url())

# ASP.NET Button1_Click logic for redirection
class TotalIssueShortageRedirectView(TemplateView):
    def get(self, request, *args, **kwargs):
        status = request.GET.get('status')
        wono = request.GET.get('wono')

        # Mimic ASP.NET's redirection logic
        if status == '0':
            # This would redirect to the Django equivalent of WIS_Dry_Actual_Run.aspx
            return HttpResponseRedirect(reverse_lazy('wis_dry_actual_run_list') + f'?wono={wono}')
        elif status == '1':
            # This would redirect to the Django equivalent of WIS_ActualRun_Print.aspx
            return HttpResponseRedirect(reverse_lazy('wis_actual_run_print') + f'?wono={wono}')
        else:
            # Default fallback or error
            messages.warning(request, "Invalid status for redirection.")
            return HttpResponseRedirect(reverse_lazy('totalissueshortageentry_list'))

# Dummy views for redirection targets for demonstration purposes
class WISDryActualRunListView(TemplateView):
    template_name = 'inventory_reports/dummy_redirect_page.html' # Create this dummy template
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = "WIS Dry Actual Run List"
        context['won_param'] = self.request.GET.get('wono', 'N/A')
        return context

class WISActualRunPrintView(TemplateView):
    template_name = 'inventory_reports/dummy_redirect_page.html' # Create this dummy template
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = "WIS Actual Run Print"
        context['won_param'] = self.request.GET.get('wono', 'N/A')
        return context

```

#### 4.4 Templates (`inventory_reports/templates/inventory_reports/totalissueshortageentry/`)

**Task:** Create templates for each view, leveraging HTMX, Alpine.js, and DataTables.

**Instructions:**
Templates are component-specific. `list.html` loads the table via HTMX. The table and form templates are partials.

##### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 bg-blue-600 text-white p-4 rounded-lg shadow-md">
        <h2 class="text-3xl font-extrabold">Total Issue - Print Report</h2>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <button 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out shadow-lg"
                hx-get="{% url 'totalissueshortageentry_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Report Entry (Conceptual)
            </button>
            <a href="{% url 'totalissueshortage_redirect' %}?status=0&wono={{ report_params.WONo|default:'' }}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded inline-flex items-center transition duration-200 ease-in-out shadow-lg">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                Back to WIS Dry Actual Run
            </a>
            <a href="{% url 'totalissueshortage_redirect' %}?status=1&wono={{ report_params.WONo|default:'' }}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded inline-flex items-center transition duration-200 ease-in-out shadow-lg">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                Go to WIS Actual Run Print
            </a>
        </div>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">Report Parameters:</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-gray-700">
            <div><strong>Company:</strong> {{ report_params.Company|default:'N/A' }}</div>
            <div><strong>Address:</strong> {{ report_params.Address|default:'N/A' }}</div>
            <div><strong>Project Title:</strong> {{ report_params.Title|default:'N/A' }}</div>
            <div><strong>Work Order No:</strong> {{ report_params.WONo|default:'N/A' }}</div>
            <div><strong>Try Out Date (From-To):</strong> {{ report_params.TryOut_FDate|default:'N/A' }} - {{ report_params.TryOut_TDate|default:'N/A' }}</div>
            <div><strong>Dispatch Date (From-To):</strong> {{ report_params.Despach_FDate|default:'N/A' }} - {{ report_params.Despach_TDate|default:'N/A' }}</div>
            <div><strong>Project Leader:</strong> {{ report_params.ProjectLeader|default:'N/A' }}</div>
        </div>
    </div>

    <div id="totalissueshortageentryTable-container"
         hx-trigger="load, refreshTotalIssueShortageEntryList from:body"
         hx-get="{% url 'totalissueshortageentry_table' %}?wono={{ request.GET.wono|default:'' }}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-4xl w-full mx-4 my-8 relative overflow-y-auto max-h-[90vh]">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For simple modal handling, Alpine can be used, or just HTMX + _hyperscript
    });

    // Event listener for HTMX triggered refresh for DataTables
    document.body.addEventListener('refreshTotalIssueShortageEntryList', function() {
        console.log('HTMX triggered refreshTotalIssueShortageEntryList');
        // DataTable will be re-initialized when the container loads new content
    });
</script>
{% endblock %}
```

##### `_totalissueshortageentry_table.html` (Partial for HTMX)

```html
<div class="overflow-x-auto">
    <table id="totalissueshortageentryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf. Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shortage Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">A/C</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in totalissueshortageentries %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.item_code }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.manf_desc }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.uom_basic }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.bom_qty|floatformat:3 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.issued_qty|floatformat:3 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.shortage_qty|floatformat:3 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.ac }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.rate|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded transition duration-200 ease-in-out text-xs mr-2"
                        hx-get="{% url 'totalissueshortageentry_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit (Conceptual)
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded transition duration-200 ease-in-out text-xs"
                        hx-get="{% url 'totalissueshortageentry_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete (Conceptual)
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" class="py-4 px-4 text-center text-sm text-gray-500">No report entries found for the given Work Order.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#totalissueshortageentryTable')) {
            $('#totalissueshortageentryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "order": [[1, "asc"]] // Order by Item Code by default
            });
        }
    });
</script>
```

##### `_totalissueshortageentry_form.html` (Partial for HTMX modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Total Issue Shortage Entry (Conceptual)</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <p class="text-sm text-gray-600 mb-4">
            Note: For this report, 'Add/Edit' operations are conceptual. In a real application,
            these would typically apply to the underlying Bill of Materials or Inventory Issue records.
            This form demonstrates the UI component.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-red-600 text-xs list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4 border-t pt-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md transition duration-200 ease-in-out shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md transition duration-200 ease-in-out shadow-sm">
                Save (Conceptual)
            </button>
        </div>
    </form>
</div>
```

##### `_totalissueshortageentry_confirm_delete.html` (Partial for HTMX modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to conceptually delete the report entry for "<strong>{{ object.item_code }} - {{ object.manf_desc }}</strong>"?
    </p>
    <p class="text-sm text-red-600 mb-6">
        Note: Deleting a report entry is conceptual. In a real application, this would involve
        deleting or marking inactive the underlying Bill of Materials or Inventory Issue records.
    </p>
    <form hx-post="{% url 'totalissueshortageentry_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md transition duration-200 ease-in-out shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md transition duration-200 ease-in-out shadow-sm">
                Delete (Conceptual)
            </button>
        </div>
    </form>
</div>
```

##### `dummy_redirect_page.html` (for redirect targets)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 text-center">
    <h1 class="text-3xl font-bold mb-4">{{ page_title }}</h1>
    <p class="text-gray-700">This is a placeholder page for the redirection from the report.</p>
    <p class="text-gray-700 mt-2">Work Order Number: <strong>{{ won_param }}</strong></p>
    <a href="{% url 'totalissueshortageentry_list' %}?wono={{ won_param }}" 
       class="mt-6 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Go back to Total Issue Report
    </a>
</div>
{% endblock %}
```

#### 4.5 URLs (`inventory_reports/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
Paths for the report list view and conceptual CRUD operations, plus the redirection endpoint.

```python
from django.urls import path
from .views import (
    TotalIssueShortageEntryListView,
    TotalIssueShortageEntryTablePartialView,
    TotalIssueShortageEntryCreateView,
    TotalIssueShortageEntryUpdateView,
    TotalIssueShortageEntryDeleteView,
    TotalIssueShortageRedirectView,
    WISDryActualRunListView, # Dummy redirect target
    WISActualRunPrintView, # Dummy redirect target
)

urlpatterns = [
    # Main report page, expects wono as query parameter
    path('total-issue-shortage/', TotalIssueShortageEntryListView.as_view(), name='totalissueshortageentry_list'),
    
    # HTMX endpoint for loading the table content
    path('total-issue-shortage/table/', TotalIssueShortageEntryTablePartialView.as_view(), name='totalissueshortageentry_table'),

    # Conceptual CRUD operations for report entries (as per prompt)
    path('total-issue-shortage/add/', TotalIssueShortageEntryCreateView.as_view(), name='totalissueshortageentry_add'),
    path('total-issue-shortage/edit/<int:pk>/', TotalIssueShortageEntryUpdateView.as_view(), name='totalissueshortageentry_edit'),
    path('total-issue-shortage/delete/<int:pk>/', TotalIssueShortageEntryDeleteView.as_view(), name='totalissueshortageentry_delete'),

    # URL for mimicking ASP.NET's Button1_Click redirection
    path('total-issue-shortage/redirect/', TotalIssueShortageRedirectView.as_view(), name='totalissueshortage_redirect'),

    # Dummy redirect targets for demonstration
    path('wis-dry-actual-run/', WISDryActualRunListView.as_view(), name='wis_dry_actual_run_list'),
    path('wis-actual-run-print/', WISActualRunPrintView.as_view(), name='wis_actual_run_print'),
]

```

#### 4.6 Tests (`inventory_reports/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
Tests cover model properties and the report generation logic (via the custom manager). View tests simulate GET and POST requests, including HTMX headers, to ensure proper behavior and template rendering.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch, MagicMock
from .models import (
    TotalIssueShortageEntry, TotalIssueShortageEntryManager,
    Company, UnitMaster, ItemMaster, BOMMaster, WISMaster, WISDetail,
    WorkOrderMaster, RateRegister, fun
)

# Mock the clsFunctions stub for isolated testing
class MockClsFunctions:
    def getCompany(self, comp_id): return f"Company {comp_id}"
    def CompAdd(self, comp_id): return f"Address for {comp_id}"
    def getProjectTitle(self, wono): return f"Project {wono}"
    def FromDateDMY(self, date_str):
        if date_str and date_str != 'None':
            from datetime import datetime
            try: return datetime.strptime(date_str.split(' ')[0], '%Y-%m-%d').strftime('%d-%m-%Y')
            except ValueError: return date_str
        return "N/A"
    def GetItemCode_PartNo(self, comp_id, item_id): return f"ITEM-CODE-{item_id}"
    def BOMRecurQty(self, wono, p_id, c_id, multiplier, comp_id, fin_year_id): return 10.0 # Simplified for test

@patch('inventory_reports.models.fun', new_callable=MockClsFunctions)
class TotalIssueShortageEntryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary underlying data for report generation tests
        # Create dummy instances for managed=False models as they won't be in DB
        # In a real test, you'd use a test database populated with fixtures
        cls.company = Company(id=1, company_name="Test Company", address="123 Test St")
        cls.unit_pcs = UnitMaster(id=1, symbol="PCS")
        cls.item1 = ItemMaster(id=101, manf_desc="Product A", uom_basic=cls.unit_pcs, item_code="PA-001", comp_id=1)
        cls.item2 = ItemMaster(id=102, manf_desc="Component B", uom_basic=cls.unit_pcs, item_code="CB-002", comp_id=1)
        cls.bom_root = BOMMaster(id=1, parent_id=0, child_id=cls.item1.id, wo_no="WO001", comp_id=1, item=cls.item1)
        cls.bom_child = BOMMaster(id=2, parent_id=cls.item1.id, child_id=cls.item2.id, wo_no="WO001", comp_id=1, item=cls.item2)
        cls.wis_master = WISMaster(id=1, wo_no="WO001", comp_id=1, fin_year_id=1)
        cls.wis_detail1 = WISDetail(id=1, master=cls.wis_master, parent_id=0, child_id=cls.item1.id, issued_qty=5.0)
        cls.wis_detail2 = WISDetail(id=2, master=cls.wis_master, parent_id=cls.item1.id, child_id=cls.item2.id, issued_qty=2.0)
        cls.work_order = WorkOrderMaster(
            id=1, wo_no="WO001", comp_id=1, task_project_leader="John Doe",
            task_target_try_out_fdate="2023-01-01 00:00:00", task_target_try_out_tdate="2023-01-10 00:00:00",
            task_target_despach_fdate="2023-02-01 00:00:00", task_target_despach_tdate="2023-02-15 00:00:00",
            project_title="Awesome Project"
        )
        cls.rate_register1 = RateRegister(id=1, comp_id=1, item_id=cls.item1.id, rate=50.0, discount=10.0) # Effective rate 45
        cls.rate_register2 = RateRegister(id=2, comp_id=1, item_id=cls.item2.id, rate=20.0, discount=0.0)

        # Patching ORM calls for the manager, as real objects are not in DB
        cls.patcher_bom_master = patch('inventory_reports.models.BOMMaster.objects')
        cls.mock_bom_master_objects = cls.patcher_bom_master.start()

        cls.patcher_item_master = patch('inventory_reports.models.ItemMaster.objects')
        cls.mock_item_master_objects = cls.patcher_item_master.start()

        cls.patcher_wis_detail = patch('inventory_reports.models.WISDetail.objects')
        cls.mock_wis_detail_objects = cls.patcher_wis_detail.start()

        cls.patcher_work_order = patch('inventory_reports.models.WorkOrderMaster.objects')
        cls.mock_work_order_objects = cls.patcher_work_order.start()

        cls.patcher_rate_register = patch('inventory_reports.models.RateRegister.objects')
        cls.mock_rate_register_objects = cls.patcher_rate_register.start()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        cls.patcher_bom_master.stop()
        cls.patcher_item_master.stop()
        cls.patcher_wis_detail.stop()
        cls.patcher_work_order.stop()
        cls.patcher_rate_register.stop()

    def setUp(self):
        # Reset mocks before each test
        self.mock_bom_master_objects.reset_mock()
        self.mock_item_master_objects.reset_mock()
        self.mock_wis_detail_objects.reset_mock()
        self.mock_work_order_objects.reset_mock()
        self.mock_rate_register_objects.reset_mock()

        # Configure mocks for specific test scenarios
        self.mock_bom_master_objects.filter.return_value.values_list.side_effect = [
            # For root_bom_entries call
            MagicMock(return_value=[self.item1.id]),
            # For child_bom_entries call within _process_node_recursive (for item1's children)
            MagicMock(return_value=[self.item2.id]),
            # For child_bom_entries call for item2 (no children)
            MagicMock(return_value=[]),
        ]
        self.mock_bom_master_objects.select_related.return_value.get.side_effect = [
            # For first get in _process_node_recursive (for item1)
            self.bom_root,
            # For second get in _process_node_recursive (for item2)
            self.bom_child,
        ]
        self.mock_item_master_objects.get.side_effect = [
            self.item1, # For fun.GetItemCode_PartNo(comp_id, item_master.id) for item1
            self.item2, # For fun.GetItemCode_PartNo(comp_id, item_master.id) for item2
        ]
        self.mock_wis_detail_objects.filter.return_value.aggregate.side_effect = [
            {'total_issued': self.wis_detail1.issued_qty}, # For item1
            {'total_issued': self.wis_detail2.issued_qty}, # For item2
        ]
        self.mock_rate_register_objects.filter.return_value.annotate.return_value.order_by.return_value.first.side_effect = [
            self.rate_register1, # For item1
            self.rate_register2, # For item2
        ]
        self.mock_work_order_objects.get.return_value = self.work_order


    def test_total_issue_shortage_entry_creation(self):
        # Test creation of a derived object
        entry = TotalIssueShortageEntry(
            pk=1, item_code="TEST001", manf_desc="Test Item", uom_basic="KG",
            bom_qty=100.0, issued_qty=75.0, shortage_qty=25.0, ac="A", rate=10.0, amount=750.0
        )
        self.assertEqual(entry.item_code, "TEST001")
        self.assertEqual(entry.manf_desc, "Test Item")
        self.assertEqual(entry.shortage_qty, 25.0)

    def test_total_issue_shortage_entry_methods(self):
        entry = TotalIssueShortageEntry(
            pk=1, item_code="TEST001", manf_desc="Test Item", uom_basic="KG",
            bom_qty=100.0, issued_qty=75.0, shortage_qty=25.0, ac="A", rate=10.0, amount=750.0
        )
        self.assertTrue(entry.is_shortage_critical())
        self.assertEqual(entry.get_display_rate(), "10.00")
        self.assertEqual(entry.get_display_amount(), "750.00")

        entry_no_shortage = TotalIssueShortageEntry(
            pk=2, item_code="TEST002", manf_desc="Test Item 2", uom_basic="KG",
            bom_qty=100.0, issued_qty=100.0, shortage_qty=0.0, ac="A", rate=10.0, amount=1000.0
        )
        self.assertFalse(entry_no_shortage.is_shortage_critical())


    def test_generate_report_method(self, mock_fun_stub):
        # This will test the generate_report logic by mocking underlying ORM calls
        report_entries, report_params = TotalIssueShortageEntry.objects.generate_report("WO001", 1, 1)

        self.assertEqual(len(report_entries), 2) # Should generate entries for item1 and item2
        self.assertEqual(report_entries[0].item_code, "ITEM-CODE-101") # ItemCode-101 should come first due to sorting
        self.assertEqual(report_entries[1].item_code, "ITEM-CODE-102")

        # Verify calculations for the first entry (item1)
        self.assertEqual(report_entries[0].bom_qty, 10.0) # From mocked BOMRecurQty
        self.assertEqual(report_entries[0].issued_qty, 5.0) # From mocked WISDetail
        self.assertEqual(report_entries[0].shortage_qty, 5.0)
        self.assertEqual(report_entries[0].rate, 45.0) # 50 * (1 - 0.10)
        self.assertEqual(report_entries[0].amount, 225.0) # 5.0 * 45.0

        # Verify report parameters
        self.assertEqual(report_params["Company"], "Company 1")
        self.assertEqual(report_params["WONo"], "WO001")
        self.assertEqual(report_params["TryOut_FDate"], "01-01-2023") # Converted by mocked FromDateDMY

class TotalIssueShortageEntryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('totalissueshortageentry_list')
        self.add_url = reverse('totalissueshortageentry_add')
        self.edit_url = lambda pk: reverse('totalissueshortageentry_edit', args=[pk])
        self.delete_url = lambda pk: reverse('totalissueshortageentry_delete', args=[pk])
        self.table_partial_url = reverse('totalissueshortageentry_table')

        # Mock the manager's generate_report to return dummy data for view tests
        self.mock_generate_report_patch = patch('inventory_reports.models.TotalIssueShortageEntryManager.generate_report')
        self.mock_generate_report = self.mock_generate_report_patch.start()
        
        # Define some dummy report data for views
        self.dummy_report_data = [
            TotalIssueShortageEntry(pk=1, item_code="ITEM-A", manf_desc="Product Alpha", uom_basic="PCS",
                                    bom_qty=10, issued_qty=5, shortage_qty=5, ac="A", rate=10.0, amount=50.0),
            TotalIssueShortageEntry(pk=2, item_code="ITEM-B", manf_desc="Product Beta", uom_basic="KG",
                                    bom_qty=20, issued_qty=20, shortage_qty=0, ac="A", rate=5.0, amount=100.0),
        ]
        self.dummy_report_params = {
            "Company": "Test Co", "Address": "123 Test St", "Title": "Test Project", "WONo": "WO-TEST",
            "TryOut_FDate": "01-01-2023", "TryOut_TDate": "01-01-2023", "Despach_FDate": "01-01-2023",
            "Despach_TDate": "01-01-2023", "ProjectLeader": "Test User",
        }
        self.mock_generate_report.return_value = (self.dummy_report_data, self.dummy_report_params)

        # Mock the clsFunctions stub for isolated testing of redirect view
        self.patcher_fun = patch('inventory_reports.views.fun', new_callable=MockClsFunctions)
        self.mock_fun = self.patcher_fun.start()

    def tearDown(self):
        self.mock_generate_report_patch.stop()
        self.patcher_fun.stop()

    def test_list_view(self):
        response = self.client.get(self.list_url + '?wono=WO-TEST')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/totalissueshortageentry/list.html')
        self.assertIn('totalissueshortageentries', response.context)
        self.assertEqual(len(response.context['totalissueshortageentries']), 2)
        self.assertIn('report_params', response.context)
        self.assertEqual(response.context['report_params']['WONo'], "WO-TEST")
        self.mock_generate_report.assert_called_with("WO-TEST", 1, 1) # Check default session comp/fin year

    def test_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_partial_url + '?wono=WO-TEST', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_table.html')
        self.assertContains(response, 'ITEM-A')
        self.assertContains(response, 'Product Beta')
        self.mock_generate_report.assert_called_with("WO-TEST", 1, 1)

    def test_create_view_get(self):
        response = self.client.get(self.add_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_htmx(self):
        data = {
            'item_code': 'NEW-ITEM', 'manf_desc': 'New Product', 'uom_basic': 'L',
            'bom_qty': 100, 'issued_qty': 50, 'shortage_qty': 50, 'ac': 'A', 'rate': 10, 'amount': 500
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.add_url, data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTotalIssueShortageEntryList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Total Issue Shortage Entry conceptually added.')

    def test_create_view_post_non_htmx(self):
        data = {
            'item_code': 'NEW-ITEM', 'manf_desc': 'New Product', 'uom_basic': 'L',
            'bom_qty': 100, 'issued_qty': 50, 'shortage_qty': 50, 'ac': 'A', 'rate': 10, 'amount': 500
        }
        response = self.client.post(self.add_url, data)
        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX
        self.assertRedirects(response, self.list_url)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Total Issue Shortage Entry conceptually added.')

    def test_update_view_get(self):
        response = self.client.get(self.edit_url(1))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.pk, 1)
        self.assertEqual(response.context['form'].instance.item_code, "ITEM-1") # From dummy object

    def test_update_view_post_htmx(self):
        data = {
            'item_code': 'UPDATED-ITEM', 'manf_desc': 'Updated Product', 'uom_basic': 'L',
            'bom_qty': 100, 'issued_qty': 50, 'shortage_qty': 50, 'ac': 'A', 'rate': 10, 'amount': 500
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.edit_url(1), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTotalIssueShortageEntryList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Total Issue Shortage Entry conceptually updated.')

    def test_delete_view_get(self):
        response = self.client.get(self.delete_url(1))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/totalissueshortageentry/_totalissueshortageentry_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].pk, 1)

    def test_delete_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.delete_url(1), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTotalIssueShortageEntryList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Total Issue Shortage Entry conceptually deleted.')

    def test_redirect_view_status_0(self):
        response = self.client.get(reverse('totalissueshortage_redirect') + '?status=0&wono=WO-TEST')
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('wis_dry_actual_run_list') + '?wono=WO-TEST')

    def test_redirect_view_status_1(self):
        response = self.client.get(reverse('totalissueshortage_redirect') + '?status=1&wono=WO-TEST')
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('wis_actual_run_print') + '?wono=WO-TEST')

    def test_redirect_view_invalid_status(self):
        response = self.client.get(reverse('totalissueshortage_redirect') + '?status=99&wono=WO-TEST', follow=True)
        self.assertEqual(response.status_code, 200) # Follows redirect to list page
        self.assertTemplateUsed(response, 'inventory_reports/totalissueshortageentry/list.html')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Invalid status for redirection.')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for dynamic updates:**
    -   The main report list (`list.html`) uses `hx-get` and `hx-trigger="load, refreshTotalIssueShortageEntryList from:body"` to load the table content dynamically into `totalissueshortageentryTable-container`.
    -   `_totalissueshortageentry_table.html` is the partial loaded by HTMX.
    -   CRUD buttons in `_totalissueshortageentry_table.html` use `hx-get` to load forms/confirmation into a modal (`#modalContent`).
    -   Form submissions (`_totalissueshortageentry_form.html`, `_totalissueshortageentry_confirm_delete.html`) use `hx-post` with `hx-swap="none"` and `hx-trigger="refreshTotalIssueShortageEntryList"`. This tells HTMX to not swap content but to trigger a refresh of the main list, which then reloads the table.
    -   The views respond with `status=204` and `HX-Trigger` headers for HTMX requests, ensuring smooth updates without full page reloads.
-   **Alpine.js for UI state:**
    -   The modal (`#modal`) uses `_hyperscript` (`on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me`) to control its visibility. While Alpine.js could also manage this, `_hyperscript` offers a concise way to achieve it with HTMX. A dummy `alpine:init` is present for future Alpine.js components.
-   **DataTables for list views:**
    -   The `_totalissueshortageentry_table.html` includes a `<script>` block that initializes jQuery DataTables on the `totalissueshortageentryTable`. This provides client-side searching, sorting, and pagination for the report data. The `$(document).ready()` ensures DataTables is re-initialized every time the partial is loaded via HTMX.

This approach ensures a highly responsive and interactive user experience, similar to a Single Page Application (SPA) but with the simplicity and robustness of traditional server-rendered pages.

## Final Notes

This comprehensive modernization plan provides a robust framework for migrating the ASP.NET Crystal Reports page to Django.

-   **Business Value:** This transition moves the application from a proprietary, desktop-oriented reporting solution (Crystal Reports) to a modern, web-native, and open-source platform (Django, HTMX, DataTables). This enhances accessibility, improves user experience with dynamic data interaction, reduces licensing costs, and positions the application for future cloud deployments and scalable development. The "Fat Model, Thin View" architecture ensures business logic is centralized and easily maintainable.
-   **Automation Focus:** The detailed structure and templates provided are designed for AI-assisted automation. Each section (`models.py`, `forms.py`, `views.py`, `urls.py`, `templates`, `tests.py`) represents a distinct, predictable component. AI tools can be trained to parse existing legacy code, extract relevant information (table names, column types, business logic patterns, UI components), and automatically generate these Django files, significantly reducing manual effort and potential errors.
-   **Derived Data Handling:** The plan effectively addresses the complexity of a report page that consolidates data from multiple sources by defining a derived `TotalIssueShortageEntry` model and a custom manager. This allows the use of standard Django patterns (ModelForms, CBVs) while encapsulating the intricate report generation logic within the model layer.
-   **Test Coverage:** Including comprehensive unit and integration tests is crucial. They validate the correctness of the migration, ensure business logic is accurately translated, and provide a safety net for future development. Automating test generation is a key part of the migration strategy.
-   **Future Scalability:** Django's robust ORM, scalable architecture, and active community make it an excellent choice for modernizing legacy applications, allowing for easier feature expansion and integration with other systems.

By following this plan, organizations can confidently transition their critical ASP.NET functionalities to a modern, maintainable, and high-performing Django environment.