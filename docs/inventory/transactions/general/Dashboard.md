## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

Upon analyzing the provided ASP.NET `.aspx` and C# code-behind files for `Dashboard.aspx`, it's evident that the `Dashboard.aspx` contains only content placeholders and links a `loadingNotifier.js` script, while its corresponding `Dashboard.aspx.cs` file has an empty `Page_Load` method and no explicit UI controls or database interactions. This implies the ASP.NET dashboard page itself is a structural placeholder, and its actual content or dynamic behavior would typically be injected through user controls, master page logic, or complex client-side scripting not present in these minimal files.

Given the page's path `Module_Inventory_Transactions_Dashboard`, we will infer its purpose: to manage or display information related to 'Inventory Transactions'. To provide a comprehensive modernization plan demonstrating the full Django capabilities as requested, we will assume this dashboard is the entry point for a typical CRUD (Create, Read, Update, Delete) interface for 'Inventory Transactions'.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code does not contain explicit database interaction elements (like `SqlDataSource`, `SELECT` commands, or `GridView` bindings), we infer a database table and its columns based on the module name: `Module_Inventory_Transactions`.

**Inferred Database Schema:**
*   **Table Name:** `tblInventoryTransactions`
*   **Inferred Columns:**
    *   `TransactionID` (Primary Key, integer)
    *   `TransactionDate` (Date, e.g., `DateTime` in C#)
    *   `ItemName` (String, e.g., `nvarchar` in SQL)
    *   `Quantity` (Integer, e.g., `int` in SQL)
    *   `TransactionType` (String, e.g., `nvarchar` - 'In', 'Out')
    *   `ReferenceNumber` (String, nullable, e.g., invoice number)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET `Dashboard.aspx` and `Dashboard.aspx.cs` files do not contain any explicit CRUD operations. The `Page_Load` method is empty, and there are no UI controls to infer data binding or event handlers.

**Inferred Backend Functionality for Django:**
Based on the assumption that this dashboard serves as an entry point for 'Inventory Transactions', the modernized Django application will support the following standard CRUD operations:
*   **Create:** Adding new inventory transactions.
*   **Read:** Displaying a list of all inventory transactions and individual transaction details.
*   **Update:** Modifying existing inventory transactions.
*   **Delete:** Removing inventory transactions.
*   **Validation Logic:** Basic form validation (e.g., required fields, numeric quantity).

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
No ASP.NET controls (e.g., `GridView`, `TextBox`, `Button`) are present in the `Dashboard.aspx` file.

**Inferred UI Components for Django:**
To provide a fully functional Django solution adhering to modern practices, the UI will be designed with:
*   **List View:** A table using DataTables for displaying `InventoryTransaction` records, allowing client-side searching, sorting, and pagination. Each row will include "Edit" and "Delete" action buttons.
*   **Form for Create/Update:** A modal form containing fields for `Transaction Date`, `Item Name`, `Quantity`, `Transaction Type`, and `Reference Number`. This form will be loaded dynamically via HTMX.
*   **Confirmation Dialog for Delete:** A modal dialog for confirming deletion, also loaded via HTMX.
*   **Dynamic Interactions:** All form submissions and modal displays will use HTMX. Alpine.js will manage the modal's `hidden/is-active` state.
*   **Loading Indicators:** `loadingNotifier.js` in ASP.NET hints at dynamic loading, which will be handled gracefully by HTMX's `hx-indicator` and `hx-swap` mechanisms.

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The model `InventoryTransaction` will map to the `tblInventoryTransactions` table.

```python
# inventory_transactions/models.py
from django.db import models
from django.utils import timezone

class InventoryTransaction(models.Model):
    transaction_id = models.AutoField(db_column='TransactionID', primary_key=True)
    transaction_date = models.DateField(db_column='TransactionDate', default=timezone.now)
    item_name = models.CharField(db_column='ItemName', max_length=255)
    quantity = models.IntegerField(db_column='Quantity')
    transaction_type = models.CharField(db_column='TransactionType', max_length=50, choices=[('IN', 'In'), ('OUT', 'Out')])
    reference_number = models.CharField(db_column='ReferenceNumber', max_length=255, blank=True, null=True)

    class Meta:
        managed = False # Set to True if Django manages this table, False if it's pre-existing
        db_table = 'tblInventoryTransactions'
        verbose_name = 'Inventory Transaction'
        verbose_name_plural = 'Inventory Transactions'
        ordering = ['-transaction_date', 'item_name']

    def __str__(self):
        return f"{self.item_name} ({self.transaction_type} {self.quantity} on {self.transaction_date})"
        
    def is_inbound(self):
        """Business logic: Check if the transaction is an 'In' type."""
        return self.transaction_type == 'IN'

    def get_display_quantity(self):
        """Business logic: Return quantity with sign based on transaction type."""
        return self.quantity if self.transaction_type == 'IN' else -self.quantity
```

### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` for `InventoryTransaction` will be created with appropriate fields and Tailwind CSS styling.

```python
# inventory_transactions/forms.py
from django import forms
from .models import InventoryTransaction

class InventoryTransactionForm(forms.ModelForm):
    class Meta:
        model = InventoryTransaction
        fields = ['transaction_date', 'item_name', 'quantity', 'transaction_type', 'reference_number']
        widgets = {
            'transaction_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date'
            }),
            'item_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'min': '1' # Example validation: quantity must be at least 1
            }),
            'transaction_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'reference_number': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }
        
    def clean_quantity(self):
        """Example custom validation: Ensure quantity is positive."""
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive number.")
        return quantity
```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views will be thin, relying on models for business logic and using HTMX for dynamic content updates. A `TablePartialView` will be added to serve just the DataTables content.

```python
# inventory_transactions/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import InventoryTransaction
from .forms import InventoryTransactionForm

class InventoryTransactionListView(ListView):
    model = InventoryTransaction
    template_name = 'inventory_transactions/inventorytransaction/list.html'
    context_object_name = 'inventorytransactions' # Plural for context

class InventoryTransactionTablePartialView(ListView):
    """View to return only the table content for HTMX swaps."""
    model = InventoryTransaction
    template_name = 'inventory_transactions/inventorytransaction/_inventorytransaction_table.html'
    context_object_name = 'inventorytransactions' # Plural for context

    def get_queryset(self):
        return InventoryTransaction.objects.all() # Fetch all data for DataTables

class InventoryTransactionCreateView(CreateView):
    model = InventoryTransaction
    form_class = InventoryTransactionForm
    template_name = 'inventory_transactions/inventorytransaction/form.html'
    success_url = reverse_lazy('inventorytransaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Inventory Transaction added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInventoryTransactionList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX, render the form again with errors
            return response
        return response

class InventoryTransactionUpdateView(UpdateView):
    model = InventoryTransaction
    form_class = InventoryTransactionForm
    template_name = 'inventory_transactions/inventorytransaction/form.html'
    success_url = reverse_lazy('inventorytransaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Inventory Transaction updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInventoryTransactionList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX, render the form again with errors
            return response
        return response

class InventoryTransactionDeleteView(DeleteView):
    model = InventoryTransaction
    template_name = 'inventory_transactions/inventorytransaction/confirm_delete.html'
    success_url = reverse_lazy('inventorytransaction_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Inventory Transaction deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInventoryTransactionList'
                }
            )
        return response
```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates follow DRY principles, extending `core/base.html` and using partials for HTMX-loaded content.

#### `inventory_transactions/inventorytransaction/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Inventory Transactions</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'inventorytransaction_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Transaction
        </button>
    </div>
    
    <div id="inventorytransactionTable-container"
         hx-trigger="load, refreshInventoryTransactionList from:body"
         hx-get="{% url 'inventorytransaction_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading inventory transactions...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ showModal: false }"
         x-init="document.body.classList.remove('overflow-hidden')"
         x-show="showModal"
         @click.away="showModal = false"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @htmx:after-request="if (event.detail.successful) {
                // If form submitted successfully, close modal
                if (event.detail.xhr.status === 204) {
                    document.getElementById('modal').classList.remove('is-active');
                    htmx.trigger(document.body, 'refreshInventoryTransactionList');
                } else {
                    // If form has errors, keep modal open and update content
                    // HTMX will swap the target with the new form content (including errors)
                }
            }">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for modal state
        Alpine.data('modal', () => ({
            show: false,
            open() { this.show = true },
            close() { this.show = false },
        }));
    });
    // HTMX setup for modal management
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
        }
    });
</script>
{% endblock %}
```

#### `inventory_transactions/inventorytransaction/_inventorytransaction_table.html`

```html
<table id="inventorytransactionTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference No.</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in inventorytransactions %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.transaction_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.item_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700 text-right">{{ obj.quantity }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.get_transaction_type_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.reference_number|default_if_none:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                    hx-get="{% url 'inventorytransaction_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-get="{% url 'inventorytransaction_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#inventorytransactionTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "order": [[ 1, "desc" ]] // Order by Transaction Date descending by default
    });
});
</script>
```

#### `inventory_transactions/inventorytransaction/form.html` (partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Inventory Transaction</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-2 sm:gap-x-6">
            {% for field in form %}
            <div class="col-span-1">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Save Transaction
            </button>
        </div>
    </form>
</div>
```

#### `inventory_transactions/inventorytransaction/confirm_delete.html` (partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the inventory transaction for "{{ object.item_name }}" on {{ object.transaction_date|date:"Y-m-d" }}?</p>
    <form hx-post="{% url 'inventorytransaction_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URL patterns for list, create, update, delete, and the HTMX table partial.

```python
# inventory_transactions/urls.py
from django.urls import path
from .views import (
    InventoryTransactionListView, 
    InventoryTransactionCreateView, 
    InventoryTransactionUpdateView, 
    InventoryTransactionDeleteView,
    InventoryTransactionTablePartialView
)

urlpatterns = [
    path('inventorytransactions/', InventoryTransactionListView.as_view(), name='inventorytransaction_list'),
    path('inventorytransactions/table/', InventoryTransactionTablePartialView.as_view(), name='inventorytransaction_table'), # For HTMX refresh
    path('inventorytransactions/add/', InventoryTransactionCreateView.as_view(), name='inventorytransaction_add'),
    path('inventorytransactions/edit/<int:pk>/', InventoryTransactionUpdateView.as_view(), name='inventorytransaction_edit'),
    path('inventorytransactions/delete/<int:pk>/', InventoryTransactionDeleteView.as_view(), name='inventorytransaction_delete'),
]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and properties, and integration tests for all views, including HTMX interactions.

```python       
# inventory_transactions/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import InventoryTransaction

class InventoryTransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.transaction1 = InventoryTransaction.objects.create(
            transaction_date=timezone.now().date(),
            item_name='Widget A',
            quantity=100,
            transaction_type='IN',
            reference_number='REF001'
        )
        cls.transaction2 = InventoryTransaction.objects.create(
            transaction_date=timezone.now().date(),
            item_name='Gadget B',
            quantity=50,
            transaction_type='OUT',
            reference_number='REF002'
        )
  
    def test_inventory_transaction_creation(self):
        self.assertEqual(self.transaction1.item_name, 'Widget A')
        self.assertEqual(self.transaction1.quantity, 100)
        self.assertEqual(self.transaction1.transaction_type, 'IN')
        self.assertEqual(self.transaction1.reference_number, 'REF001')
        self.assertTrue(isinstance(self.transaction1.transaction_date, type(timezone.now().date())))

    def test_item_name_label(self):
        field_label = self.transaction1._meta.get_field('item_name').verbose_name
        self.assertEqual(field_label, 'item name') # Django's default verbose_name for CharField

    def test_str_representation(self):
        expected_str = f"Widget A (IN 100 on {self.transaction1.transaction_date})"
        self.assertEqual(str(self.transaction1), expected_str)
        
    def test_is_inbound_method(self):
        self.assertTrue(self.transaction1.is_inbound())
        self.assertFalse(self.transaction2.is_inbound())

    def test_get_display_quantity_method(self):
        self.assertEqual(self.transaction1.get_display_quantity(), 100)
        self.assertEqual(self.transaction2.get_display_quantity(), -50)

class InventoryTransactionViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.transaction = InventoryTransaction.objects.create(
            transaction_date=timezone.now().date(),
            item_name='Test Item',
            quantity=10,
            transaction_type='IN',
            reference_number='TESTREF'
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('inventorytransaction_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/inventorytransaction/list.html')
        self.assertTrue('inventorytransactions' in response.context)
        self.assertContains(response, 'Test Item') # Check if item is in context

    def test_table_partial_view_get(self):
        # Test HTMX partial view
        response = self.client.get(reverse('inventorytransaction_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/inventorytransaction/_inventorytransaction_table.html')
        self.assertTrue('inventorytransactions' in response.context)
        self.assertContains(response, 'Test Item')

    def test_create_view_get(self):
        response = self.client.get(reverse('inventorytransaction_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/inventorytransaction/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Inventory Transaction')

    def test_create_view_post_success(self):
        data = {
            'transaction_date': (timezone.now() - timezone.timedelta(days=1)).strftime('%Y-%m-%d'),
            'item_name': 'New Item',
            'quantity': 20,
            'transaction_type': 'OUT',
            'reference_number': 'NEWREF'
        }
        # Simulate an HTMX POST request
        response = self.client.post(reverse('inventorytransaction_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response for no content swap
        self.assertTrue(InventoryTransaction.objects.filter(item_name='New Item').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInventoryTransactionList')
        
    def test_create_view_post_invalid(self):
        data = {
            'transaction_date': timezone.now().date().strftime('%Y-%m-%d'),
            'item_name': '', # Invalid: empty item name
            'quantity': 0, # Invalid: quantity <= 0
            'transaction_type': 'IN',
        }
        response = self.client.post(reverse('inventorytransaction_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'inventory_transactions/inventorytransaction/form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Quantity must be a positive number.')
        self.assertFalse(InventoryTransaction.objects.filter(item_name='').exists()) # Ensure no object created

    def test_update_view_get(self):
        response = self.client.get(reverse('inventorytransaction_edit', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/inventorytransaction/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Inventory Transaction')
        self.assertContains(response, 'Test Item')

    def test_update_view_post_success(self):
        data = {
            'transaction_date': self.transaction.transaction_date.strftime('%Y-%m-%d'),
            'item_name': 'Updated Item',
            'quantity': 15,
            'transaction_type': 'OUT',
            'reference_number': 'UPDATEDREF'
        }
        response = self.client.post(reverse('inventorytransaction_edit', args=[self.transaction.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.item_name, 'Updated Item')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInventoryTransactionList')

    def test_delete_view_get(self):
        response = self.client.get(reverse('inventorytransaction_delete', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/inventorytransaction/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, 'Test Item')

    def test_delete_view_post_success(self):
        # Create a new item to delete so it doesn't affect other tests
        item_to_delete = InventoryTransaction.objects.create(
            transaction_date=timezone.now().date(),
            item_name='Delete Me',
            quantity=1,
            transaction_type='IN'
        )
        response = self.client.post(reverse('inventorytransaction_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(InventoryTransaction.objects.filter(pk=item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInventoryTransactionList')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:**
    *   The main `list.html` uses `hx-get` to load the table content (`_inventorytransaction_table.html`) dynamically on page load and whenever a `refreshInventoryTransactionList` event is triggered.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch their respective forms (`_inventorytransaction_form.html`, `_inventorytransaction_confirm_delete.html`) into a modal via `hx-target="#modalContent"`.
    *   Form submissions (Create/Update/Delete) use `hx-post` on the form itself, with `hx-swap="none"` and `hx-trigger="submit"`. The views then respond with `HttpResponse(status=204, headers={'HX-Trigger': 'refreshInventoryTransactionList'})` to indicate success and trigger the list refresh without a full page reload or content swap on the form itself.
    *   Error handling for forms in `form_invalid` sends the form with errors back, allowing HTMX to swap the `modalContent` with the updated form, keeping the modal open for corrections.

*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses `x-data` and `_` (Hyperscript syntax for Alpine.js) to manage its visibility (`hidden` and `is-active` CSS classes). The `on click add .is-active to #modal` ensures the modal opens when the trigger button is clicked.
    *   Clicking outside the modal or on a "Cancel" button triggers `remove .is-active from #modal` to close it.

*   **DataTables for List Views:**
    *   The `_inventorytransaction_table.html` partial contains the `<table>` element with `id="inventorytransactionTable"`.
    *   A `<script>` block within this partial initializes DataTables on this table, providing client-side searching, sorting, and pagination. This script is re-run every time the partial is reloaded via HTMX.

*   **No Full Page Reloads:** All CRUD operations and list refreshes are handled dynamically using HTMX, providing a smooth, single-page application-like experience without the complexity of a full SPA framework.

---

## Final Notes

This comprehensive plan provides a complete, runnable Django application structure for managing "Inventory Transactions," fully adhering to the specified modern patterns: fat models, thin views (max 15 lines in core logic), HTMX + Alpine.js for frontend, DataTables for lists, and robust testing. The solution explicitly addresses the inferred nature of the original ASP.NET code by providing a complete, example implementation of common business functionalities.