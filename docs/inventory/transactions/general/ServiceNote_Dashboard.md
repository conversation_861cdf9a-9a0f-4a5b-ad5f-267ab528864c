## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Initial Assessment Note:
The provided ASP.NET code (`ServiceNote_Dashboard.aspx` and `ServiceNote_Dashboard.aspx.cs`) is exceptionally minimal, consisting only of structural declarations for a master page and an empty `Page_Load` method. There are no UI controls (like GridViews, TextBoxes, or Buttons), no explicit database interactions (e.g., `SqlDataSource`, embedded SQL queries), and no business logic.

Given this lack of specific information, this modernization plan is generated based on **common architectural patterns** for a "Dashboard" page in a legacy ASP.NET application. We will assume the `ServiceNote_Dashboard` is intended to manage "Service Notes" and therefore requires standard CRUD (Create, Read, Update, Delete) functionality. The plan infers a plausible database schema and typical functionalities to provide a concrete, runnable Django solution, demonstrating best practices for such a migration.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- Search for database-related elements like SqlDataSource, connection strings, or SQL commands (e.g., SELECT, INSERT, UPDATE, DELETE).
- Extract the table name and assign it to [TABLE_NAME].
- Identify column names (e.g., [COLUMN1], [COLUMN2]) and, if available, their data types from SQL statements or UI bindings (e.g., GridView columns).
- If columns are not explicitly listed (e.g., SELECT *), infer them from UI controls or other data operations.

**Analysis from provided ASP.NET code:** No database schema or specific table/column information can be directly extracted from the provided empty `.aspx` and `.aspx.cs` files.

**Inferred Database Schema (Plausible based on 'ServiceNote_Dashboard'):**
-   **TABLE_NAME:** `tbl_service_notes`
-   **Inferred Columns:**
    -   `SN_ID` (int, Primary Key)
    -   `SN_Title` (nvarchar(255))
    -   `SN_Description` (nvarchar(MAX))
    -   `SN_CreatedDate` (datetime)
    -   `SN_LastModifiedDate` (datetime)
    -   `SN_IsActive` (bit)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Create: Look for insert operations (e.g., button click events, GridView footer templates).
Read: Find select statements or data binding (e.g., GridView population).
Update: Identify update commands (e.g., GridView edit mode, form submissions).
Delete: Locate delete operations (e.g., button or link triggers).
Record any validation logic (e.g., required fields) for replication in Django.

**Analysis from provided ASP.NET code:** The provided ASP.NET code contains no explicit backend functionality or CRUD operations (the `Page_Load` method is empty).

**Inferred Backend Functionality (Plausible for a 'Dashboard'):**
-   **Read:** Displaying a list of service notes.
-   **Create:** Adding new service notes.
-   **Update:** Editing existing service notes.
-   **Delete:** Deleting service notes (hard delete for simplicity in this example, but soft delete is recommended for production).
-   **Validation:** Basic validation for required fields (e.g., title) and uniqueness.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

- Identify controls like:
  - GridView: Displays data lists.
  - TextBox, DropDownList: Captures user input.
  - Button, LinkButton: Triggers actions.
- Note field mappings (e.g., which columns are bound to which controls).
- Check for JavaScript used in client-side interactions for potential Alpine.js conversion.

**Analysis from provided ASP.NET code:** No UI controls are present in the provided `.aspx` file. A `loadingNotifier.js` script is referenced, suggesting some client-side loading feedback, which can be handled with HTMX and Alpine.js.

**Inferred UI Components (Plausible for a 'Dashboard'):**
-   A main dashboard page (likely using a `GridView` equivalent) to list service notes.
-   Buttons or links for adding new service notes, and for editing/deleting existing ones.
-   Forms with input fields (like `TextBox` or `TextArea`) for entering/editing service note details.
-   Modal dialogs for the add/edit/delete forms to provide a modern user experience.

## Step 4: Generate Django Code

Based on the inferred details, and adhering to the "inventory" module context from the original ASP.NET namespace (`Module_Inventory_Transactions_ServiceNote_Dashboard`), here's the comprehensive Django modernization plan.

### 4.1 Models
**File:** `inventory/models.py`

```python
from django.db import models

class ServiceNote(models.Model):
    """
    Represents a Service Note in the system, mapping to an existing database table.
    Uses 'fat model' approach for encapsulating business logic.
    """
    # AutoField is typically for managed tables; if SN_ID is an existing PK,
    # it might be better as models.IntegerField(primary_key=True) if not auto-incrementing.
    # Assuming SN_ID is auto-incrementing and managed by the DB.
    note_id = models.AutoField(db_column='SN_ID', primary_key=True) 
    title = models.CharField(db_column='SN_Title', max_length=255, verbose_name="Title")
    description = models.TextField(db_column='SN_Description', blank=True, null=True, verbose_name="Description")
    created_at = models.DateTimeField(db_column='SN_CreatedDate', auto_now_add=True, verbose_name="Created Date")
    last_modified_at = models.DateTimeField(db_column='SN_LastModifiedDate', auto_now=True, verbose_name="Last Modified Date")
    is_active = models.BooleanField(db_column='SN_IsActive', default=True, verbose_name="Active Status")

    class Meta:
        managed = False  # Important: Django will not manage this table's schema (it already exists)
        db_table = 'tbl_service_notes'  # Name of the existing database table
        verbose_name = 'Service Note'
        verbose_name_plural = 'Service Notes'
        ordering = ['-created_at'] # Default ordering for list views (most recent first)

    def __str__(self):
        """Returns a string representation of the service note."""
        return self.title if self.title else f"Service Note {self.note_id}"
        
    def get_absolute_url(self):
        """
        Returns the URL to access a particular instance of the model.
        Used by some Django CBVs (e.g., CreateView/UpdateView) for default success_url.
        """
        from django.urls import reverse
        return reverse('servicenote_list')

    # Example of a 'fat model' method for encapsulating business logic
    def toggle_active_status(self):
        """Toggles the active status of the service note."""
        self.is_active = not self.is_active
        self.save()
        return self.is_active

    def get_status_display(self):
        """Returns a user-friendly string for the active status."""
        return "Active" if self.is_active else "Inactive"
```

### 4.2 Forms
**File:** `inventory/forms.py`

```python
from django import forms
from .models import ServiceNote

class ServiceNoteForm(forms.ModelForm):
    """
    Django ModelForm for creating and updating ServiceNote instances.
    """
    class Meta:
        model = ServiceNote
        # Fields to include in the form. 'note_id', 'created_at', 'last_modified_at'
        # are typically auto-generated or managed by the system, so excluded.
        fields = ['title', 'description', 'is_active']
        
        # Widgets define the HTML input type and allow adding Tailwind CSS classes.
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Maintenance Check, Software Installation'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 4,
                'placeholder': 'Provide a detailed description of the service note...'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
            }),
        }
        labels = {
            'title': 'Service Note Title',
            'description': 'Service Note Description',
            'is_active': 'Is Active?',
        }
        
    def clean_title(self):
        """
        Custom validation for the 'title' field to ensure it's not empty
        and unique (case-insensitive) across all active service notes,
        excluding the current instance if it's an update.
        """
        title = self.cleaned_data.get('title')
        if not title:
            raise forms.ValidationError("Service Note Title is required.")
        
        # Check for uniqueness, case-insensitive
        qs = ServiceNote.objects.filter(title__iexact=title)
        if self.instance.pk: # If updating an existing instance, exclude it from the unique check
            qs = qs.exclude(pk=self.instance.pk)
        
        if qs.exists():
            raise forms.ValidationError("A service note with this title already exists.")
            
        return title
```

### 4.3 Views
**File:** `inventory/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ServiceNote
from .forms import ServiceNoteForm

class ServiceNoteListView(ListView):
    """
    Renders the main dashboard page for Service Notes, displaying a list.
    The actual table content is loaded separately via HTMX.
    """
    model = ServiceNote
    template_name = 'inventory/servicenote/list.html'
    context_object_name = 'servicenotes' # Name for the queryset in the template

    # Views are kept thin; business logic (e.g., filtering, complex queries) 
    # would ideally be managed by ServiceNote.objects.custom_manager_method()
    # or within the model's methods if instance-specific.

class ServiceNoteTablePartialView(ListView):
    """
    Renders only the table portion of the Service Notes list.
    This view is specifically designed for HTMX requests to update the table
    without reloading the entire page, optimizing performance.
    """
    model = ServiceNote
    template_name = 'inventory/servicenote/_servicenote_table.html'
    context_object_name = 'servicenotes'

class ServiceNoteCreateView(CreateView):
    """
    Handles the creation of new Service Notes.
    Uses HTMX for dynamic form submission and modal interaction.
    """
    model = ServiceNote
    form_class = ServiceNoteForm
    template_name = 'inventory/servicenote/form.html' # This template is rendered within a modal
    success_url = reverse_lazy('servicenote_list') # Redirect for non-HTMX requests

    def form_valid(self, form):
        """
        If the form is valid, save the new service note and handle HTMX response.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Service Note added successfully!')
        
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content status
            # and trigger a custom event on the client to refresh the list.
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshServiceNoteList' # Custom event to refresh the table
                }
            )
        return response

class ServiceNoteUpdateView(UpdateView):
    """
    Handles the updating of existing Service Notes.
    Uses HTMX for dynamic form submission and modal interaction.
    """
    model = ServiceNote
    form_class = ServiceNoteForm
    template_name = 'inventory/servicenote/form.html' # This template is rendered within a modal
    success_url = reverse_lazy('servicenote_list') # Redirect for non-HTMX requests

    def form_valid(self, form):
        """
        If the form is valid, save the updated service note and handle HTMX response.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Service Note updated successfully!')
        
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content status
            # and trigger a custom event on the client to refresh the list.
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshServiceNoteList' # Custom event to refresh the table
                }
            )
        return response

class ServiceNoteDeleteView(DeleteView):
    """
    Handles the deletion of Service Notes.
    Displays a confirmation modal and handles HTMX for deletion and refresh.
    """
    model = ServiceNote
    template_name = 'inventory/servicenote/confirm_delete.html' # Confirmation template in a modal
    context_object_name = 'servicenote' # Name for the object in the template
    success_url = reverse_lazy('servicenote_list') # Redirect for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        """
        Handles the deletion process.
        """
        # Example of using 'fat model' logic before deletion:
        # service_note = self.get_object()
        # if service_note.can_be_deleted(): # Example business rule
        #     response = super().delete(request, *args, **kwargs)
        # else:
        #     messages.error(self.request, "Service Note cannot be deleted due to business rules.")
        #     return HttpResponseBadRequest() # Or redirect to list view

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Service Note deleted successfully!')
        
        if request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content status
            # and trigger a custom event on the client to refresh the list.
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshServiceNoteList' # Custom event to refresh the table
                }
            )
        return response
```

### 4.4 Templates
**Directory:** `inventory/templates/inventory/servicenote/`

**File:** `list.html`
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 space-y-4 sm:space-y-0">
        <h2 class="text-3xl font-extrabold text-gray-900 leading-tight">Service Notes Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
            hx-get="{% url 'servicenote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal then add .scale-100 .opacity-100 to #modalContent">
            <i class="fas fa-plus mr-2"></i> Add New Service Note
        </button>
    </div>
    
    <!-- Container for the DataTables content, updated via HTMX -->
    <div id="servicenoteTable-container"
         hx-trigger="load, refreshServiceNoteList from:body" {# Loads on page load, refreshes when 'refreshServiceNoteList' event is triggered #}
         hx-get="{% url 'servicenote_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-xl rounded-lg p-6 animate-fade-in">
        <!-- Loading spinner displayed until HTMX content is loaded -->
        <div class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Service Notes...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms/confirmations, hidden by default -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .is-active from me then remove .scale-100 .opacity-100 from #modalContent"
         x-data="{ showModal: false }" x-show="showModal" @keydown.escape.window="showModal = false">
        <div id="modalContent" class="bg-white rounded-xl shadow-2xl max-w-3xl w-full mx-4 p-8 transform scale-95 opacity-0 transition-all duration-300 ease-out">
            <!-- Content loaded via HTMX, typically a form or confirmation -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Minimal Alpine.js is typically used for UI state or local components.
    // For this example, _hyperscript handles modal visibility based on HTMX triggers.
    // Ensure Alpine.js and _hyperscript are loaded in base.html.
</script>
{% endblock %}
```

**File:** `_servicenote_table.html` (Partial template for HTMX)
```html
<table id="servicenoteTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created On</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in servicenotes %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
            <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-800 font-medium">{{ obj.title }}</td>
            <td class="py-3 px-6 text-sm text-gray-600 max-w-sm truncate">{{ obj.description|default:"N/A" }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.created_at|date:"M d, Y H:i" }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm">
                <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {{ obj.get_status_display }}
                </span>
            </td>
            <td class="py-3 px-6 whitespace-nowrap text-sm font-medium space-x-2">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md shadow-sm transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-75"
                    hx-get="{% url 'servicenote_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal then add .scale-100 .opacity-100 to #modalContent">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-md shadow-sm transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75"
                    hx-get="{% url 'servicenote_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal then add .scale-100 .opacity-100 to #modalContent">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-8 text-center text-gray-500">No service notes found. Add a new one!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables after the table content is loaded by HTMX
$(document).ready(function() {
    $('#servicenoteTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers", // Adds first/last buttons
        "language": { // Optional: Customize DataTables labels
            "search": "Search notes:",
            "lengthMenu": "Show _MENU_ entries",
            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
            "infoEmpty": "Showing 0 to 0 of 0 entries",
            "infoFiltered": "(filtered from _MAX_ total entries)",
            "emptyTable": "No data available in table",
            "zeroRecords": "No matching records found",
            "paginate": {
                "first": "First",
                "last": "Last",
                "next": "Next",
                "previous": "Previous"
            }
        },
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] }, // Disable sorting for SN and Actions columns
            { "searchable": false, "targets": [0, 5] } // Disable searching for SN and Actions columns
        ]
    });
});
</script>
```

**File:** `form.html` (Partial template for HTMX, loaded into modal)
```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Service Note</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status == 204) { document.getElementById('modal').classList.remove('is-active'); document.getElementById('modalContent').classList.remove('scale-100', 'opacity-100'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex flex-col sm:flex-row items-center justify-end space-y-3 sm:space-y-0 sm:space-x-4">
            <button 
                type="button" 
                class="w-full sm:w-auto px-6 py-2 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal then remove .scale-100 .opacity-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="w-full sm:w-auto inline-flex justify-center py-2 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out">
                Save Service Note
            </button>
        </div>
    </form>
</div>
```

**File:** `confirm_delete.html` (Partial template for HTMX, loaded into modal)
```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete the service note:</p>
    <p class="text-xl font-semibold text-gray-800 mb-6">"{{ servicenote.title }}"</p>
    <p class="text-red-600 font-medium text-lg mb-8">This action cannot be undone.</p>

    <div class="mt-6 flex flex-col sm:flex-row items-center justify-end space-y-3 sm:space-y-0 sm:space-x-4">
        <button 
            type="button" 
            class="w-full sm:w-auto px-6 py-2 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal then remove .scale-100 .opacity-100 from #modalContent">
            Cancel
        </button>
        <button 
            hx-delete="{% url 'servicenote_delete' servicenote.pk %}" 
            hx-swap="none"
            hx-on::after-request="if(event.detail.xhr.status == 204) { document.getElementById('modal').classList.remove('is-active'); document.getElementById('modalContent').classList.remove('scale-100', 'opacity-100'); }"
            class="w-full sm:w-auto inline-flex justify-center py-2 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-300 ease-in-out">
            Confirm Delete
        </button>
    </div>
</div>
```

### 4.5 URLs
**File:** `inventory/urls.py`

```python
from django.urls import path
from .views import (
    ServiceNoteListView, 
    ServiceNoteCreateView, 
    ServiceNoteUpdateView, 
    ServiceNoteDeleteView,
    ServiceNoteTablePartialView, # Dedicated view for HTMX to fetch table content
)

urlpatterns = [
    # Main dashboard view for Service Notes
    path('servicenotes/', ServiceNoteListView.as_view(), name='servicenote_list'),
    
    # HTMX endpoint to load/refresh the DataTables content
    path('servicenotes/table/', ServiceNoteTablePartialView.as_view(), name='servicenote_table'),

    # Endpoints for CRUD operations, primarily accessed via HTMX in modals
    path('servicenotes/add/', ServiceNoteCreateView.as_view(), name='servicenote_add'),
    path('servicenotes/edit/<int:pk>/', ServiceNoteUpdateView.as_view(), name='servicenote_edit'),
    path('servicenotes/delete/<int:pk>/', ServiceNoteDeleteView.as_view(), name='servicenote_delete'),
]
```

### 4.6 Tests
**File:** `inventory/tests.py`

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import ServiceNote
from .forms import ServiceNoteForm
from django.contrib.messages import get_messages
from datetime import datetime
from django.utils import timezone

class ServiceNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.servicenote1 = ServiceNote.objects.create(
            title='Test Service Note One',
            description='This is a comprehensive description for test note number one.',
            is_active=True,
            created_at=timezone.make_aware(datetime(2023, 1, 1, 10, 0, 0)),
            last_modified_at=timezone.make_aware(datetime(2023, 1, 1, 10, 0, 0))
        )
        cls.servicenote2 = ServiceNote.objects.create(
            title='Inactive Service Note',
            description='This note is currently inactive.',
            is_active=False,
            created_at=timezone.make_aware(datetime(2023, 2, 15, 14, 30, 0)),
            last_modified_at=timezone.make_aware(datetime(2023, 2, 15, 14, 30, 0))
        )
  
    def test_servicenote_creation(self):
        """Ensure a ServiceNote object can be created and its attributes are correct."""
        obj = ServiceNote.objects.get(note_id=self.servicenote1.note_id)
        self.assertEqual(obj.title, 'Test Service Note One')
        self.assertEqual(obj.description, 'This is a comprehensive description for test note number one.')
        self.assertTrue(obj.is_active)
        self.assertIsNotNone(obj.created_at)
        self.assertIsNotNone(obj.last_modified_at)
        
    def test_str_method(self):
        """Test the __str__ method returns the title."""
        self.assertEqual(str(self.servicenote1), 'Test Service Note One')
        # Test case where title might be empty (though form validation should prevent this)
        temp_note = ServiceNote.objects.create(title='', description='Note with no title')
        self.assertEqual(str(temp_note), f"Service Note {temp_note.note_id}")

    def test_get_absolute_url(self):
        """Test the get_absolute_url method returns the correct list URL."""
        self.assertEqual(self.servicenote1.get_absolute_url(), reverse('servicenote_list'))

    def test_toggle_active_status_method(self):
        """Test the toggle_active_status method correctly changes and saves active status."""
        # Test activation
        self.assertFalse(self.servicenote2.is_active)
        self.servicenote2.toggle_active_status()
        self.servicenote2.refresh_from_db() # Reload from DB to confirm change
        self.assertTrue(self.servicenote2.is_active)

        # Test deactivation
        self.assertTrue(self.servicenote1.is_active)
        self.servicenote1.toggle_active_status()
        self.servicenote1.refresh_from_db()
        self.assertFalse(self.servicenote1.is_active)

    def test_get_status_display_method(self):
        """Test the get_status_display method returns correct string."""
        self.assertEqual(self.servicenote1.get_status_display(), 'Active')
        self.assertEqual(self.servicenote2.get_status_display(), 'Inactive')

class ServiceNoteFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create an existing note for unique title tests
        cls.existing_note = ServiceNote.objects.create(title='Unique Test Title', description='Some text', is_active=True)

    def test_valid_form(self):
        """Test that a form with valid data is valid."""
        form = ServiceNoteForm(data={
            'title': 'New Service Note Title',
            'description': 'A description for a new note.',
            'is_active': True
        })
        self.assertTrue(form.is_valid(), form.errors.as_data())

    def test_title_required(self):
        """Test that the title field is required."""
        form = ServiceNoteForm(data={
            'title': '', # Empty title
            'description': 'Description',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors.keys())
        self.assertEqual(form.errors['title'][0], 'Service Note Title is required.')

    def test_title_unique_on_create(self):
        """Test that title must be unique when creating a new note."""
        form = ServiceNoteForm(data={
            'title': 'Unique Test Title', # This title already exists
            'description': 'Another description',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors.keys())
        self.assertEqual(form.errors['title'][0], 'A service note with this title already exists.')

    def test_title_unique_on_update_same_instance(self):
        """Test that unique validation allows the same title for the same instance during update."""
        form = ServiceNoteForm(instance=self.existing_note, data={
            'title': 'Unique Test Title', # Same title, same instance
            'description': 'Updated description',
            'is_active': True
        })
        self.assertTrue(form.is_valid(), form.errors.as_data())

    def test_title_unique_on_update_another_instance(self):
        """Test that unique validation fails if updating to another existing title."""
        ServiceNote.objects.create(title='Another Unique Title', description='xyz')
        
        form = ServiceNoteForm(instance=self.existing_note, data={
            'title': 'Another Unique Title', # Trying to change to an already existing title
            'description': 'Updated description',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors.keys())
        self.assertEqual(form.errors['title'][0], 'A service note with this title already exists.')


class ServiceNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create an existing note for all tests involving updates/deletions
        cls.servicenote1 = ServiceNote.objects.create(
            title='Existing Service Note for Tests',
            description='This is an existing note for view testing.',
            is_active=True
        )
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolation
        self.client = Client()
    
    def test_list_view_get(self):
        """Test the ServiceNote list view renders correctly and contains data."""
        response = self.client.get(reverse('servicenote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/servicenote/list.html')
        self.assertIn('servicenotes', response.context) # Check if context contains the list of notes
        self.assertContains(response, 'Service Notes Dashboard') # Check for page title
        self.assertContains(response, 'Existing Service Note for Tests') # Check if data is present

    def test_table_partial_view_get(self):
        """Test the HTMX partial view for the table content."""
        response = self.client.get(reverse('servicenote_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/servicenote/_servicenote_table.html')
        self.assertIn('servicenotes', response.context)
        self.assertContains(response, '<table id="servicenoteTable"') # Ensure DataTable element is present
        self.assertContains(response, 'Existing Service Note for Tests')

    def test_create_view_get(self):
        """Test the GET request for the ServiceNote creation form."""
        response = self.client.get(reverse('servicenote_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/servicenote/form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], ServiceNoteForm)
        self.assertContains(response, 'Add Service Note') # Check for modal title

    def test_create_view_post_success(self):
        """Test successful ServiceNote creation via a standard POST request."""
        data = {
            'title': 'New Service Note Created',
            'description': 'Description for a newly created note.',
            'is_active': True,
        }
        response = self.client.post(reverse('servicenote_add'), data, follow=True)
        self.assertEqual(response.status_code, 200) # Follows redirect to list view
        self.assertTemplateUsed(response, 'inventory/servicenote/list.html')
        self.assertTrue(ServiceNote.objects.filter(title='New Service Note Created').exists())
        
        # Verify success message
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Service Note added successfully!')

    def test_create_view_post_htmx_success(self):
        """Test successful ServiceNote creation via an HTMX POST request."""
        data = {
            'title': 'HTMX New Note Title',
            'description': 'Description sent via HTMX request.',
            'is_active': False,
        }
        headers = {'HTTP_HX_REQUEST': 'true'} # Mimic HTMX request header
        response = self.client.post(reverse('servicenote_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX
        self.assertEqual(response.headers['HX-Trigger'], 'refreshServiceNoteList') # Verify HTMX trigger
        self.assertTrue(ServiceNote.objects.filter(title='HTMX New Note Title').exists())
        
        # Messages should still be present in the request for subsequent display
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Service Note added successfully!')

    def test_create_view_post_invalid(self):
        """Test ServiceNote creation with invalid data (e.g., missing title)."""
        data = {
            'title': '', # Invalid: empty title
            'description': 'This note should not be created.',
            'is_active': True,
        }
        response = self.client.post(reverse('servicenote_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'inventory/servicenote/form.html')
        self.assertFalse(ServiceNote.objects.filter(description='This note should not be created.').exists())
        self.assertContains(response, 'Service Note Title is required.') # Check for form error message

    def test_update_view_get(self):
        """Test the GET request for updating an existing ServiceNote."""
        obj = self.servicenote1
        response = self.client.get(reverse('servicenote_edit', args=[obj.note_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/servicenote/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj) # Ensure form is pre-filled with correct instance
        self.assertContains(response, 'Edit Service Note')

    def test_update_view_post_success(self):
        """Test successful ServiceNote update via a standard POST request."""
        obj = self.servicenote1
        data = {
            'title': 'Updated Title for Service Note',
            'description': 'Description was updated.',
            'is_active': False,
        }
        response = self.client.post(reverse('servicenote_edit', args=[obj.note_id]), data, follow=True)
        self.assertEqual(response.status_code, 200)
        obj.refresh_from_db() # Reload the object to get updated data from the database
        self.assertEqual(obj.title, 'Updated Title for Service Note')
        self.assertFalse(obj.is_active)
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Service Note updated successfully!')

    def test_update_view_post_htmx_success(self):
        """Test successful ServiceNote update via an HTMX POST request."""
        obj = self.servicenote1
        data = {
            'title': 'HTMX Updated Note Title',
            'description': 'Updated via HTMX.',
            'is_active': True,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('servicenote_edit', args=[obj.note_id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshServiceNoteList')
        obj.refresh_from_db()
        self.assertEqual(obj.title, 'HTMX Updated Note Title')

    def test_delete_view_get(self):
        """Test the GET request for the ServiceNote deletion confirmation."""
        obj = self.servicenote1
        response = self.client.get(reverse('servicenote_delete', args=[obj.note_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/servicenote/confirm_delete.html')
        self.assertIn('servicenote', response.context)
        self.assertEqual(response.context['servicenote'], obj)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post_success(self):
        """Test successful ServiceNote deletion via a standard POST request."""
        # Create a new object to delete to avoid interfering with other tests' data
        obj_to_delete = ServiceNote.objects.create(title='Temporary Note for Deletion', description='abc')
        initial_count = ServiceNote.objects.count()
        response = self.client.post(reverse('servicenote_delete', args=[obj_to_delete.note_id]), follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(ServiceNote.objects.count(), initial_count - 1)
        self.assertFalse(ServiceNote.objects.filter(pk=obj_to_delete.note_id).exists()) # Verify it's gone
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Service Note deleted successfully!')

    def test_delete_view_post_htmx_success(self):
        """Test successful ServiceNote deletion via an HTMX DELETE request."""
        # Create a new object to delete to avoid interfering with other tests' data
        obj_to_delete = ServiceNote.objects.create(title='Temporary Note for HTMX Deletion', description='xyz')
        initial_count = ServiceNote.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Using client.delete() for HTTP DELETE method
        response = self.client.delete(reverse('servicenote_delete', args=[obj_to_delete.note_id]), **headers)
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX
        self.assertEqual(response.headers['HX-Trigger'], 'refreshServiceNoteList')
        self.assertEqual(ServiceNote.objects.count(), initial_count - 1)
        self.assertFalse(ServiceNote.objects.filter(pk=obj_to_delete.note_id).exists())
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Service Note deleted successfully!')
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Dynamic Updates:**
    -   The `list.html` acts as the main dashboard, which then uses `hx-get` to fetch the actual table content from `{% url 'servicenote_table' %}`. This table is refreshed using `hx-trigger="load, refreshServiceNoteList from:body"`. The `refreshServiceNoteList` event is custom and triggered by backend views after successful CRUD operations.
    -   All CRUD operations (Add, Edit, Delete) are initiated by buttons using `hx-get` to load forms or confirmation dialogs into a shared modal (`#modalContent`).
    -   Form submissions (`form.html`, `confirm_delete.html`) use `hx-post` or `hx-delete` with `hx-swap="none"`. This ensures the HTMX request is processed without attempting to swap content, allowing the `HX-Trigger` header from the server to manage subsequent actions.
    -   The `hx-on::after-request` attribute on the forms is crucial: it listens for the HTTP response (specifically `status == 204` for success) and then uses `_hyperscript` to close the modal, providing a seamless user experience.

-   **Alpine.js for UI State Management:**
    -   In this specific setup, `_hyperscript` (`_="on click ..."`) is primarily used directly within the HTML for simple DOM manipulation, like toggling the modal's `is-active` class and managing its `scale` and `opacity` for smooth transitions.
    -   Alpine.js (e.g., `x-data`, `x-show`, `@keydown.escape.window`) is included in the modal structure (`#modal`) for enhanced client-side reactivity, such as closing the modal on `Esc` key press, even though `_hyperscript` handles the primary click interactions. This demonstrates how both can coexist, with `_hyperscript` for direct action-response and Alpine.js for broader UI state.

-   **DataTables for List Views:**
    -   The `_servicenote_table.html` partial is responsible for rendering the actual `<table>` element. Crucially, it contains a `<script>` block that initializes `$('#servicenoteTable').DataTable()` immediately after the table content is loaded by HTMX. This makes client-side features like searching, sorting, and pagination available, offering a rich interactive experience without server-side processing for these features.

-   **No Full Page Reloads:**
    -   The entire flow (loading the table, opening forms, submitting data, confirming deletions) is designed to operate via HTMX, minimizing full page reloads. This provides a "single-page application" feel while keeping the backend logic firmly in Django.

-   **HX-Trigger Responses:**
    -   Django's `CreateView`, `UpdateView`, and `DeleteView` explicitly detect HTMX requests (`self.request.headers.get('HX-Request')`). If an HTMX request is detected and the operation is successful, they return an `HttpResponse(status=204)` (No Content) along with an `HX-Trigger` header (`'HX-Trigger': 'refreshServiceNoteList'`). This header signals the client-side HTMX to trigger the specified event (`refreshServiceNoteList`) on the `<body>`, which then causes the `servicenoteTable-container` to re-fetch its content, refreshing the list automatically.