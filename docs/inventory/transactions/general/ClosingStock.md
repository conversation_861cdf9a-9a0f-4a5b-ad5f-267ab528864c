## ASP.NET to Django Conversion Script: Closing Stock Module

This document outlines a strategic plan to modernize the existing ASP.NET 'Closing Stock' module into a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation to systematically transform your legacy code into a modern, highly efficient Django 5.0+ solution, emphasizing a 'fat model, thin view' architecture, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for advanced data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`ClosingStock`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code utilizes a `SqlDataSource` named `LocalSqlServer` which interacts with the database.
- The `SelectCommand` is `SELECT * FROM [tblInv_ClosingStck] order by [Id] desc`.
- The `InsertCommand` is `INSERT INTO [tblInv_ClosingStck] ([FromDt], [ToDt],[ClStock]) VALUES (@FromDt, @ToDt,@Stock)`.
- The `DeleteCommand` is `DELETE FROM [tblInv_ClosingStck] WHERE [Id] = @Id`.
- Parameters reveal data types: `FromDt` (String, inferred as Date), `ToDt` (String, inferred as Date), `Stock` (Double, inferred as Float), `Id` (Int32).

**Identified Schema:**
- **TABLE_NAME**: `tblInv_ClosingStck`
- **Columns**:
    - `Id`: Primary Key, Integer
    - `FromDt`: Date/String (will be `DateField` in Django)
    - `ToDt`: Date/String (will be `DateField` in Django)
    - `ClStock`: Double (will be `FloatField` in Django)

### Step 2: Identify Backend Functionality

**Analysis:**
The C# code-behind handles the backend logic:
- **Create (Add):** The `GridView1_RowCommand` method, specifically for `CommandName="Add"` (from the footer row) and `CommandName="Add1"` (from the empty data template), retrieves values from `TextBox` controls (`txtFrom`, `txtTo`, `txtClStk`) and inserts them into `tblInv_ClosingStck` via `LocalSqlServer.Insert()`. Basic null/empty string check is performed.
- **Read:** Data is read from `tblInv_ClosingStck` and displayed in `GridView1` (implicitly handled by `SqlDataSource`).
- **Update:** While `GridView1_RowUpdated` is present, there's no `UpdateCommand` in `SqlDataSource` or explicit update logic in the C# `RowCommand` for an "Edit" action. However, the `GridView1_RowDataBound` includes `LinkButton edit = (LinkButton)e.Row.Cells[1].Controls[0];` with an `onclick` for `confirmationUpdate();`, indicating an update functionality *was* intended or present. For a complete CRUD migration, we will implement an Update feature.
- **Delete:** The `GridView1_RowDeleted` method and `DeleteCommand` in `SqlDataSource` confirm deletion functionality.
- **Validation:** `RequiredFieldValidator` controls are used in the ASPX, implying basic presence validation. `CalendarExtender` suggests date format validation.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET `.aspx` file primarily uses `asp:GridView` for data display and input.
- **Data Display:** `asp:GridView` (`GridView1`) displays `SN` (row index), `FromDt`, `ToDt`, `ClStock`. `DataKeyNames="Id"` identifies the primary key.
- **Data Input:**
    - `txtFrom`: `asp:TextBox` with `cc1:CalendarExtender` for date input.
    - `txtTo`: `asp:TextBox` with `cc1:CalendarExtender` for date input.
    - `txtClStk`: `asp:TextBox` for numerical input (`Closing Stock`).
- **Actions:**
    - `btnInsert`: `asp:Button` for adding new records (in footer and empty template).
    - `CommandField` with `ShowDeleteButton="True"`: Provides a delete link.
    - Implied `LinkButton` for 'Edit' (based on C# code-behind).
- **Styling:** `CssClass="yui-datatable-theme"`, `redbox`, `box3`, `fontcsswhite`, `cal_Theme2`. These will be replaced by Tailwind CSS classes.
- **Client-side scripts:** `PopUpMsg.js`, `loadingNotifier.js` and `confirmationAdd()`, `confirmationDelete()`, `confirmationUpdate()`. These will be replaced by HTMX and Alpine.js.

---

### Step 4: Generate Django Code

We will create a new Django app, let's call it `inventory`.

#### 4.1 Models (`inventory/models.py`)

This model directly maps to `tblInv_ClosingStck`. The business logic for `ClosingStock` will reside here.

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class ClosingStock(models.Model):
    """
    Represents the closing stock for a specific period.
    Maps to the existing tblInv_ClosingStck table.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    from_dt = models.DateField(db_column='FromDt', verbose_name=_("From Date"))
    to_dt = models.DateField(db_column='ToDt', verbose_name=_("To Date"))
    closing_stock = models.FloatField(db_column='ClStock', verbose_name=_("Closing Stock"))

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblInv_ClosingStck'
        verbose_name = _("Closing Stock Record")
        verbose_name_plural = _("Closing Stock Records")
        ordering = ['-id'] # Matches the 'order by [Id] desc' from ASP.NET

    def __str__(self):
        """
        Returns a string representation of the closing stock record.
        """
        return f"Closing Stock from {self.from_dt.strftime('%d-%m-%Y')} to {self.to_dt.strftime('%d-%m-%Y')}: {self.closing_stock}"

    def clean(self):
        """
        Custom validation for the model instance.
        Ensures 'from_dt' is not after 'to_dt'.
        """
        if self.from_dt and self.to_dt and self.from_dt > self.to_dt:
            raise ValidationError(
                {'from_dt': _("From Date cannot be after To Date."),
                 'to_dt': _("To Date cannot be before From Date.")}
            )

    def save(self, *args, **kwargs):
        """
        Overrides the save method to include model-level validation.
        """
        self.full_clean()  # Calls clean() method for validation
        super().save(*args, **kwargs)

    @classmethod
    def get_latest_record(cls):
        """
        Returns the latest closing stock record, if any.
        """
        return cls.objects.first() # Due to default ordering by '-id'
```

#### 4.2 Forms (`inventory/forms.py`)

This form handles validation and input for `ClosingStock` records.

```python
from django import forms
from .models import ClosingStock

class ClosingStockForm(forms.ModelForm):
    """
    Form for creating and updating ClosingStock records.
    """
    class Meta:
        model = ClosingStock
        fields = ['from_dt', 'to_dt', 'closing_stock']
        widgets = {
            'from_dt': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date', # HTML5 date input for browser pickers
                'onfocus': "(this.type='date')", # Ensure date picker shows on focus
                'onblur': "(this.type='date')"
            }, format='%Y-%m-%d'), # Use YYYY-MM-DD for HTML5 date input, displayed as dd-MM-yyyy via template
            'to_dt': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date',
                'onfocus': "(this.type='date')",
                'onblur': "(this.type='date')"
            }, format='%Y-%m-%d'),
            'closing_stock': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'number',
                'step': 'any' # Allows float values
            }),
        }

    def clean(self):
        """
        Override clean to add cross-field validation for dates.
        Model's clean method is called by full_clean() in save.
        """
        cleaned_data = super().clean()
        from_dt = cleaned_data.get('from_dt')
        to_dt = cleaned_data.get('to_dt')

        if from_dt and to_dt and from_dt > to_dt:
            self.add_error('from_dt', "From Date cannot be after To Date.")
            self.add_error('to_dt', "To Date cannot be before From Date.")
        return cleaned_data

```

#### 4.3 Views (`inventory/views.py`)

These views handle the CRUD operations and partial content loading via HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ClosingStock
from .forms import ClosingStockForm

class ClosingStockListView(ListView):
    """
    Displays the main page for listing closing stock records.
    This view just renders the container for the HTMX-loaded table.
    """
    model = ClosingStock
    template_name = 'inventory/closingstock/list.html'
    context_object_name = 'closing_stock_records' # Not directly used in list.html, but good practice.

class ClosingStockTablePartialView(ListView):
    """
    Returns only the table HTML for HTMX to refresh the list.
    """
    model = ClosingStock
    template_name = 'inventory/closingstock/_closingstock_table.html'
    context_object_name = 'closing_stock_records'
    # No pagination needed here as DataTables handles it client-side.

class ClosingStockCreateView(CreateView):
    """
    Handles creation of new closing stock records via a modal form.
    Responds to HTMX requests with 204 No Content and a trigger header.
    """
    model = ClosingStock
    form_class = ClosingStockForm
    template_name = 'inventory/closingstock/_closingstock_form.html'
    success_url = reverse_lazy('closingstock_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Business logic can be added to the model's save method or clean() method
        response = super().form_valid(form)
        messages.success(self.request, 'Closing Stock record added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing with response body
                headers={
                    'HX-Trigger': 'refreshClosingStockList', # Custom event to refresh the table
                    'HX-Refresh': 'true' # Full page refresh if needed, for messages
                }
            )
        return response

    def form_invalid(self, form):
        # Render the form again with errors for HTMX
        return self.render_to_response(self.get_context_data(form=form))

class ClosingStockUpdateView(UpdateView):
    """
    Handles updating existing closing stock records via a modal form.
    Responds to HTMX requests with 204 No Content and a trigger header.
    """
    model = ClosingStock
    form_class = ClosingStockForm
    template_name = 'inventory/closingstock/_closingstock_form.html'
    context_object_name = 'closing_stock_record'
    success_url = reverse_lazy('closingstock_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Closing Stock record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshClosingStockList',
                    'HX-Refresh': 'true'
                }
            )
        return response

    def form_invalid(self, form):
        return self.render_to_response(self.get_context_data(form=form))

class ClosingStockDeleteView(DeleteView):
    """
    Handles deletion of closing stock records via a confirmation modal.
    Responds to HTMX requests with 204 No Content and a trigger header.
    """
    model = ClosingStock
    template_name = 'inventory/closingstock/_closingstock_confirm_delete.html'
    context_object_name = 'closing_stock_record'
    success_url = reverse_lazy('closingstock_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Closing Stock record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshClosingStockList',
                    'HX-Refresh': 'true'
                }
            )
        return response

```

#### 4.4 Templates (`inventory/templates/inventory/closingstock/`)

**`list.html`** (Main page for Closing Stock):

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Closing Stock Records</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'closingstock_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Closing Stock
        </button>
    </div>
    
    <div id="closingStockTable-container"
         hx-trigger="load, refreshClosingStockList from:body"
         hx-get="{% url 'closingstock_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Closing Stock data...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 transition-transform duration-200 ease-out"
             _="on modal.htmx:afterOnLoad add .scale-100 remove .scale-95">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js components here if necessary, though HTMX handles most interactions -->
<script>
    // Example: Alpine.js for a more complex modal state if HTMX isn't enough
    // document.addEventListener('alpine:init', () => {
    //     Alpine.data('modalController', () => ({
    //         isOpen: false,
    //         open() { this.isOpen = true },
    //         close() { this.isOpen = false },
    //     }));
    // });
</script>
{% endblock %}
```

**`_closingstock_table.html`** (Partial template for the DataTables table):

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="closingStockTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right">Closing Stock</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in closing_stock_records %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.from_dt|date:"d-m-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.to_dt|date:"d-m-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.closing_stock|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'closingstock_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'closingstock_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">No closing stock records found. Click "Add New Closing Stock" to add one.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the partial is loaded
    $(document).ready(function() {
        $('#closingStockTable').DataTable({
            "pageLength": 10, // Default page length
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for number of entries
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable ordering for SN and Actions columns
            ]
        });
    });
</script>
```

**`_closingstock_form.html`** (Partial template for Create/Update forms):

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Closing Stock Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <p>{{ error }}</p>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
            
            {% if form.non_field_errors %}
            <div class="mt-4 text-sm text-red-600">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                <span id="form-spinner" class="htmx-indicator animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
                Save Record
            </button>
        </div>
    </form>
</div>
```

**`_closingstock_confirm_delete.html`** (Partial template for delete confirmation):

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the closing stock record for the period: <span class="font-bold">{{ closing_stock_record.from_dt|date:"d-m-Y" }} to {{ closing_stock_record.to_dt|date:"d-m-Y" }}</span> with stock value <span class="font-bold">{{ closing_stock_record.closing_stock|floatformat:2 }}</span>?</p>
    
    <div class="flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'closingstock_delete' closing_stock_record.pk %}"
            hx-swap="none"
            hx-target="body"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    ClosingStockListView, 
    ClosingStockTablePartialView,
    ClosingStockCreateView, 
    ClosingStockUpdateView, 
    ClosingStockDeleteView
)

urlpatterns = [
    path('closing-stock/', ClosingStockListView.as_view(), name='closingstock_list'),
    path('closing-stock/table/', ClosingStockTablePartialView.as_view(), name='closingstock_table'), # HTMX partial
    path('closing-stock/add/', ClosingStockCreateView.as_view(), name='closingstock_add'),
    path('closing-stock/edit/<int:pk>/', ClosingStockUpdateView.as_view(), name='closingstock_edit'),
    path('closing-stock/delete/<int:pk>/', ClosingStockDeleteView.as_view(), name='closingstock_delete'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import ClosingStock
from .forms import ClosingStockForm

class ClosingStockModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.record1 = ClosingStock.objects.create(
            from_dt=timezone.now().date() - timedelta(days=30),
            to_dt=timezone.now().date() - timedelta(days=1),
            closing_stock=150.75
        )
        cls.record2 = ClosingStock.objects.create(
            from_dt=timezone.now().date() - timedelta(days=60),
            to_dt=timezone.now().date() - timedelta(days=31),
            closing_stock=200.00
        )
  
    def test_closing_stock_creation(self):
        obj = ClosingStock.objects.get(id=self.record1.id)
        self.assertEqual(obj.from_dt, timezone.now().date() - timedelta(days=30))
        self.assertEqual(obj.to_dt, timezone.now().date() - timedelta(days=1))
        self.assertEqual(obj.closing_stock, 150.75)
        self.assertEqual(str(obj), f"Closing Stock from {obj.from_dt.strftime('%d-%m-%Y')} to {obj.to_dt.strftime('%d-%m-%Y')}: {obj.closing_stock}")
        
    def test_from_dt_label(self):
        field_label = self.record1._meta.get_field('from_dt').verbose_name
        self.assertEqual(field_label, 'From Date')
        
    def test_to_dt_label(self):
        field_label = self.record1._meta.get_field('to_dt').verbose_name
        self.assertEqual(field_label, 'To Date')

    def test_closing_stock_label(self):
        field_label = self.record1._meta.get_field('closing_stock').verbose_name
        self.assertEqual(field_label, 'Closing Stock')

    def test_model_ordering(self):
        # Default ordering is by '-id', so latest created record should be first
        latest_record = ClosingStock.objects.first()
        self.assertEqual(latest_record.id, self.record2.id)

    def test_get_latest_record(self):
        latest = ClosingStock.get_latest_record()
        self.assertEqual(latest, self.record2) # Due to descending ID order

    def test_model_validation_dates(self):
        # Test case where from_dt is after to_dt
        with self.assertRaises(Exception) as cm: # Expect ValidationError, but it might be wrapped
            ClosingStock.objects.create(
                from_dt=timezone.now().date(),
                to_dt=timezone.now().date() - timedelta(days=10),
                closing_stock=100.00
            )
        # Check that the exception message indicates a validation error.
        # Specific assertion depends on how Django's ValidationError is raised in your environment.
        self.assertTrue('From Date cannot be after To Date.' in str(cm.exception))


class ClosingStockViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.record1 = ClosingStock.objects.create(
            from_dt=timezone.now().date() - timedelta(days=30),
            to_dt=timezone.now().date() - timedelta(days=1),
            closing_stock=150.75
        )
        cls.record2 = ClosingStock.objects.create(
            from_dt=timezone.now().date() - timedelta(days=60),
            to_dt=timezone.now().date() - timedelta(days=31),
            closing_stock=200.00
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('closingstock_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/closingstock/list.html')
        # We don't check for closing_stock_records in context directly in list view
        # because the table content is loaded via HTMX.

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('closingstock_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/closingstock/_closingstock_table.html')
        self.assertIn('closing_stock_records', response.context)
        self.assertContains(response, self.record1.from_dt.strftime('%d-%m-%Y'))
        self.assertContains(response, str(self.record2.closing_stock))
        
    def test_create_view_get(self):
        response = self.client.get(reverse('closingstock_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/closingstock/_closingstock_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_valid(self):
        new_from_dt = timezone.now().date() - timedelta(days=90)
        new_to_dt = timezone.now().date() - timedelta(days=80)
        data = {
            'from_dt': new_from_dt.strftime('%Y-%m-%d'),
            'to_dt': new_to_dt.strftime('%Y-%m-%d'),
            'closing_stock': 300.50,
        }
        response = self.client.post(reverse('closingstock_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(ClosingStock.objects.filter(from_dt=new_from_dt).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshClosingStockList')
        self.assertEqual(response.headers['HX-Refresh'], 'true')

    def test_create_view_post_invalid(self):
        data = { # Invalid dates
            'from_dt': (timezone.now().date()).strftime('%Y-%m-%d'),
            'to_dt': (timezone.now().date() - timedelta(days=1)).strftime('%Y-%m-%d'),
            'closing_stock': 100.00,
        }
        response = self.client.post(reverse('closingstock_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'inventory/closingstock/_closingstock_form.html')
        self.assertContains(response, "From Date cannot be after To Date.")
        self.assertFalse(ClosingStock.objects.filter(closing_stock=100.00).exists())
        
    def test_update_view_get(self):
        obj = self.record1
        response = self.client.get(reverse('closingstock_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/closingstock/_closingstock_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_valid(self):
        obj = self.record1
        updated_stock = 999.99
        data = {
            'from_dt': obj.from_dt.strftime('%Y-%m-%d'),
            'to_dt': obj.to_dt.strftime('%Y-%m-%d'),
            'closing_stock': updated_stock,
        }
        response = self.client.post(reverse('closingstock_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        obj.refresh_from_db()
        self.assertEqual(obj.closing_stock, updated_stock)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshClosingStockList')
        self.assertEqual(response.headers['HX-Refresh'], 'true')

    def test_delete_view_get(self):
        obj = self.record1
        response = self.client.get(reverse('closingstock_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/closingstock/_closingstock_confirm_delete.html')
        self.assertIn('closing_stock_record', response.context)
        self.assertEqual(response.context['closing_stock_record'], obj)
        
    def test_delete_view_post(self):
        obj_to_delete = self.record1
        response = self.client.post(reverse('closingstock_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertFalse(ClosingStock.objects.filter(id=obj_to_delete.id).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshClosingStockList')
        self.assertEqual(response.headers['HX-Refresh'], 'true')

```

### Step 5: HTMX and Alpine.js Integration

**HTMX for dynamic interactions:**
- **List View Refresh:** The `closingStockTable-container` div uses `hx-trigger="load, refreshClosingStockList from:body"` and `hx-get="{% url 'closingstock_table' %}"` to load the table on page load and whenever a `refreshClosingStockList` custom event is fired from anywhere on the body.
- **Form Loading (Modal):** Buttons for "Add New", "Edit", and "Delete" use `hx-get` to fetch their respective forms (`_closingstock_form.html` or `_closingstock_confirm_delete.html`) into the `#modalContent` div.
- **Form Submission:** Forms use `hx-post` to submit data. On successful submission (`204 No Content` response from Django views), the `HX-Trigger: refreshClosingStockList` header is sent back, which causes the main table to reload.
- **Loading Indicators:** A simple `htmx-indicator` class on a spinner is used to show loading state during form submissions.

**Alpine.js/Hyperscript for UI State:**
- The modal visibility is managed using Hyperscript (`_`).
    - `on click add .is-active to #modal`: When an `hx-get` button is clicked, it adds the `is-active` class to the modal, making it visible (assuming `is-active` overrides `hidden`).
    - `on click if event.target.id == 'modal' remove .is-active from me`: Clicking outside the `modalContent` (i.e., on the modal background) closes it.
    - `on click remove .is-active from #modal`: The "Cancel" button in forms explicitly closes the modal.
    - `on modal.htmx:afterOnLoad add .scale-100 remove .scale-95`: Simple animation for modal opening.

**DataTables for List Views:**
- The `_closingstock_table.html` partial explicitly initializes DataTables on the `closingStockTable` HTML element using jQuery. This script runs every time the partial is loaded by HTMX, ensuring DataTables functionality persists across refreshes.
- It provides client-side searching, sorting, and pagination, mirroring the functionality of the ASP.NET GridView.
- CDN links for jQuery and DataTables should be included in `core/base.html`.

**Ensuring DRY Template Inheritance:**
- All module-specific templates (`list.html`) extend `core/base.html`.
- Partial templates (`_closingstock_table.html`, `_closingstock_form.html`, `_closingstock_confirm_delete.html`) are used to render specific components that are loaded dynamically via HTMX, avoiding duplication of form or table structures.

**Tailwind CSS:**
- All HTML elements in the templates are styled using Tailwind CSS utility classes, providing a modern and responsive design without custom CSS.

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Closing Stock module to a modern Django application, focusing on automation-driven approaches and business benefits.