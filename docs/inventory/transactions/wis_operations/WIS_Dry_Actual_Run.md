This document outlines a comprehensive plan to modernize the provided ASP.NET application code into a Django-based solution, leveraging AI-assisted automation strategies. The focus is on creating a modular, maintainable, and high-performance system using Django's best practices, HTMX, Alpine.js, and DataTables.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with at least two key entities: `tblSD_WO_Category` and an inferred `WorkOrder` table that the stored procedure `Sp_WIS_ActualRun_Grid` primarily queries.

**Identified Tables and Columns:**
1.  **`tblSD_WO_Category`**:
    *   `CId` (Integer, inferred Primary Key from `DataValueField="CId"`)
    *   `Symbol` (String, from `Symbol+' - '+CName` concatenation)
    *   `CName` (String, from `Symbol+' - '+CName` concatenation)
2.  **`tblSD_WorkOrder` (Inferred for Work Orders)**:
    *   `Id` (Integer, inferred Primary Key from `lblId` in `GridView2`)
    *   `WONo` (String, from `lblwono` and `TxtWONo`, used in `loadgrid` filter `x`)
    *   `SysDate` (Date, from `lbldate`)
    *   `PrjTitle` (String, from `lblprjtitle`)
    *   `CId` (Integer, Foreign Key to `tblSD_WO_Category`, used in `loadgrid` filter `Z` and `DrpWOType.SelectedValue`)
    *   `CompId` (Integer, used as `@CompId` parameter to stored procedure, from `Session["compid"]`)
    *   `CloseOpen` (Boolean/Integer, filtered by `CloseOpen=0` in `loadgrid` `y`)

### Step 2: Identify Backend Functionality

The ASP.NET page primarily acts as a "Read" (display) and "Action" (redirect) interface for Work Orders.

**Operations and Logic:**

*   **Read (Work Order Categories)**: Populating `DrpWOType` from `tblSD_WO_Category` at `Page_Load`.
*   **Read (Work Orders)**:
    *   Initial loading of the `GridView2` by calling `loadgrid`.
    *   Filtering `GridView2` based on `DrpWOType` selection (`DrpWOType_SelectedIndexChanged`) and `TxtWONo` input (`Button1_Click`).
    *   Pagination of `GridView2` (`GridView2_PageIndexChanging`).
    *   The core data retrieval is through a stored procedure `Sp_WIS_ActualRun_Grid`, which takes `CompId`, category filter (`Z`), WO number filter (`x`), and a fixed `CloseOpen=0` filter (`y`).
*   **Actions (Redirections)**:
    *   "Dry Run" button (`CommandName="add"`): Redirects to `WIS_ActualRun_Assembly.aspx` with `WONo`.
    *   "View" button (`CommandName="view"`): Redirects to one of three different pages (`WIS_View_TransWise.aspx`, `TotalIssueAndShortage_Print.aspx`, `TotalShortage_Print.aspx`) based on the `drpIssueShortage` dropdown selection, passing `WONo` and other parameters.
*   **Session Management**: `CompId`, `FinYearId`, `SId` (`username`) are retrieved from session.
*   **Client-Side Alerts**: Displaying messages like "No records found" using `ClientScript.RegisterStartupScript`.

### Step 3: Infer UI Components

The ASP.NET page uses standard Web Forms controls for user interaction and data display.

**Mapped UI Components to Django/HTMX/Tailwind:**

*   **Master Page / ContentPlaceHolders**: Replaced by Django's template inheritance (`{% extends 'core/base.html' %}`).
*   **`UpdatePanel`**: Replaced by HTMX for partial page updates (e.g., refreshing the data table).
*   **`DrpWOType` (DropDownList)**: Django `forms.ModelChoiceField` within a search form, rendered as an HTML `<select>` with HTMX `hx-trigger="change"`.
*   **`TxtWONo` (TextBox)**: Django `forms.CharField` within a search form, rendered as an HTML `<input type="text">`.
*   **`Button1` (Button, "Search")**: HTML `<button type="submit">` within the HTMX-driven search form.
*   **`GridView2`**: Replaced by a standard HTML `<table>` combined with the DataTables.js library for client-side pagination, sorting, and filtering.
    *   "SN" column: Django template `forloop.counter`.
    *   "Dry Run" button: HTML `<button>` with HTMX `hx-post` for the action.
    *   "Issue/Shortage" dropdown: HTML `<select>` within each table row. Its value is sent with the "View" action using `hx-include`.
    *   "View" button: HTML `<button>` with HTMX `hx-post` for the action.
    *   Data columns (`WONo`, `Date`, `Project Title`): Direct display of model fields.
*   **Styling**: `Css/yui-datatable.css`, `Css/StyleSheet.css` will be replaced by Tailwind CSS utility classes and DataTables' default styling.
*   **JavaScript**: `loadingNotifier.js`, `PopUpMsg.js` replaced by HTMX indicators, Django messages framework, and potentially minimal Alpine.js for specific UI state (though not strictly required for this page's functionality).

### Step 4: Generate Django Code

We will create a new Django application (e.g., `inventory`) to house this functionality.

#### 4.1 Models (`inventory/models.py`)

Models are mapped directly to the existing database tables using `managed = False`. A custom manager is used for the `WorkOrder` model to encapsulate the data retrieval logic that mirrors the `Sp_WIS_ActualRun_Grid` stored procedure's filtering parameters.

```python
from django.db import models
from django.db.models import Q # For potential complex queries

class WOCategory(models.Model):
    """
    Maps to the tblSD_WO_Category table, representing Work Order Categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class WorkOrderManager(models.Manager):
    """
    Custom manager for WorkOrder to encapsulate specific filtering logic,
    mimicking the Sp_WIS_ActualRun_Grid stored procedure's parameters.
    """
    def get_dry_run_list(self, company_id, wo_no_filter=None, category_id_filter=None):
        queryset = self.filter(company_id=company_id, close_open=False)

        if wo_no_filter:
            # Using icontains for partial search, similar to how text box search might function
            queryset = queryset.filter(won_o__icontains=wo_no_filter)
        if category_id_filter:
            queryset = queryset.filter(category__cid=category_id_filter)

        # Order by SysDate descending, then WONo for consistent results
        return queryset.order_by('-sys_date', 'won_o')

class WorkOrder(models.Model):
    """
    Maps to the inferred tblSD_WorkOrder table, representing individual Work Orders.
    Contains fields identified from the GridView and the stored procedure parameters.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    won_o = models.CharField(db_column='WONo', max_length=50, verbose_name="WO No")
    sys_date = models.DateField(db_column='SysDate', verbose_name="Date")
    prj_title = models.CharField(db_column='PrjTitle', max_length=255, verbose_name="Project Title")
    
    # Foreign key to WOCategory, inferred from CId parameter in loadgrid
    category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', related_name='work_orders', verbose_name="WO Category")
    
    # company_id and close_open are inferred from stored procedure parameters
    company_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")
    close_open = models.BooleanField(db_column='CloseOpen', default=False, verbose_name="Closed/Open Status")

    objects = WorkOrderManager() # Attach the custom manager

    class Meta:
        managed = False # Crucial for mapping to existing database tables
        db_table = 'tblSD_WorkOrder' # Inferred table name
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.won_o} - {self.prj_title}"

    # Business logic methods can be added here if complex actions were involved
    # For example, if "Dry Run" involved more than just a redirect.
```

#### 4.2 Forms (`inventory/forms.py`)

A simple form is defined to handle the search/filter inputs (Work Order Category and Work Order Number). This form is for display and validation, not for CRUD operations on `WorkOrder` objects directly.

```python
from django import forms
from .models import WOCategory

class WorkOrderSearchForm(forms.Form):
    """
    Form for filtering Work Orders based on WO Category and WO Number.
    """
    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('cname'),
        required=False,
        empty_label="WO Category", # Matches ASP.NET's "WO Category" item
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': "{% url 'inventory:workorder_table_partial' %}", # Trigger HTMX on change
            'hx-target': "#workOrderTable-container",
            'hx-swap': "innerHTML",
            'hx-trigger': "change", # Explicitly trigger on change
            'hx-include': "#search-form", # Include other form fields
        })
    )
    wo_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'WO No',
        })
    )
```

#### 4.3 Views (`inventory/views.py`)

Views are kept thin, adhering to the 15-line limit for logic, delegating data retrieval to the model manager. `TemplateView` is used for the main page, `ListView` for the HTMX-driven table partial, and `View` for handling action redirects.

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponseRedirect, HttpResponse
import random # For generating random key as in ASP.NET's fun.GetRandomAlphaNumeric()

from .models import WorkOrder, WOCategory
from .forms import WorkOrderSearchForm

class WISDryActualRunView(TemplateView):
    """
    Renders the main page for 'WIS Dry/Actual Run', including the search form.
    The table content is loaded dynamically via HTMX.
    """
    template_name = 'inventory/wis_dry_actual_run/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with any existing query parameters
        context['form'] = WorkOrderSearchForm(self.request.GET or None)
        return context

class WorkOrderTablePartialView(ListView):
    """
    An HTMX-driven view that renders only the Work Order table.
    It applies filters based on GET parameters, mimicking the ASP.NET loadgrid logic.
    """
    model = WorkOrder
    template_name = 'inventory/wis_dry_actual_run/_workorder_table.html'
    context_object_name = 'work_orders'
    paginate_by = 17 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Simulate Session["compid"]. In a production environment, this would come from
        # the authenticated user's profile or a site-wide configuration.
        company_id = self.request.session.get('compid', 1) # Default to 1 if not found
        
        # Get form data from GET request
        form = WorkOrderSearchForm(self.request.GET)
        wo_no_filter = None
        category_id_filter = None

        if form.is_valid():
            wo_no_filter = form.cleaned_data.get('wo_no')
            # If a WOCategory object is selected, extract its primary key (cid)
            selected_category = form.cleaned_data.get('wo_category')
            if selected_category:
                category_id_filter = selected_category.cid

        # Use the custom manager method to fetch filtered data
        return WorkOrder.objects.get_dry_run_list(
            company_id=company_id,
            wo_no_filter=wo_no_filter,
            category_id_filter=category_id_filter
        )

    # No need to override render_to_response as ListView naturally renders its template.
    # HTMX handles how this partial is inserted into the main page.

class DryRunActionView(View):
    """
    Handles the 'Dry Run' action, redirecting to the external assembly page.
    """
    def post(self, request, pk):
        try:
            work_order = WorkOrder.objects.get(pk=pk)
            # In a real migration, this logic might be moved to a new Django view
            # that performs the dry run operation, rather than an external redirect.
            
            # Display a success message
            messages.success(request, f"Dry Run initiated for WO No: {work_order.won_o}")
            
            # Redirect to the legacy ASP.NET URL for initial phased migration
            redirect_url = f"/legacy/Module/Inventory/Transactions/WIS_ActualRun_Assembly.aspx?WONo={work_order.won_o}&ModId=9&SubModId=53"
            return HttpResponseRedirect(redirect_url)
        except WorkOrder.DoesNotExist:
            messages.error(request, "Work Order not found.")
            return HttpResponseRedirect(reverse_lazy('inventory:wis_dry_actual_run_list'))
        except Exception as e:
            messages.error(request, f"An error occurred during Dry Run: {e}")
            return HttpResponseRedirect(reverse_lazy('inventory:wis_dry_actual_run_list'))

class ViewActionView(View):
    """
    Handles the 'View' action, redirecting to different external view/print pages
    based on the selected type from the dropdown.
    """
    def post(self, request, pk):
        try:
            work_order = WorkOrder.objects.get(pk=pk)
            # The dropdown value (drpIssueShortage) is sent via hx-include in the POST request
            view_type = int(request.POST.get('view_type', 0))
            
            # Simulate getRandomAlphaNumeric() for 'Key' parameter
            get_random_key = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=10))

            redirect_url = reverse_lazy('inventory:wis_dry_actual_run_list') # Default fallback
            
            # Construct the redirect URL based on view_type
            if view_type == 0:
                redirect_url = f"/legacy/Module/Inventory/Transactions/WIS_View_TransWise.aspx?WONo={work_order.won_o}&ModId=9&SubModId=53&Type={view_type}&status=0"
            elif view_type == 1:
                redirect_url = f"/legacy/Module/Inventory/Transactions/TotalIssueAndShortage_Print.aspx?WONo={work_order.won_o}&Key={get_random_key}&ModId=9&SubModId=53&Type={view_type}&status=0"
            elif view_type == 2:
                redirect_url = f"/legacy/Module/Inventory/Transactions/TotalShortage_Print.aspx?WONo={work_order.won_o}&Key={get_random_key}&ModId=9&SubModId=53&Type={view_type}&status=0"
            
            messages.success(request, f"Viewing {work_order.won_o} (Type: {view_type}).")
            return HttpResponseRedirect(redirect_url)

        except WorkOrder.DoesNotExist:
            messages.error(request, "Work Order not found.")
            return HttpResponseRedirect(reverse_lazy('inventory:wis_dry_actual_run_list'))
        except ValueError: # If view_type cannot be converted to int
            messages.error(request, "Invalid view type selected.")
            return HttpResponseRedirect(reverse_lazy('inventory:wis_dry_actual_run_list'))
        except Exception as e:
            messages.error(request, f"An error occurred during View action: {e}")
            return HttpResponseRedirect(reverse_lazy('inventory:wis_dry_actual_run_list'))

```

#### 4.4 Templates (`inventory/templates/inventory/wis_dry_actual_run/`)

Two templates are needed: one for the main page (`list.html`) and one for the HTMX-loaded table partial (`_workorder_table.html`).

**`inventory/templates/inventory/wis_dry_actual_run/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}WIS Dry/Actual Run{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-3 rounded-t-lg mb-4">
        <h2 class="text-xl font-bold">WIS Dry/Actual Run</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form id="search-form" hx-get="{% url 'inventory:workorder_table_partial' %}" hx-target="#workOrderTable-container" hx-swap="innerHTML" hx-trigger="submit">
            <div class="flex flex-wrap items-center space-x-4">
                <div class="flex-grow">
                    <label for="{{ form.wo_category.id_for_label }}" class="sr-only">WO Category</label>
                    {{ form.wo_category }}
                </div>
                <div class="flex-grow">
                    <label for="{{ form.wo_no.id_for_label }}" class="sr-only">WO No</label>
                    {{ form.wo_no }}
                </div>
                <div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Messages Display Area -->
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- HTMX Container for the Work Order Table -->
    <div id="workOrderTable-container"
         hx-trigger="load, searchFormSubmit from:#search-form" {# Initial load and trigger on search form submit #}
         hx-get="{% url 'inventory:workorder_table_partial' %}"
         hx-target="#workOrderTable-container"
         hx-swap="innerHTML">
        <!-- Loading indicator for initial load -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
        // For this page, simple HTMX is sufficient.
    });
</script>
{% endblock %}
```

**`inventory/templates/inventory/wis_dry_actual_run/_workorder_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    {% if page_obj.object_list %} {# Use page_obj.object_list when using ListView with pagination #}
    <table id="workOrderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dry Run</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue/Shortage</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">View</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for wo in page_obj.object_list %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-center">
                    <button 
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded redbox"
                        hx-post="{% url 'inventory:dry_run_action' wo.pk %}"
                        hx-confirm="Are you sure you want to initiate Dry Run for {{ wo.won_o }}?"
                        hx-swap="none" {# No content swap, just trigger a redirect #}
                        hx-indicator="#loading-spinner">
                        Dry Run
                    </button>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-center">
                    <select id="drpIssueShortage-{{ wo.pk }}" name="view_type" class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                        <option value="0" selected>Transaction wise Issue</option>
                        <option value="1">Issue List</option>
                        <option value="2">Shortage List</option>
                    </select>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-center">
                    <button 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-3 rounded redbox"
                        hx-post="{% url 'inventory:view_action' wo.pk %}"
                        hx-include="#drpIssueShortage-{{ wo.pk }}" {# Include the selected value from this row's dropdown #}
                        hx-swap="none" {# No content swap, just trigger a redirect #}
                        hx-indicator="#loading-spinner">
                        View
                    </button>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ wo.won_o }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ wo.sys_date|date:"Y-m-d" }}</td>
                <td class="py-4 px-6 text-sm text-gray-900">{{ wo.prj_title }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="text-center text-red-500 font-bold py-10">No data found to display</p>
    {% endif %}

    {# DataTables pagination will override Django's default pagination for client-side control #}
    {% comment %}
    <div class="mt-4 flex justify-between items-center">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.wo_no %}&wo_no={{ request.GET.wo_no }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}"
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
               hx-get="?page={{ page_obj.previous_page_number }}" hx-target="#workOrderTable-container" hx-swap="innerHTML">Previous</a>
        {% endif %}
        <span class="text-sm text-gray-700">
            Page {{ page_obj.number }} of {{ page_obj.num_pages }}.
        </span>
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.wo_no %}&wo_no={{ request.GET.wo_no }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}"
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
               hx-get="?page={{ page_obj.next_page_number }}" hx-target="#workOrderTable-container" hx-swap="innerHTML">Next</a>
        {% endif %}
    </div>
    {% endcomment %}
</div>

<!-- HTMX Loading Spinner for all requests originating from this partial -->
<div id="loading-spinner" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
    <p class="ml-3 text-white text-lg">Processing...</p>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    // Destroy existing DataTable instance before re-initializing to prevent errors
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#workOrderTable')) {
            $('#workOrderTable').DataTable().destroy();
        }
        $('#workOrderTable').DataTable({
            "pageLength": 17, // Matches ASP.NET GridView PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "responsive": true, // Make table responsive
            "autoWidth": false, // Disable auto-width to allow more flexible sizing
            // Disable ordering on the first column (SN) and action columns (Dry Run, Issue/Shortage, View)
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 2, 3] }
            ]
        });
    });
</script>
```

#### 4.5 URLs (`inventory/urls.py`)

URL patterns for the main page, the HTMX-loaded table, and the action-specific endpoints.

```python
from django.urls import path
from .views import WISDryActualRunView, WorkOrderTablePartialView, DryRunActionView, ViewActionView

app_name = 'inventory' # Namespace for this application's URLs

urlpatterns = [
    # Main page for WIS Dry/Actual Run, displaying the search form and table container
    path('wis-dry-run/', WISDryActualRunView.as_view(), name='wis_dry_actual_run_list'),
    
    # HTMX endpoint to load/refresh the Work Order table partial
    path('wis-dry-run/table/', WorkOrderTablePartialView.as_view(), name='workorder_table_partial'),
    
    # Endpoint for the 'Dry Run' action button
    path('wis-dry-run/dry-run/<int:pk>/', DryRunActionView.as_view(), name='dry_run_action'),
    
    # Endpoint for the 'View' action button
    path('wis-dry-run/view/<int:pk>/', ViewActionView.as_view(), name='view_action'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for both models and views, ensuring functionality and adherence to requirements. This includes mocking session data for `company_id`.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date

from .models import WorkOrder, WOCategory
from .forms import WorkOrderSearchForm

# Mock the request.session for tests where company_id is needed
class MockSession:
    def get(self, key, default=None):
        if key == 'compid':
            return 1 # Simulate a default company ID for tests
        return default

# Patch the RequestFactory to inject the mocked session for all client requests
# This is a common pattern for testing session-dependent logic
mock_session_patcher = patch('django.test.client.RequestFactory.request', new_callable=MagicMock)

class WOCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for WOCategory
        WOCategory.objects.create(cid=101, symbol='MBL', cname='Mobile Category')
        WOCategory.objects.create(cid=102, symbol='LPT', cname='Laptop Category')

    def test_wo_category_creation(self):
        category = WOCategory.objects.get(cid=101)
        self.assertEqual(category.symbol, 'MBL')
        self.assertEqual(category.cname, 'Mobile Category')
        self.assertEqual(str(category), 'MBL - Mobile Category')

    def test_meta_options(self):
        # Ensure model meta options are correctly set for existing DB mapping
        self.assertEqual(WOCategory._meta.db_table, 'tblSD_WO_Category')
        self.assertFalse(WOCategory._meta.managed)
        self.assertEqual(WOCategory._meta.verbose_name, 'WO Category')
        self.assertEqual(WOCategory._meta.verbose_name_plural, 'WO Categories')


class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a WOCategory first as WorkOrder has a ForeignKey to it
        cls.category_mobile = WOCategory.objects.create(cid=201, symbol='MOB', cname='Mobile Production')
        cls.category_laptop = WOCategory.objects.create(cid=202, symbol='LPT', cname='Laptop Assembly')

        # Create test WorkOrder data
        WorkOrder.objects.create(
            id=1, won_o='WO001', sys_date='2023-01-15', prj_title='Mobile Phone Production Run',
            category=cls.category_mobile, company_id=1, close_open=False
        )
        WorkOrder.objects.create(
            id=2, won_o='WO002', sys_date='2023-01-16', prj_title='Laptop Assembly Phase 1',
            category=cls.category_laptop, company_id=1, close_open=False
        )
        WorkOrder.objects.create(
            id=3, won_o='WO003', sys_date='2023-01-17', prj_title='Old Closed Order',
            category=cls.category_mobile, company_id=1, close_open=True # Closed order, should be filtered out
        )
        WorkOrder.objects.create(
            id=4, won_o='WO004', sys_date='2023-01-18', prj_title='Mobile Testing Phase',
            category=cls.category_mobile, company_id=2, close_open=False # Different company, should be filtered out
        )

    def test_work_order_creation(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.won_o, 'WO001')
        self.assertEqual(wo.prj_title, 'Mobile Phone Production Run')
        self.assertEqual(wo.sys_date, date(2023, 1, 15))
        self.assertEqual(wo.category.cname, 'Mobile Production')
        self.assertEqual(wo.company_id, 1)
        self.assertFalse(wo.close_open)

    def test_work_order_manager_get_dry_run_list(self):
        # Test with no filters (only company_id and close_open=False)
        work_orders = WorkOrder.objects.get_dry_run_list(company_id=1)
        self.assertEqual(len(work_orders), 2) # WO001, WO002
        self.assertIn(WorkOrder.objects.get(id=1), work_orders)
        self.assertIn(WorkOrder.objects.get(id=2), work_orders)

        # Test with WO No filter (partial match)
        work_orders_wo_filter = WorkOrder.objects.get_dry_run_list(company_id=1, wo_no_filter='WO001')
        self.assertEqual(len(work_orders_wo_filter), 1)
        self.assertEqual(work_orders_wo_filter.first().won_o, 'WO001')

        # Test with Category filter
        work_orders_cat_filter = WorkOrder.objects.get_dry_run_list(company_id=1, category_id_filter=self.category_laptop.cid)
        self.assertEqual(len(work_orders_cat_filter), 1)
        self.assertEqual(work_orders_cat_filter.first().won_o, 'WO002')

        # Test with both filters
        work_orders_both_filter = WorkOrder.objects.get_dry_run_list(
            company_id=1, wo_no_filter='Mobile', category_id_filter=self.category_mobile.cid
        )
        self.assertEqual(len(work_orders_both_filter), 1)
        self.assertEqual(work_orders_both_filter.first().won_o, 'WO001')

        # Test no results for non-existent filter
        work_orders_no_result = WorkOrder.objects.get_dry_run_list(company_id=1, wo_no_filter='NonExistentWO')
        self.assertEqual(len(work_orders_no_result), 0)

        # Test filtering by different company_id
        work_orders_other_company = WorkOrder.objects.get_dry_run_list(company_id=2)
        self.assertEqual(len(work_orders_other_company), 1)
        self.assertEqual(work_orders_other_company.first().won_o, 'WO004')


class WISDryActualRunViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = WOCategory.objects.create(cid=100, symbol='TEST', cname='Test Category')
        cls.wo1 = WorkOrder.objects.create(
            id=10, won_o='WOX01', sys_date='2023-01-20', prj_title='Test Project One',
            category=cls.category, company_id=1, close_open=False
        )
        cls.wo2 = WorkOrder.objects.create(
            id=11, won_o='WOX02', sys_date='2023-01-21', prj_title='Another Project Two',
            category=cls.category, company_id=1, close_open=False
        )
        # Define expected legacy URLs for redirection tests
        cls.legacy_assembly_url = "/legacy/Module/Inventory/Transactions/WIS_ActualRun_Assembly.aspx"
        cls.legacy_transwise_url = "/legacy/Module/Inventory/Transactions/WIS_View_TransWise.aspx"
        cls.legacy_issue_shortage_url = "/legacy/Module/Inventory/Transactions/TotalIssueAndShortage_Print.aspx"
        cls.legacy_shortage_url = "/legacy/Module/Inventory/Transactions/TotalShortage_Print.aspx"

    def setUp(self):
        self.client = Client()
        # Start the patcher for the session mock
        self.mock_request_patch = mock_session_patcher.start()
        self.mock_request_patch.session = MockSession() # Assign our mock session instance
        # When accessing request.user or request.session in a view, it will go through this patched object
        self.addCleanup(mock_session_patcher.stop) # Ensure patch is stopped after test

    def test_wis_dry_actual_run_list_view_get(self):
        response = self.client.get(reverse('inventory:wis_dry_actual_run_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wis_dry_actual_run/list.html')
        self.assertIsInstance(response.context['form'], WorkOrderSearchForm)
        # Check that the HTMX container is present
        self.assertContains(response, '<div id="workOrderTable-container"')

    def test_work_order_table_partial_view_initial_load(self):
        response = self.client.get(reverse('inventory:workorder_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wis_dry_actual_run/_workorder_table.html')
        self.assertIn('page_obj', response.context) # Check for pagination object
        self.assertEqual(len(response.context['page_obj'].object_list), 2) # Both WOX01 and WOX02
        self.assertContains(response, self.wo1.won_o)
        self.assertContains(response, self.wo2.won_o)

    def test_work_order_table_partial_view_with_wo_no_filter(self):
        response = self.client.get(reverse('inventory:workorder_table_partial'), {'wo_no': 'WOX01'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['page_obj'].object_list), 1)
        self.assertEqual(response.context['page_obj'].object_list.first().won_o, 'WOX01')

    def test_work_order_table_partial_view_with_category_filter(self):
        response = self.client.get(reverse('inventory:workorder_table_partial'), {'wo_category': self.category.pk})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['page_obj'].object_list), 2) # Both WOX01 and WOX02 are from this category

    def test_dry_run_action_view_post_success(self):
        with patch('inventory.views.messages') as mock_messages: # Mock messages for assertions
            response = self.client.post(reverse('inventory:dry_run_action', args=[self.wo1.pk]))
            self.assertEqual(response.status_code, 302) # Expect redirect
            self.assertTrue(response.url.startswith(self.legacy_assembly_url))
            self.assertIn(f"WONo={self.wo1.won_o}", response.url)
            mock_messages.success.assert_called_once_with(self.mock_request_patch, f"Dry Run initiated for WO No: {self.wo1.won_o}")

    def test_dry_run_action_view_post_not_found(self):
        with patch('inventory.views.messages') as mock_messages:
            response = self.client.post(reverse('inventory:dry_run_action', args=[999])) # Non-existent PK
            self.assertEqual(response.status_code, 302)
            self.assertEqual(response.url, reverse('inventory:wis_dry_actual_run_list'))
            mock_messages.error.assert_called_once_with(self.mock_request_patch, "Work Order not found.")

    def test_view_action_view_post_type_0(self):
        with patch('inventory.views.messages') as mock_messages:
            response = self.client.post(reverse('inventory:view_action', args=[self.wo1.pk]), {'view_type': 0})
            self.assertEqual(response.status_code, 302)
            self.assertTrue(response.url.startswith(self.legacy_transwise_url))
            self.assertIn(f"WONo={self.wo1.won_o}", response.url)
            self.assertIn("Type=0", response.url)
            mock_messages.success.assert_called_once_with(self.mock_request_patch, f"Viewing {self.wo1.won_o} (Type: 0).")

    def test_view_action_view_post_type_1(self):
        with patch('inventory.views.messages') as mock_messages:
            response = self.client.post(reverse('inventory:view_action', args=[self.wo1.pk]), {'view_type': 1})
            self.assertEqual(response.status_code, 302)
            self.assertTrue(response.url.startswith(self.legacy_issue_shortage_url))
            self.assertIn(f"WONo={self.wo1.won_o}", response.url)
            self.assertIn("Type=1", response.url)
            self.assertIn("Key=", response.url) # Check for presence of Key
            mock_messages.success.assert_called_once_with(self.mock_request_patch, f"Viewing {self.wo1.won_o} (Type: 1).")

    def test_view_action_view_post_type_2(self):
        with patch('inventory.views.messages') as mock_messages:
            response = self.client.post(reverse('inventory:view_action', args=[self.wo1.pk]), {'view_type': 2})
            self.assertEqual(response.status_code, 302)
            self.assertTrue(response.url.startswith(self.legacy_shortage_url))
            self.assertIn(f"WONo={self.wo1.won_o}", response.url)
            self.assertIn("Type=2", response.url)
            self.assertIn("Key=", response.url) # Check for presence of Key
            mock_messages.success.assert_called_once_with(self.mock_request_patch, f"Viewing {self.wo1.won_o} (Type: 2).")

    def test_view_action_view_post_invalid_type(self):
        with patch('inventory.views.messages') as mock_messages:
            response = self.client.post(reverse('inventory:view_action', args=[self.wo1.pk]), {'view_type': 'invalid'})
            self.assertEqual(response.status_code, 302)
            self.assertEqual(response.url, reverse('inventory:wis_dry_actual_run_list'))
            mock_messages.error.assert_called_once_with(self.mock_request_patch, "Invalid view type selected.")

    def test_view_action_view_post_not_found(self):
        with patch('inventory.views.messages') as mock_messages:
            response = self.client.post(reverse('inventory:view_action', args=[999]), {'view_type': 0})
            self.assertEqual(response.status_code, 302)
            self.assertEqual(response.url, reverse('inventory:wis_dry_actual_run_list'))
            mock_messages.error.assert_called_once_with(self.mock_request_patch, "Work Order not found.")

```

### Step 5: HTMX and Alpine.js Integration

The core of the frontend modernization relies heavily on HTMX for dynamic content loading and form submissions without full page reloads. Alpine.js is included for lightweight interactivity but is not critically used for the primary functionality of this specific page, as HTMX handles most of the dynamic updates. DataTables.js provides enhanced table features.

*   **HTMX for Search/Filtering**:
    *   The `WorkOrderSearchForm` uses `hx-get` to target the `workOrderTable-container`.
    *   `hx-trigger="submit"` on the form ensures the search is performed when the "Search" button is clicked.
    *   `hx-trigger="change from:#id_wo_category"` is added to the `wo_category` dropdown in the form's `widget` attributes to automatically trigger a search on selection change.
    *   `hx-swap="innerHTML"` replaces the content of the table container.
*   **HTMX for Action Buttons**:
    *   "Dry Run" and "View" buttons use `hx-post` to send data to their respective Django views.
    *   `hx-swap="none"` is used because these actions typically result in a `HttpResponseRedirect` (a full page reload to a new page or legacy URL). The `messages` framework in Django will handle displaying feedback after the redirect.
    *   `hx-confirm` provides a native browser confirmation dialog before triggering the action.
    *   `hx-include="#drpIssueShortage-{{ wo.pk }}"` on the "View" button is crucial to send the selected value of the per-row dropdown to the backend.
*   **DataTables**:
    *   The `_workorder_table.html` partial template includes a JavaScript block that initializes DataTables on the `#workOrderTable`.
    *   It's important to destroy any existing DataTable instance (`.DataTable().destroy()`) before re-initializing it when HTMX swaps in new content, to prevent errors.
    *   `pageLength` is set to `17` to match the original ASP.NET `PageSize`.
    *   `columnDefs` is used to disable sorting on the "SN" and action columns, improving user experience.
*   **Loading Indicators**: `hx-indicator="#loading-spinner"` is used on buttons and the table container to display a visual loading spinner (`htmx-indicator` class in CSS) during HTMX requests, providing feedback to the user.
*   **Alpine.js**: While Alpine.js is included in `core/base.html`, its direct application for specific interactive elements on this page is minimal, as HTMX handles the primary dynamic content. It's ready for future use if more complex client-side state management is needed within the partials.

### Final Notes

*   This modernization plan provides a direct conversion path, maintaining the existing logic and UI flow using modern Django, HTMX, and Tailwind.
*   The redirection to `/legacy/...` URLs is a placeholder for a phased migration. In a complete modernization, these legacy ASP.NET pages would also be converted into new Django views and models.
*   The `company_id` retrieval from `request.session` simulates the ASP.NET `Session["compid"]`. In a robust Django application, this would typically be managed via user authentication and user profiles.
*   Error handling and user feedback are managed using Django's `messages` framework, which integrates well with HTMX by showing messages on redirects.
*   The "Fat Model, Thin View" principle is applied by moving data filtering logic into the `WorkOrderManager`.
*   The tests ensure the correctness of models, forms, and views, including HTMX interactions and legacy URL redirection.