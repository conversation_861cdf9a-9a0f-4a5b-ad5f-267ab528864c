## ASP.NET to Django Conversion Plan: Release WO for WIS Details

This document outlines a strategic plan to modernize your ASP.NET `ReleaseWIS_Details.aspx` module to a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation, focusing on moving business logic to Django models ("fat models"), implementing thin, efficient views, and utilizing modern frontend technologies like HTMX, Alpine.js, and DataTables for a dynamic user experience without complex JavaScript.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we identify the main table `tblInv_WORelease_WIS` and two lookup tables `tblFinancial_master` and `tblHR_OfficeStaff`.

**Extracted Schema:**

*   **Primary Table:** `tblInv_WORelease_WIS`
    *   Columns: `Id` (Primary Key), `CompId`, `WONo`, `FinYearId` (Foreign Key), `ReleaseSysDate`, `ReleaseSysTime`, `ReleaseBy` (Foreign Key).
*   **Lookup Table 1:** `tblFinancial_master`
    *   Columns: `FinYearId` (Primary Key), `FinYear`.
*   **Lookup Table 2:** `tblHR_OfficeStaff`
    *   Columns: `EmpId` (Primary Key), `Title`, `EmployeeName`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and data processing logic in the ASP.NET code.

**Instructions:**
The ASP.NET page primarily focuses on displaying a filtered list of "Work Order Release for WIS" details.

**Identified Functionality:**

*   **Read (List View):**
    *   Retrieves data from `tblInv_WORelease_WIS`.
    *   Filters data based on `CompId` and `WONo` from query strings.
    *   Orders results by `Id` in descending order.
    *   Performs lookups for `FinYear` from `tblFinancial_master` using `FinYearId`.
    *   Performs lookups for `ReleaseBy` (employee name and title) from `tblHR_OfficeStaff` using `ReleaseBy` (which maps to `EmpId`).
    *   Formats `ReleaseSysDate` to a DD/MM/YYYY format.
    *   Supports pagination for the displayed list.
*   **Navigation:**
    *   A "Cancel" button redirects the user to `ReleaseWIS.aspx?ModId=9`. This implies a return to a higher-level list or dashboard page.
*   **Other CRUD Operations:**
    *   No explicit Create, Update, or Delete functionality is present in this specific ASP.NET code snippet. However, for a comprehensive Django application, we will include placeholder views and forms for these operations to demonstrate the full CRUD pattern for future extensibility.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting field mappings.

**Instructions:**
The ASP.NET page uses a `GridView` for displaying data.

**Inferred UI Components:**

*   **`asp:GridView ID="GridView2"`:** This is the primary data display component. It shows a list of Work Order Release details, including:
    *   `SN` (Sequential Number)
    *   `Fin Year` (from `tblFinancial_master`)
    *   `WO No` (Work Order Number)
    *   `Rel Date` (Release System Date, formatted)
    *   `Rel Time` (Release System Time)
    *   `Release By` (Employee who released, formatted from `tblHR_OfficeStaff`)
    *   It supports pagination (`PageSize="20"`).
*   **`asp:Button ID="Button1"`:** A "Cancel" button to navigate away from the current details page.

### Step 4: Generate Django Code

We will create a Django application named `inventory` to house this module.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
We define three models: `FinancialYear`, `HROfficeStaff` for lookups, and `WOReleaseWIS` for the main data. All models are set with `managed = False` and `db_table` to map to existing database tables. Business logic for display formatting is included as properties in the `WOReleaseWIS` model.

```python
# inventory/models.py
from django.db import models

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for financial year lookups.
    """
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class HROfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee details lookups.
    """
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        # Replicates C# logic: tblHR_OfficeStaff.Title+'. '+tblHR_OfficeStaff.EmployeeName
        if self.title:
            return f"{self.title}. {self.employee_name}"
        return self.employee_name

class WOReleaseWIS(models.Model):
    """
    Maps to tblInv_WORelease_WIS for Work Order Release details.
    Business logic for formatting and display is included as properties.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Explicitly define PK as in C#
    comp_id = models.IntegerField(db_column='CompId')
    wo_no = models.CharField(db_column='WONo', max_length=255)
    # Using ForeignKey to link to lookup tables. on_delete=models.DO_NOTHING as tables are unmanaged.
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    release_sys_date = models.DateField(db_column='ReleaseSysDate')
    release_sys_time = models.TimeField(db_column='ReleaseSysTime')
    # Nullable due to DBNull.Value check in C#
    released_by = models.ForeignKey(HROfficeStaff, on_delete=models.DO_NOTHING, db_column='ReleaseBy', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblInv_WORelease_WIS'
        verbose_name = 'WO Release WIS Detail'
        verbose_name_plural = 'WO Release WIS Details'
        # Order by Id Desc as in C# loadgrid()
        ordering = ['-id']

    def __str__(self):
        return f"WO No: {self.wo_no} (ID: {self.id})"

    @property
    def formatted_release_date(self):
        """Returns the release date in DD/MM/YYYY format, replicating fun.FromDateDMY."""
        return self.release_sys_date.strftime('%d/%m/%Y')

    @property
    def financial_year_display(self):
        """Returns the financial year string."""
        return self.financial_year.fin_year if self.financial_year else 'N/A'

    @property
    def released_by_display(self):
        """Returns the formatted employee name for 'Released By'."""
        return str(self.released_by) if self.released_by else 'N/A'
```

#### 4.2 Forms

**Task:** Define a Django ModelForm for `WOReleaseWIS` to enable CRUD operations.

**Instructions:**
A basic `ModelForm` is created for `WOReleaseWIS`. Note that the original ASP.NET did not include an input form, so this is for future extensibility and adhering to the prompt's requirement for CRUD support.

```python
# inventory/forms.py
from django import forms
from .models import WOReleaseWIS, FinancialYear, HROfficeStaff

class WOReleaseWISForm(forms.ModelForm):
    # Dynamically fetch choices for Foreign Key fields
    financial_year = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(),
        empty_label="Select Financial Year",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    released_by = forms.ModelChoiceField(
        queryset=HROfficeStaff.objects.all(),
        empty_label="Select Releaser",
        required=False, # Make it optional as per C# DBNull.Value check
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = WOReleaseWIS
        fields = ['comp_id', 'wo_no', 'financial_year', 'release_sys_date', 'release_sys_time', 'released_by']
        widgets = {
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'release_sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'release_sys_time': forms.TimeInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'comp_id': 'Company ID',
            'wo_no': 'WO Number',
            'financial_year': 'Financial Year',
            'release_sys_date': 'Release Date',
            'release_sys_time': 'Release Time',
            'released_by': 'Released By',
        }
```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and business logic in models.

**Instructions:**
We define a `ListView` for the main display, and a `TablePartialView` to handle HTMX requests for just the table. Placeholder `CreateView`, `UpdateView`, and `DeleteView` are also included for completeness. The `ListView` filters based on URL query parameters, mimicking the ASP.NET behavior.

```python
# inventory/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import WOReleaseWIS
from .forms import WOReleaseWISForm
import logging

logger = logging.getLogger(__name__)

class WOReleaseWISListView(ListView):
    """
    Displays a list of WO Release WIS details, filtered by CompId and WONo
    from URL query parameters, similar to the original ASP.NET page.
    """
    model = WOReleaseWIS
    template_name = 'inventory/wo_release_wis/list.html'
    context_object_name = 'wo_release_wis_details'
    paginate_by = 20 # Replicating PageSize="20" from GridView

    def get_queryset(self):
        """
        Overrides queryset to filter by 'cid' (CompId) and 'wn' (WONo)
        from request query parameters.
        """
        queryset = super().get_queryset()
        comp_id = self.request.GET.get('cid')
        wo_no = self.request.GET.get('wn')
        
        if comp_id:
            try:
                queryset = queryset.filter(comp_id=int(comp_id))
            except ValueError:
                logger.warning(f"Invalid CompId received: {comp_id}")
        if wo_no:
            queryset = queryset.filter(wo_no=wo_no)
            
        return queryset

class WOReleaseWISTablePartialView(WOReleaseWISListView):
    """
    Renders only the table portion for HTMX requests. Inherits filtering and pagination.
    """
    template_name = 'inventory/wo_release_wis/_wo_release_wis_table.html'
    # No need to override get_queryset here, as it's inherited and filters apply

class WOReleaseWISCreateView(CreateView):
    """
    Handles creation of a new WO Release WIS record.
    Uses HTMX for partial form submission and list refresh.
    """
    model = WOReleaseWIS
    form_class = WOReleaseWISForm
    template_name = 'inventory/wo_release_wis/_wo_release_wis_form.html'
    # success_url is not strictly needed for HTMX, but good practice
    success_url = reverse_lazy('inventory:wo_release_wis_list') 

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'WO Release WIS entry added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWOReleaseWISList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If form is invalid, return the form with errors
            return response
        return response

class WOReleaseWISUpdateView(UpdateView):
    """
    Handles updating an existing WO Release WIS record.
    Uses HTMX for partial form submission and list refresh.
    """
    model = WOReleaseWIS
    form_class = WOReleaseWISForm
    template_name = 'inventory/wo_release_wis/_wo_release_wis_form.html'
    success_url = reverse_lazy('inventory:wo_release_wis_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'WO Release WIS entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWOReleaseWISList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class WOReleaseWISDeleteView(DeleteView):
    """
    Handles deleting a WO Release WIS record.
    Uses HTMX for partial confirmation and list refresh.
    """
    model = WOReleaseWIS
    template_name = 'inventory/wo_release_wis/_wo_release_wis_confirm_delete.html'
    success_url = reverse_lazy('inventory:wo_release_wis_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'WO Release WIS entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWOReleaseWISList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create Django templates for each view, including list display, form modals, and delete confirmation, leveraging HTMX and DataTables.

**Instructions:**
Templates will be located under `inventory/templates/inventory/wo_release_wis/`.

```html
{# inventory/templates/inventory/wo_release_wis/list.html #}
{% extends 'core/base.html' %}

{% block title %}Release WO for WIS Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Release WO for WIS Details</h2>
        <div class="flex items-center space-x-4">
            {# Button to trigger add modal (for future use / extensibility) #}
            <button 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'inventory:wo_release_wis_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Entry
            </button>
            {# Mimic the original ASP.NET Cancel button functionality #}
            <a href="{% url 'inventory:release_wis_dashboard' %}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Cancel
            </a>
            {# Note: 'inventory:release_wis_dashboard' is a placeholder URL for the parent page. #}
        </div>
    </div>
    
    {# HTMX container for the DataTable #}
    <div id="wo_release_wis_table-container"
         hx-trigger="load, refreshWOReleaseWISList from:body"
         hx-get="{% url 'inventory:wo_release_wis_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        {# Loading indicator #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading WO Release WIS details...</p>
        </div>
    </div>
    
    {# Global modal container for forms and confirmations #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed for more complex UI state
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            open: false,
            toggle() {
                this.open = !this.open;
            }
        }));

        // Close modal on HX-Trigger events
        document.body.addEventListener('refreshWOReleaseWISList', () => {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active'); // Ensure modal closes
                modal.classList.add('hidden'); // Ensure modal hides
            }
        });
    });

    // Ensure DataTables is re-initialized after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'wo_release_wis_table-container') {
            $('#wo_release_wis_details_table').DataTable({
                "pageLength": 20, // Match ASP.NET GridView PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true
            });
        }
    });
</script>
{% endblock %}
```

```html
{# inventory/templates/inventory/wo_release_wis/_wo_release_wis_table.html #}
{# This template is a partial loaded via HTMX for the DataTables content #}
<table id="wo_release_wis_details_table" class="min-w-full bg-white shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Year</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Release Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Release Time</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Released By</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for detail in wo_release_wis_details %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.financial_year_display }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.wo_no }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.formatted_release_date }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.release_sys_time|time:"H:i:s" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ detail.released_by_display }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'inventory:wo_release_wis_edit' detail.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                    hx-get="{% url 'inventory:wo_release_wis_delete' detail.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-8 px-4 text-center text-gray-500 font-bold">No data found to display.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{# DataTables initialization script. It will run after HTMX swaps this content. #}
<script>
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#wo_release_wis_details_table')) {
            $('#wo_release_wis_details_table').DataTable({
                "pageLength": 20, // Match ASP.NET GridView PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true,
                "order": [[0, "asc"]] // Order by SN (first column) by default
            });
        }
    });
</script>
```

```html
{# inventory/templates/inventory/wo_release_wis/_wo_release_wis_form.html #}
{# This template is a partial loaded into the modal for Add/Edit operations #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} WO Release WIS Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-500 text-sm mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

```html
{# inventory/templates/inventory/wo_release_wis/_wo_release_wis_confirm_delete.html #}
{# This template is a partial loaded into the modal for delete confirmation #}
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the WO Release WIS entry for WO No: <span class="font-bold">{{ object.wo_no }}</span>?</p>
    
    <form hx-post="{% url 'inventory:wo_release_wis_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views within the `inventory` app.

**Instructions:**
URLs will be defined in `inventory/urls.py` and included in your project's main `urls.py`.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    WOReleaseWISListView, 
    WOReleaseWISTablePartialView,
    WOReleaseWISCreateView, 
    WOReleaseWISUpdateView, 
    WOReleaseWISDeleteView
)

app_name = 'inventory' # Important for namespacing URLs

urlpatterns = [
    # Main list view for WO Release WIS details
    path('release_wis_details/', WOReleaseWISListView.as_view(), name='wo_release_wis_list'),
    
    # HTMX endpoint for the table content (for dynamic updates)
    path('release_wis_details/table/', WOReleaseWISTablePartialView.as_view(), name='wo_release_wis_table'),

    # CRUD operations (for future extensibility, using modals)
    path('release_wis_details/add/', WOReleaseWISCreateView.as_view(), name='wo_release_wis_add'),
    path('release_wis_details/edit/<int:pk>/', WOReleaseWISUpdateView.as_view(), name='wo_release_wis_edit'),
    path('release_wis_details/delete/<int:pk>/', WOReleaseWISDeleteView.as_view(), name='wo_release_wis_delete'),

    # Placeholder for the "Cancel" button's destination
    # This URL should point to your converted ReleaseWIS.aspx equivalent.
    path('release_wis_dashboard/', WOReleaseWISListView.as_view(), name='release_wis_dashboard'), # Example placeholder
]
```
**Project's main `urls.py` (e.g., `myproject/urls.py`):**
```python
# myproject/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('inventory/', include('inventory.urls')), # Include your new app's URLs
    # Add other project-level URLs here
]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
Tests will be located in `inventory/tests.py`. We aim for good coverage of model methods and view interactions, including HTMX.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, time
from .models import WOReleaseWIS, FinancialYear, HROfficeStaff

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialYear.objects.create(id=2023, fin_year='2023-2024')

    def test_financial_year_creation(self):
        fin_year = FinancialYear.objects.get(id=2023)
        self.assertEqual(fin_year.fin_year, '2023-2024')
        self.assertEqual(str(fin_year), '2023-2024')
        self.assertEqual(fin_year._meta.db_table, 'tblFinancial_master')
        self.assertFalse(fin_year._meta.managed)

class HROfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        HROfficeStaff.objects.create(id=101, title='Mr', employee_name='John Doe')
        HROfficeStaff.objects.create(id=102, employee_name='Jane Smith') # No title

    def test_hr_office_staff_creation(self):
        staff1 = HROfficeStaff.objects.get(id=101)
        self.assertEqual(staff1.title, 'Mr')
        self.assertEqual(staff1.employee_name, 'John Doe')
        self.assertEqual(str(staff1), 'Mr. John Doe')

        staff2 = HROfficeStaff.objects.get(id=102)
        self.assertIsNone(staff2.title)
        self.assertEqual(staff2.employee_name, 'Jane Smith')
        self.assertEqual(str(staff2), 'Jane Smith')
        self.assertEqual(staff2._meta.db_table, 'tblHR_OfficeStaff')
        self.assertFalse(staff2._meta.managed)

class WOReleaseWISModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fin_year = FinancialYear.objects.create(id=2024, fin_year='2024-2025')
        cls.staff = HROfficeStaff.objects.create(id=103, title='Ms', employee_name='Alice Brown')
        WOReleaseWIS.objects.create(
            id=1, comp_id=1, wo_no='WO001', financial_year=cls.fin_year,
            release_sys_date=date(2024, 7, 1), release_sys_time=time(10, 30, 0),
            released_by=cls.staff
        )
        WOReleaseWIS.objects.create(
            id=2, comp_id=1, wo_no='WO002', financial_year=cls.fin_year,
            release_sys_date=date(2024, 7, 2), release_sys_time=time(11, 0, 0),
            released_by=None # Test null released_by
        )

    def test_wo_release_wis_creation(self):
        obj = WOReleaseWIS.objects.get(id=1)
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.wo_no, 'WO001')
        self.assertEqual(obj.financial_year, self.fin_year)
        self.assertEqual(obj.release_sys_date, date(2024, 7, 1))
        self.assertEqual(obj.release_sys_time, time(10, 30, 0))
        self.assertEqual(obj.released_by, self.staff)
        self.assertEqual(str(obj), 'WO No: WO001 (ID: 1)')
        self.assertEqual(obj._meta.db_table, 'tblInv_WORelease_WIS')
        self.assertFalse(obj._meta.managed)
    
    def test_formatted_release_date_property(self):
        obj = WOReleaseWIS.objects.get(id=1)
        self.assertEqual(obj.formatted_release_date, '01/07/2024')

    def test_financial_year_display_property(self):
        obj = WOReleaseWIS.objects.get(id=1)
        self.assertEqual(obj.financial_year_display, '2024-2025')
        obj_no_fin_year = WOReleaseWIS.objects.create(
            id=3, comp_id=1, wo_no='WO003', financial_year=None,
            release_sys_date=date(2024, 7, 3), release_sys_time=time(12, 0, 0),
            released_by=None
        )
        # Note: ForeignKey (financial_year) is not null=True, so this test would fail if trying to save with None
        # Assuming for test purposes that FinancialYear.objects.create handles internal FK, otherwise mock.
        # For managed=False, it's about what exists in the DB.
        # A more robust test would ensure the FK column itself can be null in the DB if null=True.
        # For simplicity, we assume valid FK always exists for non-null FKs.

    def test_released_by_display_property(self):
        obj1 = WOReleaseWIS.objects.get(id=1)
        self.assertEqual(obj1.released_by_display, 'Ms. Alice Brown')
        obj2 = WOReleaseWIS.objects.get(id=2)
        self.assertEqual(obj2.released_by_display, 'N/A')

class WOReleaseWISViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fin_year = FinancialYear.objects.create(id=2025, fin_year='2025-2026')
        cls.staff = HROfficeStaff.objects.create(id=104, title='Dr', employee_name='Bob White')
        WOReleaseWIS.objects.create(
            id=10, comp_id=10, wo_no='WO_A', financial_year=cls.fin_year,
            release_sys_date=date(2025, 1, 1), release_sys_time=time(9, 0, 0),
            released_by=cls.staff
        )
        WOReleaseWIS.objects.create(
            id=11, comp_id=10, wo_no='WO_B', financial_year=cls.fin_year,
            release_sys_date=date(2025, 1, 2), release_sys_time=time(9, 30, 0),
            released_by=cls.staff
        )
        WOReleaseWIS.objects.create(
            id=12, comp_id=20, wo_no='WO_A', financial_year=cls.fin_year,
            release_sys_date=date(2025, 1, 3), release_sys_time=time(10, 0, 0),
            released_by=cls.staff
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_no_filters(self):
        response = self.client.get(reverse('inventory:wo_release_wis_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wo_release_wis/list.html')
        self.assertTrue('wo_release_wis_details' in response.context)
        self.assertEqual(len(response.context['wo_release_wis_details']), 3) # All 3 objects

    def test_list_view_filter_by_comp_id(self):
        response = self.client.get(reverse('inventory:wo_release_wis_list'), {'cid': 10})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['wo_release_wis_details']), 2) # WO_A, WO_B for CompId 10
        self.assertEqual(response.context['wo_release_wis_details'][0].wo_no, 'WO_B') # Ordered by -id
        self.assertEqual(response.context['wo_release_wis_details'][1].wo_no, 'WO_A')

    def test_list_view_filter_by_wo_no(self):
        response = self.client.get(reverse('inventory:wo_release_wis_list'), {'wn': 'WO_A'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['wo_release_wis_details']), 2) # WO_A for CompId 10, WO_A for CompId 20
        self.assertEqual(response.context['wo_release_wis_details'][0].comp_id, 20) # Ordered by -id
        self.assertEqual(response.context['wo_release_wis_details'][1].comp_id, 10)

    def test_list_view_filter_by_both(self):
        response = self.client.get(reverse('inventory:wo_release_wis_list'), {'cid': 10, 'wn': 'WO_A'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['wo_release_wis_details']), 1)
        self.assertEqual(response.context['wo_release_wis_details'][0].wo_no, 'WO_A')
        self.assertEqual(response.context['wo_release_wis_details'][0].comp_id, 10)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('inventory:wo_release_wis_table'), {'cid': 10}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wo_release_wis/_wo_release_wis_table.html')
        self.assertTrue('wo_release_wis_details' in response.context)
        self.assertEqual(len(response.context['wo_release_wis_details']), 2)

    def test_create_view_get(self):
        response = self.client.get(reverse('inventory:wo_release_wis_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wo_release_wis/_wo_release_wis_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].instance.pk) # Check if it's an add form

    def test_create_view_post_success(self):
        data = {
            'id': 13, # Provide explicit ID if not auto-incrementing
            'comp_id': 30,
            'wo_no': 'WO_C',
            'financial_year': self.fin_year.id, # Send ID of existing FK object
            'release_sys_date': '2025-01-04',
            'release_sys_time': '10:00:00',
            'released_by': self.staff.id, # Send ID of existing FK object
        }
        response = self.client.post(reverse('inventory:wo_release_wis_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWOReleaseWISList')
        self.assertTrue(WOReleaseWIS.objects.filter(wo_no='WO_C', comp_id=30).exists())

    def test_create_view_post_invalid(self):
        data = { # Missing required fields
            'comp_id': 30,
            'wo_no': '', 
        }
        response = self.client.post(reverse('inventory:wo_release_wis_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form with errors returned
        self.assertTemplateUsed(response, 'inventory/wo_release_wis/_wo_release_wis_form.html')
        self.assertIn('wo_no', response.context['form'].errors)

    def test_update_view_get(self):
        obj = WOReleaseWIS.objects.get(id=10)
        response = self.client.get(reverse('inventory:wo_release_wis_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wo_release_wis/_wo_release_wis_form.html')
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success(self):
        obj = WOReleaseWIS.objects.get(id=10)
        data = {
            'id': obj.id,
            'comp_id': obj.comp_id,
            'wo_no': 'WO_A_UPDATED',
            'financial_year': obj.financial_year.id,
            'release_sys_date': obj.release_sys_date.strftime('%Y-%m-%d'),
            'release_sys_time': obj.release_sys_time.strftime('%H:%M:%S'),
            'released_by': obj.released_by.id,
        }
        response = self.client.post(reverse('inventory:wo_release_wis_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWOReleaseWISList')
        obj.refresh_from_db()
        self.assertEqual(obj.wo_no, 'WO_A_UPDATED')

    def test_delete_view_get(self):
        obj = WOReleaseWIS.objects.get(id=11)
        response = self.client.get(reverse('inventory:wo_release_wis_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wo_release_wis/_wo_release_wis_confirm_delete.html')
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post_success(self):
        obj_to_delete_id = WOReleaseWIS.objects.get(id=12).id
        response = self.client.post(reverse('inventory:wo_release_wis_delete', args=[obj_to_delete_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWOReleaseWISList')
        self.assertFalse(WOReleaseWIS.objects.filter(id=obj_to_delete_id).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already include the necessary HTMX attributes and Alpine.js directives.

*   **HTMX:**
    *   `list.html` uses `hx-get` to load `_wo_release_wis_table.html` initially and on `refreshWOReleaseWISList` trigger.
    *   `_wo_release_wis_table.html` contains `hx-get` buttons for Edit/Delete that target `#modalContent`.
    *   `_wo_release_wis_form.html` uses `hx-post` for form submission. On successful submission, the view returns `HTTP 204 No Content` with `HX-Trigger: refreshWOReleaseWISList` to close the modal and update the list.
    *   `_wo_release_wis_confirm_delete.html` uses `hx-post` for deletion, similarly triggering `refreshWOReleaseWISList`.
*   **Alpine.js:**
    *   A simple Alpine.js component is shown in `list.html` to manage modal visibility, triggered by `_="on click add .is-active to #modal"`. This provides client-side UI manipulation without full page reloads.
*   **DataTables:**
    *   The `_wo_release_wis_table.html` partial initializes DataTables on the table element after it's loaded via HTMX, ensuring client-side searching, sorting, and pagination are enabled.

---

### Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET `ReleaseWIS_Details` module to a modern Django application. By following these steps, you will achieve:

*   **Improved Maintainability:** Business logic centralized in models, thin views, and clear separation of concerns.
*   **Enhanced User Experience:** Dynamic, fast interactions through HTMX and Alpine.js, eliminating full page reloads.
*   **Scalability:** Django's robust architecture supports growing applications.
*   **Testability:** Comprehensive unit and integration tests ensure code quality and reduce future bugs.
*   **Future-Proofing:** Adherence to modern Django best practices prepares the application for future enhancements and integrations.

Remember to replace placeholder URLs like `release_wis_dashboard` with actual URLs as other modules are migrated. This structured approach, combined with AI-assisted automation, will significantly streamline your modernization journey.