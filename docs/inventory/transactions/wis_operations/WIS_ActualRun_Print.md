This comprehensive plan outlines the modernization of the provided ASP.NET application to a modern Django-based solution. The focus is on leveraging AI-assisted automation by providing clear, actionable steps that can be translated into automated migration scripts and tools. We will prioritize a "Fat Model, Thin View" architecture, emphasizing HTMX for dynamic interactions, Alpine.js for lightweight UI state management, and DataTables for superior data presentation. All communication is in plain English, suitable for business stakeholders to understand the benefits and outcomes of the transition.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code, particularly the `DrpWOType` (WO Category dropdown list) population and the `GridView2` (Work Order list) data binding which references the `Sp_WIS_ActualRun_Grid` stored procedure, we infer the following database structures:

-   **WO Category Table:**
    -   **Table Name:** `tblSD_WO_Category`
    -   **Columns:**
        -   `CId` (Integer, Primary Key): Used as the value for the WO Category dropdown.
        -   `Symbol` (String): Part of the display text for categories.
        -   `CName` (String): Part of the display text for categories.
        -   `CompId` (Integer): A common identifier for company/organization, used for data filtering.

-   **Work Order (WO) Table:**
    -   **Table Name:** `tblWIS_WorkOrder` (This table name is inferred based on the context of the "WIS Print" module and the columns displayed in the `GridView2`).
    -   **Columns:**
        -   `Id` (Integer, Primary Key): A unique identifier for each Work Order.
        -   `WONo` (String): The Work Order Number, used for searching, display, and linking to other pages.
        -   `SysDate` (Date/DateTime): The system date associated with the Work Order.
        -   `PrjTitle` (String): The project title related to the Work Order.
        -   `CId` (Integer, Foreign Key to `tblSD_WO_Category`): Implicitly used to link Work Orders to their categories for filtering.
        -   `CompId` (Integer): Another company/organization identifier, commonly used for data partitioning.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET page, `WIS_ActualRun_Print.aspx`, primarily serves as a **Search, List, and Report Initiation** interface. It does not contain direct functionalities for creating, updating, or deleting Work Order records themselves.

-   **Read (List & Search):**
    -   The core functionality involves displaying a list of Work Orders in `GridView2`. This list is populated by calling a `loadgrid` method, which executes the `Sp_WIS_ActualRun_Grid` stored procedure.
    -   Users can refine this list by:
        -   Entering a Work Order Number (`WONo`) in `TxtWO` and clicking the "Search" button (`Button1`).
        -   Selecting a Work Order Category from the `DrpWOType` dropdown, which automatically updates the list.
    -   The `GridView2` supports basic pagination (`GridView2_PageIndexChanging`), allowing users to browse through multiple pages of results.

-   **Report Generation / Redirection:**
    -   **Custom Search Tab:** This section allows users to define a date range, a Work Order number, and an overheads percentage. Upon clicking `BtnSearch`, the application generates a URL with these parameters and loads it into an `iframe` (`Iframe1`). This indicates that a separate report generation or display service (likely another ASP.NET page using Crystal Reports) is being invoked.
    -   **"Dry Run" Action:** A button labeled "Dry Run" in each row of the `GridView2` redirects the user to `WIS_ActualRun_Assembly.aspx`. This suggests a separate page where detailed assembly instructions or processes for a specific Work Order are handled.
    -   **"View" Action:** A "View" button in each `GridView2` row, combined with a dropdown (`drpIssueShortage`) that allows selecting different report types (Transaction wise Issue, Issue List, Shortage List), redirects the user to various print/view pages (`WIS_View_TransWise.aspx`, `TotalIssueAndShortage_Print.aspx`, `TotalShortage_Print.aspx`). This signifies multiple specialized reports or views are available for Work Orders.

-   **Validation:**
    -   The `txtOverheads` input field includes server-side validation (`RequiredFieldValidator`, `RegularExpressionValidator`) to ensure it's a required numerical input conforming to a specific decimal pattern.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET controls and their behaviors will be translated into modern Django components with a focus on interactive, dynamic user experience using HTMX and Alpine.js, eliminating full-page reloads.

-   **Master Page (`MasterPage.master`):** This is equivalent to your foundational `core/base.html` template in Django, which provides the overall layout, navigation, and common scripts/styles for the entire application.
-   **`UpdatePanel` (`UpdatePanel1`):** This ASP.NET AJAX control enabled partial page updates. In Django, this functionality will be replaced by specific HTMX attributes (`hx-get`, `hx-target`, `hx-swap`) on various HTML elements. When an action occurs, HTMX will fetch and swap only the necessary parts of the page, providing a seamless user experience without full page refreshes.
-   **`TabContainer` (`TabContainer1`):** This will be implemented using standard HTML `div` and `a` (or `button`) elements. HTMX will handle the loading of tab content (e.g., `_search_wo_tab.html` and `_custom_search_tab.html`) dynamically when a tab is clicked. Alpine.js will manage the active tab state for visual styling.
-   **`DrpWOType` (DropDownList):** This will be a standard HTML `<select>` element in Django, rendered by a `forms.ModelChoiceField`. Its `AutoPostBack="True"` behavior will be replicated using `hx-trigger="change"` on the `<select>` element, causing an HTMX request to update the Work Order list immediately upon selection.
-   **`TxtWO` (TextBox):** This will be a standard HTML `<input type="text">` element rendered by a Django `forms.CharField`.
-   **`Button1` (Search Button for WO List):** A standard HTML `<button>` element. It will trigger an HTMX `GET` request to refresh the Work Order list, passing the `TxtWO` and `DrpWOType` values as parameters.
-   **`GridView2` (Data Grid):** This will be completely replaced by an HTML `<table>` element styled with Tailwind CSS and enhanced with **DataTables.js** for client-side functionality. DataTables will provide robust features like client-side pagination (matching the original 15 items per page), sorting, and a built-in search box. The table content itself will be loaded dynamically via HTMX.
    -   **`TemplateField` (inside `GridView2`):**
        -   **SN (Serial Number):** This will be rendered using Django's `forloop.counter` in the template.
        -   **"Dry Run" Button:** Since this originally redirected to another ASP.NET page, it will be an HTML `<a>` tag or `<button>` that links to a corresponding new Django view (e.g., `/inventory/workorder/assembly/<wonum>/`), opening in a new tab to maintain existing workflow patterns.
        -   **`drpIssueShortage` (DropDownList):** A standard HTML `<select>` element within each table row.
        -   **"View" Button:** This button, acting upon the selected `drpIssueShortage` value, will also be an HTML `<button>` that triggers a JavaScript `window.open` or similar action to redirect the user to the appropriate report view (e.g., PDF generation or a detailed report page) in a new tab, much like the original ASP.NET behavior.
-   **`txtFromDate`, `txtToDate` (TextBoxes with `CalendarExtender`):** These will be HTML `<input type="date">` elements, which provide native date pickers in modern browsers. They will be rendered by Django `forms.DateField` widgets. Alpine.js can be used to further enhance or provide fallback date picker functionality for older browsers if needed.
-   **`txtWONo` (for Custom Search):** Another standard HTML `<input type="text">` element rendered by a Django `forms.CharField`.
-   **`txtOverheads` (TextBox):** An HTML `<input type="number">` element rendered by a Django `forms.FloatField`. Client-side validation for numerical input can be handled by Alpine.js, while server-side validation will be handled by the Django form.
-   **`BtnSearch` (Custom Search Button):** A standard HTML `<button>` element that will submit the "Custom Search" form via HTMX. Upon successful submission, HTMX will receive a header to update the `src` attribute of the `reportIframe`.
-   **`Iframe1` (Iframe):** This will remain an HTML `<iframe>` element. Its `src` attribute will be dynamically set by HTMX after the "Custom Search" form is successfully submitted, pointing to a Django report generation view (e.g., a PDF view or a detailed HTML report view).

## Step 4: Generate Django Code

The Django application for this module will be named `inventory`.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

We will define two Django models, `WOCategory` and `WorkOrder`, mapping them directly to the existing database tables using `managed = False` and `db_table`. The `WorkOrder` model will include a custom manager and methods to encapsulate data fetching and business logic, adhering to the "Fat Model" principle.

```python
# inventory/models.py
from django.db import models
from django.db.models import Q # Used for complex queries in managers

class WOCategory(models.Model):
    """
    Represents Work Order Categories from tblSD_WO_Category.
    This model is primarily used for populating the filter dropdown.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        # managed = False indicates that Django will not create or modify this table.
        # It assumes the table already exists in the database.
        managed = False
        db_table = 'tblSD_WO_Category' # Matches the ASP.NET database table name
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        # Provides a human-readable representation, useful for dropdowns and admin.
        return f"{self.symbol} - {self.cname}" if self.symbol else self.cname


class WorkOrderQuerySet(models.QuerySet):
    """
    Custom QuerySet for WorkOrder model to encapsulate business logic
    related to fetching and filtering Work Orders.
    This keeps the view thin by moving query logic here.
    """
    def filter_by_criteria(self, company_id, wo_number=None, category_id=None):
        """
        Filters Work Orders based on company ID, Work Order Number, and category.
        
        Args:
            company_id (int): The ID of the company to filter by.
            wo_number (str, optional): Work Order number to filter by. Case-insensitive.
            category_id (str/int, optional): Category ID to filter by. 
                                            'WO Category' string is treated as 'all'.
        Returns:
            QuerySet: A filtered and ordered QuerySet of WorkOrder objects.
        """
        filters = Q(compid=company_id) # Always filter by company ID

        if wo_number:
            filters &= Q(wono__iexact=wo_number) # Add WO number filter (case-insensitive)

        if category_id and category_id != 'WO Category': # 'WO Category' is the default dropdown option for 'all'
            try:
                category_id = int(category_id)
                filters &= Q(category__cid=category_id) # Filter by CId from WOCategory relationship
            except ValueError:
                # Silently ignore if category_id is not a valid integer, e.g., malformed input
                pass 
        
        # Default ordering as seen in the GridView, by date (descending) and WO number
        return self.filter(filters).order_by('-sysdate', 'wono')


class WorkOrder(models.Model):
    """
    Represents Work Order details, inferred from tblWIS_WorkOrder.
    This model contains the core data for the Work Order listing.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    wono = models.CharField(db_column='WONo', max_length=50, unique=True, verbose_name="WO No")
    sysdate = models.DateField(db_column='SysDate', verbose_name="Date") # Assuming Date, not DateTime, from ASP.NET display format
    prjtitle = models.CharField(db_column='PrjTitle', max_length=500, blank=True, null=True, verbose_name="Project Title")
    
    # Foreign key to WOCategory, linked by CId. 
    # on_delete=models.DO_NOTHING because the table is unmanaged (legacy DB).
    category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', blank=True, null=True, related_name='workorders')
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    # Attach the custom QuerySet to the default manager
    objects = WorkOrderQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = 'tblWIS_WorkOrder' # Inferred table name from ASP.NET context
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        ordering = ['-sysdate', 'wono'] # Default ordering for consistency

    def __str__(self):
        return f"{self.wono} - {self.prjtitle or 'No Title'}"

    # Business logic methods for WorkOrder (Fat Model approach)
    # These methods encapsulate logic that in ASP.NET might be in the code-behind or helper functions.

    def get_assembly_link(self):
        """
        Generates the URL for the 'Dry Run' (Assembly) page.
        Mimics the redirect to WIS_ActualRun_Assembly.aspx.
        """
        # In a real Django app, this would use reverse('appname:assembly_view_name', args=[self.wono])
        # For direct migration, we provide the path as it was in ASP.NET relative to module.
        return f"/module/inventory/transactions/wis_actualrun_assembly/{self.wono}/?ModId=9&SubModId=53"

    def get_report_link(self, report_type):
        """
        Generates the URL for the 'View' report based on the selected type.
        Mimics the switch-case redirect logic in GridView2_RowCommand.
        """
        # A placeholder for random key, in production this might be generated by a utility service.
        random_key_placeholder = "DJANGO_KEY_PLACEHOLDER" 
        
        base_path = "/module/inventory/transactions/"
        common_params = f"&ModId=9&SubModId=53&Type={report_type}&status=1"

        if report_type == 0: # "Transaction wise Issue"
            return f"{base_path}wis_view_transwise/{self.wono}/?{common_params}"
        elif report_type == 1: # "Issue List"
            return f"{base_path}total_issue_and_shortage_print/{self.wono}/?Key={random_key_placeholder}{common_params}"
        elif report_type == 2: # "Shortage List"
            return f"{base_path}total_shortage_print/{self.wono}/?Key={random_key_placeholder}{common_params}"
        return "#" # Fallback for unknown types

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

We'll create distinct forms: one for filtering the Work Order list (`WorkOrderFilterForm`) and another for the "Custom Search" (WIP) tab (`CustomSearchForm`), including its specific validation rules. Although the original ASP.NET page doesn't perform direct CRUD for `WorkOrder` records, a placeholder `WorkOrderForm` is included to illustrate how such a form would be structured for future module expansion, adhering to the prompt's template.

```python
# inventory/forms.py
from django import forms
from .models import WorkOrder, WOCategory
import datetime
import re # For regex validation


class WorkOrderFilterForm(forms.Form):
    """
    Form for filtering the Work Order list by category and WO number.
    This replaces the DrpWOType and TxtWO controls.
    """
    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('cname'), # Populate dropdown from WOCategory model
        empty_label="WO Category", # Corresponds to the "WO Category" initial item in ASP.NET
        required=False, # Filtering is optional
        label="WO Category",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/workorder/table/', # HTMX trigger: on change, fetch new table
            'hx-target': '#workOrderTable-container', # Replace content of this div
            'hx-trigger': 'change', # AutoPostBack equivalent
            'hx-swap': 'innerHTML',
            'hx-indicator': '#wo-table-spinner' # Show loading spinner
        })
    )
    wo_number = forms.CharField(
        max_length=50,
        required=False,
        label="WONo",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Work Order Number',
            'hx-get': '/inventory/workorder/table/', # HTMX trigger: on input, fetch new table (can be adjusted to 'keyup changed delay:500ms')
            'hx-target': '#workOrderTable-container',
            'hx-trigger': 'keyup changed delay:500ms', # Dynamic search as user types
            'hx-swap': 'innerHTML',
            'hx-indicator': '#wo-table-spinner'
        })
    )


class CustomSearchForm(forms.Form):
    """
    Form for the "Custom Search" (WIP) tab.
    Replaces txtFromDate, txtToDate, txtWONo (for report), and txtOverheads.
    """
    from_date = forms.DateField(
        required=False, # Dates are optional
        label="From Date",
        widget=forms.DateInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # HTML5 date picker for modern browsers
        })
    )
    to_date = forms.DateField(
        required=False,
        label="To Date",
        widget=forms.DateInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date'
        })
    )
    wo_no_report = forms.CharField( # Renamed to avoid name collision with filter form's 'wo_number'
        max_length=100,
        required=False,
        label="WONo",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Work Order Number for Report'
        })
    )
    overheads = forms.FloatField(
        required=True, # Matches RequiredFieldValidator
        label="Overheads",
        initial=75.0, # Matches default value in ASP.NET
        widget=forms.NumberInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'min': 0, 'step': 0.01 # Basic HTML5 validation hints
        })
    )

    def clean_overheads(self):
        """
        Custom validation for the overheads field, mimicking the RegularExpressionValidator.
        Expression: ^\d{1,15}(\.\d{0,3})?$
        This means 1 to 15 digits before decimal, and 0 to 3 digits after decimal.
        """
        overheads = self.cleaned_data['overheads']
        if overheads is not None:
            # Convert float to string to apply regex
            overheads_str = str(overheads)
            # Regex pattern: start with 1-15 digits, optionally followed by . and 0-3 digits, end.
            pattern = re.compile(r"^\d{1,15}(\.\d{0,3})?$")
            if not pattern.match(overheads_str):
                raise forms.ValidationError("Overheads must be a number with up to 15 digits before and 3 after the decimal point.")
        return overheads
    
    def clean(self):
        """
        Global form validation, e.g., for date range consistency.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', "To Date cannot be earlier than From Date.")
        return cleaned_data


# Placeholder WorkOrderForm for generic WorkOrder CRUD operations.
# This form is included as per the prompt's template, but is NOT directly used
# by the original ASP.NET page's primary functionality (which is search/list/report initiation).
# It serves as an example for how add/edit modals would work for a WorkOrder record.
class WorkOrderForm(forms.ModelForm):
    class Meta:
        model = WorkOrder
        # Fields to be displayed/edited in the form. 'id' is primary key, 'compid' is session-derived.
        fields = ['wono', 'sysdate', 'prjtitle', 'category'] 
        widgets = {
            'wono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sysdate': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'prjtitle': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'category': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'wono': 'Work Order No.',
            'sysdate': 'Date',
            'prjtitle': 'Project Title',
            'category': 'WO Category',
        }
    
    def clean_wono(self):
        """
        Ensures Work Order Number is unique for new records,
        or unchanged for existing records (if not creating a duplicate).
        """
        wono = self.cleaned_data['wono']
        # Check for uniqueness only if creating a new instance or if WO No changed
        if self.instance.pk is None or self.instance.wono != wono:
            if WorkOrder.objects.filter(wono__iexact=wono).exists():
                raise forms.ValidationError("A Work Order with this number already exists. Please choose a unique one.")
        return wono

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Django Class-Based Views (CBVs) will be used to create clean, reusable, and concise views. The main `WorkOrderListView` serves as the entry point, while `WorkOrderTablePartialView` handles HTMX-driven table refreshes. `CustomSearchReportView` processes the custom search form and triggers the report display. Placeholder `Create`, `Update`, and `Delete` views for `WorkOrder` are included to demonstrate common CRUD patterns, adhering to the "thin view" principle by delegating logic to models and forms.

```python
# inventory/views.py
from django.views.generic import ListView, FormView, TemplateView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
import urllib.parse
from .models import WorkOrder, WOCategory
from .forms import WorkOrderFilterForm, CustomSearchForm, WorkOrderForm

# Helper function to mimic ASP.NET Session data access.
# In a real Django application, this would typically involve proper authentication
# and user/company profiles managed within Django's auth system or custom middleware.
def get_session_data(request):
    """Retrieves session-dependent IDs (CompId, FinYearId, Username)."""
    # Default values are used for demonstration if not found in session.
    # In production, these should be robustly handled, e.g., via login.
    return {
        'compid': request.session.get('compid', 1), 
        'finyearid': request.session.get('finyearid', 1),
        'username': request.session.get('username', 'admin'),
    }

class WorkOrderListView(TemplateView):
    """
    Main view for displaying the Work Order search, list, and tabbed interface.
    This serves as the primary entry point for the 'WIS Print' module.
    """
    template_name = 'inventory/workorder/list.html'

    def get_context_data(self, **kwargs):
        """
        Provides initial forms to the template for rendering.
        """
        context = super().get_context_data(**kwargs)
        # Initialize filter form and custom search form without data on initial GET request
        context['filter_form'] = WorkOrderFilterForm(self.request.GET or None)
        context['custom_search_form'] = CustomSearchForm(self.request.GET or None)
        return context

class WorkOrderTablePartialView(ListView):
    """
    Renders only the Work Order table content.
    This view is specifically designed to be targeted by HTMX requests
    to refresh the Work Order list dynamically.
    """
    model = WorkOrder
    template_name = 'inventory/workorder/_workorder_table.html'
    context_object_name = 'workorders'
    # paginate_by = 15 # DataTables will handle client-side pagination. 
                     # This Django setting is a fallback for server-side pagination if needed.

    def get_queryset(self):
        """
        Retrieves and filters Work Order data based on GET parameters.
        Leverages the WorkOrder model's custom manager for business logic.
        """
        session_data = get_session_data(self.request)
        company_id = session_data['compid']
        
        # Extract filter parameters from the GET request, sent by HTMX
        wo_number = self.request.GET.get('wo_number')
        category_id = self.request.GET.get('wo_category')

        # Delegate filtering logic to the WorkOrder model's custom manager
        queryset = WorkOrder.objects.filter_by_criteria(
            company_id=company_id,
            wo_number=wo_number,
            category_id=category_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        """
        Ensures the filter form is also available within the partial template
        if its state needs to be reflected (e.g., selected dropdown value).
        """
        context = super().get_context_data(**kwargs)
        context['filter_form'] = WorkOrderFilterForm(self.request.GET or None)
        return context


class CustomSearchReportView(FormView):
    """
    Handles the submission of the Custom Search form from the 'WIP' tab.
    Processes input, constructs a report URL, and sends an HTMX trigger
    to update an iframe's source.
    """
    template_name = 'inventory/workorder/_custom_search_tab.html' # Render this partial for HTMX
    form_class = CustomSearchForm
    
    def form_valid(self, form):
        """
        Processes valid form data, generates a report URL, and triggers an HTMX event.
        Mimics the ASP.NET BtnSearch_Click logic.
        """
        wo_no = form.cleaned_data.get('wo_no_report', '')
        from_date = form.cleaned_data.get('from_date')
        to_date = form.cleaned_data.get('to_date')
        overheads = form.cleaned_data.get('overheads', 0.0)

        # Construct query parameters for the hypothetical report URL
        params = {
            'wo_no': wo_no,
            'from_date': from_date.strftime('%d-%m-%Y') if from_date else '', # Match ASP.NET format
            'to_date': to_date.strftime('%d-%m-%Y') if to_date else '',
            'overheads': overheads,
            'key': 'DJANGO_RANDOM_KEY', # Placeholder for ASP.NET's fun.GetRandomAlphaNumeric()
            'ModId': 9, # Example static parameters
            'SubModId': 53,
        }

        # The ASP.NET code updated an iframe. In Django, we'll build a URL
        # to a hypothetical report view and trigger an HTMX event to update the iframe's src.
        report_url = reverse('inventory:wis_wono_report_view') + '?' + urllib.parse.urlencode(params)
        
        # Return an HTMX response (status 204 means No Content, just headers)
        # The 'HX-Trigger' header will send a custom event to the client-side JavaScript,
        # which will then update the iframe's src attribute.
        response = HttpResponse(status=204)
        response['HX-Trigger'] = f"setReportIframeSrc:{report_url}"
        messages.success(self.request, "Report parameters submitted. Report loading...")
        return response

    def form_invalid(self, form):
        """
        Handles invalid form submissions by re-rendering the form with errors.
        This partial will be swapped back into the main view's tab content.
        """
        messages.error(self.request, "Please correct the errors in the form before searching.")
        # Re-render the form with errors. The outerHTML swap will refresh the form.
        return render(self.request, self.template_name, {'custom_search_form': form})

# Placeholder for the actual report view that the iframe would load.
# This view would typically contain the logic for generating a detailed report
# (e.g., fetching specific data, generating a PDF, or complex HTML rendering).
class WisWonoReportView(TemplateView):
    """
    A placeholder view for the WIS Work Order Report.
    This would be the target for the iframe.
    """
    template_name = 'inventory/reports/wis_wono_report.html' # This template would show the report content
    
    def get_context_data(self, **kwargs):
        """
        Passes received query parameters to the template for display/processing.
        """
        context = super().get_context_data(**kwargs)
        # Parameters would be parsed from self.request.GET
        context['wo_no'] = self.request.GET.get('wo_no', 'N/A')
        context['from_date'] = self.request.GET.get('from_date', 'N/A')
        context['to_date'] = self.request.GET.get('to_date', 'N/A')
        context['overheads'] = self.request.GET.get('overheads', 'N/A')
        context['key'] = self.request.GET.get('key', 'N/A')
        # Add any other report-specific data fetching here
        return context
    
    def render_to_response(self, context, **response_kwargs):
        """
        In a real scenario, this method might return a FileResponse for a PDF report
        or a rendered HTML page with dynamic report content.
        """
        # For now, it just renders an HTML page showing the received parameters.
        return super().render_to_response(context, **response_kwargs)

# --- Placeholder CRUD Views for WorkOrder (as per template instructions) ---
# These views are provided to illustrate how standard CRUD operations for WorkOrder
# records would be implemented in Django using CBVs and HTMX/Alpine.js for modals.
# They are NOT directly part of the original ASP.NET page's functionality,
# but represent patterns for related or future modules.

class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'inventory/workorder/_workorder_form.html' # This is a partial template for HTMX modal loading
    success_url = reverse_lazy('inventory:workorder_list') # Redirect to the list view after success

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass categories to form for dropdown if not handled by ModelChoiceField's queryset directly
        context['wo_categories'] = WOCategory.objects.all().order_by('cname') 
        return context

    def form_valid(self, form):
        """
        Sets the company ID before saving the new Work Order.
        Sends HTMX trigger to refresh the list upon successful creation.
        """
        session_data = get_session_data(self.request)
        form.instance.compid = session_data['compid'] # Set company ID from session
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header.
            # This allows the modal to close and the list to refresh without full page reload.
            return HttpResponse(
                status=204, 
                headers={
                    'HX-Trigger': 'refreshWorkOrderList' # Custom HTMX event to refresh the table
                }
            )
        return response

    def form_invalid(self, form):
        """
        Renders the form again with validation errors if submission fails.
        """
        messages.error(self.request, "Please correct the errors below.")
        context = self.get_context_data()
        context['form'] = form
        return render(self.request, self.template_name, context)

class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'inventory/workorder/_workorder_form.html' # Partial template for HTMX modal
    success_url = reverse_lazy('inventory:workorder_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wo_categories'] = WOCategory.objects.all().order_by('cname')
        return context

    def form_valid(self, form):
        """
        Ensures company ID consistency and sends HTMX trigger on successful update.
        """
        session_data = get_session_data(self.request)
        form.instance.compid = session_data['compid'] 
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response
    
    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        context = self.get_context_data()
        context['form'] = form
        return render(self.request, self.template_name, context)

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'inventory/workorder/_workorder_confirm_delete.html' # Partial template for HTMX modal
    success_url = reverse_lazy('inventory:workorder_list')

    def delete(self, request, *args, **kwargs):
        """
        Handles deletion and sends HTMX trigger for list refresh.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # You might add more details about the object being deleted for the confirmation message
        return context

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will be designed to be modular and reusable, adhering to DRY principles. They will extend `core/base.html` (which is assumed to exist) and utilize HTMX for dynamic content updates. Alpine.js will handle UI state like tab switching and modal visibility. DataTables.js will be initialized on the `_workorder_table.html` partial to provide rich list functionalities.

```html
{# inventory/templates/inventory/workorder/list.html #}
{% extends 'core/base.html' %} {# Assumes core/base.html provides the main layout, headers, footers, etc. #}
{% load crispy_forms_tags %} {# Optional: Using django-crispy-forms for cleaner form rendering #}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'searchWO' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">WIS Print</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'inventory:workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
            data-modal-title="Add New Work Order"> {# Data attribute for Alpine.js to set modal title #}
            Add New Work Order
        </button>
    </div>

    {# Tab Container structure using Alpine.js for active state and HTMX for loading content #}
    <div class="bg-white rounded-lg shadow-xl overflow-hidden">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-4" aria-label="Tabs">
                <a href="#" 
                   @click.prevent="activeTab = 'searchWO'" 
                   :class="{'border-indigo-500 text-indigo-600': activeTab === 'searchWO', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'searchWO'}" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-300 ease-in-out"
                   hx-get="{% url 'inventory:workorder_search_tab' %}" {# HTMX will load this tab content #}
                   hx-target="#tabContent"
                   hx-swap="innerHTML"
                   hx-trigger="click">
                    &nbsp;&nbsp; Search by WO No &nbsp;
                </a>
                <a href="#" 
                   @click.prevent="activeTab = 'customSearch'" 
                   :class="{'border-indigo-500 text-indigo-600': activeTab === 'customSearch', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'customSearch'}" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition duration-300 ease-in-out"
                   hx-get="{% url 'inventory:workorder_custom_search_tab' %}" {# HTMX will load this tab content #}
                   hx-target="#tabContent"
                   hx-swap="innerHTML"
                   hx-trigger="click">
                    &nbsp;&nbsp;WIP&nbsp;&nbsp;
                </a>
            </nav>
        </div>

        <div id="tabContent" class="p-6">
            {# Initial content for the first tab (Search by WO No) is loaded here #}
            {# This is a direct include for the initial page load. Subsequent loads are via HTMX #}
            {% include 'inventory/workorder/_search_wo_tab.html' with filter_form=filter_form %}
        </div>
    </div>
</div>

<!-- Global Modal Structure for HTMX-loaded forms (Add, Edit, Delete) -->
<div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 hidden items-center justify-center z-50 transition-opacity ease-out duration-300 opacity-0"
     _="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from me then remove .scale-100 from #modalContent"
     x-data="{ title: '' }" {# Alpine.js state for modal title #}
     @set-modal-title.window="title = $event.detail.title"> {# Listen for custom event to set title #}
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full transform scale-95 transition-transform ease-out duration-300">
        <h3 class="text-xl font-semibold text-gray-800 mb-4" x-text="title"></h3> {# Display modal title #}
        <!-- Content loaded by HTMX will go here (forms, confirm_delete, etc.) -->
    </div>
</div>

{% endblock %}

{% block extra_js %}
{# Include DataTables CSS and JS (assuming CDN links are in base.html or explicitly added here for clarity) #}
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>

<script>
    // Global HTMX and Alpine.js interactions setup
    document.addEventListener('alpine:init', () => {
        // Alpine.js components specific to this page can be defined here if needed
    });

    // Listen for htmx:afterSwap to re-initialize DataTables when the table content is loaded/refreshed
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Check if the swapped content is our work order table container
        if (evt.detail.target.id === 'workOrderTable-container') {
            // Destroy existing DataTable instance before re-initializing if it exists
            if ($.fn.DataTable.isDataTable('#workOrderTable')) {
                $('#workOrderTable').DataTable().destroy();
            }
            // Initialize DataTables on the new table element
            $('#workOrderTable').DataTable({
                "pageLength": 15, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "searching": true, // Enable client-side search box
                "ordering": true,  // Enable sorting
                "paging": true     // Enable pagination
            });
        }
        // Listener for modal close on successful form submission (triggered by HX-Trigger header)
        if (evt.detail.trigger === 'refreshWorkOrderList') {
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            if (modal) {
                // Hide modal gracefully using Alpine.js and HTMX's _ attributes
                modal.classList.remove('flex');
                modal.classList.remove('opacity-100');
                modalContent.classList.remove('scale-100');
            }
        }
    });

    // Custom event listener for setting iframe src from HTMX (from CustomSearchReportView)
    document.body.addEventListener('setReportIframeSrc', function(evt) {
        const iframe = document.getElementById('reportIframe');
        if (iframe && evt.detail && evt.detail.value) {
            iframe.src = evt.detail.value;
            console.log("Iframe src set to:", evt.detail.value);
        }
    });

    // Event listener to set modal title based on data-modal-title attribute of the triggering button
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            const button = evt.detail.elt; // The element that triggered the hx-get
            const title = button.dataset.modalTitle;
            if (title) {
                // Update the Alpine.js data property for the modal title
                document.getElementById('modal')._x_dataStack[0].title = title;
            }
        }
    });
</script>
{% endblock %}
```

```html
{# inventory/templates/inventory/workorder/_search_wo_tab.html #}
{# This partial template represents the "Search by WO No" tab content #}
<div id="searchWO_content"> {# This div is loaded into #tabContent #}
    <div class="mb-4 flex flex-wrap items-end space-x-4">
        <div class="flex-grow">
            <label for="{{ filter_form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700">WO Category</label>
            {{ filter_form.wo_category }}
            {% if filter_form.wo_category.errors %}<p class="text-red-500 text-xs mt-1">{{ filter_form.wo_category.errors }}</p>{% endif %}
        </div>
        <div class="flex-grow">
            <label for="{{ filter_form.wo_number.id_for_label }}" class="block text-sm font-medium text-gray-700">WONo</label>
            {{ filter_form.wo_number }}
            {% if filter_form.wo_number.errors %}<p class="text-red-500 text-xs mt-1">{{ filter_form.wo_number.errors }}</p>{% endif %}
        </div>
        <div>
            <button type="button" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
                hx-get="{% url 'inventory:workorder_table' %}" {# HTMX will fetch the table content #}
                hx-target="#workOrderTable-container"
                hx-swap="innerHTML"
                hx-indicator="#wo-table-spinner"
                {# Pass current values of filter inputs as parameters to the HTMX request #}
                hx-vals='{"wo_number": document.getElementById("id_wo_number").value, "wo_category": document.getElementById("id_wo_category").value}'>
                Search
            </button>
        </div>
    </div>
    
    {# Loading spinner for HTMX requests #}
    <div id="wo-table-spinner" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading Work Orders...</p>
    </div>

    {# Container for the dynamically loaded Work Order table #}
    <div id="workOrderTable-container"
         {# HTMX triggers to load initial table and refresh it after CRUD operations #}
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'inventory:workorder_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#wo-table-spinner">
        <!-- The DataTables table content will be loaded here via HTMX -->
        <!-- Initial content will be the loading spinner, then replaced -->
    </div>
</div>
```

```html
{# inventory/templates/inventory/workorder/_custom_search_tab.html #}
{# This partial template represents the "WIP" (Custom Search) tab content #}
<div id="customSearch_content"> {# This div is loaded into #tabContent #}
    <form hx-post="{% url 'inventory:workorder_custom_search' %}" 
          hx-target="this" {# Target self to display form errors back in the same place #}
          hx-swap="outerHTML"> {# Replace the entire form with the updated version (including errors) #}
        {% csrf_token %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end mb-6">
            <div>
                <label for="{{ custom_search_form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date:</label>
                {{ custom_search_form.from_date }}
                {% if custom_search_form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ custom_search_form.from_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ custom_search_form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date:</label>
                {{ custom_search_form.to_date }}
                {% if custom_search_form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ custom_search_form.to_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ custom_search_form.wo_no_report.id_for_label }}" class="block text-sm font-medium text-gray-700">WONo:</label>
                {{ custom_search_form.wo_no_report }}
            </div>
            <div class="flex items-center">
                <label for="{{ custom_search_form.overheads.id_for_label }}" class="block text-sm font-medium text-gray-700 mr-2">Overheads:</label>
                <div class="flex-grow">
                    {{ custom_search_form.overheads }}
                    {% if custom_search_form.overheads.errors %}<p class="text-red-500 text-xs mt-1">{{ custom_search_form.overheads.errors }}</p>{% endif %}
                </div>
                <span class="ml-1 text-gray-700">%</span>
            </div>
            <div>
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </div>
        {# Display non-field errors if any #}
        {% if custom_search_form.non_field_errors %}
            <div class="text-red-500 text-sm mb-4">
                {% for error in custom_search_form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
    </form>
    
    <div class="mt-6">
        {# The iframe that will display the report, its src updated by HTMX #}
        <iframe id="reportIframe" width="100%" height="410px" frameborder="0" scrolling="auto" class="border border-gray-300 rounded-md shadow-inner"></iframe>
    </div>
</div>
```

```html
{# inventory/templates/inventory/workorder/_workorder_table.html #}
{# This partial template contains only the Work Order table, designed to be loaded by HTMX #}
<table id="workOrderTable" class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Report Type</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if workorders %}
            {% for wo in workorders %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ wo.wono }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ wo.sysdate|date:"d-m-Y" }}</td> {# Format date to match ASP.NET dd-MM-yyyy #}
                <td class="py-3 px-6 text-sm text-gray-900">{{ wo.prjtitle }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900 text-center">
                    {# Dropdown for report type, mimicking drpIssueShortage #}
                    <select id="drpIssueShortage-{{ wo.id }}" class="form-select block w-full px-2 py-1 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="0" selected>Transaction wise Issue</option>
                        <option value="1">Issue List</option>
                        <option value="2">Shortage List</option>
                    </select>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-center">
                    {# Dry Run button (links to assembly page) #}
                    <a href="{{ wo.get_assembly_link }}" 
                       class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 mr-2"
                       target="_blank" rel="noopener noreferrer"> {# Opens in new tab #}
                        Dry Run
                    </a>
                    {# View button (triggers redirect to report based on dropdown) #}
                    <button type="button" 
                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            {# Dynamically construct URL based on selected dropdown value and open in new tab #}
                            hx-on:click="window.open(htmx.find('#drpIssueShortage-{{ wo.id }}').value == '0' ? '{{ wo.get_report_link(0) }}' : (htmx.find('#drpIssueShortage-{{ wo.id }}').value == '1' ? '{{ wo.get_report_link(1) }}' : '{{ wo.get_report_link(2) }}'), '_blank')">
                        View
                    </button>
                    {# Placeholder for Edit button (demonstrates HTMX modal for CRUD) #}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md shadow-sm text-xs transition duration-300 ease-in-out ml-2"
                        hx-get="{% url 'inventory:workorder_edit' wo.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        data-modal-title="Edit Work Order">
                        Edit
                    </button>
                    {# Placeholder for Delete button (demonstrates HTMX modal for CRUD) #}
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md shadow-sm text-xs transition duration-300 ease-in-out ml-1"
                        hx-get="{% url 'inventory:workorder_delete' wo.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                        data-modal-title="Delete Work Order">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500 text-lg font-bold">No data found to display</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // DataTables initialization for the loaded table content
    // This script runs when the partial is loaded by HTMX
    $(document).ready(function() {
        // Only initialize if DataTables hasn't been initialized on this table yet
        // (The htmx:afterSwap listener in list.html should handle destroy/re-init)
        if (!$.fn.DataTable.isDataTable('#workOrderTable')) { 
            $('#workOrderTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "searching": true,
                "ordering": true,
                "paging": true
            });
        }
    });
</script>
```

```html
{# inventory/templates/inventory/workorder/_workorder_form.html #}
{# This is a partial template for WorkOrder Add/Edit forms, designed to be loaded into a modal by HTMX #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    {# hx-post to submit form. hx-swap="none" indicates that the HTMX response headers will control behavior. #}
    <form hx-post="{{ request.path }}" hx-swap="none"> 
        {% csrf_token %}
        
        <div class="space-y-4">
            {# Render each form field dynamically #}
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }} {# Render the input widget for the field #}
                {% if field.errors %} {# Display field-specific errors #}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            {# Cancel button uses Alpine.js _ attribute to close the modal #}
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            {# Submit button #}
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# inventory/templates/inventory/workorder/_workorder_confirm_delete.html #}
{# This partial template is for the WorkOrder Delete confirmation, loaded into a modal by HTMX #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Work Order "{{ object.wono }}" (ID: {{ object.pk }})?</p>
    {# hx-post to trigger delete. hx-swap="none" indicates HTMX response headers will control behavior. #}
    <form hx-post="{{ request.path }}" hx-swap="none"> 
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            {# Cancel button uses Alpine.js _ attribute to close the modal #}
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            {# Delete confirmation button #}
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

```html
{# inventory/templates/inventory/reports/wis_wono_report.html #}
{# This is a placeholder for your Crystal Report equivalent. #}
{# In a production system, this template would contain logic for rendering a full report, #}
{# possibly by fetching data, using a charting library, or integrating with a PDF generator. #}
{% extends 'core/base.html' %} {# Extend your base template, or a minimal one if no navigation is needed #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Work Order Print Report (Simulated)</h2>
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <p class="text-gray-700 mb-2">This page simulates the content that would be displayed in the iframe for the WIS Work Order Report.</p>
        <p class="mb-4">It demonstrates receiving parameters from the "Custom Search" form.</p>

        <h3 class="text-xl font-semibold text-gray-800 mb-3">Report Parameters Received:</h3>
        <ul class="list-disc list-inside text-gray-700 space-y-1">
            <li><strong>Work Order No:</strong> {{ wo_no }}</li>
            <li><strong>From Date:</strong> {{ from_date }}</li>
            <li><strong>To Date:</strong> {{ to_date }}</li>
            <li><strong>Overheads:</strong> {{ overheads }}%</li>
            <li><strong>Key:</strong> {{ key }}</li>
            {# Add other parameters as needed here #}
        </ul>

        <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-800">
            <p><strong>Note:</strong> In a production system, this page would typically render a comprehensive report, possibly a PDF generated using a library like WeasyPrint or ReportLab, or by integrating with a dedicated reporting service. The ASP.NET Crystal Reports functionality would require a modern, open-source equivalent in Django. For example, a dedicated microservice for report generation could be invoked here.</p>
        </div>
    </div>
</div>
{% endblock %}
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create a `urls.py` file within the `inventory` Django app to define the URL patterns that map to the views. We'll use Django's `path` function for clean, readable URLs. A namespace `inventory` is used for easy referencing of URLs within templates and other parts of the Django application.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    WorkOrderListView, 
    WorkOrderTablePartialView, 
    CustomSearchReportView, 
    WisWonoReportView,
    WorkOrderCreateView,
    WorkOrderUpdateView,
    WorkOrderDeleteView,
)

app_name = 'inventory' # Define the app's namespace

urlpatterns = [
    # Main Work Order list and search page
    path('workorder/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX endpoint for refreshing only the Work Order table content
    path('workorder/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    
    # HTMX endpoint for the "Search by WO No" tab content
    path('workorder/search-tab/', TemplateView.as_view(template_name='inventory/workorder/_search_wo_tab.html'), name='workorder_search_tab'),
    
    # HTMX endpoint for the "Custom Search" (WIP) tab content and form submission
    path('workorder/custom-search-tab/', CustomSearchReportView.as_view(), name='workorder_custom_search_tab'),
    path('workorder/custom-search/', CustomSearchReportView.as_view(), name='workorder_custom_search'), # POST endpoint for form submission
    
    # Placeholder URL for the report that the iframe would load
    path('workorder/report/wis-wono/', WisWonoReportView.as_view(), name='wis_wono_report_view'),

    # --- Placeholder CRUD URLs for WorkOrder (as per template instructions) ---
    # These URLs are for demonstrating standard add/edit/delete functionality
    # using HTMX modals, even if not directly present in the original ASP.NET page.
    path('workorder/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorder/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorder/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),

    # --- Example of how other legacy ASP.NET pages would be mapped ---
    # These would typically be separate Django apps or views handling their specific logic.
    path('transactions/wis_actualrun_assembly/<str:wono>/', TemplateView.as_view(template_name='inventory/placeholder_assembly.html'), name='wis_actualrun_assembly_view'),
    path('transactions/wis_view_transwise/<str:wono>/', TemplateView.as_view(template_name='inventory/reports/placeholder_report.html'), name='wis_view_transwise_view'),
    path('transactions/total_issue_and_shortage_print/<str:wono>/', TemplateView.as_view(template_name='inventory/reports/placeholder_report.html'), name='total_issue_and_shortage_print_view'),
    path('transactions/total_shortage_print/<str:wono>/', TemplateView.as_view(template_name='inventory/reports/placeholder_report.html'), name='total_shortage_print_view'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive tests are crucial for ensuring the correctness and reliability of the migrated application. We will include:
-   **Unit Tests for Models:** To verify the `WorkOrder` model's custom manager methods and any business logic methods (`get_assembly_link`, `get_report_link`).
-   **Integration Tests for Views:** To test the functionality of `WorkOrderListView`, `WorkOrderTablePartialView`, `CustomSearchReportView`, and the placeholder CRUD views, including their HTMX interactions and form submissions.

```python       
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.http import HttpResponse
from django.contrib.messages import get_messages
from .models import WOCategory, WorkOrder
from .forms import WorkOrderFilterForm, CustomSearchForm, WorkOrderForm
import datetime
import json

class WOCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for WOCategory
        WOCategory.objects.create(cid=101, symbol='WO1', cname='Category A', compid=1)
        WOCategory.objects.create(cid=102, symbol='WO2', cname='Category B', compid=1)
  
    def test_wocategory_creation(self):
        category = WOCategory.objects.get(cid=101)
        self.assertEqual(category.symbol, 'WO1')
        self.assertEqual(category.cname, 'Category A')
        self.assertEqual(category.compid, 1)
        
    def test_str_representation(self):
        category = WOCategory.objects.get(cid=101)
        self.assertEqual(str(category), 'WO1 - Category A')

    def test_wocategory_verbose_name(self):
        self.assertEqual(WOCategory._meta.verbose_name, 'WO Category')
        self.assertEqual(WOCategory._meta.verbose_name_plural, 'WO Categories')


class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent WOCategory objects first
        cls.category_a = WOCategory.objects.create(cid=101, symbol='WO1', cname='Category A', compid=1)
        cls.category_b = WOCategory.objects.create(cid=102, symbol='WO2', cname='Category B', compid=1)

        # Create test data for WorkOrder
        cls.wo1 = WorkOrder.objects.create(
            id=1, wono='WO-001', sysdate=datetime.date(2023, 1, 15), 
            prjtitle='Project Alpha', category=cls.category_a, compid=1
        )
        cls.wo2 = WorkOrder.objects.create(
            id=2, wono='WO-002', sysdate=datetime.date(2023, 1, 20), 
            prjtitle='Project Beta', category=cls.category_b, compid=1
        )
        cls.wo3 = WorkOrder.objects.create(
            id=3, wono='WO-003', sysdate=datetime.date(2023, 2, 10), 
            prjtitle='Project Gamma', category=cls.category_a, compid=2 # Different company
        )
  
    def test_workorder_creation(self):
        self.assertEqual(self.wo1.wono, 'WO-001')
        self.assertEqual(self.wo1.sysdate, datetime.date(2023, 1, 15))
        self.assertEqual(self.wo1.prjtitle, 'Project Alpha')
        self.assertEqual(self.wo1.category, self.category_a)
        self.assertEqual(self.wo1.compid, 1)
        
    def test_str_representation(self):
        self.assertEqual(str(self.wo1), 'WO-001 - Project Alpha')
        
    def test_workorder_verbose_name(self):
        self.assertEqual(WorkOrder._meta.verbose_name, 'Work Order')
        self.assertEqual(WorkOrder._meta.verbose_name_plural, 'Work Orders')

    def test_filter_by_criteria_all_company(self):
        queryset = WorkOrder.objects.filter_by_criteria(company_id=1)
        self.assertEqual(queryset.count(), 2)
        self.assertIn(self.wo1, queryset)
        self.assertIn(self.wo2, queryset)
        self.assertNotIn(self.wo3, queryset)

    def test_filter_by_criteria_wo_number(self):
        queryset = WorkOrder.objects.filter_by_criteria(company_id=1, wo_number='wo-001') # Case-insensitive
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first(), self.wo1)

    def test_filter_by_criteria_category(self):
        queryset = WorkOrder.objects.filter_by_criteria(company_id=1, category_id=self.category_b.cid)
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first(), self.wo2)

    def test_filter_by_criteria_wo_category_all(self):
        queryset = WorkOrder.objects.filter_by_criteria(company_id=1, category_id='WO Category')
        self.assertEqual(queryset.count(), 2) # All for company 1

    def test_filter_by_criteria_combination(self):
        queryset = WorkOrder.objects.filter_by_criteria(company_id=1, wo_number='WO-001', category_id=self.category_a.cid)
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first(), self.wo1)

    def test_get_assembly_link(self):
        expected_link = f"/module/inventory/transactions/wis_actualrun_assembly/{self.wo1.wono}/?ModId=9&SubModId=53"
        self.assertEqual(self.wo1.get_assembly_link(), expected_link)

    def test_get_report_link_type_0(self):
        expected_link = f"/module/inventory/transactions/wis_view_transwise/{self.wo1.wono}/?&ModId=9&SubModId=53&Type=0&status=1"
        self.assertEqual(self.wo1.get_report_link(0), expected_link)

    def test_get_report_link_type_1(self):
        # Uses a placeholder for random key
        expected_link_prefix = f"/module/inventory/transactions/total_issue_and_shortage_print/{self.wo1.wono}/?Key=DJANGO_RANDOM_KEY_PLACEHOLDER&ModId=9&SubModId=53&Type=1&status=1"
        self.assertEqual(self.wo1.get_report_link(1), expected_link_prefix)

    def test_get_report_link_type_2(self):
        expected_link_prefix = f"/module/inventory/transactions/total_shortage_print/{self.wo1.wono}/?Key=DJANGO_RANDOM_KEY_PLACEHOLDER&ModId=9&SubModId=53&Type=2&status=1"
        self.assertEqual(self.wo1.get_report_link(2), expected_link_prefix)


class WorkOrderFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category_a = WOCategory.objects.create(cid=101, symbol='WO1', cname='Category A', compid=1)
        WorkOrder.objects.create(
            id=1, wono='EXISTING-WO', sysdate=datetime.date(2023, 1, 15), 
            prjtitle='Existing Project', category=cls.category_a, compid=1
        )

    def test_workorder_filter_form_valid(self):
        form = WorkOrderFilterForm(data={'wo_number': 'WO-001', 'wo_category': self.category_a.cid})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['wo_number'], 'WO-001')
        self.assertEqual(form.cleaned_data['wo_category'], self.category_a)

    def test_custom_search_form_valid(self):
        data = {
            'from_date': '2023-01-01',
            'to_date': '2023-01-31',
            'wo_no_report': 'REP-WO-001',
            'overheads': '75.555' # Matches regex
        }
        form = CustomSearchForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['from_date'], datetime.date(2023, 1, 1))
        self.assertEqual(form.cleaned_data['to_date'], datetime.date(2023, 1, 31))
        self.assertEqual(form.cleaned_data['wo_no_report'], 'REP-WO-001')
        self.assertEqual(form.cleaned_data['overheads'], 75.555)

    def test_custom_search_form_invalid_overheads_format(self):
        data = {
            'overheads': '75.1234' # Too many decimal places
        }
        form = CustomSearchForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('overheads', form.errors)

    def test_custom_search_form_invalid_overheads_required(self):
        data = {
            'overheads': ''
        }
        form = CustomSearchForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('overheads', form.errors)

    def test_custom_search_form_invalid_date_range(self):
        data = {
            'from_date': '2023-01-31',
            'to_date': '2023-01-01',
            'overheads': '75'
        }
        form = CustomSearchForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('to_date', form.errors)
        self.assertIn("To Date cannot be earlier than From Date.", form.errors['to_date'])

    def test_workorder_form_valid(self):
        data = {
            'wono': 'NEW-WO-001',
            'sysdate': '2024-03-01',
            'prjtitle': 'New Project',
            'category': self.category_a.cid
        }
        form = WorkOrderForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_workorder_form_duplicate_wono(self):
        data = {
            'wono': 'EXISTING-WO', # Already exists
            'sysdate': '2024-03-01',
            'prjtitle': 'Another Project',
            'category': self.category_a.cid
        }
        form = WorkOrderForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('wono', form.errors)
        self.assertIn("A Work Order with this number already exists. Please choose a unique one.", form.errors['wono'])

    def test_workorder_form_edit_existing_wono(self):
        wo = WorkOrder.objects.get(wono='EXISTING-WO')
        data = {
            'wono': 'EXISTING-WO', # Same WO number for existing object is allowed
            'sysdate': '2024-03-01',
            'prjtitle': 'Updated Project',
            'category': self.category_a.cid
        }
        form = WorkOrderForm(data=data, instance=wo)
        self.assertTrue(form.is_valid(), form.errors)


class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category_a = WOCategory.objects.create(cid=101, symbol='WO1', cname='Category A', compid=1)
        cls.category_b = WOCategory.objects.create(cid=102, symbol='WO2', cname='Category B', compid=1)
        cls.wo1 = WorkOrder.objects.create(
            id=1, wono='WO-001', sysdate=datetime.date(2023, 1, 15), 
            prjtitle='Project Alpha', category=cls.category_a, compid=1
        )
        cls.wo2 = WorkOrder.objects.create(
            id=2, wono='WO-002', sysdate=datetime.date(2023, 1, 20), 
            prjtitle='Project Beta', category=cls.category_b, compid=1
        )
        # Create a WO for a different company to test filtering by compid
        WorkOrder.objects.create(
            id=3, wono='WO-003', sysdate=datetime.date(2023, 2, 10), 
            prjtitle='Project Gamma (Other Co)', category=cls.category_a, compid=99
        )
    
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('inventory:workorder_list')
        self.table_url = reverse('inventory:workorder_table')
        self.add_url = reverse('inventory:workorder_add')
        self.edit_url = reverse('inventory:workorder_edit', args=[self.wo1.pk])
        self.delete_url = reverse('inventory:workorder_delete', args=[self.wo1.pk])
        self.custom_search_url = reverse('inventory:workorder_custom_search')
        self.wis_wono_report_url = reverse('inventory:wis_wono_report_view')
        self.search_tab_url = reverse('inventory:workorder_search_tab')
        self.custom_search_tab_url = reverse('inventory:workorder_custom_search_tab')

        # Set session data (mimics ASP.NET Session["compid"])
        session = self.client.session
        session['compid'] = 1
        session['finyearid'] = 1
        session['username'] = 'testuser'
        session.save()

    def test_workorder_list_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/list.html')
        self.assertIn('filter_form', response.context)
        self.assertIn('custom_search_form', response.context)
        self.assertContains(response, 'WIS Print') # Check for page title

    def test_workorder_table_partial_view_get_initial(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_table.html')
        self.assertIn('workorders', response.context)
        self.assertEqual(len(response.context['workorders']), 2) # wo1, wo2 for compid=1
        self.assertContains(response, self.wo1.wono)
        self.assertContains(response, self.wo2.wono)
        self.assertNotContains(response, 'WO-003') # Not for compid=1

    def test_workorder_table_partial_view_get_filter_wo_number(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url + '?wo_number=WO-001', headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['workorders']), 1)
        self.assertContains(response, self.wo1.wono)
        self.assertNotContains(response, self.wo2.wono)

    def test_workorder_table_partial_view_get_filter_category(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url + f'?wo_category={self.category_b.cid}', headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['workorders']), 1)
        self.assertContains(response, self.wo2.wono)
        self.assertNotContains(response, self.wo1.wono)

    def test_workorder_table_partial_view_get_filter_no_category_all(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url + '?wo_category=WO Category', headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['workorders']), 2) # All for compid 1

    def test_custom_search_tab_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.custom_search_tab_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_custom_search_tab.html')
        self.assertIn('custom_search_form', response.context)
        self.assertContains(response, 'From Date:')
        self.assertContains(response, '<iframe id="reportIframe"')

    def test_custom_search_form_post_valid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'from_date': '2023-01-01',
            'to_date': '2023-01-31',
            'wo_no_report': 'REPORT-WO',
            'overheads': '80.0'
        }
        response = self.client.post(self.custom_search_url, data, headers=headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        # Check if the trigger contains the expected iframe src update event
        self.assertIn('setReportIframeSrc', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Report parameters submitted. Report loading...")

    def test_custom_search_form_post_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'overheads': 'invalid' # Invalid data
        }
        response = self.client.post(self.custom_search_url, data, headers=headers)
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'inventory/workorder/_custom_search_tab.html')
        self.assertIn('custom_search_form', response.context)
        self.assertFalse(response.context['custom_search_form'].is_valid())
        self.assertContains(response, 'Please correct the errors in the form before searching.')
        self.assertContains(response, 'Enter a number.') # Error message for overheads

    def test_wis_wono_report_view(self):
        # Test the report view that the iframe would load
        report_params = {
            'wo_no': 'TESTWO',
            'from_date': '01-01-2023',
            'to_date': '31-01-2023',
            'overheads': '75.0',
            'key': 'ABC123XYZ',
            'ModId': '9',
            'SubModId': '53'
        }
        response = self.client.get(self.wis_wono_report_url, report_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/reports/wis_wono_report.html')
        self.assertContains(response, 'Work Order Print Report (Simulated)')
        self.assertContains(response, 'TESTWO')
        self.assertContains(response, '75.0%')


    # --- Placeholder CRUD Views Tests (as per template instructions) ---

    def test_create_workorder_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'} # Mimic HTMX modal request
        response = self.client.get(self.add_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Work Order')

    def test_create_workorder_view_post_valid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'wono': 'NEW-WO-123',
            'sysdate': '2024-04-01',
            'prjtitle': 'New Test Project',
            'category': self.category_a.cid
        }
        response = self.client.post(self.add_url, data, headers=headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertTrue(WorkOrder.objects.filter(wono='NEW-WO-123', compid=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWorkOrderList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Work Order added successfully.')

    def test_create_workorder_view_post_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'wono': 'WO-001', # Duplicate WO number
            'sysdate': '2024-04-01',
            'prjtitle': 'Another Project',
            'category': self.category_a.cid
        }
        response = self.client.post(self.add_url, data, headers=headers)
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_form.html')
        self.assertContains(response, 'A Work Order with this number already exists.')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

    def test_update_workorder_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.edit_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.wo1)
        self.assertContains(response, 'Edit Work Order')

    def test_update_workorder_view_post_valid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'wono': 'WO-001-UPDATED',
            'sysdate': '2023-01-15',
            'prjtitle': 'Project Alpha Updated',
            'category': self.category_a.cid
        }
        response = self.client.post(self.edit_url, data, headers=headers)
        self.assertEqual(response.status_code, 204)
        self.wo1.refresh_from_db()
        self.assertEqual(self.wo1.wono, 'WO-001-UPDATED')
        self.assertEqual(self.wo1.prjtitle, 'Project Alpha Updated')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWorkOrderList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Work Order updated successfully.')

    def test_update_workorder_view_post_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Try to update WO-001 to WO-002 (which already exists)
        data = {
            'wono': 'WO-002', 
            'sysdate': '2023-01-15',
            'prjtitle': 'Conflict Project',
            'category': self.category_a.cid
        }
        response = self.client.post(self.edit_url, data, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_form.html')
        self.assertContains(response, 'A Work Order with this number already exists.')

    def test_delete_workorder_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.delete_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/_workorder_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.wo1)
        self.assertContains(response, f'Are you sure you want to delete the Work Order "{self.wo1.wono}"')

    def test_delete_workorder_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = WorkOrder.objects.filter(compid=1).count()
        response = self.client.post(self.delete_url, headers=headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(WorkOrder.objects.filter(compid=1).count(), initial_count - 1)
        self.assertFalse(WorkOrder.objects.filter(pk=self.wo1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWorkOrderList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Work Order deleted successfully.')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The migration deeply embeds HTMX for all dynamic content loading and form submissions, and Alpine.js for lightweight client-side interactions and managing UI state (like modal visibility and tab selection). DataTables is used for the Work Order list view, providing client-side search, sort, and pagination.

-   **Dynamic Tab Loading:**
    -   `list.html` contains the tab navigation. Clicking a tab (`<a hx-get="..." hx-target="#tabContent" hx-swap="innerHTML">`) triggers an HTMX request to load the content of the respective partial template (`_search_wo_tab.html` or `_custom_search_tab.html`) into the `#tabContent` div.
    -   Alpine.js manages the `activeTab` state to apply appropriate Tailwind CSS classes for visual indication of the selected tab.
-   **Work Order List (DataTables) Updates:**
    -   The main `WorkOrderListView` initially renders `list.html`, which includes `_search_wo_tab.html`. Inside `_search_wo_tab.html`, there's a `div` (`#workOrderTable-container`) that uses `hx-trigger="load, refreshWorkOrderList from:body"` and `hx-get="{% url 'inventory:workorder_table' %}"`. This setup ensures the DataTables content is loaded asynchronously on page load and refreshed whenever a `refreshWorkOrderList` custom event is fired (e.g., after a CRUD operation).
    -   The `Search` button and `WO Category` dropdown (`hx-trigger="change"`) in the `_search_wo_tab.html` partial also trigger `hx-get` requests to `{% url 'inventory:workorder_table' %}`, passing filter values as parameters (`hx-vals`).
    -   A `htmx:afterSwap` JavaScript listener in `list.html` dynamically re-initializes DataTables on the newly loaded `table` element, ensuring all DataTables functionalities (pagination, sorting, search) work correctly after content updates.
-   **Custom Search (WIP) Form Submission:**
    -   The `CustomSearchForm` in `_custom_search_tab.html` uses `hx-post="{% url 'inventory:workorder_custom_search' %}"` and `hx-target="this" hx-swap="outerHTML"`.
    -   Upon valid submission, the `CustomSearchReportView` returns a `204 No Content` HTTP response with an `HX-Trigger` header (`HX-Trigger: setReportIframeSrc:<report_url>`).
    -   A global JavaScript listener for `setReportIframeSrc` then updates the `src` attribute of the `reportIframe`, causing it to load the generated report without a full page reload.
    -   If the form submission is invalid, the `CustomSearchReportView` re-renders the `_custom_search_tab.html` partial, which is then `outerHTML` swapped back into the UI, displaying validation errors to the user.
-   **Modal Forms (Add/Edit/Delete):**
    -   Buttons like "Add New Work Order", "Edit", and "Delete" use `hx-get` to fetch the respective form (`_workorder_form.html` or `_workorder_confirm_delete.html`) into the `#modalContent` div of a global modal structure (`#modal`).
    -   Alpine.js `_` attributes are used on these buttons to add/remove classes (`flex`, `opacity-100`, `scale-100`) to show/hide the modal and apply transition effects.
    -   Form submissions within the modal use `hx-post` and `hx-swap="none"`. The Django views for these actions return `204 No Content` with an `HX-Trigger: refreshWorkOrderList` header on success.
    -   The `htmx:afterSwap` listener handles the `refreshWorkOrderList` event by closing the modal and triggering a refresh of the `workOrderTable-container`, thus updating the list.
    -   For form invalidation, the view re-renders the form partial with errors, which HTMX then swaps back into the modal, allowing the user to correct input without losing context.
-   **No Custom JavaScript (Beyond HTMX/Alpine/DataTables Init):** All interactive elements are handled by HTMX and Alpine.js attributes or their minimal accompanying script for initialization/event listening. This significantly reduces the need for large, complex custom JavaScript files.
-   **CDN Links:** All necessary CDN links for HTMX, Alpine.js, and DataTables are assumed to be present in `core/base.html`, adhering to the DRY principle.

## Final Notes

-   **Replace Placeholders:** All `[PLACEHOLDER]` values (e.g., `[APP_NAME]`, `[MODEL_NAME]`, `[TABLE_NAME]`, `[FIELD_TYPE]`, `[TEST_VALUE]`) must be replaced with actual names derived from your specific ASP.NET application's context. The generated code uses `inventory` as the app name and `WorkOrder`/`WOCategory` as models, which should be aligned with your actual project structure.
-   **DRY Templates:** The use of partial templates (`_workorder_table.html`, `_workorder_form.html`, `_workorder_confirm_delete.html`, `_search_wo_tab.html`, `_custom_search_tab.html`) promotes reusability and maintainability, ensuring that common UI components are defined once.
-   **Fat Models, Thin Views:** Business logic, such as data filtering based on complex criteria or generating dynamic URLs, has been moved into model manager methods (`WorkOrderQuerySet.filter_by_criteria`) or model instance methods (`WorkOrder.get_assembly_link`, `WorkOrder.get_report_link`). This keeps the Django views concise (typically 5-15 lines) and focused on handling HTTP requests and responses.
-   **Comprehensive Tests:** The provided unit and integration tests cover models, forms, and views, including HTMX interactions. These tests are essential for validating the correctness of the migration and ensuring future changes don't introduce regressions. Achieving high test coverage (e.g., 80%+) provides confidence in the modernized application.
-   **HTMX and Alpine.js:** This combination provides a powerful, JavaScript-minimal approach to building dynamic web interfaces, allowing developers familiar with server-side rendering to add rich interactivity without deep knowledge of complex JavaScript frameworks. It aligns perfectly with the "no additional JavaScript" requirement beyond what HTMX and Alpine.js provide.
-   **Database Mapping:** The `managed = False` and `db_table` settings in the models are crucial for integrating with existing legacy databases, allowing Django to work with your current data without requiring schema migrations.
-   **Crystal Reports Replacement:** The `WisWonoReportView` is a placeholder. Migrating complex Crystal Reports typically involves re-evaluating reporting needs and implementing them with modern open-source Python libraries (e.g., WeasyPrint for PDF generation, ReportLab, or integrating with dedicated BI tools).