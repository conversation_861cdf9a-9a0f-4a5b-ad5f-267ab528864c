This modernization plan details the transition of your ASP.NET Goods Service Note (GSN) application to a modern Django-based solution. We'll leverage AI-assisted automation by focusing on a systematic, component-by-component conversion. The goal is to move from a legacy, tightly coupled architecture to a clean, maintainable, and scalable Django application.

Our strategy prioritizes the "Fat Model, Thin View" approach, ensuring that business logic resides primarily within your Django models, making your application easier to understand, test, and maintain. We will also implement a dynamic, responsive user interface using HTMX and Alpine.js, minimizing JavaScript complexity and eliminating full page reloads. Data presentation will be enhanced with DataTables for efficient data handling.

## ASP.NET to Django Conversion Plan: Goods Service Note

**Business Value:**
This migration will transform a rigid, manual-intensive ASP.NET application into a flexible, automated Django system. Key benefits include:
1.  **Reduced Manual Effort:** Automating data validation, sequence generation, and complex transaction logic minimizes human error and speeds up GSN processing.
2.  **Improved User Experience:** A modern, dynamic interface with HTMX and Alpine.js provides a smoother, faster interaction, reducing user frustration and increasing productivity.
3.  **Enhanced Maintainability:** By centralizing business rules in "Fat Models" and separating concerns, the application becomes easier to update, debug, and expand, leading to lower long-term maintenance costs.
4.  **Scalability:** Django's robust architecture and efficient ORM allow the application to scale with your business needs, accommodating more users and transactions without performance degradation.
5.  **Future-Proofing:** Moving to an open-source, actively maintained framework like Django positions your ERP for continuous innovation and integration with other modern systems.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the current `inventory_transactions` module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
Based on the ASP.NET code-behind, the primary tables involved in this module, particularly for the "Add" operation and data display, are:

-   `tblinv_MaterialServiceNote_Master`: Stores high-level Goods Service Note details.
-   `tblinv_MaterialServiceNote_Details`: Stores line-item details for each GSN.
-   `tblInv_Inward_Master`: Provides general inward information (GIN No, Challan No, Date).
-   `tblInv_Inward_Details`: Details of items received through inward, linked to PO details.
-   `tblMM_Supplier_master`: Stores supplier information.
-   `tblDG_Item_Master`: Contains item definitions, stock quantity, and manufacturing descriptions.
-   `tblMM_PO_Details`: Stores details of Purchase Order items, including quantity and linked item.
-   `Unit_Master`: Defines Units of Measurement (UOM).
-   `SD_Cust_WorkOrder_Master`: Contains work order information, specifically the `ReleaseWIS` flag that triggers auto-MRS/MIN.
-   `tblInv_MaterialRequisition_Master`: Master for Material Requisition (MRS).
-   `tblInv_MaterialRequisition_Details`: Details for MRS.
-   `tblInv_MaterialIssue_Master`: Master for Material Issue (MIN).
-   `tblInv_MaterialIssue_Details`: Details for MIN.

**Inferred Columns (key columns relevant to this module):**

-   **`tblinv_MaterialServiceNote_Master`**: `Id` (PK), `SysDate`, `SysTime`, `CompId`, `SessionId`, `FinYearId`, `GSNNo`, `GINNo`, `GINId` (FK to `tblInv_Inward_Master`), `TaxInvoiceNo`, `TaxInvoiceDate`.
-   **`tblinv_MaterialServiceNote_Details`**: `Id` (PK), `MId` (FK to `tblinv_MaterialServiceNote_Master`), `GSNNo` (denormalized), `POId` (FK to `tblMM_PO_Details`), `ReceivedQty`.
-   **`tblInv_Inward_Master`**: `Id` (PK), `GINNo`, `ChallanNo`, `ChallanDate`, `PONo`, `POMId`, `CompId`, `FinYearId`.
-   **`tblInv_Inward_Details`**: `Id` (PK), `GINId` (FK to `tblInv_Inward_Master`), `POId` (FK to `tblMM_PO_Details`), `ReceivedQty`, `PRSPRFlag`.
-   **`tblMM_Supplier_master`**: `SupplierId` (PK), `SupplierName`, `CompId`.
-   **`tblDG_Item_Master`**: `Id` (PK), `ItemCode`, `ManfDesc`, `UOMBasic` (FK to `Unit_Master`), `StockQty`, `Process`, `CompId`.
-   **`tblMM_PO_Details`**: `Id` (PK), `ItemId` (FK to `tblDG_Item_Master`), `Qty`, `PRId`, `SPRId`.
-   **`Unit_Master`**: `Id` (PK), `Symbol`.
-   **`SD_Cust_WorkOrder_Master`**: `Id` (PK), `WONo`, `ReleaseWIS`, `CompId`.
-   **`tblInv_MaterialRequisition_Master`**: `Id` (PK), `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `MRSNo`.
-   **`tblInv_MaterialRequisition_Details`**: `Id` (PK), `MId` (FK to `tblInv_MaterialRequisition_Master`), `MRSNo`, `ItemId` (FK to `tblDG_Item_Master`), `WONo`, `DeptId`, `ReqQty`, `Remarks`.
-   **`tblInv_MaterialIssue_Master`**: `Id` (PK), `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `MINNo`, `MRSNo` (denormalized), `MRSId` (FK to `tblInv_MaterialRequisition_Master`).
-   **`tblInv_MaterialIssue_Details`**: `Id` (PK), `MId` (FK to `tblInv_MaterialIssue_Master`), `MINNo` (denormalized), `MRSId` (FK to `tblInv_MaterialRequisition_Details`), `IssueQty`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

-   **Create (Add):** The `btnInsert_Click` event handler is responsible for creating new Goods Service Note (GSN) records. This involves:
    -   Generating unique GSN, MRS, and MIN numbers.
    -   Inserting records into `tblinv_MaterialServiceNote_Master` and `tblinv_MaterialServiceNote_Details`.
    -   Updating `StockQty` in `tblDG_Item_Master` for received items.
    -   Conditionally triggering automatic Material Requisition (MRS) and Material Issue (MIN) processes by inserting into `tblInv_MaterialRequisition_Master`, `tblInv_MaterialRequisition_Details`, `tblInv_MaterialIssue_Master`, and `tblInv_MaterialIssue_Details`, and subsequently updating `StockQty` again if an issue occurs.
    -   Validation of input fields (`txtTaxInvoice`, `txtDate`, `txtrecQty`) and quantities (e.g., `recqty1 <= (inwqty - totrecqty)`).
-   **Read (Display):** The `Page_Load` event and `loadData()` function perform read operations to display existing inward details. This involves:
    -   Fetching basic GIN, Challan, Date, and Supplier details based on query string parameters.
    -   A complex SQL query (mimicked by `loadData()`) that joins multiple tables (`tblInv_Inward_Master`, `tblInv_Inward_Details`, `tblMM_PO_Master`, `tblMM_PR_Details`, `tblMM_SPR_Details`, `tblDG_Item_Master`, `Unit_Master`) to populate the `GridView2` with item details, PO quantities, inward quantities, and previously received GSN quantities.
    -   Calculation of remaining quantities for GSN.
-   **Update:** No direct update functionality for existing GSNs or inward records is explicitly shown in the provided code. The code focuses on *creating* new GSN entries which affect stock and potentially other related transactions.
-   **Delete:** No delete functionality is shown.
-   **Validation Logic:** Required field validators for `txtTaxInvoice`, `txtDate`, and `txtrecQty`. Regular expression validators for `txtDate` (date format) and `txtrecQty` (numeric quantity). Logic to hide checkboxes and quantity input if the remaining quantity is zero.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

-   **Header/Basic Info:** `lblGIn`, `lblChNo`, `lblDate`, `lblSupplier`. These display read-only information about the current Goods Inward Note (GIN) and its supplier.
-   **Input Fields:**
    -   `txtTaxInvoice`: Text input for the Tax Invoice Number.
    -   `txtDate`: Text input for the Tax Invoice Date, with a `CalendarExtender` for date picking.
-   **Main Data Grid:** `GridView2` is the central component for displaying a list of items from the associated GIN. It features:
    -   **SN (Serial Number):** Auto-generated row number.
    -   **Checkbox (`ck`):** Allows selection of items for GSN processing, with `AutoPostBack` to trigger client-side visibility logic for quantity input.
    -   **Read-only Labels:** `lblItemCode`, `lblpurchDesc` (Description), `lbluompurch` (UOM), `lblpoqty` (PO Qty), `lblInwrdqty` (Inward Qty), `lbltotrecevdqty` (Total Received GSN Qty). These display item details and quantities.
    -   **`Reced Qty` (Received Quantity):** Contains a hidden `lblrecevdqty` (label showing remaining quantity) and a `txtrecQty` (text box for user input, initially hidden, made visible when checkbox is checked). This is where the user enters the quantity for the current GSN. It has client-side validation for numeric input and range.
-   **Action Buttons:**
    -   `btnInsert`: "Add" button to submit the form and process the GSN creation.
    -   `btnCancel`: "Cancel" button to navigate back.

The ASP.NET validators and client-side scripts (`loadingNotifier.js`, `PopUpMsg.js`) will be replaced by Django's form validation and HTMX/Alpine.js for dynamic UI interactions. The `yui-datatable.css` will be replaced by DataTables integration with Tailwind CSS.

---

### Step 4: Generate Django Code

We'll place all module-specific code within a Django app named `inventory_transactions`.

#### 4.1 Models (`inventory_transactions/models.py`)

**Task:** Create Django models based on the identified database schema. We include helper functions for sequence generation.

**Instructions:**
-   Each model maps directly to an existing database table.
-   `managed = False` is crucial as we are working with an existing database.
-   `db_table` explicitly defines the table name.
-   `DecimalField` is used for all quantity fields to ensure precision.
-   Foreign keys are defined using `models.ForeignKey` and `db_column`.
-   The `GoodsServiceNoteMaster.create_gsn_with_details` method encapsulates the entire complex business logic from `btnInsert_Click`.
-   `InwardDetail.get_items_for_gsn_creation` handles the complex query logic from `loadData`.

```python
from django.db import models, transaction
from django.utils import timezone
from datetime import date
import decimal

# --- Utility Functions ---

def generate_next_sequence_number(prefix_field_name, model_class, company_id, financial_year_id):
    """
    Generates the next sequential number (e.g., '0001', '0002') for a given model
    based on Company ID and Financial Year ID.
    Ensures thread-safe sequence generation using a database lock within a transaction.
    """
    with transaction.atomic():
        # Get the highest current number for the given company and financial year
        # Use select_for_update() to lock the row and prevent race conditions
        last_obj = model_class.objects.filter(
            CompId=company_id,
            FinYearId=financial_year_id
        ).order_by(f'-{prefix_field_name}').select_for_update().first() # Lock the last object

        if last_obj and getattr(last_obj, prefix_field_name):
            try:
                last_num = int(getattr(last_obj, prefix_field_name))
                next_num = last_num + 1
            except (ValueError, TypeError):
                next_num = 1 # Fallback if sequence field isn't purely numeric
        else:
            next_num = 1
        
        return f"{next_num:04d}" # Format as 4-digit string


# --- Core Models for Goods Service Note ---

class Supplier(models.Model):
    # Assuming primary key is 'SupplierId' and not 'id'
    SupplierId = models.AutoField(db_column='SupplierId', primary_key=True)
    SupplierName = models.CharField(db_column='SupplierName', max_length=255)
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.SupplierName

class Unit(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.Symbol

class Item(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    ItemCode = models.CharField(db_column='ItemCode', max_length=50, null=True, blank=True)
    ManfDesc = models.CharField(db_column='ManfDesc', max_length=255, null=True, blank=True)
    UOMBasic = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')
    StockQty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, default=decimal.Decimal('0.000'))
    Process = models.CharField(db_column='Process', max_length=50, null=True, blank=True)
    CompId = models.IntegerField(db_column='CompId', null=True, blank=True) 

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.ItemCode} - {self.ManfDesc}"

    def get_item_code_part_no(self, company_id):
        # This function 'fun.GetItemCode_PartNo' from ASP.NET suggests a specific way
        # to get an item's display code. For this example, we assume it's simply the ItemCode.
        # If it involves more complex logic (e.g., concatenating with PartNo from another table),
        # that logic would be implemented here.
        return self.ItemCode

    @transaction.atomic
    def update_stock(self, quantity, is_increase=True):
        """Updates the item's stock quantity. Assumes quantity is positive."""
        if not isinstance(quantity, decimal.Decimal):
            quantity = decimal.Decimal(str(quantity))

        # Lock the row for update to prevent race conditions in stock updates
        item_to_update = Item.objects.select_for_update().get(pk=self.pk)
        
        if is_increase:
            item_to_update.StockQty += quantity
        else:
            if item_to_update.StockQty < quantity:
                # Log an error or raise a more specific exception for insufficient stock
                raise ValueError(f"Insufficient stock for {self.ItemCode}. Available: {item_to_update.StockQty}, Required: {quantity}")
            item_to_update.StockQty -= quantity
        item_to_update.save(update_fields=['StockQty'])
        self.StockQty = item_to_update.StockQty # Update current object's StockQty

class PurchaseOrderDetail(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    ItemId = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='po_details')
    Qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, default=decimal.Decimal('0.000'))
    # Assuming PRId and SPRId might be integer fields or foreign keys if needed for specific logic.
    PRId = models.IntegerField(db_column='PRId', null=True, blank=True)
    SPRId = models.IntegerField(db_column='SPRId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"PO Detail {self.Id} for Item {self.ItemId.ItemCode}"

class InwardMaster(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    GINNo = models.CharField(db_column='GINNo', max_length=50)
    ChallanNo = models.CharField(db_column='ChallanNo', max_length=50)
    ChallanDate = models.DateField(db_column='ChallanDate')
    PONo = models.CharField(db_column='PONo', max_length=50) 
    POMId = models.IntegerField(db_column='POMId') # Foreign Key to tblMM_PO_Master
    CompId = models.IntegerField(db_column='CompId')
    FinYearId = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Master'
        verbose_name_plural = 'Inward Masters'

    def __str__(self):
        return self.GINNo

class InwardDetail(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True) # PK for InwardDetail row
    GINId = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', related_name='details')
    POId = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='POId', related_name='inward_details') # This POId actually refers to tblMM_PO_Details.Id
    ReceivedQty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, default=decimal.Decimal('0.000')) # This is the inward quantity for this item
    PRSPRFlag = models.BooleanField(db_column='PRSPRFlag') # 0 for PR, 1 for SPR

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Inward Detail'
        verbose_name_plural = 'Inward Details'

    def __str__(self):
        return f"Inward Detail {self.Id} for GIN {self.GINId.GINNo}"

    @classmethod
    def get_items_for_gsn_creation(cls, gin_id, company_id):
        """
        Mimics the complex loadData function to get items for GSN creation.
        Returns a list of dictionaries, each representing a row in the GridView.
        This method will fetch necessary data from multiple related tables.
        """
        from django.db.models import Sum, F, ExpressionWrapper, DecimalField, Case, When

        # Fetch inward details related to the GIN ID for the specified company
        inward_details_qs = cls.objects.filter(GINId=gin_id, GINId__CompId=company_id) \
                                .select_related('POId__ItemId__UOMBasic', 'GINId') \
                                .only(
                                    'POId__ItemId__Id', 
                                    'POId__ItemId__ItemCode', 
                                    'POId__ItemId__ManfDesc', 
                                    'POId__ItemId__UOMBasic__Symbol', 
                                    'POId__Qty', 
                                    'ReceivedQty', 
                                    'POId', 
                                    'PRSPRFlag',
                                    'GINId__PONo', # Assuming PONo from InwardMaster for WONo link for simplicity,
                                                   # as actual ASP.NET code had more complex lookup for WONo
                                    'POId__PRId', # For PRSPRFlag 0
                                    'POId__SPRId' # For PRSPRFlag 1
                                ).iterator() # Use iterator for potentially large querysets

        result_data = []
        for detail in inward_details_qs:
            item_id = detail.POId.ItemId.Id
            po_detail_id = detail.POId.Id

            # Calculate GSNQty (TotRecQty in ASP.NET)
            # This is the total quantity already noted in MaterialServiceNote for this item and GIN
            gsn_qty_agg = GoodsServiceNoteDetail.objects.filter(
                MId__GINId=gin_id,
                POId=po_detail_id
            ).aggregate(total_gsn_qty=Sum('ReceivedQty'))
            gsn_qty = gsn_qty_agg['total_gsn_qty'] or decimal.Decimal('0.000')

            inward_qty = detail.ReceivedQty
            po_qty = detail.POId.Qty

            # TotRemainQty in ASP.NET code: (InvQty - TotRecQty) which is (inward_qty - gsn_qty)
            tot_remain_qty = inward_qty - gsn_qty

            # Determine WONo based on PRSPRFlag, similar to ASP.NET logic
            # This is a simplification; a more robust solution might involve direct FKs or a dedicated manager.
            # ASP.NET code had 'tblMM_PR_Master.WONo' or 'tblMM_SPR_Details.WONo'
            # We'll use GINId__PONo as a fallback for now due to lack of full schema for PR/SPR masters.
            wo_no = detail.GINId.PONo 
            # In a real migration, this would be a lookup like:
            # if not detail.PRSPRFlag: # PR
            #     pr_master = PRMaster.objects.filter(Id=detail.POId.PRId).first()
            #     if pr_master: wo_no = pr_master.WONo
            # else: # SPR
            #     spr_detail = SPRDetail.objects.filter(Id=detail.POId.SPRId).first()
            #     if spr_detail: wo_no = spr_detail.WONo

            row = {
                'item_id': item_id,
                'item_code': Item.objects.get(Id=item_id).get_item_code_part_no(company_id),
                'description': detail.POId.ItemId.ManfDesc,
                'uom': detail.POId.ItemId.UOMBasic.Symbol,
                'po_qty': po_qty,
                'inward_qty': inward_qty,
                'po_detail_id': po_detail_id, # This is the 'Id' column in the ASP.NET GridView
                'total_received_gsn_qty': gsn_qty,
                'remaining_qty_for_gsn': tot_remain_qty,
                'wo_no': wo_no,
                'can_receive': tot_remain_qty > decimal.Decimal('0.000') # For checkbox visibility
            }
            result_data.append(row)
        return result_data


class GoodsServiceNoteMaster(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    SysDate = models.DateField(db_column='SysDate')
    SysTime = models.TimeField(db_column='SysTime')
    CompId = models.IntegerField(db_column='CompId')
    SessionId = models.CharField(db_column='SessionId', max_length=50) # User ID/Username
    FinYearId = models.IntegerField(db_column='FinYearId')
    GSNNo = models.CharField(db_column='GSNNo', max_length=50, unique=True)
    GINNo = models.CharField(db_column='GINNo', max_length=50) # Denormalized from InwardMaster
    GINId = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', related_name='gsn_masters')
    TaxInvoiceNo = models.CharField(db_column='TaxInvoiceNo', max_length=100)
    TaxInvoiceDate = models.DateField(db_column='TaxInvoiceDate')

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'
        verbose_name = 'Goods Service Note Master'
        verbose_name_plural = 'Goods Service Note Masters'

    def __str__(self):
        return self.GSNNo

    @classmethod
    @transaction.atomic
    def create_gsn_with_details(cls, user_id, company_id, fin_year_id, gin_id, tax_invoice_no, tax_invoice_date, items_data):
        """
        Creates a new Goods Service Note (GSN) master and its details,
        updates item stock, and potentially triggers auto MRS/MIN.
        This method consolidates the complex btnInsert_Click logic from ASP.NET.
        It runs within a single atomic transaction for data consistency.
        """
        current_date = timezone.localdate()
        current_time = timezone.localtime().time()

        # 1. Generate GSN No
        gsn_no = generate_next_sequence_number('GSNNo', cls, company_id, fin_year_id)

        inward_master = InwardMaster.objects.get(Id=gin_id, CompId=company_id)

        # 2. Create GSN Master
        gsn_master = cls.objects.create(
            SysDate=current_date,
            SysTime=current_time,
            CompId=company_id,
            SessionId=user_id, # Assuming user_id is the string username from ASP.NET Session["username"]
            FinYearId=fin_year_id,
            GSNNo=gsn_no,
            GINNo=inward_master.GINNo, # Denormalized field
            GINId=inward_master,
            TaxInvoiceNo=tax_invoice_no,
            TaxInvoiceDate=tax_invoice_date,
        )

        mrs_master_instance = None # To hold a single MRS master for this batch if auto-MRS is triggered
        min_master_instance = None # To hold a single MIN master for this batch if auto-MIN is triggered

        # Pre-generate MRS and MIN numbers for this batch
        mrs_no = generate_next_sequence_number('MRSNo', MaterialRequisitionMaster, company_id, fin_year_id)
        min_no = generate_next_sequence_number('MINNo', MaterialIssueMaster, company_id, fin_year_id)

        for item_data in items_data:
            po_detail_id = item_data['po_detail_id']
            received_qty_str = item_data['received_qty']
            
            # Input validation for quantity. ASP.NET used client-side and server-side checks.
            # We'll use a try-except for conversion and enforce business rules here.
            try:
                received_qty = decimal.Decimal(received_qty_str)
            except decimal.InvalidOperation:
                raise ValueError(f"Invalid quantity for item {item_data['item_code']}.")

            # Validate received quantity against remaining quantity
            remaining_qty = item_data['remaining_qty_for_gsn'] # This was pre-calculated in get_items_for_gsn_creation
            if not (received_qty > decimal.Decimal('0.000') and received_qty <= remaining_qty):
                raise ValueError(f"Received quantity for item {item_data['item_code']} must be greater than 0 and not exceed remaining quantity.")

            # Get the item for stock update
            item = Item.objects.get(Id=item_data['item_id'])

            # 3. Create GSN Details
            GoodsServiceNoteDetail.objects.create(
                MId=gsn_master,
                GSNNo=gsn_no, # Denormalized field
                POId_id=po_detail_id, 
                ReceivedQty=received_qty
            )

            # 4. Update StockQty in tblDG_Item_Master (increase)
            item.update_stock(received_qty, is_increase=True)

            # 5. Check for Auto MRS/MIN (Mimic ASP.NET logic)
            # This logic is extracted from btnInsert_Click and is applied per item.
            # It checks if the work order for this item's WO No has ReleaseWIS enabled.
            work_order_master = WorkOrderMaster.objects.filter(
                CompId=company_id,
                WONo=item_data['wo_no'],
                ReleaseWIS=True
            ).first()

            # Ensure we only proceed if auto-WIS is enabled for this WO, WO is present, and item stock is > 0
            if work_order_master and item_data['wo_no'] and item.StockQty > decimal.Decimal('0.000'):
                # Ensure MRS Master is created only once per GSN batch
                if not mrs_master_instance:
                    mrs_master_instance = MaterialRequisitionMaster.objects.create(
                        SysDate=current_date,
                        SysTime=current_time,
                        CompId=company_id,
                        FinYearId=fin_year_id,
                        SessionId=user_id,
                        MRSNo=mrs_no
                    )
                
                # Auto MRS Detail
                # The ASP.NET code set ReqQty to current stock quantity after inward.
                MaterialRequisitionDetail.objects.create(
                    MId=mrs_master_instance,
                    MRSNo=mrs_no, # Denormalized
                    ItemId=item,
                    WONo=item_data['wo_no'],
                    DeptId=1, # Hardcoded '1' in ASP.NET code
                    ReqQty=item.StockQty, # Requested quantity is the current stock after inward!
                    Remarks='-' # Hardcoded '-' in ASP.NET
                )
                
                # Ensure MIN Master is created only once per GSN batch
                if not min_master_instance:
                    min_master_instance = MaterialIssueMaster.objects.create(
                        SysDate=current_date,
                        SysTime=current_time,
                        CompId=company_id,
                        FinYearId=fin_year_id,
                        SessionId=user_id,
                        MINNo=min_no,
                        MRSNo=mrs_no, # Denormalized
                        MRSId=mrs_master_instance # Link to MRS Master
                    )
                
                # Auto MIN Detail
                # The ASP.NET logic for issue_qty was complex: min(ReqQty, BalStkQty).
                # Here, ReqQty is set to Item.StockQty. So IssueQty will be Item.StockQty.
                issue_qty_to_issue = item.StockQty 

                # Find the MRS Detail that was just created, to link MIN Detail to it.
                mrs_detail_for_min = MaterialRequisitionDetail.objects.get(
                    MId=mrs_master_instance, ItemId=item, WONo=item_data['wo_no']
                )

                MaterialIssueDetail.objects.create(
                    MId=min_master_instance,
                    MINNo=min_no, # Denormalized
                    MRSId=mrs_detail_for_min, # Link to MRS Detail's ID
                    IssueQty=issue_qty_to_issue
                )

                # Update stock again (decrease for issue)
                item.update_stock(issue_qty_to_issue, is_increase=False)
        
        return gsn_master


class GoodsServiceNoteDetail(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    MId = models.ForeignKey(GoodsServiceNoteMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    GSNNo = models.CharField(db_column='GSNNo', max_length=50) # Denormalized, usually comes from MId
    POId = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='POId', related_name='gsn_details')
    ReceivedQty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'
        verbose_name = 'Goods Service Note Detail'
        verbose_name_plural = 'Goods Service Note Details'

    def __str__(self):
        return f"GSN Detail {self.Id} for GSN {self.MId.GSNNo}"


# --- Placeholder Models for Auto MRS/MIN Functionality ---
# These are simplified based on usage in the provided ASP.NET code.
# Full schema would need more detailed analysis.

class WorkOrderMaster(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    WONo = models.CharField(db_column='WONo', max_length=50, unique=True)
    ReleaseWIS = models.BooleanField(db_column='ReleaseWIS') # Work Order Issue System
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.WONo

class MaterialRequisitionMaster(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    SysDate = models.DateField(db_column='SysDate')
    SysTime = models.TimeField(db_column='SysTime')
    CompId = models.IntegerField(db_column='CompId')
    FinYearId = models.IntegerField(db_column='FinYearId')
    SessionId = models.CharField(db_column='SessionId', max_length=50)
    MRSNo = models.CharField(db_column='MRSNo', max_length=50, unique=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Master'
        verbose_name_plural = 'Material Requisition Masters'

    def __str__(self):
        return self.MRSNo

class MaterialRequisitionDetail(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    MId = models.ForeignKey(MaterialRequisitionMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    MRSNo = models.CharField(db_column='MRSNo', max_length=50) # Denormalized
    ItemId = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId')
    WONo = models.CharField(db_column='WONo', max_length=50, null=True, blank=True)
    DeptId = models.IntegerField(db_column='DeptId')
    ReqQty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3)
    Remarks = models.CharField(db_column='Remarks', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

    def __str__(self):
        return f"MRS Detail {self.Id} for MRS {self.MId.MRSNo}"

class MaterialIssueMaster(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    SysDate = models.DateField(db_column='SysDate')
    SysTime = models.TimeField(db_column='SysTime')
    CompId = models.IntegerField(db_column='CompId')
    FinYearId = models.IntegerField(db_column='FinYearId')
    SessionId = models.CharField(db_column='SessionId', max_length=50)
    MINNo = models.CharField(db_column='MINNo', max_length=50, unique=True)
    MRSNo = models.CharField(db_column='MRSNo', max_length=50) # Denormalized
    MRSId = models.ForeignKey(MaterialRequisitionMaster, on_delete=models.DO_NOTHING, db_column='MRSId')

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Master'
        verbose_name_plural = 'Material Issue Masters'

    def __str__(self):
        return self.MINNo

class MaterialIssueDetail(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    MId = models.ForeignKey(MaterialIssueMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    MINNo = models.CharField(db_column='MINNo', max_length=50) # Denormalized
    MRSId = models.ForeignKey(MaterialRequisitionDetail, on_delete=models.DO_NOTHING, db_column='MRSId') # Note: this links to MRS_Detail, not MRS_Master
    IssueQty = models.DecimalField(db_column='IssueQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Details'
        verbose_name = 'Material Issue Detail'
        verbose_name_plural = 'Material Issue Details'

    def __str__(self):
        return f"MIN Detail {self.Id} for MIN {self.MId.MINNo}"

```

#### 4.2 Forms (`inventory_transactions/forms.py`)

**Task:** Define a Django form for user input for `TaxInvoiceNo` and `TaxInvoiceDate`. The grid items will be handled dynamically via HTMX and not as a traditional formset.

**Instructions:**
-   Create a `ModelForm` for `GoodsServiceNoteMaster` to handle the top-level details.
-   Include custom validation for date format, mimicking ASP.NET's `DateValidation`.
-   Apply Tailwind CSS classes to widgets.

```python
from django import forms
from .models import GoodsServiceNoteMaster
from django.core.exceptions import ValidationError
import datetime
import re # For regular expression validation

class GoodsServiceNoteCreateForm(forms.ModelForm):
    # TaxInvoiceDate will be handled as a CharField for initial input, then converted to DateField
    # to match ASP.NET's dd-MM-yyyy format handling with CalendarExtender
    tax_invoice_date_str = forms.CharField(
        label='Tax Invoice Date',
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'dd-MM-yyyy',
            'x-mask': '99-99-9999', # Alpine.js masking for date format
            'autocomplete': 'off',
            'readonly': 'true' # Mimics ASP.NET attribute
        })
    )

    class Meta:
        model = GoodsServiceNoteMaster
        fields = ['TaxInvoiceNo'] # Only TaxInvoiceNo is a direct field, date is custom
        widgets = {
            'TaxInvoiceNo': forms.TextInput(attrs={
                'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Tax Invoice No.'
            }),
        }

    def clean_tax_invoice_date_str(self):
        date_str = self.cleaned_data['tax_invoice_date_str']
        # Mimic ASP.NET RegularExpressionValidator: ^([1-9]|0[1-9]|[12][0-9]|3[01])[- /.]([1-9]|0[1-9]|1[012])[- /.][0-9]{4}$
        date_regex = r"^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$" # Enforce dd-MM-yyyy format
        if not re.match(date_regex, date_str):
            raise ValidationError("Invalid date format. Please use DD-MM-YYYY.")
        
        try:
            # Convert dd-MM-yyyy to a date object
            return datetime.datetime.strptime(date_str, '%d-%m-%Y').date()
        except ValueError:
            raise ValidationError("Invalid date. Please enter a valid date (DD-MM-YYYY).")

    def clean(self):
        cleaned_data = super().clean()
        # Assign the cleaned date from tax_invoice_date_str to TaxInvoiceDate for model saving
        if 'tax_invoice_date_str' in cleaned_data:
            cleaned_data['TaxInvoiceDate'] = cleaned_data['tax_invoice_date_str']
            # Remove the temporary field as it's not part of the model's direct fields
            del cleaned_data['tax_invoice_date_str'] 
        return cleaned_data

```

#### 4.3 Views (`inventory_transactions/views.py`)

**Task:** Implement the main Goods Service Note creation page and an HTMX partial view for the item details table.

**Instructions:**
-   `GoodsServiceNoteCreateView` will handle the initial page load and form submission for `TaxInvoiceNo` and `TaxInvoiceDate`.
-   `InwardItemsTablePartialView` will render the dynamic DataTables content, populated by `InwardDetail.get_items_for_gsn_creation`. This mimics the `loadData` functionality.
-   Views remain thin, delegating complex logic to models.
-   HTMX triggers are used for successful form submissions.

```python
from django.views.generic import FormView, TemplateView
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction
import decimal

from .models import InwardMaster, InwardDetail, GoodsServiceNoteMaster, Supplier
from .forms import GoodsServiceNoteCreateForm

# Assume these values come from user session/context (e.g., from middleware or user profile)
# For demonstration, we'll hardcode them or pass as query params, similar to ASP.NET.
# In a real Django app, these would typically be part of a custom user model or a request context.
CURRENT_COMPANY_ID = 1 # Example
CURRENT_FIN_YEAR_ID = 1 # Example

class GoodsServiceNoteCreateView(FormView):
    template_name = 'inventory_transactions/goodsservicenote/create.html'
    form_class = GoodsServiceNoteCreateForm
    success_url = reverse_lazy('goodsservicenote_create') # Redirect back to the same page for new entry

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # ASP.NET used Request.QueryString["GINNo"], ["SupId"], ["Id"] (GINId), ["PONo"], ["FyId"]
        # We need to pass these via URL parameters or query parameters
        gin_no = self.request.GET.get('gin_no')
        supplier_id = self.request.GET.get('sup_id')
        gin_id = self.request.GET.get('gin_id')
        
        context['gin_no'] = gin_no
        context['gin_id'] = gin_id

        if gin_id:
            try:
                inward_master = InwardMaster.objects.get(Id=gin_id, CompId=CURRENT_COMPANY_ID)
                context['challan_no'] = inward_master.ChallanNo
                context['challan_date'] = inward_master.ChallanDate.strftime('%d-%m-%Y')
            except InwardMaster.DoesNotExist:
                messages.error(self.request, "Invalid GIN ID provided.")
                return context # Or redirect to an error page

        if supplier_id:
            try:
                supplier = Supplier.objects.get(SupplierId=supplier_id, CompId=CURRENT_COMPANY_ID)
                context['supplier_name'] = supplier.SupplierName
            except Supplier.DoesNotExist:
                messages.error(self.request, "Invalid Supplier ID provided.")
        
        return context

    def form_valid(self, form):
        # This handles the main form submission for Tax Invoice No. and Date
        tax_invoice_no = form.cleaned_data['TaxInvoiceNo']
        tax_invoice_date = form.cleaned_data['TaxInvoiceDate'] # Cleaned date object from custom field

        gin_id = self.request.GET.get('gin_id')
        if not gin_id:
            messages.error(self.request, "GIN ID is missing for GSN creation.")
            return self.form_invalid(form)

        # Extract items data from the request body (HTMX sends this as JSON or form data)
        # Assuming items_data comes from a hidden field or JS submission as structured JSON
        # For simplicity, we'll assume it's passed as a JSON string in a hidden input named 'items_json'
        try:
            import json
            items_json_str = self.request.POST.get('items_json')
            if not items_json_str:
                messages.error(self.request, "No items selected for GSN.")
                return self.form_invalid(form)
            
            items_data = json.loads(items_json_str)
            if not items_data:
                messages.error(self.request, "No valid items data received for GSN.")
                return self.form_invalid(form)

            # Pass user.username (or user.pk if using ID) to the model method
            current_user_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous'
            
            # Delegate complex business logic to the model's static method
            gsn_master = GoodsServiceNoteMaster.create_gsn_with_details(
                user_id=current_user_id,
                company_id=CURRENT_COMPANY_ID,
                fin_year_id=CURRENT_FIN_YEAR_ID,
                gin_id=gin_id,
                tax_invoice_no=tax_invoice_no,
                tax_invoice_date=tax_invoice_date,
                items_data=items_data
            )
            messages.success(self.request, f"Goods Service Note {gsn_master.GSNNo} created successfully!")
            
            # HTMX response for success - trigger a refresh and close modal/clear form
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204, # No Content
                    headers={'HX-Trigger': 'gsnCreated'} # Custom event to handle UI updates
                )
            return super().form_valid(form) # Standard redirect
        
        except InwardMaster.DoesNotExist:
            messages.error(self.request, "Invalid GIN ID. Cannot create GSN.")
        except Item.DoesNotExist as e:
            messages.error(self.request, f"Item not found: {e}. Cannot create GSN.")
        except ValueError as e:
            messages.error(self.request, f"Validation error: {e}")
        except Exception as e:
            # Catch broader exceptions for unexpected errors
            messages.error(self.request, f"An unexpected error occurred: {e}")
        
        return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        if self.request.headers.get('HX-Request'):
            # If it's an HTMX request, render the form again with errors
            return render(self.request, self.template_name, self.get_context_data(form=form))
        return super().form_invalid(form)


class InwardItemsTablePartialView(TemplateView):
    template_name = 'inventory_transactions/goodsservicenote/_inward_items_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        gin_id = self.request.GET.get('gin_id')
        
        if gin_id:
            try:
                # Call the model method to get the prepared data for the table
                context['inward_items'] = InwardDetail.get_items_for_gsn_creation(
                    gin_id=gin_id, company_id=CURRENT_COMPANY_ID
                )
            except InwardMaster.DoesNotExist:
                context['error_message'] = "Invalid GIN ID. No items to display."
                context['inward_items'] = []
            except Exception as e:
                context['error_message'] = f"Error loading items: {e}"
                context['inward_items'] = []
        else:
            context['error_message'] = "GIN ID is missing."
            context['inward_items'] = []
        
        return context

```

#### 4.4 Templates (`inventory_transactions/templates/inventory_transactions/goodsservicenote/`)

**Task:** Create templates for the main GSN creation page and the HTMX-loaded item table.

**Instructions:**
-   `create.html`: The main page that extends `core/base.html`. It will contain the main form fields and a container for the HTMX-loaded DataTables. It also includes an Alpine.js component to manage the selected items for submission and the modal for the date picker.
-   `_inward_items_table.html`: A partial template rendered via HTMX. It contains the DataTables structure and Alpine.js logic for individual item checkboxes and quantity inputs.
-   No `base.html` code is included.

**`inventory_transactions/templates/inventory_transactions/goodsservicenote/create.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Goods Service Note (GSN) - New</h2>

    {% comment %} GSN Header Details {% endcomment %}
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white font-bold py-2 px-4 rounded-t-lg mb-4 shadow-md">
        &nbsp;Goods Service Note [GSN] - New
    </div>
    
    <div class="bg-white p-6 rounded-b-lg shadow-md mb-8">
        <table class="w-full text-sm text-gray-700 mb-4">
            <tbody>
                <tr class="border-b border-gray-200">
                    <td class="py-2 pr-4 font-semibold text-gray-600 w-1/4">GIN No:</td>
                    <td class="py-2 pl-4 text-gray-800 font-bold">{{ gin_no|default:"N/A" }}</td>
                </tr>
                <tr class="border-b border-gray-200">
                    <td class="py-2 pr-4 font-semibold text-gray-600">Challan No:</td>
                    <td class="py-2 pl-4 text-gray-800 font-bold">{{ challan_no|default:"N/A" }}</td>
                </tr>
                <tr class="border-b border-gray-200">
                    <td class="py-2 pr-4 font-semibold text-gray-600">Date:</td>
                    <td class="py-2 pl-4 text-gray-800 font-bold">{{ challan_date|default:"N/A" }}</td>
                </tr>
                <tr>
                    <td class="py-2 pr-4 font-semibold text-gray-600">Supplier:</td>
                    <td class="py-2 pl-4 text-gray-800 font-bold">{{ supplier_name|default:"N/A" }}</td>
                </tr>
            </tbody>
        </table>

        {% comment %} Main Form for Tax Invoice and Date {% endcomment %}
        <form id="gsnForm" hx-post="{% url 'goodsservicenote_create' %}?gin_id={{ gin_id }}&gin_no={{ gin_no }}&sup_id={{ supplier_id }}" 
              hx-swap="outerHTML" hx-target="#gsnForm" hx-indicator="#loadingIndicator"
              hx-on::after-request="if(event.detail.successful) {
                this.reset(); 
                document.getElementById('items_json').value = '[]';
                htmx.trigger('#inwardItemsTableContainer', 'refreshItems');
                Alpine.$data(document.getElementById('gsnApp')).resetSelectedItems();
              } else { 
                // Handle form validation errors, messages are already displayed by Django messages
              }"
              x-data="gsnApp()" x-init="init()">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label for="{{ form.TaxInvoiceNo.id_for_label }}" class="block text-sm font-medium text-gray-700">Tax Invoice No.:</label>
                    {{ form.TaxInvoiceNo }}
                    {% if form.TaxInvoiceNo.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.TaxInvoiceNo.errors }}</p>
                    {% endif %}
                </div>
                <div x-data="{ open: false }">
                    <label for="{{ form.tax_invoice_date_str.id_for_label }}" class="block text-sm font-medium text-gray-700">Date:</label>
                    <div class="relative">
                        {{ form.tax_invoice_date_str }}
                        {% if form.tax_invoice_date_str.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.tax_invoice_date_str.errors }}</p>
                        {% endif %}
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer" @click="open = true">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                        </div>
                    </div>
                    {% comment %} Simple date picker simulation using flatpickr (add CDN in base.html) {% endcomment %}
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            flatpickr(document.getElementById('{{ form.tax_invoice_date_str.id_for_label }}'), {
                                dateFormat: "d-m-Y",
                                defaultDate: new Date(),
                                allowInput: false, // Prevents manual input, forces selection
                                onClose: function(selectedDates, dateStr, instance) {
                                    // Ensure Alpine.js model is updated if using x-model on this field
                                    // No need if Alpine directly binds to the form field.
                                }
                            });
                        });
                    </script>
                </div>
            </div>

            {% comment %} Hidden input to store selected items data from Alpine.js {% endcomment %}
            <input type="hidden" name="items_json" id="items_json" :value="JSON.stringify(selectedItems)">

            {% comment %} Inward Items DataTables Container {% endcomment %}
            <div class="overflow-x-auto relative shadow-md sm:rounded-lg mb-6">
                <div id="inwardItemsTableContainer" 
                     hx-trigger="load, refreshItems from:body" 
                     hx-get="{% url 'inward_items_table_partial' %}?gin_id={{ gin_id }}" 
                     hx-swap="innerHTML"
                     class="min-h-[375px] max-h-[375px] overflow-auto">
                    <!-- Loading indicator for HTMX -->
                    <div class="flex justify-center items-center h-full">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                        <p class="ml-3 text-lg text-gray-600">Loading items...</p>
                    </div>
                </div>
            </div>

            {% comment %} Form Actions {% endcomment %}
            <div class="flex justify-center space-x-4 mt-6">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded shadow-lg transition duration-300 ease-in-out"
                        hx-confirm="Are you sure you want to add this Goods Service Note?"
                        hx-indicator="#loadingIndicator">
                    Add
                </button>
                <a href="{% url 'goodsservicenote_cancel' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded shadow-lg transition duration-300 ease-in-out">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <!-- Global Loading Indicator for HTMX submissions -->
    <div id="loadingIndicator" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 htmx-indicator" style="display: none;">
        <div class="animate-spin rounded-full h-20 w-20 border-t-4 border-b-4 border-blue-500"></div>
        <span class="ml-4 text-white text-xl">Processing...</span>
    </div>

</div>

{% endblock %}

{% block extra_js %}
<!-- Add flatpickr CDN to base.html if not already there -->
<!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"> -->
<!-- <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script> -->

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('gsnApp', () => ({
            selectedItems: [], // Stores { po_detail_id, item_id, received_qty, remaining_qty_for_gsn, wo_no }
            
            init() {
                // Initialize selectedItems from hidden input if form is re-rendered with errors
                const initialItemsJson = document.getElementById('items_json').value;
                if (initialItemsJson) {
                    try {
                        this.selectedItems = JSON.parse(initialItemsJson);
                    } catch (e) {
                        console.error("Error parsing initial items_json:", e);
                        this.selectedItems = [];
                    }
                }
            },

            // Called by child component when checkbox changes or qty changes
            updateSelectedItem(item, isChecked, receivedQty, remainingQty, woNo) {
                const existingIndex = this.selectedItems.findIndex(i => i.po_detail_id === item.po_detail_id);
                
                if (isChecked) {
                    if (existingIndex === -1) {
                        this.selectedItems.push({
                            po_detail_id: item.po_detail_id,
                            item_id: item.item_id,
                            received_qty: receivedQty,
                            remaining_qty_for_gsn: remainingQty,
                            wo_no: woNo
                        });
                    } else {
                        // Update existing item's quantity if it was already selected
                        this.selectedItems[existingIndex].received_qty = receivedQty;
                    }
                } else {
                    if (existingIndex !== -1) {
                        this.selectedItems.splice(existingIndex, 1);
                    }
                }
                console.log("Selected Items:", this.selectedItems);
            },

            // Called by the main form after successful submission
            resetSelectedItems() {
                this.selectedItems = [];
                // Additionally, reset the checkboxes and textboxes in the table via HTMX reload
                // htmx.trigger('#inwardItemsTableContainer', 'refreshItems'); // Already handled by hx-on::after-request
            }
        }));
    });

    // Helper for DataTables initialization after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'inwardItemsTableContainer') {
            const table = $('#inwardItemsTable');
            if (table.length && !$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Allows re-initialization
                    "order": [], // Disable initial sorting
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 1] } // SN and Checkbox columns not orderable
                    ]
                });
            }
        }
    });

    // Handle form submission completion for messages
    document.body.addEventListener('gsnCreated', function(event) {
        // This event is triggered from hx-trigger on successful GSN creation
        // You might want to display a success message via Alpine.js or Django's built-in messages
        // (Django messages are already handled by _messages.html partial if included in base.html)
        // Optionally, redirect if not using HTMX fully for next step, or just clear the form.
    });
</script>
{% endblock %}
```

**`inventory_transactions/templates/inventory_transactions/goodsservicenote/_inward_items_table.html`**
```html
{% comment %}
This partial template is loaded via HTMX into #inwardItemsTableContainer
It includes Alpine.js for local state management (checkboxes, qty input)
and initializes DataTables after load.
{% endcomment %}

{% if error_message %}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline">{{ error_message }}</span>
    </div>
{% elif not inward_items %}
    <table class="w-full text-sm text-gray-700">
        <tr>
            <td colspan="11" class="py-4 text-center font-bold text-red-700 text-lg">No data to display !</td>
        </tr>
    </table>
{% else %}
    <table id="inwardItemsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th> {# Checkbox column #}
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Inward Qty</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot Rec'd GSN Qty</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rem. Qty for GSN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rec'd Qty (Current GSN)</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in inward_items %}
            <tr x-data="{ 
                isChecked: false, 
                currentReceivedQty: '{{ item.remaining_qty_for_gsn|floatformat:"3" }}', 
                initialRemainingQty: '{{ item.remaining_qty_for_gsn|floatformat:"3" }}', 
                validationError: '',
                item: {{ item|json_script:"item_data"|safe }} 
            }" 
            x-init="
                {% if not item.can_receive %} isChecked = false; {% endif %}
                // Initialize based on whether this item was previously selected
                const appData = Alpine.$data(document.getElementById('gsnApp'));
                const preSelected = appData.selectedItems.find(s => s.po_detail_id === item.po_detail_id);
                if (preSelected) {
                    isChecked = true;
                    currentReceivedQty = preSelected.received_qty;
                }
            ">
                <td class="py-2 px-4 whitespace-nowrap text-right text-gray-800">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">
                    <input type="checkbox" 
                           class="form-checkbox h-4 w-4 text-blue-600 rounded" 
                           x-model="isChecked" 
                           {% if not item.can_receive %} disabled {% endif %}
                           @change="
                                if(isChecked) { 
                                    currentReceivedQty = initialRemainingQty; 
                                } else { 
                                    currentReceivedQty = ''; 
                                    validationError = ''; 
                                }
                                $dispatch('update-selected-item', {
                                    item: JSON.parse($el.closest('tr').querySelector('[id^=\'item_data\']').textContent),
                                    isChecked: isChecked,
                                    receivedQty: currentReceivedQty,
                                    remainingQty: initialRemainingQty,
                                    woNo: item.wo_no
                                });
                           ">
                </td>
                <td class="py-2 px-4 whitespace-nowrap">{{ item.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ item.description }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ item.uom }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.po_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.inward_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.total_received_gsn_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.remaining_qty_for_gsn|floatformat:"3" }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <input type="text" 
                           class="box3 w-24 px-2 py-1 border border-gray-300 rounded-md text-right {% if validationError %}border-red-500{% endif %}" 
                           x-model="currentReceivedQty" 
                           x-show="isChecked" 
                           {% if not item.can_receive %} disabled {% endif %}
                           @keyup="
                               currentReceivedQty = currentReceivedQty.replace(/[^0-9.]/g, ''); // Numeric only
                               let val = parseFloat(currentReceivedQty);
                               let rem = parseFloat(initialRemainingQty);
                               if (isNaN(val) || val <= 0) {
                                   validationError = 'Required and > 0';
                               } else if (val > rem) {
                                   validationError = 'Cannot exceed remaining qty';
                               } else {
                                   validationError = '';
                               }
                               $dispatch('update-selected-item', {
                                    item: JSON.parse($el.closest('tr').querySelector('[id^=\'item_data\']').textContent),
                                    isChecked: isChecked,
                                    receivedQty: currentReceivedQty,
                                    remainingQty: initialRemainingQty,
                                    woNo: item.wo_no
                               });
                           "
                           @blur="
                                // Format to 3 decimal places on blur if valid
                                if (!validationError && currentReceivedQty) {
                                    currentReceivedQty = parseFloat(currentReceivedQty).toFixed(3);
                                } else if (isNaN(parseFloat(currentReceivedQty)) || parseFloat(currentReceivedQty) <= 0) {
                                     currentReceivedQty = ''; // Clear if invalid or zero
                                }
                                $dispatch('update-selected-item', {
                                    item: JSON.parse($el.closest('tr').querySelector('[id^=\'item_data\']').textContent),
                                    isChecked: isChecked,
                                    receivedQty: currentReceivedQty,
                                    remainingQty: initialRemainingQty,
                                    woNo: item.wo_no
                                });
                           ">
                    <p x-show="validationError" class="text-red-500 text-xs mt-1">{{ item.can_receive|yesno:'*,Qty Cannot be received anymore.' }}</p>
                    <p x-show="validationError" class="text-red-500 text-xs mt-1" x-text="validationError"></p>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
{% endif %}

<script>
    // DataTable re-initialization is handled by the parent template's htmx:afterSwap listener.
    // However, if this partial needs its own isolated JS logic that runs ONLY when it's swapped in,
    // you would put it here.
    document.addEventListener('DOMContentLoaded', () => {
        // Listen for item updates from child rows and propagate to parent Alpine app
        document.getElementById('inwardItemsTableContainer').querySelectorAll('tr[x-data]').forEach(row => {
            row.addEventListener('update-selected-item', (event) => {
                const appData = Alpine.$data(document.getElementById('gsnApp'));
                if (appData) {
                    appData.updateSelectedItem(
                        event.detail.item,
                        event.detail.isChecked,
                        event.detail.receivedQty,
                        event.detail.remainingQty,
                        event.detail.woNo
                    );
                }
            });
        });
    });
</script>
```

#### 4.5 URLs (`inventory_transactions/urls.py`)

**Task:** Define URL patterns for the Goods Service Note functionality.

**Instructions:**
-   Create a main URL for the GSN creation page.
-   Create a separate URL for the HTMX-loaded DataTables partial.

```python
from django.urls import path
from .views import GoodsServiceNoteCreateView, InwardItemsTablePartialView
from django.views.generic import RedirectView

urlpatterns = [
    # Main GSN creation page
    path('goodsservicenote/new_details/', GoodsServiceNoteCreateView.as_view(), name='goodsservicenote_create'),
    
    # HTMX endpoint for the inward items table partial
    path('goodsservicenote/new_details/items_table/', InwardItemsTablePartialView.as_view(), name='inward_items_table_partial'),

    # URL for cancel button (mimics ASP.NET redirect)
    # Redirect to a placeholder for GoodsServiceNote_SN_New.aspx, perhaps a list view
    path('goodsservicenote/cancel/', RedirectView.as_view(url=reverse_lazy('goodsservicenote_list_placeholder'), permanent=False), name='goodsservicenote_cancel'),
    # Define a placeholder for the list view if it doesn't exist yet
    path('goodsservicenote/list/', RedirectView.as_view(url='/', permanent=False), name='goodsservicenote_list_placeholder'),
]
```

#### 4.6 Tests (`inventory_transactions/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
-   `setUpTestData`: Create necessary mock data (suppliers, items, inward masters/details) to ensure tests are self-contained and repeatable.
-   Model tests: Verify model field attributes, string representations, and especially the complex `create_gsn_with_details` and `get_items_for_gsn_creation` methods, including stock updates and auto-MRS/MIN logic.
-   View tests: Test GET and POST requests, form validation, HTMX headers, and successful redirects/responses.
-   Edge cases and error handling (e.g., invalid quantities, insufficient stock).

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db import transaction
from django.contrib.auth import get_user_model
import datetime
import decimal

from .models import (
    Supplier, Unit, Item, PurchaseOrderDetail, InwardMaster, InwardDetail,
    GoodsServiceNoteMaster, GoodsServiceNoteDetail, WorkOrderMaster,
    MaterialRequisitionMaster, MaterialRequisitionDetail,
    MaterialIssueMaster, MaterialIssueDetail,
    generate_next_sequence_number
)

# Mock user for testing authenticated actions
User = get_user_model()

class GoodsServiceNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = 1
        cls.fin_year_id = 1
        cls.user = User.objects.create_user(username='testuser', password='password')

        cls.unit = Unit.objects.create(Symbol='KGS')
        cls.item1 = Item.objects.create(Id=101, ItemCode='ITEM001', ManfDesc='Test Item 1', UOMBasic=cls.unit, StockQty=decimal.Decimal('100.000'), CompId=cls.company_id)
        cls.item2 = Item.objects.create(Id=102, ItemCode='ITEM002', ManfDesc='Test Item 2', UOMBasic=cls.unit, StockQty=decimal.Decimal('50.000'), CompId=cls.company_id)

        cls.supplier = Supplier.objects.create(SupplierId=1, SupplierName='Test Supplier', CompId=cls.company_id)

        cls.po_detail1 = PurchaseOrderDetail.objects.create(Id=1, ItemId=cls.item1, Qty=decimal.Decimal('100.000'))
        cls.po_detail2 = PurchaseOrderDetail.objects.create(Id=2, ItemId=cls.item2, Qty=decimal.Decimal('50.000'))

        cls.inward_master = InwardMaster.objects.create(
            Id=1, GINNo='GIN001', ChallanNo='CH001', ChallanDate=datetime.date(2023, 1, 1), 
            PONo='PO001', POMId=1, CompId=cls.company_id, FinYearId=cls.fin_year_id
        )
        cls.inward_detail1 = InwardDetail.objects.create(
            Id=1, GINId=cls.inward_master, POId=cls.po_detail1, ReceivedQty=decimal.Decimal('75.000'), PRSPRFlag=False
        )
        cls.inward_detail2 = InwardDetail.objects.create(
            Id=2, GINId=cls.inward_master, POId=cls.po_detail2, ReceivedQty=decimal.Decimal('40.000'), PRSPRFlag=True
        )

        cls.work_order_master_enabled = WorkOrderMaster.objects.create(Id=1, WONo='WO001', ReleaseWIS=True, CompId=cls.company_id)
        cls.work_order_master_disabled = WorkOrderMaster.objects.create(Id=2, WONo='WO002', ReleaseWIS=False, CompId=cls.company_id)

    def test_model_creation(self):
        gsn_master = GoodsServiceNoteMaster.objects.create(
            SysDate=datetime.date.today(),
            SysTime=datetime.time(10, 0, 0),
            CompId=self.company_id,
            SessionId=self.user.username,
            FinYearId=self.fin_year_id,
            GSNNo='GSN001',
            GINNo='GIN001',
            GINId=self.inward_master,
            TaxInvoiceNo='TINV001',
            TaxInvoiceDate=datetime.date(2023, 1, 15)
        )
        self.assertEqual(gsn_master.GSNNo, 'GSN001')
        self.assertEqual(gsn_master.GINId, self.inward_master)
        
        detail = GoodsServiceNoteDetail.objects.create(
            MId=gsn_master,
            GSNNo='GSN001',
            POId=self.po_detail1,
            ReceivedQty=decimal.Decimal('10.000')
        )
        self.assertEqual(detail.POId, self.po_detail1)
        self.assertEqual(detail.ReceivedQty, decimal.Decimal('10.000'))

    def test_generate_next_sequence_number(self):
        # Clear existing GSNs to test sequence from 0001
        GoodsServiceNoteMaster.objects.all().delete()
        self.assertEqual(generate_next_sequence_number('GSNNo', GoodsServiceNoteMaster, self.company_id, self.fin_year_id), '0001')
        
        GoodsServiceNoteMaster.objects.create(
            SysDate=datetime.date.today(), SysTime=datetime.time.now(),
            CompId=self.company_id, SessionId='user', FinYearId=self.fin_year_id,
            GSNNo='0001', GINNo='GIN001', GINId=self.inward_master, TaxInvoiceNo='T1', TaxInvoiceDate=datetime.date.today()
        )
        self.assertEqual(generate_next_sequence_number('GSNNo', GoodsServiceNoteMaster, self.company_id, self.fin_year_id), '0002')

    def test_item_update_stock(self):
        initial_stock = self.item1.StockQty
        self.item1.update_stock(decimal.Decimal('5.000'), is_increase=True)
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.StockQty, initial_stock + decimal.Decimal('5.000'))

        self.item1.update_stock(decimal.Decimal('2.000'), is_increase=False)
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.StockQty, initial_stock + decimal.Decimal('3.000'))

        with self.assertRaises(ValueError):
            self.item1.update_stock(self.item1.StockQty + decimal.Decimal('1.000'), is_increase=False)

    def test_get_items_for_gsn_creation(self):
        items_data = InwardDetail.get_items_for_gsn_creation(self.inward_master.Id, self.company_id)
        self.assertEqual(len(items_data), 2)

        item1_data = next(item for item in items_data if item['item_id'] == self.item1.Id)
        self.assertEqual(item1_data['item_code'], self.item1.ItemCode)
        self.assertEqual(item1_data['inward_qty'], self.inward_detail1.ReceivedQty)
        self.assertEqual(item1_data['total_received_gsn_qty'], decimal.Decimal('0.000')) # No GSNs created yet
        self.assertEqual(item1_data['remaining_qty_for_gsn'], self.inward_detail1.ReceivedQty)
        self.assertTrue(item1_data['can_receive'])

        # Create a partial GSN for item1 and re-test
        gsn_master_partial = GoodsServiceNoteMaster.objects.create(
            SysDate=datetime.date.today(), SysTime=datetime.time.now(), CompId=self.company_id,
            SessionId=self.user.username, FinYearId=self.fin_year_id, GSNNo='TEMP001',
            GINNo='GIN001', GINId=self.inward_master, TaxInvoiceNo='TINVTEMP', TaxInvoiceDate=datetime.date.today()
        )
        GoodsServiceNoteDetail.objects.create(
            MId=gsn_master_partial, GSNNo='TEMP001', POId=self.po_detail1, ReceivedQty=decimal.Decimal('25.000')
        )
        items_data_after_gsn = InwardDetail.get_items_for_gsn_creation(self.inward_master.Id, self.company_id)
        item1_data_updated = next(item for item in items_data_after_gsn if item['item_id'] == self.item1.Id)
        self.assertEqual(item1_data_updated['total_received_gsn_qty'], decimal.Decimal('25.000'))
        self.assertEqual(item1_data_updated['remaining_qty_for_gsn'], self.inward_detail1.ReceivedQty - decimal.Decimal('25.000'))
        self.assertTrue(item1_data_updated['can_receive'])

        # Simulate full GSN for item2
        GoodsServiceNoteDetail.objects.create(
            MId=gsn_master_partial, GSNNo='TEMP001', POId=self.po_detail2, ReceivedQty=self.inward_detail2.ReceivedQty
        )
        items_data_full_gsn = InwardDetail.get_items_for_gsn_creation(self.inward_master.Id, self.company_id)
        item2_data_full_gsn = next(item for item in items_data_full_gsn if item['item_id'] == self.item2.Id)
        self.assertEqual(item2_data_full_gsn['total_received_gsn_qty'], self.inward_detail2.ReceivedQty)
        self.assertEqual(item2_data_full_gsn['remaining_qty_for_gsn'], decimal.Decimal('0.000'))
        self.assertFalse(item2_data_full_gsn['can_receive'])


    def test_create_gsn_with_details_success(self):
        initial_item1_stock = self.item1.StockQty
        initial_item2_stock = self.item2.StockQty

        items_to_receive = [
            {
                'po_detail_id': self.po_detail1.Id,
                'item_id': self.item1.Id,
                'received_qty': '10.000', # String to mimic form input
                'remaining_qty_for_gsn': self.inward_detail1.ReceivedQty, # Required for validation
                'wo_no': 'WO001' # Item 1 with auto WIS enabled WO
            },
            {
                'po_detail_id': self.po_detail2.Id,
                'item_id': self.item2.Id,
                'received_qty': '5.000',
                'remaining_qty_for_gsn': self.inward_detail2.ReceivedQty,
                'wo_no': 'WO002' # Item 2 with auto WIS disabled WO
            }
        ]

        gsn_master = GoodsServiceNoteMaster.create_gsn_with_details(
            user_id=self.user.username,
            company_id=self.company_id,
            fin_year_id=self.fin_year_id,
            gin_id=self.inward_master.Id,
            tax_invoice_no='TINV002',
            tax_invoice_date=datetime.date(2023, 2, 1),
            items_data=items_to_receive
        )

        self.assertIsInstance(gsn_master, GoodsServiceNoteMaster)
        self.assertEqual(gsn_master.details.count(), 2)
        
        # Verify GSN details
        detail1 = GoodsServiceNoteDetail.objects.get(MId=gsn_master, POId=self.po_detail1)
        self.assertEqual(detail1.ReceivedQty, decimal.Decimal('10.000'))

        detail2 = GoodsServiceNoteDetail.objects.get(MId=gsn_master, POId=self.po_detail2)
        self.assertEqual(detail2.ReceivedQty, decimal.Decimal('5.000'))

        # Verify Item Stock Update (GSN Receipt)
        self.item1.refresh_from_db()
        self.item2.refresh_from_db()
        self.assertEqual(self.item1.StockQty, initial_item1_stock + decimal.Decimal('10.000')) # Item 1 stock increased
        self.assertEqual(self.item2.StockQty, initial_item2_stock + decimal.Decimal('5.000')) # Item 2 stock increased

        # Verify Auto MRS/MIN for Item 1 (WO001 - ReleaseWIS=True)
        mrs_master = MaterialRequisitionMaster.objects.get(MRSNo=gsn_master.GSNNo.replace('GSN', 'MRS')) # GSNNo is base for MRS/MIN
        self.assertIsNotNone(mrs_master)
        self.assertEqual(MaterialRequisitionDetail.objects.filter(MId=mrs_master, ItemId=self.item1).count(), 1)
        mrs_detail_item1 = MaterialRequisitionDetail.objects.get(MId=mrs_master, ItemId=self.item1)
        self.assertEqual(mrs_detail_item1.ReqQty, self.item1.StockQty) # ReqQty is current stock after GSN inward

        min_master = MaterialIssueMaster.objects.get(MINNo=gsn_master.GSNNo.replace('GSN', 'MIN')) # GSNNo is base for MRS/MIN
        self.assertIsNotNone(min_master)
        self.assertEqual(MaterialIssueDetail.objects.filter(MId=min_master, MRSId=mrs_detail_item1).count(), 1)
        min_detail_item1 = MaterialIssueDetail.objects.get(MId=min_master, MRSId=mrs_detail_item1)
        self.assertEqual(min_detail_item1.IssueQty, mrs_detail_item1.ReqQty) # IssueQty is what was requested (current stock)

        self.item1.refresh_from_db()
        self.assertEqual(self.item1.StockQty, initial_item1_stock + decimal.Decimal('10.000') - decimal.Decimal('10.000')) # Stock after GSN - Issue
        self.assertEqual(self.item1.StockQty, initial_item1_stock) # Should be back to initial stock after auto issue

        # Verify NO Auto MRS/MIN for Item 2 (WO002 - ReleaseWIS=False)
        self.assertEqual(MaterialRequisitionDetail.objects.filter(ItemId=self.item2).count(), 0)
        self.assertEqual(MaterialIssueDetail.objects.filter(MRSId__ItemId=self.item2).count(), 0)


    def test_create_gsn_with_details_validation_failure(self):
        # Test case: received_qty > remaining_qty_for_gsn
        items_with_invalid_qty = [
            {
                'po_detail_id': self.po_detail1.Id,
                'item_id': self.item1.Id,
                'received_qty': '100.000', # More than available remaining_qty (75.000)
                'remaining_qty_for_gsn': self.inward_detail1.ReceivedQty,
                'wo_no': 'WO001'
            }
        ]

        with self.assertRaisesMessage(ValueError, "Received quantity for item ITEM001 must be greater than 0 and not exceed remaining quantity."):
            GoodsServiceNoteMaster.create_gsn_with_details(
                user_id=self.user.username,
                company_id=self.company_id,
                fin_year_id=self.fin_year_id,
                gin_id=self.inward_master.Id,
                tax_invoice_no='TINV003',
                tax_invoice_date=datetime.date(2023, 3, 1),
                items_data=items_with_invalid_qty
            )
        
        # Verify no GSN, MRS, MIN records were created due to atomic transaction rollback
        self.assertEqual(GoodsServiceNoteMaster.objects.filter(TaxInvoiceNo='TINV003').count(), 0)
        self.assertEqual(MaterialRequisitionMaster.objects.filter(MRSNo__startswith='00').count(), 0) # Only test GSN-generated ones
        self.assertEqual(MaterialIssueMaster.objects.filter(MINNo__startswith='00').count(), 0)


class GoodsServiceNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data (same as model tests for consistency)
        cls.company_id = 1
        cls.fin_year_id = 1
        cls.user = User.objects.create_user(username='testuser', password='password')

        cls.unit = Unit.objects.create(Symbol='KGS')
        cls.item1 = Item.objects.create(Id=101, ItemCode='ITEM001', ManfDesc='Test Item 1', UOMBasic=cls.unit, StockQty=decimal.Decimal('100.000'), CompId=cls.company_id)
        cls.item2 = Item.objects.create(Id=102, ItemCode='ITEM002', ManfDesc='Test Item 2', UOMBasic=cls.unit, StockQty=decimal.Decimal('50.000'), CompId=cls.company_id)

        cls.supplier = Supplier.objects.create(SupplierId=1, SupplierName='Test Supplier', CompId=cls.company_id)

        cls.po_detail1 = PurchaseOrderDetail.objects.create(Id=1, ItemId=cls.item1, Qty=decimal.Decimal('100.000'))
        cls.po_detail2 = PurchaseOrderDetail.objects.create(Id=2, ItemId=cls.item2, Qty=decimal.Decimal('50.000'))

        cls.inward_master = InwardMaster.objects.create(
            Id=1, GINNo='GIN001', ChallanNo='CH001', ChallanDate=datetime.date(2023, 1, 1), 
            PONo='PO001', POMId=1, CompId=cls.company_id, FinYearId=cls.fin_year_id
        )
        cls.inward_detail1 = InwardDetail.objects.create(
            Id=1, GINId=cls.inward_master, POId=cls.po_detail1, ReceivedQty=decimal.Decimal('75.000'), PRSPRFlag=False
        )
        cls.inward_detail2 = InwardDetail.objects.create(
            Id=2, GINId=cls.inward_master, POId=cls.po_detail2, ReceivedQty=decimal.Decimal('40.000'), PRSPRFlag=True
        )

        cls.work_order_master_enabled = WorkOrderMaster.objects.create(Id=1, WONo='WO001', ReleaseWIS=True, CompId=cls.company_id)
        cls.work_order_master_disabled = WorkOrderMaster.objects.create(Id=2, WONo='WO002', ReleaseWIS=False, CompId=cls.company_id)

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password') # Log in the test user
        self.base_url_params = f'gin_id={self.inward_master.Id}&gin_no={self.inward_master.GINNo}&sup_id={self.supplier.SupplierId}'
        self.create_url = reverse('goodsservicenote_create') + f'?{self.base_url_params}'
        self.table_url = reverse('inward_items_table_partial') + f'?gin_id={self.inward_master.Id}'

    def test_create_view_get(self):
        response = self.client.get(self.create_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsservicenote/create.html')
        self.assertContains(response, 'GIN No: GIN001')
        self.assertContains(response, 'Supplier: Test Supplier')
        self.assertContains(response, 'Tax Invoice No.')
        self.assertContains(response, 'Date')

    def test_inward_items_table_partial_view_get(self):
        response = self.client.get(self.table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsservicenote/_inward_items_table.html')
        self.assertContains(response, 'id="inwardItemsTable"')
        self.assertContains(response, 'Item Code')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'ITEM002')

    def test_inward_items_table_partial_view_get_no_gin_id(self):
        response = self.client.get(reverse('inward_items_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GIN ID is missing.')

    def test_create_view_post_success(self):
        items_data = [
            {
                'po_detail_id': self.po_detail1.Id,
                'item_id': self.item1.Id,
                'received_qty': '10.000',
                'remaining_qty_for_gsn': str(self.inward_detail1.ReceivedQty),
                'wo_no': 'WO001'
            },
            {
                'po_detail_id': self.po_detail2.Id,
                'item_id': self.item2.Id,
                'received_qty': '5.000',
                'remaining_qty_for_gsn': str(self.inward_detail2.ReceivedQty),
                'wo_no': 'WO002'
            }
        ]
        form_data = {
            'TaxInvoiceNo': 'TINV_HTMX_001',
            'tax_invoice_date_str': '01-03-2023',
            'items_json': json.dumps(items_data) # Send selected items as JSON string
        }

        response = self.client.post(self.create_url, form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response for hx-swap="none"
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'gsnCreated')
        
        # Verify GSN was created
        gsn_master = GoodsServiceNoteMaster.objects.get(TaxInvoiceNo='TINV_HTMX_001')
        self.assertIsNotNone(gsn_master)
        self.assertEqual(gsn_master.details.count(), 2)

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), f"Goods Service Note {gsn_master.GSNNo} created successfully!")

    def test_create_view_post_validation_error(self):
        # Invalid date format
        form_data = {
            'TaxInvoiceNo': 'TINV_ERROR',
            'tax_invoice_date_str': '2023/03/01', # Incorrect format
            'items_json': '[]'
        }
        response = self.client.post(self.create_url, form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX re-renders form with errors
        self.assertTemplateUsed(response, 'inventory_transactions/goodsservicenote/create.html')
        self.assertContains(response, 'Invalid date format. Please use DD-MM-YYYY.')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Please correct the errors below.")

    def test_create_view_post_business_logic_error(self):
        # Quantity exceeds remaining
        items_data = [
            {
                'po_detail_id': self.po_detail1.Id,
                'item_id': self.item1.Id,
                'received_qty': '1000.000', # Too much
                'remaining_qty_for_gsn': str(self.inward_detail1.ReceivedQty),
                'wo_no': 'WO001'
            }
        ]
        form_data = {
            'TaxInvoiceNo': 'TINV_BUSINESS_ERROR',
            'tax_invoice_date_str': '01-03-2023',
            'items_json': json.dumps(items_data)
        }

        response = self.client.post(self.create_url, form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX re-renders form with messages
        self.assertTemplateUsed(response, 'inventory_transactions/goodsservicenote/create.html')
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Validation error: Received quantity for item ITEM001 must be greater than 0 and not exceed remaining quantity.", str(messages[0]))
        
        # Ensure no GSN was created
        self.assertEqual(GoodsServiceNoteMaster.objects.filter(TaxInvoiceNo='TINV_BUSINESS_ERROR').count(), 0)

    def test_cancel_redirect(self):
        response = self.client.get(reverse('goodsservicenote_cancel'))
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('goodsservicenote_list_placeholder'))

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **Main Form Submission:** The `gsnForm` in `create.html` uses `hx-post` for submission. On success (`hx-on::after-request` with `event.detail.successful`), it triggers `gsnCreated` event and calls Alpine.js methods to `resetSelectedItems` and `htmx.trigger` to `refreshItems` in the table.
-   **Dynamic Table Loading:** The `inwardItemsTableContainer` in `create.html` uses `hx-get` to load `_inward_items_table.html` initially and on `refreshItems` event.
-   **Checkbox and Quantity Input:** In `_inward_items_table.html`, each table row uses `x-data` to manage `isChecked` and `currentReceivedQty`.
    -   `x-model="isChecked"` binds the checkbox state.
    -   `x-show="isChecked"` controls the visibility of the quantity `input`.
    -   `@keyup` and `@blur` events on the quantity `input` update `currentReceivedQty`, perform client-side numeric/range validation, and update `validationError`.
    -   `$dispatch('update-selected-item', ...)` is used to communicate item selection and quantity changes from the individual table rows (child Alpine components) up to the main `gsnApp` (parent Alpine component) which manages the `selectedItems` array.
-   **DataTables:** Initialized in the `htmx:afterSwap` listener in `create.html`'s `extra_js` block. This ensures DataTables is applied after the HTMX content is loaded into the DOM.
-   **Date Picker:** `flatpickr` is used as a lightweight date picker. Its integration ensures the `dd-MM-yyyy` format and read-only behavior consistent with ASP.NET's `CalendarExtender`.
-   **Error Handling:** Django's messages framework is used for server-side messages. HTMX `hx-swap="outerHTML"` for the main form ensures that form validation errors are re-rendered along with the form itself, preserving validation messages.

**Final Notes:**
-   This plan provides a comprehensive framework for migrating the Goods Service Note module.
-   The "Fat Model, Thin View" approach centralizes complex business logic, making it more testable and maintainable.
-   HTMX and Alpine.js significantly reduce the need for custom JavaScript, leading to a leaner, more performant frontend.
-   The detailed models, forms, views, templates, and tests ensure a robust and well-documented solution.
-   Placeholders like `CURRENT_COMPANY_ID` and `CURRENT_FIN_YEAR_ID` should be replaced with dynamic values from your Django authentication and multi-tenancy system (e.g., `self.request.user.company_id` if part of a custom user model or context processor).
-   The `fun.GetItemCode_PartNo` and `WONo` lookup logic in the ASP.NET `loadData` was a simplification. In a real-world scenario, more precise foreign key relationships or specific lookups would be implemented if the `WONo` does not directly come from `InwardMaster.PONo`.
-   The auto-generated numbering logic is simplified. For high-volume transactional systems, dedicated sequence generators or database sequences might be considered for robustness.