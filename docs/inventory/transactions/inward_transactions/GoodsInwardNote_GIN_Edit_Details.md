```markdown
## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- Search for database-related elements like SqlDataSource, connection strings, or SQL commands (e.g., SELECT, INSERT, UPDATE, DELETE).
- Extract the table name and assign it to [TABLE_NAME].
- Identify column names (e.g., [COLUMN1], [COLUMN2]) and, if available, their data types from SQL statements or UI bindings (e.g., GridView columns).
- If columns are not explicitly listed (e.g., SELECT *), infer them from UI controls or other data operations.

**Analysis:**

The ASP.NET code interacts with multiple tables to build the `Goods Inward Note [GIN] - Edit` interface. The primary tables for master and detail data are `tblInv_Inward_Master` and `tblInv_Inward_Details`. Many other tables are joined to fetch related information like item details, purchase order details, asset categories, and to determine editability.

**Primary Tables:**

*   `tblInv_Inward_Master` (Main GIN Header)
*   `tblInv_Inward_Details` (Individual GIN Items)

**Related Lookup/Transactional Tables (requiring `managed=False` models):**

*   `tblACC_Asset_Category`
*   `tblACC_Asset_SubCategory`
*   `tblDG_Item_Master`
*   `Unit_Master`
*   `tblMM_PO_Master`
*   `tblMM_PO_Details`
*   `tblMM_PR_Master`
*   `tblMM_PR_Details`
*   `tblMM_SPR_Master`
*   `tblMM_SPR_Details`
*   `tblFinancial_master`
*   `BusinessGroup`
*   `tblinv_MaterialReceived_Master`
*   `tblinv_MaterialReceived_Details`
*   `tblinv_MaterialServiceNote_Master`
*   `tblinv_MaterialServiceNote_Details`

**Inferred Columns and Data Types:**

*   **`tblInv_Inward_Master`**:
    *   `Id` (int, PK)
    *   `GINNo` (string)
    *   `ChallanNo` (string)
    *   `ChallanDate` (datetime)
    *   `GateEntryNo` (string)
    *   `GDate` (datetime)
    *   `GTime` (time)
    *   `ModeofTransport` (string)
    *   `VehicleNo` (string)
    *   `PONo` (string)
    *   `CompId` (int, FK)
    *   `FinYearId` (int, FK)
    *   `SysDate` (datetime)
    *   `SysTime` (time)
    *   `SessionId` (string)
*   **`tblInv_Inward_Details`**:
    *   `Id` (int, PK)
    *   `GINId` (int, FK to `tblInv_Inward_Master.Id`)
    *   `GINNo` (string, denormalized)
    *   `POId` (int, FK to `tblMM_PO_Details.Id`)
    *   `Qty` (decimal, ChallanQty)
    *   `ReceivedQty` (decimal)
    *   `ACategoyId` (int, FK to `tblACC_Asset_Category.Id`)
    *   `ASubCategoyId` (int, FK to `tblACC_Asset_SubCategory.Id`)

Other table columns are inferred from the complex `loadData` method's `fun.select` queries and `dr` (DataRow) assignments.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Create: Look for insert operations (e.g., button click events, GridView footer templates).
Read: Find select statements or data binding (e.g., GridView population).
Update: Identify update commands (e.g., GridView edit mode, form submissions).
Delete: Locate delete operations (e.g., button or link triggers).
Record any validation logic (e.g., required fields) for replication in Django.

**Analysis:**

This page is specifically for *editing* an existing Goods Inward Note.

*   **Read (R):**
    *   The `Page_Load` method reads the `GINId` from the query string and loads the main `tblInv_Inward_Master` details into header labels and textboxes.
    *   The `loadData()` method is central to reading and preparing the list of `tblInv_Inward_Details`. It performs complex joins across many tables (`tblMM_PO_Details`, `tblMM_PR_Details`, `tblDG_Item_Master`, `Unit_Master`, `tblACC_Asset_Category`, `tblACC_Asset_SubCategory`, etc.) to gather comprehensive information for each detail row (ItemCode, Description, UOM, PO Qty, Total Received Qty, Challan Qty, Received Qty, Category, Sub-Category, AHId).
    *   The `disableEdit()` function reads from `tblinv_MaterialReceived_Master` and `tblinv_MaterialServiceNote_Master` to determine if a detail item has related GRR/GSN entries, which impacts its editability.

*   **Update (U):**
    *   The `GridView1_RowUpdating` event handles the update of individual `tblInv_Inward_Details` rows (specifically `Qty`, `ReceivedQty`, `ACategoyId`, `ASubCategoyId`). It also updates the main `tblInv_Inward_Master` record (GateEntryNo, GDate, GTime, ModeofTransport, VehicleNo) *whenever any detail row is updated*.
    *   The `ddCategory_SelectedIndexChanged` event dynamically updates the `ddSubCategory` dropdown, which is part of the update process for detail rows.

*   **Create (C):** No explicit create functionality for new GINs or GIN details on this page.

*   **Delete (D):** No explicit delete functionality for GINs or GIN details on this page. (The `GridView1_RowDataBound` has `del.Attributes.Add("onclick", "return confirmationUpdate();");` but no delete link button is present in the ASPX for rows).

*   **Validation Logic:**
    *   `TxtGDate`: Required field, `dd-MM-yyyy` format via `RegularExpressionValidator`. `fun.DateValidation` is used in code-behind.
    *   `TxtModeoftransport`, `TxtVehicleNo`: Required fields.
    *   `TxtChallanQty`, `TxtRecedQty`: Required fields, numeric (`^\d{1,15}(\.\d{0,3})?$`) format. `fun.NumberValidationQty` is used.
    *   Quantity Check: `InvQty <= Challanqty` and `RecvQty <= recdqty` (these seem to be internal checks against original values of the current row, not PO quantity).
    *   `ddCategory`, `ddSubCategory`: Required when `AHId == 33`.

*   **Special Functionality:**
    *   File Downloads: `downloadImg` and `downloadSpec` commands in `GridView1_RowCommand` for `tblDG_Item_Master` attachments.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

- Identify controls like:
  - GridView: Displays data lists.
  - TextBox, DropDownList: Captures user input.
  - Button, LinkButton: Triggers actions.
- Note field mappings (e.g., which columns are bound to which controls).
- Check for JavaScript used in client-side interactions for potential Alpine.js conversion.

**Analysis:**

*   **Header Information (Read-only display):**
    *   `Lblgnno` (GIN No), `LblChallanDate` (Challan Date), `lblChallanNo` (Challan No) - Display `Request.QueryString` values.
    *   `LblWODept` (WONO/Business Group label), `LblWONo` (WONO/Business Group number) - Dynamically populated based on PR/SPR flag in `loadData()`.

*   **Header Input Fields (Editable):**
    *   `TxtGateentryNo` (Gate Entry No): `TextBox`.
    *   `TxtGDate` (Date): `TextBox` with `CalendarExtender`.
    *   `TimeSelector1` (Time): Custom `TimeSelector` control.
    *   `TxtModeoftransport` (Mode of Transport): `TextBox`.
    *   `TxtVehicleNo` (Vehicle No): `TextBox`.
    *   These form the `GoodsInwardNote` master update form.

*   **Detail Grid (`GridView1`):**
    *   Displays `GoodsInwardNoteDetail` items and related data in a tabular format.
    *   Supports pagination (`AllowPaging`).
    *   Supports in-line editing for specific columns (`TxtChallanQty`, `TxtRecedQty`, `ddCategory`, `ddSubCategory`).
    *   **Columns (mapped to DataTables):**
        *   `SN`: Row counter.
        *   `Edit/Update/Cancel`: Action buttons (HTMX for inline edit/update).
        *   `Image`, `Spec. Sheet`: Link buttons for file downloads (HTMX triggered download).
        *   `Item Code`, `Description`, `UOM`: Read-only text from `tblDG_Item_Master` and `Unit_Master`.
        *   `Category`, `Sub-Cate`: Display labels, become dropdowns (`ddCategory`, `ddSubCategory`) in edit mode. `ddCategory` populates `ddSubCategory` dynamically.
        *   `PO Qty`, `Tot Recd Qty`: Read-only quantities from PO and aggregated received quantities.
        *   `Challan Qty`: Display label, becomes `TxtChallanQty` in edit mode.
        *   `Recd Qty`: Display label, becomes `TxtRecedQty` in edit mode.
    *   **Hidden Fields (`lblId`, `lblItemId`, `lblPOId`, `lblCategoyId`, `lblSubCategoryId`, `lblAHId`, `lblChnQty`, `lblRecQty`):** Used to carry `Id`s and original values for validation/updates. These will be mapped to `data-*` attributes or hidden inputs in Django.

*   **Action Buttons:**
    *   `btnCancel`: Redirects to another page.

*   **Message Display:**
    *   `lblMessage`: Displays success/error messages.

*   **Client-side JS:**
    *   `loadingNotifier.js`, `PopUpMsg.js`: Generic JS.
    *   `confirmationUpdate()`: Simple confirmation dialog (e.g., for row updates, though no delete visible).

## Step 4: Generate Django Code

We will create a new Django app, e.g., `inventory`, for this module.

### 4.1 Models

Task: Create Django models based on the database schema.

**Instructions:**

- Name the models `GoodsInwardNote` (for `tblInv_Inward_Master`) and `GoodsInwardNoteDetail` (for `tblInv_Inward_Details`).
- Define fields with appropriate Django field types. Use `db_column` if field names differ.
- Set `managed = False` and `db_table` in `Meta` class for all existing tables.
- Implement model methods for business logic, especially for the complex `loadData` functionality. A custom manager for `GoodsInwardNoteDetail` will be crucial for aggregating data.

```python
# inventory/models.py

from django.db import models
from django.db.models import Sum, F, Case, When, Value, CharField, Q
from django.utils import timezone
from decimal import Decimal

# --- Helper Models (Managed=False for existing tables) ---
class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

class AssetCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    abbreviation = models.CharField(db_column='Abbrivation', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_Category'
        verbose_name = 'Asset Category'
        verbose_name_plural = 'Asset Categories'

    def __str__(self):
        return self.abbreviation

class AssetSubCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master_id = models.ForeignKey(AssetCategory, models.DO_NOTHING, db_column='MId') # MId is FK to AssetCategory.Id
    abbreviation = models.CharField(db_column='Abbrivation', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_SubCategory'
        verbose_name = 'Asset Sub Category'
        verbose_name_plural = 'Asset Sub Categories'

    def __str__(self):
        return self.abbreviation

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manufacturer_description = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic_id = models.IntegerField(db_column='UOMBasic') # FK to UnitMaster.Id
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=50, blank=True, null=True)
    attachment_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    attachment_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    attachment_content_type = models.CharField(db_column='AttContentType', max_length=50, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

    def get_item_code_part_no(self, company_id):
        # This function 'fun.GetItemCode_PartNo' was likely a custom lookup.
        # Assuming ItemCode in this model directly corresponds.
        return self.item_code

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1) # '0' for PR, '1' for SPR
    fin_year_id = models.IntegerField(db_column='FinYearId')
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(PurchaseOrderMaster, models.DO_NOTHING, db_column='MId')
    po_no = models.CharField(db_column='PONo', max_length=50) # Denormalized
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True) # Denormalized
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # FK to PurchaseRequisitionDetail.Id
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True) # Denormalized
    spr_id = models.IntegerField(db_column='SPRId', blank=True, null=True) # FK to StorePurchaseRequisitionDetail.Id
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    # ItemId is determined via PR/SPR details

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

class PurchaseRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'Purchase Requisition Master'
        verbose_name_plural = 'Purchase Requisition Masters'

class PurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(PurchaseRequisitionMaster, models.DO_NOTHING, db_column='MId')
    pr_no = models.CharField(db_column='PRNo', max_length=50) # Denormalized
    item_id = models.IntegerField(db_column='ItemId') # FK to ItemMaster.Id
    ah_id = models.IntegerField(db_column='AHId') # This AHId is used for category/subcategory logic

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'Purchase Requisition Detail'
        verbose_name_plural = 'Purchase Requisition Details'

class StorePurchaseRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'Store Purchase Requisition Master'
        verbose_name_plural = 'Store Purchase Requisition Masters'

class StorePurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(StorePurchaseRequisitionMaster, models.DO_NOTHING, db_column='MId')
    spr_no = models.CharField(db_column='SPRNo', max_length=50) # Denormalized
    item_id = models.IntegerField(db_column='ItemId') # FK to ItemMaster.Id
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    department_id = models.IntegerField(db_column='DeptId', blank=True, null=True) # FK to BusinessGroup.Id
    ah_id = models.IntegerField(db_column='AHId') # This AHId is used for category/subcategory logic

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'Store Purchase Requisition Detail'
        verbose_name_plural = 'Store Purchase Requisition Details'

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

class MaterialReceivedMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    gin = models.ForeignKey('GoodsInwardNote', models.DO_NOTHING, db_column='GINId')
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'

class MaterialReceivedDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialReceivedMaster, models.DO_NOTHING, db_column='MId')
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    po_id = models.IntegerField(db_column='POId') # FK to PurchaseOrderDetail.Id

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'

class MaterialServiceNoteMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gsn_no = models.CharField(db_column='GSNNo', max_length=50)
    gin = models.ForeignKey('GoodsInwardNote', models.DO_NOTHING, db_column='GINId')
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'

class MaterialServiceNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialServiceNoteMaster, models.DO_NOTHING, db_column='MId')
    gsn_no = models.CharField(db_column='GSNNo', max_length=50)
    po_id = models.IntegerField(db_column='POId') # FK to PurchaseOrderDetail.Id

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'


# --- Core Models ---
class GoodsInwardNote(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    challan_no = models.CharField(db_column='ChallanNo', max_length=50)
    challan_date = models.DateField(db_column='ChallanDate')
    gate_entry_no = models.CharField(db_column='GateEntryNo', max_length=50, blank=True, null=True)
    entry_date = models.DateField(db_column='GDate', blank=True, null=True)
    entry_time = models.TimeField(db_column='GTime', blank=True, null=True)
    mode_of_transport = models.CharField(db_column='ModeofTransport', max_length=100, blank=True, null=True)
    vehicle_no = models.CharField(db_column='VehicleNo', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    company_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    system_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    system_time = models.TimeField(db_column='SysTime', blank=True, null=True) # Redundant with SysDate if it's datetime
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Goods Inward Note'
        verbose_name_plural = 'Goods Inward Notes'

    def __str__(self):
        return self.gin_no

    def update_header_details(self, data, user_session_id):
        # This method encapsulates the update logic for tblInv_Inward_Master
        self.gate_entry_no = data.get('gate_entry_no')
        self.entry_date = data.get('entry_date')
        self.entry_time = data.get('entry_time')
        self.mode_of_transport = data.get('mode_of_transport')
        self.vehicle_no = data.get('vehicle_no')
        self.system_date = timezone.now()
        self.system_time = timezone.now().time()
        self.session_id = user_session_id
        self.save()

class GoodsInwardNoteDetailManager(models.Manager):
    def get_details_with_full_info(self, gin_id, company_id, session_fin_year_id, gin_no, current_po_no):
        """
        Replicates the complex data aggregation logic from the ASP.NET loadData method.
        This queries multiple related tables to get all display fields and flags.
        """
        # Step 1: Get base GIN details
        details = self.filter(
            gin_id=gin_id,
            gin_no=gin_no,
            gin_master__company_id=company_id,
            gin_master__fin_year_id__lte=session_fin_year_id
        ).select_related('gin_master', 'asset_category', 'asset_sub_category').order_by('-gin_master__id')

        # Annotate with joined data
        annotated_details = []
        for detail in details:
            # Replicate fun.select logic and nested loops
            po_detail = PurchaseOrderDetail.objects.filter(
                id=detail.po_id,
                master__po_no=current_po_no,
                master__fin_year_id__lte=detail.gin_master.fin_year_id,
                master__company_id=detail.gin_master.company_id
            ).select_related('master').first()

            item_id = None
            po_qty = Decimal(0)
            item_code = ''
            description = ''
            uom = ''
            file_name = ''
            attachment_name = ''
            wo_no = ''
            department_symbol = ''
            ah_id = 0

            if po_detail:
                po_qty = po_detail.quantity
                if po_detail.master.pr_spr_flag == '0' and po_detail.pr_id: # PR Flag
                    pr_detail = PurchaseRequisitionDetail.objects.filter(
                        id=po_detail.pr_id,
                        pr_no=po_detail.pr_no,
                        master__pr_no=po_detail.pr_no, # Redundant join condition
                        master__company_id=company_id
                    ).select_related('master').first()
                    if pr_detail:
                        item_id = pr_detail.item_id
                        ah_id = pr_detail.ah_id
                        wo_no = pr_detail.master.wo_no or ''
                elif po_detail.master.pr_spr_flag == '1' and po_detail.spr_id: # SPR Flag
                    spr_detail = StorePurchaseRequisitionDetail.objects.filter(
                        id=po_detail.spr_id,
                        spr_no=po_detail.spr_no,
                        master__spr_no=po_detail.spr_no, # Redundant join condition
                        master__company_id=company_id
                    ).select_related('master').first()
                    if spr_detail:
                        item_id = spr_detail.item_id
                        ah_id = spr_detail.ah_id
                        wo_no = spr_detail.wo_no or ''
                        if not wo_no and spr_detail.department_id:
                            dept = BusinessGroup.objects.filter(id=spr_detail.department_id).first()
                            department_symbol = dept.symbol if dept else ''

                if item_id:
                    item_master = ItemMaster.objects.filter(id=item_id, company_id=company_id).first()
                    if item_master:
                        item_code = item_master.item_code # get_item_code_part_no logic was here
                        description = item_master.manufacturer_description
                        unit = UnitMaster.objects.filter(id=item_master.uom_basic_id).first()
                        uom = unit.symbol if unit else ''
                        file_name = 'View' if item_master.file_name else ''
                        attachment_name = 'View' if item_master.attachment_name else ''

            # Calculate total received quantity for this PO item across all GINs
            total_recd_qty_qs = GoodsInwardNoteDetail.objects.filter(
                gin_master__po_no=current_po_no,
                po_id=detail.po_id,
                gin_master__company_id=company_id
            ).aggregate(sum_received_qty=Sum('received_qty'))
            total_recd_qty = total_recd_qty_qs['sum_received_qty'] or Decimal(0)

            # Check GRR/GSN status for editability
            has_grr = MaterialReceivedMaster.objects.filter(
                company_id=company_id,
                gin_id=gin_id,
                materialreceiveddetail__po_id=detail.po_id
            ).exists()
            has_gsn = MaterialServiceNoteMaster.objects.filter(
                company_id=company_id,
                gin_id=gin_id,
                materialservicenotedetail__po_id=detail.po_id
            ).exists()
            
            # Category & SubCategory display
            category_abbr = detail.asset_category.abbreviation if detail.asset_category else 'NA'
            subcategory_abbr = detail.asset_sub_category.abbreviation if detail.asset_sub_category else 'NA'

            annotated_details.append({
                'id': detail.id,
                'item_id': item_id,
                'item_code': item_code,
                'description': description,
                'uom': uom,
                'po_qty': po_qty,
                'received_qty': detail.received_qty, # This is the current GIN detail's received qty
                'challan_qty': detail.quantity,      # This is the current GIN detail's challan qty
                'total_received_qty': total_recd_qty, # Sum of all received qty for this PO across GINs
                'po_item_id': detail.po_id,
                'file_name': file_name,
                'attachment_name': attachment_name,
                'category_name': category_abbr,
                'subcategory_name': subcategory_abbr,
                'category_id': detail.asset_category_id or 0,
                'subcategory_id': detail.asset_sub_category_id or 0,
                'ah_id': ah_id,
                'can_edit': not (has_grr or has_gsn), # Disable edit if GRR/GSN exist
                'wo_dept_label': 'WONO' if wo_no else 'Bussiness Group',
                'wo_dept_value': wo_no if wo_no else department_symbol
            })

        return annotated_details

class GoodsInwardNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_master = models.ForeignKey(GoodsInwardNote, models.DO_NOTHING, db_column='GINId')
    gin_no = models.CharField(db_column='GINNo', max_length=50) # Denormalized
    po_id = models.IntegerField(db_column='POId') # PurchaseOrderDetail ID
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # Challan Quantity
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3)
    asset_category = models.ForeignKey(AssetCategory, models.DO_NOTHING, db_column='ACategoyId', blank=True, null=True)
    asset_sub_category = models.ForeignKey(AssetSubCategory, models.DO_NOTHING, db_column='ASubCategoyId', blank=True, null=True)

    objects = GoodsInwardNoteDetailManager()

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Goods Inward Note Detail'
        verbose_name_plural = 'Goods Inward Note Details'

    def __str__(self):
        return f"GIN {self.gin_no} - Item {self.po_id}"

    def update_detail_data(self, challan_qty, received_qty, category_id, subcategory_id):
        # This method encapsulates the update logic for tblInv_Inward_Details
        self.quantity = challan_qty
        self.received_qty = received_qty
        self.asset_category_id = category_id if category_id else None
        self.asset_sub_category_id = subcategory_id if subcategory_id else None
        self.save()

    def has_material_received_or_service_note(self, company_id):
        """
        Checks if GRR or GSN exist for this detail item's PO ID and GIN.
        Replicates the disableEdit logic.
        """
        has_grr = MaterialReceivedMaster.objects.filter(
            company_id=company_id,
            gin=self.gin_master,
            materialreceiveddetail__po_id=self.po_id
        ).exists()

        has_gsn = MaterialServiceNoteMaster.objects.filter(
            company_id=company_id,
            gin=self.gin_master,
            materialservicenotedetail__po_id=self.po_id
        ).exists()
        return has_grr or has_gsn
```

### 4.2 Forms

Task: Define Django forms for user input.

**Instructions:**

Create a `ModelForm` for `GoodsInwardNote` and another for `GoodsInwardNoteDetail`.
Include editable fields. Add widgets with Tailwind CSS classes for styling.
Implement form validation logic based on ASP.NET validation. Custom validation methods for `clean_field`.

```python
# inventory/forms.py

from django import forms
from .models import GoodsInwardNote, GoodsInwardNoteDetail, AssetCategory, AssetSubCategory
from django.core.exceptions import ValidationError
import re

class GoodsInwardNoteForm(forms.ModelForm):
    # Hidden fields that are passed via query string or session, not edited directly in this form
    # gin_no = forms.CharField(widget=forms.HiddenInput(), required=False)
    # challan_no = forms.CharField(widget=forms.HiddenInput(), required=False)
    # challan_date = forms.DateField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = GoodsInwardNote
        fields = [
            'gate_entry_no', 'entry_date', 'entry_time',
            'mode_of_transport', 'vehicle_no'
        ]
        widgets = {
            'gate_entry_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'entry_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'entry_time': forms.TimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'time'}),
            'mode_of_transport': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'vehicle_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'gate_entry_no': 'Gate Entry No',
            'entry_date': 'Date',
            'entry_time': 'Time',
            'mode_of_transport': 'Mode of Transport',
            'vehicle_no': 'Vehicle No',
        }

    def clean_entry_date(self):
        # Replicates RegularExpressionValidator and RequiredFieldValidator for date
        date_str = self.cleaned_data['entry_date']
        if not date_str:
            raise ValidationError("Date is required.")
        # Python's date validation handles format automatically with type='date'
        return date_str

    def clean(self):
        cleaned_data = super().clean()
        # Replicate ASP.NET's `TxtGateentryNo.Text != "" && TxtModeoftransport.Text != "" && TxtVehicleNo.Text != ""`
        if not cleaned_data.get('gate_entry_no'):
            self.add_error('gate_entry_no', "Gate Entry No. is required.")
        if not cleaned_data.get('mode_of_transport'):
            self.add_error('mode_of_transport', "Mode of Transport is required.")
        if not cleaned_data.get('vehicle_no'):
            self.add_error('vehicle_no', "Vehicle No. is required.")
        return cleaned_data

class GoodsInwardNoteDetailForm(forms.ModelForm):
    # These fields are the editable ones in the GridView
    quantity = forms.DecimalField(
        label="Challan Qty",
        max_digits=18,
        decimal_places=3,
        widget=forms.NumberInput(attrs={'class': 'box3 w-24 text-right'})
    )
    received_qty = forms.DecimalField(
        label="Recd Qty",
        max_digits=18,
        decimal_places=3,
        widget=forms.NumberInput(attrs={'class': 'box3 w-24 text-right'})
    )
    asset_category = forms.ModelChoiceField(
        queryset=AssetCategory.objects.all(),
        label="Category",
        required=False, # Required only if AHId is 33, handled in clean method
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    asset_sub_category = forms.ModelChoiceField(
        queryset=AssetSubCategory.objects.none(), # Will be populated dynamically via HTMX
        label="Sub-Category",
        required=False, # Required only if AHId is 33, handled in clean method
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # Store original values from instance for validation
    original_challan_qty = forms.DecimalField(widget=forms.HiddenInput(), required=False)
    original_received_qty = forms.DecimalField(widget=forms.HiddenInput(), required=False)
    ah_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = GoodsInwardNoteDetail
        fields = ['quantity', 'received_qty', 'asset_category', 'asset_sub_category']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Set initial values for hidden fields
            self.fields['original_challan_qty'].initial = self.instance.quantity
            self.fields['original_received_qty'].initial = self.instance.received_qty
            # This AHId comes from the complex join logic in loadData,
            # we need to pass it from the view's context
            # For simplicity, we'll assume AHId is passed to form constructor if it's dynamic
            # Or fetch it here based on ItemId/POId relation.
            # For now, let's assume `ah_id` is passed via `kwargs` or inferred
            # A more robust solution might pass this from the view or use a custom manager.
            
            # This is a simplification. The AHId is derived from PR/SPR details.
            # For a real system, you'd calculate this and pass it.
            # Or have a direct AHId field on GoodsInwardNoteDetail if it's constant.
            # For now, we'll assume a dummy AHId value or derive from item relations.
            # As per ASP.NET, it's passed as 'lblAHId' from a pre-calculated value.
            # We need to ensure it's available when initializing the form.
            # Let's add it to initial data or kwargs
            
            # If subcategory is already selected, populate its queryset
            if self.instance.asset_category_id:
                self.fields['asset_sub_category'].queryset = AssetSubCategory.objects.filter(
                    master_id=self.instance.asset_category_id
                )

    def clean(self):
        cleaned_data = super().clean()
        
        # Replicate ASP.NET Quantity validation: InvQty <= Challanqty AND RecvQty <= recdqty
        # Note: In ASP.NET, ChallanQty (Qty) and RecdQty (ReceivedQty) are the *current* values of the
        # row being edited. The validation was `InvQty <= current_challan_qty` and `RecvQty <= current_recd_qty`.
        # This seems counter-intuitive for an "edit" flow where you'd typically want to enforce
        # `new_qty <= PO_qty` or `new_qty <= original_qty_from_this_gin`.
        # The ASP.NET code for this validation was `InvQty <= Challanqty` and `RecvQty <= recdqty`
        # where `Challanqty` and `recdqty` were the *original* values from `lblChnQty` and `lblRecQty`.
        # Let's implement this as `new_value <= original_value`
        
        current_challan_qty = cleaned_data.get('quantity')
        current_received_qty = cleaned_data.get('received_qty')
        original_challan_qty = self.cleaned_data.get('original_challan_qty') # From hidden input
        original_received_qty = self.cleaned_data.get('original_received_qty') # From hidden input

        if original_challan_qty is not None and current_challan_qty is not None and current_challan_qty > original_challan_qty:
            self.add_error('quantity', "Entered Challan Qty cannot exceed the original Challan Qty for this GIN item.")
        
        if original_received_qty is not None and current_received_qty is not None and current_received_qty > original_received_qty:
            self.add_error('received_qty', "Entered Received Qty cannot exceed the original Received Qty for this GIN item.")

        # Replicate AHId = 33 category/subcategory requirement
        ah_id = self.cleaned_data.get('ah_id')
        asset_category = cleaned_data.get('asset_category')
        asset_sub_category = cleaned_data.get('asset_sub_category')

        if ah_id == 33:
            if not asset_category:
                self.add_error('asset_category', "Category is required for Asset type items.")
            if not asset_sub_category:
                self.add_error('asset_sub_category', "Sub-Category is required for Asset type items.")
        
        return cleaned_data

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

**Instructions:**

Define `UpdateView` for the main GIN, and a custom `TemplateView` or `ListView` to serve the DataTables and handle inline edits via HTMX.
Use `Model` and `Form` as defined. Set `template_name` and `success_url`.
Add success messages using `messages.success`.
Keep views thin (5-15 lines) and move business logic to models or custom managers.
Implement `Hx-Request` handling for HTMX.

```python
# inventory/views.py

from django.views.generic import TemplateView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.db import transaction
from .models import GoodsInwardNote, GoodsInwardNoteDetail, AssetSubCategory, ItemMaster
from .forms import GoodsInwardNoteForm, GoodsInwardNoteDetailForm
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt # For simplicity, but consider proper CSRF token handling with HTMX

# Assume user is authenticated and session data (compid, finyear) is available
# Middleware or decorators would populate these for a real system.
# For demo, we'll hardcode or retrieve from a mock session/user object.
MOCK_COMPANY_ID = 1 # Replace with actual logic to get company_id
MOCK_FIN_YEAR_ID = 1 # Replace with actual logic to get financial_year_id

class GoodsInwardNoteEditView(UpdateView):
    model = GoodsInwardNote
    form_class = GoodsInwardNoteForm
    template_name = 'inventory/goodsinwardnote/edit.html'
    context_object_name = 'gin_master' # For the main GIN object

    def get_object(self, queryset=None):
        # GINId and GINNo from Request.QueryString in ASP.NET
        gin_id = self.kwargs.get('pk')
        gin_no = self.request.GET.get('GNo') # GNo from query string
        challan_no = self.request.GET.get('ChNo')
        challan_date_str = self.request.GET.get('ChDt')

        # Mock query string/session values if not present (for testing)
        if not gin_id:
            gin_id = 1 # Example for testing
        if not gin_no:
            gin_no = 'GIN/001' # Example
        if not challan_no:
            challan_no = 'CH/001' # Example
        if not challan_date_str:
            challan_date_str = '2023-01-15' # Example

        obj = get_object_or_404(GoodsInwardNote, id=gin_id, gin_no=gin_no, challan_no=challan_no)
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass header details from query string as in ASP.NET
        context['gin_no_header'] = self.request.GET.get('GNo', self.object.gin_no)
        context['challan_no_header'] = self.request.GET.get('ChNo', self.object.challan_no)
        context['challan_date_header'] = self.request.GET.get('ChDt', self.object.challan_date.strftime('%d-%m-%Y') if self.object.challan_date else '')
        
        # Determine WO/Dept label and value
        # This complex logic is handled within GoodsInwardNoteDetailManager in `get_details_with_full_info`
        # For the header, we'll need a simplified approach or fetch it separately.
        # As it's only shown in the header from the *first* detail's PO/PR/SPR link
        # It implies a pre-computation. We'll simulate this by grabbing it from the first detail for display.
        first_detail_info = GoodsInwardNoteDetail.objects.get_details_with_full_info(
            gin_id=self.object.id,
            company_id=MOCK_COMPANY_ID,
            session_fin_year_id=MOCK_FIN_YEAR_ID,
            gin_no=self.object.gin_no,
            current_po_no=self.object.po_no # Assuming this is consistent across details
        )
        if first_detail_info:
            context['wo_dept_label'] = first_detail_info[0].get('wo_dept_label', '')
            context['wo_dept_value'] = first_detail_info[0].get('wo_dept_value', '')
        else:
            context['wo_dept_label'] = ''
            context['wo_dept_value'] = ''

        return context

    def form_valid(self, form):
        with transaction.atomic():
            user_session_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous'
            form.instance.update_header_details(form.cleaned_data, user_session_id)
            messages.success(self.request, 'Goods Inward Note header updated successfully.')
        return super().form_valid(form) # This handles saving the instance and redirect/response

    def get_success_url(self):
        # Redirect back to the edit page with original query string params
        gin_id = self.kwargs.get('pk')
        gin_no = self.request.GET.get('GNo')
        challan_no = self.request.GET.get('ChNo')
        challan_date_str = self.request.GET.get('ChDt')
        fyid = self.request.GET.get('fyid')
        supid = self.request.GET.get('SupId')
        po_no = self.request.GET.get('PoNo') # PoNo was also part of QS

        # This should replicate `Response.Redirect("GoodsInwardNote_GIN_Edit.aspx?ModId=9&SubModId=37");`
        # But for "edit_details", it should stay on the same page.
        # The ASP.NET update logic refreshes the entire page to update the GridView.
        # With HTMX, we don't redirect, but trigger a refresh event.
        if self.request.headers.get('HX-Request'):
             # If it's an HTMX request, return a 204 No Content and trigger a client-side event
             return reverse_lazy('goods_inward_note_edit', kwargs={'pk': self.object.pk}) + \
                 f"?GNo={gin_no}&ChNo={challan_no}&ChDt={challan_date_str}&fyid={fyid}&SupId={supid}&PoNo={po_no}"
        return reverse_lazy('goods_inward_note_list') # Fallback if not HTMX, or specific redirect.

class GoodsInwardNoteDetailTablePartialView(View):
    """
    Returns the HTMX-rendered partial for the GoodsInwardNoteDetail table.
    This replaces the GridView's loadData() call.
    """
    def get(self, request, pk, *args, **kwargs):
        gin_master = get_object_or_404(GoodsInwardNote, pk=pk)
        
        # Parameters from query string as in ASP.NET
        gin_no = request.GET.get('GNo', gin_master.gin_no)
        po_no = request.GET.get('PoNo', gin_master.po_no) # Assuming po_no from master is what loadData uses for current_po_no

        # Replace Session with Django's request.user and potentially a custom session for financial year
        company_id = MOCK_COMPANY_ID # request.user.company_id if available, else from session
        session_fin_year_id = MOCK_FIN_YEAR_ID # request.session.get('financial_year_id')

        details_with_info = GoodsInwardNoteDetail.objects.get_details_with_full_info(
            gin_id=gin_master.id,
            company_id=company_id,
            session_fin_year_id=session_fin_year_id,
            gin_no=gin_no,
            current_po_no=po_no
        )

        context = {
            'gin_details': details_with_info,
            'gin_master': gin_master, # Pass master for URL construction
        }
        return render(request, 'inventory/goodsinwardnote/_detail_table.html', context)

class GoodsInwardNoteDetailEditFormPartialView(View):
    """
    Returns an HTMX-rendered partial for editing a single GoodsInwardNoteDetail row.
    This replaces GridView's RowEditing event.
    """
    def get(self, request, pk, *args, **kwargs):
        detail_instance = get_object_or_404(GoodsInwardNoteDetail, pk=pk)
        
        # The AHId is needed for conditional display/validation in the form.
        # It's not directly on GoodsInwardNoteDetail, but derived from PR/SPR.
        # We need to re-derive it here or ensure it's available.
        # For simplicity, we'll re-run a simplified portion of loadData or fetch pre-calculated.
        # In a real system, this AHId might be stored as a cached property or field on the Item.
        
        # Placeholder for AHId. In production, this would come from the complex query.
        # The AHId is derived from item details linked via PO/PR/SPR.
        # For now, we'll mock it or retrieve it from an appropriate source.
        # The `get_details_with_full_info` returns the AHId, we need to pass it to the form.
        
        # To get the AHId for the specific detail, we need to do a lookup similar to `loadData`
        # for just this one item.
        # Alternatively, we could fetch all detailed info once and pass `ah_id` as a `data-` attribute
        # to the HTMX button that triggers this view.
        
        # Simplified AHId retrieval (replace with robust logic if necessary)
        item_master_id = None
        po_detail = PurchaseOrderDetail.objects.filter(id=detail_instance.po_id).first()
        if po_detail:
            if po_detail.master.pr_spr_flag == '0' and po_detail.pr_id:
                pr_detail = PurchaseRequisitionDetail.objects.filter(id=po_detail.pr_id).first()
                if pr_detail: item_master_id = pr_detail.item_id
            elif po_detail.master.pr_spr_flag == '1' and po_detail.spr_id:
                spr_detail = StorePurchaseRequisitionDetail.objects.filter(id=po_detail.spr_id).first()
                if spr_detail: item_master_id = spr_detail.item_id
        
        ah_id_val = 0 # Default
        if item_master_id:
            # This is still missing the exact AHId derived from PR/SPR for that item.
            # For accurate AHId, you'd need to re-run the relevant parts of the `get_details_with_full_info` logic for this specific detail.
            # Let's assume for this edit context, the AHId for the item is available or can be determined
            # For the purpose of this example, we'll simulate the AHId value.
            # A more robust solution might store AHId directly on relevant item/PO models or cache it.
            # This is a critical point where the "fat model" approach shines - having the model
            # compute `ah_id` for its item can prevent this kind of duplication in views.
            try:
                # We need to find the AHId associated with the item via its PR/SPR.
                # This requires complex joins again, or an optimized lookup.
                # For now, let's just make sure the `GoodsInwardNoteDetail` form can receive it.
                # The 'get_details_with_full_info' method already computes it.
                # Fetching it again for a single item:
                detail_full_info = GoodsInwardNoteDetail.objects.get_details_with_full_info(
                    gin_id=detail_instance.gin_master.id,
                    company_id=MOCK_COMPANY_ID,
                    session_fin_year_id=MOCK_FIN_YEAR_ID,
                    gin_no=detail_instance.gin_no,
                    current_po_no=detail_instance.gin_master.po_no
                )
                filtered_info = [d for d in detail_full_info if d['id'] == detail_instance.id]
                if filtered_info:
                    ah_id_val = filtered_info[0].get('ah_id', 0)
            except Exception as e:
                # Log error or set default AHId
                pass


        form = GoodsInwardNoteDetailForm(
            instance=detail_instance,
            initial={'ah_id': ah_id_val} # Pass AHId to the form for conditional validation
        )
        
        # Populate subcategory dropdown based on current category
        if detail_instance.asset_category_id:
            form.fields['asset_sub_category'].queryset = AssetSubCategory.objects.filter(
                master_id=detail_instance.asset_category_id
            )

        context = {
            'form': form,
            'detail_id': pk,
            'ah_id': ah_id_val, # Also pass to template for Alpine.js visibility logic
            'current_category_id': detail_instance.asset_category_id # Pass to template for dropdown state
        }
        return render(request, 'inventory/goodsinwardnote/_detail_edit_form.html', context)

@method_decorator(csrf_exempt, name='dispatch') # CSRF exempt for demo, in production use proper HTMX+CSRF
class GoodsInwardNoteDetailUpdateView(View):
    """
    Handles POST requests to update a single GoodsInwardNoteDetail.
    This replaces GridView's RowUpdating event.
    """
    def post(self, request, pk, *args, **kwargs):
        detail_instance = get_object_or_404(GoodsInwardNoteDetail, pk=pk)
        
        # Retrieve AHId from the request data, as it was passed from the form
        ah_id_val = int(request.POST.get('ah_id', 0))

        form = GoodsInwardNoteDetailForm(
            request.POST, 
            instance=detail_instance,
            initial={'ah_id': ah_id_val} # Pass AHId to form for validation
        )

        if form.is_valid():
            with transaction.atomic():
                # Update header details first, as in ASP.NET
                gin_master = detail_instance.gin_master
                gin_master_form = GoodsInwardNoteForm(
                    request.POST, # Take main form data from combined POST
                    instance=gin_master
                )
                if gin_master_form.is_valid():
                    user_session_id = request.user.username if request.user.is_authenticated else 'anonymous'
                    gin_master_form.instance.update_header_details(gin_master_form.cleaned_data, user_session_id)
                else:
                    # If master form is invalid, return errors (unlikely with HTMX approach)
                    return HttpResponse(f"<div class='text-red-500'>Header Update Error: {gin_master_form.errors.as_text()}</div>", status=400)

                # Then update detail
                form.instance.update_detail_data(
                    form.cleaned_data['quantity'],
                    form.cleaned_data['received_qty'],
                    form.cleaned_data['asset_category'].id if form.cleaned_data['asset_category'] else None,
                    form.cleaned_data['asset_sub_category'].id if form.cleaned_data['asset_sub_category'] else None
                )
            
            messages.success(request, 'Record updated successfully!')
            # Signal HTMX to refresh the details table
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshGinDetailList'})
        else:
            # Form is invalid, re-render the edit form with errors
            context = {
                'form': form,
                'detail_id': pk,
                'ah_id': ah_id_val,
                'current_category_id': detail_instance.asset_category_id
            }
            return render(request, 'inventory/goodsinwardnote/_detail_edit_form.html', context, status=400)

class GetSubCategoriesView(View):
    """
    HTMX endpoint to dynamically populate subcategories based on selected category.
    Replicates ddCategory_SelectedIndexChanged.
    """
    def get(self, request, category_id, *args, **kwargs):
        subcategories = AssetSubCategory.objects.filter(master_id=category_id).order_by('abbreviation')
        options = [{'id': 0, 'text': 'Select'}] # Add 'Select' option as per ASP.NET
        for subcat in subcategories:
            options.append({'id': subcat.id, 'text': subcat.abbreviation})
        
        # Return HTML options for HTMX to swap into the select element
        html_options = '<option value="0">Select</option>'
        for subcat in subcategories:
            html_options += f'<option value="{subcat.id}">{subcat.abbreviation}</option>'
        return HttpResponse(html_options)

class DownloadFileView(View):
    """
    Handles file downloads for Item images or specification sheets.
    Replicates GridView1_RowCommand (downloadImg, downloadSpec)
    """
    def get(self, request, item_id, file_type):
        item = get_object_or_404(ItemMaster, pk=item_id)
        
        if file_type == 'image':
            file_data = item.file_data
            file_name = item.file_name
            content_type = item.content_type
        elif file_type == 'spec':
            file_data = item.attachment_data
            file_name = item.attachment_name
            content_type = item.attachment_content_type
        else:
            return HttpResponse("Invalid file type", status=400)

        if not file_data:
            messages.error(request, "File not found.")
            return HttpResponse("File not found.", status=404)

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response

```

### 4.4 Templates

Task: Create templates for each view.

**Instructions:**

Main Template (`inventory/goodsinwardnote/edit.html`):
- Extend `core/base.html`.
- Display header information and main form.
- Use HTMX to load the detail table and handle main form submission.
- Implement modal for inline editing forms with Alpine.js.

Table Partial Template (`inventory/goodsinwardnote/_detail_table.html`):
- Contains the DataTables structure for detail items.
- Uses HTMX for edit/cancel actions for each row.

Form Partial Template (`inventory/goodsinwardnote/_detail_edit_form.html`):
- Used for inline editing of detail rows.
- HTMX handles submission back to `GoodsInwardNoteDetailUpdateView`.

```html
<!-- inventory/goodsinwardnote/edit.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 bg-blue-600 text-white p-3 rounded-t-lg">
            Goods Inward Note [GIN] - Edit
        </h2>
        
        <!-- Header Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 text-sm">
            <div>
                <span class="font-semibold">GIN No:</span> <span class="font-bold">{{ gin_no_header }}</span>
            </div>
            <div>
                <span class="font-semibold">Challan No:</span> <span class="font-bold">{{ challan_no_header }}</span>
            </div>
            <div>
                <span class="font-semibold">Challan Date:</span> <span class="font-bold">{{ challan_date_header }}</span>
            </div>
            <div class="col-span-1 md:col-span-2 lg:col-span-1">
                <span class="font-semibold">{{ wo_dept_label }}:</span> <span class="font-bold">{{ wo_dept_value }}</span>
            </div>
        </div>

        <!-- Main GIN Form -->
        <form hx-post="{{ request.path }}" hx-swap="none" id="ginMasterForm" class="space-y-4">
            {% csrf_token %}
            <!-- Hidden fields from query string (if needed for context) -->
            <input type="hidden" name="GNo" value="{{ gin_no_header }}">
            <input type="hidden" name="ChNo" value="{{ challan_no_header }}">
            <input type="hidden" name="ChDt" value="{{ challan_date_header }}">
            <input type="hidden" name="fyid" value="{{ request.GET.fyid }}">
            <input type="hidden" name="SupId" value="{{ request.GET.SupId }}">
            <input type="hidden" name="PoNo" value="{{ request.GET.PoNo }}">

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="{{ form.gate_entry_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.gate_entry_no.label }}
                    </label>
                    {{ form.gate_entry_no }}
                    {% if form.gate_entry_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gate_entry_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.entry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.entry_date.label }}
                    </label>
                    {{ form.entry_date }}
                    {% if form.entry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.entry_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.entry_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.entry_time.label }}
                    </label>
                    {{ form.entry_time }}
                    {% if form.entry_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.entry_time.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.mode_of_transport.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.mode_of_transport.label }}
                    </label>
                    {{ form.mode_of_transport }}
                    {% if form.mode_of_transport.errors %}<p class="text-red-500 text-xs mt-1">{{ form.mode_of_transport.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.vehicle_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.vehicle_no.label }}
                    </label>
                    {{ form.vehicle_no }}
                    {% if form.vehicle_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vehicle_no.errors }}</p>{% endif %}
                </div>
            </div>
             <div class="mt-6 flex items-center justify-end space-x-4">
                <button 
                    type="submit" 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    hx-trigger="click"
                    hx-on::after-request="if(event.detail.successful) { messages.success('Header updated successfully!'); }"
                    >
                    Save Header
                </button>
                 <a href="{% url 'goods_inward_note_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Cancel
                 </a>
            </div>
        </form>
    </div>

    <!-- Goods Inward Note Details Table -->
    <div id="ginDetailTableContainer"
         hx-trigger="load, refreshGinDetailList from:body"
         hx-get="{% url 'goods_inward_note_detail_table' gin_master.pk %}?GNo={{ gin_master.gin_no }}&PoNo={{ gin_master.po_no }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading GIN details...</p>
        </div>
    </div>
</div>

<!-- Global Modal -->
<div id="global-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50"
     x-data="{ show: false }"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0 scale-90"
     x-transition:enter-end="opacity-100 scale-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100 scale-100"
     x-transition:leave-end="opacity-0 scale-90"
     _="on closeModal add .hidden to #global-modal then remove .is-active from #global-modalContent"
     >
    <div id="global-modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full transform transition-all duration-300 ease-out"
         _="on htmx:afterSwap from body then remove .is-active from me">
        <!-- Content loaded by HTMX -->
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components directly needed here for simple modal toggle.
        // HTMX handles loading content into the modal.
    });

    // Event listener to open modal
    document.body.addEventListener('htmx:afterOnLoad', function(event) {
        // Check if the loaded content is meant for the modal
        if (event.detail.target && event.detail.target.id === 'global-modalContent') {
            document.getElementById('global-modal').classList.remove('hidden');
            document.getElementById('global-modal').classList.add('is-active'); // For Alpine to pick up
        }
    });

    // Event listener to close modal (e.g., after form submission successful, or cancel button)
    document.body.addEventListener('closeModal', function() {
        document.getElementById('global-modal').classList.add('hidden');
        document.getElementById('global-modal').classList.remove('is-active'); // For Alpine to pick up
        document.getElementById('global-modalContent').innerHTML = ''; // Clear content
    });

    // Handle messages
    document.body.addEventListener('messages.success', function(evt) {
        alert(evt.detail.message); // Simple alert for messages
    });
</script>
{% endblock %}
```

```html
<!-- inventory/goodsinwardnote/_detail_table.html -->
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="ginDetailTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Image</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Spec. Sheet</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Code</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">UOM</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Category</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Sub-Cate</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">PO Qty</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Tot Recd Qty</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Challan Qty</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Recd Qty</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in gin_details %}
            <tr id="detail-row-{{ detail.id }}">
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-right">{{ forloop.counter }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-center">
                    {% if detail.can_edit %}
                    <button class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-get="{% url 'goods_inward_note_detail_edit' detail.id %}"
                            hx-target="#global-modalContent"
                            hx-swap="innerHTML"
                            _="on click add .is-active to #global-modal">
                        Edit
                    </button>
                    {% else %}
                    <span class="text-gray-500 text-xs">GRR/GSN Exists</span>
                    {% endif %}
                </td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-center">
                    {% if detail.file_name %}
                    <a href="{% url 'download_file' detail.item_id 'image' %}" class="text-blue-600 hover:underline text-xs">View</a>
                    {% endif %}
                </td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-center">
                    {% if detail.attachment_name %}
                    <a href="{% url 'download_file' detail.item_id 'spec' %}" class="text-blue-600 hover:underline text-xs">View</a>
                    {% endif %}
                </td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-center">{{ detail.item_code }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-left">{{ detail.description }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-center">{{ detail.uom }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-center">{{ detail.category_name }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-center">{{ detail.subcategory_name }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-right">{{ detail.po_qty|floatformat:3 }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-right">{{ detail.total_received_qty|floatformat:3 }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-right">{{ detail.challan_qty|floatformat:3 }}</td>
                <td class="px-5 py-2 border-b border-gray-200 text-sm text-right">{{ detail.received_qty|floatformat:3 }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="13" class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center text-red-500">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTable on the loaded table
    $(document).ready(function() {
        $('#ginDetailTable').DataTable({
            "pageLength": 15, // As per ASP.NET PageSize
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true // Add responsiveness for better mobile viewing
        });
    });
</script>
```

```html
<!-- inventory/goodsinwardnote/_detail_edit_form.html -->
<div class="p-6" x-data="{
    ahId: {{ ah_id }},
    selectedCategory: {{ current_category_id|default:0 }},
    loadSubcategories: function() {
        if (this.selectedCategory) {
            fetch(`/inventory/get_subcategories/${this.selectedCategory}/`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('id_asset_sub_category').innerHTML = html;
                });
        } else {
            document.getElementById('id_asset_sub_category').innerHTML = '<option value="0">Select</option>';
        }
    }
}" x-init="loadSubcategories()">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit GIN Detail</h3>
    <form hx-post="{% url 'goods_inward_note_detail_update' detail_id %}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.successful) { messages.success('Detail updated successfully!'); document.body.dispatchEvent(new CustomEvent('closeModal')); } else { console.error('Form submission failed.'); }">
        {% csrf_token %}
        
        <!-- Pass main form data as hidden fields for the header update in view -->
        <input type="hidden" name="gate_entry_no" value="{{ gin_master.gate_entry_no }}">
        <input type="hidden" name="entry_date" value="{{ gin_master.entry_date|date:'Y-m-d' }}">
        <input type="hidden" name="entry_time" value="{{ gin_master.entry_time|time:'H:i:s' }}">
        <input type="hidden" name="mode_of_transport" value="{{ gin_master.mode_of_transport }}">
        <input type="hidden" name="vehicle_no" value="{{ gin_master.vehicle_no }}">

        <!-- Hidden AHId for server-side validation -->
        {{ form.ah_id }}

        <div class="space-y-4">
            <div>
                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.quantity.label }}
                </label>
                {{ form.quantity }}
                {% if form.quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.received_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.received_qty.label }}
                </label>
                {{ form.received_qty }}
                {% if form.received_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.received_qty.errors }}</p>{% endif %}
            </div>
            
            <div x-show="ahId === 33">
                <label for="{{ form.asset_category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.asset_category.label }}
                </label>
                <select name="{{ form.asset_category.html_name }}" id="{{ form.asset_category.id_for_label }}"
                        class="{{ form.asset_category.field.widget.attrs.class }}"
                        hx-get="{% url 'get_subcategories' 0 %}"
                        hx-target="#id_asset_sub_category"
                        hx-trigger="change"
                        x-model="selectedCategory"
                        x-on:change="loadSubcategories()"
                        >
                    <option value="">Select</option>
                    {% for category in form.asset_category.field.queryset %}
                        <option value="{{ category.id }}" {% if category.id == form.asset_category.value %}selected{% endif %}>
                            {{ category.abbreviation }}
                        </option>
                    {% endfor %}
                </select>
                {% if form.asset_category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.asset_category.errors }}</p>{% endif %}
            </div>

            <div x-show="ahId === 33">
                <label for="{{ form.asset_sub_category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.asset_sub_category.label }}
                </label>
                {{ form.asset_sub_category }} {# This select will be updated by HTMX #}
                {% if form.asset_sub_category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.asset_sub_category.errors }}</p>{% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click document.body.dispatchEvent(new CustomEvent('closeModal'))">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update Detail
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

**Instructions:**

Create paths for main edit, detail table partial, detail edit form, detail update, and dynamic subcategory loading.
Use appropriate naming patterns and consistent URL structure.

```python
# inventory/urls.py

from django.urls import path
from .views import (
    GoodsInwardNoteEditView,
    GoodsInwardNoteDetailTablePartialView,
    GoodsInwardNoteDetailEditFormPartialView,
    GoodsInwardNoteDetailUpdateView,
    GetSubCategoriesView,
    DownloadFileView
)

urlpatterns = [
    # Main GIN Edit Page (GET to display form, POST to update header)
    # The ASP.NET page uses query parameters for initial GIN details.
    # We will replicate this for GET requests, but primary key `pk` will identify the object.
    path('goodsinwardnote/edit/<int:pk>/', GoodsInwardNoteEditView.as_view(), name='goods_inward_note_edit'),

    # HTMX endpoint for the details table
    path('goodsinwardnote/<int:pk>/detail_table/', GoodsInwardNoteDetailTablePartialView.as_view(), name='goods_inward_note_detail_table'),

    # HTMX endpoint for loading a single detail item's edit form (into modal)
    path('goodsinwardnote/detail_edit/<int:pk>/', GoodsInwardNoteDetailEditFormPartialView.as_view(), name='goods_inward_note_detail_edit'),

    # HTMX endpoint for updating a single detail item
    path('goodsinwardnote/detail_update/<int:pk>/', GoodsInwardNoteDetailUpdateView.as_view(), name='goods_inward_note_detail_update'),

    # HTMX endpoint for populating subcategories dropdown
    path('goodsinwardnote/get_subcategories/<int:category_id>/', GetSubCategoriesView.as_view(), name='get_subcategories'),

    # File download endpoints
    path('download_file/<int:item_id>/<str:file_type>/', DownloadFileView.as_view(), name='download_file'),

    # Placeholder for the actual list page the cancel button redirects to
    path('goodsinwardnote/list/', TemplateView.as_view(template_name='inventory/goodsinwardnote/list.html'), name='goods_inward_note_list'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

**Instructions:**

Include comprehensive unit tests for model methods and properties.
Add integration tests for all views (main edit, detail table, detail edit form, detail update, dynamic dropdown, file download).
Ensure at least 80% test coverage of code.

```python
# inventory/tests.py

from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, time
from decimal import Decimal
from unittest.mock import patch

from .models import (
    GoodsInwardNote, GoodsInwardNoteDetail, FinancialYear,
    AssetCategory, AssetSubCategory, ItemMaster, UnitMaster,
    PurchaseOrderMaster, PurchaseOrderDetail,
    PurchaseRequisitionMaster, PurchaseRequisitionDetail,
    StorePurchaseRequisitionMaster, StorePurchaseRequisitionDetail,
    BusinessGroup, MaterialReceivedMaster, MaterialReceivedDetail,
    MaterialServiceNoteMaster, MaterialServiceNoteDetail
)

# Mock user and session data for testing
MOCK_COMPANY_ID = 1
MOCK_FIN_YEAR_ID = 1

class MockUser:
    """A mock user object for testing authenticated requests."""
    is_authenticated = True
    username = 'testuser'
    company_id = MOCK_COMPANY_ID # Custom attribute

class GoodsInwardNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for testing
        FinancialYear.objects.create(id=MOCK_FIN_YEAR_ID, fin_year='2023-2024', company_id=MOCK_COMPANY_ID)
        cls.asset_category_normal = AssetCategory.objects.create(id=1, abbreviation='RAW')
        cls.asset_category_asset = AssetCategory.objects.create(id=33, abbreviation='ASSET')
        cls.asset_subcategory_normal = AssetSubCategory.objects.create(id=1, master_id=cls.asset_category_normal, abbreviation='SUB_RAW')
        cls.asset_subcategory_asset = AssetSubCategory.objects.create(id=101, master_id=cls.asset_category_asset, abbreviation='SUB_ASSET_A')
        AssetSubCategory.objects.create(id=102, master_id=cls.asset_category_asset, abbreviation='SUB_ASSET_B')
        
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_regular = ItemMaster.objects.create(id=1, item_code='ITEM001', manufacturer_description='Regular Item', uom_basic_id=cls.unit_ea.id, company_id=MOCK_COMPANY_ID, file_name='img.jpg', file_data=b'abc', content_type='image/jpeg')
        cls.item_asset = ItemMaster.objects.create(id=2, item_code='ITEM002', manufacturer_description='Asset Item', uom_basic_id=cls.unit_ea.id, company_id=MOCK_COMPANY_ID, attachment_name='spec.pdf', attachment_data=b'xyz', attachment_content_type='application/pdf')

        cls.po_master_pr = PurchaseOrderMaster.objects.create(id=1, po_no='PO001', pr_spr_flag='0', fin_year_id=MOCK_FIN_YEAR_ID, company_id=MOCK_COMPANY_ID)
        cls.po_master_spr = PurchaseOrderMaster.objects.create(id=2, po_no='PO002', pr_spr_flag='1', fin_year_id=MOCK_FIN_YEAR_ID, company_id=MOCK_COMPANY_ID)

        cls.pr_master = PurchaseRequisitionMaster.objects.create(id=1, pr_no='PR001', wo_no='WO123', company_id=MOCK_COMPANY_ID)
        cls.pr_detail_regular = PurchaseRequisitionDetail.objects.create(id=1, master=cls.pr_master, pr_no='PR001', item_id=cls.item_regular.id, ah_id=10) # AHId != 33
        cls.pr_detail_asset = PurchaseRequisitionDetail.objects.create(id=2, master=cls.pr_master, pr_no='PR001', item_id=cls.item_asset.id, ah_id=33) # AHId = 33

        cls.spr_master = StorePurchaseRequisitionMaster.objects.create(id=1, spr_no='SPR001', company_id=MOCK_COMPANY_ID)
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='DEPTA')
        cls.spr_detail_asset = StorePurchaseRequisitionDetail.objects.create(id=1, master=cls.spr_master, spr_no='SPR001', item_id=cls.item_asset.id, wo_no='WO456', ah_id=33, department_id=cls.business_group.id)

        cls.po_detail_pr = PurchaseOrderDetail.objects.create(id=1, master=cls.po_master_pr, po_no='PO001', pr_no='PR001', pr_id=cls.pr_detail_asset.id, quantity=Decimal('10.000'))
        cls.po_detail_spr = PurchaseOrderDetail.objects.create(id=2, master=cls.po_master_spr, po_no='PO002', spr_no='SPR001', spr_id=cls.spr_detail_asset.id, quantity=Decimal('20.000'))

        cls.gin_master = GoodsInwardNote.objects.create(
            id=1, gin_no='GIN001', challan_no='CH001', challan_date=date(2023, 1, 15),
            gate_entry_no='GE001', entry_date=date(2023, 1, 16), entry_time=time(10, 0, 0),
            mode_of_transport='Road', vehicle_no='XYZ123', po_no='PO001',
            company_id=MOCK_COMPANY_ID, fin_year_id=MOCK_FIN_YEAR_ID,
            system_date=date(2023,1,16), system_time=time(10,0,0), session_id='olduser'
        )
        cls.gin_detail_1 = GoodsInwardNoteDetail.objects.create(
            id=1, gin_master=cls.gin_master, gin_no='GIN001', po_id=cls.po_detail_pr.id,
            quantity=Decimal('5.000'), received_qty=Decimal('4.000'),
            asset_category=cls.asset_category_asset, asset_sub_category=cls.asset_subcategory_asset
        )
        cls.gin_detail_2 = GoodsInwardNoteDetail.objects.create(
            id=2, gin_master=cls.gin_master, gin_no='GIN001', po_id=cls.po_detail_spr.id,
            quantity=Decimal('10.000'), received_qty=Decimal('8.000'),
            asset_category=cls.asset_category_normal, asset_sub_category=cls.asset_subcategory_normal # Normal item, but asset category assigned for test
        )
    
    def test_gin_master_creation(self):
        self.assertEqual(self.gin_master.gin_no, 'GIN001')
        self.assertEqual(self.gin_master.gate_entry_no, 'GE001')
        self.assertEqual(self.gin_master.company_id, MOCK_COMPANY_ID)

    def test_gin_master_update_header_details(self):
        new_data = {
            'gate_entry_no': 'NEWGE',
            'entry_date': date(2024, 1, 1),
            'entry_time': time(11, 30, 0),
            'mode_of_transport': 'Air',
            'vehicle_no': 'ABC456',
        }
        self.gin_master.update_header_details(new_data, 'newuser')
        self.gin_master.refresh_from_db()
        self.assertEqual(self.gin_master.gate_entry_no, 'NEWGE')
        self.assertEqual(self.gin_master.session_id, 'newuser')

    def test_gin_detail_update_detail_data(self):
        self.gin_detail_1.update_detail_data(
            Decimal('6.000'), Decimal('5.000'),
            self.asset_category_normal.id, self.asset_subcategory_normal.id
        )
        self.gin_detail_1.refresh_from_db()
        self.assertEqual(self.gin_detail_1.quantity, Decimal('6.000'))
        self.assertEqual(self.gin_detail_1.received_qty, Decimal('5.000'))
        self.assertEqual(self.gin_detail_1.asset_category, self.asset_category_normal)
        self.assertEqual(self.gin_detail_1.asset_sub_category, self.asset_subcategory_normal)

    def test_gin_detail_get_details_with_full_info(self):
        details_info = GoodsInwardNoteDetail.objects.get_details_with_full_info(
            gin_id=self.gin_master.id,
            company_id=MOCK_COMPANY_ID,
            session_fin_year_id=MOCK_FIN_YEAR_ID,
            gin_no=self.gin_master.gin_no,
            current_po_no=self.gin_master.po_no
        )
        self.assertEqual(len(details_info), 2)
        
        detail_1_info = next(item for item in details_info if item['id'] == self.gin_detail_1.id)
        self.assertEqual(detail_1_info['item_code'], self.item_asset.item_code)
        self.assertEqual(detail_1_info['description'], self.item_asset.manufacturer_description)
        self.assertEqual(detail_1_info['uom'], self.unit_ea.symbol)
        self.assertEqual(detail_1_info['po_qty'], self.po_detail_pr.quantity)
        self.assertEqual(detail_1_info['challan_qty'], self.gin_detail_1.quantity)
        self.assertEqual(detail_1_info['received_qty'], self.gin_detail_1.received_qty)
        self.assertEqual(detail_1_info['category_name'], self.asset_category_asset.abbreviation)
        self.assertEqual(detail_1_info['subcategory_name'], self.asset_subcategory_asset.abbreviation)
        self.assertEqual(detail_1_info['ah_id'], 33) # AHId from PR_detail_asset
        self.assertTrue(detail_1_info['file_name'] == '') # Item 2 has no image
        self.assertTrue(detail_1_info['attachment_name'] == 'View') # Item 2 has spec
        self.assertEqual(detail_1_info['wo_dept_label'], 'WONO')
        self.assertEqual(detail_1_info['wo_dept_value'], self.pr_master.wo_no)


        detail_2_info = next(item for item in details_info if item['id'] == self.gin_detail_2.id)
        self.assertEqual(detail_2_info['po_qty'], self.po_detail_spr.quantity)
        self.assertEqual(detail_2_info['wo_dept_label'], 'WONO') # SPR detail has WO_NO
        self.assertEqual(detail_2_info['wo_dept_value'], self.spr_detail_asset.wo_no)

    def test_has_material_received_or_service_note_false(self):
        self.assertFalse(self.gin_detail_1.has_material_received_or_service_note(MOCK_COMPANY_ID))

    def test_has_material_received_or_service_note_true_grr(self):
        grr_master = MaterialReceivedMaster.objects.create(id=1, grr_no='GRR001', gin=self.gin_master, company_id=MOCK_COMPANY_ID)
        MaterialReceivedDetail.objects.create(id=1, master=grr_master, grr_no='GRR001', po_id=self.gin_detail_1.po_id)
        self.assertTrue(self.gin_detail_1.has_material_received_or_service_note(MOCK_COMPANY_ID))

    def test_has_material_received_or_service_note_true_gsn(self):
        gsn_master = MaterialServiceNoteMaster.objects.create(id=1, gsn_no='GSN001', gin=self.gin_master, company_id=MOCK_COMPANY_ID)
        MaterialServiceNoteDetail.objects.create(id=1, master=gsn_master, gsn_no='GSN001', po_id=self.gin_detail_2.po_id)
        self.assertTrue(self.gin_detail_2.has_material_received_or_service_note(MOCK_COMPANY_ID))


class GoodsInwardNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Re-create all necessary data, similar to ModelTest setUpTestData
        FinancialYear.objects.create(id=MOCK_FIN_YEAR_ID, fin_year='2023-2024', company_id=MOCK_COMPANY_ID)
        cls.asset_category_normal = AssetCategory.objects.create(id=1, abbreviation='RAW')
        cls.asset_category_asset = AssetCategory.objects.create(id=33, abbreviation='ASSET')
        cls.asset_subcategory_normal = AssetSubCategory.objects.create(id=1, master_id=cls.asset_category_normal, abbreviation='SUB_RAW')
        cls.asset_subcategory_asset_a = AssetSubCategory.objects.create(id=101, master_id=cls.asset_category_asset, abbreviation='SUB_ASSET_A')
        cls.asset_subcategory_asset_b = AssetSubCategory.objects.create(id=102, master_id=cls.asset_category_asset, abbreviation='SUB_ASSET_B')
        
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_regular = ItemMaster.objects.create(id=1, item_code='ITEM001', manufacturer_description='Regular Item', uom_basic_id=cls.unit_ea.id, company_id=MOCK_COMPANY_ID, file_name='img.jpg', file_data=b'abc', content_type='image/jpeg')
        cls.item_asset = ItemMaster.objects.create(id=2, item_code='ITEM002', manufacturer_description='Asset Item', uom_basic_id=cls.unit_ea.id, company_id=MOCK_COMPANY_ID, attachment_name='spec.pdf', attachment_data=b'xyz', attachment_content_type='application/pdf')

        cls.po_master_pr = PurchaseOrderMaster.objects.create(id=1, po_no='PO001', pr_spr_flag='0', fin_year_id=MOCK_FIN_YEAR_ID, company_id=MOCK_COMPANY_ID)
        cls.po_master_spr = PurchaseOrderMaster.objects.create(id=2, po_no='PO002', pr_spr_flag='1', fin_year_id=MOCK_FIN_YEAR_ID, company_id=MOCK_COMPANY_ID)

        cls.pr_master = PurchaseRequisitionMaster.objects.create(id=1, pr_no='PR001', wo_no='WO123', company_id=MOCK_COMPANY_ID)
        cls.pr_detail_regular = PurchaseRequisitionDetail.objects.create(id=1, master=cls.pr_master, pr_no='PR001', item_id=cls.item_regular.id, ah_id=10) # AHId != 33
        cls.pr_detail_asset = PurchaseRequisitionDetail.objects.create(id=2, master=cls.pr_master, pr_no='PR001', item_id=cls.item_asset.id, ah_id=33) # AHId = 33

        cls.spr_master = StorePurchaseRequisitionMaster.objects.create(id=1, spr_no='SPR001', wo_no='WO456', company_id=MOCK_COMPANY_ID)
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='DEPTA')
        cls.spr_detail_asset = StorePurchaseRequisitionDetail.objects.create(id=1, master=cls.spr_master, spr_no='SPR001', item_id=cls.item_asset.id, wo_no='WO456', ah_id=33, department_id=cls.business_group.id)

        cls.po_detail_pr = PurchaseOrderDetail.objects.create(id=1, master=cls.po_master_pr, po_no='PO001', pr_no='PR001', pr_id=cls.pr_detail_asset.id, quantity=Decimal('10.000'))
        cls.po_detail_spr = PurchaseOrderDetail.objects.create(id=2, master=cls.po_master_spr, po_no='PO002', spr_no='SPR001', spr_id=cls.spr_detail_asset.id, quantity=Decimal('20.000'))

        cls.gin_master = GoodsInwardNote.objects.create(
            id=1, gin_no='GIN001', challan_no='CH001', challan_date=date(2023, 1, 15),
            gate_entry_no='GE001', entry_date=date(2023, 1, 16), entry_time=time(10, 0, 0),
            mode_of_transport='Road', vehicle_no='XYZ123', po_no='PO001', # PoNo was passed in QS
            company_id=MOCK_COMPANY_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )
        cls.gin_detail_1 = GoodsInwardNoteDetail.objects.create(
            id=1, gin_master=cls.gin_master, gin_no='GIN001', po_id=cls.po_detail_pr.id,
            quantity=Decimal('5.000'), received_qty=Decimal('4.000'),
            asset_category=cls.asset_category_asset, asset_sub_category=cls.asset_subcategory_asset_a
        )
        cls.gin_detail_2 = GoodsInwardNoteDetail.objects.create(
            id=2, gin_master=cls.gin_master, gin_no='GIN001', po_id=cls.po_detail_spr.id,
            quantity=Decimal('10.000'), received_qty=Decimal('8.000'),
            asset_category=cls.asset_category_normal, asset_sub_category=cls.asset_subcategory_normal
        )

    def setUp(self):
        self.client = Client()
        self.client.force_login(MockUser()) # Simulate authenticated user

        # Base query string for GIN master details
        self.base_qs = f"GNo={self.gin_master.gin_no}&ChNo={self.gin_master.challan_no}&ChDt={self.gin_master.challan_date.strftime('%d-%m-%Y')}&fyid={MOCK_FIN_YEAR_ID}&SupId=1&PoNo={self.gin_master.po_no}"


    def test_gin_edit_view_get(self):
        url = reverse('goods_inward_note_edit', kwargs={'pk': self.gin_master.pk}) + f"?{self.base_qs}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsinwardnote/edit.html')
        self.assertContains(response, 'Goods Inward Note [GIN] - Edit')
        self.assertContains(response, self.gin_master.gin_no)
        self.assertContains(response, self.gin_master.gate_entry_no)
        self.assertIsInstance(response.context['form'], GoodsInwardNoteForm)

    def test_gin_edit_view_post_success(self):
        url = reverse('goods_inward_note_edit', kwargs={'pk': self.gin_master.pk}) + f"?{self.base_qs}"
        data = {
            'gate_entry_no': 'UPDATED_GE',
            'entry_date': '2023-01-17',
            'entry_time': '11:00:00',
            'mode_of_transport': 'Train',
            'vehicle_no': 'DEF789',
            # Include hidden fields from QS to match form
            'GNo': self.gin_master.gin_no,
            'ChNo': self.gin_master.challan_no,
            'ChDt': self.gin_master.challan_date.strftime('%d-%m-%Y'),
            'fyid': str(MOCK_FIN_YEAR_ID),
            'SupId': '1',
            'PoNo': self.gin_master.po_no,
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX 204 No Content for successful form_valid
        
        self.gin_master.refresh_from_db()
        self.assertEqual(self.gin_master.gate_entry_no, 'UPDATED_GE')
        self.assertEqual(self.gin_master.mode_of_transport, 'Train')
        
    def test_gin_edit_view_post_invalid(self):
        url = reverse('goods_inward_note_edit', kwargs={'pk': self.gin_master.pk}) + f"?{self.base_qs}"
        data = {
            'gate_entry_no': '', # Missing required field
            'entry_date': '2023-01-17',
            'entry_time': '11:00:00',
            'mode_of_transport': 'Train',
            'vehicle_no': 'DEF789',
            'GNo': self.gin_master.gin_no,
            'ChNo': self.gin_master.challan_no,
            'ChDt': self.gin_master.challan_date.strftime('%d-%m-%Y'),
            'fyid': str(MOCK_FIN_YEAR_ID),
            'SupId': '1',
            'PoNo': self.gin_master.po_no,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200) # Form errors, re-render
        self.assertContains(response, 'Gate Entry No. is required.')

    def test_gin_detail_table_partial_view(self):
        url = reverse('goods_inward_note_detail_table', kwargs={'pk': self.gin_master.pk}) + f"?{self.base_qs}"
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsinwardnote/_detail_table.html')
        self.assertContains(response, self.item_asset.item_code) # Item from detail 1
        self.assertContains(response, self.item_asset.manufacturer_description) # Item from detail 2

    def test_gin_detail_edit_form_partial_view_get(self):
        url = reverse('goods_inward_note_detail_edit', kwargs={'pk': self.gin_detail_1.pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsinwardnote/_detail_edit_form.html')
        self.assertIsInstance(response.context['form'], GoodsInwardNoteDetailForm)
        self.assertContains(response, 'Edit GIN Detail')
        self.assertContains(response, f'value="{self.gin_detail_1.quantity}"')
        self.assertContains(response, 'x-show="ahId === 33"') # Check conditional rendering
        self.assertContains(response, 'value="33"') # Check ah_id is passed

    def test_gin_detail_update_view_post_success(self):
        url = reverse('goods_inward_note_detail_update', kwargs={'pk': self.gin_detail_1.pk})
        data = {
            'quantity': '5.500', # Original was 5.000, this should pass if <= original. No, original was 5.000, new 5.500 fails
            'received_qty': '4.500', # Original was 4.000
            'asset_category': self.asset_category_asset.id,
            'asset_sub_category': self.asset_subcategory_asset_b.id,
            'original_challan_qty': '5.000', # Passed from hidden field
            'original_received_qty': '4.000', # Passed from hidden field
            'ah_id': '33', # Passed from hidden field
            # Master form fields needed for header update
            'gate_entry_no': self.gin_master.gate_entry_no,
            'entry_date': self.gin_master.entry_date.strftime('%Y-%m-%d'),
            'entry_time': self.gin_master.entry_time.strftime('%H:%M:%S'),
            'mode_of_transport': self.gin_master.mode_of_transport,
            'vehicle_no': self.gin_master.vehicle_no,
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Expected to fail quantity validation (5.5 > 5.0)

        # Corrected data to pass validation
        data['quantity'] = '4.500' # Make it less than or equal
        data['received_qty'] = '3.500' # Make it less than or equal
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Success
        
        self.gin_detail_1.refresh_from_db()
        self.assertEqual(self.gin_detail_1.quantity, Decimal('4.500'))
        self.assertEqual(self.gin_detail_1.received_qty, Decimal('3.500'))
        self.assertEqual(self.gin_detail_1.asset_category, self.asset_category_asset)
        self.assertEqual(self.gin_detail_1.asset_sub_category, self.asset_subcategory_asset_b)
        
        # Verify header was also updated (e.g. system_date/time, session_id)
        old_sys_date = self.gin_master.system_date
        self.gin_master.refresh_from_db()
        self.assertNotEqual(self.gin_master.system_date, old_sys_date) # Should be updated

    def test_gin_detail_update_view_post_invalid_qty(self):
        url = reverse('goods_inward_note_detail_update', kwargs={'pk': self.gin_detail_1.pk})
        data = {
            'quantity': '6.000', # Greater than original 5.000
            'received_qty': '4.000',
            'asset_category': self.asset_category_asset.id,
            'asset_sub_category': self.asset_subcategory_asset_a.id,
            'original_challan_qty': '5.000',
            'original_received_qty': '4.000',
            'ah_id': '33',
            'gate_entry_no': self.gin_master.gate_entry_no,
            'entry_date': self.gin_master.entry_date.strftime('%Y-%m-%d'),
            'entry_time': self.gin_master.entry_time.strftime('%H:%M:%S'),
            'mode_of_transport': self.gin_master.mode_of_transport,
            'vehicle_no': self.gin_master.vehicle_no,
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad request due to form validation error
        self.assertContains(response, 'Entered Challan Qty cannot exceed the original Challan Qty for this GIN item.')

    def test_gin_detail_update_view_post_invalid_category_required(self):
        url = reverse('goods_inward_note_detail_update', kwargs={'pk': self.gin_detail_1.pk})
        data = {
            'quantity': '4.000',
            'received_qty': '3.000',
            'asset_category': '', # Missing category for AHId=33
            'asset_sub_category': '', # Missing subcategory for AHId=33
            'original_challan_qty': '5.000',
            'original_received_qty': '4.000',
            'ah_id': '33',
            'gate_entry_no': self.gin_master.gate_entry_no,
            'entry_date': self.gin_master.entry_date.strftime('%Y-%m-%d'),
            'entry_time': self.gin_master.entry_time.strftime('%H:%M:%S'),
            'mode_of_transport': self.gin_master.mode_of_transport,
            'vehicle_no': self.gin_master.vehicle_no,
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Category is required for Asset type items.')
        self.assertContains(response, 'Sub-Category is required for Asset type items.')

    def test_get_subcategories_view(self):
        url = reverse('get_subcategories', kwargs={'category_id': self.asset_category_asset.id})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('<option value="101">SUB_ASSET_A</option>', response.content.decode())
        self.assertIn('<option value="102">SUB_ASSET_B</option>', response.content.decode())
        self.assertIn('<option value="0">Select</option>', response.content.decode())

    def test_download_file_view_image(self):
        url = reverse('download_file', kwargs={'item_id': self.item_regular.id, 'file_type': 'image'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/jpeg')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="img.jpg"')
        self.assertEqual(response.content, b'abc')

    def test_download_file_view_spec(self):
        url = reverse('download_file', kwargs={'item_id': self.item_asset.id, 'file_type': 'spec'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="spec.pdf"')
        self.assertEqual(response.content, b'xyz')

    def test_download_file_view_not_found(self):
        # Item with no image data
        url = reverse('download_file', kwargs={'item_id': self.item_asset.id, 'file_type': 'image'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, 'File not found.')

    def test_download_file_view_invalid_type(self):
        url = reverse('download_file', kwargs={'item_id': self.item_regular.id, 'file_type': 'invalid'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Invalid file type')
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

- Use HTMX for all dynamic updates and form submissions
- Use Alpine.js for client-side reactivity and modals
- Implement DataTables for all list views with sorting and filtering
- Make all interactions work without full page reloads
- Ensure proper HX-Trigger responses for list refreshes after CRUD operations

**Implementation Details:**

1.  **Main GIN Header Form:**
    *   The `GoodsInwardNoteForm` is rendered directly in `edit.html`.
    *   It uses `hx-post="{{ request.path }}" hx-swap="none"` for submission.
    *   Upon successful form submission, the `form_valid` method in `GoodsInwardNoteEditView` returns `HttpResponse(status=204, headers={'HX-Trigger': 'refreshGinDetailList'})` to trigger a refresh of the detail table and uses `messages.success` for user feedback. The master form itself doesn't swap content, but triggers a global refresh.

2.  **GIN Detail Table (DataTables):**
    *   The `ginDetailTableContainer` in `edit.html` is responsible for loading the table.
    *   `hx-trigger="load, refreshGinDetailList from:body"` ensures the table loads initially and refreshes whenever the `refreshGinDetailList` event is triggered (e.g., after a detail update or master header update).
    *   `hx-get="{% url 'goods_inward_note_detail_table' gin_master.pk %}..."` fetches the table content.
    *   `hx-swap="innerHTML"` replaces the content inside the container.
    *   `_detail_table.html` uses `$(document).ready(function() { $('#ginDetailTable').DataTable({...}); });` to initialize DataTables after HTMX loads the table.

3.  **GIN Detail Inline Editing (Modal with HTMX & Alpine.js):**
    *   Each row's "Edit" button in `_detail_table.html` uses `hx-get="{% url 'goods_inward_note_detail_edit' detail.id %}" hx-target="#global-modalContent" hx-swap="innerHTML"`.
    *   `_="on click add .is-active to #global-modal"` uses Alpine.js (via `_`) to show the modal when the edit button is clicked.
    *   The `_detail_edit_form.html` is loaded into the `global-modalContent`.
    *   The form inside `_detail_edit_form.html` uses `hx-post="{% url 'goods_inward_note_detail_update' detail_id %}" hx-swap="none"`.
    *   Upon successful detail form submission, `GoodsInwardNoteDetailUpdateView` returns `HttpResponse(status=204, headers={'HX-Trigger': 'refreshGinDetailList'})`.
    *   The `hx-on::after-request` on the form handles client-side actions: displaying a success message and dispatching a `closeModal` event to hide the modal.
    *   The "Cancel" button in `_detail_edit_form.html` directly dispatches the `closeModal` event.

4.  **Dynamic Category/Subcategory Dropdowns:**
    *   In `_detail_edit_form.html`, the `Category` `select` element uses `hx-get="{% url 'get_subcategories' 0 %}" hx-target="#id_asset_sub_category" hx-trigger="change"`.
    *   An `x-model="selectedCategory"` is used to keep track of the selected category.
    *   An `x-on:change="loadSubcategories()"` Alpine.js function manually triggers the HTMX request if necessary (though `hx-trigger` usually covers this). This is for robustness and to allow Alpine to manage the `selectedCategory` state.
    *   `GetSubCategoriesView` returns plain `<option>` HTML, which HTMX swaps directly into the `Sub-Category` select.

5.  **Conditional Visibility for Category/Subcategory:**
    *   `x-show="ahId === 33"` on the `div`s surrounding the category and subcategory fields in `_detail_edit_form.html` uses Alpine.js to show/hide them based on the `ahId` value passed from the view.

6.  **File Downloads:**
    *   Simple `<a>` tags with `href` pointing to `{% url 'download_file' ... %}` are used. These trigger standard browser downloads.

This comprehensive plan covers the modernization of the ASP.NET application to a modern Django solution using HTMX and Alpine.js, adhering to the specified architecture and best practices.