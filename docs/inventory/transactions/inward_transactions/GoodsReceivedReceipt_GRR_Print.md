## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with two primary data sources:
1.  **Supplier Data**: The `sql` web method queries `tblMM_Supplier_master` for supplier names and IDs.
2.  **Goods Received Receipt (GRR) Data**: The `loadData` method calls a stored procedure `Sp_GRR_Edit`. This procedure returns a dataset with columns such as `Id`, `FinYearId`, `FinYear`, `GRRNo`, `SysDate`, `GINId`, `GINNo`, `PONo`, `Supplier` (name), `SupId` (supplier ID), `ChNO`, `ChDT`. For Django to interact with this, the best practice is to create a SQL database VIEW that materializes the results of `Sp_GRR_Edit` (or the underlying query it represents) and then map the Django model to this view. Let's assume such a view is named `vw_GoodsReceivedReceipt_Print`.

**Inferred Database Structures:**

*   **Table: `tblMM_Supplier_master`**
    *   `SupplierId` (Primary Key, INT)
    *   `SupplierName` (NVARCHAR/VARCHAR)
    *   `CompId` (INT) - Company ID for multi-tenancy/context.

*   **View: `vw_GoodsReceivedReceipt_Print` (Based on `Sp_GRR_Edit` output)**
    *   `Id` (Primary Key, INT) - Unique ID for the GRR record.
    *   `FinYearId` (INT)
    *   `FinYear` (VARCHAR)
    *   `GRRNo` (VARCHAR)
    *   `SysDate` (DATE/DATETIME)
    *   `GINId` (INT)
    *   `GINNo` (VARCHAR)
    *   `PONo` (VARCHAR)
    *   `Supplier` (VARCHAR) - Supplier Name (denormalized from `tblMM_Supplier_master`).
    *   `SupId` (INT) - Supplier ID (denormalized from `tblMM_Supplier_master`).
    *   `ChNO` (VARCHAR) - Challan Number.
    *   `ChDT` (DATE/DATETIME) - Challan Date.
    *   **(Implicit)** `CompId` (INT) and `FinId` (INT) - These seem to be context variables from the ASP.NET session. We'll need to pass these as parameters to our Django query methods or ensure the view incorporates them if it's context-aware. For a `managed=False` model, these fields would need to exist in the view if filtered upon directly. We'll assume they exist for filtering.

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page primarily serves to display a paginated, searchable list of Goods Received Receipts and allows navigation to a detail page for a selected GRR.

*   **Read Operation**: Displaying a list of GRRs.
*   **Search/Filter**: Filtering the GRR list based on a supplier name. This search includes an autocomplete feature for the supplier input.
*   **Navigation**: Clicking a "Select" link on a GRR row redirects to a `GoodsReceivedReceipt_GRR_Print_Details.aspx` page, passing multiple GRR-related identifiers as query parameters.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET UI consists of:

*   **Header**: "Goods Received Receipt \[GRR] - Print".
*   **Search Section**:
    *   `txtSupplier` (TextBox): Text input for supplier search. This had an `AutoCompleteExtender` linked to a `WebMethod`.
    *   `btnSearch` (Button): Triggers the search.
*   **Data Display**:
    *   `GridView2` (GridView): Displays the GRR list. It supports pagination, custom columns (including hidden IDs), and a "Select" link for each row. It uses `CssClass="yui-datatable-theme"`.

**Django UI Mapping:**

*   The overall page will be a Django template (`inventory/goodsreceivedreceipt/list.html`).
*   Supplier search input will be a standard HTML `<input type="text">` enhanced with HTMX for autocomplete suggestions and to trigger the search.
*   The GRR list will be rendered by a separate HTMX-loaded partial template (`inventory/goodsreceivedreceipt/_table.html`) which will contain a `<table>` initialized with DataTables.
*   The "Select" action will be an HTMX click event that triggers a client-side redirect using `window.location.href` or similar, mimicking the ASP.NET `Response.Redirect`.
*   Styling will leverage Tailwind CSS.

### Step 4: Generate Django Code

We will create a new Django app named `inventory` to house these components.

#### 4.1 Models (`inventory/models.py`)

```python
from django.db import models
from django.urls import reverse_lazy

class Supplier(models.Model):
    """
    Maps to the existing tblMM_Supplier_master table.
    Used for supplier search and autocomplete.
    """
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId') # Company ID, for multi-tenancy

    class Meta:
        managed = False  # Tells Django not to manage this table (it exists already)
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

    @classmethod
    def search_suppliers(cls, prefix_text, company_id, limit=10):
        """
        Mimics the ASP.NET 'sql' web method for supplier autocomplete.
        Filters suppliers by name starting with the prefix and by company ID.
        """
        return cls.objects.filter(
            supplier_name__istartswith=prefix_text,
            comp_id=company_id
        ).order_by('supplier_name')[:limit]

class GoodsReceivedReceipt(models.Model):
    """
    Maps to a database view (e.g., vw_GoodsReceivedReceipt_Print)
    that materializes the data returned by the Sp_GRR_Edit stored procedure.
    This view should include the CompId and FinYearId columns for filtering.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # GRR primary key
    fin_year_id = models.IntegerField(db_column='FinYearId')
    fin_year = models.CharField(db_column='FinYear', max_length=10)
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    gin_id = models.IntegerField(db_column='GINId')
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    po_no = models.CharField(db_column='PONo', max_length=50)
    supplier_name = models.CharField(db_column='Supplier', max_length=255) # Supplier Name from SP result
    supplier_id = models.IntegerField(db_column='SupId') # Supplier ID from SP result
    challan_no = models.CharField(db_column='ChNO', max_length=50)
    challan_date = models.DateField(db_column='ChDT')
    # Assuming CompId and FinId are also part of this view for filtering purposes
    comp_id = models.IntegerField(db_column='CompId')
    
    class Meta:
        managed = False
        # IMPORTANT: This `db_table` should be a SQL VIEW that consolidates the data
        # returned by the Sp_GRR_Edit stored procedure.
        db_table = 'vw_GoodsReceivedReceipt_Print'
        verbose_name = 'Goods Received Receipt'
        verbose_name_plural = 'Goods Received Receipts'

    def __str__(self):
        return f"GRR No: {self.grr_no} ({self.supplier_name})"

    @classmethod
    def get_grrs_for_print(cls, company_id, financial_year_id, supplier_obj=None):
        """
        Retrieves GRR data, mimicking the Sp_GRR_Edit logic with filters.
        Assumes the underlying view includes CompId and FinYearId for filtering.
        """
        queryset = cls.objects.filter(
            comp_id=company_id,
            fin_year_id=financial_year_id
        )
        if supplier_obj:
            queryset = queryset.filter(supplier_id=supplier_obj.supplier_id)
        return queryset.order_by('-sys_date', '-grr_no') # Order by date and GRR no, similar to what a grid might do
    
    def get_details_url_params(self):
        """
        Prepares parameters for the detail page redirect, mimicking ASP.NET.
        In Django, this would typically be `reverse_lazy` to a named URL.
        """
        # The original ASP.NET URL was:
        # GoodsReceivedReceipt_GRR_Print_Details.aspx?Id={Id}&GRRNo={GrrNo}&GINNo={GinNo}&GINId={GinId}&PONo={poNo}&FyId={fyid}&Key={getRandomKey}&ModId=9&SubModId=38
        # We'll map to a Django URL pattern like 'inventory:grr_detail'
        # The 'Key', 'ModId', 'SubModId' are likely context/security parameters that would be handled
        # differently in Django (e.g., session, permissions middleware) or not needed.
        # We'll focus on the data-specific parameters.
        return {
            'Id': self.id,
            'GRRNo': self.grr_no,
            'GINNo': self.gin_no,
            'GINId': self.gin_id,
            'PONo': self.po_no,
            'FyId': self.fin_year_id,
            # Placeholder for Django's equivalent to getRandomKey, if needed for security:
            # 'Key': 'some_generated_key' 
            # Or omit if Django's URL security (e.g., signed URLs) is used.
        }

```

#### 4.2 Forms (`inventory/forms.py`)

For the supplier search, we don't strictly need a `ModelForm`. A simple `Form` for search criteria will suffice, or handle the query parameters directly in the view. Given it's a simple text input, direct handling in the view/template is cleaner with HTMX. However, for robustness and validation, a form can be beneficial.

```python
from django import forms
from .models import Supplier

class GRRSearchForm(forms.Form):
    """
    Form for filtering Goods Received Receipts by supplier.
    """
    supplier_name = forms.CharField(
        label="Supplier",
        required=False,
        max_length=255,
        widget=forms.TextInput(attrs={
            'id': 'txtSupplier', # Match original ID for clarity
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3',
            'placeholder': 'Start typing supplier name...',
            'autocomplete': 'off', # Disable browser autocomplete
            # HTMX attributes for live search
            'hx-get': "{% url 'inventory:supplier_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms, search",
            'hx-target': "#supplier-suggestions",
            'hx-swap': "innerHTML",
            'hx-indicator': "#supplier-loading-indicator",
            'name': 'supplier_name_input' # A distinct name for the raw input value
        })
    )
    # Hidden input to store the actual supplier ID once selected from autocomplete
    supplier_id = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput(attrs={
            'id': 'hiddenSupplierId' # To store the actual ID for lookup
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        # Custom cleaning/validation if needed
        return cleaned_data

```

#### 4.3 Views (`inventory/views.py`)

This section will define the views for listing GRRs, handling the supplier autocomplete, and rendering the GRR table partial.

```python
from django.views.generic import ListView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse
from django.shortcuts import render, redirect
from django.template.loader import render_to_string
from django.contrib import messages
import json # For JSON response for DataTables, if server-side processing is adopted later

from .models import GoodsReceivedReceipt, Supplier
from .forms import GRRSearchForm

# Assume session variables are available from request.session or a middleware
# For demonstration, we'll use placeholder values or assume they're set via middleware
def get_current_company_id(request):
    return request.session.get('compid', 1) # Default to 1 if not in session
def get_current_financial_year_id(request):
    return request.session.get('finyear', 1) # Default to 1 if not in session


class GoodsReceivedReceiptListView(ListView):
    """
    Main view for displaying the GRR search form and the container for the GRR list.
    """
    model = GoodsReceivedReceipt
    template_name = 'inventory/goodsreceivedreceipt/list.html'
    context_object_name = 'goods_received_receipts' # Not directly used in list view, but good practice

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['search_form'] = GRRSearchForm(self.request.GET or None)
        return context

    def get_queryset(self):
        # This view doesn't directly return the full queryset; the partial view does.
        # This is kept thin, as per best practices.
        return GoodsReceivedReceipt.objects.none() # Return empty queryset


class GoodsReceivedReceiptTablePartialView(ListView):
    """
    HTMX-driven view to render the GRR table partial.
    This will be loaded dynamically into the main list.html.
    """
    model = GoodsReceivedReceipt
    template_name = 'inventory/goodsreceivedreceipt/_table.html'
    context_object_name = 'goods_received_receipts'

    def get_queryset(self):
        company_id = get_current_company_id(self.request)
        financial_year_id = get_current_financial_year_id(self.request)
        
        # Get supplier ID from the request (from the hidden input or directly from the search input)
        supplier_id_from_form = self.request.GET.get('supplier_id')
        supplier_obj = None
        if supplier_id_from_form:
            try:
                supplier_obj = Supplier.objects.get(
                    supplier_id=int(supplier_id_from_form), 
                    comp_id=company_id # Ensure supplier belongs to current company
                )
            except Supplier.DoesNotExist:
                pass # Supplier not found, treat as no filter

        # Call the model method to get filtered GRRs
        return GoodsReceivedReceipt.get_grrs_for_print(
            company_id=company_id,
            financial_year_id=financial_year_id,
            supplier_obj=supplier_obj
        )

    def render_to_response(self, context, **response_kwargs):
        # DataTables is client-side, so just render the HTML table
        return super().render_to_response(context, **response_kwargs)


class SupplierAutocompleteView(View):
    """
    HTMX-driven view to provide autocomplete suggestions for suppliers.
    Mimics the ASP.NET 'sql' WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('supplier_name_input', '').strip()
        company_id = get_current_company_id(request)
        
        suggestions = []
        if prefix_text:
            suppliers = Supplier.search_suppliers(prefix_text, company_id)
            for supplier in suppliers:
                # Format: "SupplierName [SupplierId]" as in ASP.NET
                suggestions.append({
                    'id': supplier.supplier_id,
                    'name': f"{supplier.supplier_name} [{supplier.supplier_id}]"
                })
        
        # This can return a list of <option> elements or JSON,
        # depending on how the frontend handles suggestions.
        # For a simple list, HTML is often easier with HTMX.
        # For more complex scenarios (e.g., select2), JSON is better.
        
        # Let's return HTML for a simple dropdown list via HTMX
        # This will be swapped into #supplier-suggestions
        html = render_to_string('inventory/goodsreceivedreceipt/_supplier_suggestions.html', {'suggestions': suggestions})
        return HttpResponse(html)


class GRRDetailRedirectView(View):
    """
    Handles the 'Select' command to redirect to the GRR print details page.
    Mimics Response.Redirect in ASP.NET.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            # Retrieve the GRR object to get all necessary details for the redirect
            grr = GoodsReceivedReceipt.objects.get(id=pk)
            # Get the parameters for the detail URL
            params = grr.get_details_url_params()
            
            # Construct the query string
            query_string = "&".join([f"{key}={value}" for key, value in params.items()])
            
            # This URL would point to the *actual* GRR details page in your Django app.
            # Assuming a URL pattern like 'inventory:grr_print_details_base_url'
            # and then appending query parameters.
            # For this example, we'll construct a mock URL.
            # In a real scenario, this would be `reverse_lazy('inventory:grr_print_details', kwargs={'pk': pk})`
            # and the detail view would handle fetching its own data.
            # For exact ASP.NET mimicry of passing many GET params:
            
            # THIS IS A MOCK REDIRECT URL TO SHOW THE CONCEPT
            # In a real Django app, you would define a proper URL for the detail page
            # and the detail view would fetch its own data based on `pk`.
            # For direct migration, we'll try to replicate the ASP.NET behavior.
            
            detail_base_url = reverse('inventory:grr_print_details_page_placeholder') # Placeholder URL name
            redirect_url = f"{detail_base_url}?{query_string}"
            
            # In a true Django application, we would simply redirect to a view
            # that takes the GRR's primary key and fetches its own data securely.
            # E.g., return redirect('inventory:grr_detail', pk=pk)
            # The current approach is for mimicking the legacy system's direct parameter passing.
            return redirect(redirect_url)

        except GoodsReceivedReceipt.DoesNotExist:
            messages.error(request, "Goods Received Receipt not found.")
            return redirect('inventory:grr_list') # Redirect back to list if not found
        except Exception as e:
            messages.error(request, f"An error occurred: {e}")
            return redirect('inventory:grr_list')


```

#### 4.4 Templates (`inventory/templates/inventory/goodsreceivedreceipt/`)

##### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Received Receipt [GRR] - Print</h2>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search GRR</h3>
        <form id="searchForm" hx-get="{% url 'inventory:grr_table' %}" hx-target="#grrTable-container" hx-swap="innerHTML" hx-trigger="submit">
            <div class="mb-4">
                <label for="{{ search_form.supplier_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier:</label>
                {{ search_form.supplier_name }}
                <div id="supplier-loading-indicator" class="htmx-indicator mt-1 text-blue-500 text-sm">
                    Searching...
                </div>
                <div id="supplier-suggestions" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto mt-1"
                     x-data="{ showSuggestions: false }"
                     x-show="showSuggestions"
                     @click.away="showSuggestions = false">
                    <!-- Suggestions will be loaded here via HTMX -->
                </div>
                {{ search_form.supplier_id }} {# Hidden field for actual supplier ID #}
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md redbox">
                Search
            </button>
        </form>
    </div>
    
    <div id="grrTable-container"
         class="bg-white shadow-md rounded-lg p-6"
         hx-trigger="load, reloadGRRTable from:body, submit from:#searchForm"
         hx-get="{% url 'inventory:grr_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Goods Received Receipts...</p>
        </div>
    </div>
    
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for managing supplier suggestion dropdown visibility
        Alpine.data('supplierAutocomplete', () => ({
            init() {
                this.$watch('$el.children.length', (length) => {
                    this.showSuggestions = length > 0;
                });
            },
            selectSuggestion(name, id) {
                document.getElementById('txtSupplier').value = name;
                document.getElementById('hiddenSupplierId').value = id;
                this.showSuggestions = false;
                // Optional: Trigger search immediately after selection
                // htmx.trigger("#searchForm", "submit");
            }
        }));
    });

    // Function to re-initialize DataTables after HTMX swap
    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#grrTable')) {
            $('#grrTable').DataTable().destroy();
        }
        $('#grrTable').DataTable({
            "pageLength": 20,
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true
        });
    }

    // HTMX lifecycle event to re-initialize DataTables after content swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'grrTable-container') {
            initializeDataTable();
        }
    });

    // Initial load of DataTables (if table is pre-rendered or on first HTMX load)
    $(document).ready(function() {
        initializeDataTable();
    });
</script>
{% endblock %}
```

##### `_table.html` (Partial for HTMX)

```html
<table id="grrTable" class="min-w-full bg-white border border-gray-200 yui-datatable-theme">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GRR No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if goods_received_receipts %}
            {% for grr in goods_received_receipts %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ grr.grr_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ grr.sys_date|date:"d M Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ grr.gin_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ grr.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ grr.supplier_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ grr.challan_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ grr.challan_date|date:"d M Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'inventory:grr_detail_redirect' pk=grr.id %}"
                        hx-trigger="click"
                        hx-target="body" {# Target body to execute script for redirect #}
                        hx-swap="none" {# No swap, just execute script #}
                        _="on htmx:afterRequest if event.detail.xhr.status == 200 then window.location.href = event.detail.xhr.responseURL">
                        Select
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

```

##### `_supplier_suggestions.html` (Partial for Autocomplete)

```html
{% if suggestions %}
    <ul x-show="showSuggestions" x-cloak class="list-none p-0 m-0" @click="showSuggestions = false">
        {% for suggestion in suggestions %}
            <li class="px-4 py-2 hover:bg-blue-100 cursor-pointer border-b border-gray-200 last:border-b-0"
                @click="selectSuggestion('{{ suggestion.name|escapejs }}', {{ suggestion.id }})">
                {{ suggestion.name }}
            </li>
        {% endfor %}
    </ul>
{% else %}
    <div x-show="showSuggestions" x-cloak class="px-4 py-2 text-gray-500">No suggestions</div>
{% endif %}
```

#### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    GoodsReceivedReceiptListView,
    GoodsReceivedReceiptTablePartialView,
    SupplierAutocompleteView,
    GRRDetailRedirectView
)

app_name = 'inventory'

urlpatterns = [
    # Main GRR list page
    path('grr/print/', GoodsReceivedReceiptListView.as_view(), name='grr_list'),
    
    # HTMX endpoint for the GRR table partial (including search results)
    path('grr/print/table/', GoodsReceivedReceiptTablePartialView.as_view(), name='grr_table'),
    
    # HTMX endpoint for supplier autocomplete suggestions
    path('grr/print/supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # Endpoint to handle the 'Select' action and redirect to the detail page
    # This URL receives the primary key (pk) of the selected GRR
    # This view constructs and issues the redirect to the actual detail page (which would have its own URL pattern)
    path('grr/print/redirect/<int:pk>/', GRRDetailRedirectView.as_view(), name='grr_detail_redirect'),

    # PLACEHOLDER: This URL would be defined in your actual Django app's detail module
    # and would likely take a single PK and fetch its own data.
    # It's here to show the target of the redirect.
    path('grr/print/details/', lambda request: HttpResponse("This is the GRR Details Page. Parameters passed: " + str(request.GET)), name='grr_print_details_page_placeholder'),
]

```

#### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .models import GoodsReceivedReceipt, Supplier

# Mock for session data if needed by views/models
class MockSession:
    def get(self, key, default=None):
        if key == 'compid':
            return 123 # Example company ID
        if key == 'finyear':
            return 2023 # Example financial year ID
        return default

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = 123
        Supplier.objects.create(
            supplier_id=1,
            supplier_name='Supplier A',
            comp_id=cls.company_id
        )
        Supplier.objects.create(
            supplier_id=2,
            supplier_name='Another Supplier',
            comp_id=cls.company_id
        )
        Supplier.objects.create(
            supplier_id=3,
            supplier_name='Third Party Inc.',
            comp_id=cls.company_id + 1 # Different company
        )
  
    def test_supplier_creation(self):
        supplier = Supplier.objects.get(supplier_id=1)
        self.assertEqual(supplier.supplier_name, 'Supplier A')
        self.assertEqual(supplier.comp_id, self.company_id)
        
    def test_supplier_name_label(self):
        supplier = Supplier.objects.get(supplier_id=1)
        field_label = supplier._meta.get_field('supplier_name').verbose_name
        self.assertEqual(field_label, 'SupplierName') # Assuming db_column name is verbose_name

    def test_search_suppliers(self):
        # Test case-insensitive prefix search
        suppliers = Supplier.search_suppliers('supp', self.company_id)
        self.assertEqual(suppliers.count(), 2)
        self.assertIn(Supplier.objects.get(supplier_id=1), suppliers)
        self.assertIn(Supplier.objects.get(supplier_id=2), suppliers)

        suppliers = Supplier.search_suppliers('another', self.company_id)
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 2)

        # Test with company filter
        suppliers = Supplier.search_suppliers('supp', self.company_id + 1)
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 3)

        # Test no results
        suppliers = Supplier.search_suppliers('xyz', self.company_id)
        self.assertEqual(suppliers.count(), 0)

class GoodsReceivedReceiptModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 123
        cls.fin_year_id = 2023
        cls.supplier1 = Supplier.objects.create(supplier_id=100, supplier_name='Test Supplier 1', comp_id=cls.company_id)
        cls.supplier2 = Supplier.objects.create(supplier_id=101, supplier_name='Test Supplier 2', comp_id=cls.company_id)

        GoodsReceivedReceipt.objects.create(
            id=1, grr_no='GRR001', sys_date='2023-01-15', gin_id=1, gin_no='GIN001', po_no='PO001',
            supplier_id=cls.supplier1.supplier_id, supplier_name=cls.supplier1.supplier_name,
            challan_no='CH001', challan_date='2023-01-10', fin_year_id=cls.fin_year_id, comp_id=cls.company_id
        )
        GoodsReceivedReceipt.objects.create(
            id=2, grr_no='GRR002', sys_date='2023-02-20', gin_id=2, gin_no='GIN002', po_no='PO002',
            supplier_id=cls.supplier2.supplier_id, supplier_name=cls.supplier2.supplier_name,
            challan_no='CH002', challan_date='2023-02-18', fin_year_id=cls.fin_year_id, comp_id=cls.company_id
        )
        GoodsReceivedReceipt.objects.create(
            id=3, grr_no='GRR003', sys_date='2022-12-01', gin_id=3, gin_no='GIN003', po_no='PO003',
            supplier_id=cls.supplier1.supplier_id, supplier_name=cls.supplier1.supplier_name,
            challan_no='CH003', challan_date='2022-11-28', fin_year_id=cls.fin_year_id - 1, comp_id=cls.company_id
        )
  
    def test_grr_creation(self):
        grr = GoodsReceivedReceipt.objects.get(id=1)
        self.assertEqual(grr.grr_no, 'GRR001')
        self.assertEqual(grr.supplier_name, 'Test Supplier 1')
        
    def test_get_grrs_for_print_no_filter(self):
        grrs = GoodsReceivedReceipt.get_grrs_for_print(
            company_id=self.company_id, 
            financial_year_id=self.fin_year_id
        )
        self.assertEqual(grrs.count(), 2) # GRR001, GRR002
        
    def test_get_grrs_for_print_with_supplier_filter(self):
        grrs = GoodsReceivedReceipt.get_grrs_for_print(
            company_id=self.company_id, 
            financial_year_id=self.fin_year_id, 
            supplier_obj=self.supplier1
        )
        self.assertEqual(grrs.count(), 1) # GRR001
        self.assertEqual(grrs.first().grr_no, 'GRR001')

    def test_get_details_url_params(self):
        grr = GoodsReceivedReceipt.objects.get(id=1)
        params = grr.get_details_url_params()
        self.assertEqual(params['Id'], 1)
        self.assertEqual(params['GRRNo'], 'GRR001')
        self.assertIn('PONo', params)


class GRRViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.company_id = 123
        self.fin_year_id = 2023
        self.supplier1 = Supplier.objects.create(supplier_id=100, supplier_name='Test Supplier 1', comp_id=self.company_id)
        self.supplier2 = Supplier.objects.create(supplier_id=101, supplier_name='Test Supplier 2', comp_id=self.company_id)
        GoodsReceivedReceipt.objects.create(
            id=1, grr_no='GRR001', sys_date='2023-01-15', gin_id=1, gin_no='GIN001', po_no='PO001',
            supplier_id=self.supplier1.supplier_id, supplier_name=self.supplier1.supplier_name,
            challan_no='CH001', challan_date='2023-01-10', fin_year_id=self.fin_year_id, comp_id=self.company_id
        )
        GoodsReceivedReceipt.objects.create(
            id=2, grr_no='GRR002', sys_date='2023-02-20', gin_id=2, gin_no='GIN002', po_no='PO002',
            supplier_id=self.supplier2.supplier_id, supplier_name=self.supplier2.supplier_name,
            challan_no='CH002', challan_date='2023-02-18', fin_year_id=self.fin_year_id, comp_id=self.company_id
        )
        
        # Mock session for the views
        self.client.session['compid'] = self.company_id
        self.client.session['finyear'] = self.fin_year_id

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:grr_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceipt/list.html')
        self.assertIn('search_form', response.context)
    
    def test_table_partial_view_no_filter(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:grr_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceipt/_table.html')
        self.assertIn('goods_received_receipts', response.context)
        self.assertEqual(response.context['goods_received_receipts'].count(), 2)
        self.assertContains(response, 'GRR001')
        self.assertContains(response, 'GRR002')

    def test_table_partial_view_with_supplier_filter(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Pass supplier_id as a query parameter
        response = self.client.get(
            reverse('inventory:grr_table'), 
            {'supplier_id': self.supplier1.supplier_id}, 
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceipt/_table.html')
        self.assertEqual(response.context['goods_received_receipts'].count(), 1)
        self.assertContains(response, 'GRR001')
        self.assertNotContains(response, 'GRR002')

    def test_supplier_autocomplete_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('inventory:supplier_autocomplete'), 
            {'supplier_name_input': 'Test'}, 
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceipt/_supplier_suggestions.html')
        self.assertContains(response, 'Test Supplier 1 [100]')
        self.assertContains(response, 'Test Supplier 2 [101]')

    def test_grr_detail_redirect_view(self):
        grr_id = GoodsReceivedReceipt.objects.get(grr_no='GRR001').id
        response = self.client.get(reverse('inventory:grr_detail_redirect', args=[grr_id]))
        self.assertEqual(response.status_code, 302) # Expect a redirect
        # Check the redirected URL's query parameters
        expected_url_params = f"Id={grr_id}&GRRNo=GRR001&GINNo=GIN001&GINId=1&PONo=PO001&FyId={self.fin_year_id}"
        self.assertIn(expected_url_params, response.url)

    def test_grr_detail_redirect_view_not_found(self):
        response = self.client.get(reverse('inventory:grr_detail_redirect', args=[999])) # Non-existent ID
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('inventory:grr_list'))
        # Check for error message (requires Django messages framework setup in test client)
        # messages = list(get_messages(response.wsgi_request))
        # self.assertEqual(len(messages), 1)
        # self.assertEqual(str(messages[0]), "Goods Received Receipt not found.")

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Implementation:**

*   **HTMX for dynamic updates:**
    *   The `grrTable-container` `div` uses `hx-get` to `{% url 'inventory:grr_table' %}` and `hx-trigger="load, reloadGRRTable from:body, submit from:#searchForm"`. This ensures the table loads on page load, refreshes when `reloadGRRTable` custom event is triggered (e.g., after an edit/delete on a different page, not implemented here but for future CRUD), and reloads when the search form is submitted.
    *   The search form itself has `hx-get` to the same `grr_table` URL and `hx-target` to the container, `hx-swap="innerHTML"`.
    *   The supplier input (`txtSupplier`) uses `hx-get` to `{% url 'inventory:supplier_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms, search"` for live suggestions.
    *   "Select" button in the table uses `hx-get` to `{% url 'inventory:grr_detail_redirect' pk=grr.id %}`. It targets `body` and `hx-swap="none"`. The `_` (Alpine.js) directive `on htmx:afterRequest if event.detail.xhr.status == 200 then window.location.href = event.detail.xhr.responseURL` is used to programmatically redirect the browser after the HTMX request for the redirect is processed. This correctly mimics the server-side `Response.Redirect`.
*   **Alpine.js for UI state management:**
    *   Used in `_supplier_suggestions.html` to control the visibility of the suggestion list (`x-show="showSuggestions"`).
    *   Handles hiding the suggestions when clicking outside (`@click.away`).
    *   `selectSuggestion` function handles populating the text input and hidden ID field.
*   **DataTables for list views:**
    *   The `_table.html` partial contains the `<table>` structure.
    *   A JavaScript function `initializeDataTable()` is created to apply DataTables to the `grrTable`.
    *   An HTMX event listener `document.body.addEventListener('htmx:afterSwap', ...)` is used to call `initializeDataTable()` *after* the `grrTable-container` is swapped with new content, ensuring DataTables re-initializes correctly for dynamically loaded content.
    *   The base template (`core/base.html`) is assumed to include jQuery and the DataTables CDN links.

### Final Notes

*   **Placeholders:** `[APP_NAME]` is `inventory`, `[MODEL_NAME]` is `GoodsReceivedReceipt` or `Supplier`, etc. These have been replaced with concrete names.
*   **DRY Templates:** `list.html` extends `core/base.html` and includes partials (`_table.html`, `_supplier_suggestions.html`) for reusable components, adhering to the DRY principle.
*   **Fat Model, Thin View:** Business logic for fetching and filtering data (`get_grrs_for_print`, `search_suppliers`, `get_details_url_params`) is encapsulated within the model classes, keeping the Django views concise and focused on rendering and response.
*   **Comprehensive Tests:** Unit tests cover model methods and properties, while integration tests cover view functionality, including HTMX interactions and redirects.
*   **DB View for SP**: The conversion assumes that the `Sp_GRR_Edit` stored procedure's output can be represented by a database view (`vw_GoodsReceivedReceipt_Print`) to allow Django's ORM (with `managed=False`) to interact with it. If the SP performs complex, stateful operations or depends on input parameters in ways a simple view cannot, a custom Django manager using `raw()` SQL queries would be needed. However, for a read-only "print" view, a database view is the standard modern approach.
*   **Session Context**: The ASP.NET session variables (`compid`, `finyear`) are accessed in Django through `request.session`, providing dynamic context for data filtering.