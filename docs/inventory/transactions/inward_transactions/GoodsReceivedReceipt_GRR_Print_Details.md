## ASP.NET to Django Conversion Script: Goods Received Receipt Print Details

This document outlines a strategic plan to modernize a legacy ASP.NET application's "Goods Received Receipt (GRR) Print Details" functionality into a robust, scalable, and maintainable Django-based solution. Our approach prioritizes automation, leverages modern web technologies, and ensures a seamless transition with minimal manual effort.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in generating the Goods Received Receipt.

**Instructions:**
Based on the ASP.NET code-behind, the functionality aggregates data from multiple interconnected tables to generate the GRR report. This page doesn't perform CRUD on a single entity but reads data to present a comprehensive view. We will infer the primary tables and their key relationships for this report generation.

**Inferred Tables & Columns:**

-   **`tblinv_MaterialReceived_Master`**: Represents the main Goods Received Receipt.
    -   `Id` (Primary Key, `MasterId` in C#)
    -   `CompId` (Company ID)
    -   `GRRNo` (Goods Received Receipt Number)
-   **`tblinv_MaterialReceived_Details`**: Details/line items of the GRR.
    -   `Id` (Primary Key)
    -   `GRRNo` (Foreign Key to `tblinv_MaterialReceived_Master`)
    -   `MId` (Foreign Key to `tblinv_MaterialReceived_Master.Id`)
    -   `POId` (Foreign Key to PO details)
    -   `ReceivedQty` (Quantity received in GRR, `RecedQty` in C#)
-   **`tblInv_Inward_Master`**: Represents Inward transactions (Goods Inward Note - GIN).
    -   `Id` (Primary Key, `GINId` in C#)
    -   `CompId` (Company ID)
    -   `GINNo` (Goods Inward Note Number)
    -   `ChallanNo` (Challan Number)
    -   `ChallanDate` (Challan Date)
-   **`tblInv_Inward_Details`**: Details/line items of the GIN.
    -   `GINNo` (Foreign Key to `tblInv_Inward_Master`)
    -   `GINId` (Foreign Key to `tblInv_Inward_Master.Id`)
    -   `POId` (Foreign Key to PO details)
    -   `ReceivedQty` (Quantity received in GIN, `InvQty` in C#)
-   **`tblMM_PO_Master`**: Purchase Order Master.
    -   `Id` (Primary Key, `MId` in C#)
    -   `CompId` (Company ID)
    -   `PONo` (Purchase Order Number)
    -   `SupplierId` (Foreign Key to Supplier Master)
    -   `PRSPRFlag` (Flag for PR/SPR based PO, "0" for PR, "1" for SPR)
-   **`tblMM_PO_Details`**: Purchase Order Details.
    -   `Id` (Primary Key, `POId` in C#)
    -   `PONo` (Foreign Key to `tblMM_PO_Master`)
    -   `MId` (Foreign Key to `tblMM_PO_Master.Id`)
    -   `PRNo` (Purchase Requisition Number)
    -   `PRId` (Foreign Key to PR details)
    -   `SPRNo` (Service Purchase Requisition Number)
    -   `SPRId` (Foreign Key to SPR details)
    -   `Qty` (Quantity ordered in PO, `POQty` in C#)
-   **`tblMM_PR_Master`**: Purchase Requisition Master.
    -   `Id` (Primary Key, `MId` in C#)
    -   `PRNo` (Purchase Requisition Number)
    -   `CompId` (Company ID)
-   **`tblMM_PR_Details`**: Purchase Requisition Details.
    -   `Id` (Primary Key, `PRId` in C#)
    -   `PRNo` (Foreign Key to `tblMM_PR_Master`)
    -   `MId` (Foreign Key to `tblMM_PR_Master.Id`)
    -   `ItemId` (Foreign Key to Item Master)
-   **`tblMM_SPR_Master`**: Service Purchase Requisition Master.
    -   `Id` (Primary Key, `MId` in C#)
    -   `SPRNo` (Service Purchase Requisition Number)
    -   `CompId` (Company ID)
-   **`tblMM_SPR_Details`**: Service Purchase Requisition Details.
    -   `Id` (Primary Key, `SPRId` in C#)
    -   `SPRNo` (Foreign Key to `tblMM_SPR_Master`)
    -   `MId` (Foreign Key to `tblMM_SPR_Master.Id`)
    -   `ItemId` (Foreign Key to Item Master)
-   **`tblDG_Item_Master`**: Item Master Data.
    -   `Id` (Primary Key, `ItemId` in C#)
    -   `CompId` (Company ID)
    -   `ItemCode` (Item Code)
    -   `ManfDesc` (Manufacturer Description)
    -   `UOMBasic` (Unit of Measure Basic, Foreign Key to Unit Master)
-   **`Unit_Master`**: Unit of Measure Master.
    -   `Id` (Primary Key)
    -   `Symbol` (Unit Symbol)
-   **`tblMM_Supplier_master`**: Supplier Master.
    -   `SupplierId` (Primary Key)
    -   `CompId` (Company ID)
    -   `SupplierName` (Supplier Name)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET page `GoodsReceivedReceipt_GRR_Print_Details.aspx` is exclusively a **Read/Display** operation. Its purpose is to gather all relevant data for a specific Goods Received Receipt and present it in a formatted report. There are no explicit Create, Update, or Delete operations on the GRR or its underlying data from this page.

-   **Create:** Not present on this page.
-   **Read:** This is the core functionality. The page reads data from numerous interconnected tables (`tblinv_MaterialReceived_Master`, `tblinv_MaterialReceived_Details`, `tblInv_Inward_Master`, `tblInv_Inward_Details`, `tblMM_PO_Master`, `tblMM_PO_Details`, `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_SPR_Master`, `tblMM_SPR_Details`, `tblDG_Item_Master`, `Unit_Master`, `tblMM_Supplier_master`) based on query string parameters (`GRRNo`, `GINNo`, `Id`, `GINId`, `CompId`).
-   **Update:** Not present on this page.
-   **Delete:** Not present on this page.

The `btnCancel_Click` merely redirects the user, which is a navigational action, not a data manipulation one.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern Django/HTMX/Alpine.js patterns.

**Instructions:**
The original ASP.NET page uses `CrystalReportViewer` to embed a pre-generated report. In Django, this will be replaced by a standard HTML template that dynamically renders the data fetched from the backend.

-   **`CR:CrystalReportViewer`**: This proprietary component will be replaced by a standard Django HTML template. The complex data aggregation logic will be moved to a custom method on the `GoodsReceivedReceipt` model, which then passes the structured data to the template.
-   **`asp:Panel`**: A simple container. In Django, this will be a `<div>` or other HTML semantic tag.
-   **`asp:Button ID="btnCancel"`**: A simple form button that triggers a server-side redirect. In Django, this will be an `<a>` tag or a `hx-get` button to navigate back to the GRR list page.

**UI Elements for Django:**

-   **Report Header:** Display GRR No, GIN No, Supplier Name, Challan No, Challan Date, Company Address. These will be plain HTML elements (`<p>`, `<span>`, `<div>`).
-   **Report Line Items:** A table to display item details (Item Code, Description, UOM, PO Qty, Inv Qty, Reced Qty). This table will leverage DataTables for client-side interactivity as per guidelines, even though it's a print view (assuming a user might want to interact with the details before printing).
-   **Cancel Button:** A simple link/button to navigate back.

### Step 4: Generate Django Code

We will structure the Django application, named `inventory`, to handle the Goods Received Receipt functionality. Since this page primarily focuses on *displaying* a GRR, we will primarily define models for the involved entities and a `DetailView` for the GRR.

#### 4.1 Models

**Task:** Create Django models representing the identified database tables. These models will be `managed=False` as they map to existing tables. The complex data retrieval logic for the GRR report will be implemented as a method within the `GoodsReceivedReceipt` model (or a dedicated manager).

**Instructions:**
We'll define core models involved in the GRR printing. For brevity, we'll focus on the main ones directly related to the GRR and items, and a helper method to encapsulate the complex report data retrieval. We assume `tblDG_Item_Master`, `Unit_Master`, and `tblMM_Supplier_master` also exist as models.

```python
# inventory/models.py
from django.db import models

class CompanyMaster(models.Model):
    # This model represents the company details, needed for address
    id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255)
    address = models.CharField(db_column='Address', max_length=1000) # Assuming an address field exists

    class Meta:
        managed = False
        db_table = 'Company_Master' # Assuming this table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='items')
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    # Simulates fun.GetItemCode_PartNo, assuming ItemCode is already formatted
    def get_formatted_item_code(self):
        return self.item_code

class SupplierMaster(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='suppliers')
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

# Core models for GRR and related documents
class MaterialReceivedMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # MId in C# for GRR Master
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='material_received_masters')
    grr_no = models.CharField(db_column='GRRNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received Master'
        verbose_name_plural = 'Material Received Masters'

    def __str__(self):
        return self.grr_no

class MaterialReceivedDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50) # Assuming this is the direct foreign key from master, or use MId
    master = models.ForeignKey(MaterialReceivedMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details') # MId
    po_id = models.IntegerField(db_column='POId') # References tblMM_PO_Details.Id
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3) # RecedQty

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'
        verbose_name = 'Material Received Detail'
        verbose_name_plural = 'Material Received Details'

    def __str__(self):
        return f"Detail for GRR {self.grr_no} - POId {self.po_id}"


class InwardMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # GINId in C#
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='inward_masters')
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    challan_no = models.CharField(db_column='ChallanNo', max_length=50)
    challan_date = models.DateField(db_column='ChallanDate')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Master'
        verbose_name_plural = 'Inward Masters'

    def __str__(self):
        return self.gin_no

class InwardDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Not explicitly used, but good to have
    gin_no = models.CharField(db_column='GINNo', max_length=50) # Assuming string FK
    master = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', related_name='details')
    po_id = models.IntegerField(db_column='POId') # References tblMM_PO_Details.Id
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3) # InvQty

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Inward Detail'
        verbose_name_plural = 'Inward Details'

    def __str__(self):
        return f"Detail for GIN {self.gin_no} - POId {self.po_id}"

class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # MId in C# for PO Master
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='po_masters')
    po_no = models.CharField(db_column='PONo', max_length=50)
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='purchase_orders')
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1) # "0" for PR, "1" for SPR
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

    def __str__(self):
        return self.po_no

class PODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # POId in C#
    po_no = models.CharField(db_column='PONo', max_length=50) # Assuming string FK
    master = models.ForeignKey(POMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pr_no = models.CharField(db_column='PRNo', max_length=50, null=True, blank=True)
    pr_id = models.IntegerField(db_column='PRId', null=True, blank=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, null=True, blank=True)
    spr_id = models.IntegerField(db_column='SPRId', null=True, blank=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # POQty

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Detail {self.id} for PO {self.po_no}"

class PRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='pr_masters')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no

class PRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50) # Assuming string FK
    master = models.ForeignKey(PRMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='pr_details')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR Detail {self.id} for PR {self.pr_no}"

class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='spr_masters')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50) # Assuming string FK
    master = models.ForeignKey(SPRMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='spr_details')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"SPR Detail {self.id} for SPR {self.spr_no}"

# This is the "Fat Model" for the GRR, handling data aggregation.
# We'll use MaterialReceivedMaster as the primary entry point for GRR details.
class GoodsReceivedReceiptManager(models.Manager):
    def get_grr_report_data(self, comp_id, grr_master_id, gin_id):
        # This method replicates the complex C# data fetching logic
        # It aggregates data from multiple related tables
        
        grr_master = self.get(id=grr_master_id, comp_id=comp_id)
        inward_master = InwardMaster.objects.get(id=gin_id, comp_id=comp_id)

        report_details = []

        # Iterate through GRR details to find corresponding PO, GIN, Item details
        for grr_detail in grr_master.details.filter(master__comp_id=comp_id):
            try:
                # Get PO details based on POId from GRR details
                po_detail = PODetail.objects.select_related('master__supplier').get(
                    id=grr_detail.po_id,
                    master__comp_id=comp_id
                )
                po_master = po_detail.master

                # Get Inward Quantity for this POId from GIN details
                inward_detail = InwardDetail.objects.filter(
                    master=inward_master,
                    po_id=grr_detail.po_id
                ).first()
                
                if not inward_detail:
                    continue # Skip if no matching inward detail found

                item = None
                if po_master.pr_spr_flag == '0': # PR based PO
                    pr_detail = PRDetail.objects.select_related('item__uom_basic').filter(
                        master__pr_no=po_detail.pr_no, master__comp_id=comp_id, id=po_detail.pr_id
                    ).first()
                    if pr_detail:
                        item = pr_detail.item
                elif po_master.pr_spr_flag == '1': # SPR based PO
                    spr_detail = SPRDetail.objects.select_related('item__uom_basic').filter(
                        master__spr_no=po_detail.spr_no, master__comp_id=comp_id, id=po_detail.spr_id
                    ).first()
                    if spr_detail:
                        item = spr_detail.item
                
                if item:
                    report_details.append({
                        'id': grr_master.id, # MasterId
                        'item_code': item.get_formatted_item_code(), # fun.GetItemCode_PartNo
                        'description': item.manf_desc,
                        'uom': item.uom_basic.symbol,
                        'po_qty': po_detail.qty, # From tblMM_PO_Details
                        'inv_qty': inward_detail.received_qty, # From tblInv_Inward_Details (ReceivedQty)
                        'reced_qty': grr_detail.received_qty, # From tblinv_MaterialReceived_Details (ReceivedQty)
                        'comp_id': comp_id,
                        'po_id': grr_detail.po_id,
                    })
            except (PODetail.DoesNotExist, InwardDetail.DoesNotExist, PRDetail.DoesNotExist, SPRDetail.DoesNotExist, ItemMaster.DoesNotExist) as e:
                # Log or handle cases where related data is missing
                print(f"Data inconsistency found for GRR detail POId {grr_detail.po_id}: {e}")
                continue

        # Prepare header data
        supplier_name = po_master.supplier.supplier_name if po_master else "N/A"
        company_address = grr_master.comp_id.address # fun.CompAdd(CompId)

        return {
            'grr_no': grr_master.grr_no,
            'gin_no': inward_master.gin_no,
            'challan_no': inward_master.challan_no,
            'challan_date': inward_master.challan_date,
            'supplier_name': supplier_name,
            'company_address': company_address,
            'details': report_details
        }

class GoodsReceivedReceipt(MaterialReceivedMaster):
    """
    A proxy model or a combined view model to encapsulate GRR report logic.
    Inherits from MaterialReceivedMaster to easily use its ID.
    """
    objects = GoodsReceivedReceiptManager()

    class Meta:
        proxy = True # This means no new table will be created
        verbose_name = 'Goods Received Receipt'
        verbose_name_plural = 'Goods Received Receipts'
```

#### 4.2 Forms

**Task:** Since this page is a report viewer, it doesn't involve form submissions for CRUD operations. Therefore, no `ModelForm` is required for this specific page.

```python
# inventory/forms.py
# No forms needed for GRR_Print_Details view as it's a display/report page.
# If you were to add GRR creation/update functionality elsewhere,
# forms for MaterialReceivedMaster and MaterialReceivedDetail would be here.
```

#### 4.3 Views

**Task:** Implement a Django Class-Based View (CBV) to display the GRR details. This will be a `TemplateView` or a custom `DetailView` which fetches the aggregated report data using the "fat model" method.

**Instructions:**
The view will be thin, primarily orchestrating the data retrieval from the model and passing it to the template.

```python
# inventory/views.py
from django.views.generic import TemplateView
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.http import HttpResponse
from django.contrib import messages
from .models import GoodsReceivedReceipt, MaterialReceivedMaster, InwardMaster # Import necessary models

class GoodsReceivedReceiptDetailView(TemplateView):
    template_name = 'inventory/goodsreceivedreceipt/detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve parameters from URL (analogous to Request.QueryString)
        grr_master_id = self.kwargs.get('grr_master_id') # MId
        gin_id = self.kwargs.get('gin_id')

        # Assuming CompId comes from the user's session or profile,
        # for demonstration, let's hardcode or fetch from logged-in user's company
        # comp_id = self.request.session.get('compid') # Example: from session
        comp_id = 1 # Placeholder for demonstration

        try:
            # Use the fat model method to get all report data
            report_data = GoodsReceivedReceipt.objects.get_grr_report_data(
                comp_id=comp_id,
                grr_master_id=grr_master_id,
                gin_id=gin_id
            )
            context['report'] = report_data
            context['grr_master_id'] = grr_master_id # For the Cancel button
            context['gin_id'] = gin_id # For the Cancel button
            messages.success(self.request, "Goods Received Receipt data loaded successfully.")
        except (MaterialReceivedMaster.DoesNotExist, InwardMaster.DoesNotExist) as e:
            messages.error(self.request, f"Error loading GRR data: {e}. Please check the IDs.")
            context['report'] = None # Ensure report is None on error
        except Exception as e:
            messages.error(self.request, f"An unexpected error occurred: {e}")
            context['report'] = None

        return context

# Dummy/Placeholder views to satisfy the template structure for general CRUD examples
# These are NOT directly used by the GRR Print Details page, but provided for completeness
# if a full GRR management module were to be implemented.
class GoodsReceivedReceiptListView(TemplateView): # Not a real ListView for GRR_Print, but a placeholder
    template_name = 'inventory/goodsreceivedreceipt/list.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This would typically fetch a list of GRRs. For now, it's just a placeholder.
        context['goodsreceivedreceipts'] = MaterialReceivedMaster.objects.all()[:10] # Example
        return context

# No CreateView, UpdateView, DeleteView for this *specific print page*,
# but they would follow the pattern below for a full GRR management.
# class GoodsReceivedReceiptCreateView(CreateView): ...
# class GoodsReceivedReceiptUpdateView(UpdateView): ...
# class GoodsReceivedReceiptDeleteView(DeleteView): ...
```

#### 4.4 Templates

**Task:** Create an HTML template to render the GRR report data. The template will extend `core/base.html` and use DataTables for the line items section, and simple HTML for header details.

**Instructions:**
We'll create `detail.html` for the report display and a partial for the table, as DataTables requires the table to be present on DOM load.

```html
<!-- inventory/templates/inventory/goodsreceivedreceipt/detail.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Goods Received Receipt [GRR] - Print</h2>
        <a href="{% url 'goodsreceivedreceipt_list' %}" 
           class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    {% if report %}
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h3 class="text-xl font-semibold mb-4">GRR Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700 text-sm">
                <div><strong>GRR No:</strong> {{ report.grr_no }}</div>
                <div><strong>GIN No:</strong> {{ report.gin_no }}</div>
                <div><strong>Supplier Name:</strong> {{ report.supplier_name }}</div>
                <div><strong>Challan No:</strong> {{ report.challan_no }}</div>
                <div><strong>Challan Date:</strong> {{ report.challan_date|date:"d/m/Y" }}</div>
                <div class="md:col-span-2"><strong>Company Address:</strong> {{ report.company_address|linebreaksbr }}</div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-semibold mb-4">Received Items</h3>
            <div id="grr-details-table-container">
                <!-- HTMX will load the actual table here -->
                <div hx-get="{% url 'goodsreceivedreceipt_details_table' grr_master_id=grr_master_id gin_id=gin_id %}"
                     hx-trigger="load"
                     hx-swap="outerHTML">
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading item details...</p>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error:</strong>
            <span class="block sm:inline">Could not load Goods Received Receipt details. Please check the provided GRR and GIN IDs.</span>
        </div>
    {% endif %}

    <!-- This modal structure is for general CRUD forms, not strictly for this print view. -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for general UI state
    });
</script>
{% endblock %}
```

```html
<!-- inventory/templates/inventory/goodsreceivedreceipt/_detail_table.html -->
<!-- This is a partial template loaded via HTMX for the item details table -->
<table id="goodsreceivedreceiptDetailTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inv Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received Qty</th>
        </tr>
    </thead>
    <tbody>
        {% for item in report.details %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.uom }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.po_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.inv_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.reced_qty|floatformat:3 }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-2 px-4 border-b border-gray-200 text-center text-gray-500">No items found for this GRR.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#goodsreceivedreceiptDetailTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "ordering": false, // Usually for print views, ordering might not be desired
        "paging": false,
        "info": false,
        "searching": false
    });
});
</script>
```
To support the HTMX loaded table, we need a separate view:
```python
# inventory/views.py (add to existing views.py)
from django.views.generic import ListView # or TemplateView

class GoodsReceivedReceiptDetailTablePartialView(ListView):
    """
    A partial view to render only the DataTables HTML for GRR details.
    This is fetched via HTMX.
    """
    template_name = 'inventory/goodsreceivedreceipt/_detail_table.html'
    context_object_name = 'report' # Naming consistent with the full detail view

    def get_queryset(self):
        # This view doesn't directly query a queryset, but calls the model method
        return None # Overriding get_queryset as we use a custom method

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        grr_master_id = self.kwargs.get('grr_master_id') # MId
        gin_id = self.kwargs.get('gin_id')
        comp_id = 1 # Placeholder, should derive from user session/profile

        try:
            report_data = GoodsReceivedReceipt.objects.get_grr_report_data(
                comp_id=comp_id,
                grr_master_id=grr_master_id,
                gin_id=gin_id
            )
            context['report'] = report_data
        except (MaterialReceivedMaster.DoesNotExist, InwardMaster.DoesNotExist) as e:
            messages.error(self.request, f"Error fetching report details for table: {e}")
            context['report'] = {'details': []} # Return empty details on error
        except Exception as e:
            messages.error(self.request, f"An unexpected error occurred while loading table: {e}")
            context['report'] = {'details': []}
            
        return context
```

#### 4.5 URLs

**Task:** Define URL patterns for the GRR detail view and the partial table view.

**Instructions:**
We'll define a specific URL for the GRR print view, taking the necessary IDs as parameters.

```python
# inventory/urls.py
from django.urls import path
from .views import GoodsReceivedReceiptDetailView, GoodsReceivedReceiptListView, GoodsReceivedReceiptDetailTablePartialView

urlpatterns = [
    # Main GRR List (placeholder, to mimic navigation from ASP.NET cancel button)
    path('goodsreceivedreceipt/', GoodsReceivedReceiptListView.as_view(), name='goodsreceivedreceipt_list'),

    # GRR Print/Detail View (main functionality for this conversion)
    path(
        'goodsreceivedreceipt/print/<int:grr_master_id>/<int:gin_id>/',
        GoodsReceivedReceiptDetailView.as_view(),
        name='goodsreceivedreceipt_detail_print'
    ),
    
    # HTMX endpoint for the details table
    path(
        'goodsreceivedreceipt/print/table/<int:grr_master_id>/<int:gin_id>/',
        GoodsReceivedReceiptDetailTablePartialView.as_view(),
        name='goodsreceivedreceipt_details_table'
    ),

    # Placeholder paths for general CRUD operations on a GRR entry if needed later
    # path('goodsreceivedreceipt/add/', GoodsReceivedReceiptCreateView.as_view(), name='goodsreceivedreceipt_add'),
    # path('goodsreceivedreceipt/edit/<int:pk>/', GoodsReceivedReceiptUpdateView.as_view(), name='goodsreceivedreceipt_edit'),
    # path('goodsreceivedreceipt/delete/<int:pk>/', GoodsReceivedReceiptDeleteView.as_view(), name='goodsreceivedreceipt_delete'),
]
```

#### 4.6 Tests

**Task:** Write tests for the models and views to ensure data integrity and correct rendering.

**Instructions:**
We will include comprehensive unit tests for the `GoodsReceivedReceiptManager`'s `get_grr_report_data` method and integration tests for the `GoodsReceivedReceiptDetailView` and its partial table rendering.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    CompanyMaster, UnitMaster, ItemMaster, SupplierMaster,
    MaterialReceivedMaster, MaterialReceivedDetail,
    InwardMaster, InwardDetail,
    POMaster, PODetail,
    PRMaster, PRDetail,
    SPRMaster, SPRDetail,
    GoodsReceivedReceipt
)
import datetime

class GoodsReceivedReceiptModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for the complex report generation
        cls.comp = CompanyMaster.objects.create(id=1, company_name='TestCo', address='123 Test St')
        cls.unit = UnitMaster.objects.create(id=1, symbol='Kg')
        cls.item = ItemMaster.objects.create(id=1, comp_id=cls.comp, item_code='ITEM001', manf_desc='Test Item Desc', uom_basic=cls.unit)
        cls.supplier = SupplierMaster.objects.create(supplier_id=1, comp_id=cls.comp, supplier_name='Test Supplier')

        cls.grr_master = MaterialReceivedMaster.objects.create(id=101, comp_id=cls.comp, grr_no='GRR001')
        cls.inward_master = InwardMaster.objects.create(id=201, comp_id=cls.comp, gin_no='GIN001', challan_no='CHALLAN001', challan_date='2023-01-15')
        cls.po_master_pr = POMaster.objects.create(id=301, comp_id=cls.comp, po_no='PO001', supplier=cls.supplier, pr_spr_flag='0', fin_year_id=2023)
        cls.pr_master = PRMaster.objects.create(id=401, pr_no='PR001', comp_id=cls.comp)

        cls.pr_detail = PRDetail.objects.create(id=501, pr_no='PR001', master=cls.pr_master, item=cls.item)
        cls.po_detail_pr = PODetail.objects.create(id=601, po_no='PO001', master=cls.po_master_pr, pr_no='PR001', pr_id=cls.pr_detail.id, qty=100.000)

        cls.inward_detail = InwardDetail.objects.create(id=701, gin_no='GIN001', master=cls.inward_master, po_id=cls.po_detail_pr.id, received_qty=95.000)
        cls.grr_detail = MaterialReceivedDetail.objects.create(id=801, grr_no='GRR001', master=cls.grr_master, po_id=cls.po_detail_pr.id, received_qty=90.000)

        # Create SPR-based PO test data for completeness
        cls.po_master_spr = POMaster.objects.create(id=302, comp_id=cls.comp, po_no='PO002', supplier=cls.supplier, pr_spr_flag='1', fin_year_id=2023)
        cls.spr_master = SPRMaster.objects.create(id=402, spr_no='SPR001', comp_id=cls.comp)
        cls.spr_detail = SPRDetail.objects.create(id=502, spr_no='SPR001', master=cls.spr_master, item=cls.item)
        cls.po_detail_spr = PODetail.objects.create(id=602, po_no='PO002', master=cls.po_master_spr, spr_no='SPR001', spr_id=cls.spr_detail.id, qty=50.000)
        cls.inward_detail_spr = InwardDetail.objects.create(id=702, gin_no='GIN001', master=cls.inward_master, po_id=cls.po_detail_spr.id, received_qty=48.000)
        cls.grr_detail_spr = MaterialReceivedDetail.objects.create(id=802, grr_no='GRR001', master=cls.grr_master, po_id=cls.po_detail_spr.id, received_qty=45.000)


    def test_get_grr_report_data_pr_based(self):
        report_data = GoodsReceivedReceipt.objects.get_grr_report_data(
            comp_id=self.comp.id,
            grr_master_id=self.grr_master.id,
            gin_id=self.inward_master.id
        )
        
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['grr_no'], 'GRR001')
        self.assertEqual(report_data['gin_no'], 'GIN001')
        self.assertEqual(report_data['supplier_name'], 'Test Supplier')
        self.assertEqual(report_data['company_address'], '123 Test St')
        self.assertEqual(len(report_data['details']), 2) # Both PR and SPR based items now

        # Test PR based item
        pr_item_data = next((item for item in report_data['details'] if item['po_id'] == self.po_detail_pr.id), None)
        self.assertIsNotNone(pr_item_data)
        self.assertEqual(pr_item_data['item_code'], 'ITEM001')
        self.assertEqual(pr_item_data['description'], 'Test Item Desc')
        self.assertEqual(pr_item_data['uom'], 'Kg')
        self.assertAlmostEqual(float(pr_item_data['po_qty']), 100.000)
        self.assertAlmostEqual(float(pr_item_data['inv_qty']), 95.000)
        self.assertAlmostEqual(float(pr_item_data['reced_qty']), 90.000)

        # Test SPR based item
        spr_item_data = next((item for item in report_data['details'] if item['po_id'] == self.po_detail_spr.id), None)
        self.assertIsNotNone(spr_item_data)
        self.assertEqual(spr_item_data['item_code'], 'ITEM001')
        self.assertEqual(spr_item_data['description'], 'Test Item Desc')
        self.assertEqual(spr_item_data['uom'], 'Kg')
        self.assertAlmostEqual(float(spr_item_data['po_qty']), 50.000)
        self.assertAlmostEqual(float(spr_item_data['inv_qty']), 48.000)
        self.assertAlmostEqual(float(spr_item_data['reced_qty']), 45.000)

    def test_get_grr_report_data_non_existent_master(self):
        with self.assertRaises(MaterialReceivedMaster.DoesNotExist):
            GoodsReceivedReceipt.objects.get_grr_report_data(
                comp_id=self.comp.id,
                grr_master_id=999, # Non-existent ID
                gin_id=self.inward_master.id
            )
            
    def test_get_grr_report_data_non_existent_inward(self):
        with self.assertRaises(InwardMaster.DoesNotExist):
            GoodsReceivedReceipt.objects.get_grr_report_data(
                comp_id=self.comp.id,
                grr_master_id=self.grr_master.id,
                gin_id=999 # Non-existent ID
            )

class GoodsReceivedReceiptViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.comp = CompanyMaster.objects.create(id=1, company_name='TestCo', address='123 Test St')
        cls.unit = UnitMaster.objects.create(id=1, symbol='Kg')
        cls.item = ItemMaster.objects.create(id=1, comp_id=cls.comp, item_code='ITEM001', manf_desc='Test Item Desc', uom_basic=cls.unit)
        cls.supplier = SupplierMaster.objects.create(supplier_id=1, comp_id=cls.comp, supplier_name='Test Supplier')

        cls.grr_master = MaterialReceivedMaster.objects.create(id=101, comp_id=cls.comp, grr_no='GRR001')
        cls.inward_master = InwardMaster.objects.create(id=201, comp_id=cls.comp, gin_no='GIN001', challan_no='CHALLAN001', challan_date='2023-01-15')
        cls.po_master_pr = POMaster.objects.create(id=301, comp_id=cls.comp, po_no='PO001', supplier=cls.supplier, pr_spr_flag='0', fin_year_id=2023)
        cls.pr_master = PRMaster.objects.create(id=401, pr_no='PR001', comp_id=cls.comp)
        cls.pr_detail = PRDetail.objects.create(id=501, pr_no='PR001', master=cls.pr_master, item=cls.item)
        cls.po_detail_pr = PODetail.objects.create(id=601, po_no='PO001', master=cls.po_master_pr, pr_no='PR001', pr_id=cls.pr_detail.id, qty=100.000)
        cls.inward_detail = InwardDetail.objects.create(id=701, gin_no='GIN001', master=cls.inward_master, po_id=cls.po_detail_pr.id, received_qty=95.000)
        cls.grr_detail = MaterialReceivedDetail.objects.create(id=801, grr_no='GRR001', master=cls.grr_master, po_id=cls.po_detail_pr.id, received_qty=90.000)

    def setUp(self):
        self.client = Client()
    
    def test_detail_view_success(self):
        url = reverse(
            'goodsreceivedreceipt_detail_print',
            kwargs={
                'grr_master_id': self.grr_master.id,
                'gin_id': self.inward_master.id
            }
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceipt/detail.html')
        self.assertIn('report', response.context)
        self.assertEqual(response.context['report']['grr_no'], 'GRR001')
        self.assertEqual(len(response.context['report']['details']), 1) # Only one detail created in setUpTestData for this specific test

    def test_detail_view_not_found(self):
        url = reverse(
            'goodsreceivedreceipt_detail_print',
            kwargs={
                'grr_master_id': 999, # Non-existent
                'gin_id': self.inward_master.id
            }
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200) # Still 200, but with error message
        self.assertIn('Error loading GRR data', response.content.decode())
        self.assertIsNone(response.context['report'])

    def test_detail_table_partial_view_htmx(self):
        url = reverse(
            'goodsreceivedreceipt_details_table',
            kwargs={
                'grr_master_id': self.grr_master.id,
                'gin_id': self.inward_master.id
            }
        )
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceipt/_detail_table.html')
        self.assertIn('report', response.context)
        self.assertTrue('id="goodsreceivedreceiptDetailTable"' in response.content.decode())
        self.assertIn('ITEM001', response.content.decode())
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django solution already incorporates HTMX for dynamic content loading and Alpine.js for potential client-side reactivity (though minimal for a print view).

-   **HTMX for `_detail_table.html`:** The main `detail.html` uses `hx-get` to load the item details table (`_detail_table.html`) dynamically after the page loads. This mimics the Crystal Report Viewer rendering its content.
-   **No specific Alpine.js functionality required on this page:** While Alpine.js is part of the stack, this particular "print details" page does not have complex interactive UI elements beyond a static display and a back button. The modal framework shown in the general template structure is for CRUD forms, not directly applicable here.
-   **DataTables:** Integrated into `_detail_table.html` to provide client-side features for the item list, aligning with the "DataTables for all list views" guideline. Although for a print view, sorting/searching might not be critical, having it enabled by default aligns with the pattern.
-   **Full Page Reload Avoidance:** By using HTMX for the table, we demonstrate how parts of the page can be updated without a full reload, a core benefit of HTMX.
-   **`HX-Trigger`:** Not strictly needed for this *read-only* page, but for CRUD operations elsewhere, `HX-Trigger` would be used to refresh the list table after a successful create/update/delete.

### Final Notes

-   **Placeholders:** The `comp_id` in views and tests is currently a placeholder (e.g., `1`). In a real application, this would typically come from the authenticated user's profile or session, linking to the concept of `Session["compid"]` from the ASP.NET code.
-   **Date Formatting:** `ChallanDate` is formatted using Django's `date` filter (`|date:"d/m/Y"`), replacing `fun.FromDateDMY()`.
-   **Company Address:** The `fun.CompAdd(CompId)` logic is encapsulated in the `CompanyMaster` model by assuming an `address` field.
-   **`fun.GetItemCode_PartNo`:** This is assumed to be part of the `ItemMaster` model or a utility method accessible from it, or that `ItemCode` as stored is already the formatted one. For this example, we assume `item.item_code` is sufficient.
-   **Scalability:** Moving complex SQL logic to Python ORM within the model layer (the "fat model") greatly improves maintainability, testability, and allows Django's ORM to optimize queries.
-   **Modularity:** Breaking down functionality into distinct Django app files (`models.py`, `views.py`, `urls.py`, `templates/`) promotes a clean, organized, and modular codebase.