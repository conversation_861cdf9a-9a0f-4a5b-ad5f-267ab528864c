## ASP.NET to Django Conversion Script: Goods Service Note (GSN) New

This document outlines a comprehensive plan for migrating the ASP.NET `GoodsServiceNote_SN_New.aspx` and its C# code-behind to a modern Django-based solution. The focus is on leveraging Django's robust features, promoting a "fat model, thin view" architecture, and implementing dynamic user interfaces using HTMX and Alpine.js with DataTables for efficient data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code primarily interacts with an SQL Stored Procedure `Sp_GSN_New` to retrieve data, which then undergoes further filtering in the C# code. This suggests the displayed data is a computed view rather than a direct table representation. However, the `GridView2` binds to various fields, and the `AutoCompleteExtender` uses `tblMM_Supplier_master`.

Based on the `Sp_GSN_New` usage and the `GridView2` column bindings, we infer the following primary data source and a supporting lookup table.

**Primary Data Source (Logical view of `Sp_GSN_New` output)**
The `Sp_GSN_New` procedure, combined with the C# filtering logic, effectively generates a dataset of "Goods Service Note Candidates." While this isn't a single table, the `Id` field points to `tblInv_Inward_Master`. We will define a Django model `InwardMaster` to represent the base table `tblInv_Inward_Master` and, crucially, implement the complex data retrieval and filtering logic within a custom manager for this model to adhere to the "fat model" principle.

- **Table Name**: `tblInv_Inward_Master` (base table, for `Id`, `FinYearId`, `PONo`, `GINNo`, `ChallanNo`, `ChallanDate`, `SysDate`, `PRSPRFlag`).
- **Columns inferred from `GridView2` and `Sp_GSN_New` result processing**:
    - `Id` (PK, int): `tblInv_Inward_Master.Id`
    - `FinYearId` (int): `tblInv_Inward_Master.FinYearId`
    - `FinYear` (string): From `tblFinancial_master` via join.
    - `GINNo` (string): `tblInv_Inward_Master.GINNo`
    - `SysDate` (date, `GINDate` in SP): `tblInv_Inward_Master.SysDate`
    - `PONo` (string): `tblInv_Inward_Master.PONo`
    - `Supplier` (string, `SupplierName` in SP): From `tblMM_Supplier_master` via join.
    - `ChNO` (string, `ChallanNo` in SP): `tblInv_Inward_Master.ChallanNo`
    - `ChDT` (date, `ChallanDate` in SP): `tblInv_Inward_Master.ChallanDate`
    - `SupId` (string, `SupplierId` in SP): `tblMM_Supplier_master.SupplierId`
    - `GINQty` (double, calculated): Computed by SP logic.
    - `GSNQty` (double, calculated): Computed by SP logic.
    - `PRSPRFlag` (int): `tblInv_Inward_Master.PRSPRFlag`

**Supporting Table:**

- **Table Name**: `tblMM_Supplier_master`
- **Columns**:
    - `SupplierId` (PK, string)
    - `SupplierName` (string)
    - `CompId` (int)

### Step 2: Identify Backend Functionality

The ASP.NET page provides the following functionalities:

-   **Read (List)**: Displays a list of "Goods Service Note" candidates from the database. This involves complex data retrieval and filtering logic (`Sp_GSN_New` and C# filtering of `GINQty`, `GSNQty`, and `calbalrecqty > 0`).
-   **Search/Filter**: Allows filtering the list by `Supplier`.
-   **Autocomplete**: Provides autocomplete suggestions for the `Supplier` input field.
-   **Navigation**: A "Select" button on each row redirects to a `GoodsServiceNote_SN_New_Details.aspx` page (which will be a new Django detail view).
-   **Pagination**: The `GridView` handles pagination.

No direct Create, Update, or Delete operations are performed on the `InwardMaster` from this specific page.

### Step 3: Infer UI Components

-   **Supplier Input**: A text box (`txtSupplier`) for entering supplier names.
-   **Autocomplete**: Attached to the supplier text box, providing suggestions from `tblMM_Supplier_master`.
-   **Search Button**: (`btnSearch`) to trigger the filtering of the list.
-   **Data Grid**: A `GridView` (`GridView2`) that displays the "Goods Service Note Candidates" with columns for `SN`, `Select`, `Fin Year`, `GIN No`, `Date`, `PONo`, `Name of Supplier`, `Challan No`, and `Challan Date`.
-   **Action Button**: A `LinkButton` ("Select") within each row to navigate to a details page.
-   **Pagination**: The `GridView` natively supports pagination.
-   **Empty Data Message**: "No data to display!"

### Step 4: Generate Django Code

The Django application will be named `inventory`.

#### 4.1 Models (`inventory/models.py`)

We will define two models: `SupplierMaster` for the lookup, and `InwardMaster` which primarily uses a custom manager to encapsulate the complex `Sp_GSN_New` and filtering logic.

```python
from django.db import models, connection
from datetime import datetime # Use datetime for parsing dates from SP if they come as strings

# Helper function to convert 'SupplierName [SupplierId]' to SupplierId
def parse_supplier_input(supplier_text):
    """
    Parses a string like 'SupplierName [SupplierId]' to extract 'SupplierId'.
    Used for converting autocomplete selection back to ID.
    """
    if '[' in supplier_text and ']' in supplier_text:
        try:
            return supplier_text.split('[')[-1].replace(']', '').strip()
        except IndexError:
            pass
    return ''

class SupplierMaster(models.Model):
    """
    Corresponds to tblMM_Supplier_master for supplier lookup and autocomplete.
    """
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    company_id = models.IntegerField(db_column='CompId') # Assuming CompId is part of this table

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class InwardMasterManager(models.Manager):
    """
    Custom manager to encapsulate the complex data retrieval and filtering logic
    from the original ASP.NET's Sp_GSN_New and subsequent C# filtering.
    """
    def get_eligible_for_gsn(self, company_id, fin_year_id, supplier_name_input=None):
        """
        Executes the stored procedure Sp_GSN_New and applies the C# filtering logic.
        This method replaces the `loadData()` function from the original ASP.NET code.
        """
        # Parse supplier_name_input to extract supplier_id if it's in "Name [ID]" format
        supplier_id_filter = parse_supplier_input(supplier_name_input) if supplier_name_input else ''

        # Construct the 'x' parameter for the stored procedure as in the original C# code
        # The SP expects 'x' to be an SQL fragment like " And tblMM_Supplier_master.SupplierId='sid'"
        x_param = f" And tblMM_Supplier_master.SupplierId='{supplier_id_filter}'" if supplier_id_filter else ""
        
        sql_query = """
            EXEC Sp_GSN_New @CompId=%s, @FinId=%s, @x=%s;
        """

        results = []
        with connection.cursor() as cursor:
            try:
                cursor.execute(sql_query, [company_id, fin_year_id, x_param])
                # Fetch column names from the cursor description
                columns = [col[0] for col in cursor.description]
                
                for row_tuple in cursor.fetchall():
                    row_dict = dict(zip(columns, row_tuple))
                    
                    gin_qty = float(row_dict.get('GINQty') or 0.0) # Ensure float conversion, handle DBNull
                    gsn_qty = float(row_dict.get('GSNQty') or 0.0) # Ensure float conversion, handle DBNull

                    calbalrecqty = round((gin_qty - gsn_qty), 3)

                    # Replicate C# filtering: (k > 0 && z > 0) && (calbalrecqty > 0)
                    # Based on C# logic, k increments if GINQty > 0, z increments if (GINQty - GSNQty) > 0.
                    # So, effectively, GINQty > 0 AND (GINQty - GSNQty) > 0.
                    if gin_qty > 0 and calbalrecqty > 0:
                        # Instantiate a temporary InwardMaster object to hold the data
                        # This allows us to use Django's model instance properties.
                        inward_obj = InwardMaster(
                            id=row_dict['Id'],
                            fin_year_id=row_dict['FinYearId'],
                            po_no=row_dict['PONo'],
                            gin_no=row_dict['GINNo'],
                            challan_no=row_dict['ChallanNo'],
                            # Ensure dates are datetime objects before passing
                            challan_date=row_dict['ChallanDate'] if isinstance(row_dict['ChallanDate'], datetime) else None,
                            sys_date=row_dict['GINDate'] if isinstance(row_dict['GINDate'], datetime) else None,
                            pr_spr_flag=row_dict['PRSPRFlag'],
                            # These are not direct columns in tblInv_Inward_Master but derived from SP
                            supplier_name=row_dict['SupplierName'],
                            supplier_id=row_dict['SupplierId'],
                            fin_year=row_dict['FinYear'],
                        )
                        # Attach calculated quantities for display if needed
                        inward_obj.gin_qty = gin_qty
                        inward_obj.gsn_qty = gsn_qty
                        results.append(inward_obj)
            except Exception as e:
                # Log the exception for debugging
                print(f"Error executing Sp_GSN_New: {e}")
                # Potentially re-raise or handle gracefully depending on production needs
        return results

class InwardMaster(models.Model):
    """
    Corresponds to tblInv_Inward_Master.
    Its primary purpose here is to define the structure for results from Sp_GSN_New
    and to serve as a base for the custom manager that fetches the GSN candidates.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=100, blank=True, null=True)
    gin_no = models.CharField(db_column='GINNo', max_length=100, blank=True, null=True)
    challan_no = models.CharField(db_column='ChallanNo', max_length=100, blank=True, null=True)
    challan_date = models.DateField(db_column='ChallanDate', blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True) # Corresponds to GINDate
    pr_spr_flag = models.IntegerField(db_column='PRSPRFlag', blank=True, null=True)

    # These fields are derived from joins within the stored procedure, not direct columns
    # of tblInv_Inward_Master. They are added as 'virtual' fields for convenience when
    # populating objects from the SP result set.
    supplier_name = models.CharField(max_length=255, blank=True, null=True)
    supplier_id = models.CharField(max_length=50, blank=True, null=True)
    fin_year = models.CharField(max_length=10, blank=True, null=True)

    objects = InwardMasterManager() # Assign our custom manager

    class Meta:
        managed = False  # Critical: tells Django not to manage this table's schema
        db_table = 'tblInv_Inward_Master' # The actual base table name
        verbose_name = 'Goods Service Note Candidate'
        verbose_name_plural = 'Goods Service Note Candidates'

    def __str__(self):
        return f"GIN: {self.gin_no} (PO: {self.po_no} | Supplier: {self.supplier_name})"

    # Helper methods for formatted dates, mirroring original C# `fun.FromDateDMY`
    def get_formatted_challan_date(self):
        return self.challan_date.strftime('%d/%m/%Y') if self.challan_date else ''

    def get_formatted_sys_date(self):
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

```

#### 4.2 Forms (`inventory/forms.py`)

A simple form for the supplier search input.

```python
from django import forms

class GSNFilterForm(forms.Form):
    """
    Form for filtering the GSN candidates list by supplier.
    """
    supplier_name = forms.CharField(
        label="Supplier",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'id': 'txtSupplier', # Matches original ID for HTMX/Alpine consistency
            'placeholder': 'Start typing supplier name...',
            # HTMX attributes for autocomplete
            'hx-get': '/inventory/suppliers/autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
            '@input': 'showSuggestions = true', # Alpine.js: show results on input
            '@focus': 'showSuggestions = true',
            '@blur.away': 'setTimeout(() => showSuggestions = false, 100)' # Hide after a delay
        })
    )

```

#### 4.3 Views (`inventory/views.py`)

We'll use `TemplateView` for the main page and a `ListView` for the HTMX-loaded table partial. A dedicated `JsonView` will handle the autocomplete.

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from .models import InwardMaster, SupplierMaster, parse_supplier_input
from .forms import GSNFilterForm
import json

# Assuming company_id and fin_year_id are stored in session,
# similar to ASP.NET Session["compid"] and Session["finyear"].
# These would be handled by middleware or a custom request processor
# for production. For this example, we'll use placeholder values or
# retrieve from a dummy session if available in tests.

class GSNListView(TemplateView):
    """
    Main view for Goods Service Note (GSN) New page.
    Renders the search form and the container for the GSN list.
    Corresponds to the initial load of GoodsServiceNote_SN_New.aspx.
    """
    template_name = 'inventory/goods_service_note/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = GSNFilterForm()
        # Initial load, the table content will be fetched via HTMX
        return context

class GSNTablePartialView(ListView):
    """
    HTMX-driven view to render the partial table content for GSN candidates.
    This replaces the GridView's data binding and pagination logic.
    """
    model = InwardMaster
    template_name = 'inventory/goods_service_note/_table.html'
    context_object_name = 'gsn_candidates'
    paginate_by = 20 # Matches original PageSize

    def get_queryset(self):
        # Retrieve company_id and fin_year_id from session or placeholder
        # In a real app, ensure these are securely obtained (e.g., from user profile, session)
        company_id = self.request.session.get('compid', 1) # Placeholder
        fin_year_id = self.request.session.get('finyear', 2023) # Placeholder

        supplier_name_input = self.request.GET.get('supplier_name', '').strip()
        
        # Use the custom manager to get the filtered data
        # This is where the ASP.NET loadData() logic is executed.
        queryset = InwardMaster.objects.get_eligible_for_gsn(
            company_id=company_id,
            fin_year_id=fin_year_id,
            supplier_name_input=supplier_name_input
        )
        return queryset

    # DataTables will handle client-side pagination, sorting, and search.
    # The ListView's built-in pagination might be used if server-side pagination is desired.
    # For DataTables, we usually pass all filtered data and let DataTables paginate.
    # So, we override paginate_by to None if DataTables handles it entirely,
    # or ensure our queryset is correctly sliced if we want server-side.
    # For simplicity, we'll just return the full filtered set and let DataTables do its job.

class SupplierAutocompleteView(View):
    """
    HTMX endpoint for supplier autocomplete suggestions.
    Corresponds to the `sql` WebMethod in the C# code.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '').strip()
        company_id = request.session.get('compid', 1) # Get from session, placeholder

        if len(query) < 1: # MinimumPrefixLength from ASP.NET was 1
            return HttpResponse("") # Return empty response if query too short

        suppliers = SupplierMaster.objects.filter(
            supplier_name__icontains=query,
            company_id=company_id # Filter by company as in original
        ).order_by('supplier_name')[:10] # Limit suggestions, similar to original, though original had 10

        # Format results as "SupplierName [SupplierId]" as in original
        results = [f"{s.supplier_name} [{s.supplier_id}]" for s in suppliers]

        # Return as an HTML list for HTMX hx-swap="innerHTML"
        # The original used a custom CSS class for highlighting, which Alpine/HTMX can manage.
        html_response = "<ul class='absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto'>"
        if not results:
            html_response += "<li class='px-3 py-2 text-gray-500'>No results found</li>"
        for item in results:
            # Alpine.js handles selection by setting the input value
            html_response += f"<li class='px-3 py-2 cursor-pointer hover:bg-gray-100' @click='document.getElementById(\"txtSupplier\").value=\"{item}\"; showSuggestions = false;'>{item}</li>"
        html_response += "</ul>"
        return HttpResponse(html_response)

class GSNSelectDetailView(View):
    """
    Handles the "Select" row command, redirecting to a new detail page.
    Corresponds to GridView2_RowCommand and Response.Redirect.
    """
    def get(self, request, pk, *args, **kwargs):
        # We need to retrieve other params from the original request context
        # For simplicity, we'll pass the ID and expect the new page to fetch details.
        # Original: GoodsServiceNote_SN_New_Details.aspx?Id=X&SupId=Y&GINNo=Z&PONo=A&FyId=B&ModId=9&SubModId=39
        # This assumes a new Django view `gsn_detail` exists that can handle these parameters.
        
        # Example of getting other parameters if needed, though for a redirect,
        # often only the PK is sufficient for the detail page to fetch its own data.
        # You might have to pass them through the URL if the detail page specifically needs them.
        
        # In a real Django setup, the detail page would typically fetch the related
        # InwardMaster object using the `pk` and then derive other details.
        
        # Placeholder for actual redirect
        # This URL should point to your actual GSN detail view
        messages.info(request, f"Redirecting to GSN Details for ID: {pk}")
        
        # Construct the redirect URL with necessary parameters.
        # This assumes a 'goods_service_note_detail' URL pattern exists.
        # In a real app, these parameters might be used to pre-filter/load data on the detail page.
        # For HTMX, a simple redirect header is enough, or trigger a full page navigation.
        
        # Example of extracting other parameters from the list view if they were needed
        # and somehow passed to the 'Select' button (e.g., via data attributes).
        # Since the ASP.NET extracted from Labels in the GridViewRow, the Django
        # template should also provide them via data attributes or directly in the URL.
        
        # For demonstration, let's assume the detail page needs only the PK.
        # If HTMX is used for navigation, `HX-Redirect` header would be set.
        if request.headers.get('HX-Request'):
            # If HTMX, send a client-side redirect signal
            redirect_url = reverse_lazy('inventory:goods_service_note_detail', args=[pk]) 
            # You might need to add other query params if the detail page absolutely needs them
            # E.g., f"{redirect_url}?SupId={supplier_id}&GINNo={gin_no}&PONo={po_no}&FyId={fin_year_id}"
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})
        else:
            # Standard browser redirect
            return redirect(reverse_lazy('inventory:goods_service_note_detail', args=[pk]))
            
```

#### 4.4 Templates

**`inventory/goods_service_note/list.html`** (Main page template)
This template will host the search form and an HTMX-driven container for the DataTables content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Goods Service Note [GSN] - New</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form id="gsnFilterForm" hx-get="{% url 'inventory:goods_service_note_table' %}" hx-target="#gsnTable-container" hx-swap="innerHTML" hx-trigger="submit">
            {% csrf_token %}
            <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4">
                <div class="relative w-full md:w-auto flex-grow" x-data="{ showSuggestions: false }">
                    <label for="{{ form.supplier_name.id_for_label }}" class="block text-sm font-medium text-gray-700 sr-only">
                        {{ form.supplier_name.label }}
                    </label>
                    <span class="inline-block text-gray-700 font-bold mb-1">Supplier:</span>
                    {{ form.supplier_name }}
                    <div id="supplier-autocomplete-results" x-show="showSuggestions" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
                        <!-- Autocomplete results will be loaded here via HTMX -->
                    </div>
                </div>
                <div class="w-full md:w-auto">
                    <button type="submit" class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <div id="gsnTable-container"
         hx-trigger="load, refreshGSNList from:body"
         hx-get="{% url 'inventory:goods_service_note_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Goods Service Note candidates...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader page state
    });
</script>
{% endblock %}
```

**`inventory/goods_service_note/_table.html`** (Partial template for the DataTables content)
This template is loaded via HTMX and contains the table structure.

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    {% if gsn_candidates %}
    <table id="gsnTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PONo</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for candidate in gsn_candidates %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'inventory:goods_service_note_select' candidate.id %}"
                        hx-trigger="click"
                        hx-swap="none"
                        title="Select GSN Candidate">
                        Select
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ candidate.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ candidate.gin_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ candidate.get_formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ candidate.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-justify">{{ candidate.supplier_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ candidate.challan_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ candidate.get_formatted_challan_date }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <script>
        $(document).ready(function() {
            $('#gsnTable').DataTable({
                "pageLength": 20, // Match original PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "autoWidth": false,
                "columnDefs": [
                    { "orderable": false, "targets": [0, 1] } // SN and Actions columns not sortable
                ]
            });
        });
    </script>
    {% else %}
    <div class="text-center py-10 fontcss text-maroon text-lg">
        <p>No data to display !</p>
    </div>
    {% endif %}
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

This file defines the URL patterns for our `inventory` application.

```python
from django.urls import path
from .views import GSNListView, GSNTablePartialView, SupplierAutocompleteView, GSNSelectDetailView

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    # Main page for Goods Service Note New
    path('goods-service-note/new/', GSNListView.as_view(), name='goods_service_note_list'),
    
    # HTMX endpoint for the DataTables partial
    path('goods-service-note/table/', GSNTablePartialView.as_view(), name='goods_service_note_table'),
    
    # HTMX endpoint for supplier autocomplete
    path('suppliers/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
    
    # Endpoint for the "Select" action on a GSN candidate
    # This will redirect to a new GSN detail page (which would be another Django view)
    path('goods-service-note/select/<int:pk>/', GSNSelectDetailView.as_view(), name='goods_service_note_select'),

    # Placeholder for the actual GSN detail page.
    # Replace with your actual detail view URL.
    path('goods-service-note/details/<int:pk>/', GSNSelectDetailView.as_view(), name='goods_service_note_detail'), # Assuming this URL exists
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for both models and views to ensure functionality and data integrity. We will mock the database interaction for `Sp_GSN_New` to ensure predictable test outcomes.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date
from .models import InwardMaster, SupplierMaster, parse_supplier_input

class HelperFunctionTest(TestCase):
    def test_parse_supplier_input(self):
        self.assertEqual(parse_supplier_input("Test Supplier [SUP001]"), "SUP001")
        self.assertEqual(parse_supplier_input("Another Supplier"), "")
        self.assertEqual(parse_supplier_input(""), "")
        self.assertEqual(parse_supplier_input("No Bracket Here"), "")
        self.assertEqual(parse_supplier_input("Multiple [Brackets] [Final]"), "Final")

class SupplierMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        SupplierMaster.objects.create(
            supplier_id='SUP001',
            supplier_name='Test Supplier One',
            company_id=1
        )
        SupplierMaster.objects.create(
            supplier_id='SUP002',
            supplier_name='Another Supplier',
            company_id=1
        )
        SupplierMaster.objects.create(
            supplier_id='SUP003',
            supplier_name='Third Party',
            company_id=2
        )

    def test_supplier_creation(self):
        supplier = SupplierMaster.objects.get(supplier_id='SUP001')
        self.assertEqual(supplier.supplier_name, 'Test Supplier One')
        self.assertEqual(supplier.company_id, 1)

    def test_str_representation(self):
        supplier = SupplierMaster.objects.get(supplier_id='SUP001')
        self.assertEqual(str(supplier), 'Test Supplier One')

    def test_verbose_name(self):
        self.assertEqual(SupplierMaster._meta.verbose_name, 'Supplier')
        self.assertEqual(SupplierMaster._meta.verbose_name_plural, 'Suppliers')

class InwardMasterModelTest(TestCase):
    # InwardMaster uses managed=False, so we mostly test its attributes
    # and the custom manager's logic separately.
    @classmethod
    def setUpTestData(cls):
        # We don't create InwardMaster instances directly here as it's managed=False
        # and its data comes from the SP. We'll rely on mocking the SP output
        # for testing the custom manager.
        pass

    def test_formatted_dates(self):
        test_date = date(2023, 10, 26)
        obj = InwardMaster(challan_date=test_date, sys_date=test_date)
        self.assertEqual(obj.get_formatted_challan_date(), '26/10/2023')
        self.assertEqual(obj.get_formatted_sys_date(), '26/10/2023')
        
        obj_no_date = InwardMaster(challan_date=None, sys_date=None)
        self.assertEqual(obj_no_date.get_formatted_challan_date(), '')
        self.assertEqual(obj_no_date.get_formatted_sys_date(), '')

    @patch('inventory.models.connection.cursor')
    def test_get_eligible_for_gsn_manager(self, mock_cursor):
        # Mock the cursor to simulate SP output
        mock_cursor_instance = MagicMock()
        mock_cursor.return_value.__enter__.return_value = mock_cursor_instance

        # Simulate a result set from the stored procedure
        # Columns must match those expected by the C# code and InwardMasterManager
        mock_cursor_instance.description = [
            ('Id',), ('FinYearId',), ('PONo',), ('GINNo',), ('GINDate',), ('ChallanNo',), 
            ('ChallanDate',), ('PRSPRFlag',), ('SupplierName',), ('SupplierId',), 
            ('FinYear',), ('GINQty',), ('GSNQty',)
        ]
        
        # Row 1: Valid candidate (GINQty > 0, balance > 0)
        # Row 2: Invalid candidate (balance == 0)
        # Row 3: Invalid candidate (GINQty == 0)
        sp_results = [
            (1, 2023, 'PO001', 'GIN001', datetime(2023, 10, 20), 'CH001', datetime(2023, 10, 19), 0, 'SupplierA', 'SUPA', 'FY23', 10.0, 5.0),
            (2, 2023, 'PO002', 'GIN002', datetime(2023, 10, 21), 'CH002', datetime(2023, 10, 20), 0, 'SupplierB', 'SUPB', 'FY23', 10.0, 10.0), # GINQty == GSNQty
            (3, 2023, 'PO003', 'GIN003', datetime(2023, 10, 22), 'CH003', datetime(2023, 10, 21), 0, 'SupplierC', 'SUPC', 'FY23', 0.0, 0.0),    # GINQty == 0
        ]
        mock_cursor_instance.fetchall.return_value = sp_results

        company_id = 1
        fin_year_id = 2023

        # Test with no supplier filter
        candidates = InwardMaster.objects.get_eligible_for_gsn(company_id, fin_year_id)
        self.assertEqual(len(candidates), 1)
        self.assertEqual(candidates[0].id, 1)
        self.assertEqual(candidates[0].supplier_name, 'SupplierA')
        self.assertEqual(candidates[0].gin_qty, 10.0)
        self.assertEqual(candidates[0].gsn_qty, 5.0)

        # Test with supplier filter
        mock_cursor_instance.fetchall.return_value = [
            (1, 2023, 'PO001', 'GIN001', datetime(2023, 10, 20), 'CH001', datetime(2023, 10, 19), 0, 'SupplierA', 'SUPA', 'FY23', 10.0, 5.0)
        ]
        candidates_filtered = InwardMaster.objects.get_eligible_for_gsn(company_id, fin_year_id, "SupplierA [SUPA]")
        self.assertEqual(len(candidates_filtered), 1)
        self.assertEqual(candidates_filtered[0].supplier_id, 'SUPA')
        
        # Test when SP returns no eligible results after C# filtering
        mock_cursor_instance.fetchall.return_value = [
            (2, 2023, 'PO002', 'GIN002', datetime(2023, 10, 21), 'CH002', datetime(2023, 10, 20), 0, 'SupplierB', 'SUPB', 'FY23', 10.0, 10.0) # GINQty == GSNQty
        ]
        candidates_empty = InwardMaster.objects.get_eligible_for_gsn(company_id, fin_year_id)
        self.assertEqual(len(candidates_empty), 0)


class GSNViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session variables
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        # Create a test supplier for autocomplete
        SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Test Supplier', company_id=1)
        SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='Another Vendor', company_id=1)

    @patch('inventory.models.connection.cursor')
    def mock_sp_results(self, mock_cursor, results_list):
        mock_cursor_instance = MagicMock()
        mock_cursor.return_value.__enter__.return_value = mock_cursor_instance
        mock_cursor_instance.description = [
            ('Id',), ('FinYearId',), ('PONo',), ('GINNo',), ('GINDate',), ('ChallanNo',), 
            ('ChallanDate',), ('PRSPRFlag',), ('SupplierName',), ('SupplierId',), 
            ('FinYear',), ('GINQty',), ('GSNQty',)
        ]
        mock_cursor_instance.fetchall.return_value = results_list

    def test_gsn_list_view_get(self):
        response = self.client.get(reverse('inventory:goods_service_note_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_service_note/list.html')
        self.assertContains(response, 'Goods Service Note [GSN] - New')
        self.assertContains(response, '<form id="gsnFilterForm"') # Check for the form
        self.assertContains(response, '<div id="gsnTable-container"') # Check for HTMX target

    @patch('inventory.models.connection.cursor')
    def test_gsn_table_partial_view_get(self, mock_cursor):
        # Setup mock SP results for the table content
        self.mock_sp_results(mock_cursor, [
            (101, 2023, 'PO-A', 'GIN-X', datetime(2023, 1, 1), 'CH-1', datetime(2023, 1, 2), 0, 'Test Supplier', 'SUP001', '23-24', 100.0, 50.0),
            (102, 2023, 'PO-B', 'GIN-Y', datetime(2023, 1, 3), 'CH-2', datetime(2023, 1, 4), 0, 'Another Vendor', 'SUP002', '23-24', 200.0, 100.0),
        ])
        
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX request
        response = self.client.get(reverse('inventory:goods_service_note_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_service_note/_table.html')
        self.assertContains(response, '<table id="gsnTable"')
        self.assertContains(response, 'GIN-X')
        self.assertContains(response, 'Test Supplier')
        self.assertContains(response, 'GIN-Y')
        self.assertContains(response, 'Another Vendor')
        self.assertEqual(len(response.context['gsn_candidates']), 2)

    @patch('inventory.models.connection.cursor')
    def test_gsn_table_partial_view_search(self, mock_cursor):
        # Setup mock SP results for filtered search
        self.mock_sp_results(mock_cursor, [
            (101, 2023, 'PO-A', 'GIN-X', datetime(2023, 1, 1), 'CH-1', datetime(2023, 1, 2), 0, 'Test Supplier', 'SUP001', '23-24', 100.0, 50.0),
        ])
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:goods_service_note_table'), {'supplier_name': 'Test Supplier [SUP001]'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Supplier')
        self.assertNotContains(response, 'Another Vendor')
        self.assertEqual(len(response.context['gsn_candidates']), 1)

    def test_supplier_autocomplete_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:supplier_autocomplete'), {'q': 'test'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn('Test Supplier [SUP001]', response.content.decode())
        self.assertNotIn('Another Vendor', response.content.decode())
        self.assertTrue(response.content.decode().startswith('<ul'))
        self.assertTrue(response.content.decode().endswith('</ul>'))

        response_no_query = self.client.get(reverse('inventory:supplier_autocomplete'), {'q': ''}, **headers)
        self.assertEqual(response_no_query.status_code, 200)
        self.assertEqual(response_no_query.content.decode(), "")

        response_no_results = self.client.get(reverse('inventory:supplier_autocomplete'), {'q': 'nonexistent'}, **headers)
        self.assertEqual(response_no_results.status_code, 200)
        self.assertIn('No results found', response_no_results.content.decode())

    def test_gsn_select_detail_view_redirect(self):
        pk_to_select = 123
        response = self.client.get(reverse('inventory:goods_service_note_select', args=[pk_to_select]))
        self.assertEqual(response.status_code, 302) # Standard redirect
        self.assertRedirects(response, reverse('inventory:goods_service_note_detail', args=[pk_to_select]))

    def test_gsn_select_detail_view_htmx_redirect(self):
        pk_to_select = 456
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:goods_service_note_select', args=[pk_to_select]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX silent success
        self.assertEqual(response.headers['HX-Redirect'], reverse('inventory:goods_service_note_detail', args=[pk_to_select]))

```

### Step 5: HTMX and Alpine.js Integration

-   **HTMX for Data Table Loading**: The `gsnTable-container` `div` uses `hx-get="{% url 'inventory:goods_service_note_table' %}"` with `hx-trigger="load, refreshGSNList from:body"` to initially load the table and refresh it after any relevant events (though no direct CRUD on this page, `refreshGSNList` would be triggered if the data could change from another source).
-   **HTMX for Search**: The `gsnFilterForm` uses `hx-get` and `hx-target="#gsnTable-container"` to update the table content without a full page reload when the search button is clicked.
-   **HTMX for Autocomplete**: The `txtSupplier` input has `hx-get` to `{% url 'inventory:supplier_autocomplete' %}`, `hx-trigger="keyup changed delay:500ms, search"`, and `hx-target="#supplier-autocomplete-results"` to fetch and display suggestions dynamically.
-   **HTMX for "Select" Action**: The "Select" button on each row uses `hx-get` to `{% url 'inventory:goods_service_note_select' candidate.id %}` and `hx-swap="none"` with a `HX-Redirect` header in the view to navigate to the detail page gracefully (or a standard redirect if not an HTMX request).
-   **Alpine.js for Autocomplete UI**: `x-data="{ showSuggestions: false }"` on the autocomplete container handles the visibility of the suggestion list, showing it on input focus/type and hiding it on blur. Clicking a suggestion updates the input and hides the list.
-   **DataTables Integration**: The `_table.html` partial includes a `script` block that initializes DataTables on the `gsnTable`. This ensures client-side pagination, sorting, and searching are handled by DataTables, enhancing the user experience without complex server-side logic for basic table operations. The `pageLength` and `lengthMenu` are configured to match the original `GridView` settings.
-   **DRY Templates**: The use of `{% extends 'core/base.html' %}` and a partial template (`_table.html`) ensures that base layout and common components are not duplicated.
-   **No HTML in Views**: All HTML rendering is confined to Django templates.
-   **Business Logic in Models**: The complex data retrieval and filtering logic (`Sp_GSN_New` interpretation and C# filtering) is encapsulated within the `InwardMasterManager`, adhering to the fat model principle. Views remain thin, primarily handling HTTP requests and rendering templates.

---

### Final Notes

This modernization plan provides a clear, actionable roadmap for transitioning the ASP.NET GSN list page to Django. By focusing on AI-assisted automation, the goal is to systematically convert the application's functionality. The adoption of Django, HTMX, Alpine.js, and DataTables will result in a more maintainable, scalable, and responsive web application, significantly improving user experience and developer productivity compared to the legacy ASP.NET framework. The use of managed=False and a custom manager addresses the challenge of integrating complex existing database logic, maintaining data integrity while benefiting from Django's structure.