This comprehensive modernization plan outlines the strategy for migrating your ASP.NET Goods Inward Note (GIN) report page to a modern Django-based solution. Our approach emphasizes automated conversion principles, leveraging Django's robust ORM, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation.

The original ASP.NET page primarily serves as a Crystal Report viewer, dynamically fetching complex data from various database tables and displaying it as a structured report. In Django, we will replace Crystal Reports with direct HTML rendering, using Django's powerful templating engine and applying the "fat model, thin view" pattern to encapsulate data retrieval and business logic within the models.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the primary database tables and their columns involved in the Goods Inward Note (GIN) report generation.

**Instructions:**
The ASP.NET code queries several tables to compile the GIN report. The core data for the report's line items comes from `tblInv_Inward_Details` and `tblInv_Inward_Master`. Other tables (`tblMM_PO_Details`, `tblMM_PO_Master`, `tblMM_PR_Details`, `tblMM_SPR_Details`, `tblDG_Item_Master`, `Unit_Master`, `tblFinancial_master`, `tblACC_Asset_Category`, `tblACC_Asset_SubCategory`, `tblMM_Supplier_master`, `tblcountry`, `tblState`, `tblCity`, `BusinessGroup`) are used for lookups to enrich the report data.

For the purpose of representing the main *reportable entity* on this page, which is a detail line within a GIN, we will primarily focus on `tblInv_Inward_Details` as the core `[TABLE_NAME]`. We will create a primary model for this, named `InvInwardNoteDetail`. The related `tblInv_Inward_Master` will be represented by `InvInwardNote`, and other lookups will be handled via relationships or specialized model methods.

**Inferred Tables & Key Columns (for the main report data `dt`):**

*   **`tblInv_Inward_Master`**:
    *   `Id` (primary key for `InvInwardNote`)
    *   `GINNo` (Goods Inward Note Number)
    *   `GateEntryNo`
    *   `GDate`
    *   `GTime`
    *   `ModeofTransport`
    *   `VehicleNo`
    *   `CompId` (Company ID)
    *   `FinYearId` (Financial Year ID)

*   **`tblInv_Inward_Details`**:
    *   `Id` (primary key for `InvInwardNoteDetail`)
    *   `GINId` (Foreign Key to `tblInv_Inward_Master.Id`)
    *   `GINNo` (Redundant, but used in joins)
    *   `Qty` (Challan Quantity)
    *   `ReceivedQty` (Received Quantity)
    *   `POId` (Purchase Order Detail ID)
    *   `ACategoyId` (Asset Category ID)
    *   `ASubCategoyId` (Asset Sub-Category ID)

*   **Derived/Looked-up fields (from other tables, assembled into the `dt` in C#):**
    *   `ItemCode` (from `tblDG_Item_Master`)
    *   `Description` (from `tblDG_Item_Master.ManfDesc`)
    *   `UOM` (from `Unit_Master`)
    *   `poqty` (from `tblMM_PO_Details.Qty`)
    *   `SupplierName` (from `tblMM_Supplier_master`)
    *   `SupplierAdd` (from `tblMM_Supplier_master`, `tblcountry`, `tblState`, `tblCity`)
    *   `ChallanDate` (parsed from `ChDt` parameter)
    *   `WONO` (Work Order No, from `tblMM_PR_Master` or `tblMM_SPR_Master`)
    *   `Dept` (Department, from `BusinessGroup`)
    *   `Address` (Company Address, via `fun.CompAdd`)

**Summary of Primary Model:**
*   **`[TABLE_NAME]`**: `tblInv_Inward_Details` (for the line items in the report)
*   **Key Columns (for `InvInwardNoteDetail`):** `Id`, `GINId`, `Qty`, `ReceivedQty`, `POId`, `ACategoyId`, `ASubCategoyId`.

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The provided ASP.NET code for `GoodsInwardNote_GIN_Print_Details.aspx` and its code-behind `GoodsInwardNote_GIN_Print_Details.aspx.cs` primarily implements a **Read (Report Generation)** functionality.

*   **Create:** Not present. The page does not allow new GINs or GIN details to be created.
*   **Read:** This is the core functionality. Data is fetched from multiple interconnected database tables (`tblInv_Inward_Master`, `tblInv_Inward_Details`, and numerous lookup tables like `tblMM_PO_Details`, `tblDG_Item_Master`, `tblMM_Supplier_master`, etc.) based on query parameters (`GINId`, `GINo`, `ChNo`, `fyid`, `ChDt`). This complex data is then assembled into a `DataSet` which serves as the data source for the Crystal Report.
*   **Update:** Not present. The page does not modify existing GIN or GIN detail records.
*   **Delete:** Not present. The page does not delete records.
*   **Validation Logic:** Implicit validation is handled by database lookups and `DBNull.Value` checks in the C# code, ensuring that only valid IDs retrieve data. No explicit user input validation is seen on this report display page itself.

**Conclusion:** The Django migration will focus on replicating the intricate data retrieval and aggregation logic within the "fat models" to generate the report data, and then present this data efficiently using HTML templates, DataTables, and HTMX. While the user's template requires CRUD views, we will treat this specific page as a specialized "Read" (report) view, and provide generic CRUD scaffolding for `InvInwardNoteDetail` if such operations were to be added in the future.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page's UI is very minimal, focusing almost entirely on displaying the Crystal Report.

*   **`CrystalReportViewer`**: This is the primary control. It's responsible for rendering the `GIN_Print.rpt` report. In Django, this will be replaced by a custom HTML template that dynamically renders the fetched data.
*   **`CrystalReportSource`**: Defines the path to the `.rpt` file. This is a configuration detail that points to the report definition. In Django, this functionality will be replaced by the direct structuring of data within the Django template.
*   **`asp:Panel`**: Used for layout and scrollbars. These are layout containers that will be replaced by standard HTML `<div>` elements with Tailwind CSS for styling.
*   **`asp:Button ID="btncancel"`**: A simple button to navigate back. This will be converted to a standard HTML button or anchor tag with an `href` or `hx-get` attribute to redirect to the previous page.

**UI Interactions:**
*   The page itself has no complex client-side interactions beyond the "Cancel" button.
*   Data is displayed in a static report format. In Django, we will enhance this by presenting the tabular data using DataTables for client-side search, sort, and pagination, which will be initialized using standard JavaScript (not Alpine.js for DataTables itself, but Alpine.js for other UI state management).
*   The overall page layout will leverage master page inheritance, translated to Django's `{% extends 'core/base.html' %}`.

### Step 4: Generate Django Code

We will structure the Django application within an `inventory` app.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema and to support the "fat model" approach for report data assembly.

**Instructions:**
We'll define two primary models: `InvInwardNote` (for the master GIN header) and `InvInwardNoteDetail` (for the line items). `InvInwardNoteDetail` will be our primary `[MODEL_NAME]` for the generic CRUD scaffolding, but the data-rich report logic will reside in methods, primarily on `InvInwardNoteDetail` (or a manager/helper associated with it) to fetch its associated details.

```python
# inventory/models.py
from django.db import models
from django.urls import reverse

# --- Lookup/Auxiliary Models (Simplified - assume these exist or will be migrated) ---
# In a full migration, you'd map all lookup tables. For this example,
# we'll represent them minimally or just infer data.

class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True) # Assuming CompId is PK
    name = models.CharField(db_column='CompName', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_Master' # Placeholder table name for Company data
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

    @classmethod
    def get_company_address(cls, comp_id):
        """Mimics fun.CompAdd to retrieve company address."""
        try:
            company = cls.objects.get(id=comp_id)
            return company.address
        except cls.DoesNotExist:
            return "Company Address Not Found"

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year = models.CharField(db_column='FinYear', max_length=20, blank=True, null=True)
    start_date = models.DateField(db_column='FinYearFrom', blank=True, null=True)
    end_date = models.DateField(db_column='FinYearTo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year

    def get_short_year_string(self):
        """Generates YY format from financial year dates."""
        if self.start_date and self.end_date:
            return f"{self.start_date.strftime('%y')}{self.end_date.strftime('%y')}"
        return ""

class Supplier(models.Model):
    id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True) # Assuming SupplierId is PK
    name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    regd_address = models.TextField(db_column='RegdAddress', blank=True, null=True)
    regd_country_id = models.IntegerField(db_column='RegdCountry', blank=True, null=True)
    regd_state_id = models.IntegerField(db_column='RegdState', blank=True, null=True)
    regd_city_id = models.IntegerField(db_column='RegdCity', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.name} [{self.id}]"

    def get_full_address(self):
        """Constructs supplier's full address."""
        # This would ideally join with actual Country, State, City models
        # For simplicity, we'll use placeholders if models don't exist yet
        city_name = "Unknown City" # Placeholder
        state_name = "Unknown State" # Placeholder
        country_name = "Unknown Country" # Placeholder

        # In a real scenario, you'd fetch these from related models if they exist:
        # if self.regd_city_id:
        #     city = City.objects.filter(id=self.regd_city_id).first()
        #     if city: city_name = city.name
        # etc.
        return f"{self.regd_address}, {city_name}, {state_name}, {country_name}."

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    def get_uom_symbol(self):
        """Retrieves UOM symbol."""
        # Assuming Unit_Master model exists
        # In a real scenario, this would be a ForeignKey
        # For simplicity, we'll use a placeholder or direct lookup if needed
        from .models import Unit # Avoid circular import

        uom = Unit.objects.filter(id=self.uom_basic_id).first()
        return uom.symbol if uom else "N/A"

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class BusinessGroup(models.Model): # For Department symbol
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

# --- Main GIN Models ---

class InvInwardNote(models.Model):
    """Represents the Goods Inward Note Header (tblInv_Inward_Master)"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, unique=True)
    gate_entry_no = models.CharField(db_column='GateEntryNo', max_length=50, blank=True, null=True)
    gin_date = models.DateField(db_column='GDate', blank=True, null=True)
    gin_time = models.CharField(db_column='GTime', max_length=20, blank=True, null=True)
    mode_of_transport = models.CharField(db_column='ModeofTransport', max_length=100, blank=True, null=True)
    vehicle_no = models.CharField(db_column='VehicleNo', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='inward_notes', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='inward_notes', blank=True, null=True)
    # Assuming POId in tblMM_PO_Details refers to a PO master record
    # For now, we'll keep it simple as a field.
    
    # Add other fields from tblInv_Inward_Master as needed

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Goods Inward Note'
        verbose_name_plural = 'Goods Inward Notes'

    def __str__(self):
        return self.gin_no

    def get_supplier_details(self):
        """
        Retrieves supplier information based on the PO associated with this GIN.
        Mimics complex supplier lookup logic.
        """
        # This requires more complex joins from the original code (PO -> PR/SPR -> Supplier)
        # For simplicity, we'll assume a direct link or a method to find supplier ID
        # In the C# code, supId is determined from DSSql2.Tables[0].Rows[0]["SupplierId"]
        # which comes from tblMM_PO_Master.
        
        # Placeholder logic:
        # In a real scenario, you'd find the PO master associated with this GIN,
        # then find its supplier.
        supplier_id = None # Logic to derive supplier_id based on PONo, etc.
        try:
            # This is a highly simplified lookup. The actual logic would involve
            # going from GIN's PONo -> tblMM_PO_Master -> SupplierId
            # Example: Assuming InvInwardNoteDetail might contain a PO details ID.
            # Or if POId is directly in InvInwardNote or discoverable via a related PO model.
            # Given the C# code, it's: GINDetails -> PONo (on Master) -> PO_Master -> SupplierId
            # Let's assume a simplified way to get the supplier via PO.
            # This would typically involve a specific PO model or manager method.
            from .models import PurchaseOrderMaster # Assume this exists
            po_master = PurchaseOrderMaster.objects.filter(po_no=self.po_no, company=self.company).first()
            if po_master:
                supplier_id = po_master.supplier_id # Assuming supplier_id is a direct field on PO Master
            
            if supplier_id:
                supplier = Supplier.objects.get(id=supplier_id)
                return {
                    'name': supplier.get_full_address(),
                    'address': supplier.get_full_address()
                }
        except Supplier.DoesNotExist:
            pass
        return {'name': 'N/A', 'address': 'N/A'}


class InvInwardNoteDetail(models.Model):
    """
    Represents the Goods Inward Note Detail line (tblInv_Inward_Details).
    This model is central to the report's line items.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    inward_note = models.ForeignKey(InvInwardNote, on_delete=models.CASCADE, db_column='GINId', related_name='details')
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    received_quantity = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, blank=True, null=True)
    po_detail_id = models.IntegerField(db_column='POId', blank=True, null=True) # ID from tblMM_PO_Details
    asset_category_id = models.IntegerField(db_column='ACategoyId', blank=True, null=True)
    asset_sub_category_id = models.IntegerField(db_column='ASubCategoyId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Goods Inward Note Detail'
        verbose_name_plural = 'Goods Inward Note Details'

    def __str__(self):
        return f"Detail for GIN {self.inward_note.gin_no} - ID: {self.id}"

    def get_item_data(self):
        """
        Fetches ItemCode, Description, and UOM based on PO details.
        Mimics the complex lookups in C# code from PO -> PR/SPR -> Item.
        """
        item_code = "N/A"
        description = "N/A"
        uom = "N/A"
        po_qty = 0.0

        # This logic needs to mirror the complex C# SQL queries for Item details:
        # tblMM_PO_Details -> tblMM_PR_Details/tblMM_SPR_Details -> tblDG_Item_Master -> Unit_Master
        try:
            # Simplified: Assuming a direct link or a method to get item from po_detail_id
            # In a real scenario, you'd likely have models for PO_Details, PR_Details, SPR_Details etc.
            # and use Django's ORM relationships.
            from .models import PurchaseOrderDetail # Assume this exists
            po_detail = PurchaseOrderDetail.objects.filter(id=self.po_detail_id).first()
            if po_detail:
                po_qty = float(po_detail.quantity) if po_detail.quantity else 0.0
                # Now, find the actual Item from PR/SPR linked to this PO_Detail
                # This is a very complex lookup in C#, requiring multiple steps.
                # For brevity and 'fat model' principle, this would ideally be
                # handled by a specialized manager or method on PurchaseOrderDetail.
                
                # Placeholder: Direct item lookup if PO_Detail had ItemId (simplified)
                item = Item.objects.filter(id=po_detail.item_id).first() # Assuming item_id on PurchaseOrderDetail
                if item:
                    item_code = item.item_code
                    description = item.manf_desc
                    uom = item.get_uom_symbol()
        except Exception: # Catching broad exception for migration
            pass # Handle specific exceptions
        
        return {
            'item_code': item_code,
            'description': description,
            'uom': uom,
            'po_qty': po_qty,
        }

    def get_work_order_or_department(self):
        """Fetches WO No or Department from PR/SPR details."""
        wo_no = ""
        department = ""

        # Similar complex logic to get WONO/Dept from PO -> PR/SPR
        # Placeholder for now. This would depend on how PR/SPR models are mapped.
        # This information would typically be passed from the InvInwardNote object,
        # or fetched from a dedicated report manager.
        # Given the C# code, WONO and Dept are parameters set at the report level,
        # not per line item. So, this should probably be on InvInwardNote.
        return {'wo_no': wo_no, 'department': department}

    def get_asset_info(self):
        """Constructs asset number string if applicable."""
        cat = " "
        sub_cat = " "
        fy_str = " "

        if self.asset_category_id and self.asset_category_id != 0:
            from .models import AssetCategory # Assume this exists
            asset_cat = AssetCategory.objects.filter(id=self.asset_category_id).first()
            if asset_cat: cat = asset_cat.abbrivation
        
        if self.asset_sub_category_id and self.asset_sub_category_id != 0:
            from .models import AssetSubCategory # Assume this exists
            asset_sub_cat = AssetSubCategory.objects.filter(id=self.asset_sub_category_id).first()
            if asset_sub_cat: sub_cat = asset_sub_cat.abbrivation
        
        # Financial year info from the parent GIN
        if self.inward_note and self.inward_note.financial_year:
            fy_str = self.inward_note.financial_year.get_short_year_string()

        # The original code comments out AssetNo. For now, it's "NA" if not applicable.
        # If AssetNo is added to InvInwardNoteDetail later, this method would update.
        return f"{fy_str}/{cat}/{sub_cat}/NA" if (cat.strip() or sub_cat.strip() or fy_str.strip()) else "NA"

# --- Placeholder Models for relationships derived from C# ---
# These are necessary to represent the structure of the C# queries.
# They would need full field definitions in a complete migration.

class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True) # Direct field for simplicity
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1, blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_master = models.ForeignKey(PurchaseOrderMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    po_no = models.CharField(db_column='PONo', max_length=50) # Redundant
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    spr_id = models.IntegerField(db_column='SPRId', blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # Placeholder for Item relationship

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'

class PurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    work_order_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'

class StorePurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    work_order_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept_id = models.IntegerField(db_column='DeptId', blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'

class AssetCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    abbrivation = models.CharField(db_column='Abbrivation', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_Category'

class AssetSubCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    abbrivation = models.CharField(db_column='Abbrivation', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_SubCategory'

# The C# code also references tblcountry, tblState, tblCity.
# We assume these would also be managed=False models for full integration.

```

#### 4.2 Forms

**Task:** Define a Django form for `InvInwardNoteDetail` for potential CRUD operations, even though this specific ASP.NET page is a report.

**Instructions:**
A basic `ModelForm` is provided. For a report page, direct form submission for details is less common, but this satisfies the template's requirement for form generation.

```python
# inventory/forms.py
from django import forms
from .models import InvInwardNoteDetail, InvInwardNote

class InvInwardNoteDetailForm(forms.ModelForm):
    # If the form is used for adding/editing details, you'd likely select the parent GIN
    inward_note = forms.ModelChoiceField(
        queryset=InvInwardNote.objects.all(),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select GIN"
    )

    class Meta:
        model = InvInwardNoteDetail
        fields = ['inward_note', 'quantity', 'received_quantity', 'po_detail_id', 'asset_category_id', 'asset_sub_category_id']
        widgets = {
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'received_quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_detail_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'asset_category_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'asset_sub_category_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'inward_note': 'Goods Inward Note',
            'quantity': 'Challan Quantity',
            'received_quantity': 'Received Quantity',
            'po_detail_id': 'PO Detail ID',
            'asset_category_id': 'Asset Category ID',
            'asset_sub_category_id': 'Asset Sub Category ID',
        }

    # Add custom validation methods here if needed
```

#### 4.3 Views

**Task:** Implement the main report view (`GINReportView`) and generic CRUD operations for `InvInwardNoteDetail` using Class-Based Views (CBVs), adhering to the "thin view" principle. The complex data retrieval logic will reside in model methods.

```python
# inventory/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import InvInwardNoteDetail, InvInwardNote, Company, FinancialYear, Supplier # Import all needed models
from .forms import InvInwardNoteDetailForm
from datetime import datetime

class GINReportView(TemplateView):
    """
    Main view for the Goods Inward Note Report.
    This replaces the ASP.NET Crystal Report Viewer.
    It gathers all necessary header and detail data for a specific GIN.
    """
    template_name = 'inventory/invinwardnote/gin_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract query parameters mirroring ASP.NET's Request.QueryString
        gin_id = self.request.GET.get('Id')
        gin_no = self.request.GET.get('GINo')
        challan_no = self.request.GET.get('ChNo')
        challan_date_str = self.request.GET.get('ChDt') # "DD-MM-YYYY" or similar
        
        # Required session variables from ASP.NET
        comp_id = self.request.session.get('compid') # From request.user.company.id or similar
        session_fy_id = self.request.session.get('finyear') # From request.user.financial_year.id or similar

        if not all([gin_id, gin_no, challan_date_str]):
            raise Http404("Missing required GIN report parameters.")

        # --- Data Aggregation Logic (Fat Model principle: most should be in models) ---
        try:
            # 1. Get the main GIN header (InvInwardNote)
            inward_note = get_object_or_404(
                InvInwardNote,
                id=gin_id,
                gin_no=gin_no,
                company__id=comp_id # Assuming ForeignKey link and CompId
            )
            context['inward_note'] = inward_note

            # 2. Get Supplier Details
            supplier_info = inward_note.get_supplier_details()
            context['supplier_name'] = supplier_info['name']
            context['supplier_address'] = supplier_info['address']

            # 3. Get Company Address
            company_address = Company.get_company_address(comp_id) # Using a class method on Company model
            context['company_address'] = company_address

            # 4. Process Challan Date
            try:
                # Assuming fun.FromDateDMY converts to DD-MM-YYYY
                # Django's template handles date formatting, so pass as datetime object
                challan_date = datetime.strptime(challan_date_str, '%d-%m-%Y').date()
            except ValueError:
                challan_date = None # Handle invalid date format
            context['challan_date'] = challan_date
            
            # 5. Set other header parameters
            # WONO and Dept are complex derived values in C#, likely from PR/SPR linked to PO
            # For simplicity, these would typically be fetched by the InvInwardNote model itself
            # or a dedicated service. Here we'll just set placeholders for now.
            context['work_order_no'] = "N/A" # Derived from PO -> PR/SPR
            context['department'] = "N/A" # Derived from PO -> SPR
            context['gin_no_param'] = gin_no
            context['challan_no_param'] = challan_no

            # 6. Fetch InvInwardNoteDetail line items for this GIN
            # The DataTables partial view will handle loading and rendering these details
            # This main view only sets the context for the *header* and triggers the partial load.
            # The partial view will then fetch the details for the specific GIN.
            
            # For the main view, we might want to pass the GIN ID to the table partial
            context['gin_id_for_details'] = gin_id

        except InvInwardNote.DoesNotExist:
            raise Http404("Goods Inward Note not found.")
        except Exception as e:
            messages.error(self.request, f"An error occurred while generating report: {e}")
            raise Http404("Error retrieving report data.")
        
        return context

class InvInwardNoteDetailListView(ListView):
    """
    Renders the DataTables partial for InvInwardNoteDetail.
    This view will be called by HTMX to populate the table.
    """
    model = InvInwardNoteDetail
    template_name = 'inventory/invinwardnote/_invinwardnotedetail_table.html'
    context_object_name = 'invinwardnotedetails'

    def get_queryset(self):
        """
        Filters details by GIN ID from query parameters to mimic report.
        """
        gin_id = self.request.GET.get('gin_id')
        if gin_id:
            queryset = InvInwardNoteDetail.objects.filter(inward_note__id=gin_id)
        else:
            queryset = InvInwardNoteDetail.objects.none() # Return empty if no GIN ID
        
        # Enrich each detail object with calculated properties (ItemCode, Description, etc.)
        # This is the "fat model" part - the properties are computed on the model instances
        # for efficient display.
        for detail in queryset:
            item_data = detail.get_item_data()
            detail.item_code = item_data['item_code']
            detail.description = item_data['description']
            detail.uom = item_data['uom']
            detail.po_qty = item_data['po_qty']
            detail.asset_info = detail.get_asset_info()
            
        return queryset

    # For HTMX requests, we just return the partial HTML
    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return super().render_to_response(context, **response_kwargs)
        return HttpResponse("This endpoint is for HTMX requests only.", status=400)


# --- Generic CRUD Views for InvInwardNoteDetail (as per template instructions) ---

class InvInwardNoteDetailCreateView(CreateView):
    model = InvInwardNoteDetail
    form_class = InvInwardNoteDetailForm
    template_name = 'inventory/invinwardnote/_invinwardnotedetail_form.html'
    success_url = reverse_lazy('invinwardnotedetail_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Inward Note Detail added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInvInwardNoteDetailList'
                }
            )
        return response

class InvInwardNoteDetailUpdateView(UpdateView):
    model = InvInwardNoteDetail
    form_class = InvInwardNoteDetailForm
    template_name = 'inventory/invinwardnote/_invinwardnotedetail_form.html'
    success_url = reverse_lazy('invinwardnotedetail_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Inward Note Detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInvInwardNoteDetailList'
                }
            )
        return response

class InvInwardNoteDetailDeleteView(DeleteView):
    model = InvInwardNoteDetail
    template_name = 'inventory/invinwardnote/_invinwardnotedetail_confirm_delete.html'
    success_url = reverse_lazy('invinwardnotedetail_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Goods Inward Note Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshInvInwardNoteDetailList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for the report view and the generic CRUD operations, ensuring HTMX, Alpine.js, and DataTables integration.

**Instructions:**
The main report view (`gin_report.html`) will display header information and use HTMX to load the details table (`_invinwardnotedetail_table.html`). The CRUD forms will be partials loaded into a modal.

```html
{# inventory/templates/inventory/invinwardnote/gin_report.html #}
{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Inward Note - Print{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg mb-4 shadow-md">
        <h2 class="text-xl font-bold">Goods Inward Note [GIN] - Print</h2>
    </div>

    {# GIN Header Information #}
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">GIN Details Overview</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
            <div><strong>GIN No:</strong> {{ inward_note.gin_no }} ({{ gin_no_param }})</div>
            <div><strong>Challan No:</strong> {{ challan_no_param }}</div>
            <div><strong>Challan Date:</strong> {{ challan_date|date:"d-M-Y" }}</div>
            <div><strong>Gate Entry No:</strong> {{ inward_note.gate_entry_no }}</div>
            <div><strong>GIN Date:</strong> {{ inward_note.gin_date|date:"d-M-Y" }}</div>
            <div><strong>GIN Time:</strong> {{ inward_note.gin_time }}</div>
            <div><strong>Mode of Transport:</strong> {{ inward_note.mode_of_transport }}</div>
            <div><strong>Vehicle No:</strong> {{ inward_note.vehicle_no }}</div>
            <div><strong>Work Order No:</strong> {{ work_order_no }}</div>
            <div><strong>Department:</strong> {{ department }}</div>
            <div><strong>Supplier Name:</strong> {{ supplier_name }}</div>
            <div class="col-span-1 md:col-span-2"><strong>Supplier Address:</strong> {{ supplier_address }}</div>
            <div class="col-span-1 md:col-span-2"><strong>Company Address:</strong> {{ company_address }}</div>
        </div>
    </div>

    {# GIN Detail Items (Loaded via HTMX into a DataTable) #}
    <div id="invinwardnotedetail-table-container"
         hx-trigger="load, refreshInvInwardNoteDetailList from:body"
         hx-get="{% url 'invinwardnotedetail_table' %}?gin_id={{ inward_note.id }}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">GIN Line Items</h3>
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading GIN details...</p>
        </div>
    </div>

    <div class="flex justify-center mt-6">
        <a href="{% url 'previous_gin_list_page' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow">
            Cancel
        </a>
    </div>
</div>

{# Modal for generic CRUD forms (if any are added later) #}
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for general UI state
        // E.g., for modal open/close if not solely driven by hx-trigger on body
    });
</script>
{% endblock %}

```

```html
{# inventory/templates/inventory/invinwardnote/_invinwardnotedetail_table.html #}
<table id="invinwardnotedetailTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset No</th>
            {% comment %}
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            {% endcomment %}
        </tr>
    </thead>
    <tbody>
        {% for obj in invinwardnotedetails %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.uom }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.po_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.received_quantity|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.quantity|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.asset_info }}</td>
            {% comment %}
            {# Actions column for generic CRUD if needed, not directly for report page #}
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'invinwardnotedetail_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'invinwardnotedetail_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
            {% endcomment %}
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    $('#invinwardnotedetailTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "dom": 'lfrtip', // Show length menu, filter, table, pagination, info
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": true
    });
});
</script>
```

```html
{# inventory/templates/inventory/invinwardnote/_invinwardnotedetail_form.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Inward Note Detail</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# inventory/templates/inventory/invinwardnote/_invinwardnotedetail_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this Goods Inward Note Detail (ID: {{ invinwardnotedetail.id }})?</p>
    
    <form hx-post="{% url 'invinwardnotedetail_delete' invinwardnotedetail.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views, including the main report view and the generic CRUD for `InvInwardNoteDetail`.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    GINReportView,
    InvInwardNoteDetailListView, # This serves the HTMX partial for the report details table
    InvInwardNoteDetailCreateView,
    InvInwardNoteDetailUpdateView,
    InvInwardNoteDetailDeleteView,
)

urlpatterns = [
    # Main GIN Report URL (replaces original ASPX page)
    path('gin-report/', GINReportView.as_view(), name='gin_report'),
    
    # HTMX endpoint for the GIN detail table (called by gin_report)
    path('invinwardnotedetail/table/', InvInwardNoteDetailListView.as_view(), name='invinwardnotedetail_table'),

    # Generic CRUD URLs for InvInwardNoteDetail (if needed in other parts of app)
    # The list view here would be a separate, full-page list, not the report partial
    # path('invinwardnotedetail/', InvInwardNoteDetailListView.as_view(), name='invinwardnotedetail_list'), # If full page list needed
    path('invinwardnotedetail/add/', InvInwardNoteDetailCreateView.as_view(), name='invinwardnotedetail_add'),
    path('invinwardnotedetail/edit/<int:pk>/', InvInwardNoteDetailUpdateView.as_view(), name='invinwardnotedetail_edit'),
    path('invinwardnotedetail/delete/<int:pk>/', InvInwardNoteDetailDeleteView.as_view(), name='invinwardnotedetail_delete'),

    # Placeholder for the "Cancel" button redirect
    path('previous-gin-list-page/', TemplateView.as_view(template_name='inventory/previous_gin_list_page.html'), name='previous_gin_list_page'), # Placeholder
]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the `InvInwardNoteDetail` model and the `GINReportView`, ensuring high test coverage.

**Instructions:**
We'll include unit tests for model methods and integration tests for views, mocking database interactions where necessary.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from unittest.mock import patch, MagicMock

# Import all models to ensure they are loadable for testing purposes
from .models import (
    InvInwardNoteDetail, InvInwardNote, Company, FinancialYear, Supplier, Item, Unit,
    PurchaseOrderMaster, PurchaseOrderDetail, PurchaseRequisitionDetail,
    StorePurchaseRequisitionDetail, AssetCategory, AssetSubCategory, BusinessGroup
)

class InvInwardNoteDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for testing
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St')
        cls.fin_year = FinancialYear.objects.create(id=1, year='2023-2024', start_date='2023-04-01', end_date='2024-03-31')
        cls.supplier = Supplier.objects.create(id='SUP001', name='Test Supplier', regd_address='456 Supplier Ave', regd_country_id=1, regd_state_id=1, regd_city_id=1)
        cls.item_uom = Unit.objects.create(id=1, symbol='PCS')
        cls.item = Item.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Description', uom_basic_id=cls.item_uom.id)
        cls.po_master = PurchaseOrderMaster.objects.create(id=1, po_no='PO001', supplier_id=cls.supplier.id, company=cls.company, financial_year=cls.fin_year)
        cls.po_detail = PurchaseOrderDetail.objects.create(id=1, po_master=cls.po_master, po_no='PO001', quantity=100.000, item_id=cls.item.id)
        cls.asset_cat = AssetCategory.objects.create(id=1, abbrivation='ELEC')
        cls.asset_sub_cat = AssetSubCategory.objects.create(id=1, abbrivation='COMP')

        # Create the main GIN and GIN Detail for tests
        cls.inward_note = InvInwardNote.objects.create(
            id=1,
            gin_no='GIN001',
            gate_entry_no='GE001',
            gin_date='2023-10-26',
            gin_time='10:00',
            mode_of_transport='Road',
            vehicle_no='XYZ123',
            po_no='PO001',
            company=cls.company,
            financial_year=cls.fin_year
        )
        cls.detail = InvInwardNoteDetail.objects.create(
            id=1,
            inward_note=cls.inward_note,
            quantity=50.000,
            received_quantity=45.000,
            po_detail_id=cls.po_detail.id,
            asset_category_id=cls.asset_cat.id,
            asset_sub_category_id=cls.asset_sub_cat.id
        )
  
    def test_invinwardnotedetail_creation(self):
        detail = InvInwardNoteDetail.objects.get(id=1)
        self.assertEqual(detail.quantity, 50.000)
        self.assertEqual(detail.received_quantity, 45.000)
        self.assertEqual(detail.inward_note.gin_no, 'GIN001')
        
    def test_item_data_method(self):
        detail = InvInwardNoteDetail.objects.get(id=1)
        # Mock PurchaseOrderDetail.objects.filter().first() to return our test po_detail
        with patch('inventory.models.PurchaseOrderDetail.objects.filter') as mock_filter_po_detail:
            mock_filter_po_detail.return_value.first.return_value = self.po_detail
            with patch('inventory.models.Item.objects.filter') as mock_filter_item:
                mock_filter_item.return_value.first.return_value = self.item
                with patch('inventory.models.Unit.objects.filter') as mock_filter_unit:
                    mock_filter_unit.return_value.first.return_value = self.item_uom
                    item_data = detail.get_item_data()
                    self.assertEqual(item_data['item_code'], 'ITEM001')
                    self.assertEqual(item_data['description'], 'Test Item Description')
                    self.assertEqual(item_data['uom'], 'PCS')
                    self.assertEqual(item_data['po_qty'], 100.0)

    def test_asset_info_method(self):
        detail = InvInwardNoteDetail.objects.get(id=1)
        with patch('inventory.models.AssetCategory.objects.filter') as mock_cat:
            mock_cat.return_value.first.return_value = self.asset_cat
            with patch('inventory.models.AssetSubCategory.objects.filter') as mock_sub_cat:
                mock_sub_cat.return_value.first.return_value = self.asset_sub_cat
                # The financial_year is already linked via inward_note
                asset_info = detail.get_asset_info()
                self.assertEqual(asset_info, f"{self.fin_year.get_short_year_string()}/ELEC/COMP/NA")

    def test_get_supplier_details_method(self):
        # This method is on InvInwardNote
        inward_note = InvInwardNote.objects.get(id=1)
        with patch('inventory.models.PurchaseOrderMaster.objects.filter') as mock_po_master_filter:
            mock_po_master_filter.return_value.first.return_value = self.po_master
            with patch('inventory.models.Supplier.objects.get') as mock_supplier_get:
                mock_supplier_get.return_value = self.supplier
                supplier_info = inward_note.get_supplier_details()
                self.assertIn('Test Supplier', supplier_info['name'])
                self.assertIn('456 Supplier Ave', supplier_info['address'])

    def test_get_company_address_classmethod(self):
        # This method is on Company
        address = Company.get_company_address(self.company.id)
        self.assertEqual(address, '123 Test St')

class GINReportViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal data for view tests
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St')
        cls.fin_year = FinancialYear.objects.create(id=1, year='2023-2024', start_date='2023-04-01', end_date='2024-03-31')
        cls.supplier = Supplier.objects.create(id='SUP001', name='Test Supplier', regd_address='456 Supplier Ave', regd_country_id=1, regd_state_id=1, regd_city_id=1)
        cls.item_uom = Unit.objects.create(id=1, symbol='PCS')
        cls.item = Item.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Description', uom_basic_id=cls.item_uom.id)
        cls.po_master = PurchaseOrderMaster.objects.create(id=1, po_no='PO001', supplier_id=cls.supplier.id, company=cls.company, financial_year=cls.fin_year)
        cls.po_detail = PurchaseOrderDetail.objects.create(id=1, po_master=cls.po_master, po_no='PO001', quantity=100.000, item_id=cls.item.id)
        cls.asset_cat = AssetCategory.objects.create(id=1, abbrivation='ELEC')
        cls.asset_sub_cat = AssetSubCategory.objects.create(id=1, abbrivation='COMP')

        cls.inward_note = InvInwardNote.objects.create(
            id=1,
            gin_no='GIN001',
            gate_entry_no='GE001',
            gin_date='2023-10-26',
            gin_time='10:00',
            mode_of_transport='Road',
            vehicle_no='XYZ123',
            po_no='PO001',
            company=cls.company,
            financial_year=cls.fin_year
        )
        cls.detail1 = InvInwardNoteDetail.objects.create(
            id=1, inward_note=cls.inward_note, quantity=50.000, received_quantity=45.000,
            po_detail_id=cls.po_detail.id, asset_category_id=cls.asset_cat.id, asset_sub_category_id=cls.asset_sub_cat.id
        )
        cls.detail2 = InvInwardNoteDetail.objects.create(
            id=2, inward_note=cls.inward_note, quantity=20.000, received_quantity=20.000,
            po_detail_id=cls.po_detail.id, asset_category_id=None, asset_sub_category_id=None
        )
    
    def setUp(self):
        self.client = Client()
        self.session = self.client.session
        self.session['compid'] = self.company.id
        self.session['finyear'] = self.fin_year.id
        self.session.save()

        # Mock dependent methods to control test environment
        patcher_get_supplier_details = patch.object(InvInwardNote, 'get_supplier_details', return_value={'name': 'Mock Supplier Name', 'address': 'Mock Supplier Address'})
        self.mock_get_supplier_details = patcher_get_supplier_details.start()
        self.addCleanup(patcher_get_supplier_details.stop)

        patcher_get_company_address = patch.object(Company, 'get_company_address', return_value='Mock Company Address')
        self.mock_get_company_address = patcher_get_company_address.start()
        self.addCleanup(patcher_get_company_address.stop)

        patcher_get_item_data = patch.object(InvInwardNoteDetail, 'get_item_data', return_value={'item_code': 'MOCK_ITEM', 'description': 'MOCK_DESC', 'uom': 'MOCK_UOM', 'po_qty': 99.0})
        self.mock_get_item_data = patcher_get_item_data.start()
        self.addCleanup(patcher_get_item_data.stop)

        patcher_get_asset_info = patch.object(InvInwardNoteDetail, 'get_asset_info', return_value='MOCK/ASSET/INFO')
        self.mock_get_asset_info = patcher_get_asset_info.start()
        self.addCleanup(patcher_get_asset_info.stop)

    def test_gin_report_view_success(self):
        url = reverse('gin_report') + f'?Id={self.inward_note.id}&GINo={self.inward_note.gin_no}&ChNo=CH001&ChDt=26-10-2023'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/invinwardnote/gin_report.html')
        self.assertIn('inward_note', response.context)
        self.assertEqual(response.context['inward_note'], self.inward_note)
        self.assertEqual(response.context['gin_no_param'], 'GIN001')
        self.assertEqual(response.context['supplier_name'], 'Mock Supplier Name')
        self.assertEqual(response.context['company_address'], 'Mock Company Address')
        self.assertEqual(response.context['challan_date'], date(2023, 10, 26))
        self.assertContains(response, 'Goods Inward Note [GIN] - Print')
        self.assertContains(response, 'Loading GIN details...') # HTMX loading message

    def test_gin_report_view_missing_params(self):
        url = reverse('gin_report') + f'?Id={self.inward_note.id}&GINo={self.inward_note.gin_no}' # Missing ChNo, ChDt
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Http404 due to missing parameters

    def test_gin_report_view_gin_not_found(self):
        url = reverse('gin_report') + f'?Id=9999&GINo=NONEXISTENT&ChNo=CH001&ChDt=26-10-2023'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_invinwardnotedetail_table_partial_view_success(self):
        url = reverse('invinwardnotedetail_table') + f'?gin_id={self.inward_note.id}'
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/invinwardnote/_invinwardnotedetail_table.html')
        self.assertIn('invinwardnotedetails', response.context)
        self.assertEqual(len(response.context['invinwardnotedetails']), 2)
        self.assertContains(response, 'MOCK_ITEM')
        self.assertContains(response, 'MOCK_DESC')
        self.assertContains(response, 'MOCK_UOM')
        self.assertContains(response, 'MOCK/ASSET/INFO')

    def test_invinwardnotedetail_table_partial_view_no_htmx(self):
        url = reverse('invinwardnotedetail_table') + f'?gin_id={self.inward_note.id}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 400) # Should return 400 if not HTMX request

    def test_invinwardnotedetail_create_view_get(self):
        response = self.client.get(reverse('invinwardnotedetail_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/invinwardnote/_invinwardnotedetail_form.html')
        self.assertTrue('form' in response.context)
        
    def test_invinwardnotedetail_create_view_post_htmx(self):
        data = {
            'inward_note': self.inward_note.id, # Ensure parent GIN is selected
            'quantity': 75.000,
            'received_quantity': 70.000,
            'po_detail_id': self.po_detail.id,
            'asset_category_id': self.asset_cat.id,
            'asset_sub_category_id': self.asset_sub_cat.id,
        }
        response = self.client.post(reverse('invinwardnotedetail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(InvInwardNoteDetail.objects.filter(quantity=75.000).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInvInwardNoteDetailList')
        
    def test_invinwardnotedetail_update_view_get(self):
        obj = InvInwardNoteDetail.objects.get(id=1)
        response = self.client.get(reverse('invinwardnotedetail_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/invinwardnote/_invinwardnotedetail_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_invinwardnotedetail_update_view_post_htmx(self):
        obj = InvInwardNoteDetail.objects.get(id=1)
        data = {
            'inward_note': self.inward_note.id,
            'quantity': 55.000, # Updated value
            'received_quantity': 50.000,
            'po_detail_id': self.po_detail.id,
            'asset_category_id': self.asset_cat.id,
            'asset_sub_category_id': self.asset_sub_cat.id,
        }
        response = self.client.post(reverse('invinwardnotedetail_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.quantity, 55.000)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInvInwardNoteDetailList')

    def test_invinwardnotedetail_delete_view_get(self):
        obj = InvInwardNoteDetail.objects.get(id=1)
        response = self.client.get(reverse('invinwardnotedetail_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/invinwardnote/_invinwardnotedetail_confirm_delete.html')
        self.assertIn('invinwardnotedetail', response.context)
        self.assertEqual(response.context['invinwardnotedetail'], obj)
        
    def test_invinwardnotedetail_delete_view_post_htmx(self):
        obj = InvInwardNoteDetail.objects.get(id=1)
        response = self.client.post(reverse('invinwardnotedetail_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(InvInwardNoteDetail.objects.filter(id=obj.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInvInwardNoteDetailList')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content:**
    *   The `GINReportView` renders the header part of the report. The dynamic detail table is loaded via HTMX (`hx-get`) into a `div#invinwardnotedetail-table-container`.
    *   The `invinwardnotedetailTable` partial (`_invinwardnotedetail_table.html`) is loaded on page `load` and `refreshInvInwardNoteDetailList` custom event.
    *   Generic CRUD forms (Add, Edit, Delete) for `InvInwardNoteDetail` are loaded into a modal using HTMX `hx-get` and `hx-target="#modalContent"`.
    *   Form submissions (`hx-post`) on the modal forms trigger `hx-swap="none"` and send an `HX-Trigger` header (`refreshInvInwardNoteDetailList`) upon success, which then causes the detail table to reload, ensuring the main page reflects changes without a full refresh.
    *   The "Cancel" button is a simple link `<a>` for navigation.
*   **Alpine.js for UI State:**
    *   Alpine.js is used to manage the modal's visibility (`_="on click add .is-active to #modal"` and `remove .is-active from me`). This provides a clean way to control the modal's state based on user interaction with HTMX-triggered buttons.
*   **DataTables for List Views:**
    *   The `_invinwardnotedetail_table.html` partial directly initializes DataTables on the `invinwardnotedetailTable` using a `<script>` tag. This provides client-side search, sorting, and pagination for the GIN line items.
*   **No Custom JavaScript (beyond DataTables/Alpine initialization):**
    *   All interactions are handled by HTMX attributes or simple Alpine.js directives, minimizing manual JavaScript code.
*   **DRY Templates:**
    *   `gin_report.html` extends `core/base.html`.
    *   `_invinwardnotedetail_table.html`, `_invinwardnotedetail_form.html`, and `_invinwardnotedetail_confirm_delete.html` are partials, designed to be loaded dynamically via HTMX into specific containers or modals.

### Final Notes

This modernization plan provides a structured, automated-friendly approach to migrating your ASP.NET Crystal Report page to Django. By adhering to the "fat model, thin view" principle, leveraging modern frontend technologies like HTMX and Alpine.js, and ensuring comprehensive testing, we aim to deliver a robust, maintainable, and high-performance solution. The focus has been on replicating the report generation logic and presenting it dynamically, while also providing the necessary scaffolding for generic CRUD operations as per the framework guidelines.

**Key Benefits of this Django Modernization:**
*   **Enhanced Performance:** Direct HTML rendering bypasses heavy Crystal Reports processing, leading to faster page loads.
*   **Improved User Experience:** HTMX and Alpine.js provide seamless, dynamic updates without full page refreshes, making the application feel snappier and more interactive.
*   **Simplified Maintenance:** Django's structured MVC/MVT pattern, clear separation of concerns, and robust ORM make the codebase easier to understand, maintain, and extend.
*   **Modern Technology Stack:** Transitioning to Django, HTMX, and Tailwind CSS aligns your application with modern web development best practices, ensuring future compatibility and easier talent acquisition.
*   **Scalability:** Django's architecture is inherently scalable, capable of handling increased user loads and data volumes.
*   **Testability:** The modular design with fat models and thin views, coupled with comprehensive unit and integration tests, ensures high code quality and reliability.