## ASP.NET to Django Conversion Script: Goods Inward Note (GIN) - Edit Module

This document outlines the modernization plan for the "Goods Inward Note [GIN] - Edit" module, transitioning it from ASP.NET to a modern Django-based solution. Our approach prioritizes automation-driven processes, ensuring a robust, scalable, and maintainable application with minimal manual effort.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists as `core/base.html`.
- Focus ONLY on component-specific code for the current module (`inventory` application).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several database tables:
*   **`tblInv_Inward_Master`**: This is the primary table for Goods Inward Note records. It's updated directly for `ChallanNo` and `ChallanDate`, and its `Id` column is used as a `DataKeyName` for editing. The `Sp_GIN_Edit` stored procedure also targets this table.
    *   **Inferred Columns:** `Id` (PK), `FinYear`, `PONo`, `GINNo`, `SysDate`, `ChallanNo` (from `ChNO`), `ChallanDate` (from `ChDT`).
    *   **Inferred Foreign Key/Related Data:** `Supplier` name is displayed and used for searching. This implies a link to `tblMM_Supplier_master` via `SupplierId`. `CompId` (Company ID) is used globally for filtering.
*   **`tblMM_Supplier_master`**: Used for the supplier autocomplete functionality and for displaying supplier names in the grid.
    *   **Inferred Columns:** `SupplierId` (PK), `SupplierName`, `CompId`.
*   **`tblMM_PO_Master`**: Mentioned in search logic (`tblMM_PO_Master.SupplierId`) for filtering by supplier, suggesting it might be an intermediary or the source of `PONo` in the `GIN` records. We'll model `SupplierId` directly on `tblInv_Inward_Master` for simplicity if a direct link is inferred.
*   **`tblinv_MaterialReceived_Master`**: Checked (`GINId`) to determine if a GIN can be edited (GRR check).
    *   **Inferred Columns:** `Id` (PK), `GINId` (FK to `tblInv_Inward_Master`), `CompId`.
*   **`tblinv_MaterialServiceNote_Master`**: Checked (`GINId`) to determine if a GIN can be edited (GSN check).
    *   **Inferred Columns:** `Id` (PK), `GINId` (FK to `tblInv_Inward_Master`), `CompId`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
*   **Read (Listing/Searching):** The `loadData` method, utilizing the `Sp_GIN_Edit` stored procedure, fetches GIN records. The `Button1_Click` event applies dynamic filters based on `Supplier Name`, `PO No`, or `GIN No`. The `GridView` displays these records with pagination.
*   **Update:** The `GridView2_RowUpdating` event handles the modification of `ChallanNo` and `ChallanDate` for a specific GIN record. There's validation for required fields (`Challan No` and `Challan Date`).
*   **Conditional Editability:** The `disableEdit` method, called during page load and row data binding, checks for the existence of related records in `tblinv_MaterialReceived_Master` and `tblinv_MaterialServiceNote_Master`. If such records exist, the "Edit" link is disabled.
*   **Redirect (Details View):** The `GridView2_RowCommand` for `CommandName="sel"` redirects the user to `GoodsInwardNote_GIN_Edit_Details.aspx` with various query parameters (`Id`, `GNo`, `ChNo`, `ChDt`, `fyid`, `SupId`, `PoNo`), indicating a transition to a detailed view of the selected GIN.
*   **Autocomplete:** The static `sql` method provides supplier name suggestions for the `AutoCompleteExtender`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
*   **Search Controls:**
    *   `DropDownList1`: A dropdown to select the search criterion (`Supplier Name`, `PO No`, `GIN No`). Its `AutoPostBack="True"` indicates dynamic UI changes on selection.
    *   `txtEnqId`: A `TextBox` for `PO No` or `GIN No` search.
    *   `txtSupplier`: A `TextBox` for `Supplier Name` search, enhanced with `AutoCompleteExtender`.
    *   `Button1`: A "Search" button to trigger data filtering.
*   **Data Display:**
    *   `GridView2`: The main data table, configured for `AutoGenerateColumns="False"`, `AllowPaging="True"`, and various row-level events for editing (`onrowediting`, `onrowupdating`) and command handling (`onrowcommand`).
    *   `TemplateField` controls: Used to display data (`Label`) and provide action links (`LinkButton` for Edit/Update/Cancel, Select).
    *   `EditItemTemplate`: Provides `TextBox` controls for `Challan No` and `Challan Date` during inline editing, along with `CalendarExtender` for date selection and `RequiredFieldValidator` for client-side validation.
*   **Styling:** References `Css/StyleSheet.css`, `Css/yui-datatable.css`, and inline styles. Tailwind CSS will be used in Django.

---

### Step 4: Generate Django Code

We will create a Django application named `inventory` to house this module.

#### 4.1 Models (`inventory/models.py`)

This file will define the Django models that map to the existing database tables. We use `managed = False` to ensure Django works with the existing schema and `db_table` to specify the exact table names.

```python
from django.db import models
from django.db.models import Q # For complex queries in managers

class Supplier(models.Model):
    """
    Corresponds to tblMM_Supplier_master for supplier details.
    Used for autocomplete and linking to GIN records.
    """
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True) # Company ID for filtering

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class MaterialReceived(models.Model):
    """
    Corresponds to tblinv_MaterialReceived_Master, used to check GIN editability.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    gin = models.ForeignKey('GoodsInwardNote', models.DO_NOTHING, db_column='GINId', related_name='material_received_notes')
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received Note'
        verbose_name_plural = 'Material Received Notes'

    def __str__(self):
        return f"MRN for GIN {self.gin.gin_no}"

class MaterialServiceNote(models.Model):
    """
    Corresponds to tblinv_MaterialServiceNote_Master, used to check GIN editability.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    gin = models.ForeignKey('GoodsInwardNote', models.DO_NOTHING, db_column='GINId', related_name='material_service_notes')
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'
        verbose_name = 'Material Service Note'
        verbose_name_plural = 'Material Service Notes'

    def __str__(self):
        return f"MSN for GIN {self.gin.gin_no}"

class GoodsInwardNoteManager(models.Manager):
    """
    Custom manager for GoodsInwardNote to encapsulate complex query logic,
    mimicking the Sp_GIN_Edit stored procedure and search functionality.
    """
    def get_queryset(self):
        # Select related supplier_obj for efficient access to supplier name
        return super().get_queryset().select_related('supplier_obj')

    def filter_by_criteria(self, company_id, financial_year_id, search_type, search_value):
        """
        Filters GIN records based on company, financial year, and search criteria.
        """
        qs = self.get_queryset().filter(comp_id=company_id, fin_year=financial_year_id)

        if search_type == '0' and search_value: # Supplier Name
            # The search_value from autocomplete is "Supplier Name [SupplierId]"
            # Extract SupplierId and filter
            try:
                # Attempt to extract SupplierId from the formatted string
                supplier_id_part = search_value.split('[')[-1]
                supplier_id = supplier_id_part.replace(']', '')
                qs = qs.filter(supplier_obj__supplier_id=supplier_id)
            except (IndexError, AttributeError):
                # Fallback if format is not as expected, search by name contains
                qs = qs.filter(supplier_obj__supplier_name__icontains=search_value)
        elif search_type == '1' and search_value: # PO No
            qs = qs.filter(po_no__icontains=search_value)
        elif search_type == '2' and search_value: # GIN No
            qs = qs.filter(gin_no__icontains=search_value)
        
        return qs.order_by('-sys_date', '-id') # Order by date descending, then ID to ensure consistent paging

class GoodsInwardNote(models.Model):
    """
    Corresponds to tblInv_Inward_Master, the main GIN record.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10)
    po_no = models.CharField(db_column='PONo', max_length=50)
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    
    # Assuming tblInv_Inward_Master has a SupplierId that links to tblMM_Supplier_master
    # The 'SupplierId' field is the foreign key, and 'supplier_obj' is the related object
    supplier_obj = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId', related_name='goods_inward_notes_as_supplier', to_field='supplier_id', blank=True, null=True)

    challan_no = models.CharField(db_column='ChallanNo', max_length=100, blank=True, null=True)
    challan_date = models.DateField(db_column='ChallanDate', blank=True, null=True)

    comp_id = models.IntegerField(db_column='CompId') # Company ID, typically from session

    objects = GoodsInwardNoteManager() # Use our custom manager

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Goods Inward Note'
        verbose_name_plural = 'Goods Inward Notes'

    def __str__(self):
        return f"{self.gin_no} - {self.supplier_obj.supplier_name if self.supplier_obj else 'N/A'}"
        
    def has_material_received_notes(self, company_id):
        """
        Checks if there are related material received notes (GRR) for this GIN.
        Corresponds to `DSGRRcheck.Tables[0].Rows.Count > 0` logic in ASP.NET.
        """
        return self.material_received_notes.filter(comp_id=company_id).exists()

    def has_material_service_notes(self, company_id):
        """
        Checks if there are related material service notes (GSN) for this GIN.
        Corresponds to `DSGSNcheck.Tables[0].Rows.Count > 0` logic in ASP.NET.
        """
        return self.material_service_notes.filter(comp_id=company_id).exists()

    def can_be_edited(self, company_id):
        """
        Determines if the GIN can be edited based on GRR and GSN existence.
        Corresponds to the `disableEdit` logic in ASP.NET.
        """
        return not (self.has_material_received_notes(company_id) or self.has_material_service_notes(company_id))

    def update_challan_details(self, challan_no, challan_date, company_id):
        """
        Updates challan details if GIN is editable and data is valid.
        Corresponds to GridView2_RowUpdating logic in ASP.NET.
        Returns a tuple: (success_boolean, message_string).
        """
        if not self.can_be_edited(company_id):
            return False, "This GIN cannot be edited as it has associated GRR/GSN records."

        # Mimic ASP.NET validation: "if (ChNo != "0" && ChNo != "" && ChDt != "")"
        if not challan_no or challan_no == "0" or not challan_date:
            return False, "Challan No and Challan Date are required."

        self.challan_no = challan_no
        self.challan_date = challan_date
        self.save(update_fields=['challan_no', 'challan_date'])
        return True, "Record is updated successfully."

```

#### 4.2 Forms (`inventory/forms.py`)

We'll define two forms: one for the `GoodsInwardNote` edit operation and another for the search functionality.

```python
from django import forms
from .models import GoodsInwardNote

class GoodsInwardNoteEditForm(forms.ModelForm):
    """
    Form for editing Challan No and Challan Date of a Goods Inward Note.
    """
    challan_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date', # Use HTML5 date input for better user experience
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'pattern': '\d{4}-\d{2}-\d{2}', # Expected format YYYY-MM-DD
        }),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'], # Allow both standard and dd-mm-yyyy input
        required=False # Allow empty initially for existing records that might not have a challan
    )

    class Meta:
        model = GoodsInwardNote
        fields = ['challan_no', 'challan_date']
        widgets = {
            'challan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def clean(self):
        """
        Custom validation mimicking ASP.NET's `if (ChNo != "0" && ChNo != "" && ChDt != "")`.
        """
        cleaned_data = super().clean()
        challan_no = cleaned_data.get('challan_no')
        challan_date = cleaned_data.get('challan_date')

        if not challan_no or challan_no == "0":
            self.add_error('challan_no', 'Challan Number is required and cannot be "0".')
        if not challan_date:
            self.add_error('challan_date', 'Challan Date is required.')
        
        return cleaned_data

class GoodsInwardNoteSearchForm(forms.Form):
    """
    Form for the search dropdown selection. Input fields will be handled dynamically via HTMX.
    """
    SEARCH_CHOICES = [
        ('0', 'Supplier Name'),
        ('1', 'PO No'),
        ('2', 'GIN No'),
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-52 p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/inventory/hx-post-search-toggle/', # HTMX to dynamically show search inputs
            'hx-target': '#search-inputs-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
        }),
        label="Search By"
    )

```

#### 4.3 Views (`inventory/views.py`)

Views will be thin, delegating most logic to models and managers. They will handle HTTP requests, forms, and render templates, including HTMX partials.

```python
from django.views.generic import ListView, UpdateView, View
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.shortcuts import render, get_object_or_404
from django.template.loader import render_to_string # Useful for rendering HTMX partials

from .models import GoodsInwardNote, Supplier
from .forms import GoodsInwardNoteEditForm, GoodsInwardNoteSearchForm

class GoodsInwardNoteListView(ListView):
    """
    Main view to display the list of Goods Inward Notes.
    Handles initial page load and search filtering.
    """
    model = GoodsInwardNote
    template_name = 'inventory/goods_inward_note/list.html'
    context_object_name = 'goods_inward_notes'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Access session variables for company and financial year
        company_id = self.request.session.get('compid', 1) 
        financial_year_id = self.request.session.get('finyear', '2023-24') 

        # Get search parameters from GET request
        search_type = self.request.GET.get('search_by', '0') # Default to Supplier Name
        search_value = self.request.GET.get('search_value', '').strip()
        
        # Use the custom manager method to filter the queryset
        queryset = GoodsInwardNote.objects.filter_by_criteria(
            company_id=company_id, 
            financial_year_id=financial_year_id,
            search_type=search_type,
            search_value=search_value
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form for the dropdown
        current_search_by = self.request.GET.get('search_by', '0')
        current_search_value = self.request.GET.get('search_value', '')
        context['search_form'] = GoodsInwardNoteSearchForm(initial={'search_by': current_search_by})
        context['current_search_value'] = current_search_value # To pre-fill the search text box
        
        # Pass company_id to context for model methods that need it in the template
        context['company_id'] = self.request.session.get('compid', 1)
        return context

class GoodsInwardNoteTablePartialView(GoodsInwardNoteListView):
    """
    HTMX partial view to render only the table content, allowing dynamic updates.
    """
    template_name = 'inventory/goods_inward_note/_goods_inward_note_table.html'

    def get(self, request, *args, **kwargs):
        # The parent's get_queryset already filters. DataTables will handle client-side pagination.
        self.object_list = self.get_queryset()
        context = self.get_context_data()
        return render(request, self.template_name, context)

class GoodsInwardNoteUpdateView(UpdateView):
    """
    Handles inline editing of Goods Inward Note records via a modal.
    """
    model = GoodsInwardNote
    form_class = GoodsInwardNoteEditForm
    template_name = 'inventory/goods_inward_note/_goods_inward_note_form.html'
    success_url = reverse_lazy('goods_inward_note_list') # Fallback URL, not typically used with HTMX success

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass company_id to context for the `can_be_edited` check within the template
        context['company_id'] = self.request.session.get('compid', 1)
        return context

    def form_valid(self, form):
        company_id = self.request.session.get('compid', 1)
        
        # Delegate update logic to the model method (Fat Model principle)
        success, message = form.instance.update_challan_details(
            form.cleaned_data['challan_no'], 
            form.cleaned_data['challan_date'],
            company_id
        )
        
        if success:
            messages.success(self.request, message)
            if self.request.headers.get('HX-Request'):
                # For HTMX, return a 204 No Content response with a custom trigger
                return HttpResponse(
                    status=204, 
                    headers={
                        'HX-Trigger': 'refreshGoodsInwardNoteList' # Triggers table refresh on client
                    }
                )
            return super().form_valid(form) # Fallback for non-HTMX requests
        else:
            # If update failed due to business logic (e.g., GRR/GSN check or invalid data),
            # add the message as a non-field error and re-render the form.
            form.add_error(None, message) 
            return self.form_invalid(form) # Re-render the form with errors

    def form_invalid(self, form):
        # If the form is invalid (either validation or business logic error),
        # re-render the modal content with error messages.
        messages.error(self.request, "Please correct the errors below.")
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class GoodsInwardNoteSelectView(View):
    """
    Handles the "Select" command from the GridView, redirecting to a detail page.
    This mimics the `Response.Redirect` behavior from the ASP.NET code.
    """
    def get(self, request, pk):
        gin = get_object_or_404(GoodsInwardNote, pk=pk)
        
        # Mimic ASP.NET's query string parameters for the redirect URL
        # e.g., Response.Redirect("GoodsInwardNote_GIN_Edit_Details.aspx?ModId=9&SubModId=37&Id=" + Id + "&GNo=" + Ginno + "&ChNo=" + chno + "&ChDt=" + chDate + "&fyid=" + finyrsid + "&SupId=" + supplierid + "&PoNo=" + PoNo + " ");
        
        # Define parameters to pass to the target detail URL
        query_params = {
            'ModId': '9', # Assuming a fixed module ID
            'SubModId': '37', # Assuming a fixed submodule ID
            'GNo': gin.gin_no,
            'ChNo': gin.challan_no if gin.challan_no else '', # Handle potential None for challan_no
            'ChDt': gin.challan_date.strftime('%d-%m-%Y') if gin.challan_date else '', # Format date as dd-mm-yyyy
            'fyid': gin.fin_year,
            'PoNo': gin.po_no,
            # 'SupId': gin.supplier_obj.supplier_id if gin.supplier_obj else '', # Include if SupplierId is explicitly needed in detail page, but original C# was empty string
        }
        
        # Assuming a Django detail view for GIN exists, e.g., 'goods_inward_note_detail'
        # The `pk` (Id) of the GIN is passed as a URL argument.
        detail_url = reverse('goods_inward_note_detail', args=[gin.pk]) 
        from urllib.parse import urlencode
        
        return HttpResponseRedirect(f"{detail_url}?{urlencode(query_params)}")

class SupplierAutoCompleteView(View):
    """
    Provides supplier name suggestions for the autocomplete functionality using HTMX.
    Corresponds to the ASP.NET WebMethod `sql`.
    """
    def get(self, request):
        prefix_text = request.GET.get('q', '').strip()
        company_id = request.session.get('compid', 1) # Get company_id from session

        if not prefix_text:
            return JsonResponse([], safe=False)

        # Filter suppliers by name prefix and company ID
        suppliers = Supplier.objects.filter(
            supplier_name__icontains=prefix_text,
            comp_id=company_id
        ).values('supplier_id', 'supplier_name')[:10] # Limit results similar to ASP.NET

        # Format suggestions as "Supplier Name [SupplierId]"
        suggestions = [f"{s['supplier_name']} [{s['supplier_id']}]" for s in suppliers]
        
        # Return as JSON for HTMX to consume
        return JsonResponse(suggestions, safe=False)

class SearchInputToggleView(View):
    """
    Handles dynamically displaying the correct search input field (TextBox or AutoComplete)
    based on the 'Search By' dropdown selection using HTMX.
    Corresponds to `DropDownList1_SelectedIndexChanged`.
    """
    def post(self, request):
        search_by = request.POST.get('search_by', '0')
        # Preserve the current search value if it exists for the relevant input
        current_search_value = request.POST.get('search_value', '') 
        context = {
            'search_by': search_by,
            'current_search_value': current_search_value,
        }
        # Render and return the appropriate partial template for the search inputs
        return render(request, 'inventory/goods_inward_note/_search_inputs.html', context)

```

#### 4.4 Templates (`inventory/templates/inventory/goods_inward_note/`)

Django templates will be built using DRY principles, extending `core/base.html` and using partials for reusable components like the data table and form modals.

**`list.html`** (Main page displaying search and the table container)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block extra_head %}
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.css">
<!-- Custom CSS from ASP.NET (simulate for compatibility if needed) -->
<link href="{% static 'css/StyleSheet.css' %}" rel="stylesheet" type="text/css" />
<link href="{% static 'css/yui-datatable.css' %}" rel="stylesheet" type="text/css" />
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Inward Note [GIN] - Edit</h2>
    </div>
    
    <!-- Search Bar Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form hx-get="{% url 'goods_inward_note_table' %}" 
              hx-target="#goods_inward_note_table-container" 
              hx-swap="innerHTML" 
              hx-indicator="#table-loading-indicator"
              class="space-y-4">
            <div class="flex items-end space-x-4">
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_by }}
                </div>
                
                <div id="search-inputs-container" class="flex-grow">
                    <!-- HTMX will dynamically load _search_inputs.html here -->
                    {% include 'inventory/goods_inward_note/_search_inputs.html' with search_by=search_form.search_by.value current_search_value=current_search_value %}
                </div>
                
                <div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">Search</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Container for the dynamically loaded DataTables -->
    <div id="goods_inward_note_table-container"
         hx-trigger="load, refreshGoodsInwardNoteList from:body"
         hx-get="{% url 'goods_inward_note_table' %}"
         hx-swap="innerHTML">
        <!-- Loading indicator for the table -->
        <div id="table-loading-indicator" class="htmx-indicator text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form (hidden by default, shown by Alpine.js/HTMX) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- jQuery for DataTables (ensure it's loaded before DataTables JS) -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- DataTables JS -->
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.js"></script>

<script>
    // Example of Alpine.js initialization if needed for more complex UI states
    document.addEventListener('alpine:init', () => {
        Alpine.data('goodsInwardNoteApp', () => ({
            // Define reactive properties or methods here
        }));
    });

    // Custom event listener for messages (simulating ASP.NET ClientScript.RegisterStartupScript alert)
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.xhr.status === 204 && event.detail.xhr.getResponseHeader('HX-Trigger') === 'refreshGoodsInwardNoteList') {
            const message = event.detail.xhr.getResponseHeader('X-Message'); // Assuming you add this header in Django view
            if (message) {
                alert(message); // Show message after successful update
            }
        }
        // Handle Django messages via Alpine.js or simple JS if required
        // e.g., if you pass messages to a common modal
    });
</script>
{% endblock %}
```

**`_goods_inward_note_table.html`** (Partial template for the DataTables content)

```html
<div class="bg-white shadow-md rounded-lg overflow-x-auto">
    <table id="goodsInwardNoteTable" class="min-w-full bg-white yui-datatable-theme">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PONo</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in goods_inward_notes %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if obj.can_be_edited %}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-xs"
                        hx-get="{% url 'goods_inward_note_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    {% else %}
                    <span class="text-red-500 text-xs">GRR/GSN exists</span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <a href="{% url 'goods_inward_note_select' obj.pk %}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs">Select</a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.gin_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sys_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.supplier_obj.supplier_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.challan_no|default_if_none:"" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.challan_date|date:"d-m-Y"|default_if_none:"" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 px-4 border-b border-gray-200 text-center text-lg text-maroon font-semibold">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Initialize DataTables after the table is loaded into the DOM
$(document).ready(function() {
    $('#goodsInwardNoteTable').DataTable({
        "pageLength": 20, // Default page size from ASP.NET
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 1, 2] } // Disable sorting for SN, Actions, Select
        ]
    });
});
</script>
```

**`_goods_inward_note_form.html`** (Partial template for the edit modal content)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Goods Inward Note - GIN No: {{ form.instance.gin_no }}</h3>
    <form hx-post="{% url 'goods_inward_note_edit' form.instance.pk %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {# Display non-field errors (e.g., from model's update_challan_details method) #}
            {% if form.non_field_errors %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                    </span>
                </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
        </div>
    </form>
</div>
```

**`_search_inputs.html`** (Partial template for dynamic search input fields)

```html
{# This partial is loaded dynamically by HTMX based on search_by selection #}
{% if search_by == '0' %} {# Supplier Name #}
    <div class="w-full">
        <label for="id_search_value" class="block text-sm font-medium text-gray-700">Supplier Name</label>
        <input type="text" id="id_search_value" name="search_value" value="{{ current_search_value }}" 
               class="box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
               placeholder="Start typing supplier name..." 
               hx-get="{% url 'supplier_autocomplete' %}" 
               hx-trigger="keyup changed delay:500ms, search" 
               hx-target="#supplier-suggestions" 
               hx-swap="innerHTML">
        <div id="supplier-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg absolute z-10 w-full mt-1"></div>
    </div>
{% elif search_by == '1' %} {# PO No #}
    <div class="w-full">
        <label for="id_search_value" class="block text-sm font-medium text-gray-700">PO Number</label>
        <input type="text" id="id_search_value" name="search_value" value="{{ current_search_value }}" 
               class="box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    </div>
{% elif search_by == '2' %} {# GIN No #}
    <div class="w-full">
        <label for="id_search_value" class="block text-sm font-medium text-gray-700">GIN Number</label>
        <input type="text" id="id_search_value" name="search_value" value="{{ current_search_value }}" 
               class="box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    </div>
{% endif %}
```

**`_supplier_suggestions.html`** (Partial template for autocomplete suggestions)

```html
{% for suggestion in suggestions %}
    <div class="p-2 hover:bg-blue-100 cursor-pointer text-sm" 
         hx-on:click="document.getElementById('id_search_value').value = '{{ suggestion }}'; document.getElementById('supplier-suggestions').innerHTML = '';">
        {{ suggestion }}
    </div>
{% endfor %}
```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for the views within the `inventory` application.

```python
from django.urls import path
from .views import (
    GoodsInwardNoteListView, 
    GoodsInwardNoteTablePartialView, 
    GoodsInwardNoteUpdateView,
    GoodsInwardNoteSelectView,
    SupplierAutoCompleteView,
    SearchInputToggleView,
)

urlpatterns = [
    # Main list view
    path('goods-inward-note/', GoodsInwardNoteListView.as_view(), name='goods_inward_note_list'),
    
    # HTMX partial for the DataTables content
    path('goods-inward-note/table/', GoodsInwardNoteTablePartialView.as_view(), name='goods_inward_note_table'),
    
    # HTMX view for editing a specific GIN
    path('goods-inward-note/edit/<int:pk>/', GoodsInwardNoteUpdateView.as_view(), name='goods_inward_note_edit'),
    
    # View for the "Select" command, typically redirects to a detail page
    # You would need a 'goods_inward_note_detail' view/URL in your project's main urls.py or another app.
    path('goods-inward-note/select/<int:pk>/', GoodsInwardNoteSelectView.as_view(), name='goods_inward_note_select'),
    
    # HTMX endpoint for supplier autocomplete suggestions
    path('search-suppliers/', SupplierAutoCompleteView.as_view(), name='supplier_autocomplete'),
    
    # HTMX endpoint to dynamically toggle search input fields based on dropdown
    path('hx-post-search-toggle/', SearchInputToggleView.as_view(), name='hx_post_search_toggle'),

    # Example of a placeholder for the detail view URL if not in this app:
    # path('goods-inward-note/<int:pk>/details/', YourDetailView.as_view(), name='goods_inward_note_detail'),
    # For testing, we'll assume a `goods_inward_note_detail` URL exists.
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for both models and views will ensure the migration's success and maintain code quality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from datetime import date
from unittest.mock import patch # Used to mock session for testing

from .models import GoodsInwardNote, Supplier, MaterialReceived, MaterialServiceNote

class GoodsInwardNoteModelTest(TestCase):
    """
    Unit tests for the GoodsInwardNote model and its business logic methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data that will be available for all test methods in this class
        cls.supplier1 = Supplier.objects.create(supplier_id='SUP001', supplier_name='ABC Corp', comp_id=1)
        cls.supplier2 = Supplier.objects.create(supplier_id='SUP002', supplier_name='XYZ Ltd', comp_id=1)

        # GIN record that should be non-editable (has GRR/GSN)
        cls.gin1 = GoodsInwardNote.objects.create(
            id=1, 
            fin_year='2023-24', 
            po_no='PO001', 
            gin_no='GIN001', 
            sys_date=date(2023, 1, 15), 
            supplier_obj=cls.supplier1, 
            challan_no='CH001', 
            challan_date=date(2023, 1, 10), 
            comp_id=1
        )
        # GIN record that should be editable (no GRR/GSN)
        cls.gin2 = GoodsInwardNote.objects.create(
            id=2, 
            fin_year='2023-24', 
            po_no='PO002', 
            gin_no='GIN002', 
            sys_date=date(2023, 2, 20), 
            supplier_obj=cls.supplier2,
            challan_no=None, 
            challan_date=None, 
            comp_id=1
        )
        # GIN record for filtering test (different comp_id)
        cls.gin3 = GoodsInwardNote.objects.create(
            id=3,
            fin_year='2023-24',
            po_no='PO003',
            gin_no='GIN003',
            sys_date=date(2023, 3, 1),
            supplier_obj=cls.supplier1,
            challan_no='CH003',
            challan_date=date(2023, 2, 25),
            comp_id=2 # Different company ID
        )
        
        # Create related GRR/GSN for gin1 to make it non-editable
        MaterialReceived.objects.create(gin=cls.gin1, comp_id=1)
        MaterialServiceNote.objects.create(gin=cls.gin1, comp_id=1)

    def test_goods_inward_note_creation(self):
        """Test that a GIN object is created correctly."""
        gin = GoodsInwardNote.objects.get(id=1)
        self.assertEqual(gin.gin_no, 'GIN001')
        self.assertEqual(gin.supplier_obj.supplier_name, 'ABC Corp')
        self.assertEqual(gin.challan_no, 'CH001')
        self.assertEqual(gin.challan_date, date(2023, 1, 10))
        
    def test_has_material_received_notes(self):
        """Test the logic for checking related GRR records."""
        self.assertTrue(self.gin1.has_material_received_notes(company_id=1))
        self.assertFalse(self.gin2.has_material_received_notes(company_id=1))

    def test_has_material_service_notes(self):
        """Test the logic for checking related GSN records."""
        self.assertTrue(self.gin1.has_material_service_notes(company_id=1))
        self.assertFalse(self.gin2.has_material_service_notes(company_id=1))

    def test_can_be_edited(self):
        """Test the combined logic for GIN editability."""
        self.assertFalse(self.gin1.can_be_edited(company_id=1)) # gin1 has GRR/GSN
        self.assertTrue(self.gin2.can_be_edited(company_id=1)) # gin2 does not

    def test_update_challan_details_success(self):
        """Test successful update of challan details when editable."""
        success, message = self.gin2.update_challan_details('NEWCH002', date(2023, 3, 1), company_id=1)
        self.assertTrue(success)
        self.assertEqual(message, "Record is updated successfully.")
        self.gin2.refresh_from_db() # Reload from DB to verify changes
        self.assertEqual(self.gin2.challan_no, 'NEWCH002')
        self.assertEqual(self.gin2.challan_date, date(2023, 3, 1))

    def test_update_challan_details_not_editable(self):
        """Test update attempt on a non-editable GIN."""
        success, message = self.gin1.update_challan_details('NEWCH001', date(2023, 3, 1), company_id=1)
        self.assertFalse(success)
        self.assertEqual(message, "This GIN cannot be edited as it has associated GRR/GSN records.")
        self.gin1.refresh_from_db() # Ensure no changes occurred
        self.assertEqual(self.gin1.challan_no, 'CH001')

    def test_update_challan_details_invalid_data(self):
        """Test update attempt with invalid (missing) challan data."""
        # Test missing challan_no
        success, message = self.gin2.update_challan_details('', date(2023, 3, 1), company_id=1)
        self.assertFalse(success)
        self.assertEqual(message, "Challan No and Challan Date are required.")
        self.gin2.refresh_from_db()
        self.assertIsNone(self.gin2.challan_no) # Still None

        # Test missing challan_date
        success, message = self.gin2.update_challan_details('ValidNo', None, company_id=1)
        self.assertFalse(success)
        self.assertEqual(message, "Challan No and Challan Date are required.")
        self.gin2.refresh_from_db()
        self.assertIsNone(self.gin2.challan_date) # Still None

    def test_filter_by_criteria_all(self):
        """Test filtering with no specific search criteria."""
        queryset = GoodsInwardNote.objects.filter_by_criteria(company_id=1, financial_year_id='2023-24', search_type='0', search_value='')
        self.assertEqual(queryset.count(), 2) # gin1, gin2

    def test_filter_by_criteria_po_no(self):
        """Test filtering by PO number."""
        queryset = GoodsInwardNote.objects.filter_by_criteria(company_id=1, financial_year_id='2023-24', search_type='1', search_value='PO001')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().po_no, 'PO001')

    def test_filter_by_criteria_gin_no(self):
        """Test filtering by GIN number."""
        queryset = GoodsInwardNote.objects.filter_by_criteria(company_id=1, financial_year_id='2023-24', search_type='2', search_value='GIN002')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().gin_no, 'GIN002')

    def test_filter_by_criteria_supplier_name_formatted(self):
        """Test filtering by formatted supplier name (from autocomplete)."""
        queryset = GoodsInwardNote.objects.filter_by_criteria(company_id=1, financial_year_id='2023-24', search_type='0', search_value='ABC Corp [SUP001]')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().supplier_obj.supplier_name, 'ABC Corp')

    def test_filter_by_criteria_supplier_name_unformatted(self):
        """Test filtering by unformatted supplier name (contains)."""
        queryset = GoodsInwardNote.objects.filter_by_criteria(company_id=1, financial_year_id='2023-24', search_type='0', search_value='XYZ')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().supplier_obj.supplier_name, 'XYZ Ltd')

class GoodsInwardNoteViewsTest(TestCase):
    """
    Integration tests for Django views, covering HTTP requests and responses.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests within this class
        cls.supplier1 = Supplier.objects.create(supplier_id='SUP001', supplier_name='ABC Corp', comp_id=1)
        cls.supplier2 = Supplier.objects.create(supplier_id='SUP002', supplier_name='XYZ Ltd', comp_id=1)
        cls.gin1 = GoodsInwardNote.objects.create(
            id=1, fin_year='2023-24', po_no='PO001', gin_no='GIN001', sys_date=date(2023, 1, 15), 
            supplier_obj=cls.supplier1, challan_no='CH001', challan_date=date(2023, 1, 10), comp_id=1
        )
        cls.gin2 = GoodsInwardNote.objects.create(
            id=2, fin_year='2023-24', po_no='PO002', gin_no='GIN002', sys_date=date(2023, 2, 20), 
            supplier_obj=cls.supplier2, challan_no=None, challan_date=None, comp_id=1
        )
        
        # Create related GRR/GSN for gin1 to make it non-editable
        MaterialReceived.objects.create(gin=cls.gin1, comp_id=1)
        MaterialServiceNote.objects.create(gin=cls.gin1, comp_id=1)

    def setUp(self):
        """Set up for each test method: client and mock session."""
        self.client = Client()
        # Patch the session to simulate ASP.NET session variables
        with patch('django.contrib.sessions.backends.db.SessionStore.get_session_key', return_value='test_session_key'):
            session = self.client.session
            session['compid'] = 1
            session['finyear'] = '2023-24'
            session.save()
        
        # Define a mock detail URL for the 'select' redirect to work in tests
        self.client.get('/mock-detail-url/1/') # Access a dummy URL to register it in resolver cache
        from django.urls import re_path
        self.detail_url_pattern = re_path(r'^goods-inward-note/(?P<pk>\d+)/details/$', lambda r, pk: HttpResponse(f"Detail page for {pk}"), name='goods_inward_note_detail')
        self.client.get(reverse('goods_inward_note_list')) # To ensure URL resolver is primed for goods_inward_note_detail

    def test_list_view_get(self):
        """Test the main list view rendering and context."""
        response = self.client.get(reverse('goods_inward_note_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/list.html')
        self.assertTrue('goods_inward_notes' in response.context)
        self.assertEqual(len(response.context['goods_inward_notes']), 2) # Both GINs for comp_id=1

    def test_list_view_search_by_po_no(self):
        """Test list view with PO number search filter."""
        response = self.client.get(reverse('goods_inward_note_list'), {'search_by': '1', 'search_value': 'PO001'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['goods_inward_notes']), 1)
        self.assertEqual(response.context['goods_inward_notes'][0].po_no, 'PO001')

    def test_list_view_search_by_supplier_name(self):
        """Test list view with formatted supplier name search filter."""
        response = self.client.get(reverse('goods_inward_note_list'), {'search_by': '0', 'search_value': 'ABC Corp [SUP001]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['goods_inward_notes']), 1)
        self.assertEqual(response.context['goods_inward_notes'][0].supplier_obj.supplier_name, 'ABC Corp')

    def test_table_partial_view_get(self):
        """Test HTMX request for the table partial."""
        response = self.client.get(reverse('goods_inward_note_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_table.html')
        self.assertTrue('goods_inward_notes' in response.context)
        self.assertEqual(len(response.context['goods_inward_notes']), 2)

    def test_update_view_get_editable(self):
        """Test GET request for the update form (editable GIN)."""
        response = self.client.get(reverse('goods_inward_note_edit', args=[self.gin2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsNone(response.context['form'].instance.challan_no) # Should be None for gin2 initially

    def test_update_view_post_success(self):
        """Test successful POST request to update challan details via HTMX."""
        data = {'challan_no': 'UPDATEDCH', 'challan_date': '2023-04-01'}
        response = self.client.post(reverse('goods_inward_note_edit', args=[self.gin2.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsInwardNoteList')
        self.gin2.refresh_from_db() # Reload to verify changes
        self.assertEqual(self.gin2.challan_no, 'UPDATEDCH')
        self.assertEqual(self.gin2.challan_date, date(2023, 4, 1))
        # Check for messages (Django messages framework)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Record is updated successfully.')

    def test_update_view_post_invalid_data(self):
        """Test POST request with invalid form data."""
        data = {'challan_no': '0', 'challan_date': ''} # Invalid data as per model's validation
        response = self.client.post(reverse('goods_inward_note_edit', args=[self.gin2.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_form.html')
        self.assertTrue('form' in response.context)
        self.assertTrue(response.context['form'].errors) # Form errors should be present
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

    def test_update_view_post_not_editable(self):
        """Test POST request on a GIN that cannot be edited (has GRR/GSN)."""
        data = {'challan_no': 'TRYEDIT', 'challan_date': '2023-05-01'}
        response = self.client.post(reverse('goods_inward_note_edit', args=[self.gin1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].is_valid()) # Form will be invalid due to model's can_be_edited check
        self.assertIn("This GIN cannot be edited as it has associated GRR/GSN records.", response.content.decode())
        self.gin1.refresh_from_db()
        self.assertEqual(self.gin1.challan_no, 'CH001') # Ensure no change

    def test_select_view_redirect(self):
        """Test the 'Select' view redirects with correct query parameters."""
        # Dynamically add the detail URL pattern for this test's client
        with self.settings(ROOT_URLCONF='inventory.tests_urls'): # Use a temporary URLconf for this test
            from django.urls import path
            self.urlpatterns = [
                path('goods-inward-note/1/details/', lambda r: HttpResponse("Detail page"), name='goods_inward_note_detail'),
                path('goods-inward-note/select/<int:pk>/', GoodsInwardNoteSelectView.as_view(), name='goods_inward_note_select'),
            ]
            
            response = self.client.get(reverse('goods_inward_note_select', args=[self.gin1.pk]))
            self.assertEqual(response.status_code, 302) # Expect a redirect
            
            from urllib.parse import urlparse, parse_qs
            redirect_url = response.url
            parsed_url = urlparse(redirect_url)
            
            # The path should match the assumed detail view URL
            self.assertEqual(parsed_url.path, '/goods-inward-note/1/details/') 
            
            # Check the query parameters
            actual_query_params = parse_qs(parsed_url.query)
            # Convert list values to single string values for comparison
            actual_query_params = {k: v[0] for k, v in actual_query_params.items()}
            
            expected_query_params = {
                'ModId': '9',
                'SubModId': '37',
                'GNo': 'GIN001',
                'ChNo': 'CH001',
                'ChDt': '10-01-2023', # Formatted date
                'fyid': '2023-24',
                'PoNo': 'PO001',
            }
            self.assertDictEqual(actual_query_params, expected_query_params)
            
    def test_supplier_autocomplete_view(self):
        """Test the supplier autocomplete endpoint."""
        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'abc'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['ABC Corp [SUP001]'])

        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'xyz'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['XYZ Ltd [SUP002]'])

        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'nonexistent'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

    def test_search_input_toggle_view(self):
        """Test dynamic switching of search input fields via HTMX."""
        # Test selecting PO No
        response = self.client.post(reverse('hx_post_search_toggle'), {'search_by': '1', 'search_value': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_search_inputs.html')
        self.assertIn('PO Number', response.content.decode())
        self.assertNotIn('Supplier Name', response.content.decode())

        # Test selecting Supplier Name
        response = self.client.post(reverse('hx_post_search_toggle'), {'search_by': '0', 'search_value': 'Partial Name'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_search_inputs.html')
        self.assertIn('Supplier Name', response.content.decode())
        self.assertNotIn('PO Number', response.content.decode())
        self.assertIn('value="Partial Name"', response.content.decode()) # Check if value is preserved

# Add a dummy URLconf for testing purposes within this file
# This is necessary for the `test_select_view_redirect` to resolve the `goods_inward_note_detail` URL.
# In a real project, this URL would be defined in your main `urls.py` or another app's `urls.py`.
class TestUrls(TestCase):
    urls = 'inventory.tests_urls' # Point to a temporary URLconf for this test file

    def test_dummy_url_exists(self):
        # This test ensures the dummy URLconf is correctly set up for the redirect test
        with self.settings(ROOT_URLCONF='inventory.tests_urls'):
            response = self.client.get('/goods-inward-note/1/details/')
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.content.decode(), 'Detail page for 1')

# Temporary urls.py for tests
# (inventory/tests_urls.py - implicitly created for testing purposes)
from django.urls import path
from django.http import HttpResponse
from .views import GoodsInwardNoteSelectView # Import necessary view for testing purposes

urlpatterns = [
    # A dummy detail view for testing the redirect
    path('goods-inward-note/<int:pk>/details/', lambda r, pk: HttpResponse(f"Detail page for {pk}"), name='goods_inward_note_detail'),
    # Include the actual view that performs the redirect
    path('goods-inward-note/select/<int:pk>/', GoodsInwardNoteSelectView.as_view(), name='goods_inward_note_select'),
    # For autocomplete test
    path('search-suppliers/', SupplierAutoCompleteView.as_view(), name='supplier_autocomplete'),
    # For search input toggle test
    path('hx-post-search-toggle/', SearchInputToggleView.as_view(), name='hx_post_search_toggle'),
]

```

---

## Step 5: HTMX and Alpine.js Integration

The migration explicitly leverages HTMX for all dynamic interactions and Alpine.js for localized UI state management, eliminating the need for complex JavaScript frameworks.

*   **HTMX for DataTables Loading:**
    *   The main `list.html` includes a `div` (`#goods_inward_note_table-container`) that uses `hx-trigger="load, refreshGoodsInwardNoteList from:body"` and `hx-get="{% url 'goods_inward_note_table' %}"`. This ensures the DataTable is loaded dynamically on initial page load and refreshes whenever the `refreshGoodsInwardNoteList` custom event is triggered (e.g., after a successful update).
*   **HTMX for Modals:**
    *   "Edit" buttons in the table use `hx-get="{% url 'goods_inward_note_edit' obj.pk %}"` to fetch the form partial (`_goods_inward_note_form.html`) into the `#modalContent` div.
    *   The `on click add .is-active to #modal` (Alpine.js/Hyperscript) is used to display the modal.
*   **HTMX for Form Submission:**
    *   The edit form within the modal uses `hx-post="{{ request.path }}" hx-swap="none"`. The Django view responds with `status=204` and an `HX-Trigger` header to signal the client to close the modal and refresh the table.
*   **HTMX for Dynamic Search Inputs:**
    *   The `search_by` dropdown (`search_form.search_by`) has `hx-post` to `hx-post-search-toggle/` and `hx-target="#search-inputs-container"`. This dynamically loads `_search_inputs.html` based on the selection, showing the correct `TextBox` or `AutoComplete` field.
*   **HTMX for Autocomplete:**
    *   The `txtSupplier` equivalent (`id_search_value` for supplier) uses `hx-get="{% url 'supplier_autocomplete' %}"` with `hx-trigger="keyup changed delay:500ms, search"` to fetch suggestions as the user types. These suggestions are swapped into `#supplier-suggestions` via `hx-target` and `hx-swap`. Clicking a suggestion updates the input field via `hx-on:click` (Hyperscript).
*   **Alpine.js for UI State:**
    *   Used for simple UI state management, primarily for showing/hiding the modal (`on click add .is-active to #modal` and `remove .is-active from #modal`). This keeps client-side logic minimal and declaractive.
*   **DataTables:**
    *   The `_goods_inward_note_table.html` partial includes the JavaScript to initialize `$('#goodsInwardNoteTable').DataTable()`, providing client-side searching, sorting, and pagination. It's re-initialized every time the table partial is loaded by HTMX.
*   **Styling:** Tailwind CSS classes (e.g., `bg-blue-500`, `rounded`, `shadow-md`) are applied directly in the templates for a modern, responsive UI.

---

## Final Notes

This comprehensive plan provides a structured, automated approach to migrate the ASP.NET Goods Inward Note - Edit module to Django. By adhering to the principles of fat models, thin views, and exclusive use of HTMX/Alpine.js for frontend interactivity, the resulting Django application will be robust, maintainable, performant, and aligned with modern web development best practices. The detailed code blocks and tests ensure that the generated solution is complete, runnable, and verifiable.