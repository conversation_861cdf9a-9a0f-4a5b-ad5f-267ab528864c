```markdown
## ASP.NET to Django Conversion Script: Goods Received Receipt Details

This modernization plan outlines the transition of the ASP.NET "Goods Received Receipt (GRR) - New Details" page to a modern Django application. Our approach leverages Django's robust features, emphasizing a "fat model, thin view" architecture, dynamic frontend interactions with HTMX and Alpine.js, and efficient data presentation using DataTables.

The core objective is to convert the existing functionality—displaying inbound goods details and allowing users to record received quantities against a specific Inward Note—into a highly maintainable, scalable, and user-friendly Django solution.

### Business Value of Django Modernization:

*   **Cost Efficiency:** Automating the migration process significantly reduces manual coding effort, lowering development costs and accelerating time-to-market for the modernized application.
*   **Enhanced Performance & Scalability:** Django's robust architecture and efficient ORM provide a solid foundation for high-performing applications that can scale to meet growing business demands. HTMX and Alpine.js further enhance responsiveness by minimizing full-page reloads.
*   **Improved Maintainability:** Django's clear structure, Python's readability, and adherence to best practices (like DRY and fat models) make the codebase easier to understand, debug, and extend, reducing long-term maintenance costs.
*   **Future-Proofing:** Moving to a modern, actively maintained framework like Django ensures access to ongoing security updates, new features, and a thriving community, safeguarding the application's longevity.
*   **Better User Experience:** Dynamic interfaces powered by HTMX and Alpine.js provide a smoother, more interactive user experience, similar to single-page applications but with minimal JavaScript overhead. DataTables offer powerful sorting, searching, and pagination capabilities for large datasets.
*   **Reduced Technical Debt:** Eliminating legacy ASP.NET Web Forms and server-side controls reduces technical debt, allowing for easier adoption of modern web development practices and integration with other systems.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
Based on the ASP.NET code-behind's SQL queries and `GridView` bindings, we can infer the following primary and related tables for the Goods Received Receipt (GRR) module.

**Primary Tables for GRR Entry (Managed by Django):**

*   **`tblinv_MaterialReceived_Master`** (New GRR Header)
    *   `Id` (Primary Key, INT, auto-incremented by Django)
    *   `GRRNo` (VARCHAR, generated sequence)
    *   `SysDate` (DATETIME)
    *   `SysTime` (TIME)
    *   `CompId` (INT)
    *   `SessionId` (VARCHAR, user ID)
    *   `FinYearId` (INT)
    *   `GINNo` (VARCHAR, Goods Inward Note Number)
    *   `GINId` (INT, Foreign Key to `tblInv_Inward_Master.Id`)
    *   `TaxInvoiceNo` (VARCHAR)
    *   `TaxInvoiceDate` (DATETIME)
    *   `ModVatApp` (INT, 0/1 for No/Yes)
    *   `ModVatInv` (INT, 0/1 for No/Yes)

*   **`tblinv_MaterialReceived_Details`** (New GRR Line Items)
    *   `MId` (Foreign Key to `tblinv_MaterialReceived_Master.Id`)
    *   `GRRNo` (VARCHAR, redundant, but kept for schema consistency)
    *   `POId` (INT, Foreign Key to `tblMM_PO_Details.Id`)
    *   `ReceivedQty` (FLOAT)

**Related Lookup Tables (Managed by Django = False - assuming they are external or legacy tables):**

*   **`tblInv_Inward_Master`**
    *   `Id` (PK)
    *   `GINNo`
    *   `ChallanNo`
    *   `ChallanDate`
    *   `CompId`
    *   `SupplierId` (FK)
    *   `PONo` (redundant, but used)
    *   `FinYearId`
*   **`tblInv_Inward_Details`**
    *   `GINId` (FK to `tblInv_Inward_Master.Id`)
    *   `POId` (FK to `tblMM_PO_Details.Id`)
    *   `ReceivedQty` (this is `InvQty` in `loadData` context)
*   **`tblMM_Supplier_master`**
    *   `SupplierId` (PK)
    *   `SupplierName`
    *   `CompId`
*   **`tblMM_PO_Master`**
    *   `Id` (PK)
    *   `PONo`
    *   `PRSPRFlag` (0 for PR, 1 for SPR)
    *   `FinYearId`
    *   `CompId`
*   **`tblMM_PO_Details`**
    *   `Id` (PK, `POId` in `loadData` context)
    *   `MId` (FK to `tblMM_PO_Master.Id`)
    *   `Qty` (PO Quantity)
    *   `PRNo`, `PRId` (FK to `tblMM_PR_Details.Id`)
    *   `SPRNo`, `SPRId` (FK to `tblMM_SPR_Details.Id`)
*   **`tblMM_PR_Master`**
    *   `Id` (PK)
    *   `PRNo`
    *   `CompId`
    *   `FinYearId`
*   **`tblMM_PR_Details`**
    *   `Id` (PK)
    *   `MId` (FK to `tblMM_PR_Master.Id`)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `AHId` (FK to `AccHead.Id`)
*   **`tblMM_SPR_Master`**
    *   `Id` (PK)
    *   `SPRNo`
    *   `CompId`
    *   `FinYearId`
*   **`tblMM_SPR_Details`**
    *   `Id` (PK)
    *   `MId` (FK to `tblMM_SPR_Master.Id`)
    *   `SPRNo`
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `AHId` (FK to `AccHead.Id`)
*   **`tblDG_Item_Master`**
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc` (`Description` in UI)
    *   `UOMBasic` (FK to `Unit_Master.Id`)
    *   `FileName`, `FileData`, `ContentType` (for image)
    *   `AttName`, `AttData`, `AttContentType` (for spec sheet)
    *   `CompId`
*   **`Unit_Master`**
    *   `Id` (PK)
    *   `Symbol` (UOM symbol)
*   **`AccHead`**
    *   `Id` (PK)
    *   `Category` (e.g., "With Material")

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The ASP.NET page is focused on creating new Goods Received Receipt entries.

*   **Create (Add):** The primary functionality is to **create new Goods Received Receipt entries** in `tblinv_MaterialReceived_Master` and `tblinv_MaterialReceived_Details`. This is triggered by the `btnInsert_Click` event, which generates a new GRR number, inserts header details (Tax Invoice, MODVAT info), and then iterates through selected grid items to insert individual received quantities.
*   **Read (Display):**
    *   **Header Data:** Read-only fields like GIN No., Challan No., Date, and Supplier Name are populated from `tblInv_Inward_Master` and `tblMM_Supplier_master` based on query string parameters.
    *   **Grid Data:** The `GridView2` is populated by the `loadData()` method. This is a complex operation involving joins across many tables to fetch item details, PO quantities, current inward quantities, and critically, the *total quantity already received* for each item from previous GRRs, to calculate the `TotRemainQty`.
*   **Update / Delete:** This page does not support updating or deleting existing GRR entries. It is solely for creating *new* receipts.
*   **Validation Logic:** Extensive validation is present:
    *   Required fields for Tax Invoice No. and Date, MODVAT selections.
    *   Date format validation.
    *   Quantity validation: `txtrecQty` must be a positive number and not exceed the `TotRemainQty`.
    *   Dynamic validation: the `txtrecQty` field is only required and visible if its corresponding checkbox is selected.
*   **File Download:** `GridView2_RowCommand` handles `downloadImg` and `downloadSpec` commands, which trigger redirects to a generic file download handler (`DownloadFile.aspx`) to retrieve binary data (images/spec sheets) from `tblDG_Item_Master`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The page has two main sections: a header form for GRR details and a dynamic grid for line items.

*   **Read-only Header Information:**
    *   `lblGIn`: GIN No.
    *   `lblChNo`: Challan No.
    *   `lblDate`: Challan Date
    *   `lblSupplier`: Supplier Name
*   **GRR Header Input Fields (Editable):**
    *   `txtTaxInvoice`: Text input for Tax Invoice Number.
    *   `txtDate`: Date input for Tax Invoice Date (with a calendar extender).
    *   `drpModVat`, `drpModVatInvoice`: Dropdowns for MODVAT Applicable/Invoice selection (Yes/No).
*   **Item Details Grid (`GridView2`):**
    *   Displays columns for Item Code, Description, UOM, PO Qty, Inward Qty, Total Received Qty, and a critical "Received Qty" input.
    *   **Interactive Elements:**
        *   **Checkbox (`ck`):** Toggles the visibility and required status of the "Received Qty" input (`txtrecQty`).
        *   **TextBox (`txtrecQty`):** User input for the quantity to be received for a specific item.
        *   **Link Buttons (`btnlnkImg`, `btnlnkSpec`):** Trigger file downloads (image, spec sheet) related to the item.
*   **Action Buttons:**
    *   `btnInsert` ("Add"): Submits the GRR form, including header and selected line items.
    *   `btnCancel` ("Cancel"): Navigates away from the form.

**UI Interactions to Replicate with HTMX + Alpine.js:**

*   **Form Header:** Standard Django form rendering with Tailwind CSS for styling.
*   **Date Picker:** Use HTML5 `type="date"` for `txtDate` or integrate a lightweight client-side library via Alpine.js if a specific UI is needed.
*   **Item Grid (DataTables):** The `GridView2` will be converted to a standard HTML table rendered by Django, which will then be initialized as a DataTables instance for client-side sorting, searching, and pagination.
*   **Dynamic Row Interaction (Checkbox and Quantity Input):** The `ck_CheckedChanged` logic will be handled client-side using Alpine.js to toggle the visibility and disable/enable state of the `txtrecQty` field, alongside a visual indicator for validation.
*   **Form Submission:** The "Add" button will trigger an HTMX `POST` request. The entire form data, including dynamically added received quantities for selected items, will be sent.
*   **File Downloads:** The `btnlnkImg` and `btnlnkSpec` will become standard `<a>` tags with `href` attributes pointing to dedicated Django views for serving the files.
*   **Messages:** Django's messaging framework will be used to provide user feedback (e.g., "GRR added successfully"). HTMX can then trigger a frontend message display.
*   **Modal:** The entire form (header + item grid) can be rendered as a single page view, or if this "New Details" page is meant to be a modal, then a base modal structure can be used. For this complex form, a dedicated page is more appropriate, with the item table being loaded dynamically.

### Step 4: Generate Django Code

We will create an `inventory` Django app to house these components.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.
These models will reside in `inventory/models.py`. We define the primary `MaterialReceivedMaster` and `MaterialReceivedDetail` models, and then proxy models (`managed=False`) for all the lookup tables that the existing ASP.NET code interacts with.

```python
# inventory/models.py
from django.db import models
from django.db.models import Sum, F
from django.utils import timezone
from datetime import datetime

# --- Primary Models for GRR (Managed by Django) ---
# These models represent the core data being created/managed by this application.
class MaterialReceivedMaster(models.Model):
    """
    Represents the header for a Goods Received Receipt (GRR).
    This corresponds to tblinv_MaterialReceived_Master.
    """
    grr_no = models.CharField(max_length=50, unique=True, db_column='GRRNo')
    sys_date = models.DateField(default=timezone.now, db_column='SysDate')
    sys_time = models.TimeField(default=timezone.now, db_column='SysTime')
    company_id = models.IntegerField(db_column='CompId') # Will come from user's session/profile
    session_id = models.CharField(max_length=50, db_column='SessionId') # Will come from logged-in user
    financial_year_id = models.IntegerField(db_column='FinYearId') # Will come from user's session/profile
    gin_no = models.CharField(max_length=50, db_column='GINNo')
    gin_master = models.ForeignKey('InwardMaster', on_delete=models.PROTECT, db_column='GINId', related_name='grr_masters')
    tax_invoice_no = models.CharField(max_length=100, db_column='TaxInvoiceNo')
    tax_invoice_date = models.DateField(db_column='TaxInvoiceDate')
    modvat_applicable = models.BooleanField(db_column='ModVatApp', default=False)
    modvat_invoice = models.BooleanField(db_column='ModVatInv', default=False)

    class Meta:
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received Header'
        verbose_name_plural = 'Material Received Headers'

    def __str__(self):
        return f"GRR No: {self.grr_no} for GIN: {self.gin_no}"

    # Business Logic: Generates the next GRR number
    def generate_next_grr_no(self, company_id, financial_year_id):
        """
        Generates the next sequential GRR number for the given company and financial year.
        Assumes GRRNo is an integer padded with leading zeros.
        """
        # In a real system, GRRNo generation should be highly robust to avoid race conditions.
        # This simple example mimics the ASP.NET logic.
        last_grr = MaterialReceivedMaster.objects.filter(
            company_id=company_id,
            financial_year_id=financial_year_id
        ).order_by('-id').first() # Order by ID assumes higher ID means newer record

        if last_grr and last_grr.grr_no.isdigit():
            last_grr_num = int(last_grr.grr_no)
            self.grr_no = str(last_grr_num + 1).zfill(4)
        else:
            self.grr_no = '0001' # Start from 0001 if no records or invalid format

    # Business Logic: Retrieves details needed to populate the GRR item grid
    def get_inward_items_for_receipt(self):
        """
        Retrieves relevant Inward details for creating a new GRR,
        including PO Qty, Inward Qty, and previously received GRR Qty.
        This consolidates the complex loadData() logic from ASP.NET.
        """
        items_for_grr = []
        # Query all related InwardDetails for the current InwardMaster
        # We need to explicitly select_related down the chain to avoid N+1 queries.
        inward_details = self.gin_master.inward_details.select_related(
            'po_detail__po_master',
            'po_detail__pr_detail__item_master__uom_basic', # Path if PR
            'po_detail__pr_detail__account_head',
            'po_detail__spr_detail__item_master__uom_basic', # Path if SPR
            'po_detail__spr_detail__account_head'
        ).all()

        for ind in inward_details:
            po_detail = ind.po_detail
            # Skip if PO Detail is missing or invalid
            if not po_detail or not po_detail.po_master:
                continue

            item_master = None
            account_head = None

            # Determine the item and account head based on PR/SPR flag
            if po_detail.po_master.pr_spr_flag == 0 and po_detail.pr_detail: # From Purchase Requisition
                item_master = po_detail.pr_detail.item_master
                account_head = po_detail.pr_detail.account_head
            elif po_detail.po_master.pr_spr_flag == 1 and po_detail.spr_detail: # From Sales Purchase Requisition
                item_master = po_detail.spr_detail.item_master
                account_head = po_detail.spr_detail.account_head

            # Only include items with "With Material" category and valid item data
            if not item_master or not account_head or account_head.category != 'With Material':
                continue

            # Calculate total quantity already received for this specific PO item
            # across all GRRs linked to this GIN.
            total_grr_received_qty = MaterialReceivedDetail.objects.filter(
                po_detail=po_detail,
                material_received_master__gin_master=self.gin_master # Crucial: sum for THIS GIN
            ).aggregate(sum_received_qty=Sum('received_qty'))['sum_received_qty'] or 0.0

            # PO quantity from tblMM_PO_Details.Qty
            po_qty = po_detail.qty or 0.0
            # Inward quantity from tblInv_Inward_Details.ReceivedQty (this is the 'InvQty' from ASP.NET)
            inward_qty = ind.received_qty or 0.0

            # The ASP.NET logic uses (inwqty - totrecqty) for remaining.
            # This 'inwqty' is the quantity recorded as "received" in the Inward Details table.
            # 'totrecqty' is the sum of quantities from previously created GRRs for this PO item.
            # The 'TotRemainQty' in the ASP.NET code is `inv_qty_from_inward_details - total_grr_qty_recorded_so_far`.
            remaining_to_receive_this_inward = inward_qty - total_grr_received_qty

            items_for_grr.append({
                'po_detail_id': po_detail.id,
                'item_id': item_master.id,
                'item_code': item_master.get_item_code_part_no(),
                'description': item_master.manf_desc,
                'uom': item_master.uom_basic.symbol if item_master.uom_basic else '',
                'po_qty': po_qty,
                'inward_qty': inward_qty, # This is lblInwrdqty (InvQty)
                'total_received_qty': total_grr_received_qty, # This is lbltotrecevdqty (TotRecQty)
                'remaining_qty_for_new_grr': max(0.0, remaining_to_receive_this_inward), # Max ensures no negative
                'file_name': item_master.file_name,
                'att_name': item_master.att_name,
                'can_receive': remaining_to_receive_this_inward > 0 # Determines if checkbox should be visible
            })
        return items_for_grr

    # Overriding save to inject auto-generated values
    def save(self, *args, **kwargs):
        # Placeholder for dynamic values like company_id, session_id, financial_year_id
        # In a real app, these would come from the request.user or a session middleware.
        # For demonstration, hardcoding or passing from view.
        if not self.company_id:
            self.company_id = 1 # Example: default company
        if not self.financial_year_id:
            self.financial_year_id = 1 # Example: default financial year
        if not self.session_id:
            self.session_id = 'SYSTEM' # Example: default user/session ID

        if not self.pk: # Only generate GRRNo on first save
            self.generate_next_grr_no(self.company_id, self.financial_year_id)
        
        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().time()
        super().save(*args, **kwargs)


class MaterialReceivedDetail(models.Model):
    """
    Represents line items for a Goods Received Receipt.
    This corresponds to tblinv_MaterialReceived_Details.
    """
    material_received_master = models.ForeignKey(MaterialReceivedMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    grr_no = models.CharField(max_length=50, db_column='GRRNo') # Redundant with FK, but kept for schema consistency
    po_detail = models.ForeignKey('PurchaseOrderDetail', on_delete=models.PROTECT, db_column='POId', related_name='grr_details')
    received_qty = models.FloatField(db_column='ReceivedQty')

    class Meta:
        db_table = 'tblinv_MaterialReceived_Details'
        verbose_name = 'Material Received Detail'
        verbose_name_plural = 'Material Received Details'
        # Adding a unique_together constraint might be necessary based on business rules
        # unique_together = (('material_received_master', 'po_detail'),)

    def __str__(self):
        return f"GRR: {self.material_received_master.grr_no} - Item PO: {self.po_detail.id} Qty: {self.received_qty}"

    def save(self, *args, **kwargs):
        # Ensure redundant GRRNo field is populated from master
        if not self.grr_no and self.material_received_master:
            self.grr_no = self.material_received_master.grr_no
        super().save(*args, **kwargs)

# --- Lookup Models (Managed by Django = False) ---
# These models are used for data retrieval from existing tables, not for write operations
# within this specific module.

class InwardMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    gin_no = models.CharField(max_length=50, db_column='GINNo')
    challan_no = models.CharField(max_length=50, db_column='ChallanNo')
    challan_date = models.DateField(db_column='ChallanDate')
    company_id = models.IntegerField(db_column='CompId')
    supplier = models.ForeignKey('Supplier', on_delete=models.PROTECT, db_column='SupplierId', related_name='inward_masters')
    po_no = models.CharField(max_length=50, db_column='PONo')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Master'
        verbose_name_plural = 'Inward Masters'

    def __str__(self):
        return self.gin_no

class InwardDetail(models.Model):
    # Note: tblInv_Inward_Details might not have its own primary key.
    # Assuming a composite primary key or that Django will infer it.
    # If not, add a dummy id field or handle with composite keys.
    gin_master = models.ForeignKey(InwardMaster, on_delete=models.PROTECT, db_column='GINId', related_name='inward_details')
    po_detail = models.ForeignKey('PurchaseOrderDetail', on_delete=models.PROTECT, db_column='POId', related_name='inward_details')
    received_qty = models.FloatField(db_column='ReceivedQty') # This is the "InvQty" mentioned in ASP.NET loadData()

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Inward Detail'
        verbose_name_plural = 'Inward Details'
        # Assuming a unique constraint based on common database patterns
        unique_together = (('gin_master', 'po_detail'),) # If this forms a natural primary key

    def __str__(self):
        return f"Inward Detail for {self.gin_master.gin_no} - PO Item: {self.po_detail.id}"


class Supplier(models.Model):
    supplier_id = models.IntegerField(primary_key=True, db_column='SupplierId')
    supplier_name = models.CharField(max_length=200, db_column='SupplierName')
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    po_no = models.CharField(max_length=50, db_column='PONo')
    pr_spr_flag = models.IntegerField(db_column='PRSPRFlag') # 0 for PR, 1 for SPR
    financial_year_id = models.IntegerField(db_column='FinYearId')
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'

    def __str__(self):
        return self.po_no

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    po_master = models.ForeignKey(PurchaseOrderMaster, on_delete=models.PROTECT, db_column='MId', related_name='po_details')
    po_no = models.CharField(max_length=50, db_column='PONo') # Redundant, but kept for schema consistency
    qty = models.FloatField(db_column='Qty')
    # Link to either PR or SPR details based on PRSPRFlag in master.
    # Null=True, blank=True because one of them will be None.
    pr_detail = models.ForeignKey('PurchaseRequisitionDetail', on_delete=models.PROTECT, db_column='PRId', null=True, blank=True, related_name='po_details')
    spr_detail = models.ForeignKey('SalesPurchaseRequisitionDetail', on_delete=models.PROTECT, db_column='SPRId', null=True, blank=True, related_name='po_details')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"PO Detail {self.id} for {self.po_master.po_no}"

class PurchaseRequisitionMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    pr_no = models.CharField(max_length=50, db_column='PRNo')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'Purchase Requisition Master'
        verbose_name_plural = 'Purchase Requisition Masters'

    def __str__(self):
        return self.pr_no

class PurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    pr_master = models.ForeignKey(PurchaseRequisitionMaster, on_delete=models.PROTECT, db_column='MId', related_name='pr_details')
    pr_no = models.CharField(max_length=50, db_column='PRNo')
    item_master = models.ForeignKey('Item', on_delete=models.PROTECT, db_column='ItemId', related_name='pr_details')
    account_head = models.ForeignKey('AccountHead', on_delete=models.PROTECT, db_column='AHId', related_name='pr_details')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'Purchase Requisition Detail'
        verbose_name_plural = 'Purchase Requisition Details'

    def __str__(self):
        return f"PR Detail {self.id} for {self.pr_master.pr_no}"

class SalesPurchaseRequisitionMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    spr_no = models.CharField(max_length=50, db_column='SPRNo')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'Sales Purchase Requisition Master'
        verbose_name_plural = 'Sales Purchase Requisition Masters'

    def __str__(self):
        return self.spr_no

class SalesPurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    spr_master = models.ForeignKey(SalesPurchaseRequisitionMaster, on_delete=models.PROTECT, db_column='MId', related_name='spr_details')
    spr_no = models.CharField(max_length=50, db_column='SPRNo')
    item_master = models.ForeignKey('Item', on_delete=models.PROTECT, db_column='ItemId', related_name='spr_details')
    account_head = models.ForeignKey('AccountHead', on_delete=models.PROTECT, db_column='AHId', related_name='spr_details')
    # DeptId was in ASP.NET but not explicitly used in item retrieval logic here.

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'Sales Purchase Requisition Detail'
        verbose_name_plural = 'Sales Purchase Requisition Details'

    def __str__(self):
        return f"SPR Detail {self.id} for {self.spr_master.spr_no}"

class Item(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    item_code = models.CharField(max_length=100, db_column='ItemCode')
    manf_desc = models.CharField(max_length=500, db_column='ManfDesc')
    uom_basic = models.ForeignKey('Unit', on_delete=models.PROTECT, db_column='UOMBasic', related_name='items')
    file_name = models.CharField(max_length=255, db_column='FileName', null=True, blank=True)
    file_data = models.BinaryField(db_column='FileData', null=True, blank=True) # Assuming VARBINARY is binary
    content_type = models.CharField(max_length=100, db_column='ContentType', null=True, blank=True)
    att_name = models.CharField(max_length=255, db_column='AttName', null=True, blank=True)
    att_data = models.BinaryField(db_column='AttData', null=True, blank=True)
    att_content_type = models.CharField(max_length=100, db_column='AttContentType', null=True, blank=True)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

    def get_item_code_part_no(self):
        """Replicates fun.GetItemCode_PartNo logic, assuming it's just ItemCode."""
        return self.item_code

class Unit(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class AccountHead(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    category = models.CharField(max_length=100, db_column='Category')

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.category

```

#### 4.2 Forms

**Task:** Define Django forms for user input.
We'll create a main form for `MaterialReceivedMaster` and a dynamic formset for `MaterialReceivedDetail` line items. The `received_qty` for each item will be validated against the `remaining_qty_for_new_grr` in the form's `clean` method.
These forms will reside in `inventory/forms.py`.

```python
# inventory/forms.py
from django import forms
from .models import MaterialReceivedMaster, MaterialReceivedDetail, InwardMaster, Item
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class MaterialReceivedMasterForm(forms.ModelForm):
    """
    Form for the main GRR header details.
    """
    tax_invoice_no = forms.CharField(
        max_length=100,
        required=True,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    tax_invoice_date = forms.DateField(
        required=True,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        input_formats=['%Y-%m-%d', '%d-%m-%Y', '%d-%m-%y'] # Support common date formats, including ASP.NET's dd-MM-yyyy
    )
    # The ASP.NET dropdowns for ModVatApp/ModVatInv used 'Select', '0' (No), '1' (Yes).
    # We will map this to a BooleanField with custom choices for UX consistency.
    modvat_applicable = forms.TypedChoiceField(
        choices=[('', 'Select'), ('0', 'No'), ('1', 'Yes')], # Use string keys to match ASP.NET values if needed, convert to bool in clean
        coerce=lambda x: True if x == '1' else (False if x == '0' else None), # Custom coercion
        empty_value=None,
        required=True,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    modvat_invoice = forms.TypedChoiceField(
        choices=[('', 'Select'), ('0', 'No'), ('1', 'Yes')],
        coerce=lambda x: True if x == '1' else (False if x == '0' else None),
        empty_value=None,
        required=True,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = MaterialReceivedMaster
        fields = ['tax_invoice_no', 'tax_invoice_date', 'modvat_applicable', 'modvat_invoice']
        # The 'gin_master' field will be set in the view based on URL parameters.
        # Other fields (grr_no, sys_date, etc.) are auto-generated or from session.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure 'Select' is the initial value for the dropdowns
        if not self.data: # Only set initial on GET request, not POST
            self.fields['modvat_applicable'].initial = ''
            self.fields['modvat_invoice'].initial = ''

    def clean(self):
        cleaned_data = super().clean()
        # Custom validation for 'Select' option
        if cleaned_data.get('modvat_applicable') is None:
            self.add_error('modvat_applicable', _("Please select an option for MODVAT Applicable."))
        if cleaned_data.get('modvat_invoice') is None:
            self.add_error('modvat_invoice', _("Please select an option for MODVAT Invoice."))
        return cleaned_data


class MaterialReceivedDetailForm(forms.Form):
    """
    A form representing a single line item in the GRR detail grid.
    This is a non-model form, used to capture received_qty and related item data.
    """
    # Hidden fields to pass necessary data for processing and validation
    po_detail_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    
    # Input field for the quantity being received
    received_qty = forms.FloatField(
        required=False, # This will be required only if `is_selected` is True
        min_value=0.001, # Quantity must be positive
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'})
    )
    # Checkbox to indicate if this item is being received
    is_selected = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'})
    )

    # Read-only fields to display data in the grid (not for direct input)
    item_code = forms.CharField(required=False, widget=forms.HiddenInput())
    description = forms.CharField(required=False, widget=forms.HiddenInput())
    uom = forms.CharField(required=False, widget=forms.HiddenInput())
    po_qty = forms.FloatField(required=False, widget=forms.HiddenInput())
    inward_qty = forms.FloatField(required=False, widget=forms.HiddenInput())
    total_received_qty = forms.FloatField(required=False, widget=forms.HiddenInput())
    remaining_qty_for_new_grr = forms.FloatField(required=False, widget=forms.HiddenInput())

    def __init__(self, *args, **kwargs):
        # Pass the maximum allowed quantity (remaining_qty_for_new_grr) for validation
        self.max_qty_for_validation = kwargs.pop('max_qty', None) 
        super().__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super().clean()
        is_selected = cleaned_data.get('is_selected')
        received_qty = cleaned_data.get('received_qty')
        
        # Apply validation only if the item is selected
        if is_selected:
            if received_qty is None:
                self.add_error('received_qty', _("Received quantity is required if selected."))
            elif received_qty <= 0:
                self.add_error('received_qty', _("Received quantity must be greater than zero."))
            elif self.max_qty_for_validation is not None and received_qty > self.max_qty_for_validation:
                self.add_error('received_qty', _(f"Received quantity cannot exceed remaining quantity ({self.max_qty_for_validation:.3f})."))
        else:
            # If not selected, ensure received_qty is not processed or validated as an error
            # This is important for formsets where not all rows are expected to have input
            cleaned_data['received_qty'] = 0.0 # Set to 0 if not selected

        return cleaned_data

# Formset for handling multiple MaterialReceivedDetailForm instances in the grid.
# `extra=0` means no empty forms are rendered by default, `can_delete=False` as no delete functionality.
MaterialReceivedDetailFormSet = forms.formset_factory(MaterialReceivedDetailForm, extra=0, can_delete=False)

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.
The main view will be `MaterialReceivedCreateView` to handle the form submission for `MaterialReceivedMaster` and its details. A `MaterialReceivedDetailTable` view will provide the HTMX-loaded grid data. File download functionality will be in a separate view.
These views will reside in `inventory/views.py`.

```python
# inventory/views.py
from django.views.generic import FormView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.shortcuts import get_object_or_404, render, redirect
from django.db import transaction
from django.http import HttpResponse, Http404
from django.template.loader import render_to_string
from django.utils.dateparse import parse_date
from django.utils.formats import date_format
from django.conf import settings
import os
import mimetypes

from .models import MaterialReceivedMaster, InwardMaster, MaterialReceivedDetail, Item
from .forms import MaterialReceivedMasterForm, MaterialReceivedDetailForm, MaterialReceivedDetailFormSet

# This view handles the main GRR creation page
class MaterialReceivedCreateView(FormView):
    template_name = 'inventory/grr_new_details.html'
    form_class = MaterialReceivedMasterForm
    success_url = reverse_lazy('grr_create') # Reloads the same page, mimicking ASP.NET behavior

    def get_inward_master(self):
        # Retrieve necessary query string parameters for InwardMaster context
        gin_no = self.request.GET.get('GINNo')
        supplier_id = self.request.GET.get('SupId') # supplier id from tblMM_Supplier_master
        inward_master_id = self.request.GET.get('Id') # This is MId in ASP.NET, referring to tblInv_Inward_Master.Id

        if not all([gin_no, supplier_id, inward_master_id]):
            # In a real app, this should redirect to an error page or GRR list
            raise Http404("Missing GINNo, SupId, or Id for Inward Master.")

        # Fetch InwardMaster and ensure it exists and matches supplier
        inward_master = get_object_or_404(InwardMaster.objects.select_related('supplier'),
                                          id=inward_master_id,
                                          gin_no=gin_no,
                                          supplier__supplier_id=supplier_id)
        return inward_master

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Populate header details from InwardMaster (mimics ASP.NET lbls)
        try:
            inward_master = self.get_inward_master()
            context['inward_master'] = inward_master
            # Format ChallanDate to dd-MM-yyyy as in ASP.NET
            context['challan_date_formatted'] = date_format(inward_master.challan_date, "d-m-Y")
        except Http404:
            messages.error(self.request, "Invalid GIN or Supplier ID provided.")
            context['inward_master'] = None
            context['challan_date_formatted'] = ''
        
        # Initial formset for the GRR items table
        if self.request.method == 'GET':
            # On GET, populate formset with data derived from InwardMaster
            if inward_master:
                grr_temp_master = MaterialReceivedMaster(gin_master=inward_master, gin_no=inward_master.gin_no)
                item_data = grr_temp_master.get_inward_items_for_receipt()
                initial_formset_data = []
                for item in item_data:
                    # Provide read-only fields as initial data
                    initial_formset_data.append({
                        'po_detail_id': item['po_detail_id'],
                        'item_id': item['item_id'],
                        'item_code': item['item_code'],
                        'description': item['description'],
                        'uom': item['uom'],
                        'po_qty': item['po_qty'],
                        'inward_qty': item['inward_qty'],
                        'total_received_qty': item['total_received_qty'],
                        'remaining_qty_for_new_grr': item['remaining_qty_for_new_grr'],
                        'received_qty': item['remaining_qty_for_new_grr'], # Default fill with remaining
                        'is_selected': item['can_receive'] # Auto-check if can receive
                    })
                context['detail_formset'] = MaterialReceivedDetailFormSet(initial=initial_formset_data, prefix='details')
            else:
                context['detail_formset'] = MaterialReceivedDetailFormSet(prefix='details')
        else:
            # On POST, populate formset with submitted data
            context['detail_formset'] = MaterialReceivedDetailFormSet(self.request.POST, prefix='details')

        return context

    def form_valid(self, form):
        # This method is for the MaterialReceivedMasterForm (header)
        inward_master = self.get_inward_master()

        # Instantiate formset with POST data
        detail_formset = MaterialReceivedDetailFormSet(self.request.POST, prefix='details')

        # Validate both forms
        if detail_formset.is_valid():
            try:
                with transaction.atomic():
                    # 1. Save MaterialReceivedMaster (Header)
                    grr_master = form.save(commit=False)
                    grr_master.gin_master = inward_master # Link to InwardMaster
                    grr_master.gin_no = inward_master.gin_no # Redundant GINNo field
                    # Assign user/session/company/financial year details
                    # In a real app, this would come from request.user.
                    # For demo, using placeholder values that MaterialReceivedMaster.save() will handle.
                    grr_master.save() # This will generate GRRNo and set sys_date/time

                    # 2. Save MaterialReceivedDetails (Line Items)
                    saved_details_count = 0
                    for detail_form in detail_formset:
                        if detail_form.cleaned_data.get('is_selected') and detail_form.cleaned_data.get('received_qty', 0) > 0:
                            po_detail_id = detail_form.cleaned_data['po_detail_id']
                            received_qty = detail_form.cleaned_data['received_qty']

                            # Perform a final check of remaining quantity from DB to prevent race conditions
                            # This is crucial for robust applications.
                            temp_master = MaterialReceivedMaster(gin_master=inward_master, gin_no=inward_master.gin_no)
                            current_item_data = next((item for item in temp_master.get_inward_items_for_receipt() if item['po_detail_id'] == po_detail_id), None)

                            if current_item_data and received_qty <= current_item_data['remaining_qty_for_new_grr']:
                                MaterialReceivedDetail.objects.create(
                                    material_received_master=grr_master,
                                    po_detail_id=po_detail_id,
                                    received_qty=received_qty,
                                    grr_no=grr_master.grr_no # Set redundant field
                                )
                                saved_details_count += 1
                            else:
                                # Log or add non-field error if validation fails at this stage
                                # This could happen due to concurrent updates
                                messages.warning(self.request, f"Skipped item {po_detail_id} due to quantity mismatch or concurrent update.")

                    if saved_details_count > 0:
                        messages.success(self.request, f"Goods Received Receipt {grr_master.grr_no} created successfully with {saved_details_count} items.")
                    else:
                        messages.info(self.request, "No items were selected or valid for receipt.")

                # If HTMX request, return a 204 No Content and trigger a refresh
                if self.request.headers.get('HX-Request'):
                    return HttpResponse(
                        status=204,
                        headers={
                            'HX-Trigger': 'refreshGRRPage' # Custom event to trigger page refresh
                        }
                    )
                return super().form_valid(form) # Redirect to success_url
            except Exception as e:
                # Catch any unexpected errors during save
                messages.error(self.request, f"An error occurred while saving the GRR: {e}")
                if self.request.headers.get('HX-Request'):
                    return HttpResponse(status=400, content="Error during save. Check logs.")
                return self.form_invalid(form) # Re-render with errors
        else:
            # If detail_formset is invalid, re-render the form with errors
            return self.form_invalid(form, detail_formset=detail_formset)

    def form_invalid(self, form, detail_formset=None):
        # Pass the invalid detail_formset to the context for rendering errors
        context = self.get_context_data(form=form)
        if detail_formset:
            context['detail_formset'] = detail_formset
        messages.error(self.request, "Please correct the errors in the form.")
        return render(self.request, self.template_name, context)

# View for downloading item files (images or spec sheets)
class ItemFileDownloadView(View):
    def get(self, request, item_id, file_type):
        item = get_object_or_404(Item, id=item_id)
        
        file_data = None
        file_name = None
        content_type = None

        if file_type == 'image':
            file_data = item.file_data
            file_name = item.file_name
            content_type = item.content_type
        elif file_type == 'spec':
            file_data = item.att_data
            file_name = item.att_name
            content_type = item.att_content_type
        else:
            raise Http404("Invalid file type.")

        if not file_data:
            raise Http404(f"No {file_type} data found for item {item_id}.")

        # Guess content type if not provided in DB
        if not content_type:
            content_type, _ = mimetypes.guess_type(file_name or 'download')
            if not content_type:
                content_type = 'application/octet-stream' # Default binary type

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name or file_type}"'
        return response

```

#### 4.4 Templates

**Task:** Create templates for the view.
The main page `grr_new_details.html` will contain the header form and include a partial template `_grr_items_table.html` for the dynamic item grid. The `_grr_items_table.html` will be loaded via HTMX and will be responsible for setting up DataTables.
These templates will reside in `inventory/templates/inventory/`.

**`inventory/templates/inventory/grr_new_details.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Goods Received Receipt - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-5xl">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg shadow-md mb-6">
        <h2 class="text-xl font-bold">Goods Received Receipt [GRR] - New</h2>
    </div>

    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if inward_master %}
    <div class="bg-white p-6 rounded-b-lg shadow-md mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-2 gap-x-4 mb-4 text-sm">
            <div class="flex items-center">
                <span class="font-medium w-28 text-left">GIN No:</span>
                <span class="font-bold">{{ inward_master.gin_no }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-medium w-28 text-left">Challan No:</span>
                <span class="font-bold">{{ inward_master.challan_no }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-medium w-28 text-left">Date:</span>
                <span class="font-bold">{{ challan_date_formatted }}</span>
            </div>
            <div class="flex items-center col-span-full md:col-span-2">
                <span class="font-medium w-28 text-left">Supplier:</span>
                <span class="font-bold">{{ inward_master.supplier.supplier_name }}</span>
            </div>
        </div>

        <hr class="my-4 border-gray-200" />

        <form method="post" hx-post="{% url 'grr_create' %}?GINNo={{ inward_master.gin_no }}&SupId={{ inward_master.supplier.supplier_id }}&Id={{ inward_master.id }}" 
              hx-swap="outerHTML" hx-target="#grr-form-container" hx-trigger="submit">
            {% csrf_token %}
            <div id="grr-form-container" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-center">
                    <div>
                        <label for="{{ form.tax_invoice_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Tax Invoice No.:</label>
                        {{ form.tax_invoice_no }}
                        {% if form.tax_invoice_no.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tax_invoice_no.errors|join:", " }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.tax_invoice_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Date:</label>
                        {{ form.tax_invoice_date }}
                        {% if form.tax_invoice_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.tax_invoice_date.errors|join:", " }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.modvat_applicable.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">MODVAT Applicable:</label>
                        {{ form.modvat_applicable }}
                        {% if form.modvat_applicable.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.modvat_applicable.errors|join:", " }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.modvat_invoice.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">MODVAT Invoice:</label>
                        {{ form.modvat_invoice }}
                        {% if form.modvat_invoice.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.modvat_invoice.errors|join:", " }}</p>
                        {% endif %}
                    </div>
                </div>

                <div id="grr-items-table-container"
                     hx-trigger="load, refreshGRRItems from:body"
                     hx-get="{% url 'grr_items_table' %}?GINNo={{ inward_master.gin_no }}&SupId={{ inward_master.supplier.supplier_id }}&Id={{ inward_master.id }}"
                     hx-swap="innerHTML">
                    <!-- The items table will be loaded here via HTMX -->
                    <div class="text-center p-8">
                        <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading GRR items...</p>
                    </div>
                </div>

                <div class="mt-6 flex justify-center space-x-4">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-200">
                        Add GRR
                    </button>
                    <a href="{% url 'grr_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-lg transition duration-200">
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
    {% else %}
    <div class="bg-white p-6 rounded-b-lg shadow-md mb-6 text-center text-red-600">
        <p>Could not load Inward Master details. Please ensure valid parameters are provided.</p>
        <a href="{% url 'grr_list' %}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Go to GRR List</a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables CDN - assumed to be in base.html already for production setup -->
<!-- If not in base.html, add here: -->
<!-- <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css"> -->
<!-- <script type="text/javascript" src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script> -->

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('grrItemRow', (initialIsSelected, initialReceivedQty, maxQty) => ({
            isSelected: initialIsSelected,
            receivedQty: initialReceivedQty,
            maxQty: maxQty,
            init() {
                // Ensure initial state is correctly set based on model data
                if (!this.isSelected) {
                    this.receivedQty = 0; // Clear quantity if not selectable or not selected
                }
            },
            toggleSelection() {
                this.isSelected = !this.isSelected;
                if (!this.isSelected) {
                    this.receivedQty = 0; // Clear quantity if deselected
                } else {
                    // Optionally pre-fill with maxQty if selected
                    this.receivedQty = this.maxQty > 0 ? this.maxQty : 0;
                }
            },
            validateQty() {
                let qty = parseFloat(this.receivedQty);
                if (this.isSelected) {
                    if (isNaN(qty) || qty <= 0) {
                        this.receivedQty = ''; // Clear if invalid or zero
                    } else if (this.maxQty !== null && qty > this.maxQty) {
                        this.receivedQty = this.maxQty; // Cap at max
                    }
                } else {
                    this.receivedQty = 0; // Ensure 0 if not selected
                }
            }
        }));
    });

    // Listen for custom event to trigger full page refresh after successful GRR creation
    document.body.addEventListener('refreshGRRPage', function() {
        location.reload(); // Simple full page reload to show new state
    });

    // Helper for DataTables initialization within HTMX loaded content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'grr-items-table-container') {
            $('#grrItemsTable').DataTable({
                "pagingType": "full_numbers", // or "simple_numbers"
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pageLength": 10,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "columnDefs": [
                    { "orderable": false, "targets": [0, 1, 9, 10] }, // SN, Checkbox, Image, Spec Sheet
                    { "width": "3%", "targets": 0 }, // SN
                    { "width": "2%", "targets": 1 }, // Checkbox
                    { "width": "12%", "targets": 2 }, // Item Id (hidden)
                    { "width": "5%", "targets": 3 }, // Image
                    { "width": "8%", "targets": 4 }, // Spec. Sheet
                    { "width": "12%", "targets": 5 }, // Item Code
                    { "width": "28%", "targets": 6 }, // Description
                    { "width": "7%", "targets": 7 }, // UOM
                    { "width": "8%", "targets": 8 }, // PO Qty
                    { "width": "8%", "targets": 9 }, // Inward Qty
                    { "width": "10%", "targets": 10 },// Tot Reced Qty
                    { "width": "12%", "targets": 11 },// Reced Qty
                ]
            });
        }
    });
</script>
{% endblock %}
```

**`inventory/templates/inventory/_grr_items_table.html`**

```html
<div class="overflow-x-auto">
    <table id="grrItemsTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-md">
        <thead>
            <tr class="bg-gray-100 text-gray-600 uppercase text-xs leading-normal">
                <th class="py-3 px-6 text-left">SN</th>
                <th class="py-3 px-6 text-center"></th> {# Checkbox header #}
                <th class="py-3 px-6 text-left hidden">Item Id</th> {# Hidden, but keeping for column index consistency #}
                <th class="py-3 px-6 text-center">Image</th>
                <th class="py-3 px-6 text-center">Spec. Sheet</th>
                <th class="py-3 px-6 text-left">Item Code</th>
                <th class="py-3 px-6 text-left">Description</th>
                <th class="py-3 px-6 text-center">UOM</th>
                <th class="py-3 px-6 text-right">PO Qty</th>
                <th class="py-3 px-6 text-right">Inward Qty</th>
                <th class="py-3 px-6 text-right">Tot Reced Qty</th>
                <th class="py-3 px-6 text-left">Reced Qty</th>
            </tr>
        </thead>
        <tbody>
            {% for form in detail_formset %}
            <tr class="border-b border-gray-200 hover:bg-gray-50" x-data="grrItemRow({{ form.initial.is_selected|yesno:'true,false' }}, {{ form.initial.received_qty|default:0.0 }}, {{ form.initial.remaining_qty_for_new_grr|default:'null' }})">
                <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-6 text-center">
                    {# Hidden checkbox is required for formset to register the item.
                       The visible checkbox drives the Alpine.js state. #}
                    {{ form.is_selected.as_hidden }} 
                    <input type="checkbox" 
                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded" 
                           x-model="isSelected" 
                           x-on:change="toggleSelection; $el.nextElementSibling.value = isSelected ? 'True' : 'False'">
                </td>
                <td class="py-3 px-6 text-left hidden">{{ form.initial.item_id }}</td> {# Hidden #}
                
                {# File download links #}
                <td class="py-3 px-6 text-center">
                    {% if form.initial.file_name %}
                    <a href="{% url 'item_file_download' item_id=form.initial.item_id file_type='image' %}" 
                       class="text-blue-600 hover:text-blue-800 font-semibold" target="_blank">View</a>
                    {% endif %}
                </td>
                <td class="py-3 px-6 text-center">
                    {% if form.initial.att_name %}
                    <a href="{% url 'item_file_download' item_id=form.initial.item_id file_type='spec' %}" 
                       class="text-blue-600 hover:text-blue-800 font-semibold" target="_blank">View</a>
                    {% endif %}
                </td>

                {# Display fields #}
                <td class="py-3 px-6 text-left">{{ form.initial.item_code }}</td>
                <td class="py-3 px-6 text-left">{{ form.initial.description }}</td>
                <td class="py-3 px-6 text-center">{{ form.initial.uom }}</td>
                <td class="py-3 px-6 text-right">{{ form.initial.po_qty|floatformat:3 }}</td>
                <td class="py-3 px-6 text-right">{{ form.initial.inward_qty|floatformat:3 }}</td>
                <td class="py-3 px-6 text-right">{{ form.initial.total_received_qty|floatformat:3 }}</td>
                <td class="py-3 px-6 text-left">
                    {# Hidden input for formset processing #}
                    {{ form.po_detail_id.as_hidden }} 
                    {{ form.item_id.as_hidden }}
                    {{ form.item_code.as_hidden }}
                    {{ form.description.as_hidden }}
                    {{ form.uom.as_hidden }}
                    {{ form.po_qty.as_hidden }}
                    {{ form.inward_qty.as_hidden }}
                    {{ form.total_received_qty.as_hidden }}
                    {{ form.remaining_qty_for_new_grr.as_hidden }}

                    <template x-if="isSelected">
                        <div class="relative">
                            <input type="number" 
                                   name="{{ form.received_qty.html_name }}" 
                                   step="0.001" 
                                   min="0.001" 
                                   :max="maxQty" 
                                   x-model.number="receivedQty"
                                   x-on:blur="validateQty"
                                   class="box3 w-full px-2 py-1 border rounded-md shadow-sm 
                                          {% if form.received_qty.errors %}border-red-500{% else %}border-gray-300{% endif %} 
                                          focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            {% if form.received_qty.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.received_qty.errors|join:", " }}</p>
                            {% endif %}
                            {# Display remaining qty as an overlay/tooltip or text #}
                            <div class="absolute right-1 top-1 text-xs text-gray-500 bg-white px-1 py-0.5 rounded" 
                                 title="Max allowed quantity">
                                <span x-text="maxQty.toFixed(3)"></span>
                            </div>
                        </div>
                    </template>
                    <template x-if="!isSelected">
                        <span class="text-gray-500" x-text="remaining_qty_for_new_grr.toFixed(3)"></span>
                    </template>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-3 px-6 text-center text-gray-500">No items found for this Inward Note.</td>
            </tr>
            {% endfor %}
            {{ detail_formset.management_form }} {# Crucial for formsets #}
        </tbody>
    </table>
</div>

```

#### 4.5 URLs

**Task:** Define URL patterns for the views.
These URLs will reside in `inventory/urls.py` within the `inventory` app, and then be included in the project's main `urls.py`.

```python
# inventory/urls.py
from django.urls import path
from .views import MaterialReceivedCreateView, ItemFileDownloadView # Assuming grr_list is in another module

urlpatterns = [
    # Main GRR creation page
    # Accepts GINNo, SupId, Id as query parameters to identify the Inward Master
    path('grr/new-details/', MaterialReceivedCreateView.as_view(), name='grr_create'),

    # HTMX endpoint for loading the GRR items table
    # This URL is called by HTMX to get the table content dynamically
    path('grr/new-details/items-table/', MaterialReceivedCreateView.as_view(), name='grr_items_table'),

    # URL for downloading item images/spec sheets
    path('items/<int:item_id>/download/<str:file_type>/', ItemFileDownloadView.as_view(), name='item_file_download'),

    # Placeholder for redirect after cancel or success (actual list view)
    path('grr/list/', MaterialReceivedCreateView.as_view(), name='grr_list'), # Redirect to a dummy list page
]

# Add this to your project's main urls.py
# from django.urls import path, include
# urlpatterns = [
#     path('inventory/', include('inventory.urls')),
# ]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.
We will include comprehensive unit tests for model methods and properties, and integration tests for all views.
These tests will reside in `inventory/tests.py`.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time
from unittest.mock import patch, MagicMock

from .models import (
    MaterialReceivedMaster, MaterialReceivedDetail, InwardMaster, InwardDetail,
    Supplier, PurchaseOrderMaster, PurchaseOrderDetail,
    PurchaseRequisitionMaster, PurchaseRequisitionDetail,
    SalesPurchaseRequisitionMaster, SalesPurchaseRequisitionDetail,
    Item, Unit, AccountHead
)

class InventoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data (managed=False models)
        cls.company_id = 1
        cls.fin_year_id = 2024
        
        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name="Test Supplier Co.", company_id=cls.company_id)
        cls.account_head_material = AccountHead.objects.create(id=1, category="With Material")
        cls.account_head_service = AccountHead.objects.create(id=2, category="Service")
        cls.unit_ea = Unit.objects.create(id=1, symbol="EA")
        cls.unit_kg = Unit.objects.create(id=2, symbol="KG")

        cls.item1 = Item.objects.create(id=101, item_code="ITEM001", manf_desc="Test Item 1", uom_basic=cls.unit_ea, company_id=cls.company_id, file_name="img1.jpg", att_name="spec1.pdf")
        cls.item2 = Item.objects.create(id=102, item_code="ITEM002", manf_desc="Test Item 2", uom_basic=cls.unit_kg, company_id=cls.company_id)

        # PR/SPR masters
        cls.pr_master = PurchaseRequisitionMaster.objects.create(id=1, pr_no="PR001", company_id=cls.company_id, financial_year_id=cls.fin_year_id)
        cls.spr_master = SalesPurchaseRequisitionMaster.objects.create(id=1, spr_no="SPR001", company_id=cls.company_id, financial_year_id=cls.fin_year_id)

        # PR/SPR details linked to items and account heads
        cls.pr_detail1 = PurchaseRequisitionDetail.objects.create(id=1001, pr_master=cls.pr_master, pr_no="PR001", item_master=cls.item1, account_head=cls.account_head_material)
        cls.pr_detail2 = PurchaseRequisitionDetail.objects.create(id=1002, pr_master=cls.pr_master, pr_no="PR001", item_master=cls.item2, account_head=cls.account_head_service) # Service item

        cls.spr_detail1 = SalesPurchaseRequisitionDetail.objects.create(id=2001, spr_master=cls.spr_master, spr_no="SPR001", item_master=cls.item2, account_head=cls.account_head_material)

        # PO Masters and Details
        cls.po_master_pr = PurchaseOrderMaster.objects.create(id=1, po_no="PO001", pr_spr_flag=0, company_id=cls.company_id, financial_year_id=cls.fin_year_id)
        cls.po_master_spr = PurchaseOrderMaster.objects.create(id=2, po_no="PO002", pr_spr_flag=1, company_id=cls.company_id, financial_year_id=cls.fin_year_id)

        cls.po_detail_item1_pr = PurchaseOrderDetail.objects.create(id=1, po_master=cls.po_master_pr, po_no="PO001", qty=10.0, pr_detail=cls.pr_detail1)
        cls.po_detail_item2_pr_service = PurchaseOrderDetail.objects.create(id=2, po_master=cls.po_master_pr, po_no="PO001", qty=5.0, pr_detail=cls.pr_detail2) # Service item
        cls.po_detail_item2_spr = PurchaseOrderDetail.objects.create(id=3, po_master=cls.po_master_spr, po_no="PO002", qty=20.0, spr_detail=cls.spr_detail1)

        # Inward Master and Details
        cls.inward_master = InwardMaster.objects.create(
            id=1, gin_no="GIN001", challan_no="CH001", challan_date=date(2024, 6, 1),
            company_id=cls.company_id, supplier=cls.supplier, po_no="PO001", financial_year_id=cls.fin_year_id
        )
        cls.inward_detail_item1 = InwardDetail.objects.create(gin_master=cls.inward_master, po_detail=cls.po_detail_item1_pr, received_qty=10.0)
        cls.inward_detail_item2_spr = InwardDetail.objects.create(gin_master=cls.inward_master, po_detail=cls.po_detail_item2_spr, received_qty=20.0)

    def test_material_received_master_creation_and_grr_no_generation(self):
        grr_master = MaterialReceivedMaster.objects.create(
            gin_master=self.inward_master,
            gin_no=self.inward_master.gin_no,
            tax_invoice_no="INV001",
            tax_invoice_date=date(2024, 6, 15),
            modvat_applicable=True,
            modvat_invoice=False,
            company_id=self.company_id,
            financial_year_id=self.fin_year_id,
            session_id="testuser"
        )
        self.assertIsNotNone(grr_master.pk)
        self.assertEqual(grr_master.grr_no, '0001') # First GRR
        self.assertEqual(grr_master.gin_master, self.inward_master)
        self.assertEqual(grr_master.gin_no, self.inward_master.gin_no)
        self.assertEqual(grr_master.tax_invoice_no, "INV001")
        self.assertIsInstance(grr_master.sys_date, date)
        self.assertIsInstance(grr_master.sys_time, time)

        # Test subsequent GRR number generation
        grr_master_2 = MaterialReceivedMaster.objects.create(
            gin_master=self.inward_master,
            gin_no=self.inward_master.gin_no,
            tax_invoice_no="INV002",
            tax_invoice_date=date(2024, 6, 16),
            modvat_applicable=False,
            modvat_invoice=True,
            company_id=self.company_id,
            financial_year_id=self.fin_year_id,
            session_id="testuser"
        )
        self.assertEqual(grr_master_2.grr_no, '0002')

    def test_get_inward_items_for_receipt_logic(self):
        # No previous GRR recorded yet, so remaining should be full inward qty
        grr_temp_master = MaterialReceivedMaster(gin_master=self.inward_master, gin_no=self.inward_master.gin_no)
        items = grr_temp_master.get_inward_items_for_receipt()
        
        self.assertEqual(len(items), 2) # Only material items
        item1_data = next(item for item in items if item['po_detail_id'] == self.po_detail_item1_pr.id)
        item2_data = next(item for item in items if item['po_detail_id'] == self.po_detail_item2_spr.id)

        self.assertEqual(item1_data['item_code'], self.item1.item_code)
        self.assertEqual(item1_data['po_qty'], 10.0)
        self.assertEqual(item1_data['inward_qty'], 10.0) # ReceivedQty from InwardDetail
        self.assertEqual(item1_data['total_received_qty'], 0.0)
        self.assertEqual(item1_data['remaining_qty_for_new_grr'], 10.0)
        self.assertTrue(item1_data['can_receive'])
        self.assertEqual(item1_data['uom'], "EA")

        self.assertEqual(item2_data['item_code'], self.item2.item_code)
        self.assertEqual(item2_data['po_qty'], 20.0)
        self.assertEqual(item2_data['inward_qty'], 20.0)
        self.assertEqual(item2_data['total_received_qty'], 0.0)
        self.assertEqual(item2_data['remaining_qty_for_new_grr'], 20.0)
        self.assertTrue(item2_data['can_receive'])
        self.assertEqual(item2_data['uom'], "KG")

        # Test with partial receipt
        grr_master_prev = MaterialReceivedMaster.objects.create(
            gin_master=self.inward_master, gin_no=self.inward_master.gin_no,
            tax_invoice_no="INV003", tax_invoice_date=date(2024, 6, 17),
            modvat_applicable=True, modvat_invoice=False,
            company_id=self.company_id, financial_year_id=self.fin_year_id, session_id="testuser"
        )
        MaterialReceivedDetail.objects.create(
            material_received_master=grr_master_prev, po_detail=self.po_detail_item1_pr, received_qty=5.0
        )

        items_after_receipt = grr_temp_master.get_inward_items_for_receipt()
        item1_data_updated = next(item for item in items_after_receipt if item['po_detail_id'] == self.po_detail_item1_pr.id)
        item2_data_updated = next(item for item in items_after_receipt if item['po_detail_id'] == self.po_detail_item2_spr.id)

        self.assertEqual(item1_data_updated['total_received_qty'], 5.0)
        self.assertEqual(item1_data_updated['remaining_qty_for_new_grr'], 5.0) # 10 (inward) - 5 (received)
        self.assertTrue(item1_data_updated['can_receive'])

        self.assertEqual(item2_data_updated['total_received_qty'], 0.0) # Item 2 not affected
        self.assertEqual(item2_data_updated['remaining_qty_for_new_grr'], 20.0)
        self.assertTrue(item2_data_updated['can_receive'])
        
        # Test with full receipt (remaining_qty_for_new_grr should be 0)
        MaterialReceivedDetail.objects.create(
            material_received_master=grr_master_prev, po_detail=self.po_detail_item1_pr, received_qty=5.0
        ) # Total 10.0 received for item1

        items_after_full_receipt = grr_temp_master.get_inward_items_for_receipt()
        item1_data_full = next(item for item in items_after_full_receipt if item['po_detail_id'] == self.po_detail_item1_pr.id)
        self.assertEqual(item1_data_full['total_received_qty'], 10.0)
        self.assertEqual(item1_data_full['remaining_qty_for_new_grr'], 0.0)
        self.assertFalse(item1_data_full['can_receive'])

    def test_material_received_detail_creation(self):
        grr_master = MaterialReceivedMaster.objects.create(
            gin_master=self.inward_master, gin_no=self.inward_master.gin_no,
            tax_invoice_no="INV004", tax_invoice_date=date(2024, 6, 18),
            modvat_applicable=True, modvat_invoice=False,
            company_id=self.company_id, financial_year_id=self.fin_year_id, session_id="testuser"
        )
        detail = MaterialReceivedDetail.objects.create(
            material_received_master=grr_master,
            po_detail=self.po_detail_item1_pr,
            received_qty=7.5
        )
        self.assertIsNotNone(detail.pk)
        self.assertEqual(detail.material_received_master, grr_master)
        self.assertEqual(detail.po_detail, self.po_detail_item1_pr)
        self.assertEqual(detail.received_qty, 7.5)
        self.assertEqual(detail.grr_no, grr_master.grr_no) # Check redundant field

    def test_item_get_item_code_part_no(self):
        self.assertEqual(self.item1.get_item_code_part_no(), "ITEM001")


class MaterialReceivedCreateViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up a minimal test environment with necessary foreign key objects
        cls.company_id = 1
        cls.fin_year_id = 2024

        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name="Test Supplier Co.", company_id=cls.company_id)
        cls.account_head_material = AccountHead.objects.create(id=1, category="With Material")
        cls.unit_ea = Unit.objects.create(id=1, symbol="EA")
        cls.item1 = Item.objects.create(id=101, item_code="ITEM001", manf_desc="Test Item 1", uom_basic=cls.unit_ea, company_id=cls.company_id, file_name="image.jpg", att_name="spec.pdf")
        cls.pr_master = PurchaseRequisitionMaster.objects.create(id=1, pr_no="PR001", company_id=cls.company_id, financial_year_id=cls.fin_year_id)
        cls.pr_detail1 = PurchaseRequisitionDetail.objects.create(id=1001, pr_master=cls.pr_master, pr_no="PR001", item_master=cls.item1, account_head=cls.account_head_material)
        cls.po_master_pr = PurchaseOrderMaster.objects.create(id=1, po_no="PO001", pr_spr_flag=0, company_id=cls.company_id, financial_year_id=cls.fin_year_id)
        cls.po_detail_item1_pr = PurchaseOrderDetail.objects.create(id=1, po_master=cls.po_master_pr, po_no="PO001", qty=10.0, pr_detail=cls.pr_detail1)
        cls.inward_master = InwardMaster.objects.create(
            id=1, gin_no="GIN001", challan_no="CH001", challan_date=date(2024, 6, 1),
            company_id=cls.company_id, supplier=cls.supplier, po_no="PO001", financial_year_id=cls.fin_year_id
        )
        cls.inward_detail_item1 = InwardDetail.objects.create(gin_master=cls.inward_master, po_detail=cls.po_detail_item1_pr, received_qty=10.0)

        # URL parameters for the view
        cls.view_params = {
            'GINNo': cls.inward_master.gin_no,
            'SupId': cls.supplier.supplier_id,
            'Id': cls.inward_master.id,
        }
        cls.url = reverse('grr_create') + '?' + '&'.join([f'{k}={v}' for k,v in cls.view_params.items()])
        cls.item_table_url = reverse('grr_items_table') + '?' + '&'.join([f'{k}={v}' for k,v in cls.view_params.items()])
        
    def setUp(self):
        self.client = Client()
        # Mocking MaterialReceivedMaster.generate_next_grr_no for consistent GRR number in tests
        self.patcher = patch('inventory.models.MaterialReceivedMaster.generate_next_grr_no')
        self.mock_generate_grr_no = self.patcher.start()
        self.mock_generate_grr_no.side_effect = lambda comp_id, fin_id: setattr(MaterialReceivedMaster.objects.order_by('-id').first(), 'grr_no', '0001') if MaterialReceivedMaster.objects.exists() else setattr(MaterialReceivedMaster.objects.first(), 'grr_no', '0001')


    def tearDown(self):
        self.patcher.stop()

    def test_get_grr_create_view(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/grr_new_details.html')
        self.assertIn('form', response.context)
        self.assertIn('inward_master', response.context)
        self.assertIn('detail_formset', response.context)
        
        # Check if header data is present
        self.assertContains(response, self.inward_master.gin_no)
        self.assertContains(response, self.inward_master.supplier.supplier_name)

        # Check initial formset data
        detail_formset = response.context['detail_formset']
        self.assertEqual(len(detail_formset), 1) # Only one material item expected
        form = detail_formset[0]
        self.assertEqual(form.initial['po_detail_id'], self.po_detail_item1_pr.id)
        self.assertEqual(form.initial['item_code'], self.item1.item_code)
        self.assertEqual(form.initial['remaining_qty_for_new_grr'], 10.0)
        self.assertTrue(form.initial['is_selected']) # Should be auto-selected

    def test_post_grr_create_view_success(self):
        self.mock_generate_grr_no.side_effect = lambda comp_id, fin_id: None # Prevent actual GRR number generation by mock
        
        # Simulate a successful GRR creation
        post_data = {
            'tax_invoice_no': 'TAXINV001',
            'tax_invoice_date': '2024-06-20',
            'modvat_applicable': '1', # 'Yes'
            'modvat_invoice': '0',    # 'No'
            'details-TOTAL_FORMS': '1',
            'details-INITIAL_FORMS': '1',
            'details-MIN_NUM_FORMS': '0',
            'details-MAX_NUM_FORMS': '',
            'details-0-po_detail_id': self.po_detail_item1_pr.id,
            'details-0-item_id': self.item1.id,
            'details-0-received_qty': '5.0',
            'details-0-is_selected': 'on', # Checkbox checked
            'details-0-item_code': 'ITEM001', # Include read-only fields for consistency if form needs them
            'details-0-description': 'Test Item 1',
            'details-0-uom': 'EA',
            'details-0-po_qty': '10.0',
            'details-0-inward_qty': '10.0',
            'details-0-total_received_qty': '0.0',
            'details-0-remaining_qty_for_new_grr': '10.0',
        }
        
        # Mimic HTMX request
        response = self.client.post(self.url, post_data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(MaterialReceivedMaster.objects.filter(tax_invoice_no='TAXINV001').exists())
        grr_master = MaterialReceivedMaster.objects.get(tax_invoice_no='TAXINV001')
        self.assertTrue(MaterialReceivedDetail.objects.filter(material_received_master=grr_master, received_qty=5.0).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshGRRPage')

        # Test another GRR creation with remaining quantity
        post_data_2 = {
            'tax_invoice_no': 'TAXINV002',
            'tax_invoice_date': '2024-06-21',
            'modvat_applicable': '0',
            'modvat_invoice': '1',
            'details-TOTAL_FORMS': '1',
            'details-INITIAL_FORMS': '1',
            'details-MIN_NUM_FORMS': '0',
            'details-MAX_NUM_FORMS': '',
            'details-0-po_detail_id': self.po_detail_item1_pr.id,
            'details-0-item_id': self.item1.id,
            'details-0-received_qty': '5.0', # Remaining was 5.0
            'details-0-is_selected': 'on',
            'details-0-item_code': 'ITEM001',
            'details-0-description': 'Test Item 1',
            'details-0-uom': 'EA',
            'details-0-po_qty': '10.0',
            'details-0-inward_qty': '10.0',
            'details-0-total_received_qty': '5.0', # From previous GRR
            'details-0-remaining_qty_for_new_grr': '5.0',
        }
        response_2 = self.client.post(self.url, post_data_2, HTTP_HX_REQUEST='true')
        self.assertEqual(response_2.status_code, 204)
        self.assertTrue(MaterialReceivedMaster.objects.filter(tax_invoice_no='TAXINV002').exists())
        self.assertEqual(MaterialReceivedDetail.objects.filter(po_detail=self.po_detail_item1_pr).count(), 2) # Two details for same PO item

    def test_post_grr_create_view_validation_fail_header(self):
        post_data = {
            'tax_invoice_no': '', # Missing
            'tax_invoice_date': 'invalid-date', # Invalid
            'modvat_applicable': '', # 'Select'
            'modvat_invoice': '',    # 'Select'
            'details-TOTAL_FORMS': '0',
            'details-INITIAL_FORMS': '0',
            'details-MIN_NUM_FORMS': '0',
            'details-MAX_NUM_FORMS': '',
        }
        response = self.client.post(self.url, post_data) # Not an HTMX request to see error rendering

        self.assertEqual(response.status_code, 200) # Should re-render with errors
        self.assertContains(response, "This field is required.")
        self.assertContains(response, "Enter a valid date.")
        self.assertFalse(MaterialReceivedMaster.objects.exists()) # No object created

    def test_post_grr_create_view_validation_fail_detail_qty(self):
        # Simulate a scenario where received_qty exceeds remaining_qty
        post_data = {
            'tax_invoice_no': 'TAXINV003',
            'tax_invoice_date': '2024-06-22',
            'modvat_applicable': '1',
            'modvat_invoice': '0',
            'details-TOTAL_FORMS': '1',
            'details-INITIAL_FORMS': '1',
            'details-MIN_NUM_FORMS': '0',
            'details-MAX_NUM_FORMS': '',
            'details-0-po_detail_id': self.po_detail_item1_pr.id,
            'details-0-item_id': self.item1.id,
            'details-0-received_qty': '100.0', # Too high
            'details-0-is_selected': 'on',
            'details-0-item_code': 'ITEM001',
            'details-0-description': 'Test Item 1',
            'details-0-uom': 'EA',
            'details-0-po_qty': '10.0',
            'details-0-inward_qty': '10.0',
            'details-0-total_received_qty': '0.0',
            'details-0-remaining_qty_for_new_grr': '10.0',
        }
        response = self.client.post(self.url, post_data)
        self.assertEqual(response.status_code, 200) # Re-renders with error
        self.assertContains(response, "Received quantity cannot exceed remaining quantity (10.000).")
        self.assertFalse(MaterialReceivedMaster.objects.filter(tax_invoice_no='TAXINV003').exists())

    def test_post_grr_create_view_no_items_selected(self):
        post_data = {
            'tax_invoice_no': 'TAXINV004',
            'tax_invoice_date': '2024-06-23',
            'modvat_applicable': '1',
            'modvat_invoice': '0',
            'details-TOTAL_FORMS': '1',
            'details-INITIAL_FORMS': '1',
            'details-MIN_NUM_FORMS': '0',
            'details-MAX_NUM_FORMS': '',
            'details-0-po_detail_id': self.po_detail_item1_pr.id,
            'details-0-item_id': self.item1.id,
            'details-0-received_qty': '5.0',
            'details-0-is_selected': '', # Checkbox NOT checked
            'details-0-item_code': 'ITEM001',
            'details-0-description': 'Test Item 1',
            'details-0-uom': 'EA',
            'details-0-po_qty': '10.0',
            'details-0-inward_qty': '10.0',
            'details-0-total_received_qty': '0.0',
            'details-0-remaining_qty_for_new_grr': '10.0',
        }
        response = self.client.post(self.url, post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Still 204 because header form is valid
        self.assertTrue(MaterialReceivedMaster.objects.filter(tax_invoice_no='TAXINV004').exists())
        grr_master = MaterialReceivedMaster.objects.get(tax_invoice_no='TAXINV004')
        self.assertFalse(MaterialReceivedDetail.objects.filter(material_received_master=grr_master).exists()) # No details created

    def test_get_item_file_download_image(self):
        url = reverse('item_file_download', args=[self.item1.id, 'image'])
        
        # Mock the binary data in the model instance directly for testing
        mock_file_data = b"image_binary_data"
        self.item1.file_data = mock_file_data
        self.item1.content_type = "image/jpeg"
        self.item1.save() # Save the mock data to the object

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/jpeg')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="image.jpg"')
        self.assertEqual(response.content, mock_file_data)

    def test_get_item_file_download_spec(self):
        url = reverse('item_file_download', args=[self.item1.id, 'spec'])

        mock_att_data = b"spec_sheet_binary_data"
        self.item1.att_data = mock_att_data
        self.item1.att_content_type = "application/pdf"
        self.item1.save()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="spec.pdf"')
        self.assertEqual(response.content, mock_att_data)

    def test_get_item_file_download_no_data(self):
        self.item1.file_data = None
        self.item1.att_data = None
        self.item1.save()

        url_image = reverse('item_file_download', args=[self.item1.id, 'image'])
        response_image = self.client.get(url_image)
        self.assertEqual(response_image.status_code, 404)

        url_spec = reverse('item_file_download', args=[self.item1.id, 'spec'])
        response_spec = self.client.get(url_spec)
        self.assertEqual(response_spec.status_code, 404)

    def test_get_item_file_download_invalid_type(self):
        url = reverse('item_file_download', args=[self.item1.id, 'unsupported'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic content:**
    *   The `_grr_items_table.html` partial template is loaded dynamically into `grr_new_details.html` using `hx-get` and `hx-swap="innerHTML"`.
    *   The form submission for creating the GRR uses `hx-post`, `hx-swap="outerHTML"`, and `hx-target="#grr-form-container"`. Upon successful creation, the server returns `HTTP 204 No Content` with an `HX-Trigger` header (`refreshGRRPage`), which causes the entire page to reload (`location.reload()`) to reflect the updated state and potentially the next GRR number.
*   **Alpine.js for UI state management:**
    *   Each row in the item table uses an `x-data="grrItemRow(...)"` component to manage the `isSelected` state of the checkbox and the `receivedQty` input field.
    *   `x-model` binds these to the HTML elements.
    *   `x-on:change` and `x-on:blur` listeners trigger Alpine.js methods (`toggleSelection`, `validateQty`) to update the UI (show/hide input, pre-fill, cap quantity) and the hidden form input that the Django formset processes.
    *   The `validateQty` function within Alpine.js provides immediate client-side feedback for quantity constraints, similar to ASP.NET's client-side validation.
*   **DataTables for List Views:**
    *   The `_grr_items_table.html` partial contains a `<table>` with `id="grrItemsTable"`.
    *   A JavaScript block within `grr_new_details.html` (triggered by `htmx:afterSwap`) initializes this table with DataTables, enabling client-side search, sort, and pagination.
*   **Strict Separation:** Views remain thin by delegating complex data retrieval and business logic to model methods (`MaterialReceivedMaster.get_inward_items_for_receipt`) and form `clean` methods. HTML is never generated in views.
*   **DRY Templates:** `_grr_items_table.html` is a reusable partial template. The main `grr_new_details.html` extends `core/base.html` for a consistent layout.

## Final Notes

*   **Placeholder Replacement:** The generated code replaces various placeholders from the ASP.NET analysis (e.g., table names, column names) with their Django equivalents.
*   **Session Management:** The ASP.NET application heavily relies on `Session["compid"]`, `Session["finyear"]`, `Session["username"]`. In Django, this typically maps to `request.user.profile.company_id`, `request.user.profile.financial_year_id`, or `request.user.username` if using a custom user model or a dedicated user profile system. For this migration, placeholder values (`self.company_id = 1`, `self.financial_year_id = 1`, `self.session_id = 'SYSTEM'`) are added to the `MaterialReceivedMaster.save()` method and tests to allow the code to run. In a real system, these would be retrieved from the authentication and user context.
*   **Error Handling:** Basic error handling and messaging are included, but a production system would require more comprehensive logging and user-friendly error pages.
*   **Concurrency:** The quantity validation in `form_valid` includes a re-check of `remaining_qty_for_new_grr` just before saving to mitigate race conditions. This is a critical pattern for financial or inventory systems.
*   **File Storage:** For actual file handling, Django's `FileField` or a custom storage backend (e.g., S3) combined with the binary fields in the `Item` model (if direct DB storage is retained) would be used. The `ItemFileDownloadView` demonstrates serving binary data directly.
*   **GRR List Page:** A `grr_list` URL is referenced for navigation, but the actual view and template for this list page would need to be implemented separately, likely as a `ListView` similar to the pattern above, also utilizing DataTables.
*   **Testing Coverage:** The provided tests cover model logic, view rendering, and basic form submission with validation. Expanding test cases to include edge cases, concurrency scenarios, and more detailed HTMX interaction tests would be part of achieving high coverage.
```