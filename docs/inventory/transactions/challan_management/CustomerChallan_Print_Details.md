## ASP.NET to Django Conversion Script: Customer <PERSON>llan Print Details

This document outlines a strategic plan for migrating the Customer Challan Print Details functionality from ASP.NET to a modern Django application. The focus is on leveraging AI-assisted automation to transform the legacy Crystal Reports display into a dynamic, interactive web page using Django, HTMX, Alpine.js, and DataTables, aligning with a "Fat Model, Thin View" architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code utilizes complex SQL queries to generate reports from multiple joined tables rather than directly manipulating a single entity. The primary "master" record driving these reports is identified by an `Id` passed via the query string, which corresponds to `tblInv_Customer_Challan_Master`.

**Identified Tables and Key Columns:**

*   **`tblInv_Customer_Challan_Master`** (Main entity, aliased as `CustomerChallanMaster` in Django)
    *   `Id` (PK)
    *   `CompId` (Company ID)
    *   `CustomerId` (Customer ID)
    *   `SessionId` (User/Employee ID who generated)
    *   `SysTime` (System Time)
    *   `WONo` (Work Order Number)
    *   `CCNo` (Customer Challan Number)
    *   `SysDate` (Challan Date)
*   **`tblInv_Customer_Challan_Details`** (Challan Line Items, aliased as `CustomerChallanDetail`)
    *   `Id` (PK)
    *   `MId` (Foreign Key to `tblInv_Customer_Challan_Master.Id`)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`)
    *   `ChallanQty` (Challan Quantity)
*   **`tblInv_Customer_Challan_Clear`** (Challan Clearance Records, aliased as `CustomerChallanClear`)
    *   `Id` (PK)
    *   `DId` (Foreign Key to `tblInv_Customer_Challan_Details.Id`)
    *   `ClearQty` (Cleared Quantity)
    *   `CompId` (Company ID)
    *   `SessionId` (User/Employee ID who cleared)
    *   `SysDate` (Clearance Date)
    *   `SysTime` (Clearance Time)
    *   `FinYearId` (Financial Year ID)
*   **`tblDG_Item_Master`** (Item Master, aliased as `ItemMaster`)
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc` (Manufacturer Description, used as 'Description')
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`)
*   **`Unit_Master`** (Unit of Measurement, aliased as `UnitMaster`)
    *   `Id` (PK)
    *   `Symbol`
*   **`tblHR_OfficeStaff`** (Office Staff, aliased as `OfficeStaff`)
    *   `EmpId` (PK, used for `SessionId` lookup)
    *   `CompId`
    *   `Title`
    *   `EmployeeName`
*   **`SD_Cust_master`** (Customer Master, aliased as `CustomerMaster`)
    *   `CustomerId` (PK, used for `CustomerId` lookup)
    *   `CompId`
    *   `CustomerName`
    *   `RegdAddress`
    *   `ContactPerson`
    *   `ContactNo`
*   **(Inferred)** **`tblCompanyProfile`** (Company Details, aliased as `CompanyProfile`)
    *   `CompId` (PK, used for `fun.CompAdd` equivalent)
    *   `Address` (Company Address)

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page is exclusively a report viewer. It does not perform any Create, Update, or Delete (CRUD) operations on challan records. Its primary functions are:
*   **Read:** Fetching and displaying two distinct sets of data: "Customer Challan" and "Clear Challan" details, based on a `CustomerChallanMaster` ID.
*   **Data Aggregation/Transformation:** The C# code extensively joins multiple tables, calculates derived fields, and formats dates (`fun.FromDate`). This logic will be moved to Django models (Fat Model).
*   **Redirection:** "Cancel" buttons redirect to a previous page.

### Step 3: Infer UI Components

**Analysis:**
*   **TabContainer (`cc1:TabContainer`):** This is for organizing the two reports. In Django, this will be handled by Alpine.js for client-side tab switching.
*   **CrystalReportViewer (`CR:CrystalReportViewer`):** This is the core display component for the reports. It will be replaced by standard HTML tables, enhanced with DataTables for interactivity (sorting, searching, pagination).
*   **Buttons (`asp:Button`):** "Cancel" buttons for navigation. These will be simple HTML buttons or links in Django, with a `href` attribute for redirection.
*   **Dynamic Data Binding:** The ASP.NET page dynamically binds data to the Crystal Reports. In Django, this will involve passing data from the view (fetched by fat models) to the templates and rendering it within the DataTables.

### Step 4: Generate Django Code

We will create a new Django app, e.g., `inventory`, to house this functionality.

#### 4.1 Models

The complex data retrieval and structuring for the reports will be encapsulated within a custom manager for the `CustomerChallanMaster` model. This adheres to the "Fat Model" principle.

**`inventory/models.py`**

```python
from django.db import models
from django.db.models import F
from datetime import datetime

# --- Auxiliary Models (assumed to exist in the database) ---
# These models represent the related tables from which report data is sourced.
# They are marked as managed=False as they map to existing legacy tables.

class CompanyProfile(models.Model):
    """
    Dummy model for company details, assuming a table like 'tblCompanyProfile'
    exists in the legacy database to store company address and other static info.
    """
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompanyProfile' # Hypothetical table name, adjust as per actual DB
        verbose_name = 'Company Profile'
        verbose_name_plural = 'Company Profiles'

    def __str__(self):
        return f"Company {self.comp_id}"


class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, unique=True)
    manf_desc = models.TextField(db_column='ManfDesc', blank=True, null=True)
    uom_basic = models.ForeignKey('UnitMaster', on_delete=models.SET_NULL, db_column='UOMBasic', null=True, related_name='items')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code


class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol


class OfficeStaff(models.Model):
    # Assuming EmpId is unique and can serve as PK, adjust max_length as needed
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"


class CustomerMaster(models.Model):
    # Assuming CustomerId is unique and can serve as PK, adjust max_length as needed
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    regd_address = models.TextField(db_column='RegdAddress', blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"


# --- Core Challan Models ---

class ChallanReportManager(models.Manager):
    """
    Custom manager to encapsulate the complex report data fetching logic
    from the original ASP.NET code's fillGrid and LoadGrid methods.
    """
    def get_customer_challan_report_data(self, challan_master_id, company_id, session_user_id):
        """
        Fetches data for the 'Customer Challan' report tab.
        Corresponds to the fillGrid() method in the ASP.NET code-behind.
        """
        # Annotate fields from related models directly into the queryset results.
        # This mimics the flattening of data into a DataTable in ASP.NET.
        challan_details_qs = CustomerChallanDetail.objects.filter(
            master__id=challan_master_id,
            master__comp_id=company_id
        ).select_related(
            'master', 'item', 'item__uom_basic'
        ).values(
            'id', # This is tblInv_Customer_Challan_Details.Id
            'master__won_no',
            'master__cc_no',
            'master__sys_date',
            'master__sys_time',
            'master__comp_id',
            'master__customer_id',
            'master__session_id', # SessionId from Challan Master for 'GenBy'
            'item__item_code',
            'item__manf_desc',
            'item__uom_basic__symbol',
            'challan_qty',
        ).order_by('id')

        report_data = []
        master_info = {}

        if challan_details_qs.exists():
            # Extract common master data from the first row or the master object itself
            master_obj = self.get_queryset().get(id=challan_master_id)

            # Get generated by staff details (equivalent to sql2 query in fillGrid)
            gen_by_staff = OfficeStaff.objects.filter(comp_id=company_id, emp_id=master_obj.session_id).first()
            gen_by = f"{gen_by_staff.title}. {gen_by_staff.employee_name}" if gen_by_staff else "N/A"

            # Get customer details (equivalent to cmdStr query in fillGrid)
            customer = CustomerMaster.objects.filter(comp_id=company_id, customer_id=master_obj.customer_id).first()
            deliv_to = f"{customer.customer_name}[{customer.customer_id}]" if customer else "N/A"
            del_add = customer.regd_address if customer else "N/A"
            person = customer.contact_person if customer else "N/A"
            contact_no = customer.contact_no if customer else "N/A"

            # Get company address (equivalent to fun.CompAdd)
            company_profile = CompanyProfile.objects.filter(comp_id=company_id).first()
            company_address = company_profile.address if company_profile else "N/A"

            master_info = {
                'gen_by': gen_by,
                'deliv_to': deliv_to,
                'del_add': del_add,
                'person': person,
                'contact_no': contact_no,
                'company_address': company_address,
                'won_no': master_obj.won_no,
                'cc_no': master_obj.cc_no,
                'cc_date': master_obj.sys_date.strftime('%d/%m/%Y') if master_obj.sys_date else '',
                'sys_time': master_obj.sys_time,
                'customer_id': master_obj.customer_id,
                'master_id': master_obj.id,
            }

            for row in challan_details_qs:
                report_data.append({
                    'id': row['id'],
                    'won_no': row['master__won_no'],
                    'cc_no': row['master__cc_no'],
                    'cc_date': row['master__sys_date'].strftime('%d/%m/%Y') if row['master__sys_date'] else '',
                    'item_code': row['item__item_code'],
                    'description': row['item__manf_desc'],
                    'uom': row['item__uom_basic__symbol'],
                    'challan_qty': row['challan_qty'],
                    'sys_time': row['master__sys_time'],
                    'comp_id': row['master__comp_id'],
                })
        return report_data, master_info

    def get_customer_challan_clear_report_data(self, challan_master_id, company_id, current_fin_year_id, session_user_id):
        """
        Fetches data for the 'Clear Challan' report tab.
        Corresponds to the LoadGrid() method in the ASP.NET code-behind.
        """
        # Join across ChallanClear -> ChallanDetail -> ChallanMaster
        # and ChallanDetail -> ItemMaster -> UnitMaster
        clear_details_qs = CustomerChallanClear.objects.filter(
            comp_id=company_id,
            detail__master__id=challan_master_id,
            fin_year_id__lte=current_fin_year_id # From original code: And tblInv_Customer_Challan_Clear.FinYearId<='" + fyid + "'"
        ).select_related(
            'detail__master', 'detail__item', 'detail__item__uom_basic'
        ).values(
            'id', # This is tblInv_Customer_Challan_Clear.Id
            'sys_date',
            'sys_time',
            'comp_id',
            'session_id', # SessionId from Challan Clear for 'GenBy1'
            'detail__master__won_no',
            'detail__master__cc_no',
            'detail__master__customer_id',
            'detail__challan_qty',
            'clear_qty',
            item_code=F('detail__item__item_code'),
            manf_desc=F('detail__item__manf_desc'),
            symbol=F('detail__item__uom_basic__symbol'),
        ).order_by('id')

        report_data = []
        master_info = {}

        if clear_details_qs.exists():
            # Get master info (from the first clear row's linked master)
            first_clear_row = clear_details_qs.first()
            master_obj = self.get_queryset().get(id=first_clear_row['detail__master__id'])

            # Get generated by staff details for clear report (equivalent to sql2 query in LoadGrid)
            gen_by_staff_clear = OfficeStaff.objects.filter(comp_id=company_id, emp_id=first_clear_row['session_id']).first()
            gen_by_clear = f"{gen_by_staff_clear.title}. {gen_by_staff_clear.employee_name}" if gen_by_staff_clear else "N/A"

            # Get customer details for clear report (equivalent to cmdStr query in LoadGrid)
            customer_clear = CustomerMaster.objects.filter(comp_id=company_id, customer_id=first_clear_row['detail__master__customer_id']).first()
            deliv_to_clear = f"{customer_clear.customer_name}[{customer_clear.customer_id}]" if customer_clear else "N/A"
            del_add_clear = customer_clear.regd_address if customer_clear else "N/A"
            person_clear = customer_clear.contact_person if customer_clear else "N/A"
            contact_no_clear = customer_clear.contact_no if customer_clear else "N/A"
            
            company_profile = CompanyProfile.objects.filter(comp_id=company_id).first()
            company_address = company_profile.address if company_profile else "N/A"

            master_info = {
                'gen_by': gen_by_clear,
                'deliv_to': deliv_to_clear,
                'del_add': del_add_clear,
                'person': person_clear,
                'contact_no': contact_no_clear,
                'company_address': company_address,
                'won_no': master_obj.won_no,
                'cc_no': master_obj.cc_no,
                'cc_date': master_obj.sys_date.strftime('%d/%m/%Y') if master_obj.sys_date else '',
                'sys_time': master_obj.sys_time,
                'customer_id': master_obj.customer_id,
                'master_id': master_obj.id,
            }

            for row in clear_details_qs:
                report_data.append({
                    'id': row['id'],
                    'won_no': row['detail__master__won_no'],
                    'cc_no': row['detail__master__cc_no'],
                    'sys_date': row['sys_date'].strftime('%d/%m/%Y') if row['sys_date'] else '',
                    'item_code': row['item_code'],
                    'description': row['manf_desc'],
                    'uom': row['symbol'],
                    'challan_qty': row['detail__challan_qty'],
                    'clear_qty': row['clear_qty'],
                    'sys_time': row['sys_time'],
                    'comp_id': row['comp_id'],
                })
        return report_data, master_info


class CustomerChallanMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    customer_id = models.CharField(db_column='CustomerId', max_length=50) # Assuming string ID, link to CustomerMaster if needed
    session_id = models.CharField(db_column='SessionId', max_length=50) # Assuming string ID, link to OfficeStaff if needed
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True) # Stored as string, consider DateTimeField
    won_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    cc_no = models.CharField(db_column='CCNo', max_length=50, blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Master'
        verbose_name = 'Customer Challan Master'
        verbose_name_plural = 'Customer Challan Masters'

    def __str__(self):
        return f"Challan {self.cc_no} (ID: {self.id})"

    # Assign the custom manager for report-specific methods
    challans = ChallanReportManager()


class CustomerChallanDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(CustomerChallanMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    item = models.ForeignKey(ItemMaster, on_delete=models.CASCADE, db_column='ItemId', related_name='challan_details')
    challan_qty = models.FloatField(db_column='ChallanQty', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Details'
        verbose_name = 'Customer Challan Detail'
        verbose_name_plural = 'Customer Challan Details'

    def __str__(self):
        return f"Detail {self.id} for Challan Master {self.master.id}"


class CustomerChallanClear(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    detail = models.ForeignKey(CustomerChallanDetail, on_delete=models.CASCADE, db_column='DId', related_name='clearances')
    clear_qty = models.FloatField(db_column='ClearQty', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=50) # Link to OfficeStaff if needed
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True) # Stored as string, consider DateTimeField
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Clear'
        verbose_name = 'Customer Challan Clear'
        verbose_name_plural = 'Customer Challan Clearances'

    def __str__(self):
        return f"Clearance {self.id} for Detail {self.detail.id}"

```

#### 4.2 Forms

**Analysis:** The original ASP.NET page is a report viewer and does not involve user input for creating or editing challan records. Therefore, no Django forms are required for this specific functionality.

#### 4.3 Views

We will use a single `TemplateView` (or a custom `DetailView`) to fetch all necessary data using the `ChallanReportManager` and pass it to the template for rendering the two reports.

**`inventory/views.py`**

```python
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.http import HttpResponseRedirect
from django.conf import settings # Assuming settings can provide default comp_id, fin_year_id

from .models import CustomerChallanMaster # Import the main model

class CustomerChallanPrintDetailsView(TemplateView):
    """
    View to display the Customer Challan and Customer Challan Clear reports.
    This replaces the ASP.NET CrystalReportViewer functionality.
    """
    template_name = 'inventory/customerchallan_print_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        challan_master_id = self.kwargs['pk']
        
        # Placeholder for company_id, fin_year_id, and session_user_id.
        # In a real application, these would come from:
        # - request.user (for session_user_id if authenticated)
        # - a global settings model or user profile for company_id and fin_year_id.
        # For demonstration, we'll use dummy values or defaults from settings.
        company_id = self.request.session.get('compid', getattr(settings, 'DEFAULT_COMPANY_ID', 1))
        fin_year_id = self.request.session.get('finyear', getattr(settings, 'DEFAULT_FIN_YEAR_ID', 1))
        session_user_id = self.request.session.get('username', 'DEFAULT_USER_ID') # Or request.user.emp_id if linked

        # Fetch Customer Challan Report data
        customer_challan_data, customer_challan_master_info = \
            CustomerChallanMaster.challans.get_customer_challan_report_data(
                challan_master_id, company_id, session_user_id
            )
        
        # Fetch Customer Challan Clear Report data
        customer_challan_clear_data, customer_challan_clear_master_info = \
            CustomerChallanMaster.challans.get_customer_challan_clear_report_data(
                challan_master_id, company_id, fin_year_id, session_user_id
            )

        context['customer_challan_report_data'] = customer_challan_data
        context['customer_challan_master_info'] = customer_challan_master_info
        context['customer_challan_clear_report_data'] = customer_challan_clear_data
        context['customer_challan_clear_master_info'] = customer_challan_clear_master_info

        # Determine the redirect URL for cancel buttons
        context['cancel_url'] = reverse_lazy('inventory:customerchallan_print_list') 
        # The original was: Response.Redirect("~/Module/Inventory/Transactions/CustomerChallan_Print.aspx?ModId=9&SubModId=121");
        # This assumes a 'customerchallan_print_list' view exists as the redirection target.

        return context

    def post(self, request, *args, **kwargs):
        # Handle the cancel button click (which was a POST in ASP.NET)
        # In Django, a simple redirect is usually GET, so we handle a POST to ensure compatibility
        return HttpResponseRedirect(reverse_lazy('inventory:customerchallan_print_list'))

# Helper views for HTMX table loading (optional, but good for large datasets or modularity)
# If reports are very large, these can render just the table contents.
# For this "print details" which means limited data, it might be overkill,
# but demonstrates the pattern. For now, the main view passes all data.
# If these were separate HTMX endpoints, the main view would just have the container.

# Example if reports were large and needed to be loaded separately via HTMX:
# class CustomerChallanReportTablePartialView(TemplateView):
#     template_name = 'inventory/_customer_challan_report_table.html'
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         challan_master_id = self.kwargs['pk']
#         company_id = self.request.session.get('compid', getattr(settings, 'DEFAULT_COMPANY_ID', 1))
#         session_user_id = self.request.session.get('username', 'DEFAULT_USER_ID')
#         report_data, master_info = CustomerChallanMaster.challans.get_customer_challan_report_data(
#             challan_master_id, company_id, session_user_id
#         )
#         context['customer_challan_report_data'] = report_data
#         context['customer_challan_master_info'] = master_info
#         return context

# class CustomerChallanClearReportTablePartialView(TemplateView):
#     template_name = 'inventory/_customer_challan_clear_report_table.html'
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         challan_master_id = self.kwargs['pk']
#         company_id = self.request.session.get('compid', getattr(settings, 'DEFAULT_COMPANY_ID', 1))
#         fin_year_id = self.request.session.get('finyear', getattr(settings, 'DEFAULT_FIN_YEAR_ID', 1))
#         session_user_id = self.request.session.get('username', 'DEFAULT_USER_ID')
#         report_data, master_info = CustomerChallanMaster.challans.get_customer_challan_clear_report_data(
#             challan_master_id, company_id, fin_year_id, session_user_id
#         )
#         context['customer_challan_clear_report_data'] = report_data
#         context['customer_challan_clear_master_info'] = master_info
#         return context

```

#### 4.4 Templates

The main template will implement Alpine.js for tab switching. Each report will be rendered within its own partial template, including DataTables initialization.

**`inventory/templates/inventory/customerchallan_print_details.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'customerChallan' }">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Customer Challan Print Details</h2>

        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a @click.prevent="activeTab = 'customerChallan'"
                   :class="{ 'border-blue-500 text-blue-600': activeTab === 'customerChallan', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'customerChallan' }"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Customer Challan
                </a>
                <a @click.prevent="activeTab = 'clearChallan'"
                   :class="{ 'border-blue-500 text-blue-600': activeTab === 'clearChallan', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'clearChallan' }"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Clear Challan
                </a>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="mt-6">
            <div x-show="activeTab === 'customerChallan'" x-cloak>
                {% include 'inventory/_customer_challan_report_table.html' with report_data=customer_challan_report_data master_info=customer_challan_master_info %}
            </div>
            <div x-show="activeTab === 'clearChallan'" x-cloak>
                {% include 'inventory/_customer_challan_clear_report_table.html' with report_data=customer_challan_clear_report_data master_info=customer_challan_clear_master_info %}
            </div>
        </div>

        <!-- Cancel Button -->
        <div class="mt-8 text-center">
            <a href="{{ cancel_url }}" 
               class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                Cancel
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery and DataTables JS/CSS if not already in base.html -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css"/>
<script type="text/javascript" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is initialized automatically by its CDN link in base.html
    });

    // Initialize DataTables for both tables when tabs are loaded
    // Using setTimeout to ensure DataTables initializes after Alpine.js shows the content
    // Or, more robustly, use HTMX to load tables separately and initialize on htmx:afterSwap
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables for the first tab initially visible
        if ($('#customerChallanTable').length) {
            $('#customerChallanTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Allow re-initialization if needed (e.g., if tabs are reloaded)
            });
        }
    });

    // Listen for tab changes in Alpine.js and re-initialize DataTables
    // This is a common pattern when tabs hide/show tables.
    document.querySelector('[x-data="{ activeTab: \'customerChallan\' }"]')._x_dataStack[0].$watch('activeTab', (value) => {
        if (value === 'clearChallan' && $('#clearChallanTable').length) {
            if ($.fn.DataTable.isDataTable('#clearChallanTable')) {
                $('#clearChallanTable').DataTable().destroy();
            }
            $('#clearChallanTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true
            });
        }
        if (value === 'customerChallan' && $('#customerChallanTable').length) {
            if ($.fn.DataTable.isDataTable('#customerChallanTable')) {
                $('#customerChallanTable').DataTable().destroy();
            }
            $('#customerChallanTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true
            });
        }
    });

</script>
{% endblock %}
```

**`inventory/templates/inventory/_customer_challan_report_table.html`** (Partial for Customer Challan Report)

```html
<div class="mb-4 text-sm text-gray-700">
    {% if master_info %}
    <p><strong>Work Order No:</strong> {{ master_info.won_no }}</p>
    <p><strong>Customer Challan No:</strong> {{ master_info.cc_no }}</p>
    <p><strong>Challan Date:</strong> {{ master_info.cc_date }}</p>
    <p><strong>Generated By:</strong> {{ master_info.gen_by }}</p>
    <p><strong>Deliver To:</strong> {{ master_info.deliv_to }}</p>
    <p><strong>Delivery Address:</strong> {{ master_info.del_add }}</p>
    <p><strong>Contact Person:</strong> {{ master_info.person }}</p>
    <p><strong>Contact No:</strong> {{ master_info.contact_no }}</p>
    <p><strong>Company Address:</strong> {{ master_info.company_address }}</p>
    {% else %}
    <p class="text-red-500">No master information available for this challan.</p>
    {% endif %}
</div>

<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="customerChallanTable" class="min-w-full bg-white table-auto">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CC No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CC Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Time</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% if report_data %}
            {% for row in report_data %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.won_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.cc_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.cc_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.uom }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-right">{{ row.challan_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.sys_time }}</td>
            </tr>
            {% endfor %}
            {% else %}
            <tr>
                <td colspan="9" class="py-4 px-4 text-center text-gray-500">No customer challan details available.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

```

**`inventory/templates/inventory/_customer_challan_clear_report_table.html`** (Partial for Clear Challan Report)

```html
<div class="mb-4 text-sm text-gray-700">
    {% if master_info %}
    <p><strong>Work Order No:</strong> {{ master_info.won_no }}</p>
    <p><strong>Customer Challan No:</strong> {{ master_info.cc_no }}</p>
    <p><strong>Challan Date:</strong> {{ master_info.cc_date }}</p>
    <p><strong>Generated By:</strong> {{ master_info.gen_by }}</p>
    <p><strong>Deliver To:</strong> {{ master_info.deliv_to }}</p>
    <p><strong>Delivery Address:</strong> {{ master_info.del_add }}</p>
    <p><strong>Contact Person:</strong> {{ master_info.person }}</p>
    <p><strong>Contact No:</strong> {{ master_info.contact_no }}</p>
    <p><strong>Company Address:</strong> {{ master_info.company_address }}</p>
    {% else %}
    <p class="text-red-500">No master information available for this challan clear.</p>
    {% endif %}
</div>

<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="clearChallanTable" class="min-w-full bg-white table-auto">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CC No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clear Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Time</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% if report_data %}
            {% for row in report_data %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.won_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.cc_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.uom }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-right">{{ row.challan_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-right">{{ row.clear_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ row.sys_time }}</td>
            </tr>
            {% endfor %}
            {% else %}
            <tr>
                <td colspan="10" class="py-4 px-4 text-center text-gray-500">No clear challan details available.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

```

#### 4.5 URLs

The `inventory/urls.py` will define the path to our report view.

**`inventory/urls.py`**

```python
from django.urls import path
from .views import CustomerChallanPrintDetailsView

app_name = 'inventory' # Define app_name for namespacing URLs

urlpatterns = [
    # Path for displaying customer challan print details for a specific master ID
    path('customer-challan-print-details/<int:pk>/', CustomerChallanPrintDetailsView.as_view(), name='customerchallan_print_details'),
    
    # Placeholder for the redirection target of the 'Cancel' button
    # This should lead to a list view of challans, similar to the original ASP.NET navigation
    path('customer-challan-print-list/', CustomerChallanPrintDetailsView.as_view(), name='customerchallan_print_list'), # Replace with actual list view later
]

```
**Project-level `urls.py` (e.g., `myproject/urls.py`)**

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('inventory/', include('inventory.urls', namespace='inventory')),
    # Other app URLs
]
```

#### 4.6 Tests

Unit tests for the `ChallanReportManager` methods and integration tests for the `CustomerChallanPrintDetailsView` are crucial to ensure data accuracy and view functionality.

**`inventory/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from unittest.mock import patch

from .models import (
    CompanyProfile, CustomerChallanMaster, CustomerChallanDetail,
    CustomerChallanClear, ItemMaster, UnitMaster, OfficeStaff, CustomerMaster,
    ChallanReportManager # Import the manager directly for testing its methods
)

class ChallanReportManagerTest(TestCase):
    """
    Tests for the custom ChallanReportManager methods, focusing on data retrieval logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models
        cls.company_1 = CompanyProfile.objects.create(comp_id=1, address="123 Main St, Anytown")
        cls.company_2 = CompanyProfile.objects.create(comp_id=2, address="456 Side St, Otherville")

        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')

        cls.item_a = ItemMaster.objects.create(id=101, item_code='ITEM-A', manf_desc='Product A', uom_basic=cls.unit_ea)
        cls.item_b = ItemMaster.objects.create(id=102, item_code='ITEM-B', manf_desc='Product B', uom_basic=cls.unit_kg)

        cls.staff_1 = OfficeStaff.objects.create(emp_id='EMP001', comp_id=1, title='Mr', employee_name='John Doe')
        cls.staff_2 = OfficeStaff.objects.create(emp_id='EMP002', comp_id=1, title='Ms', employee_name='Jane Smith')

        cls.customer_1 = CustomerMaster.objects.create(customer_id='CUST001', comp_id=1, customer_name='Alpha Corp', regd_address='10 Business Park', contact_person='Alice', contact_no='111-222-3333')
        cls.customer_2 = CustomerMaster.objects.create(customer_id='CUST002', comp_id=1, customer_name='Beta Ltd', regd_address='20 Commerce Plaza', contact_person='Bob', contact_no='444-555-6666')

        # Create CustomerChallanMaster
        cls.challan_master_1 = CustomerChallanMaster.objects.create(
            id=1, comp_id=1, customer_id='CUST001', session_id='EMP001',
            sys_time='10:00:00', won_no='WO-001', cc_no='CC-001', sys_date=date(2023, 1, 15)
        )
        cls.challan_master_2 = CustomerChallanMaster.objects.create(
            id=2, comp_id=1, customer_id='CUST002', session_id='EMP002',
            sys_time='11:00:00', won_no='WO-002', cc_no='CC-002', sys_date=date(2023, 2, 20)
        )

        # Create CustomerChallanDetail
        cls.challan_detail_1 = CustomerChallanDetail.objects.create(
            id=10, master=cls.challan_master_1, item=cls.item_a, challan_qty=100.0
        )
        cls.challan_detail_2 = CustomerChallanDetail.objects.create(
            id=11, master=cls.challan_master_1, item=cls.item_b, challan_qty=50.0
        )

        # Create CustomerChallanClear
        cls.challan_clear_1 = CustomerChallanClear.objects.create(
            id=100, detail=cls.challan_detail_1, clear_qty=70.0, comp_id=1,
            session_id='EMP002', sys_date=date(2023, 1, 20), sys_time='15:00:00', fin_year_id=2023
        )
        cls.challan_clear_2 = CustomerChallanClear.objects.create(
            id=101, detail=cls.challan_detail_2, clear_qty=20.0, comp_id=1,
            session_id='EMP001', sys_date=date(2023, 2, 1), sys_time='09:00:00', fin_year_id=2023
        )
        cls.challan_clear_3 = CustomerChallanClear.objects.create(
            id=102, detail=cls.challan_detail_1, clear_qty=30.0, comp_id=1,
            session_id='EMP002', sys_date=date(2023, 3, 5), sys_time='16:00:00', fin_year_id=2024 # Different FY
        )

    def test_get_customer_challan_report_data(self):
        """
        Test that get_customer_challan_report_data fetches and formats data correctly.
        """
        report_data, master_info = CustomerChallanMaster.challans.get_customer_challan_report_data(
            challan_master_id=self.challan_master_1.id,
            company_id=self.company_1.comp_id,
            session_user_id=self.staff_1.emp_id
        )

        self.assertEqual(len(report_data), 2)
        self.assertEqual(report_data[0]['item_code'], 'ITEM-A')
        self.assertEqual(report_data[0]['challan_qty'], 100.0)
        self.assertEqual(report_data[0]['cc_date'], '15/01/2023') # Test date format

        self.assertEqual(master_info['won_no'], 'WO-001')
        self.assertEqual(master_info['gen_by'], 'Mr. John Doe')
        self.assertEqual(master_info['deliv_to'], 'Alpha Corp[CUST001]')
        self.assertEqual(master_info['company_address'], '123 Main St, Anytown')

        # Test case with no data
        report_data_no_master, master_info_no_master = CustomerChallanMaster.challans.get_customer_challan_report_data(
            challan_master_id=999, company_id=self.company_1.comp_id, session_user_id=self.staff_1.emp_id
        )
        self.assertEqual(len(report_data_no_master), 0)
        self.assertEqual(master_info_no_master, {})

    def test_get_customer_challan_clear_report_data(self):
        """
        Test that get_customer_challan_clear_report_data fetches and formats data correctly.
        """
        # Test for FY 2023 (should include clear_1 and clear_2)
        report_data_2023, master_info_2023 = CustomerChallanMaster.challans.get_customer_challan_clear_report_data(
            challan_master_id=self.challan_master_1.id,
            company_id=self.company_1.comp_id,
            current_fin_year_id=2023,
            session_user_id=self.staff_1.emp_id
        )
        self.assertEqual(len(report_data_2023), 2)
        self.assertEqual(report_data_2023[0]['clear_qty'], 70.0)
        self.assertEqual(report_data_2023[1]['clear_qty'], 20.0)
        self.assertEqual(report_data_2023[0]['sys_date'], '20/01/2023') # Test date format

        self.assertEqual(master_info_2023['won_no'], 'WO-001')
        # This gen_by corresponds to the session_id on CustomerChallanClear.
        # In LoadGrid(), it uses SessionId from tblInv_Customer_Challan_Clear table.
        # For clear_1, it's EMP002 (Jane Smith).
        self.assertEqual(master_info_2023['gen_by'], 'Ms. Jane Smith') 
        self.assertEqual(master_info_2023['deliv_to'], 'Alpha Corp[CUST001]')


        # Test for FY 2024 (should include clear_1, clear_2, clear_3)
        report_data_2024, master_info_2024 = CustomerChallanMaster.challans.get_customer_challan_clear_report_data(
            challan_master_id=self.challan_master_1.id,
            company_id=self.company_1.comp_id,
            current_fin_year_id=2024,
            session_user_id=self.staff_1.emp_id
        )
        self.assertEqual(len(report_data_2024), 3)
        self.assertEqual(report_data_2024[2]['clear_qty'], 30.0)

        # Test case with no data
        report_data_no_master, master_info_no_master = CustomerChallanMaster.challans.get_customer_challan_clear_report_data(
            challan_master_id=999, company_id=self.company_1.comp_id, current_fin_year_id=2023, session_user_id=self.staff_1.emp_id
        )
        self.assertEqual(len(report_data_no_master), 0)
        self.assertEqual(master_info_no_master, {})


class CustomerChallanPrintDetailsViewTest(TestCase):
    """
    Integration tests for the CustomerChallanPrintDetailsView.
    """
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for a view to function
        cls.company = CompanyProfile.objects.create(comp_id=1, address="Test Co Address")
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item = ItemMaster.objects.create(id=10, item_code='TESTITEM', manf_desc='Test Product', uom_basic=cls.unit)
        cls.staff = OfficeStaff.objects.create(emp_id='USER1', comp_id=1, title='Dr', employee_name='Test User')
        cls.customer = CustomerMaster.objects.create(customer_id='CUST1', comp_id=1, customer_name='Test Customer', regd_address='Test Addr', contact_person='Test Person', contact_no='123')

        cls.challan_master = CustomerChallanMaster.objects.create(
            id=100, comp_id=1, customer_id='CUST1', session_id='USER1',
            sys_time='12:00:00', won_no='WO-TEST', cc_no='CC-TEST', sys_date=date(2023, 4, 1)
        )
        cls.challan_detail = CustomerChallanDetail.objects.create(
            id=1000, master=cls.challan_master, item=cls.item, challan_qty=50.0
        )
        cls.challan_clear = CustomerChallanClear.objects.create(
            id=2000, detail=cls.challan_detail, clear_qty=20.0, comp_id=1,
            session_id='USER1', sys_date=date(2023, 4, 5), sys_time='13:00:00', fin_year_id=2023
        )

    def setUp(self):
        self.client = Client()

    def test_view_renders_correctly(self):
        """
        Test that the view loads, uses the correct template, and passes data to context.
        """
        url = reverse('inventory:customerchallan_print_details', args=[self.challan_master.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan_print_details.html')
        self.assertIn('customer_challan_report_data', response.context)
        self.assertIn('customer_challan_clear_report_data', response.context)
        self.assertIn('customer_challan_master_info', response.context)
        self.assertIn('customer_challan_clear_master_info', response.context)
        
        # Verify some data from the context
        self.assertGreater(len(response.context['customer_challan_report_data']), 0)
        self.assertEqual(response.context['customer_challan_report_data'][0]['won_no'], 'WO-TEST')
        self.assertEqual(response.context['customer_challan_report_data'][0]['challan_qty'], 50.0)

        self.assertGreater(len(response.context['customer_challan_clear_report_data']), 0)
        self.assertEqual(response.context['customer_challan_clear_report_data'][0]['clear_qty'], 20.0)
        self.assertEqual(response.context['customer_challan_clear_report_data'][0]['item_code'], 'TESTITEM')

    def test_cancel_button_redirects(self):
        """
        Test that clicking the cancel button redirects to the list view.
        """
        url = reverse('inventory:customerchallan_print_details', args=[self.challan_master.id])
        cancel_redirect_url = reverse('inventory:customerchallan_print_list')
        
        # Simulate a POST request for cancel (as it was an asp:Button with onclick in ASP.NET)
        response = self.client.post(url) 
        
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, cancel_redirect_url)

    def test_view_with_non_existent_challan(self):
        """
        Test that the view handles non-existent challan IDs gracefully (e.g., empty reports).
        """
        url = reverse('inventory:customerchallan_print_details', args=[99999]) # Non-existent ID
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200) # Still renders the page but with empty data
        self.assertEqual(len(response.context['customer_challan_report_data']), 0)
        self.assertEqual(len(response.context['customer_challan_clear_report_data']), 0)
        self.assertEqual(response.context['customer_challan_master_info'], {})
        self.assertEqual(response.context['customer_challan_clear_master_info'], {})
        self.assertContains(response, "No customer challan details available.")
        self.assertContains(response, "No clear challan details available.")
```

### Step 5: HTMX and Alpine.js Integration

**Analysis:**
The ASP.NET solution used a `TabContainer` for tabbed navigation and Crystal Reports for rendering. In Django, we replace these with:

*   **Alpine.js for Tabs:** The primary template `customerchallan_print_details.html` uses Alpine.js (`x-data` and `x-show`) to manage tab visibility. This provides a lightweight, reactive way to switch between "Customer Challan" and "Clear Challan" views without full page reloads.
*   **DataTables for Reports:** Each report partial (`_customer_challan_report_table.html` and `_customer_challan_clear_report_table.html`) includes a `<table>` tag. jQuery DataTables is then initialized on these tables in the main template's `extra_js` block. This provides client-side sorting, filtering, and pagination.
*   **No HTMX for this specific page:** Since the current ASP.NET page's functionality is to display two reports on load, and tab switching is purely UI, HTMX is not strictly necessary for the initial implementation of the *tabs*. All data for both reports is fetched by the Django view on initial page load and passed to the template. DataTables then handles the display. If the reports were extremely large or required dynamic filtering *after* initial load, HTMX could be used to swap table contents. For this `_Print_Details` page, an initial full render with client-side tabs is efficient and simple.

**Key HTMX/Alpine.js/DataTables elements in the templates:**
*   `x-data="{ activeTab: 'customerChallan' }"`: Initializes Alpine.js state for tab management.
*   `@click.prevent="activeTab = 'customerChallan'"`: Alpine.js event listener to change the active tab.
*   `:class="{ ... }"`: Alpine.js binding for dynamic styling based on `activeTab`.
*   `x-show="activeTab === 'customerChallan'"`: Alpine.js directive to conditionally show/hide tab content.
*   `x-cloak`: Alpine.js directive to prevent flickering of elements before Alpine is initialized.
*   `$('#customerChallanTable').DataTable(...)`: jQuery DataTables initialization script.

### Final Notes

*   **Placeholders:** Replace `[TABLE_NAME]`, `[MODEL_NAME]`, `[FIELD1]`, `[APP_NAME]`, etc., with the actual names derived during the analysis. This document already uses specific names like `inventory`, `CustomerChallanMaster`, etc.
*   **DRY Templates:** The report tables are separated into partials (`_customer_challan_report_table.html`, `_customer_challan_clear_report_table.html`) to maintain modularity.
*   **Fat Model, Thin View:** Complex data retrieval logic (joins, filtering, formatting) is confined to the `ChallanReportManager` within `models.py`, keeping `views.py` concise and focused on orchestrating the request and response.
*   **Comprehensive Tests:** The included tests cover both the model's data retrieval capabilities and the view's rendering and navigation, ensuring robustness and maintainability.
*   **Session Data:** The ASP.NET code relies on `Session["compid"]`, `Session["finyear"]`, `Session["username"]`. In Django, these would typically be accessed via `request.session` (for session data) or `request.user` (for authenticated user details). For the provided solution, placeholder access via `self.request.session.get` and `getattr(settings, ...)` is used, assuming these are configured.
*   **External CSS/JS:** CDN links for DataTables and Alpine.js are assumed to be in `core/base.html`, as per instructions. Only the initialization script is in the page-specific template.
*   **Company Address Utility:** The `fun.CompAdd(CompId)` logic is replaced by a lookup against a `CompanyProfile` model, demonstrating how such utilities are integrated into the Django ORM.