## ASP.NET to Django Conversion Script: Supplier Challan Print Details

This modernization plan outlines the conversion of your ASP.NET Supplier Challan Print Details page to a modern Django-based solution. Our focus is on leveraging AI-assisted automation to systematically transform legacy components into an efficient, scalable, and maintainable Django application.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns inferred from the ASP.NET code.

**Instructions:**
The ASP.NET code primarily retrieves data for two distinct reports related to Supplier Challans. Based on the `SqlDataAdapter` calls to stored procedures (`GetChallan_Details`, `GetSup_Challan_Clear_Edit`) and direct SQL queries (`tblMM_Supplier_master`, `tblCompany_master`, `tblHR_OfficeStaff`, `tblMM_Rate_Register`), we can infer the following database schema.

**Inferred Tables and Key Columns:**

*   **`tblInventory_SupplierChallan`**: This is the main challan header table.
    *   `ID` (Primary Key, Integer)
    *   `SupplierID` (Foreign Key to `tblMM_Supplier_master`)
    *   `ChallanNo` (String)
    *   `ChallanDate` (Date)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff`, for 'Generated By' employee)
    *   `CompId` (Foreign Key to `tblCompany_master`)
    *   `FinYearId` (Integer, Financial Year ID)
    *   *(Other relevant Challan header details)*
*   **`tblInventory_SupplierChallanDetail`**: This is the challan line items table (assuming a 1-to-many relationship with `tblInventory_SupplierChallan`).
    *   `ID` (Primary Key, Integer)
    *   `ChallanID` (Foreign Key to `tblInventory_SupplierChallan`)
    *   `ItemID` (Foreign Key to an Item Master table, inferred from `row.ItemArray[19]` and `tblMM_Rate_Register`)
    *   `Quantity` (Decimal)
    *   `Amount` (Decimal)
    *   *(Other relevant Challan item details)*
*   **`tblMM_Supplier_master`**: Contains supplier information.
    *   `SupplierId` (Primary Key, String/Integer)
    *   `SupplierName` (String)
    *   `MaterialDelAddress` (String)
    *   `ContactPerson` (String)
    *   `ContactNo` (String)
*   **`tblCompany_master`**: Contains company information.
    *   `CompId` (Primary Key, Integer)
    *   `RegdAddress` (String)
    *   `RegdCity` (Integer, FK)
    *   `RegdState` (Integer, FK)
    *   `RegdCountry` (Integer, FK)
    *   `RegdPinCode` (String)
    *   `RegdContactNo` (String)
    *   `RegdFaxNo` (String)
    *   `RegdEmail` (String)
*   **`tblHR_OfficeStaff`**: Contains employee information.
    *   `EmpId` (Primary Key, String/Integer)
    *   `EmployeeName` (String)
    *   `Title` (String)
*   **`tblMM_Rate_Register`**: Stores item rates and discounts.
    *   `CompId` (Foreign Key to `tblCompany_master`)
    *   `ItemId` (Foreign Key to Item Master)
    *   `Rate` (Decimal)
    *   `Discount` (Decimal)
*   **Lookup Tables (Implicit from `fun.getCity`, `fun.getState`, `fun.getCountry`):**
    *   `tblCity`, `tblState`, `tblCountry`: Each likely has `Id` (PK) and `Name`.

**Note:** For the purpose of this conversion plan, we will focus on the main `SupplierChallan` and `SupplierChallanDetail` models, along with simplified representations of the related master data tables, as their full schema is not exhaustively provided. The complex Crystal Report logic will be re-imagined as structured HTML tables, compatible with DataTables.

### Step 2: Identify Backend Functionality

**Task:** Determine the business logic and data operations from the ASP.NET code.

**Instructions:**
The ASP.NET page is primarily a "print details" viewer for existing Supplier Challans, not a page for creating, updating, or deleting challans themselves.

*   **Read (Display):**
    *   Retrieves a specific Supplier Challan (`Id` from query string).
    *   Fetches detailed line items for the challan using `GetChallan_Details` stored procedure.
    *   Calculates a `Rate` for each item based on `tblMM_Rate_Register` (Rate - (Rate \* Discount / 100)). This is a critical business logic point.
    *   Retrieves associated supplier details (`tblMM_Supplier_master`).
    *   Retrieves company details (`tblCompany_master`) to display company address.
    *   Determines a "Print Type" (Original, Duplicate, etc.) based on `T` query string parameter.
    *   Fetches data for a "Clear Challan" report using `GetSup_Challan_Clear_Edit` stored procedure.
    *   Retrieves the employee name who generated the "Clear Challan" from `tblHR_OfficeStaff`.
    *   Retrieves supplier details again for the "Clear Challan" report.
    *   All collected data is then passed to Crystal Reports for rendering.
*   **Navigation/Redirection:**
    *   `Btncancel_Click` and `BtnCancel1_Click` simply redirect the user back to the `SupplierChallan_Print.aspx` list page. There are no actual CRUD operations performed by these buttons on the challan data itself.

**Validation Logic:** No explicit validation logic is visible in the provided code, as it's a display-only page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern Django/HTMX/Alpine.js components.

**Instructions:**
The ASP.NET page utilizes server-side controls to construct its UI.

*   **`TabContainer` with `TabPanel`s (AjaxControlToolkit):** This provides a tabbed interface ("Supplier Challan" and "Clear Challan"). In Django, this will be implemented using HTMX. Each tab will load its content dynamically via a separate HTMX `GET` request.
*   **`CrystalReportViewer`:** This is the core display component. It will be replaced by standard Django templates rendering structured HTML tables. DataTables will be applied to these tables for interactive features (search, sort, paginate).
*   **`asp:Panel`:** Used for grouping and scrollbars. In Django, simple `div` elements with Tailwind CSS classes will serve this purpose.
*   **`asp:Button` (`Btncancel`, `BtnCancel1`):** These buttons perform redirections. In Django, these will be standard HTML `<a>` tags or `<button>` elements that trigger a `window.location.href` change, or simply `<a>` tags with `href` pointing to the previous page.

**Field Mappings:** Data from database queries are mapped to Crystal Report parameters. In Django, this data will be prepared in the view (or a service layer called by the view) and passed directly to the template context.

**JavaScript:** The `OnChanged` JavaScript function seems to be part of the `AjaxControlToolkit`'s internal workings for tab changes and is not relevant for direct migration, as HTMX will handle tab content loading. No custom JavaScript is directly visible for complex client-side interactions, reinforcing the choice of HTMX + Alpine.js.

### Step 4: Generate Django Code

We will create a Django application named `inventory` to house the converted components.

#### 4.1 Models (`inventory/models.py`)

**Task:** Create Django models based on the inferred database schema. We'll include simplified representations of the core tables required for these reports. Note the `managed = False` and `db_table` for integration with an existing database.

```python
from django.db import models

class Company(models.Model):
    """Represents tblCompany_master."""
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    compname = models.CharField(db_column='CompName', max_length=255) # Assuming a name field
    regdaddress = models.CharField(db_column='RegdAddress', max_length=500, blank=True, null=True)
    regdcity = models.IntegerField(db_column='RegdCity', blank=True, null=True)
    regdstate = models.IntegerField(db_column='RegdState', blank=True, null=True)
    regdcountry = models.IntegerField(db_column='RegdCountry', blank=True, null=True)
    regdpincode = models.CharField(db_column='RegdPinCode', max_length=10, blank=True, null=True)
    regdcontactno = models.CharField(db_column='RegdContactNo', max_length=50, blank=True, null=True)
    regdfaxno = models.CharField(db_column='RegdFaxNo', max_length=50, blank=True, null=True)
    regdemail = models.CharField(db_column='RegdEmail', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.compname

    def get_full_address(self):
        # This simulates the fun.getCity, getState, getCountry logic
        # In a real app, these would be FKs to respective lookup tables.
        city_name = "CityName" # Placeholder
        state_name = "StateName" # Placeholder
        country_name = "CountryName" # Placeholder
        return (f"{self.regdaddress or ''},\n"
                f"{city_name}, {state_name}, {country_name} PIN No.-{self.regdpincode or ''}.\n"
                f"Ph No.-{self.regdcontactno or ''}, Fax No.-{self.regdfaxno or ''}\n"
                f"Email No.-{self.regdemail or ''}")


class Supplier(models.Model):
    """Represents tblMM_Supplier_master."""
    supplierid = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    suppliername = models.CharField(db_column='SupplierName', max_length=255)
    materialdeladdress = models.CharField(db_column='MaterialDelAddress', max_length=500, blank=True, null=True)
    contactperson = models.CharField(db_column='ContactPerson', max_length=100, blank=True, null=True)
    contactno = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.suppliername}[{self.supplierid}]"


class Employee(models.Model):
    """Represents tblHR_OfficeStaff."""
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employeename = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}. {self.employeename}"


class Item(models.Model):
    """Represents an Item Master table (inferred from ItemId references)."""
    itemid = models.CharField(db_column='ItemId', primary_key=True, max_length=50)
    itemname = models.CharField(db_column='ItemName', max_length=255) # Assuming item name
    # ... other item details

    class Meta:
        managed = False
        db_table = 'tblMM_Item_master' # Assuming a name for the Item Master
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.itemname


class RateRegister(models.Model):
    """Represents tblMM_Rate_Register."""
    # Assuming a composite primary key or an auto-incrementing ID for simplicity
    id = models.AutoField(db_column='ID', primary_key=True) # Assuming an ID for PK
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='rates', default=1) # Default for testing
    itemid = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='rates')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4)
    discount = models.DecimalField(db_column='Discount', max_digits=5, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'
        # unique_together = (('CompId', 'ItemId'),) # Might be unique by company and item

    def __str__(self):
        return f"Rate for {self.itemid.itemname} @ {self.rate} ({self.discount}% off)"

    @property
    def effective_rate(self):
        """Calculates Rate - (Rate * (Discount / 100)) as per ASP.NET logic."""
        if self.discount is not None:
            return self.rate * (1 - (self.discount / 100))
        return self.rate


class SupplierChallan(models.Model):
    """Represents tblInventory_SupplierChallan."""
    id = models.IntegerField(db_column='ID', primary_key=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='challans')
    challanno = models.CharField(db_column='ChallanNo', max_length=50)
    challandate = models.DateField(db_column='ChallanDate')
    sessionid = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='challans_generated', blank=True, null=True)
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='challans')
    finyearid = models.IntegerField(db_column='FinYearId')
    # Add other fields as needed based on actual table schema

    class Meta:
        managed = False
        db_table = 'tblInventory_SupplierChallan'
        verbose_name = 'Supplier Challan'
        verbose_name_plural = 'Supplier Challans'

    def __str__(self):
        return f"Challan No: {self.challanno} ({self.supplier.suppliername})"

    @classmethod
    def get_challan_details_report_data(cls, challan_id, company_id):
        """
        Simulates the 'GetChallan_Details' stored procedure and related logic for the first report.
        Returns data suitable for rendering the Supplier Challan report.
        """
        try:
            challan = cls.objects.select_related('supplier', 'compid').get(id=challan_id, compid=company_id)
            details = SupplierChallanDetail.objects.filter(challanid=challan).select_related('itemid')

            report_data = {
                'challan_header': challan,
                'items': [],
                'supplier_info': {
                    'DelivTo': challan.supplier.__str__(),
                    'DelAdd': challan.supplier.materialdeladdress,
                    'Person': challan.supplier.contactperson,
                    'ContactNo': challan.supplier.contactno,
                },
                'company_address': challan.compid.get_full_address(),
            }

            for detail in details:
                # Simulate the 'Rate' calculation from tblMM_Rate_Register
                effective_rate_obj = RateRegister.objects.filter(
                    compid=challan.compid,
                    itemid=detail.itemid
                ).order_by('-id').first() # Assuming latest rate if multiple
                
                rate_value = effective_rate_obj.effective_rate if effective_rate_obj else 0.0

                report_data['items'].append({
                    'ItemName': detail.itemid.itemname,
                    'Quantity': detail.quantity,
                    'Amount': detail.amount,
                    'Rate': rate_value,
                    # Add other item details from ChallanDetail
                })
            return report_data
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_clear_challan_report_data(cls, challan_id, company_id, fin_year_id):
        """
        Simulates the 'GetSup_Challan_Clear_Edit' stored procedure and related logic
        for the second report.
        Returns data suitable for rendering the Clear Challan report.
        """
        try:
            challan = cls.objects.select_related('supplier', 'compid', 'sessionid').get(
                id=challan_id, compid=company_id, finyearid=fin_year_id
            )
            details = SupplierChallanDetail.objects.filter(challanid=challan).select_related('itemid')

            report_data = {
                'challan_header': challan,
                'items': [], # Assuming the clear challan might also list items
                'generated_by': challan.sessionid.__str__() if challan.sessionid else 'N/A',
                'supplier_info': { # Duplicated but kept for clarity mirroring ASP.NET
                    'DelivTo': challan.supplier.__str__(),
                    'DelAdd': challan.supplier.materialdeladdress,
                    'Person': challan.supplier.contactperson,
                    'ContactNo': challan.supplier.contactno,
                },
                'company_address': challan.compid.get_full_address(),
            }

            for detail in details:
                 report_data['items'].append({
                    'ItemName': detail.itemid.itemname,
                    'Quantity': detail.quantity,
                    'Amount': detail.amount,
                    # Add other item details from ChallanDetail for clear challan
                })
            return report_data
        except cls.DoesNotExist:
            return None


class SupplierChallanDetail(models.Model):
    """Represents tblInventory_SupplierChallanDetail."""
    id = models.IntegerField(db_column='ID', primary_key=True)
    challanid = models.ForeignKey(SupplierChallan, on_delete=models.DO_NOTHING, db_column='ChallanID', related_name='details')
    itemid = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemID', related_name='challan_details')
    quantity = models.DecimalField(db_column='Quantity', max_digits=18, decimal_places=4)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=4)
    # Add other fields as needed

    class Meta:
        managed = False
        db_table = 'tblInventory_SupplierChallanDetail'
        verbose_name = 'Supplier Challan Detail'
        verbose_name_plural = 'Supplier Challan Details'

    def __str__(self):
        return f"Challan {self.challanid.challanno} - Item: {self.itemid.itemname}"

```

#### 4.2 Forms (`inventory/forms.py`)

**Task:** Since this page is for viewing reports, no forms are needed for `SupplierChallan` creation or modification. The `BtnCancel` buttons are for navigation only. We will omit a form file as it's not applicable for this particular screen's functionality.

#### 4.3 Views (`inventory/views.py`)

**Task:** Implement the report display logic using Class-Based Views (CBVs). We'll use a `DetailView` to fetch the main challan, and then specific HTMX-loadable views to render each report tab's content.

```python
from django.views.generic import TemplateView, DetailView
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.conf import settings # To access settings.COMP_ID, settings.FIN_YEAR_ID
from .models import SupplierChallan

# Assume these are available from session/user context in a real app
# For demo, using placeholders or from settings
DEFAULT_COMP_ID = getattr(settings, 'DEFAULT_COMP_ID', 1)
DEFAULT_FIN_YEAR_ID = getattr(settings, 'DEFAULT_FIN_YEAR_ID', 2024)

class SupplierChallanPrintDetailView(DetailView):
    """
    Main view to display the Supplier Challan print details page with tabs.
    Corresponds to the SupplierChallan_Print_Details.aspx page.
    """
    model = SupplierChallan
    template_name = 'inventory/supplierchallan/print_details.html'
    context_object_name = 'challan'
    pk_url_kwarg = 'pk' # Matches 'Id' from ASP.NET query string

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # PType from ASP.NET query string 'T'
        print_type_code = self.request.GET.get('T', '0') # Default to 0 (ORIGINAL)
        print_type_map = {
            '0': 'ORIGINAL',
            '1': 'DUPLICATE',
            '2': 'TRIPLICATE',
            '3': 'ACKNOWLEDGEMENT',
        }
        context['print_type'] = print_type_map.get(str(print_type_code), 'ORIGINAL')
        return context

class SupplierChallanReportPartialView(TemplateView):
    """
    HTMX-loadable view for the 'Supplier Challan' tab content.
    Corresponds to the fillGrid() logic in ASP.NET.
    """
    template_name = 'inventory/supplierchallan/_supplier_challan_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        challan_id = self.kwargs['pk']
        company_id = self.request.session.get('compid', DEFAULT_COMP_ID) # Use session compid
        
        report_data = SupplierChallan.get_challan_details_report_data(challan_id, company_id)
        if not report_data:
            return {'error_message': 'Supplier Challan details not found.'}

        # PType from ASP.NET query string 'T'
        print_type_code = self.request.GET.get('T', '0')
        print_type_map = {
            '0': 'ORIGINAL',
            '1': 'DUPLICATE',
            '2': 'TRIPLICATE',
            '3': 'ACKNOWLEDGEMENT',
        }
        report_data['PtintType'] = print_type_map.get(str(print_type_code), 'ORIGINAL')
        
        context['report_data'] = report_data
        return context

class ClearChallanReportPartialView(TemplateView):
    """
    HTMX-loadable view for the 'Clear Challan' tab content.
    Corresponds to the LoadGrid() logic in ASP.NET.
    """
    template_name = 'inventory/supplierchallan/_clear_challan_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        challan_id = self.kwargs['pk']
        company_id = self.request.session.get('compid', DEFAULT_COMP_ID) # Use session compid
        fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID) # Use session finyear
        
        report_data = SupplierChallan.get_clear_challan_report_data(challan_id, company_id, fin_year_id)
        if not report_data:
            return {'error_message': 'Clear Challan details not found.'}

        context['report_data'] = report_data
        return context

```

#### 4.4 Templates

**Task:** Create templates for the main view and the HTMX-loadable partials.

**`inventory/templates/inventory/supplierchallan/print_details.html` (Main Page)**
This is the main entry point, equivalent to the .aspx file. It sets up the tabs.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Supplier Challan Print Details - Challan No: {{ challan.challanno }}</h2>

    <!-- Tab Container -->
    <div x-data="{ activeTab: 'supplierChallan' }" class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6 py-4" aria-label="Tabs">
                <button 
                    @click="activeTab = 'supplierChallan'"
                    :class="activeTab === 'supplierChallan' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
                    hx-get="{% url 'inventory:supplierchallan_report_partial' pk=challan.pk %}?T={{ request.GET.T|default:'0' }}"
                    hx-target="#tab-content"
                    hx-trigger="click"
                    hx-indicator="#tab-spinner"
                    hx-swap="innerHTML"
                >
                    Supplier Challan ({{ print_type }})
                </button>
                <button 
                    @click="activeTab = 'clearChallan'"
                    :class="activeTab === 'clearChallan' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
                    hx-get="{% url 'inventory:clearchallan_report_partial' pk=challan.pk %}"
                    hx-target="#tab-content"
                    hx-trigger="click"
                    hx-indicator="#tab-spinner"
                    hx-swap="innerHTML"
                >
                    Clear Challan
                </button>
            </nav>
        </div>

        <div class="p-6">
            <div id="tab-content"
                 hx-get="{% url 'inventory:supplierchallan_report_partial' pk=challan.pk %}?T={{ request.GET.T|default:'0' }}"
                 hx-trigger="load"
                 hx-indicator="#tab-spinner"
                 hx-swap="innerHTML">
                <!-- Initial content loaded via HTMX -->
                <div id="tab-spinner" class="htmx-indicator text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Report...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8 text-center">
        <a href="{% url 'previous_challan_list_page' %}" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
        <button onclick="window.print()" class="ml-4 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Print Page
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('tabs', () => ({
            activeTab: 'supplierChallan',
        }));
    });

    // Initialize DataTables on content loaded by HTMX
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tab-content') {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
            });
        }
    });
</script>
{% endblock %}
```

**`inventory/templates/inventory/supplierchallan/_supplier_challan_report.html` (Partial for Supplier Challan Tab)**
This partial template displays the details of the first report.

```html
<div class="p-4">
    {% if report_data.error_message %}
        <p class="text-red-500 text-lg">{{ report_data.error_message }}</p>
    {% else %}
        <h3 class="text-xl font-semibold mb-4">Supplier Challan Report</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 text-sm">
            <div>
                <p><strong>Challan No:</strong> {{ report_data.challan_header.challanno }}</p>
                <p><strong>Challan Date:</strong> {{ report_data.challan_header.challandate|date:"d M Y" }}</p>
                <p class="whitespace-pre-line"><strong>Company Address:</strong><br>{{ report_data.company_address }}</p>
                <p><strong>Print Type:</strong> {{ report_data.PtintType }}</p>
            </div>
            <div>
                <p><strong>Deliver To:</strong> {{ report_data.supplier_info.DelivTo }}</p>
                <p><strong>Delivery Address:</strong> {{ report_data.supplier_info.DelAdd }}</p>
                <p><strong>Contact Person:</strong> {{ report_data.supplier_info.Person }}</p>
                <p><strong>Contact No:</strong> {{ report_data.supplier_info.ContactNo }}</p>
            </div>
        </div>

        <h4 class="text-lg font-medium mb-3">Challan Items</h4>
        <div class="overflow-x-auto">
            <table class="data-table min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.items %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.ItemName }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.Quantity|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.Rate|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.Amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
</div>
```

**`inventory/templates/inventory/supplierchallan/_clear_challan_report.html` (Partial for Clear Challan Tab)**
This partial template displays the details of the second report.

```html
<div class="p-4">
    {% if report_data.error_message %}
        <p class="text-red-500 text-lg">{{ report_data.error_message }}</p>
    {% else %}
        <h3 class="text-xl font-semibold mb-4">Clear Challan Report</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 text-sm">
            <div>
                <p><strong>Challan No:</strong> {{ report_data.challan_header.challanno }}</p>
                <p><strong>Challan Date:</strong> {{ report_data.challan_header.challandate|date:"d M Y" }}</p>
                <p class="whitespace-pre-line"><strong>Company Address:</strong><br>{{ report_data.company_address }}</p>
                <p><strong>Generated By:</strong> {{ report_data.generated_by }}</p>
            </div>
            <div>
                <p><strong>Deliver To:</strong> {{ report_data.supplier_info.DelivTo }}</p>
                <p><strong>Delivery Address:</strong> {{ report_data.supplier_info.DelAdd }}</p>
                <p><strong>Contact Person:</strong> {{ report_data.supplier_info.Person }}</p>
                <p><strong>Contact No:</strong> {{ report_data.supplier_info.ContactNo }}</p>
            </div>
        </div>

        <h4 class="text-lg font-medium mb-3">Challan Items (Clearance)</h4>
        <div class="overflow-x-auto">
            <table class="data-table min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity Cleared</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.items %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.ItemName }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.Quantity|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.Amount|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

**Task:** Define URL patterns for the main view and the HTMX-loadable partials.

```python
from django.urls import path
from .views import SupplierChallanPrintDetailView, SupplierChallanReportPartialView, ClearChallanReportPartialView

app_name = 'inventory'

urlpatterns = [
    # Main entry point for challan print details (maps to ASP.NET Id parameter)
    path('supplierchallan/print/<int:pk>/', SupplierChallanPrintDetailView.as_view(), name='supplierchallan_print_details'),
    
    # HTMX endpoint for Supplier Challan report tab content (T parameter from ASP.NET for Print Type)
    path('supplierchallan/report/<int:pk>/', SupplierChallanReportPartialView.as_view(), name='supplierchallan_report_partial'),
    
    # HTMX endpoint for Clear Challan report tab content
    path('supplierchallan/clear-report/<int:pk>/', ClearChallanReportPartialView.as_view(), name='clearchallan_report_partial'),

    # Placeholder for the previous challan list page, as per ASP.NET redirect
    path('supplierchallan/list/', lambda request: HttpResponse("Redirected from Challan Print Page"), name='previous_challan_list_page'),
]

```
**Note:** In your main `project/urls.py`, you would include this app's URLs:
`path('inventory/', include('inventory.urls'))`

#### 4.6 Tests (`inventory/tests.py`)

**Task:** Write comprehensive tests for the models and views, ensuring data retrieval and business logic are correctly implemented.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, Supplier, Employee, Item, RateRegister, SupplierChallan, SupplierChallanDetail
import datetime
from decimal import Decimal

# Configure settings for tests if not already done in settings.py
# (This is good practice for isolated test environments)
from django.conf import settings
if not settings.configured:
    settings.configure(
        DEFAULT_COMP_ID=1,
        DEFAULT_FIN_YEAR_ID=2024,
        SECRET_KEY='a_very_secret_key_for_tests', # Dummy secret key
        TEMPLATES=[{
            'BACKEND': 'django.template.backends.django.DjangoTemplates',
            'DIRS': ['inventory/templates'], # Add app template directory
            'APP_DIRS': True,
        }],
        INSTALLED_APPS=[
            'inventory', # Your app name
            # other necessary apps like 'django.contrib.sessions' if using session in views
            'django.contrib.sessions',
            'django.contrib.messages',
            'django.contrib.contenttypes', # For generic relations if needed, good to include
            'django.contrib.auth', # If authentication is involved
        ],
        MIDDLEWARE=[
            'django.contrib.sessions.middleware.SessionMiddleware',
            'django.contrib.auth.middleware.AuthenticationMiddleware',
            'django.contrib.messages.middleware.MessageMiddleware',
        ],
        DATABASES={
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': ':memory:',
            }
        }
    )

class ModelCreationTest(TestCase):
    """Test model creation and basic properties."""
    @classmethod
    def setUpTestData(cls):
        # Create dependent data first
        cls.company = Company.objects.create(compid=1, compname="Test Company", regdaddress="123 Test St")
        cls.supplier = Supplier.objects.create(supplierid="SUP001", suppliername="Test Supplier", materialdeladdress="456 Supplier Ave")
        cls.employee = Employee.objects.create(empid="EMP001", employeename="John Doe", title="Mr")
        cls.item1 = Item.objects.create(itemid="ITEM001", itemname="Test Item 1")
        cls.item2 = Item.objects.create(itemid="ITEM002", itemname="Test Item 2")
        
        # Create rate registers
        cls.rate1 = RateRegister.objects.create(compid=cls.company, itemid=cls.item1, rate=Decimal('100.00'), discount=Decimal('10.00'))
        cls.rate2 = RateRegister.objects.create(compid=cls.company, itemid=cls.item2, rate=Decimal('50.00'), discount=Decimal('5.00'))

        # Create main challan and details
        cls.challan = SupplierChallan.objects.create(
            id=1,
            supplier=cls.supplier,
            challanno="CH-001",
            challandate=datetime.date(2023, 1, 15),
            sessionid=cls.employee,
            compid=cls.company,
            finyearid=2024
        )
        cls.challan_detail1 = SupplierChallanDetail.objects.create(
            id=1, challanid=cls.challan, itemid=cls.item1, quantity=Decimal('5.00'), amount=Decimal('450.00')
        )
        cls.challan_detail2 = SupplierChallanDetail.objects.create(
            id=2, challanid=cls.challan, itemid=cls.item2, quantity=Decimal('10.00'), amount=Decimal('475.00')
        )

    def test_company_creation(self):
        self.assertEqual(self.company.compname, "Test Company")
        self.assertEqual(self.company.get_full_address(), "123 Test St,\nCityName, StateName, CountryName PIN No.-.\nPh No.-, Fax No.-\nEmail No.-")

    def test_supplier_creation(self):
        self.assertEqual(self.supplier.suppliername, "Test Supplier")
        self.assertEqual(str(self.supplier), "Test Supplier[SUP001]")

    def test_employee_creation(self):
        self.assertEqual(self.employee.employeename, "John Doe")
        self.assertEqual(str(self.employee), "Mr. John Doe")

    def test_item_creation(self):
        self.assertEqual(self.item1.itemname, "Test Item 1")

    def test_rate_register_creation(self):
        self.assertEqual(self.rate1.rate, Decimal('100.00'))
        self.assertEqual(self.rate1.discount, Decimal('10.00'))
        self.assertAlmostEqual(self.rate1.effective_rate, Decimal('90.00')) # 100 * (1 - 0.10)

    def test_supplier_challan_creation(self):
        self.assertEqual(self.challan.challanno, "CH-001")
        self.assertEqual(self.challan.supplier.suppliername, "Test Supplier")
        self.assertEqual(self.challan.compid.compname, "Test Company")
        self.assertEqual(self.challan.sessionid.employeename, "John Doe")

    def test_supplier_challan_detail_creation(self):
        self.assertEqual(self.challan_detail1.itemid.itemname, "Test Item 1")
        self.assertEqual(self.challan_detail1.quantity, Decimal('5.00'))

    def test_get_challan_details_report_data(self):
        report_data = SupplierChallan.get_challan_details_report_data(self.challan.id, self.company.compid)
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['challan_header'].challanno, "CH-001")
        self.assertEqual(len(report_data['items']), 2)
        
        # Verify rate calculation for item 1
        item1_data = next((item for item in report_data['items'] if item['ItemName'] == "Test Item 1"), None)
        self.assertIsNotNone(item1_data)
        self.assertAlmostEqual(item1_data['Rate'], Decimal('90.00'))

    def test_get_clear_challan_report_data(self):
        report_data = SupplierChallan.get_clear_challan_report_data(self.challan.id, self.company.compid, self.challan.finyearid)
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['generated_by'], "Mr. John Doe")
        self.assertEqual(len(report_data['items']), 2)


class SupplierChallanViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create necessary test data for views
        cls.company = Company.objects.create(compid=1, compname="Test Company", regdaddress="123 Test St")
        cls.supplier = Supplier.objects.create(supplierid="SUP001", suppliername="Test Supplier", materialdeladdress="456 Supplier Ave")
        cls.employee = Employee.objects.create(empid="EMP001", employeename="John Doe", title="Mr")
        cls.item1 = Item.objects.create(itemid="ITEM001", itemname="Test Item 1")
        cls.item2 = Item.objects.create(itemid="ITEM002", itemname="Test Item 2")
        cls.rate1 = RateRegister.objects.create(compid=cls.company, itemid=cls.item1, rate=Decimal('100.00'), discount=Decimal('10.00'))
        cls.challan = SupplierChallan.objects.create(
            id=1,
            supplier=cls.supplier,
            challanno="CH-VIEW-001",
            challandate=datetime.date(2023, 2, 1),
            sessionid=cls.employee,
            compid=cls.company,
            finyearid=2024
        )
        SupplierChallanDetail.objects.create(
            id=3, challanid=cls.challan, itemid=cls.item1, quantity=Decimal('2.00'), amount=Decimal('180.00')
        )
        
    def setUp(self):
        # Set session data for each test
        session = self.client.session
        session['compid'] = self.company.compid
        session['finyear'] = self.challan.finyearid
        session.save()

    def test_supplier_challan_print_detail_view_get(self):
        url = reverse('inventory:supplierchallan_print_details', args=[self.challan.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/print_details.html')
        self.assertContains(response, self.challan.challanno)
        self.assertContains(response, "Supplier Challan (ORIGINAL)") # Default print type

        # Test with 'T' query param
        url_with_t = reverse('inventory:supplierchallan_print_details', args=[self.challan.id]) + '?T=1'
        response_t = self.client.get(url_with_t)
        self.assertEqual(response_t.status_code, 200)
        self.assertContains(response_t, "Supplier Challan (DUPLICATE)")


    def test_supplier_challan_report_partial_view_htmx_get(self):
        url = reverse('inventory:supplierchallan_report_partial', args=[self.challan.id])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplier_challan_report.html')
        self.assertContains(response, "Challan Items")
        self.assertContains(response, self.item1.itemname)
        self.assertContains(response, "Test Company") # Company address in report

    def test_clear_challan_report_partial_view_htmx_get(self):
        url = reverse('inventory:clearchallan_report_partial', args=[self.challan.id])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_clear_challan_report.html')
        self.assertContains(response, "Clear Challan Report")
        self.assertContains(response, self.employee.employeename)
        self.assertContains(response, self.item1.itemname)

    def test_nonexistent_challan_handling(self):
        # Test main view
        url = reverse('inventory:supplierchallan_print_details', args=[99999])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # DetailView returns 404 for not found

        # Test partials
        partial_url_1 = reverse('inventory:supplierchallan_report_partial', args=[99999])
        response_partial_1 = self.client.get(partial_url_1, **{'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response_partial_1.status_code, 200) # HTMX partial still returns 200 with error message
        self.assertContains(response_partial_1, "Supplier Challan details not found.")

        partial_url_2 = reverse('inventory:clearchallan_report_partial', args=[99999])
        response_partial_2 = self.client.get(partial_url_2, **{'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response_partial_2.status_code, 200)
        self.assertContains(response_partial_2, "Clear Challan details not found.")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
As demonstrated in the templates and views:

*   **HTMX for Tab Content:** The `print_details.html` template uses `hx-get`, `hx-target`, `hx-trigger="click"`, and `hx-indicator` attributes on the tab buttons to load content from `supplierchallan_report_partial` and `clearchallan_report_partial` URLs dynamically into the `#tab-content` div.
*   **HTMX for Initial Load:** The `#tab-content` div also uses `hx-get` and `hx-trigger="load"` to automatically fetch and display the "Supplier Challan" report when the page first loads.
*   **Alpine.js for Tab State:** An Alpine.js `x-data` attribute (`activeTab: 'supplierChallan'`) on the `div` wrapping the tabs, combined with `@click` directives, manages which tab is visually active and sets the `activeTab` variable. `x-bind:class` dynamically applies CSS classes to highlight the active tab.
*   **DataTables Integration:** A simple JavaScript snippet in `extra_js` block (within `print_details.html`) listens for the `htmx:afterSwap` event on the `#tab-content` element. Once new content is loaded by HTMX, it targets any element with the class `data-table` and initializes `DataTable` on it. This ensures DataTables works correctly with dynamically loaded content. The `destroy: true` option is crucial for re-initializing DataTables when tab content changes.
*   **No Custom JavaScript:** All dynamic interactions are handled by HTMX, and UI state by Alpine.js, eliminating the need for traditional jQuery or custom JavaScript functions.
*   **Browser Print:** A simple `onclick="window.print()"` button is added for native browser printing, replacing complex Crystal Reports printing mechanisms.

### Final Notes

This comprehensive plan provides a clear pathway for migrating your ASP.NET Supplier Challan Print Details page to a modern Django architecture.

*   **Business Value:** By converting to Django with HTMX and Alpine.js, you will gain a faster, more responsive, and user-friendly application. The move away from proprietary Crystal Reports significantly reduces licensing costs and technical debt, making the system more flexible and easier to maintain and extend. The use of standard web technologies ensures broader compatibility and easier developer onboarding.
*   **Automation Focus:** The structured approach, inferring database schema, and providing complete code templates, are designed to be consumed by AI automation tools, accelerating the conversion process and minimizing manual coding effort.
*   **Scalability & Maintainability:** Django's robust ORM, combined with the fat model/thin view pattern, ensures business logic is centralized and testable. HTMX and Alpine.js provide a modern frontend experience without the complexity of a full JavaScript framework, making the application compact and performant.

This plan focuses on core functionality; further enhancements might include:
*   Implementing full lookup tables (`tblCity`, `tblState`, `tblCountry`) as Django models.
*   More sophisticated error handling and user feedback.
*   Dedicated PDF generation if complex, pixel-perfect printable reports are required beyond browser printing capabilities.
*   Integration with Django's authentication and authorization systems if user permissions are relevant for viewing challans.