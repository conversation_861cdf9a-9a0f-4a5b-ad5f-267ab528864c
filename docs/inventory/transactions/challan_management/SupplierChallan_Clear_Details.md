## ASP.NET to Django Conversion Script: Supplier Challan Details

This modernization plan outlines the conversion of your ASP.NET `SupplierChallan_Clear_Details.aspx` page and its C# code-behind to a modern Django 5.0+ application. We will leverage Django's ORM for database interactions, HTMX and Alpine.js for dynamic front-end experiences without complex JavaScript, and DataTables for robust list presentation, all while adhering to the "fat model, thin view" principle and emphasizing automation-driven migration.

### Business Benefits:

By moving to Django, your organization will achieve:
*   **Reduced Maintenance Costs**: Django's clear structure, Python's readability, and explicit design patterns make the codebase easier to understand and maintain.
*   **Improved Scalability**: Django is designed to scale from small projects to large, complex applications, offering better performance and resilience than legacy ASP.NET.
*   **Enhanced User Experience**: The adoption of HTMX, Alpine.js, and DataTables will provide a faster, more interactive, and responsive user interface, minimizing full-page reloads.
*   **Modernization and Future-Proofing**: Transitioning to a widely adopted, open-source framework like Django ensures your application benefits from ongoing community support, security updates, and access to a vast ecosystem of tools and libraries.
*   **Increased Development Agility**: Django's "batteries included" approach and Python's rapid development capabilities will allow for quicker iteration and new feature development.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

This plan breaks down the migration into actionable steps, focusing on mapping ASP.NET concepts to their Django equivalents, with an emphasis on automated tool assistance where possible.

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the ASP.NET code, we identify two primary data sources:
1.  **`SearchGridView1` data source**: Populated by the `GetSupChallan` stored procedure. The GridView columns (`FinYear`, `SCNo`, `Id`, `SupplierName`, `SupplierId`) directly map to fields from the underlying table, which we infer as `tblInv_Supplier_Challan_Master`.
2.  **`AutoCompleteExtender` service method**: Uses a `SqlDataAdapter` selecting from `tblMM_Supplier_master` for supplier autocomplete.

**Extracted Schema:**

*   **`tblInv_Supplier_Challan_Master`** (Aliased as `SupplierChallan` in Django)
    *   `Id` (PK, int) - Corresponds to `DataKeyNames="Id"` and `lblId`.
    *   `FinYear` (int) - `BoundField DataField="FinYear"`.
    *   `SCNo` (string) - `LinkButton Text='<%#Eval("SCNo") %>'`.
    *   `SupplierName` (string) - `BoundField DataField="SupplierName"`.
    *   `SupplierId` (string) - `BoundField DataField="SupplierId"`.
    *   `CompId` (int) - Inferred from `Session["compid"]` being passed to `GetSupChallan` stored procedure.

*   **`tblMM_Supplier_master`** (Aliased as `Supplier` in Django)
    *   `SupplierId` (PK, string) - Used in `fun.select` and `fun.getCode`.
    *   `SupplierName` (string) - Used in `fun.select` and `AutoCompleteExtender`.
    *   `CompId` (int) - Used as a filter in the `sql` web method.

## Step 2: Identify Backend Functionality

**Task:** Determine the core operations (Read, Search, Navigation) performed by the ASP.NET code.

**Instructions:**

*   **Read (Data Display)**: The `SearchGridView1` displays a list of supplier challan details. The `BindData` method fetches data from the `GetSupChallan` stored procedure based on `CompId`, `FinId`, and an optional `SupplierId`. This translates to a **Django List View** displaying a dataset potentially filtered by a supplier.
*   **Search**: The `TxtSearchValue` input and `Search` button trigger `Search_Click`, which uses `fun.getCode` to extract a `SupplierId` and then rebinds the data using `BindData`. This will be handled by a **Django form with HTMX** submitting to a view that reloads the DataTables data.
*   **Autocomplete**: The `AutoCompleteExtender` uses the `sql` web method to provide real-time suggestions for supplier names. This will be replaced by an **HTMX-powered Django endpoint returning partial HTML for suggestions**.
*   **Pagination**: The `SearchGridView1_PageIndexChanging` event handles server-side pagination. In Django, this will be managed by **DataTables server-side processing** interacting with a Django view providing JSON data.
*   **Navigation (Detail View)**: The `lnkbtnScNo` in the GridView, on `GoToPage` command, redirects to `SupplierChallan_Clear.aspx` with `Id` and `SupId` parameters. This indicates a **Django Detail View** for a specific supplier challan.

## Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactions to determine modern Django equivalents.

**Instructions:**

*   **`TxtSearchValue` (TextBox with `AutoCompleteExtender`)**: This will be a standard Django `forms.CharField` rendered as an `<input type="text">` with HTMX attributes (`hx-get`, `hx-trigger`, `hx-target`, `hx-swap`) for triggering the autocomplete. Alpine.js will manage the display and selection of autocomplete suggestions.
*   **`Search` (Button)**: This button will trigger an HTMX form submission to refresh the DataTables data.
*   **`SearchGridView1` (GridView)**: This will be replaced by a standard `<table>` element initialized with **DataTables** for dynamic list presentation, including server-side searching, sorting, and pagination.
*   **`lnkbtnScNo` (LinkButton within GridView)**: This will become a standard `<a>` tag in the Django template, linking to the detail view (`supplierchallan_detail`).
*   **`Panel2`, `Panel4` (Panels)**: These are layout components and will be replaced by standard HTML `<div>` elements styled with **Tailwind CSS**.
*   **`ScriptManager1`**: Django does not require a script manager. Frontend libraries (jQuery, DataTables, HTMX, Alpine.js) will be included directly in `base.html` or via CDN links.

## Step 4: Generate Django Code

The following sections provide the complete Django code for the `inventory` application, covering models, forms, views, templates, and URLs, along with comprehensive tests.

### 4.1 Models (`inventory/models.py`)

This file defines the Django models, mapping directly to your existing database tables. The `managed = False` setting indicates that Django will not create or modify these tables. We also implement model manager methods for business logic (`get_challans`, `search_by_name`, `get_code_from_display`) to keep views thin.

```python
from django.db import models
from django.urls import reverse

# Assume these are available from user session or configuration in a real application.
# For the purpose of this example, we define default values.
DEFAULT_COMP_ID = 1  # Example: Company ID from user session
DEFAULT_FIN_ID = 2023 # Example: Financial Year ID from user session

class Supplier(models.Model):
    """
    Maps to tblMM_Supplier_master for supplier information.
    Used for autocomplete functionality.
    """
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

    @classmethod
    def search_by_name(cls, prefix_text, comp_id):
        """
        Mimics the ASP.NET 'sql' web method for supplier autocomplete.
        Performs a case-insensitive search by supplier name and returns formatted strings.
        """
        results = cls.objects.filter(
            comp_id=comp_id,
            supplier_name__icontains=prefix_text # Use icontains for startsWith like behavior
        ).order_by('supplier_name')[:10] # Limit results to a reasonable number, similar to original implicit limit
        
        return [f"{s.supplier_name} [{s.supplier_id}]" for s in results]

    @classmethod
    def get_code_from_display(cls, display_text):
        """
        Mimics the ASP.NET fun.getCode() function.
        Extracts the SupplierId from a formatted string like "SupplierName [SupplierId]".
        """
        if display_text and '[' in display_text and ']' in display_text:
            try:
                # Extract the ID from the brackets
                return display_text.split('[')[-1].replace(']', '').strip()
            except IndexError:
                pass
        return None # Return None if ID cannot be extracted


class SupplierChallanManager(models.Manager):
    """
    Custom manager for SupplierChallan model to encapsulate data retrieval logic.
    Mimics the ASP.NET 'BindData' method and 'GetSupChallan' stored procedure.
    """
    def get_challans(self, comp_id, fin_id, supplier_id=None):
        """
        Retrieves supplier challans based on company, financial year, and optional supplier ID.
        """
        queryset = self.filter(
            comp_id=comp_id,
            fin_year=fin_id
        )
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)
        
        return queryset.order_by('-sc_no') # Default order by SCNo, assuming latest first


class SupplierChallan(models.Model):
    """
    Maps to tblInv_Supplier_Challan_Master for supplier challan details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # DataKeyNames="Id"
    fin_year = models.IntegerField(db_column='FinYear')
    sc_no = models.CharField(db_column='SCNo', max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # Matches Supplier.supplier_id
    comp_id = models.IntegerField(db_column='CompId')

    # Assign custom manager
    objects = SupplierChallanManager()

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Challan_Master'
        verbose_name = 'Supplier Challan'
        verbose_name_plural = 'Supplier Challans'

    def __str__(self):
        return f"{self.sc_no} - {self.supplier_name}"

    def get_absolute_url(self):
        """
        Returns the URL for the detail view of this challan.
        Mimics the ASP.NET Response.Redirect for 'GoToPage' command.
        """
        # Pass SupplierId as well, as in ASP.NET original redirect structure
        return reverse('inventory:supplierchallan_detail', kwargs={'pk': self.pk, 'supplier_id': self.supplier_id})

```

### 4.2 Forms (`inventory/forms.py`)

This file defines the Django forms used for user input and validation.

```python
from django import forms
from .models import SupplierChallan

class SupplierSearchForm(forms.Form):
    """
    Form for the supplier search input, integrated with HTMX for autocomplete.
    """
    search_supplier_display = forms.CharField(
        max_length=255,
        required=False,
        label="Supplier Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter supplier name',
            'hx-get': "{% url 'inventory:supplier_autocomplete' %}", # HTMX for autocomplete suggestions
            'hx-trigger': 'keyup changed delay:300ms', # Trigger on keyup after 300ms delay
            'hx-target': '#autocomplete-results', # Target div for suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete interfering
            'x-model': 'searchText', # Alpine.js binding for display text
            '@input': 'showSuggestions = true', # Show suggestions on input
            '@focus': 'showSuggestions = true',
            '@click.outside': 'showSuggestions = false', # Hide on click outside
        })
    )
    # This hidden field will store the actual SupplierId selected from autocomplete
    # or extracted from the display text when the form is submitted.
    supplier_id_actual = forms.CharField(
        widget=forms.HiddenInput(),
        required=False,
        initial='' # Will be populated by Alpine.js based on selection
    )

class SupplierChallanForm(forms.ModelForm):
    """
    Form for creating and updating SupplierChallan records.
    (Placeholder, as original code only shows list/search)
    """
    class Meta:
        model = SupplierChallan
        fields = ['fin_year', 'sc_no', 'supplier_name', 'supplier_id'] # Include relevant fields for CRUD
        widgets = {
            'fin_year': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sc_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

```

### 4.3 Views (`inventory/views.py`)

This file contains the Django Class-Based Views (CBVs) for handling requests. Views are kept thin, delegating business logic to models.

```python
from django.views.generic import TemplateView, DetailView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q # For complex search queries

from .models import SupplierChallan, Supplier, DEFAULT_COMP_ID, DEFAULT_FIN_ID
from .forms import SupplierSearchForm, SupplierChallanForm

class SupplierChallanListView(TemplateView):
    """
    Renders the main Supplier Challan list page.
    This view primarily sets up the page structure and the search form.
    The actual table data is loaded via HTMX and DataTables from SupplierChallanJSONDataView.
    """
    template_name = 'inventory/supplierchallan/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = SupplierSearchForm(self.request.GET or None)
        # Pass initial supplier_id from query string to Alpine.js for pre-population
        context['initial_supplier_id'] = self.request.GET.get('supid', '')
        return context

class SupplierChallanJSONDataView(View):
    """
    Provides JSON data for DataTables server-side processing.
    Handles searching, pagination, and sorting dynamically.
    """
    def get(self, request, *args, **kwargs):
        draw = int(request.GET.get('draw', 1))
        start = int(request.GET.get('start', 0))
        length = int(request.GET.get('length', 10))
        
        # DataTables search value (global search box)
        search_value = request.GET.get('search[value]', '').strip()
        
        # Custom search_supplier_display from our form for explicit search
        explicit_search_display = request.GET.get('search_supplier_display', '').strip()
        
        # Explicit supplier_id from the hidden form field (selected from autocomplete)
        explicit_supplier_id = request.GET.get('supplier_id_actual', '').strip()

        # Column ordering
        order_column_index = request.GET.get('order[0][column]')
        order_direction = request.GET.get('order[0][dir]', 'asc')
        
        # Map DataTable column index to model field names
        # Ensure this order matches the column definitions in the DataTables JS initialization
        columns = ['id', 'fin_year', 'sc_no', 'supplier_name', 'supplier_id']
        order_by_field = columns[int(order_column_index)] if order_column_index else None
        
        if order_by_field:
            if order_direction == 'desc':
                order_by_field = '-' + order_by_field
        
        # Retrieve context from session/user (mimicking ASP.NET Session variables)
        comp_id = DEFAULT_COMP_ID 
        fin_id = DEFAULT_FIN_ID   

        # Determine the primary supplier filter based on explicit selection or URL
        supplier_id_filter = None
        if explicit_supplier_id:
            # If an actual supplier ID was explicitly selected/passed, use it.
            supplier_id_filter = explicit_supplier_id
        else:
            # Otherwise, check if an initial supplier ID was in the URL (Page_Load behavior)
            supplier_id_filter = request.GET.get('supid', '').strip()
        
        # Get base queryset
        all_challans = SupplierChallan.objects.get_challans(comp_id, fin_id, supplier_id=supplier_id_filter)

        # Apply additional search filtering if the global DataTables search box is used,
        # or if the explicit search display text is provided without a selected ID.
        if search_value and not supplier_id_filter: # DataTables global search
             all_challans = all_challans.filter(
                Q(sc_no__icontains=search_value) |
                Q(supplier_name__icontains=search_value) |
                Q(supplier_id__icontains=search_value) |
                Q(fin_year__icontains=search_value)
            )
        elif explicit_search_display and not supplier_id_filter: # Our custom search field, if no ID extracted
            all_challans = all_challans.filter(
                Q(sc_no__icontains=explicit_search_display) |
                Q(supplier_name__icontains=explicit_search_display) |
                Q(supplier_id__icontains=explicit_search_display) |
                Q(fin_year__icontains=explicit_search_display)
            )

        total_records = all_challans.count()
        
        # Apply ordering
        if order_by_field:
            all_challans = all_challans.order_by(order_by_field)

        # Apply pagination
        paged_challans = all_challans[start:start + length]

        data = []
        for i, challan in enumerate(paged_challans):
            data.append([
                start + i + 1, # SN (serial number for current page)
                challan.fin_year,
                # SC No as LinkButton to detail page
                f'<a href="{challan.get_absolute_url()}" hx-boost="true">{challan.sc_no}</a>', 
                challan.supplier_name,
                challan.supplier_id,
                # Actions column with Edit/Delete buttons triggering HTMX modals
                f'''
                <button class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{reverse('inventory:supplierchallan_edit', args=[challan.pk])}"
                    hx-target="#modalContent" hx-trigger="click" _="on click add .is-active to #modal">
                    Edit
                </button>
                <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{reverse('inventory:supplierchallan_delete', args=[challan.pk])}"
                    hx-target="#modalContent" hx-trigger="click" _="on click add .is-active to #modal">
                    Delete
                </button>
                '''
            ])

        response = {
            "draw": draw,
            "recordsTotal": total_records,
            "recordsFiltered": total_records, # For simple search, filtered is same as total after queryset applied
            "data": data,
        }
        return JsonResponse(response)


class SupplierAutoCompleteView(View):
    """
    Handles autocomplete suggestions for supplier names.
    Mimics the ASP.NET 'sql' WebMethod. Returns an HTML partial via HTMX.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('query', '')
        comp_id = DEFAULT_COMP_ID 
        
        suggestions = []
        if query:
            suggestions = Supplier.search_by_name(query, comp_id)
        
        context = {'suggestions': suggestions, 'query': query}
        return render(request, 'inventory/supplierchallan/_supplier_autocomplete_results.html', context)


class SupplierChallanDetailView(DetailView):
    """
    Displays details of a single Supplier Challan.
    This corresponds to the target page of the ASP.NET redirect ('SupplierChallan_Clear.aspx').
    """
    model = SupplierChallan
    template_name = 'inventory/supplierchallan/detail.html'
    context_object_name = 'challan'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass supid from original redirect if needed for context in detail page
        context['supplier_id_from_url'] = self.kwargs.get('supplier_id') # From URL capture
        return context

# --- Placeholder CRUD Views (as per template structure, not directly from source ASP.NET) ---

class SupplierChallanCreateView(CreateView):
    """
    Handles creation of new Supplier Challan records via a modal form.
    """
    model = SupplierChallan
    form_class = SupplierChallanForm
    template_name = 'inventory/supplierchallan/form.html'
    success_url = reverse_lazy('inventory:supplierchallan_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Set default values for comp_id and fin_year if not set by form
        if not form.instance.comp_id:
            form.instance.comp_id = DEFAULT_COMP_ID 
        if not form.instance.fin_year:
            form.instance.fin_year = DEFAULT_FIN_ID 
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier Challan added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger client-side refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierChallanList' # Custom HTMX event to refresh table
                }
            )
        return response

class SupplierChallanUpdateView(UpdateView):
    """
    Handles updating existing Supplier Challan records via a modal form.
    """
    model = SupplierChallan
    form_class = SupplierChallanForm
    template_name = 'inventory/supplierchallan/form.html'
    success_url = reverse_lazy('inventory:supplierchallan_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier Challan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierChallanList'
                }
            )
        return response

class SupplierChallanDeleteView(DeleteView):
    """
    Handles deletion of Supplier Challan records via a modal confirmation.
    """
    model = SupplierChallan
    template_name = 'inventory/supplierchallan/confirm_delete.html'
    success_url = reverse_lazy('inventory:supplierchallan_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier Challan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierChallanList'
                }
            )
        return response

```

### 4.4 Templates (`inventory/templates/inventory/supplierchallan/`)

These templates handle the rendering of your application's UI, utilizing HTMX, Alpine.js, and DataTables for dynamic interactions.

#### `list.html`

This is the main page template, responsible for the overall layout, the search form, and the container for the DataTables-driven list.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" 
     x-data="{ 
         showSuggestions: false, 
         searchText: '{{ search_form.search_supplier_display.value|default:"" }}', 
         selectedSupplierId: '{{ initial_supplier_id }}' 
     }">
    
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Supplier Challans</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'inventory:supplierchallan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Supplier Challan
        </button>
    </div>
    
    <div class="mb-4">
        <form id="searchForm" hx-trigger="submit" hx-swap="none">
            {{ search_form.search_supplier_display.label_tag }}
            {{ search_form.search_supplier_display }}
            <!-- Hidden field to store the actual supplier ID for submission -->
            <input type="hidden" name="supplier_id_actual" x-model="selectedSupplierId">

            <div id="autocomplete-results-container" class="relative">
                <div id="autocomplete-results" 
                     x-show="showSuggestions && searchText.length > 0" 
                     class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">
                    <!-- HTMX will load autocomplete suggestions here via hx-target #autocomplete-results -->
                </div>
            </div>
            
            <button type="submit" class="mt-4 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Search</button>
        </form>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table id="supplierChallanTable" class="min-w-full bg-white">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SC No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- DataTables will populate this tbody dynamically via AJAX -->
            </tbody>
        </table>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery and DataTables CDN links in base.html if not already there -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script>
    $(document).ready(function() {
        const supplierChallanTable = $('#supplierChallanTable').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "{% url 'inventory:supplierchallan_json_data' %}",
                "type": "GET",
                "data": function (d) {
                    // Pass current search form values to the server for filtering
                    d.search_supplier_display = $('#id_search_supplier_display').val();
                    d.supplier_id_actual = $('#id_supplier_id_actual').val();
                    // Pass initial supid from URL if present (mimics ASP.NET Page_Load)
                    const urlParams = new URLSearchParams(window.location.search);
                    if (urlParams.has('supid')) {
                        d.supid = urlParams.get('supid');
                    }
                }
            },
            "columns": [
                {"data": null, "orderable": false}, // SN (serial number generated on server)
                {"data": 1}, // Fin Yrs
                {"data": 2}, // SC No (HTML link)
                {"data": 3}, // Supplier Name
                {"data": 4}, // Code
                {"data": 5, "orderable": false}, // Actions (HTML buttons)
            ],
            "order": [[2, 'desc']], // Default order by SC No (column index 2) descending
            "pageLength": 17, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "language": {
                "emptyTable": "No data to display !",
                "loadingRecords": "Loading...",
                "processing": "<div class='inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500'></div><p class='mt-2'>Loading...</p>"
            }
        });

        // Event listener for HTMX to reload the DataTables
        document.body.addEventListener('refreshSupplierChallanList', function() {
            supplierChallanTable.ajax.reload(null, false); // Reload without resetting pagination
            $('#modal').removeClass('is-active').addClass('hidden'); // Close modal
        });

        // Trigger DataTables reload when the search form is submitted
        $('#searchForm').on('submit', function(e) {
            e.preventDefault(); // Prevent default form submission
            supplierChallanTable.ajax.reload(); // Reload DataTables data
        });
    });
</script>
<style>
    /* Basic styling for autocomplete suggestions */
    #autocomplete-results ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    #autocomplete-results li {
        padding: 8px 12px;
        cursor: pointer;
    }
    #autocomplete-results li:hover {
        background-color: #f0f0f0;
    }
</style>
{% endblock %}

```

#### `_supplier_autocomplete_results.html`

This partial template is rendered by the `SupplierAutoCompleteView` and injected into the DOM via HTMX to display autocomplete suggestions.

```html
<!-- This template is loaded into #autocomplete-results by HTMX -->
<ul>
    {% for suggestion in suggestions %}
        <li class="p-2 hover:bg-gray-100 cursor-pointer" 
            x-on:click="
                searchText = '{{ suggestion }}'; 
                selectedSupplierId = '{{ suggestion.split('[')[1]|cut:"]"|cut:" "|safe }}'; 
                showSuggestions = false; 
                document.getElementById('searchForm').requestSubmit(); // Trigger form submission
            ">
            {{ suggestion }}
        </li>
    {% empty %}
        {% if query %}
            <li class="p-3 text-gray-500">No suggestions found.</li>
        {% endif %}
    {% endfor %}
</ul>

```

#### `detail.html`

A placeholder template for the supplier challan detail view, which is the target of the "SC No" link.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-4">Supplier Challan Details: {{ challan.sc_no }}</h2>
    <div class="bg-white shadow-md rounded-lg p-6">
        <p class="mb-2"><strong>ID:</strong> {{ challan.id }}</p>
        <p class="mb-2"><strong>Financial Year:</strong> {{ challan.fin_year }}</p>
        <p class="mb-2"><strong>Supplier Name:</strong> {{ challan.supplier_name }}</p>
        <p class="mb-2"><strong>Supplier Code:</strong> {{ challan.supplier_id }}</p>
        <p class="mb-2"><strong>Company ID:</strong> {{ challan.comp_id }}</p>
        {% if supplier_id_from_url %}
        <p class="mb-2 text-sm text-gray-600">Initial Supplier ID from URL: {{ supplier_id_from_url }}</p>
        {% endif %}
        <a href="{% url 'inventory:supplierchallan_list' %}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Back to List</a>
    </div>
</div>
{% endblock %}

```

#### `form.html` (for Add/Edit)

A partial template for displaying the add/edit form within the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Supplier Challan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `confirm_delete.html` (for Delete)

A partial template for displaying the delete confirmation within the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="mb-4">Are you sure you want to delete Supplier Challan "{{ object.sc_no }} - {{ object.supplier_name }}"?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`inventory/urls.py`)

This file defines the URL patterns for your Django views.

```python
from django.urls import path
from .views import (
    SupplierChallanListView, 
    SupplierChallanJSONDataView, 
    SupplierAutoCompleteView,
    SupplierChallanDetailView,
    SupplierChallanCreateView, 
    SupplierChallanUpdateView, 
    SupplierChallanDeleteView,
)

app_name = 'inventory' # Define app_name for namespacing URLs

urlpatterns = [
    # Main list page for Supplier Challans
    path('supplierchallan/', SupplierChallanListView.as_view(), name='supplierchallan_list'),
    
    # Endpoint for DataTables server-side processing (JSON data)
    path('supplierchallan/data/', SupplierChallanJSONDataView.as_view(), name='supplierchallan_json_data'),
    
    # Endpoint for supplier autocomplete suggestions (HTML partial)
    path('supplierchallan/autocomplete/', SupplierAutoCompleteView.as_view(), name='supplier_autocomplete'),
    
    # Detail view for a specific Supplier Challan (mimics ASP.NET redirect target)
    # The supplier_id is included as a path parameter to reflect original ASP.NET behavior.
    path('supplierchallan/<int:pk>/<str:supplier_id>/', SupplierChallanDetailView.as_view(), name='supplierchallan_detail'),

    # Placeholder CRUD operations (exposed via HTMX modals)
    path('supplierchallan/add/', SupplierChallanCreateView.as_view(), name='supplierchallan_add'),
    path('supplierchallan/edit/<int:pk>/', SupplierChallanUpdateView.as_view(), name='supplierchallan_edit'),
    path('supplierchallan/delete/<int:pk>/', SupplierChallanDeleteView.as_view(), name='supplierchallan_delete'),
]

```

### 4.6 Tests (`inventory/tests.py`)

Comprehensive unit and integration tests ensure the migrated functionality works as expected, aiming for high test coverage.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.http import JsonResponse
from unittest.mock import patch, MagicMock

from .models import Supplier, SupplierChallan, DEFAULT_COMP_ID, DEFAULT_FIN_ID
from .forms import SupplierSearchForm, SupplierChallanForm

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for Supplier model
        Supplier.objects.create(supplier_id='SUP001', supplier_name='Alpha Supplier', comp_id=DEFAULT_COMP_ID)
        Supplier.objects.create(supplier_id='SUP002', supplier_name='Beta Industries', comp_id=DEFAULT_COMP_ID)
        Supplier.objects.create(supplier_id='SUP003', supplier_name='Gamma Corp', comp_id=DEFAULT_COMP_ID + 1) # Different comp_id

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(supplier.supplier_name, 'Alpha Supplier')
        self.assertEqual(supplier.comp_id, DEFAULT_COMP_ID)

    def test_str_method(self):
        supplier = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(str(supplier), 'Alpha Supplier')

    def test_search_by_name(self):
        # Test basic search
        results = Supplier.search_by_name('alpha', DEFAULT_COMP_ID)
        self.assertIn('Alpha Supplier [SUP001]', results)
        self.assertNotIn('Beta Industries [SUP002]', results)

        # Test case-insensitivity
        results = Supplier.search_by_name('ALPHA', DEFAULT_COMP_ID)
        self.assertIn('Alpha Supplier [SUP001]', results)

        # Test with no results
        results = Supplier.search_by_name('xyz', DEFAULT_COMP_ID)
        self.assertEqual(results, [])

        # Test with different comp_id
        results = Supplier.search_by_name('Gamma', DEFAULT_COMP_ID)
        self.assertEqual(results, [])
        results = Supplier.search_by_name('Gamma', DEFAULT_COMP_ID + 1)
        self.assertIn('Gamma Corp [SUP003]', results)

    def test_get_code_from_display(self):
        self.assertEqual(Supplier.get_code_from_display('Alpha Supplier [SUP001]'), 'SUP001')
        self.assertIsNone(Supplier.get_code_from_display('Just a name'))
        self.assertIsNone(Supplier.get_code_from_display(None))
        self.assertIsNone(Supplier.get_code_from_display(''))

class SupplierChallanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for SupplierChallan model
        SupplierChallan.objects.create(
            id=1, fin_year=DEFAULT_FIN_ID, sc_no='SC001', 
            supplier_name='Alpha Supplier', supplier_id='SUP001', comp_id=DEFAULT_COMP_ID
        )
        SupplierChallan.objects.create(
            id=2, fin_year=DEFAULT_FIN_ID, sc_no='SC002', 
            supplier_name='Beta Industries', supplier_id='SUP002', comp_id=DEFAULT_COMP_ID
        )
        SupplierChallan.objects.create(
            id=3, fin_year=DEFAULT_FIN_ID + 1, sc_no='SC003', 
            supplier_name='Alpha Supplier', supplier_id='SUP001', comp_id=DEFAULT_COMP_ID
        )
        SupplierChallan.objects.create(
            id=4, fin_year=DEFAULT_FIN_ID, sc_no='SC004', 
            supplier_name='Delta Corp', supplier_id='SUP004', comp_id=DEFAULT_COMP_ID + 1 # Different comp_id
        )

    def test_supplierchallan_creation(self):
        challan = SupplierChallan.objects.get(id=1)
        self.assertEqual(challan.sc_no, 'SC001')
        self.assertEqual(challan.supplier_name, 'Alpha Supplier')

    def test_str_method(self):
        challan = SupplierChallan.objects.get(id=1)
        self.assertEqual(str(challan), 'SC001 - Alpha Supplier')

    def test_get_absolute_url(self):
        challan = SupplierChallan.objects.get(id=1)
        expected_url = reverse('inventory:supplierchallan_detail', kwargs={'pk': challan.id, 'supplier_id': challan.supplier_id})
        self.assertEqual(challan.get_absolute_url(), expected_url)

    def test_get_challans_no_filter(self):
        challans = SupplierChallan.objects.get_challans(DEFAULT_COMP_ID, DEFAULT_FIN_ID)
        self.assertEqual(challans.count(), 2) # SC001, SC002
        self.assertIn(SupplierChallan.objects.get(id=1), challans)
        self.assertIn(SupplierChallan.objects.get(id=2), challans)

    def test_get_challans_with_supplier_id_filter(self):
        challans = SupplierChallan.objects.get_challans(DEFAULT_COMP_ID, DEFAULT_FIN_ID, supplier_id='SUP001')
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().sc_no, 'SC001')

    def test_get_challans_different_fin_year(self):
        challans = SupplierChallan.objects.get_challans(DEFAULT_COMP_ID, DEFAULT_FIN_ID + 1)
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().sc_no, 'SC003')

    def test_get_challans_different_comp_id(self):
        challans = SupplierChallan.objects.get_challans(DEFAULT_COMP_ID + 1, DEFAULT_FIN_ID)
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().sc_no, 'SC004')

class SupplierChallanViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create test data for views
        self.supplier1 = Supplier.objects.create(supplier_id='SUP001', supplier_name='Alpha Supplier', comp_id=DEFAULT_COMP_ID)
        self.supplier2 = Supplier.objects.create(supplier_id='SUP002', supplier_name='Beta Industries', comp_id=DEFAULT_COMP_ID)
        self.challan1 = SupplierChallan.objects.create(
            id=101, fin_year=DEFAULT_FIN_ID, sc_no='CH001', 
            supplier_name='Alpha Supplier', supplier_id='SUP001', comp_id=DEFAULT_COMP_ID
        )
        self.challan2 = SupplierChallan.objects.create(
            id=102, fin_year=DEFAULT_FIN_ID, sc_no='CH002', 
            supplier_name='Beta Industries', supplier_id='SUP002', comp_id=DEFAULT_COMP_ID
        )
        self.challan3 = SupplierChallan.objects.create(
            id=103, fin_year=DEFAULT_FIN_ID, sc_no='CH003', 
            supplier_name='Alpha Supplier', supplier_id='SUP001', comp_id=DEFAULT_COMP_ID
        )
        self.challan_other_finyear = SupplierChallan.objects.create(
            id=104, fin_year=DEFAULT_FIN_ID + 1, sc_no='CH004', 
            supplier_name='Other Supplier', supplier_id='SUP005', comp_id=DEFAULT_COMP_ID
        )

    def test_supplierchallan_list_view_get(self):
        response = self.client.get(reverse('inventory:supplierchallan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/list.html')
        self.assertIsInstance(response.context['search_form'], SupplierSearchForm)

    def test_supplierchallan_list_view_initial_supid_filter(self):
        response = self.client.get(reverse('inventory:supplierchallan_list') + f'?supid={self.supplier1.supplier_id}')
        self.assertEqual(response.status_code, 200)
        # Verify initial_supplier_id is passed to context
        self.assertEqual(response.context['initial_supplier_id'], self.supplier1.supplier_id)


    def test_supplierchallan_json_data_view(self):
        url = reverse('inventory:supplierchallan_json_data')
        
        # Test basic data retrieval
        response = self.client.get(url, {'draw': 1, 'start': 0, 'length': 10})
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, JsonResponse)
        data = response.json()
        self.assertEqual(data['draw'], 1)
        self.assertEqual(data['recordsTotal'], 3) # CH001, CH002, CH003 for default comp/fin year
        self.assertEqual(data['recordsFiltered'], 3)
        self.assertEqual(len(data['data']), 3)

        # Test search by supplier_id_actual
        response = self.client.get(url, {
            'draw': 2, 'start': 0, 'length': 10, 
            'search_supplier_display': f'Alpha Supplier [{self.supplier1.supplier_id}]', 
            'supplier_id_actual': self.supplier1.supplier_id
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['recordsTotal'], 2) # CH001, CH003
        self.assertEqual(len(data['data']), 2)
        self.assertIn(self.challan1.sc_no, data['data'][0][2]) # Check SCNo in returned data
        self.assertIn(self.challan3.sc_no, data['data'][1][2])

        # Test global search (DataTable's 'search[value]')
        response = self.client.get(url, {
            'draw': 3, 'start': 0, 'length': 10, 
            'search[value]': 'Beta'
        })
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['recordsTotal'], 1) # CH002
        self.assertEqual(len(data['data']), 1)
        self.assertIn(self.challan2.sc_no, data['data'][0][2])

        # Test pagination
        response = self.client.get(url, {'draw': 4, 'start': 0, 'length': 1})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['recordsTotal'], 3)
        self.assertEqual(len(data['data']), 1)

        response = self.client.get(url, {'draw': 5, 'start': 1, 'length': 1})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['recordsTotal'], 3)
        self.assertEqual(len(data['data']), 1)

        # Test ordering
        # order[0][column] = 2 (SC No), order[0][dir] = 'asc'
        response = self.client.get(url, {'draw': 6, 'start': 0, 'length': 10, 'order[0][column]': '2', 'order[0][dir]': 'asc'})
        data = response.json()
        self.assertEqual(data['data'][0][1], self.challan1.fin_year) # First item is CH001 (based on asc SCNo)

        # Test with supid from URL
        response = self.client.get(url + f'?supid={self.supplier2.supplier_id}', {'draw': 7, 'start': 0, 'length': 10})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['recordsTotal'], 1) # Only CH002
        self.assertIn(self.challan2.sc_no, data['data'][0][2])

    def test_supplier_autocomplete_view(self):
        url = reverse('inventory:supplier_autocomplete')
        
        # Test with query
        response = self.client.get(url, {'query': 'alp'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplier_autocomplete_results.html')
        self.assertContains(response, 'Alpha Supplier [SUP001]')
        self.assertNotContains(response, 'Beta Industries [SUP002]')

        # Test with no query
        response = self.client.get(url, {'query': ''})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions found.', count=0) # Should be empty results but no "no suggestions found" message for empty query

    def test_supplierchallan_detail_view(self):
        url = reverse('inventory:supplierchallan_detail', args=[self.challan1.pk, self.challan1.supplier_id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/detail.html')
        self.assertContains(response, self.challan1.sc_no)
        self.assertContains(response, self.challan1.supplier_name)
        self.assertEqual(response.context['challan'], self.challan1)
        self.assertEqual(response.context['supplier_id_from_url'], self.challan1.supplier_id)

    # --- CRUD Views Tests (Placeholders) ---
    def test_supplierchallan_create_view_get(self):
        response = self.client.get(reverse('inventory:supplierchallan_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/form.html')
        self.assertIsInstance(response.context['form'], SupplierChallanForm)

    def test_supplierchallan_create_view_post_htmx(self):
        url = reverse('inventory:supplierchallan_add')
        data = {
            'fin_year': 2024,
            'sc_no': 'CH005',
            'supplier_name': 'New Supplier',
            'supplier_id': 'SUP005',
            # comp_id and fin_year are auto-set by form_valid if not provided
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierChallanList')
        self.assertTrue(SupplierChallan.objects.filter(sc_no='CH005').exists())

    def test_supplierchallan_update_view_get(self):
        url = reverse('inventory:supplierchallan_edit', args=[self.challan1.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/form.html')
        self.assertIsInstance(response.context['form'], SupplierChallanForm)
        self.assertEqual(response.context['form'].instance, self.challan1)

    def test_supplierchallan_update_view_post_htmx(self):
        url = reverse('inventory:supplierchallan_edit', args=[self.challan1.pk])
        data = {
            'fin_year': self.challan1.fin_year,
            'sc_no': 'CH001_updated',
            'supplier_name': self.challan1.supplier_name,
            'supplier_id': self.challan1.supplier_id,
            'comp_id': self.challan1.comp_id
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierChallanList')
        self.challan1.refresh_from_db()
        self.assertEqual(self.challan1.sc_no, 'CH001_updated')

    def test_supplierchallan_delete_view_get(self):
        url = reverse('inventory:supplierchallan_delete', args=[self.challan1.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/confirm_delete.html')
        self.assertEqual(response.context['object'], self.challan1)

    def test_supplierchallan_delete_view_post_htmx(self):
        challan_to_delete_id = self.challan2.pk
        url = reverse('inventory:supplierchallan_delete', args=[challan_to_delete_id])
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierChallanList')
        self.assertFalse(SupplierChallan.objects.filter(pk=challan_to_delete_id).exists())

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Autocomplete**: The `search_supplier_display` input uses `hx-get` to `{% url 'inventory:supplier_autocomplete' %}`. The `hx-target` is `#autocomplete-results`, where the `_supplier_autocomplete_results.html` partial containing `<li>` elements is injected.
*   **Alpine.js for Autocomplete Selection**: Alpine.js (`x-data` on the main container) manages `searchText` (what's visible in the input), `selectedSupplierId` (the actual ID to send for search), and `showSuggestions` (to control visibility of the dropdown). When an `<li>` is clicked, `x-on:click` updates `searchText` and `selectedSupplierId`, then triggers the form submission to refresh the DataTables.
*   **HTMX for Form Submission/Table Refresh**: The `searchForm` uses `hx-trigger="submit"` to intercept form submission. When the form submits, the DataTables `ajax.reload()` function is called, which in turn hits `supplierchallan_json_data` with the latest form values.
*   **DataTables Server-Side Processing**: The `supplierChallanTable` is initialized with `"serverSide": true` and `"ajax"` pointing to `{% url 'inventory:supplierchallan_json_data' %}`. The `data` function in `ajax` ensures that form input values are sent to the server for filtering.
*   **HTMX for CRUD Modals**: The "Add", "Edit", and "Delete" buttons use `hx-get` to load forms/confirmations into a modal (`#modalContent`). The `hx-target` and `hx-trigger` attributes control this behavior, and Alpine.js `_="on click add .is-active to #modal"` manages the modal's display.
*   **HTMX Response Triggers**: After a successful form submission (create/update/delete) from a modal, the views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshSupplierChallanList'})`. This custom HTMX event is listened for by `document.body.addEventListener('refreshSupplierChallanList', ...)` to trigger `$('#supplierChallanTable').DataTable().ajax.reload(null, false);` ensuring the list is updated without a full page refresh.

## Final Notes

This comprehensive plan provides a clear, actionable path for migrating your ASP.NET `SupplierChallan_Clear_Details` functionality to a modern Django application. It prioritizes AI-assisted automation by focusing on structured output, clear mappings, and leveraging Django's robust ecosystem to minimize manual code rewriting. The emphasis on "fat models, thin views," HTMX, Alpine.js, and DataTables ensures a clean, maintainable, and highly interactive user experience.

Remember to:
*   Ensure your Django project settings are configured to connect to your existing SQL Server database (e.g., using `django-pyodbc-azure` or `django-mssql-backend`).
*   Define `DEFAULT_COMP_ID` and `DEFAULT_FIN_ID` based on your application's logic (e.g., from logged-in user profile, site settings, or middleware).
*   Thoroughly test all components to ensure full functional parity and performance.
*   Integrate this `inventory` app into your main Django project's `urls.py`.