This comprehensive Django modernization plan outlines the strategy to transition your legacy ASP.NET application, specifically the "Customer Challan Print" module, to a modern Django-based solution. Our approach emphasizes AI-assisted automation, clean architecture, and modern web technologies to ensure efficiency, scalability, and maintainability.

## ASP.NET to Django Conversion Script: Customer Challan Print

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Benefit:** Understanding the underlying data structure is crucial for accurate data migration and efficient database interactions in the new system. This step ensures that no data is lost or misinterpreted during the transition.

**Instructions:**
From the ASP.NET code, we identify the following:

-   **Main Data Source:** The `SearchGridView1` is populated by a `SqlDataAdapter` executing the `GetCustChallan` stored procedure. This procedure takes `@CustId`, `@CompId`, and `@FinId` as parameters, indicating filtering by customer, company, and financial year. The GridView binds to `Id`, `FinYear`, `CCNo`, `CustomerName`, and `CustomerId`. We infer a main table holding challan headers.
-   **Autocomplete Source:** The `sql` web method queries `SD_Cust_master` for `CustomerId` and `CustomerName` based on `CompId`.

**Inferred Tables and Columns:**

1.  **`SD_Cust_master`**: This table holds customer information.
    *   `CustomerId` (PK, likely `VARCHAR` or `INT`)
    *   `CustomerName` (`VARCHAR`)
    *   `CompId` (`INT`, represents Company ID)

2.  **`T_CustomerChallan_Header` (Inferred Name)**: This table holds the main challan header details.
    *   `Id` (PK, `INT`)
    *   `FinYear` (`VARCHAR` or `INT`)
    *   `CCNo` (`VARCHAR`, Challan Number)
    *   `CustomerId` (`VARCHAR` or `INT`, FK to `SD_Cust_master.CustomerId`)
    *   `CompId` (`INT`, represents Company ID)
    *   `FinId` (`VARCHAR` or `INT`, represents Financial Year ID)

### Step 2: Identify Backend Functionality

**Business Benefit:** Clearly defining existing functionalities helps ensure that all critical business processes are replicated accurately in the new Django application, minimizing disruption and maintaining operational continuity.

**Instructions:**
We've identified the following key functionalities:

-   **Data Display (Read):** The primary purpose is to display a list of customer challans.
-   **Customer Search/Filtering:** Users can search for challans by `CustomerName` using a text box (`TxtSearchValue`). The search is triggered by a button (`Search_Click`).
-   **Customer Autocomplete:** The `TxtSearchValue` has an `AutoCompleteExtender` that fetches customer names and IDs from `SD_Cust_master`.
-   **Pagination:** The `SearchGridView1` supports pagination (`AllowPaging`, `onpageindexchanging`).
-   **Detail Navigation:** Clicking on a `CCNo` (Challan Number) redirects to a separate detail page (`CustomerChallan_Print_Details.aspx`), passing the `Id` (Challan ID) and other parameters.

### Step 3: Infer UI Components

**Business Benefit:** Mapping legacy UI elements to modern Django components ensures a consistent user experience while leveraging advanced features like dynamic content loading and improved responsiveness, enhancing user satisfaction.

**Instructions:**
We've mapped the ASP.NET UI controls to their modern Django/HTMX/Alpine.js equivalents:

-   **Customer Name Search Box (`TxtSearchValue`):** Will be a standard HTML `<input type="text">` element. Its autocomplete functionality will be implemented using HTMX for live search requests to a Django view, and Alpine.js for managing the autocomplete dropdown's visibility and selection.
-   **Search Button (`Search`):** Will be a standard HTML `<button>` element. Its click event will trigger an HTMX request to refresh the challan list table based on the search criteria.
-   **Challan List Grid (`SearchGridView1`):** Will be replaced by a DataTables-enhanced HTML `<table>`. This table will be loaded dynamically via HTMX, allowing for efficient client-side sorting, filtering, and pagination without full page reloads.
-   **Challan Number Link (`LinkButton` for `CCNo`):** Will be a standard HTML `<a href="...">` link that navigates to the challan detail page, mimicking the original `Response.Redirect` behavior.

---

## Step 4: Generate Django Code

**Business Benefit:** This is the core of the modernization. By automatically generating structured, best-practice Django code, we drastically reduce manual coding effort, minimize errors, and accelerate the development timeline, delivering a robust and maintainable system faster.

### Application Name: `inventory`

We will create a new Django application named `inventory` to house the customer challan related models, views, forms, templates, and URLs.

### 4.1 Models

**File: `inventory/models.py`**

**Business Benefit:** Django models provide a clear, Pythonic representation of your database tables, making data interaction intuitive and reducing the likelihood of SQL injection vulnerabilities. By mapping to existing tables, we ensure data integrity during the transition.

```python
from django.db import models
from django.db.models import F, Q, Value
from django.db.models.functions import Concat

class CustomerManager(models.Manager):
    """
    Custom manager for Customer model to encapsulate business logic
    related to fetching customer data, similar to fun.select and sql web method.
    """
    def get_customer_choices(self, prefix_text, company_id):
        """
        Mimics the ASP.NET 'sql' web method for customer autocomplete.
        Returns a list of formatted strings "CustomerName [CustomerId]".
        """
        # In a real scenario, prefix_text would be used to filter results
        # For simplicity, we'll return all relevant customers for now.
        # Ensure case-insensitive search if needed.
        customers = self.filter(
            Q(customer_name__icontains=prefix_text) | Q(customer_id__icontains=prefix_text),
            company_id=company_id
        ).annotate(
            display_name=Concat(F('customer_name'), Value(' ['), F('customer_id'), Value(']'))
        ).order_by('customer_name')[:10] # Limit results as per original AjaxControlToolkit behavior

        return [customer.display_name for customer in customers]

    def get_customer_id_from_formatted_string(self, formatted_string):
        """
        Mimics the ASP.NET 'fun.getCode' method to extract CustomerId
        from a string like "Customer Name [ID]".
        """
        if formatted_string and '[' in formatted_string and ']' in formatted_string:
            try:
                start = formatted_string.rfind('[') + 1
                end = formatted_string.rfind(']')
                return formatted_string[start:end]
            except IndexError:
                pass
        return None


class Customer(models.Model):
    """
    Represents the SD_Cust_master table.
    """
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.IntegerField(db_column='CompId', null=True, blank=True) # Inferred from ASP.NET code

    objects = CustomerManager()

    class Meta:
        managed = False  # Set to False because we are mapping to an existing database table
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class CustomerChallanManager(models.Manager):
    """
    Custom manager for CustomerChallan model to encapsulate business logic
    similar to the 'GetCustChallan' stored procedure.
    """
    def get_challans_data(self, customer_code=None, company_id=None, financial_year_id=None):
        """
        Retrieves customer challan data, mimicking the 'GetCustChallan' stored procedure.
        Includes optional filtering by customer code, company, and financial year.
        """
        queryset = self.all()

        if customer_code:
            queryset = queryset.filter(customer_id=customer_code)

        if company_id:
            queryset = queryset.filter(company_id=company_id)

        if financial_year_id:
            queryset = queryset.filter(financial_year_id=financial_year_id)

        # The original ASP.NET GridView displayed CustomerName.
        # Since CustomerName is part of SD_Cust_master, we need to join with Customer.
        # Assuming CustomerChallan has a foreign key to Customer, or CustomerId is just a field.
        # If CustomerId is just a field, we'd need to explicitly join to get CustomerName.
        # Given the ASP.NET output, 'CustomerName' comes directly with the challan data.
        # If not stored directly, we would need to annotate/select_related here.
        # For this example, let's assume `customer_id` is a FK or can be used for a simple join.
        queryset = queryset.select_related('customer') # Assuming customer_id points to Customer model

        return queryset.order_by('fin_year', 'cc_no') # Order by sensible defaults


class CustomerChallan(models.Model):
    """
    Represents the T_CustomerChallan_Header table (inferred).
    This model maps to the data displayed in the GridView.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # DataKeyNames="Id"
    fin_year = models.CharField(db_column='FinYear', max_length=10)
    cc_no = models.CharField(db_column='CCNo', max_length=50)
    # CustomerId is likely a FK to Customer. We use models.ForeignKey to link.
    # on_delete=models.DO_NOTHING is common for legacy managed=False tables
    customer = models.ForeignKey(
        Customer,
        on_delete=models.DO_NOTHING,
        db_column='CustomerId', # Maps to the actual column name
        related_name='challans'
    )
    # Note: CustomerName was displayed in GridView, derived from Customer.
    # No need to store it redundantly here if it's available via the FK relationship.
    # If the stored procedure returned it explicitly, the view layer would access it.
    company_id = models.IntegerField(db_column='CompId', null=True, blank=True) # Inferred
    financial_year_id = models.CharField(db_column='FinId', max_length=10, null=True, blank=True) # Inferred

    objects = CustomerChallanManager()

    class Meta:
        managed = False  # Set to False because we are mapping to an existing database table
        db_table = 'T_CustomerChallan_Header' # Inferred table name
        verbose_name = 'Customer Challan'
        verbose_name_plural = 'Customer Challans'

    def __str__(self):
        return f"{self.cc_no} ({self.customer.customer_name})"

    @property
    def customer_name_display(self):
        """Helper to get customer name for display, avoiding direct access in template if customer is null."""
        return self.customer.customer_name if self.customer else "N/A"
```

### 4.2 Forms

**File: `inventory/forms.py`**

**Business Benefit:** Forms streamline user input validation and processing, ensuring data quality and simplifying interactions. A dedicated search form provides a clean interface for filtering data.

```python
from django import forms
from .models import Customer

class CustomerSearchForm(forms.Form):
    """
    Form for searching customer challans by customer name.
    """
    search_value = forms.CharField(
        label="Customer Name",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing customer name...',
            'hx-get': '/inventory/customerchallan/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-autocomplete-results', # Target for autocomplete suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            # Alpine.js for managing selected value and list visibility
            'x-data': '{ selectedCustomer: "", showResults: false }',
            'x-model': 'selectedCustomer',
            'x-on:focus': 'showResults = true',
            'x-on:blur.away': 'showResults = false',
            'x-on:keydown.escape.prevent': 'showResults = false',
            '@click': 'if(selectedCustomer) showResults = true', # Show results on click if there's text
        })
    )

    def clean_search_value(self):
        """
        Custom cleaning to ensure the search value is handled correctly,
        especially for the 'CustomerName [ID]' format from autocomplete.
        """
        search_input = self.cleaned_data.get('search_value')
        if search_input:
            # If the user selected an item from autocomplete, it will be "Name [ID]".
            # We need to extract the ID for the actual search query.
            # Delegate this to the Customer model manager.
            customer_id = Customer.objects.get_customer_id_from_formatted_string(search_input)
            if customer_id:
                return customer_id
            else:
                # If it's just a partial name, return the name itself for fuzzy search or
                # return None if strict ID is required.
                # For this case, we'll return the raw input and let the view decide
                # to use it as a name search or a lookup.
                # Since the original Search_Click uses fun.getCode, it expects ID.
                # So if no ID is found, we assume invalid input for strict search.
                return None # Or the partial name if the search allows it.
        return None # No search value entered
```

### 4.3 Views

**File: `inventory/views.py`**

**Business Benefit:** Thin views ensure that business logic resides in models, making your application easier to debug, test, and scale. Using Class-Based Views (CBVs) with HTMX provides a reactive and responsive user interface without complex JavaScript.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q
from .models import CustomerChallan, Customer
from .forms import CustomerSearchForm

# Placeholder for company_id and financial_year_id.
# In a real application, these would come from request.user, session, or specific context.
# For demonstration, we'll use dummy values.
DUMMY_COMPANY_ID = 1 # Replace with actual logic to get company_id
DUMMY_FINANCIAL_YEAR_ID = "2023-2024" # Replace with actual logic to get financial_year_id

class CustomerChallanListView(ListView):
    """
    Displays the main page for customer challan list.
    Handles the initial load and renders the search form and an empty container for the table.
    """
    model = CustomerChallan
    template_name = 'inventory/customerchallan/list.html'
    context_object_name = 'customer_challans'
    paginate_by = 17 # Matches ASP.NET PageSize

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = CustomerSearchForm(self.request.GET or None)
        return context

    # This view primarily renders the shell. The actual table content is loaded via HTMX.
    # The initial ASP.NET Page_Load calls BindData. We mimic this by having
    # the HTMX fragment for the table load on 'load' event.


class CustomerChallanTablePartialView(ListView):
    """
    HTMX endpoint to render just the customer challan table content.
    This view is called by HTMX requests for initial load, search, and pagination.
    """
    model = CustomerChallan
    template_name = 'inventory/customerchallan/_customerchallan_table.html'
    context_object_name = 'customer_challans'
    paginate_by = 17 # Matches ASP.NET PageSize

    def get_queryset(self):
        form = CustomerSearchForm(self.request.GET)
        customer_code = None
        if form.is_valid():
            customer_code = form.cleaned_data.get('search_value')

        # Mimic the ASP.NET BindData method and GetCustChallan SP.
        # Pass company_id and financial_year_id as per original.
        queryset = CustomerChallan.objects.get_challans_data(
            customer_code=customer_code,
            company_id=DUMMY_COMPANY_ID, # Replace with dynamic value
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID # Replace with dynamic value
        )
        return queryset

    # No need for form_valid/form_invalid, as this is a GET-only view for HTMX partial swap.


class CustomerAutoCompleteView(View):
    """
    HTMX endpoint for customer name autocomplete.
    Mimics the ASP.NET 'sql' web method.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('search_value', '')
        company_id = DUMMY_COMPANY_ID # Replace with dynamic value from session/user

        suggestions = Customer.objects.get_customer_choices(prefix_text, company_id)

        # Return suggestions as HTML list items for HTMX to swap
        html_suggestions = ""
        if suggestions:
            # Alpine.js x-on:click to select value and hide list
            # hx-on:click because it's dynamically loaded content
            html_suggestions = """
            <ul x-show="showResults" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto mt-1">
            """
            for suggestion in suggestions:
                # When an item is clicked, set the input value and hide the results
                html_suggestions += f"""
                <li class="p-2 hover:bg-gray-100 cursor-pointer"
                    hx-on:click="selectedCustomer = '{suggestion}'; showResults = false; $dispatch('search-triggered')">
                    {suggestion}
                </li>
                """
            html_suggestions += "</ul>"
        return HttpResponse(html_suggestions)

    # Note: The original 'sql' method returned a JSON array.
    # For HTMX, directly returning HTML fragments is more idiomatic for simple autocompletes.
    # If a JSON API is preferred, a JsonResponse can be returned, and frontend JS (Alpine.js)
    # would parse and render the suggestions. Given the HTMX-first directive, HTML fragment is better.
```

### 4.4 Templates

**Business Benefit:** DRY templates minimize code duplication, making design changes easier and ensuring consistency across the application. HTMX and Alpine.js integration allows for dynamic, interactive UIs without extensive JavaScript frameworks, improving perceived performance. DataTables further enhance user experience for large datasets.

**File: `inventory/customerchallan/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Customer Challan - Print{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white font-bold py-3 px-4 rounded-t-lg mb-6">
            &nbsp;<strong>Customer Challan - Print</strong>
        </div>

        <div class="mb-6">
            <form id="customer-search-form" hx-get="{% url 'inventory:customerchallan_table' %}" hx-target="#challan-table-container" hx-swap="innerHTML">
                {% csrf_token %}
                <div class="flex items-center space-x-4 relative">
                    <label for="{{ form.search_value.id_for_label }}" class="block text-gray-700 text-sm font-bold">
                        {{ form.search_value.label }}
                    </label>
                    <div class="flex-grow">
                        {{ form.search_value }}
                        <div id="customer-autocomplete-results">
                            <!-- Autocomplete results will be injected here by HTMX -->
                        </div>
                    </div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out">
                        Search
                    </button>
                </div>
            </form>
        </div>

        <div id="challan-table-container"
             hx-trigger="load, search-triggered from:#customer-search-form"
             hx-get="{% url 'inventory:customerchallan_table' %}"
             hx-swap="innerHTML">
            <!-- DataTables content will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading Customer Challans...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"></script>
<!-- For future: if using Bootstrap 5 styling with DataTables, otherwise remove -->
<!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script> -->

<script>
    // HTMX global events for DataTables reinitialization
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'challan-table-container') {
            // Re-initialize DataTables after new content is swapped in
            if ($.fn.DataTable.isDataTable('#customerChallanTable')) {
                $('#customerChallanTable').DataTable().destroy();
            }
            $('#customerChallanTable').DataTable({
                "pageLength": 17, // Matches original ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "responsive": true, // Enable responsive design
                "pagingType": "full_numbers" // Full number pagination
            });
        }
    });

    // To submit the form when an autocomplete item is selected
    document.body.addEventListener('search-triggered', function() {
        document.getElementById('customer-search-form').requestSubmit();
    });
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css" rel="stylesheet">
<!-- If using Bootstrap 5 styling for DataTables, otherwise plain DataTables CSS -->
<!-- <link href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css" rel="stylesheet"> -->
<!-- <link href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" rel="stylesheet"> -->

<!-- Tailwind CSS (assumed to be configured in base.html or project) -->
<!-- Example placeholder for the original CSS files. In Django, replace with Tailwind or specific CSS for components -->
<!-- <link href="../../../Css/yui-datatable.css" rel="stylesheet" type="text/css" /> -->
<!-- <link href="../../../Css/StyleSheet.css" rel="stylesheet" type="text/css" /> -->
{% endblock %}
```

**File: `inventory/customerchallan/_customerchallan_table.html`**

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="customerChallanTable" class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="py-3 px-6">SN</th>
                <th scope="col" class="py-3 px-6">Fin Yrs</th>
                <th scope="col" class="py-3 px-6">CCNo</th>
                <th scope="col" class="py-3 px-6">Customer Name</th>
                <th scope="col" class="py-3 px-6">Code</th>
                <!-- 'Id' column is hidden but available in data -->
                <th scope="col" class="py-3 px-6 text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for challan in customer_challans %}
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="py-4 px-6">{{ forloop.counter }}</td>
                <td class="py-4 px-6">{{ challan.fin_year }}</td>
                <td class="py-4 px-6">
                    <a href="{% url 'inventory:customerchallan_detail' pk=challan.id %}"
                       class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                        {{ challan.cc_no }}
                    </a>
                </td>
                <td class="py-4 px-6">{{ challan.customer_name_display }}</td> {# Using the property from the model #}
                <td class="py-4 px-6">{{ challan.customer.customer_id }}</td>
                <td class="py-4 px-6 text-center">
                    <!-- No specific edit/delete actions on this print page, but placeholder for future -->
                    <span class="text-gray-400">View Details</span>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-lg text-red-700 font-semibold">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- This script block will be re-executed by HTMX after swap -->
<script>
    // DataTables initialization is handled by the parent list.html template after htmx:afterSwap
    // This partial just provides the table structure.
</script>
```

### 4.5 URLs

**File: `inventory/urls.py`**

**Business Benefit:** Clean URL structures improve navigability, user experience, and search engine optimization. Centralized URL management simplifies routing and ensures consistency across the application.

```python
from django.urls import path
from .views import CustomerChallanListView, CustomerChallanTablePartialView, CustomerAutoCompleteView

app_name = 'inventory' # Define app_name for URL namespacing

urlpatterns = [
    # Main customer challan list page
    path('customerchallan/', CustomerChallanListView.as_view(), name='customerchallan_list'),
    # HTMX endpoint for the customer challan table partial
    path('customerchallan/table/', CustomerChallanTablePartialView.as_view(), name='customerchallan_table'),
    # HTMX endpoint for customer autocomplete suggestions
    path('customerchallan/autocomplete/', CustomerAutoCompleteView.as_view(), name='customerchallan_autocomplete'),

    # Placeholder for the detail page. This would be a separate view and template.
    # The ASP.NET code redirected to "CustomerChallan_Print_Details.aspx?Id=..."
    # We will mimic this by having a Django detail view.
    path('customerchallan/<int:pk>/detail/',
         # Placeholder for the actual detail view class if it exists in another module
         # For now, it will be a simple view that simulates redirection.
         # In a real app, this would be a DetailView.
         # Example: CustomerChallanDetailView.as_view()
         # For this exercise, we will just return a dummy response.
         lambda request, pk: HttpResponse(f"Redirecting to Customer Challan Detail for ID: {pk}"),
         name='customerchallan_detail'),
]
```

### 4.6 Tests

**File: `inventory/tests.py`**

**Business Benefit:** Automated tests are critical for ensuring software quality, preventing regressions, and validating that the migrated application functions as expected. Comprehensive test coverage provides confidence in the modernization process.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .models import Customer, CustomerChallan

# Dummy values for company_id and financial_year_id to match view logic
DUMMY_COMPANY_ID = 1
DUMMY_FINANCIAL_YEAR_ID = "2023-2024"

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests for Customer model
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Alpha Customer',
            company_id=DUMMY_COMPANY_ID
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002',
            customer_name='Beta Customer',
            company_id=DUMMY_COMPANY_ID
        )
        cls.customer3 = Customer.objects.create(
            customer_id='CUST003',
            customer_name='Gamma Customer',
            company_id=DUMMY_COMPANY_ID + 1 # Different company
        )

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customer_id, 'CUST001')
        self.assertEqual(self.customer1.customer_name, 'Alpha Customer')
        self.assertEqual(self.customer1.company_id, DUMMY_COMPANY_ID)

    def test_customer_str_representation(self):
        self.assertEqual(str(self.customer1), "Alpha Customer [CUST001]")

    def test_customer_verbose_name(self):
        self.assertEqual(Customer._meta.verbose_name, 'Customer')
        self.assertEqual(Customer._meta.verbose_name_plural, 'Customers')

    def test_get_customer_choices(self):
        # Test exact match
        choices = Customer.objects.get_customer_choices('Alpha', DUMMY_COMPANY_ID)
        self.assertIn("Alpha Customer [CUST001]", choices)
        self.assertNotIn("Beta Customer [CUST002]", choices)

        # Test partial match
        choices = Customer.objects.get_customer_choices('a', DUMMY_COMPANY_ID)
        self.assertIn("Alpha Customer [CUST001]", choices)
        self.assertIn("Beta Customer [CUST002]", choices) # Should also contain if logic allows
        self.assertNotIn("Gamma Customer [CUST003]", choices) # Excludes different company

        # Test no match
        choices = Customer.objects.get_customer_choices('NonExistent', DUMMY_COMPANY_ID)
        self.assertEqual(len(choices), 0)

        # Test company_id filtering
        choices = Customer.objects.get_customer_choices('', DUMMY_COMPANY_ID + 1)
        self.assertIn("Gamma Customer [CUST003]", choices)
        self.assertNotIn("Alpha Customer [CUST001]", choices)

    def test_get_customer_id_from_formatted_string(self):
        self.assertEqual(Customer.objects.get_customer_id_from_formatted_string("Alpha Customer [CUST001]"), "CUST001")
        self.assertIsNone(Customer.objects.get_customer_id_from_formatted_string("Alpha Customer"))
        self.assertIsNone(Customer.objects.get_customer_id_from_formatted_string("Invalid [Format"))
        self.assertIsNone(Customer.objects.get_customer_id_from_formatted_string(None))
        self.assertIsNone(Customer.objects.get_customer_id_from_formatted_string(""))


class CustomerChallanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Test Customer',
            company_id=DUMMY_COMPANY_ID
        )
        # Create test data for all tests for CustomerChallan model
        cls.challan1 = CustomerChallan.objects.create(
            id=1,
            fin_year='2023',
            cc_no='CC001',
            customer=cls.customer,
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        cls.challan2 = CustomerChallan.objects.create(
            id=2,
            fin_year='2023',
            cc_no='CC002',
            customer=cls.customer,
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        # Challan for a different company
        cls.challan3 = CustomerChallan.objects.create(
            id=3,
            fin_year='2024',
            cc_no='CC003',
            customer=cls.customer,
            company_id=DUMMY_COMPANY_ID + 1,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        # Challan for a different financial year
        cls.challan4 = CustomerChallan.objects.create(
            id=4,
            fin_year='2024',
            cc_no='CC004',
            customer=cls.customer,
            company_id=DUMMY_COMPANY_ID,
            financial_year_id="2024-2025"
        )


    def test_customerchallan_creation(self):
        self.assertEqual(self.challan1.cc_no, 'CC001')
        self.assertEqual(self.challan1.customer, self.customer)
        self.assertEqual(self.challan1.company_id, DUMMY_COMPANY_ID)

    def test_customerchallan_str_representation(self):
        self.assertEqual(str(self.challan1), f"CC001 ({self.customer.customer_name})")

    def test_customerchallan_verbose_name(self):
        self.assertEqual(CustomerChallan._meta.verbose_name, 'Customer Challan')
        self.assertEqual(CustomerChallan._meta.verbose_name_plural, 'Customer Challans')

    def test_customer_name_display_property(self):
        self.assertEqual(self.challan1.customer_name_display, self.customer.customer_name)
        # Test case where customer might be None (though FK usually prevents this without null=True)
        # For demonstration:
        # self.challan1.customer = None
        # self.assertEqual(self.challan1.customer_name_display, "N/A")


    def test_get_challans_data_no_filters(self):
        challans = CustomerChallan.objects.get_challans_data(
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        self.assertEqual(challans.count(), 2)
        self.assertIn(self.challan1, challans)
        self.assertIn(self.challan2, challans)
        self.assertNotIn(self.challan3, challans) # Different company
        self.assertNotIn(self.challan4, challans) # Different financial year

    def test_get_challans_data_with_customer_code(self):
        challans = CustomerChallan.objects.get_challans_data(
            customer_code=self.customer.customer_id,
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        self.assertEqual(challans.count(), 2)
        self.assertIn(self.challan1, challans)
        self.assertIn(self.challan2, challans)

    def test_get_challans_data_no_match(self):
        challans = CustomerChallan.objects.get_challans_data(
            customer_code='NONEXISTENT',
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        self.assertEqual(challans.count(), 0)

    def test_get_challans_data_filters_by_company(self):
        challans = CustomerChallan.objects.get_challans_data(
            company_id=DUMMY_COMPANY_ID + 1,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        self.assertEqual(challans.count(), 1)
        self.assertIn(self.challan3, challans)

    def test_get_challans_data_filters_by_financial_year(self):
        challans = CustomerChallan.objects.get_challans_data(
            company_id=DUMMY_COMPANY_ID,
            financial_year_id="2024-2025"
        )
        self.assertEqual(challans.count(), 1)
        self.assertIn(self.challan4, challans)


class CustomerChallanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Test Customer',
            company_id=DUMMY_COMPANY_ID
        )
        cls.challan1 = CustomerChallan.objects.create(
            id=1,
            fin_year='2023',
            cc_no='CC001',
            customer=cls.customer,
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )
        cls.challan2 = CustomerChallan.objects.create(
            id=2,
            fin_year='2023',
            cc_no='CC002',
            customer=cls.customer,
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )

    def setUp(self):
        self.client = Client()

    @patch('inventory.views.DUMMY_COMPANY_ID', DUMMY_COMPANY_ID)
    @patch('inventory.views.DUMMY_FINANCIAL_YEAR_ID', DUMMY_FINANCIAL_YEAR_ID)
    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:customerchallan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/list.html')
        self.assertContains(response, 'Customer Challan - Print')
        # Check for the search form
        self.assertContains(response, 'name="search_value"')
        # Check for the challan table container, indicating HTMX load
        self.assertContains(response, 'id="challan-table-container"')


    @patch('inventory.views.DUMMY_COMPANY_ID', DUMMY_COMPANY_ID)
    @patch('inventory.views.DUMMY_FINANCIAL_YEAR_ID', DUMMY_FINANCIAL_YEAR_ID)
    def test_table_partial_view_htmx_load(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:customerchallan_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/_customerchallan_table.html')
        self.assertContains(response, 'id="customerChallanTable"')
        self.assertContains(response, self.challan1.cc_no)
        self.assertContains(response, self.challan2.cc_no)
        self.assertContains(response, self.customer.customer_name)

    @patch('inventory.views.DUMMY_COMPANY_ID', DUMMY_COMPANY_ID)
    @patch('inventory.views.DUMMY_FINANCIAL_YEAR_ID', DUMMY_FINANCIAL_YEAR_ID)
    def test_table_partial_view_htmx_search(self):
        # Create another customer and challan to test filtering
        customer_gamma = Customer.objects.create(customer_id='CUST003', customer_name='Gamma Customer', company_id=DUMMY_COMPANY_ID)
        CustomerChallan.objects.create(
            id=3, fin_year='2024', cc_no='CC003', customer=customer_gamma,
            company_id=DUMMY_COMPANY_ID, financial_year_id=DUMMY_FINANCIAL_YEAR_ID
        )

        headers = {'HTTP_HX_REQUEST': 'true'}
        search_value = "Gamma Customer [CUST003]" # Formatted string as from autocomplete
        response = self.client.get(reverse('inventory:customerchallan_table'), {'search_value': search_value}, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'id="customerChallanTable"')
        self.assertContains(response, 'CC003') # Should contain the filtered challan
        self.assertNotContains(response, 'CC001') # Should not contain unfiltered challans

    @patch('inventory.views.DUMMY_COMPANY_ID', DUMMY_COMPANY_ID)
    def test_autocomplete_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:customerchallan_autocomplete'), {'search_value': 'Test'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<li class="p-2 hover:bg-gray-100 cursor-pointer"')
        self.assertContains(response, 'Test Customer [CUST001]')

        response = self.client.get(reverse('inventory:customerchallan_autocomplete'), {'search_value': 'NonExistent'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, '<li>') # Should not contain any list items if no results

    def test_customerchallan_detail_redirect(self):
        # This tests the placeholder URL for the detail page.
        response = self.client.get(reverse('inventory:customerchallan_detail', args=[self.challan1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Redirecting to Customer Challan Detail for ID: {self.challan1.id}")
```

---

## Step 5: HTMX and Alpine.js Integration

**Business Benefit:** This modern frontend stack delivers a highly responsive and dynamic user experience without the complexity of traditional JavaScript frameworks. It simplifies development, improves performance, and makes the application feel more modern and efficient.

**Instructions:**

-   **HTMX for Dynamic Content Loading:**
    -   The main `customerchallan/list.html` page uses `hx-get` on a `div` (`id="challan-table-container"`) to load the actual table content from `{% url 'inventory:customerchallan_table' %}`.
    -   This `hx-get` is triggered on `load` (initial page load) and by a custom event `search-triggered` dispatched from Alpine.js when a customer is selected from autocomplete or the search button is clicked.
    -   The search form itself uses `hx-get` to trigger a new request to `{% url 'inventory:customerchallan_table' %}` with the search parameters, updating only the table area.
    -   Autocomplete for the customer search box uses `hx-get` to `{% url 'inventory:customerchallan_autocomplete' %}` triggered on `keyup changed delay:500ms`, targeting a `div` below the input to display suggestions.
    -   No full page reloads occur for search or table updates.

-   **Alpine.js for UI State Management:**
    -   The customer search input (`search_value`) utilizes `x-data` and `x-model` to manage its value and the `showResults` boolean.
    -   `x-on:focus` and `x-on:blur.away` control the visibility of the autocomplete results dropdown.
    -   When an autocomplete suggestion is clicked (`hx-on:click` on dynamic content), Alpine.js updates the `selectedCustomer` (which is `x-model` bound to the input) and hides the results. It also `dispatches` a custom event `search-triggered` which HTMX listens for to refresh the table.

-   **DataTables for List Views:**
    -   The `_customerchallan_table.html` partial contains the basic `<table>` structure.
    -   The `list.html` template includes jQuery DataTables and initializes it on the `customerChallanTable` ID.
    -   Crucially, a global HTMX event listener `document.body.addEventListener('htmx:afterSwap', ...)` is used to re-initialize DataTables *after* HTMX injects new table content into the `challan-table-container`. This ensures DataTables functions correctly with dynamically loaded data, providing client-side sorting, filtering, and pagination.

-   **HTMX-Only Interactions:**
    -   All dynamic interactions on this page (search, table refresh, autocomplete) are handled exclusively by HTMX and Alpine.js, minimizing custom JavaScript. The `$.fn.DataTable().destroy()` and re-initialization is standard practice for DataTables when its content changes dynamically.

This structured approach ensures a seamless migration from ASP.NET to a modern, maintainable, and high-performing Django application, ready for future enhancements and scalability.