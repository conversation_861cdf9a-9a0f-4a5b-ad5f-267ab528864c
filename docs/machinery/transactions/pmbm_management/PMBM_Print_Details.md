## ASP.NET to Django Conversion Script: PMBM Print Details

This modernization plan outlines the strategic transition of your existing ASP.NET application, specifically the `PMBM_Print_Details.aspx` module, to a modern Django-based solution. Our focus is on leveraging AI-assisted automation, adhering to the "Fat Model, Thin View" paradigm, and integrating contemporary frontend technologies like HTMX and Alpine.js for a highly interactive and efficient user experience.

### Business Value Proposition:
Migrating this critical "Preventive / Breakdown Maintenance Print Details" module to Django offers significant benefits:
*   **Reduced Operational Costs:** By replacing legacy Crystal Reports with native Django rendering and an efficient ORM, you eliminate licensing fees and simplify deployment, leading to long-term cost savings.
*   **Enhanced Maintainability:** Django's structured, "batteries-included" approach, combined with clear separation of concerns (business logic in models, presentation in templates), drastically improves code readability and maintainability.
*   **Improved Scalability & Performance:** Django is renowned for its scalability, capable of handling increased user loads and data volumes more efficiently than older ASP.NET Web Forms architectures. HTMX minimizes server load by handling partial page updates.
*   **Modern User Experience:** Adopting HTMX and Alpine.js delivers a highly responsive, single-page application-like feel without the complexity of traditional JavaScript frameworks, enhancing user satisfaction.
*   **Future-Proofing:** Transitioning to a widely supported, open-source framework like Django ensures your application remains relevant, secure, and adaptable to future business needs and technological advancements.
*   **Automation-Ready:** This plan is designed for automated execution, minimizing manual coding effort and accelerating the migration timeline, delivering business value faster.

### Core Transformation Goal:
The original ASP.NET page is primarily a report viewer, aggregating data from multiple tables to display detailed information about a specific Preventive/Breakdown Maintenance record. Our Django solution will replicate this functionality by centralizing data aggregation logic within the models and rendering it efficiently using Django templates, HTMX, and Alpine.js. While the original page doesn't perform CRUD operations, the comprehensive migration strategy will include general CRUD capabilities for the core `PmbmMaster` records, aligning with a typical Django application architecture and the provided template requirements.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination (for list views)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The ASP.NET code-behind extensively uses `SqlConnection`, `SqlCommand`, `SqlDataAdapter`, and raw SQL queries (via `fun.select`) to retrieve data from multiple tables.
We identify the following tables and their relevant columns as accessed by the application:

*   **`tblMS_PMBM_Master`**: This is the primary table for Preventive/Breakdown Maintenance records.
    *   Columns inferred: `Id`, `SysDate`, `CompId`, `MachineId`, `PMBM` (0/1), `FromDate`, `ToDate`, `FromTime`, `ToTime`, `NameOfAgency`, `NameOfEngineer`, `NextPMDueOn`, `Remarks`.
*   **`tblMS_Master`**: This table seems to hold machine details.
    *   Columns inferred: `Id`, `ItemId`, `CompId`, `FinYearId`, `Model`, `Make`, `Capacity`, `Location`.
*   **`tblDG_Item_Master`**: This table holds general item/product master data.
    *   Columns inferred: `Id`, `ManfDesc`, `UOMBasic`, `ItemCode`, `CId`, `CompId`.
*   **`tblDG_Category_Master`**: This table holds item categories.
    *   Columns inferred: `CId`.
*   **`Unit_Master`**: This table stores units of measurement.
    *   Columns inferred: `Id`, `Symbol`.
*   **Implicit Tables**: `Company` (for `CompId` and `fun.CompAdd`) and `FinancialYear` (for `FinYearId`). We will create placeholder models for these.

## Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The `PMBM_Print_Details.aspx` page is exclusively a **Read/Report** page. It does not perform Create, Update, or Delete (CRUD) operations on `tblMS_PMBM_Master` or any other table directly from this page.

*   **Read Operation:**
    *   The page fetches a specific `PMBMId` from the query string.
    *   It then performs a series of linked `SELECT` queries across `tblMS_PMBM_Master`, `tblMS_Master`, `tblDG_Item_Master`, `tblDG_Category_Master`, and `Unit_Master` to gather all necessary details for the report.
    *   It transforms the `PMBM` numerical value (0/1) into "Preventive" or "Breckdown" text.
    *   It formats various date fields from database strings to `DMY` format.
    *   It retrieves company address information using `fun.CompAdd(CompId)`.
    *   All collected and transformed data is then passed to a `CrystalReportViewer` for rendering.
*   **Parameters:** `PMBMId` (query string), `CompId` (session), `FinYearId` (session), `Key` (query string, Crystal Reports internal).
*   **Navigation:** A "Cancel" button redirects to `PMBM_Print.aspx`, implying a return to a list or search page.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page is quite simple visually, focused on rendering a Crystal Report.
*   **`CR:CrystalReportViewer`**: This is the main component, responsible for displaying the generated report. In Django, this functionality will be replaced by direct HTML rendering using the aggregated data.
*   **`CR:CrystalReportSource`**: Defines the report file (`PMBM.rpt`) and its data source, managed in the code-behind. This will be replaced by Django's ORM queries and template rendering.
*   **`asp:Panel`**: A container, which will be naturally replaced by `div` elements in Django templates.
*   **`asp:Button` (ID="Cancel")**: Triggers a server-side redirect. In Django, this will be a simple link or an HTMX-powered back button/modal closer.

## Step 4: Generate Django Code

We will create a new Django application named `machinery_maint` to house these components.

### 4.1 Models

**Task:** Create Django models based on the identified database schema. We include placeholder models for `Company` and `FinancialYear` as inferred dependencies. The `PmbmMaster` model will include a custom manager or method to aggregate report data, following the "fat model" principle.

**File:** `machinery_maint/models.py`

```python
from django.db import models
from django.utils import timezone
from django.db.models import F

# Placeholder models for external dependencies
class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Company_Master'  # Assuming a common company master table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    year_name = models.CharField(db_column='FinYearName', max_length=50)

    class Meta:
        managed = False
        db_table = 'Financial_Year_Master' # Assuming a common financial year table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class CategoryMaster(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True) # CId in source
    category_name = models.CharField(db_column='CategoryName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.category_name or f"Category {self.id}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, verbose_name='Item Name')
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')
    item_code = models.CharField(db_column='ItemCode', max_length=100, verbose_name='Item Code')
    category = models.ForeignKey(CategoryMaster, on_delete=models.DO_NOTHING, db_column='CId', related_name='items')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='items')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.manf_desc

class MachineMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_master = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='machines')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='machines')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='machines')
    model_name = models.CharField(db_column='Model', max_length=255, verbose_name='Model')
    make = models.CharField(db_column='Make', max_length=255, verbose_name='Make')
    capacity = models.CharField(db_column='Capacity', max_length=255, verbose_name='Capacity')
    location = models.CharField(db_column='Location', max_length=255, verbose_name='Location')

    class Meta:
        managed = False
        db_table = 'tblMS_Master' # tblMS_Master is for Machine Master
        verbose_name = 'Machine'
        verbose_name_plural = 'Machines'

    def __str__(self):
        return f"{self.item_master.manf_desc} ({self.model_name})"


class PmbmMaster(models.Model):
    PMBM_CHOICES = (
        (0, 'Preventive'),
        (1, 'Breakdown'), # Original code uses 'Breckdown', correcting to 'Breakdown'
    )

    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', verbose_name='System Date')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='pmbm_records')
    machine = models.ForeignKey(MachineMaster, on_delete=models.DO_NOTHING, db_column='MachineId', related_name='pmbm_records')
    pmbm_type = models.IntegerField(db_column='PMBM', choices=PMBM_CHOICES, verbose_name='PM/BM Type')
    from_date = models.DateField(db_column='FromDate', verbose_name='From Date')
    to_date = models.DateField(db_column='ToDate', verbose_name='To Date')
    from_time = models.CharField(db_column='FromTime', max_length=50, verbose_name='From Time') # Using CharField as time format is unknown from original
    to_time = models.CharField(db_column='ToTime', max_length=50, verbose_name='ToTime')
    name_of_agency = models.CharField(db_column='NameOfAgency', max_length=255, blank=True, null=True, verbose_name='Name of Agency')
    name_of_engineer = models.CharField(db_column='NameOfEngineer', max_length=255, blank=True, null=True, verbose_name='Name of Engineer')
    next_pm_due_on = models.DateField(db_column='NextPMDueOn', blank=True, null=True, verbose_name='Next PM Due On')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True, verbose_name='Remarks')

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Master'
        verbose_name = 'P/BM Master Record'
        verbose_name_plural = 'P/BM Master Records'

    def __str__(self):
        return f"{self.get_pmbm_type_display()} - {self.machine.item_master.manf_desc} on {self.sys_date}"

    @classmethod
    def get_report_details(cls, pmbm_id, comp_id, fin_year_id):
        """
        Aggregates and returns comprehensive details for a specific PMBM record
        similar to how the Crystal Report data source was prepared.
        This is a fat model method centralizing complex report data retrieval.
        """
        try:
            pmbm_record = cls.objects.select_related(
                'company',
                'machine__item_master__unit_master',
                'machine__item_master__category',
                'machine__financial_year'
            ).get(
                id=pmbm_id,
                company__id=comp_id,
                machine__financial_year__id__lte=fin_year_id # Assuming FinYearId check applies to machine's finyear
            )

            # Data transformation as seen in original C# code
            pmbm_type_display = pmbm_record.get_pmbm_type_display()

            # Company Address (simulated from fun.CompAdd)
            company_address = pmbm_record.company.address if pmbm_record.company else "N/A"

            return {
                'id': pmbm_record.id,
                'sys_date': pmbm_record.sys_date,
                'company_id': pmbm_record.company.id,
                'machine_id': pmbm_record.machine.id,
                'pmbm_type_display': pmbm_type_display,
                'from_date': pmbm_record.from_date,
                'to_date': pmbm_record.to_date,
                'from_time': pmbm_record.from_time,
                'to_time': pmbm_record.to_time,
                'name_of_agency': pmbm_record.name_of_agency,
                'name_of_engineer': pmbm_record.name_of_engineer,
                'next_pm_due_on': pmbm_record.next_pm_due_on,
                'remarks': pmbm_record.remarks,
                'model': pmbm_record.machine.model_name,
                'make': pmbm_record.machine.make,
                'capacity': pmbm_record.machine.capacity,
                'location': pmbm_record.machine.location,
                'machine_code': pmbm_record.machine.item_master.item_code,
                'uom_basic_symbol': pmbm_record.machine.item_master.uom_basic.symbol,
                'item_name': pmbm_record.machine.item_master.manf_desc,
                'company_address': company_address,
            }
        except cls.DoesNotExist:
            return None
        except Exception as e:
            # Log the exception for debugging
            print(f"Error retrieving PMBM report details: {e}")
            return None

```

### 4.2 Forms

**Task:** Define a Django form for the `PmbmMaster` model. This is provided for general CRUD operations on `PmbmMaster` records, even though the original ASP.NET page is a report viewer.

**File:** `machinery_maint/forms.py`

```python
from django import forms
from .models import PmbmMaster, MachineMaster, Company # Import necessary models

class PmbmMasterForm(forms.ModelForm):
    # Dynamically populate choices for ForeignKey fields
    # In a real app, you'd filter these based on user permissions or context
    machine = forms.ModelChoiceField(
        queryset=MachineMaster.objects.all(),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    company = forms.ModelChoiceField(
        queryset=Company.objects.all(),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = PmbmMaster
        fields = [
            'sys_date', 'company', 'machine', 'pmbm_type', 'from_date', 
            'to_date', 'from_time', 'to_time', 'name_of_agency', 
            'name_of_engineer', 'next_pm_due_on', 'remarks'
        ]
        widgets = {
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pmbm_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'from_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'to_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'from_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'to_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'name_of_agency': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'name_of_engineer': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'next_pm_due_on': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }

    # Add custom validation if needed, e.g., to_date after from_date
    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and to_date < from_date:
            self.add_error('to_date', 'To Date cannot be before From Date.')
        return cleaned_data

```

### 4.3 Views

**Task:** Implement standard CRUD operations for `PmbmMaster` using Class-Based Views (CBVs), and crucially, a specific `TemplateView` or `DetailView` to render the "Print Details" report by leveraging the model's data aggregation method.

**File:** `machinery_maint/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import PmbmMaster, Company, FinancialYear # Import all models needed for context
from .forms import PmbmMasterForm
from django.contrib.auth.mixins import LoginRequiredMixin # For authentication

# --- Standard CRUD Views for PmbmMaster (as per template requirement) ---

class PmbmMasterListView(LoginRequiredMixin, ListView):
    model = PmbmMaster
    template_name = 'machinery_maint/pmbmmaster/list.html'
    context_object_name = 'pmbmmaster_records'
    paginate_by = 10 # Example pagination

class PmbmMasterCreateView(LoginRequiredMixin, CreateView):
    model = PmbmMaster
    form_class = PmbmMasterForm
    template_name = 'machinery_maint/pmbmmaster/form.html'
    success_url = reverse_lazy('pmbmmaster_list')

    def form_valid(self, form):
        # Assign session-based data if applicable (e.g., CompId from session)
        # Assuming user profile or session provides comp_id and fin_year_id
        # For demonstration, hardcoding or getting from current user
        # user_company = self.request.user.userprofile.company # Example if UserProfile exists
        # form.instance.company = user_company
        # form.instance.financial_year = current_fin_year 
        
        # Example: Ensure company is set, if not already selected via form
        if not form.instance.company_id:
            # This would likely come from the authenticated user's context
            # For now, pick the first company or handle error
            try:
                form.instance.company = Company.objects.first() 
            except Company.DoesNotExist:
                messages.error(self.request, "No company found. Please configure company data.")
                return self.form_invalid(form)

        response = super().form_valid(form)
        messages.success(self.request, 'P/BM Master Record added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPmbmMasterList'
                }
            )
        return response

class PmbmMasterUpdateView(LoginRequiredMixin, UpdateView):
    model = PmbmMaster
    form_class = PmbmMasterForm
    template_name = 'machinery_maint/pmbmmaster/form.html'
    success_url = reverse_lazy('pmbmmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'P/BM Master Record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPmbmMasterList'
                }
            )
        return response

class PmbmMasterDeleteView(LoginRequiredMixin, DeleteView):
    model = PmbmMaster
    template_name = 'machinery_maint/pmbmmaster/confirm_delete.html'
    success_url = reverse_lazy('pmbmmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'P/BM Master Record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPmbmMasterList'
                }
            )
        return response

# --- Partial View for DataTable HTMX Load ---
class PmbmMasterTablePartialView(LoginRequiredMixin, ListView):
    model = PmbmMaster
    template_name = 'machinery_maint/pmbmmaster/_pmbmmaster_table.html'
    context_object_name = 'pmbmmaster_records'

# --- Specific View for PMBM_Print_Details.aspx functionality ---
class PmbmMasterReportDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'machinery_maint/pmbmmaster/report_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        pmbm_id = self.kwargs.get('pk')
        
        # Simulating session data from ASP.NET (CompId, FinYearId)
        # In a real Django app, this would come from authenticated user's profile or session
        # For demonstration, retrieve from request or set defaults.
        # Ensure your authentication system provides this or adapt accordingly.
        user_comp_id = self.request.session.get('compid', None) # Get from session
        user_fin_year_id = self.request.session.get('finyear', None) # Get from session

        if not user_comp_id:
            # Fallback for testing or if session not set, try to get a default
            try:
                user_comp_id = Company.objects.first().id
            except Company.DoesNotExist:
                raise Http404("Company ID not found in session or database.")
        
        if not user_fin_year_id:
             try:
                user_fin_year_id = FinancialYear.objects.first().id # Or get current active fin year
             except FinancialYear.DoesNotExist:
                 raise Http404("Financial Year ID not found in session or database.")

        report_details = PmbmMaster.get_report_details(pmbm_id, user_comp_id, user_fin_year_id)

        if not report_details:
            raise Http404("PMBM Report details not found or access denied.")
        
        context['report_details'] = report_details
        context['company_address'] = report_details.get('company_address', 'Company Address Not Available') # Pass the address separately
        return context

```

### 4.4 Templates

**Task:** Create templates for each view, including list, form (as partials for HTMX modals), delete confirmation, and the specific report detail page.

**File:** `machinery_maint/templates/machinery_maint/pmbmmaster/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">P/BM Master Records</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'pmbmmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Record
        </button>
    </div>
    
    <div id="pmbmmasterTable-container"
         hx-trigger="load, refreshPmbmMasterList from:body"
         hx-get="{% url 'pmbmmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading records...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader page interactions
        // e.g., x-data="{ showModal: false }" on #modal and then toggle class 'hidden'
    });
</script>
{% endblock %}
```

**File:** `machinery_maint/templates/machinery_maint/pmbmmaster/_pmbmmaster_table.html`

```html
<table id="pmbmmasterTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in pmbmmaster_records %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_pmbm_type_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.machine.item_master.manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.to_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'pmbmmaster_report_detail' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    View Report
                </button>
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'pmbmmaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'pmbmmaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#pmbmmasterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**File:** `machinery_maint/templates/machinery_maint/pmbmmaster/_pmbmmaster_form.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} P/BM Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}<p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>{% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File:** `machinery_maint/templates/machinery_maint/pmbmmaster/confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the P/BM record for <strong>{{ object.machine.item_master.manf_desc }} ({{ object.get_pmbm_type_display }})</strong>?</p>
    <form hx-post="{% url 'pmbmmaster_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File:** `machinery_maint/templates/machinery_maint/pmbmmaster/report_detail.html` (Specific to PMBM_Print_Details.aspx)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Preventive / Breakdown Maintenance Details</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
        <div>
            <p><strong>Record ID:</strong> {{ report_details.id }}</p>
            <p><strong>System Date:</strong> {{ report_details.sys_date|date:"d M, Y" }}</p>
            <p><strong>Type:</strong> {{ report_details.pmbm_type_display }}</p>
            <p><strong>From Date:</strong> {{ report_details.from_date|date:"d M, Y" }}</p>
            <p><strong>To Date:</strong> {{ report_details.to_date|date:"d M, Y" }}</p>
            <p><strong>From Time:</strong> {{ report_details.from_time }}</p>
            <p><strong>To Time:</strong> {{ report_details.to_time }}</p>
            <p><strong>Next PM Due:</strong> {{ report_details.next_pm_due_on|default:"N/A"|date:"d M, Y" }}</p>
            <p><strong>Name of Agency:</strong> {{ report_details.name_of_agency|default:"N/A" }}</p>
            <p><strong>Name of Engineer:</strong> {{ report_details.name_of_engineer|default:"N/A" }}</p>
        </div>
        <div>
            <p><strong>Machine Name:</strong> {{ report_details.item_name }}</p>
            <p><strong>Machine Code:</strong> {{ report_details.machine_code }}</p>
            <p><strong>Model:</strong> {{ report_details.model }}</p>
            <p><strong>Make:</strong> {{ report_details.make }}</p>
            <p><strong>Capacity:</strong> {{ report_details.capacity }}</p>
            <p><strong>Location:</strong> {{ report_details.location }}</p>
            <p><strong>UOM Basic:</strong> {{ report_details.uom_basic_symbol }}</p>
            <p><strong>Company Address:</strong> {{ company_address|linebreaksbr }}</p>
        </div>
    </div>
    <div class="mt-4">
        <p class="text-sm text-gray-700"><strong>Remarks:</strong> {{ report_details.remarks|default:"N/A" }}</p>
    </div>

    <div class="mt-6 flex items-center justify-end">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Close
        </button>
        <!-- A print button could be added here if client-side printing is desired -->
        <!-- <button onclick="window.print()" class="ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Print</button> -->
    </div>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for all the views.

**File:** `machinery_maint/urls.py`

```python
from django.urls import path
from .views import (
    PmbmMasterListView, PmbmMasterCreateView, PmbmMasterUpdateView, 
    PmbmMasterDeleteView, PmbmMasterTablePartialView, PmbmMasterReportDetailView
)

urlpatterns = [
    # Standard P/BM Master CRUD URLs
    path('pmbm_master/', PmbmMasterListView.as_view(), name='pmbmmaster_list'),
    path('pmbm_master/table/', PmbmMasterTablePartialView.as_view(), name='pmbmmaster_table'),
    path('pmbm_master/add/', PmbmMasterCreateView.as_view(), name='pmbmmaster_add'),
    path('pmbm_master/edit/<int:pk>/', PmbmMasterUpdateView.as_view(), name='pmbmmaster_edit'),
    path('pmbm_master/delete/<int:pk>/', PmbmMasterDeleteView.as_view(), name='pmbmmaster_delete'),

    # Specific URL for the PMBM Print Details report
    path('pmbm_master/report/<int:pk>/', PmbmMasterReportDetailView.as_view(), name='pmbmmaster_report_detail'),
]

```

### 4.6 Tests

**Task:** Write comprehensive unit tests for the models and integration tests for the views.

**File:** `machinery_maint/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from .models import (
    Company, FinancialYear, UnitMaster, CategoryMaster, 
    ItemMaster, MachineMaster, PmbmMaster
)

class ModelSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for all tests
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St.')
        cls.fin_year = FinancialYear.objects.create(id=2023, year_name='2023-2024')
        cls.unit = UnitMaster.objects.create(id=1, symbol='NOS')
        cls.category = CategoryMaster.objects.create(id=1, category_name='Machinery')
        cls.item = ItemMaster.objects.create(
            id=1, manf_desc='Excavator', uom_basic=cls.unit, item_code='EXC001', 
            category=cls.category, company=cls.company
        )
        cls.machine = MachineMaster.objects.create(
            id=1, item_master=cls.item, company=cls.company, 
            financial_year=cls.fin_year, model_name='Cat 320', make='Caterpillar', 
            capacity='20 Tons', location='Site A'
        )
        cls.pmbm_record = PmbmMaster.objects.create(
            id=1, sys_date=date(2023, 1, 10), company=cls.company, 
            machine=cls.machine, pmbm_type=0, from_date=date(2023, 1, 15), 
            to_date=date(2023, 1, 15), from_time='09:00', to_time='17:00',
            name_of_agency='ABC Services', name_of_engineer='John Doe', 
            next_pm_due_on=date(2024, 1, 15), remarks='Routine checkup'
        )

class PmbmMasterModelTest(ModelSetupMixin, TestCase):
    def test_pmbmmaster_creation(self):
        record = PmbmMaster.objects.get(id=1)
        self.assertEqual(record.sys_date, date(2023, 1, 10))
        self.assertEqual(record.company, self.company)
        self.assertEqual(record.machine, self.machine)
        self.assertEqual(record.get_pmbm_type_display(), 'Preventive')
        self.assertEqual(record.name_of_engineer, 'John Doe')

    def test_pmbm_type_display(self):
        record = PmbmMaster.objects.get(id=1)
        self.assertEqual(record.get_pmbm_type_display(), 'Preventive')
        
        # Create a breakdown record
        PmbmMaster.objects.create(
            id=2, sys_date=date(2023, 2, 10), company=self.company, 
            machine=self.machine, pmbm_type=1, from_date=date(2023, 2, 15), 
            to_date=date(2023, 2, 15), from_time='10:00', to_time='12:00',
            name_of_agency='XYZ Repairs', name_of_engineer='Jane Smith', 
            remarks='Engine failure'
        )
        breakdown_record = PmbmMaster.objects.get(id=2)
        self.assertEqual(breakdown_record.get_pmbm_type_display(), 'Breakdown')

    def test_get_report_details_method(self):
        details = PmbmMaster.get_report_details(
            self.pmbm_record.id, 
            self.company.id, 
            self.fin_year.id
        )
        self.assertIsNotNone(details)
        self.assertEqual(details['id'], self.pmbm_record.id)
        self.assertEqual(details['item_name'], 'Excavator')
        self.assertEqual(details['machine_code'], 'EXC001')
        self.assertEqual(details['pmbm_type_display'], 'Preventive')
        self.assertEqual(details['company_address'], '123 Test St.')

    def test_get_report_details_not_found(self):
        details = PmbmMaster.get_report_details(999, self.company.id, self.fin_year.id)
        self.assertIsNone(details)

    def test_get_report_details_wrong_company_or_finyear(self):
        details = PmbmMaster.get_report_details(self.pmbm_record.id, 999, self.fin_year.id) # Wrong company
        self.assertIsNone(details)
        details = PmbmMaster.get_report_details(self.pmbm_record.id, self.company.id, 999) # Wrong fin year
        self.assertIsNone(details)


class PmbmMasterViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Simulate a logged-in user and session data
        # For actual authentication, use Django's auth system to log in a user
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.fin_year.id

        # Mock a logged-in user for LoginRequiredMixin (simplistic, for testing)
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(username='testuser', password='password')
        self.client.force_login(self.user)

    def test_list_view_get(self):
        response = self.client.get(reverse('pmbmmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maint/pmbmmaster/list.html')
        self.assertIn('pmbmmaster_records', response.context)
        self.assertContains(response, 'P/BM Master Records')

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('pmbmmaster_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maint/pmbmmaster/_pmbmmaster_table.html')
        self.assertIn('pmbmmaster_records', response.context)
        self.assertContains(response, 'Excavator') # Check for sample data

    def test_create_view_get(self):
        response = self.client.get(reverse('pmbmmaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maint/pmbmmaster/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post(self):
        initial_count = PmbmMaster.objects.count()
        data = {
            'sys_date': '2023-03-01',
            'company': self.company.id, 
            'machine': self.machine.id, 
            'pmbm_type': 0, # Preventive
            'from_date': '2023-03-05',
            'to_date': '2023-03-05',
            'from_time': '08:00',
            'to_time': '16:00',
            'name_of_agency': 'New Agency',
            'name_of_engineer': 'Alice Smith',
            'next_pm_due_on': '2024-03-05',
            'remarks': 'New record for testing',
        }
        response = self.client.post(reverse('pmbmmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(PmbmMaster.objects.count(), initial_count + 1)
        self.assertTrue(PmbmMaster.objects.filter(name_of_agency='New Agency').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('pmbmmaster_edit', args=[self.pmbm_record.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maint/pmbmmaster/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.pmbm_record)

    def test_update_view_post(self):
        updated_remarks = "Updated routine checkup."
        data = {
            'sys_date': self.pmbm_record.sys_date.isoformat(),
            'company': self.company.id, 
            'machine': self.machine.id, 
            'pmbm_type': self.pmbm_record.pmbm_type, 
            'from_date': self.pmbm_record.from_date.isoformat(),
            'to_date': self.pmbm_record.to_date.isoformat(),
            'from_time': self.pmbm_record.from_time,
            'to_time': self.pmbm_record.to_time,
            'name_of_agency': self.pmbm_record.name_of_agency,
            'name_of_engineer': self.pmbm_record.name_of_engineer,
            'next_pm_due_on': self.pmbm_record.next_pm_due_on.isoformat(),
            'remarks': updated_remarks,
        }
        response = self.client.post(reverse('pmbmmaster_edit', args=[self.pmbm_record.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.pmbm_record.refresh_from_db()
        self.assertEqual(self.pmbm_record.remarks, updated_remarks)

    def test_delete_view_get(self):
        response = self.client.get(reverse('pmbmmaster_delete', args=[self.pmbm_record.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maint/pmbmmaster/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.pmbm_record)

    def test_delete_view_post(self):
        initial_count = PmbmMaster.objects.count()
        response = self.client.post(reverse('pmbmmaster_delete', args=[self.pmbm_record.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(PmbmMaster.objects.count(), initial_count - 1)
        self.assertFalse(PmbmMaster.objects.filter(id=self.pmbm_record.id).exists())

    def test_report_detail_view_get(self):
        response = self.client.get(reverse('pmbmmaster_report_detail', args=[self.pmbm_record.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maint/pmbmmaster/report_detail.html')
        self.assertIn('report_details', response.context)
        self.assertEqual(response.context['report_details']['id'], self.pmbm_record.id)
        self.assertContains(response, 'Preventive / Breakdown Maintenance Details')
        self.assertContains(response, 'Excavator') # Check for aggregated data

    def test_report_detail_view_not_found(self):
        response = self.client.get(reverse('pmbmmaster_report_detail', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404)

    def test_login_required(self):
        self.client.logout() # Ensure user is logged out
        response = self.client.get(reverse('pmbmmaster_list'))
        self.assertEqual(response.status_code, 302) # Should redirect to login
        self.assertIn('/accounts/login/', response.url) # Assuming default login URL
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX for CRUD Modals:** All create, edit, and delete actions load their forms into a shared modal (`#modalContent`) via HTMX GET requests. Upon successful form submission (POST), HTMX triggers a `204 No Content` response with `HX-Trigger: refreshPmbmMasterList`, which tells the list view to re-fetch its data (`hx-get="{% url 'pmbmmaster_table' %}"`) and update the table without a full page refresh.
*   **HTMX for Report Details:** The "View Report" button on the list also uses HTMX to load the `report_detail.html` into the same modal, providing a quick view of the report without leaving the list page.
*   **DataTables Integration:** The `_pmbmmaster_table.html` partial is dynamically loaded. Once loaded, a small JavaScript snippet initializes DataTables, providing client-side search, sort, and pagination.
*   **Alpine.js for Modal State:** An `x-data` attribute (not explicitly shown in the template but implied by the `_` syntax) on the `#modal` element would manage its visibility. The `on click add .is-active to #modal` and `remove .is-active from me` (or `on click if event.target.id == 'modal' remove .is-active from me`) along with Tailwind's `hidden` class are core Alpine.js patterns for managing modal state.
*   **DRY Templates:** The `core/base.html` (not included as per instructions) is assumed to contain the basic HTML structure, CDN links for HTMX, Alpine.js, jQuery, and DataTables, and common styling. This ensures all components inherit consistent styling and functionality.

## Final Notes

*   **Session Management:** The original ASP.NET code relies on `Session["compid"]` and `Session["finyear"]`. In Django, these would typically be derived from the authenticated user's profile or custom session management. The provided views assume these are available either directly in the session or can be retrieved from the current user.
*   **Error Handling:** The `PmbmMaster.get_report_details` method includes basic `try-except` for `DoesNotExist` and general exceptions. Robust production systems would require more specific error logging and user feedback mechanisms.
*   **Security:** `LoginRequiredMixin` has been added to all views, ensuring only authenticated users can access the functionality. Further authorization (e.g., role-based access to specific companies or financial years) would be implemented in a real application, possibly using Django Guardian or custom permission classes.
*   **Date/Time Handling:** The ASP.NET code uses `fun.FromDateDMY` which implies specific date formatting. Django's `DateField` and `DateTimeField` handle this automatically, and templates use filters like `|date:"d M, Y"` for display. `FromTime` and `ToTime` are kept as `CharField` due to unknown exact format from source, could be `TimeField` if standard time string.
*   **`fun.CompAdd`:** This utility method is simulated by fetching the `address` field from the `Company` model linked to the `PmbmMaster` record.
*   **Scalability:** The `get_report_details` method uses `select_related` for efficient fetching of related foreign key data, reducing database queries, which is crucial for performance.