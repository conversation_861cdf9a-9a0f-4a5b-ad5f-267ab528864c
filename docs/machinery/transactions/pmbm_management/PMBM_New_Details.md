## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a modernization plan to transition your legacy ASP.NET application, specifically the "Preventive / Breakdown Maintenance - New" details page, to a modern Django-based solution. Our approach prioritizes automation, leverages contemporary web technologies like HTMX and Alpine.js, and adheres to best practices for maintainable and scalable applications.

### Business Benefits of Django Modernization:

*   **Improved Scalability & Performance:** Django's robust architecture and efficient ORM (Object-Relational Mapper) can handle increased user loads and process data more effectively than legacy ASP.NET Web Forms.
*   **Reduced Development Costs:** Python's readability and Django's "batteries-included" philosophy lead to faster development cycles and easier maintenance, lowering long-term operational expenses.
*   **Enhanced User Experience:** By adopting HTMX and Alpine.js, the application will provide a highly responsive, app-like feel without complex JavaScript, leading to smoother interactions and higher user satisfaction.
*   **Modern Technology Stack:** Moving to Django, Python, and a modern frontend (HTMX/Alpine.js) future-proofs your application, making it easier to integrate with other services and attract skilled developers.
*   **Simplified Maintenance:** The "Fat Model, Thin View" architecture ensures business logic is centralized and easily testable, leading to fewer bugs and quicker feature implementations.
*   **Actionable Insights:** Integration with DataTables provides powerful client-side data manipulation, allowing users to quickly find and analyze information.

---

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code-behind, we've identified several key database tables involved in displaying machine details, listing spare parts, and recording maintenance events.

**Identified Tables and Inferred Columns:**

*   **`tblDG_Item_Master` (Django Model: `DGItemMaster`)**:
    *   `Id` (Primary Key)
    *   `ItemCode` (string)
    *   `ManfDesc` (string - Machine Name, Spare Description)
    *   `UOMBasic` (integer/FK - Unit of Measure)
    *   `StockQty` (decimal - Current Stock Quantity)
    *   `CId` (integer/FK - Category ID, used in ASP.NET query)
    *   `CompId` (integer - Company ID)

*   **`Unit_Master` (Django Model: `UnitMaster`)**:
    *   `Id` (Primary Key)
    *   `Symbol` (string - Unit Symbol like "PCS", "KG")

*   **`tblMS_Master` (Django Model: `Machine`)**:
    *   `Id` (Primary Key - This is `MasterId` in query string)
    *   `ItemId` (integer/FK to `tblDG_Item_Master.Id` - This is `ItemId` in query string, representing the machine type)
    *   `Model` (string)
    *   `Make` (string)
    *   `Capacity` (string)
    *   `Location` (string)
    *   `CompId` (integer - Company ID)
    *   `FinYearId` (integer - Financial Year ID)

*   **`tblMS_Spares` (Django Model: `MachinePlannedSpare`)**:
    *   `Id` (Primary Key)
    *   `MId` (integer/FK to `tblMS_Master.Id` - `Machine.id`)
    *   `ItemId` (integer/FK to `tblDG_Item_Master.Id` - the spare part item ID)
    *   `Qty` (decimal - Required Quantity of the spare for this machine)

*   **`tblMS_PMBM_Master` (Django Model: `MaintenanceEntry`)**:
    *   `Id` (Primary Key)
    *   `SysDate` (date - System Date of creation)
    *   `SysTime` (time - System Time of creation)
    *   `CompId` (integer - Company ID)
    *   `FinYearId` (integer - Financial Year ID)
    *   `SessionId` (string - User Session ID/Username)
    *   `MachineId` (integer/FK to `tblMS_Master.Id` - the specific machine instance)
    *   `PMBM` (integer - 0 for Preventive, 1 for Breakdown Maintenance)
    *   `FromDate` (date)
    *   `ToDate` (date)
    *   `FromTime` (string - format 'HH:MM:SS AM/PM')
    *   `ToTime` (string - format 'HH:MM:SS AM/PM')
    *   `NameOfAgency` (string)
    *   `NameOfEngineer` (string)
    *   `NextPMDueOn` (date)
    *   `Remarks` (text)

*   **`tblMS_PMBM_Details` (Django Model: `MaintenanceSpareUsed`)**:
    *   `Id` (Primary Key - likely auto-incremented)
    *   `MId` (integer/FK to `tblMS_PMBM_Master.Id` - `MaintenanceEntry.id`)
    *   `SpareId` (integer/FK to `tblDG_Item_Master.Id` - the specific spare part item ID)
    *   `Qty` (decimal - Availed Quantity of the spare)

### Step 2: Identify Backend Functionality

The ASP.NET page `PMBM_New_Details.aspx` primarily performs a **Create** operation for a new Preventive/Breakdown Maintenance record, along with associated spare parts usage. It also involves **Read** operations to display machine details and a list of planned spare parts.

*   **Read Operations:**
    *   Fetching details of a specific machine (`Machine`) using `MId` and `ItemId` from the URL.
    *   Fetching a list of planned spare parts (`MachinePlannedSpare`) associated with that machine, along with their `DGItemMaster` details and `UnitMaster` symbols.
*   **Create Operations:**
    *   Creating a new `MaintenanceEntry` record with all the main form fields.
    *   Conditionally creating multiple `MaintenanceSpareUsed` records based on user selection (checkbox) and input (Availed Quantity) from the spares grid.
*   **Validation Logic:**
    *   Required fields for dates, times, agency, engineer, next PM due, remarks.
    *   Date format validation (`dd-MM-yyyy`).
    *   Numeric validation for 'Availed Qty' for selected spare parts.
    *   Client-side confirmation before "Proceed" (will be handled by Alpine.js/HTMX confirmation).

### Step 3: Infer UI Components

The ASP.NET controls map directly to standard HTML form elements and a data table structure.

*   **Labels (`<asp:Label>`):** Will be rendered as plain text in Django templates.
*   **Dropdown (`<asp:DropDownList>`):** HTML `<select>` element.
*   **Textboxes (`<asp:TextBox>`):** HTML `<input type="text">`, `<input type="date">`, `<input type="time">`, `<textarea>`.
*   **Date/Time Extenders (`<cc1:CalendarExtender>`, `<MKB:TimeSelector>`):** Will be replaced by HTML5 date/time inputs or a modern JS library like Flatpickr/Alpine.js for enhanced UX, integrated via HTMX.
*   **Validation Controls (`<asp:RequiredFieldValidator>`, `<asp:RegularExpressionValidator>`):** Handled by Django's form validation and error display in the template.
*   **GridView (`<asp:GridView>`):** Will be rendered as an HTML `<table>` and enhanced with DataTables.js for client-side features. Checkboxes and textboxes within the grid will be standard HTML inputs.
*   **Buttons (`<asp:Button>`):** HTML `<button type="submit">` or `<button type="button">`.

### Step 4: Generate Django Code

We will structure the Django application within a module named `machinery_maintenance`.

#### 4.1 Models (`machinery_maintenance/models.py`)

This file defines the Django ORM models, mapping directly to your existing database tables. `managed = False` tells Django not to manage table creation/alteration, as they already exist.

```python
from django.db import models
from django.utils import timezone

# Choices for Maintenance Type
MAINTENANCE_TYPE_CHOICES = [
    (0, 'Preventive'),
    (1, 'Breakdown'),
]

class DGItemMaster(models.Model):
    """
    Corresponds to tblDG_Item_Master.
    Represents general items, including machine types and spare parts.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=250, blank=True, null=True) # Machine Name / Spare Description
    uom_basic = models.ForeignKey('UnitMaster', models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    # CId and CompId inferred, but not directly used in the current transaction details
    # cid = models.IntegerField(db_column='CId', blank=True, null=True)
    # comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class UnitMaster(models.Model):
    """
    Corresponds to Unit_Master.
    Represents units of measure.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class Machine(models.Model):
    """
    Corresponds to tblMS_Master.
    Represents a specific machine instance.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # MId from query string
    item_master = models.ForeignKey(DGItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True) # ItemId from query string
    model = models.CharField(db_column='Model', max_length=100, blank=True, null=True)
    make = models.CharField(db_column='Make', max_length=100, blank=True, null=True)
    capacity = models.CharField(db_column='Capacity', max_length=100, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machine'
        verbose_name_plural = 'Machines'

    def __str__(self):
        return f"{self.item_master.manf_desc} ({self.item_master.item_code})" if self.item_master else "Machine"

class MachinePlannedSpare(models.Model):
    """
    Corresponds to tblMS_Spares.
    Represents spare parts planned/required for a specific machine.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    machine = models.ForeignKey(Machine, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    spare_item = models.ForeignKey(DGItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4, blank=True, null=True) # Required Qty

    class Meta:
        managed = False
        db_table = 'tblMS_Spares'
        verbose_name = 'Machine Planned Spare'
        verbose_name_plural = 'Machine Planned Spares'

    def __str__(self):
        return f"{self.machine.item_master.manf_desc if self.machine else 'N/A'} - {self.spare_item.manf_desc if self.spare_item else 'N/A'}"

class MaintenanceEntry(models.Model):
    """
    Corresponds to tblMS_PMBM_Master.
    Represents a Preventive or Breakdown Maintenance event.
    Includes business logic for saving maintenance details and related spares.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # User Session ID
    machine = models.ForeignKey(Machine, models.DO_NOTHING, db_column='MachineId', blank=True, null=True)
    maintenance_type = models.IntegerField(db_column='PMBM', choices=MAINTENANCE_TYPE_CHOICES, blank=True, null=True)
    from_date = models.DateField(db_column='FromDate', blank=True, null=True)
    to_date = models.DateField(db_column='ToDate', blank=True, null=True)
    from_time = models.CharField(db_column='FromTime', max_length=20, blank=True, null=True) # Stored as string 'HH:MM:SS AM/PM'
    to_time = models.CharField(db_column='ToTime', max_length=20, blank=True, null=True) # Stored as string 'HH:MM:SS AM/PM'
    agency_name = models.CharField(db_column='NameOfAgency', max_length=250, blank=True, null=True)
    engineer_name = models.CharField(db_column='NameOfEngineer', max_length=250, blank=True, null=True)
    next_pm_due_on = models.DateField(db_column='NextPMDueOn', blank=True, null=True)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Master'
        verbose_name = 'Maintenance Entry'
        verbose_name_plural = 'Maintenance Entries'

    def __str__(self):
        return f"Maintenance for {self.machine} on {self.sys_date}"

    def save_with_details(self, user_id, comp_id, fin_year_id, machine_id, spared_data):
        """
        Business logic to save the MaintenanceEntry and its associated spare parts.
        This method replaces the logic in btnProceed_Click.
        """
        self.sys_date = timezone.localdate()
        self.sys_time = timezone.localtime().time()
        self.session_id = user_id
        self.comp_id = comp_id
        self.fin_year_id = fin_year_id
        self.machine_id = machine_id # Ensure machine FK is set
        self.save() # Save the main entry first to get an ID

        # Process spare parts details
        for spare_id, availed_qty_str in spared_data.items():
            if availed_qty_str: # Only save if quantity is provided
                try:
                    # ASP.NET code converts to double, so we'll use DecimalField in Django for consistency
                    availed_qty = models.DecimalField().to_python(availed_qty_str)
                    MaintenanceSpareUsed.objects.create(
                        maintenance_entry=self,
                        spare_item_id=spare_id,
                        qty=availed_qty
                    )
                except Exception as e:
                    # Log or handle error for invalid quantity, similar to ASP.NET's k++ logic
                    print(f"Error saving spare {spare_id}: {e}")
                    # In a real app, you might roll back the transaction or flag the error.
                    # For this migration, we mirror the 'k>0' logic in the view's success handling.

class MaintenanceSpareUsed(models.Model):
    """
    Corresponds to tblMS_PMBM_Details.
    Represents spare parts actually used in a MaintenanceEntry.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Auto-incremented
    maintenance_entry = models.ForeignKey(MaintenanceEntry, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    spare_item = models.ForeignKey(DGItemMaster, models.DO_NOTHING, db_column='SpareId', blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4, blank=True, null=True) # Availed Qty

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Details'
        verbose_name = 'Maintenance Spare Used'
        verbose_name_plural = 'Maintenance Spares Used'

    def __str__(self):
        return f"{self.spare_item.manf_desc if self.spare_item else 'N/A'} - {self.qty}"
```

#### 4.2 Forms (`machinery_maintenance/forms.py`)

This defines the form for the main maintenance entry. Date and Time fields will use appropriate widgets for HTML5 input types.

```python
from django import forms
from .models import MaintenanceEntry, MAINTENANCE_TYPE_CHOICES
import re

class MaintenanceEntryForm(forms.ModelForm):
    """
    Form for creating/updating a MaintenanceEntry.
    Includes validation logic from the original ASP.NET code.
    """
    from_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-full'}, format='%Y-%m-%d'),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'], # Allow multiple input formats
        required=True
    )
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-full'}, format='%Y-%m-%d'),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'],
        required=True
    )
    next_pm_due_on = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-full'}, format='%Y-%m-%d'),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'],
        required=True
    )
    from_time = forms.CharField( # Storing as CharField to match DB format 'HH:MM:SS AM/PM'
        widget=forms.TextInput(attrs={'type': 'time', 'class': 'box3 w-full'}),
        required=True
    )
    to_time = forms.CharField( # Storing as CharField to match DB format 'HH:MM:SS AM/PM'
        widget=forms.TextInput(attrs={'type': 'time', 'class': 'box3 w-full'}),
        required=True
    )

    class Meta:
        model = MaintenanceEntry
        fields = [
            'maintenance_type', 'from_date', 'to_date', 'from_time', 'to_time',
            'agency_name', 'engineer_name', 'next_pm_due_on', 'remarks'
        ]
        widgets = {
            'maintenance_type': forms.Select(attrs={'class': 'box3 w-full'}),
            'agency_name': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'engineer_name': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'remarks': forms.Textarea(attrs={'class': 'box3 w-full', 'rows': 4}),
        }
        labels = {
            'maintenance_type': 'Maintenance',
            'agency_name': 'Name of Agency',
            'engineer_name': 'Name of Engineer',
            'next_pm_due_on': 'Next PM Due on',
        }

    def clean(self):
        cleaned_data = super().clean()
        # Custom validation for dates (can be more sophisticated if needed, e.g., to_date >= from_date)
        # ASP.NET only checked format and required, which Django's DateField already handles.
        # Times are also handled by CharField with time input type.
        return cleaned_data

    def _convert_time_to_ampm_string(self, time_obj):
        """Converts datetime.time object to 'HH:MM:SS AM/PM' string."""
        if isinstance(time_obj, str): # If it's already a string from a failed validation perhaps
            return time_obj
        if time_obj:
            return time_obj.strftime("%I:%M:%S %p")
        return ""
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        # Convert time objects to the specific string format before saving
        if 'from_time' in self.cleaned_data:
            instance.from_time = self._convert_time_to_ampm_string(self.cleaned_data['from_time'])
        if 'to_time' in self.cleaned_data:
            instance.to_time = self._convert_time_to_ampm_string(self.cleaned_data['to_time'])
        
        if commit:
            instance.save()
        return instance

class SparePartForm(forms.Form):
    """
    A placeholder form for a single spare part row, to handle the Availed Qty.
    This would typically be part of a formset if more complex interactions were needed.
    For this migration, we'll manually process these fields in the view.
    """
    is_checked = forms.BooleanField(required=False, widget=forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}))
    availed_qty = forms.DecimalField(
        required=False,
        max_digits=18,
        decimal_places=4,
        widget=forms.NumberInput(attrs={'class': 'box3 w-full', 'placeholder': 'Availed Qty'}),
        min_value=0 # Qty should be non-negative
    )
    spare_id = forms.IntegerField(widget=forms.HiddenInput()) # To identify which spare part this row is for

    def clean(self):
        cleaned_data = super().clean()
        is_checked = cleaned_data.get('is_checked')
        availed_qty = cleaned_data.get('availed_qty')

        if is_checked and (availed_qty is None or availed_qty == ''):
            self.add_error('availed_qty', 'This field is required if spare is checked.')
        elif not is_checked and (availed_qty is not None and availed_qty != ''):
            # Optional: warn if qty is entered but not checked, or clear it
            pass
        return cleaned_data
```

#### 4.3 Views (`machinery_maintenance/views.py`)

This file contains the logic for rendering the page and handling form submissions. We'll use a `CreateView` and a `TemplateView` for the partial data.

```python
from django.views.generic import CreateView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.db import transaction

from .models import MaintenanceEntry, Machine, DGItemMaster, MachinePlannedSpare, MaintenanceSpareUsed
from .forms import MaintenanceEntryForm, SparePartForm

# Helper to fetch machine details (Fat Model - part of business logic)
class MachineService:
    @staticmethod
    def get_machine_details(item_id, master_id, comp_id, fin_year_id):
        try:
            # Replicates the ASP.NET logic for fetching machine details
            # Assumes item_id is DGItemMaster.Id and master_id is Machine.Id
            machine = get_object_or_404(
                Machine.objects.select_related('item_master'),
                id=master_id,
                item_master__id=item_id,
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id # Assuming FinYearId check
            )
            return machine
        except Exception:
            return None # Handle case where machine is not found

    @staticmethod
    def get_planned_spares_for_machine(machine_id, comp_id):
        # Replicates LoadDataSpareMaster() logic
        # Gets spares planned for this specific machine, not just item_id
        planned_spares = MachinePlannedSpare.objects.filter(machine_id=machine_id)\
                                                 .select_related('spare_item', 'spare_item__uom_basic')\
                                                 .order_by('-id') # Order By Id Desc as in ASP.NET

        # Augment with StockQty and ItemCode/ManfDesc from DGItemMaster for display
        spare_data = []
        for ps in planned_spares:
            spare_data.append({
                'id': ps.id,
                'item_id': ps.spare_item.id, # The DGItemMaster.Id for the spare
                'item_code': ps.spare_item.item_code,
                'manf_desc': ps.spare_item.manf_desc,
                'uom_basic_symbol': ps.spare_item.uom_basic.symbol if ps.spare_item.uom_basic else '',
                'stock_qty': ps.spare_item.stock_qty,
                'required_qty': ps.qty, # This is lblQty in ASP.NET
                'availed_qty_form_field_name': f'availed_qty_{ps.spare_item.id}', # Unique name for each input
                'is_checked_form_field_name': f'is_checked_{ps.spare_item.id}',
            })
        return spare_data

class MaintenanceEntryCreateView(CreateView):
    """
    Handles the display and creation of a new Maintenance Entry.
    Combines the machine details display with the maintenance form and spares grid.
    """
    model = MaintenanceEntry
    form_class = MaintenanceEntryForm
    template_name = 'machinery_maintenance/maintenanceentry/create.html'
    success_url = reverse_lazy('maintenanceentry_list') # Redirect to a list view after success

    def get_initial(self):
        initial = super().get_initial()
        # Set initial values for dropdown if needed, though form handles choices
        initial['maintenance_type'] = 0 # Default to Preventive
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        machine_id = self.kwargs.get('master_id')
        item_id = self.kwargs.get('item_id')

        # Dummy session data for migration. In a real app, use request.user.profile etc.
        # The ASP.NET app got CompId, FinYearId, SessionId from Session variables.
        # We'll use mock values for demonstration; replace with actual user/company logic.
        mock_comp_id = 1 # Replace with actual logic for current company ID
        mock_fin_year_id = 2023 # Replace with actual logic for current financial year ID
        mock_session_id = 'testuser' # Replace with request.user.username

        context['machine'] = MachineService.get_machine_details(item_id, machine_id, mock_comp_id, mock_fin_year_id)
        if context['machine']:
            context['planned_spares'] = MachineService.get_planned_spares_for_machine(machine_id, mock_comp_id)
        else:
            context['planned_spares'] = []
            messages.error(self.request, "Machine details could not be loaded. Please ensure correct Item ID and Master ID.")

        context['mock_comp_id'] = mock_comp_id
        context['mock_fin_year_id'] = mock_fin_year_id
        context['mock_session_id'] = mock_session_id
        return context

    def form_valid(self, form):
        # This method handles the main form and then the dynamically provided spare parts data.
        # It replicates the btnProceed_Click logic.
        machine_id = self.kwargs.get('master_id')
        # Get session/company/finyear data as done in get_context_data
        mock_comp_id = 1
        mock_fin_year_id = 2023
        mock_session_id = 'testuser'

        spared_data_from_post = {}
        invalid_spares_count = 0

        # Process the spare parts from the POST data
        # We look for inputs named 'availed_qty_<spare_item_id>' and 'is_checked_<spare_item_id>'
        for key, value in self.request.POST.items():
            if key.startswith('availed_qty_'):
                # Extract the spare_item.id (DGItemMaster.Id) from the key
                spare_item_id = key.split('availed_qty_')[1]
                is_checked_field = f'is_checked_{spare_item_id}'

                if self.request.POST.get(is_checked_field) == 'on': # Check if the checkbox was ticked
                    # Validate the quantity if checked
                    try:
                        # Use DecimalField's to_python for robust numeric conversion
                        # This also acts as a validation against non-numeric inputs
                        availed_qty = forms.DecimalField(required=True).to_python(value)
                        spared_data_from_post[spare_item_id] = availed_qty
                    except forms.ValidationError:
                        invalid_spares_count += 1
                        messages.error(self.request, f"Invalid quantity for spare part (ID: {spare_item_id}).")
                # ASP.NET allowed entering quantity without checking checkbox, but only processed if checked.
                # Here, we only process if checked. If not checked, we ignore it.
                # If quantity is entered but not checked, it's simply ignored.

        if invalid_spares_count > 0:
            # If any spare quantity is invalid for a checked item, re-render form with errors
            messages.error(self.request, "Invalid data found in spare parts section. Please correct the highlighted errors.")
            # Re-populating form fields might be complex; simplest is to re-render.
            # To show specific errors per spare, you would need a formset or custom error handling per field.
            return self.form_invalid(form) # Re-render the form with errors

        try:
            with transaction.atomic():
                # Save the main MaintenanceEntry using the model method
                # This ensures SysDate, SysTime, CompId, FinYearId, SessionId are set.
                maintenance_entry_instance = form.save(commit=False) # Get instance but don't save yet
                maintenance_entry_instance.save_with_details(
                    user_id=mock_session_id,
                    comp_id=mock_comp_id,
                    fin_year_id=mock_fin_year_id,
                    machine_id=machine_id,
                    spared_data=spared_data_from_post
                )

            messages.success(self.request, "Preventive/Breakdown Maintenance entry created successfully.")
            # HTMX response for success, triggering a redirect or UI update
            if self.request.headers.get('HX-Request'):
                # Redirect to the list view (e.g., PMBM_New.aspx)
                return HttpResponse(
                    status=204, # No content
                    headers={
                        'HX-Redirect': reverse_lazy('maintenanceentry_list') # Redirect using HX-Redirect header
                    }
                )
            return super().form_valid(form) # Standard redirect
        except Exception as e:
            messages.error(self.request, f"An error occurred while saving the maintenance entry: {e}")
            return self.form_invalid(form) # Re-render form with errors

class SparePartsTablePartialView(TemplateView):
    """
    A view to render only the spare parts table for HTMX loading.
    """
    template_name = 'machinery_maintenance/maintenanceentry/_spare_parts_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        machine_id = self.kwargs.get('master_id')
        item_id = self.kwargs.get('item_id') # Not directly used for spares list, but good to have if context matters

        # Dummy session data as above
        mock_comp_id = 1
        mock_fin_year_id = 2023

        machine = MachineService.get_machine_details(item_id, machine_id, mock_comp_id, mock_fin_year_id)
        if machine:
            context['planned_spares'] = MachineService.get_planned_spares_for_machine(machine_id, mock_comp_id)
        else:
            context['planned_spares'] = []
            # Note: Messages here won't show on initial HTMX load without specific setup.
            # Error handling for missing machine is better in the main CreateView.
        return context

# Dummy view for success redirect target
class MaintenanceEntryListView(TemplateView):
    template_name = 'machinery_maintenance/maintenanceentry/list.html' # This should be your actual list page

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['maintenance_entries'] = MaintenanceEntry.objects.all().select_related('machine', 'machine__item_master') # Example for a list view
        return context
```

#### 4.4 Templates (`machinery_maintenance/templates/machinery_maintenance/maintenanceentry/`)

The templates are structured to follow DRY principles, with a main page and HTMX-loaded partials.

**`create.html` (Main page for creating new maintenance entry)**

```html
{% extends 'core/base.html' %}

{% block title %}New Maintenance Entry{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Preventive / Breakdown Maintenance - New</h2>
    </div>

    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="{{ message.tags }} p-3 rounded-md text-sm {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if machine %}
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-800 border-b pb-3 mb-4">Machine Details</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm font-medium text-gray-600">Machine Code:</p>
                <p class="text-lg font-bold text-gray-900">{{ machine.item_master.item_code }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">UOM:</p>
                <p class="text-lg font-bold text-gray-900">{{ machine.item_master.uom_basic.symbol }}</p>
            </div>
            <div class="col-span-1 md:col-span-2">
                <p class="text-sm font-medium text-gray-600">Name:</p>
                <p class="text-lg font-bold text-gray-900">{{ machine.item_master.manf_desc }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Model:</p>
                <p class="text-lg font-bold text-gray-900">{{ machine.model }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Make:</p>
                <p class="text-lg font-bold text-gray-900">{{ machine.make }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Capacity:</p>
                <p class="text-lg font-bold text-gray-900">{{ machine.capacity }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Location:</p>
                <p class="text-lg font-bold text-gray-900">{{ machine.location }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-gray-800 border-b pb-3 mb-4">Maintenance Details</h3>
        <form method="post" hx-post="." hx-swap="outerHTML" hx-target="#mainFormContainer" hx-push-url="false"
              hx-on::after-request="if(event.detail.xhr.status === 204) { window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect'); }">
            {% csrf_token %}
            <div id="mainFormContainer" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.maintenance_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Maintenance</label>
                        {{ form.maintenance_type }}
                        {% if form.maintenance_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.maintenance_type.errors }}</p>{% endif %}
                    </div>
                    <div><!-- Empty cell for alignment, mirroring ASP.NET layout --></div>

                    <div>
                        <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
                        {{ form.from_date }}
                        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
                        {{ form.to_date }}
                        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                    </div>

                    <div>
                        <label for="{{ form.from_time.id_for_label }}" class="block text-sm font-medium text-gray-700">From Time</label>
                        {{ form.from_time }}
                        {% if form.from_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_time.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.to_time.id_for_label }}" class="block text-sm font-medium text-gray-700">To Time</label>
                        {{ form.to_time }}
                        {% if form.to_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_time.errors }}</p>{% endif %}
                    </div>

                    <div>
                        <label for="{{ form.agency_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name of Agency</label>
                        {{ form.agency_name }}
                        {% if form.agency_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.agency_name.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.engineer_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name of Engineer</label>
                        {{ form.engineer_name }}
                        {% if form.engineer_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.engineer_name.errors }}</p>{% endif %}
                    </div>

                    <div>
                        <label for="{{ form.next_pm_due_on.id_for_label }}" class="block text-sm font-medium text-gray-700">Next PM Due on</label>
                        {{ form.next_pm_due_on }}
                        {% if form.next_pm_due_on.errors %}<p class="text-red-500 text-xs mt-1">{{ form.next_pm_due_on.errors }}</p>{% endif %}
                    </div>
                    <div><!-- Empty cell for alignment --></div>

                    <div class="col-span-1 md:col-span-2">
                        <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                        <p class="text-red-500 text-xs mt-1">* PM excluding holiday</p>
                    </div>
                </div>

                <h3 class="text-xl font-semibold text-gray-800 border-b pb-3 mb-4 mt-8">Spare Parts</h3>
                <div id="sparePartsTableContainer"
                     hx-trigger="load"
                     hx-get="{% url 'maintenanceentry_spare_parts_table' master_id=machine.id item_id=machine.item_master.id %}"
                     hx-swap="innerHTML">
                    <!-- The spare parts table will be loaded here via HTMX -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Spare Parts...</p>
                    </div>
                </div>

                <div class="mt-8 flex justify-center space-x-4">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            hx-confirm="Are you sure you want to proceed with this maintenance entry?">
                        Proceed
                    </button>
                    <a href="{% url 'maintenanceentry_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
    {% else %}
    <p class="text-red-500 text-center font-bold text-lg">Error: Machine details could not be loaded. Please check the provided IDs.</p>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup if needed for modals or specific UI state, currently minimal as HTMX drives interactions
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components required here due to HTMX's direct form submission.
        // For date pickers, you might integrate Flatpickr like this:
        // document.querySelectorAll('input[type="date"]').forEach(function(el) {
        //     flatpickr(el, { dateFormat: 'Y-m-d' }); // Format for HTML5 date input
        // });
        // For time pickers:
        // document.querySelectorAll('input[type="time"]').forEach(function(el) {
        //     flatpickr(el, { enableTime: true, noCalendar: true, dateFormat: 'H:i' });
        // });
    });

    // Custom script for DataTables initialization
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'sparePartsTableContainer') {
            $('#sparePartsTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Handle checkboxes and quantity fields similar to ASP.NET's ValidateTextBox
    // This client-side validation is basic, Django's server-side validation is primary.
    document.addEventListener('DOMContentLoaded', function() {
        document.body.addEventListener('change', function(event) {
            if (event.target.matches('input[type="checkbox"][name^="is_checked_"]')) {
                const spareItemId = event.target.name.split('_')[2];
                const qtyInput = document.querySelector(`input[name="availed_qty_${spareItemId}"]`);
                if (qtyInput) {
                    if (event.target.checked) {
                        qtyInput.setAttribute('required', 'required');
                        qtyInput.placeholder = 'Required';
                        qtyInput.focus();
                    } else {
                        qtyInput.removeAttribute('required');
                        qtyInput.placeholder = 'Availed Qty';
                        qtyInput.value = ''; // Clear value if unchecked
                    }
                }
            }
        });
    });
</script>
{% endblock %}
```

**`_spare_parts_table.html` (HTMX partial for the spare parts grid)**

```html
<div class="overflow-x-auto">
    <table id="sparePartsTable" class="min-w-full divide-y divide-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Required Qty</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Availed Qty</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if planned_spares %}
                {% for spare in planned_spares %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-center">
                        <input type="checkbox" name="{{ spare.is_checked_form_field_name }}" class="h-4 w-4 text-indigo-600 border-gray-300 rounded"
                               hx-target="#{{ spare.availed_qty_form_field_name }}_error" hx-swap="outerHTML">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ spare.item_code }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ spare.manf_desc }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{{ spare.uom_basic_symbol }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ spare.stock_qty }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ spare.required_qty }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="number" step="any" name="{{ spare.availed_qty_form_field_name }}" class="box3 w-28" placeholder="Availed Qty">
                        <div id="{{ spare.availed_qty_form_field_name }}_error" class="text-red-500 text-xs mt-1"></div>
                    </td>
                    <input type="hidden" name="spare_item_id_{{ forloop.counter }}" value="{{ spare.item_id }}">
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="8" class="px-6 py-4 whitespace-nowrap text-sm text-center text-maroon font-bold">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>
```

#### 4.5 URLs (`machinery_maintenance/urls.py`)

Defines the URL patterns that map to our views.

```python
from django.urls import path
from .views import MaintenanceEntryCreateView, SparePartsTablePartialView, MaintenanceEntryListView

urlpatterns = [
    # Main page for creating a new maintenance entry
    # URL parameters match ASP.NET query string MId and ItemId
    path('pmbm/new/<int:master_id>/<int:item_id>/', MaintenanceEntryCreateView.as_view(), name='maintenanceentry_create'),

    # HTMX endpoint for loading the spare parts table (partial)
    path('pmbm/spares_table/<int:master_id>/<int:item_id>/', SparePartsTablePartialView.as_view(), name='maintenanceentry_spare_parts_table'),

    # Dummy list view for redirection after successful creation (from ASP.NET redirect)
    path('pmbm/list/', MaintenanceEntryListView.as_view(), name='maintenanceentry_list'),
]
```

#### 4.6 Tests (`machinery_maintenance/tests.py`)

Comprehensive tests for models and views ensure code quality and prevent regressions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, time
from decimal import Decimal
from .models import (
    DGItemMaster, UnitMaster, Machine, MachinePlannedSpare,
    MaintenanceEntry, MaintenanceSpareUsed, MAINTENANCE_TYPE_CHOICES
)
from django.utils import timezone
from unittest.mock import patch

class ModelCreationTest(TestCase):
    """Test model creation and basic field properties."""
    @classmethod
    def setUpTestData(cls):
        # Create shared test data for all tests
        cls.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item_machine_type = DGItemMaster.objects.create(id=101, item_code='MACH001', manf_desc='Drilling Machine', uom_basic=cls.unit_pcs, stock_qty=Decimal('1.00'))
        cls.item_spare_part_a = DGItemMaster.objects.create(id=102, item_code='SPARE001', manf_desc='Drill Bit', uom_basic=cls.unit_pcs, stock_qty=Decimal('100.00'))
        cls.item_spare_part_b = DGItemMaster.objects.create(id=103, item_code='SPARE002', manf_desc='Lubricant', uom_basic=cls.unit_pcs, stock_qty=Decimal('50.00'))

        cls.machine_instance = Machine.objects.create(
            id=1, item_master=cls.item_machine_type, model='XYZ-100', make='Acme',
            capacity='Large', location='Workshop A', comp_id=1, fin_year_id=2023
        )

        cls.planned_spare_a = MachinePlannedSpare.objects.create(
            id=1001, machine=cls.machine_instance, spare_item=cls.item_spare_part_a, qty=Decimal('5.00')
        )
        cls.planned_spare_b = MachinePlannedSpare.objects.create(
            id=1002, machine=cls.machine_instance, spare_item=cls.item_spare_part_b, qty=Decimal('1.00')
        )

    def test_unit_master_creation(self):
        self.assertEqual(self.unit_pcs.symbol, 'PCS')
        self.assertEqual(UnitMaster.objects.count(), 1)

    def test_dg_item_master_creation(self):
        self.assertEqual(self.item_machine_type.manf_desc, 'Drilling Machine')
        self.assertEqual(self.item_machine_type.uom_basic.symbol, 'PCS')
        self.assertEqual(DGItemMaster.objects.count(), 3)

    def test_machine_creation(self):
        self.assertEqual(self.machine_instance.model, 'XYZ-100')
        self.assertEqual(self.machine_instance.item_master.manf_desc, 'Drilling Machine')
        self.assertEqual(Machine.objects.count(), 1)

    def test_machine_planned_spare_creation(self):
        self.assertEqual(self.planned_spare_a.qty, Decimal('5.00'))
        self.assertEqual(self.planned_spare_a.spare_item.manf_desc, 'Drill Bit')
        self.assertEqual(MachinePlannedSpare.objects.count(), 2)

    def test_maintenance_entry_creation_and_save_with_details(self):
        # Using mocks for timezone.localdate and timezone.localtime
        with patch('django.utils.timezone.localdate') as mock_localdate, \
             patch('django.utils.timezone.localtime') as mock_localtime:
            mock_localdate.return_value = date(2023, 10, 26)
            mock_localtime.return_value = timezone.datetime(2023, 10, 26, 10, 30, 0, tzinfo=timezone.utc)

            entry = MaintenanceEntry(
                maintenance_type=0, # Preventive
                from_date=date(2023, 10, 26),
                to_date=date(2023, 10, 26),
                from_time='10:00:00 AM',
                to_time='11:00:00 AM',
                agency_name='Acme Services',
                engineer_name='John Doe',
                next_pm_due_on=date(2024, 10, 26),
                remarks='Standard PM.'
            )

            # Test spare parts data, matching the structure expected by save_with_details
            spared_data = {
                str(self.item_spare_part_a.id): '2.5',
                str(self.item_spare_part_b.id): '0.5'
            }

            entry.save_with_details(
                user_id='testuser', comp_id=1, fin_year_id=2023,
                machine_id=self.machine_instance.id, spared_data=spared_data
            )

            self.assertIsNotNone(entry.id)
            self.assertEqual(entry.sys_date, date(2023, 10, 26))
            self.assertEqual(entry.sys_time, time(10, 30, 0))
            self.assertEqual(entry.session_id, 'testuser')
            self.assertEqual(entry.machine, self.machine_instance)

            # Check associated spare parts
            used_spares = MaintenanceSpareUsed.objects.filter(maintenance_entry=entry)
            self.assertEqual(used_spares.count(), 2)

            used_spare_a = used_spares.get(spare_item=self.item_spare_part_a)
            self.assertEqual(used_spare_a.qty, Decimal('2.5'))

            used_spare_b = used_spares.get(spare_item=self.item_spare_part_b)
            self.assertEqual(used_spare_b.qty, Decimal('0.5'))

    def test_maintenance_entry_save_with_details_invalid_qty(self):
        initial_count = MaintenanceSpareUsed.objects.count()
        entry = MaintenanceEntry(
            maintenance_type=0, from_date=date(2023, 10, 26), to_date=date(2023, 10, 26),
            from_time='10:00:00 AM', to_time='11:00:00 AM', agency_name='Test',
            engineer_name='Test', next_pm_due_on=date(2024, 10, 26), remarks='Test'
        )
        spared_data = {str(self.item_spare_part_a.id): 'invalid_qty'}

        # Expecting an error message print, but the save_with_details will proceed for valid items
        # and skip invalid ones, as per ASP.NET's k++ logic
        entry.save_with_details(
            user_id='testuser', comp_id=1, fin_year_id=2023,
            machine_id=self.machine_instance.id, spared_data=spared_data
        )
        self.assertEqual(MaintenanceSpareUsed.objects.count(), initial_count) # No new spare used

class ViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Ensure test data exists for views
        self.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        self.item_machine_type = DGItemMaster.objects.create(id=101, item_code='MACH001', manf_desc='Drilling Machine', uom_basic=self.unit_pcs, stock_qty=Decimal('1.00'))
        self.item_spare_part_a = DGItemMaster.objects.create(id=102, item_code='SPARE001', manf_desc='Drill Bit', uom_basic=self.unit_pcs, stock_qty=Decimal('100.00'))
        self.item_spare_part_b = DGItemMaster.objects.create(id=103, item_code='SPARE002', manf_desc='Lubricant', uom_basic=self.unit_pcs, stock_qty=Decimal('50.00'))

        self.machine_instance = Machine.objects.create(
            id=1, item_master=self.item_machine_type, model='XYZ-100', make='Acme',
            capacity='Large', location='Workshop A', comp_id=1, fin_year_id=2023
        )

        MachinePlannedSpare.objects.create(id=1001, machine=self.machine_instance, spare_item=self.item_spare_part_a, qty=Decimal('5.00'))
        MachinePlannedSpare.objects.create(id=1002, machine=self.machine_instance, spare_item=self.item_spare_part_b, qty=Decimal('1.00'))

        self.create_url = reverse('maintenanceentry_create', kwargs={'master_id': self.machine_instance.id, 'item_id': self.item_machine_type.id})
        self.spare_table_url = reverse('maintenanceentry_spare_parts_table', kwargs={'master_id': self.machine_instance.id, 'item_id': self.item_machine_type.id})
        self.list_url = reverse('maintenanceentry_list')

    def test_maintenance_entry_create_view_get(self):
        response = self.client.get(self.create_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maintenance/maintenanceentry/create.html')
        self.assertContains(response, 'Machine Details')
        self.assertContains(response, 'Preventive / Breakdown Maintenance - New')
        self.assertIsInstance(response.context['form'], self.create_url.view_class.form_class)
        self.assertIsNotNone(response.context['machine'])
        self.assertGreater(len(response.context['planned_spares']), 0)

    def test_maintenance_entry_create_view_post_success(self):
        initial_entry_count = MaintenanceEntry.objects.count()
        initial_spare_used_count = MaintenanceSpareUsed.objects.count()

        data = {
            'maintenance_type': 0, # Preventive
            'from_date': '2023-10-26',
            'to_date': '2023-10-26',
            'from_time': '10:00', # HTML5 time input format
            'to_time': '11:00',
            'agency_name': 'New Agency',
            'engineer_name': 'Jane Doe',
            'next_pm_due_on': '2024-10-26',
            'remarks': 'Test entry.',
            # Spare parts data simulating checked and filled quantities
            f'is_checked_{self.item_spare_part_a.id}': 'on',
            f'availed_qty_{self.item_spare_part_a.id}': '3.0',
            f'is_checked_{self.item_spare_part_b.id}': 'on',
            f'availed_qty_{self.item_spare_part_b.id}': '0.75',
            # Unchecked spare, should not be processed
            f'is_checked_999': '',
            f'availed_qty_999': '1.0',
        }
        response = self.client.post(self.create_url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX success response for redirect
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], self.list_url)

        # Verify entry created
        self.assertEqual(MaintenanceEntry.objects.count(), initial_entry_count + 1)
        new_entry = MaintenanceEntry.objects.latest('id')
        self.assertEqual(new_entry.agency_name, 'New Agency')
        self.assertEqual(new_entry.machine, self.machine_instance)

        # Verify associated spare parts
        used_spares = MaintenanceSpareUsed.objects.filter(maintenance_entry=new_entry)
        self.assertEqual(used_spares.count(), initial_spare_used_count + 2)
        self.assertTrue(used_spares.filter(spare_item=self.item_spare_part_a, qty=Decimal('3.0')).exists())
        self.assertTrue(used_spares.filter(spare_item=self.item_spare_part_b, qty=Decimal('0.75')).exists())

    def test_maintenance_entry_create_view_post_invalid_form(self):
        initial_entry_count = MaintenanceEntry.objects.count()
        data = {
            # Missing required fields like from_date, agency_name
            'maintenance_type': 0,
            'to_date': '2023-10-26',
            'next_pm_due_on': '2024-10-26',
            'remarks': 'Test entry.',
        }
        response = self.client.post(self.create_url, data, HTTP_HX_REQUEST='true')

        # HTMX partial swap, should return 200 with errors in the form
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'This field is required.')
        self.assertNotContains(response, 'Maintenance entry created successfully') # No success message
        self.assertEqual(MaintenanceEntry.objects.count(), initial_entry_count) # No entry created

    def test_maintenance_entry_create_view_post_invalid_spare_qty(self):
        initial_entry_count = MaintenanceEntry.objects.count()
        data = {
            'maintenance_type': 0,
            'from_date': '2023-10-26',
            'to_date': '2023-10-26',
            'from_time': '10:00',
            'to_time': '11:00',
            'agency_name': 'New Agency',
            'engineer_name': 'Jane Doe',
            'next_pm_due_on': '2024-10-26',
            'remarks': 'Test entry.',
            f'is_checked_{self.item_spare_part_a.id}': 'on',
            f'availed_qty_{self.item_spare_part_a.id}': 'invalid_number', # Invalid quantity
        }
        response = self.client.post(self.create_url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Invalid data found in spare parts section.')
        self.assertEqual(MaintenanceEntry.objects.count(), initial_entry_count) # No entry created

    def test_spare_parts_table_partial_view(self):
        response = self.client.get(self.spare_table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maintenance/maintenanceentry/_spare_parts_table.html')
        self.assertContains(response, 'Drill Bit')
        self.assertContains(response, 'Lubricant')
        self.assertContains(response, '<table id="sparePartsTable"') # Ensures DataTables hook is present

    def test_maintenance_entry_list_view(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_maintenance/maintenanceentry/list.html')
        # Check for context data typical of a list view
        self.assertTrue('maintenance_entries' in response.context)
        # Assuming there's at least one entry after setup or post
        MaintenanceEntry.objects.create(
            id=2, machine=self.machine_instance, sys_date=date.today(),
            maintenance_type=0, from_date=date.today(), to_date=date.today(),
            from_time='10:00:00 AM', to_time='11:00:00 AM', agency_name='Test',
            engineer_name='Test', next_pm_due_on=date.today(), remarks='Test'
        )
        response = self.client.get(self.list_url)
        self.assertContains(response, 'Drilling Machine') # Check if machine name is rendered
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Form Submission:** The main form in `create.html` uses `hx-post`, `hx-swap`, `hx-target`, and `hx-on::after-request` to submit data without a full page reload. Upon successful submission, a `HX-Redirect` header is sent to navigate the user to the list page, mimicking the original ASP.NET behavior.
*   **HTMX for Dynamic Content Loading:** The spare parts table within `create.html` is initially loaded via `hx-get` to `maintenanceentry_spare_parts_table`. This keeps the initial page load faster and allows the table to be refreshed independently if needed (though not explicitly required by the original ASP.NET code).
*   **DataTables Integration:** The `_spare_parts_table.html` partial includes the DataTables initialization script, ensuring that once the table content is loaded via HTMX, DataTables enhances it with sorting, searching, and pagination. The `htmx:afterSwap` event listener ensures DataTables is initialized only after the table is present in the DOM.
*   **Alpine.js for UI State:** While not heavily used for this specific page (as HTMX handles most of the dynamic behavior), Alpine.js can be employed for minor client-side state management or interactive elements. For instance, the client-side logic that makes `Availed Qty` required when a `chkSpare` is checked is implemented with plain JavaScript for simplicity, but could be managed by Alpine.js for more complex scenarios.
*   **No Additional JavaScript:** The design avoids large, custom JavaScript frameworks, relying on the lightweight nature of HTMX and Alpine.js for efficient and maintainable frontend interactions.
*   **Error Handling:** Form validation errors are displayed inline by Django, and server-side messages (success/error) are handled by Django's `messages` framework and displayed at the top of the form after an HTMX swap.

---

### Final Notes

*   **Placeholders:** Remember to replace mock values for `CompId`, `FinYearId`, and `SessionId` with actual logic in your Django application (e.g., retrieving them from `request.user` or global settings).
*   **`base.html`:** This plan assumes `core/base.html` exists and provides the necessary boilerplate, including CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables.
*   **Database Connections:** Django's ORM handles database connections automatically. The `fun.Connection()` and `fun.select()` raw SQL calls are replaced by Django ORM queries.
*   **Date/Time Formatting:** The original ASP.NET code stores times as strings. Django's `TimeField` expects `datetime.time` objects. The form handles the conversion to match the database's string format for `from_time` and `to_time`. For frontend date pickers, consider a library like Flatpickr alongside HTML5 `type="date"` inputs.
*   **Security:** Ensure proper Django security practices, such as CSRF protection (automatically included in forms).
*   **Extensibility:** This modular approach allows for easy expansion. For instance, creating "Update" and "Delete" views for `MaintenanceEntry` would follow similar patterns.