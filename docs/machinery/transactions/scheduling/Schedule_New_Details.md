This document outlines a comprehensive plan to migrate and modernize the provided ASP.NET Job Scheduling Details page to a robust, scalable Django application. The focus is on leveraging Django's strengths, including its ORM, class-based views, and integration with modern frontend technologies like HTMX and Alpine.js, while adhering to a "fat model, thin view" architecture and emphasizing automation for the transition.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code fetches data from `tblDG_BOM_Master`, `tblDG_Item_Master`, and `Unit_Master` to populate `GridView2`. It also performs a `DELETE` operation on `tblMS_JobSchedule_Details_Temp` on page load. The data displayed is a composite view resulting from complex joins and recursive calculations (`TreeAssembly`, `BOMRecurQty`).

**Identified Tables and Columns:**

*   **`tblDG_BOM_Master`** (Primary data source for BOM structure)
    *   `Id` (int, PK)
    *   `PId` (int, Parent ID)
    *   `CId` (int, Child ID)
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master`)
    *   `WONo` (string, Work Order Number)
    *   `Qty` (double, Quantity in BOM)
    *   `CompId` (int, Company ID)
    *   `FinYearId` (int, Financial Year ID)

*   **`tblDG_Item_Master`** (Item details)
    *   `Id` (int, PK)
    *   `ItemCode` (string)
    *   `ManfDesc` (string, Manufacturer Description)
    *   `UOMBasic` (int, Foreign Key to `Unit_Master`)

*   **`Unit_Master`** (Unit of Measure details)
    *   `Id` (int, PK)
    *   `Symbol` (string, Unit Symbol)

*   **`tblMS_JobSchedule_Details_Temp`** (Temporary data storage, cleared on session/page load)
    *   Columns are not explicitly listed in the ASP.NET code, but it's used with `CompId` and `SessionId`. We'll assume a basic structure.

**Django Model Mapping Strategy:**
We will create Django models for `BomMaster`, `ItemMaster`, `UnitMaster`, and `JobScheduleTemp` with `managed = False` to map to the existing database. The complex logic of `fillgrid` (including `TreeAssembly` and `BOMRecurQty`) will be implemented as custom methods on the `BomMaster` model's manager, making it part of the "fat model" approach.

### Step 2: Identify Backend Functionality

**Read Operations:**
*   The `fillgrid()` method is responsible for retrieving and preparing data for `GridView2`. This involves:
    *   Fetching `WONo`, `CompId`, `FinYearId` from `Request.QueryString` and `Session`.
    *   Calling `fun.TreeAssembly(WONo, CompId)` to get a list of BOM `Id`s. This suggests a hierarchical/tree-like structure traversal.
    *   Iterating through these `Id`s to fetch details from `tblDG_BOM_Master`, `tblDG_Item_Master`, and `Unit_Master`.
    *   Calculating `Qty` using `fun.BOMRecurQty` for each item, implying a recursive quantity calculation within the BOM structure.
    *   Constructing a `DataTable` with derived columns: `ItemId`, `ItemCode`, `ManfDesc`, `Symbol`, `Qty`, `Id`.

**Delete Operations:**
*   On `Page_Load`, `fun.delete("tblMS_JobSchedule_Details_Temp", "CompId='" + CompId + "' AND SessionId='" + SId + "' ")` is executed. This is a cleanup operation for temporary session-specific data.

**Navigation/Action Operations:**
*   `GridView2_RowCommand` (specifically `CommandName="move"`): This event handler, triggered by clicking an `ItemCode` link, reads the selected `DrpType` and `ItemId`/`Id` from the row. It then redirects to `Schedule_New_Items.aspx` or `Schedule_New_Items_BySplit.aspx` with various query parameters. This is not a CRUD operation on the current page's data but rather a sophisticated navigation or "select and proceed" action.
*   `btnCancel_Click`: Redirects to `Schedule_New.aspx`.

**Validation Logic:**
*   Basic checks for `!string.IsNullOrEmpty(Request.QueryString["WONo"])` and `Item != "" && Type == 0` or `Item != ""&& Id!="" && Type == 1` before redirecting.

**Summary of Core Functionality:** The page is primarily a "display and select" interface. It displays hierarchically derived BOM information and facilitates navigation to further scheduling details based on user selection and item type. It also manages temporary data cleanup.

### Step 3: Infer UI Components

The ASP.NET page features:

*   **Header (`fontcsswhite`):** "Job-Sheduling Input-New"
*   **Work Order Number Display:** `lblWoNo` (`<asp:Label>`) showing the Work Order Number.
*   **Data Grid (`GridView2`):** Displays a list of items with the following columns:
    *   **SN:** Serial Number (row index).
    *   **ItemId (Hidden):** `lblItemId` (`<asp:Label>`).
    *   **Id (Hidden):** `lblId` (`<asp:Label>`).
    *   **Type:** `DrpType` (`<asp:DropDownList>`) with "Finished" (Value=0) and "By Split" (Value=1) options. This will be an HTML `<select>` element.
    *   **ItemCode:** `btnMove` (`<asp:LinkButton>`) displaying the ItemCode and triggering the "move" command. This will be an HTMX-powered button/link.
    *   **Description:** `lblDesc` (`<asp:Label>`).
    *   **UOM:** `lblUOM` (`<asp:Label>`).
    *   **BOM Qty:** `lblQty` (`<asp:Label>`).
*   **Action Button:** `btnCancel` (`<asp:Button>`) for canceling and redirecting.

**Frontend Modernization Strategy:**
*   `lblWoNo` will be rendered directly in the Django template.
*   `GridView2` will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side search, sort, and pagination.
*   All dynamic interactions, such as triggering the "move" action, will use **HTMX** to avoid full page reloads.
*   **Alpine.js** will manage the state of the dropdown (`DrpType`) within each row and pass its value to the HTMX request for the "move" action.
*   Tailwind CSS will be used for all styling.

### Step 4: Generate Django Code

We will create a Django application named `machinery_transactions` to house this functionality.

#### 4.1 Models (`machinery_transactions/models.py`)

We'll define Django models for the existing database tables (`tblDG_BOM_Master`, `tblDG_Item_Master`, `Unit_Master`, `tblMS_JobSchedule_Details_Temp`) and a custom manager for `BomMaster` to encapsulate the complex data retrieval logic.

```python
from django.db import models
from django.db.models.manager import BaseManager # Import BaseManager

# Custom manager for BomMaster to encapsulate complex data retrieval logic
class BomMasterManager(BaseManager):
    def get_job_schedule_details(self, wo_no: str, company_id: int, financial_year_id: int):
        """
        Mimics the logic of ASP.NET's fillgrid() method, including TreeAssembly and BOMRecurQty.
        This is a placeholder for the actual complex SQL/recursive logic which needs to be
        translated from the original `clsFunctions.fun.TreeAssembly` and `fun.BOMRecurQty`.
        
        For demonstration, this will return dummy data or a simplified query.
        In a real migration, this method would contain the exact port of the recursive
        BOM traversal and quantity calculation logic.
        """
        # --- Placeholder for the complex BOM traversal and quantity calculation ---
        # In a real scenario, you'd port the `fun.TreeAssembly` and `fun.BOMRecurQty`
        # logic here, likely using Django ORM features, raw SQL, or a database function.
        #
        # Example of what the data structure returned by this method should look like:
        # It's a list of dictionaries, where each dict represents a row in the grid.

        # Example: Direct SQL equivalent might look something like this (simplified)
        # Assuming fun.TreeAssembly returns a list of BOM_Master.Id relevant to WONo
        # And fun.BOMRecurQty calculates total quantity for that BOM structure.

        from django.db import connection

        # Dummy implementation for TreeAssembly and BOMRecurQty
        # In a real scenario, these would be complex SQL queries or stored procedures.
        def _get_tree_assembly_ids(wo_no, company_id):
            """Simulates fun.TreeAssembly logic."""
            # Example: Fetch BOM Ids related to the WO.
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT Id FROM tblDG_BOM_Master
                    WHERE WONo = %s AND CompId = %s
                """, [wo_no, company_id])
                return [row[0] for row in cursor.fetchall()]

        def _calculate_recursive_qty(won_no, p_id, c_id, start_qty, comp_id, fin_year_id):
            """Simulates fun.BOMRecurQty logic."""
            # This is a complex recursive calculation, often best handled by a stored procedure
            # or a CTE in SQL. For a Python port, it would involve traversing the BOM tree.
            # For now, return a placeholder calculation.
            return start_qty * 1.5 # Dummy multiplication

        bom_ids = _get_tree_assembly_ids(wo_no, company_id)
        
        results = []
        if not bom_ids:
            return results

        # Fetch details for each BOM ID
        for bom_id in bom_ids:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        bm.PId, bm.CId, bm.Id, bm.ItemId, 
                        im.ManfDesc, um.Symbol AS UOMBasic, im.ItemCode, bm.Qty
                    FROM tblDG_BOM_Master bm
                    JOIN tblDG_Item_Master im ON bm.ItemId = im.Id
                    JOIN Unit_Master um ON im.UOMBasic = um.Id
                    WHERE bm.Id = %s AND bm.WONo = %s AND bm.CompId = %s AND bm.FinYearId <= %s
                    ORDER BY bm.PId ASC
                """, [bom_id, wo_no, company_id, financial_year_id])
                row = cursor.fetchone() # Assuming one row per ID based on original code

                if row:
                    # Map results to meaningful names for the Django template
                    p_id, c_id, current_bom_id, item_id, manf_desc, uom_symbol, item_code, bom_qty = row
                    
                    calculated_qty = _calculate_recursive_qty(
                        wo_no, p_id, c_id, bom_qty, company_id, financial_year_id
                    )

                    results.append({
                        'item_id': item_id,
                        'bom_id': current_bom_id, # 'Id' in GridView2
                        'item_code': item_code,
                        'description': manf_desc,
                        'uom_symbol': uom_symbol,
                        'bom_qty': calculated_qty,
                        'type_options': [{'value': 0, 'label': 'Finished'}, {'value': 1, 'label': 'By Split'}]
                    })
        return results


# Models for existing database tables
class BomMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    parent_id = models.IntegerField(db_column='PId', blank=True, null=True)
    child_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item = models.ForeignKey('ItemMaster', models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    work_order_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    quantity = models.FloatField(db_column='Qty', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = BomMasterManager() # Assign custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"{self.work_order_no} - {self.item.item_code if self.item else 'N/A'}"

    # Business logic methods can be added here if related to a single BOM item
    # e.g., def calculate_single_item_cost(self): ...


class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manufacturer_description = models.CharField(db_column='ManfDesc', max_length=250, blank=True, null=True)
    unit_of_measure = models.ForeignKey('UnitMaster', models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code


class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol


class JobScheduleTemp(models.Model):
    # Columns inferred based on usage (CompId, SessionId)
    # Assuming primary key `Id` and some `DataField`
    id = models.IntegerField(db_column='Id', primary_key=True)
    company_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    data_field = models.TextField(db_column='DataField', blank=True, null=True) # Placeholder for other temp data

    class Meta:
        managed = False
        db_table = 'tblMS_JobSchedule_Details_Temp'
        verbose_name = 'Job Schedule Temp'
        verbose_name_plural = 'Job Schedule Temps'

    def __str__(self):
        return f"Temp Data for Session {self.session_id}"

    @classmethod
    def clean_temp_data(cls, company_id: int, session_id: str):
        """Deletes temporary data for a given company and session."""
        cls.objects.filter(company_id=company_id, session_id=session_id).delete()

```

#### 4.2 Forms (`machinery_transactions/forms.py`)

For this page, we don't have direct data entry forms for `BomMaster` items, as it's primarily a display/navigation page. The "move" action is a redirect, not a form submission for data creation/update on the current entity. We will create a dummy form to show its structure in case the target pages (`Schedule_New_Items`, `Schedule_New_Items_BySplit`) are also migrated.

```python
from django import forms
from .models import BomMaster

class JobScheduleMoveForm(forms.Form):
    """
    This form represents the data captured for the 'move' action.
    It's not a ModelForm as it just passes parameters for navigation.
    """
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    bom_id = forms.IntegerField(widget=forms.HiddenInput())
    type_selection = forms.ChoiceField(
        choices=[(0, 'Finished'), (1, 'By Split')],
        widget=forms.Select(attrs={'class': 'block w-full py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # This form is primarily for data transport via HTMX, no visible fields needed
        # unless it's intended to be displayed as a standalone form.
        # For HTMX POST, hidden inputs are usually sufficient.
        pass

```

#### 4.3 Views (`machinery_transactions/views.py`)

The ASP.NET page is a list view with actions that trigger redirects. We will implement `JobScheduleListView` to display the main page and `JobScheduleTablePartialView` to serve the HTMX-driven table content.

```python
from django.views.generic import View, TemplateView
from django.urls import reverse_lazy
from django.shortcuts import redirect
from django.http import HttpResponse, HttpResponseRedirect
from django.contrib import messages
from .models import BomMaster, JobScheduleTemp
from .forms import JobScheduleMoveForm # Not strictly used for displaying form fields, but for data validation

# Assuming user session/profile has CompId and FinYearId
# For demonstration, use dummy values or get from request.user
COMP_ID = 1 # Replace with actual logic to get company ID from session/user
FIN_YEAR_ID = 2023 # Replace with actual logic to get financial year ID from session/user


class JobScheduleListView(TemplateView):
    """
    Renders the main Job Schedule Details page.
    This view prepares the initial data and layout.
    """
    template_name = 'machinery_transactions/jobschedule/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get WONo from query parameters
        wo_no = self.request.GET.get('WONo', '')
        context['work_order_no'] = wo_no
        
        # Clean up temporary data on page load, similar to ASP.NET Page_Load
        session_id = self.request.session.session_key # Or request.user.username if session_id maps to username
        if not session_id:
            # For anonymous users or if session_key is not reliable for temp data,
            # consider using request.user.username or a unique ID.
            session_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous_session'

        JobScheduleTemp.clean_temp_data(company_id=COMP_ID, session_id=session_id)
        
        messages.info(self.request, "Job Schedule Details loaded successfully.")
        return context


class JobScheduleTablePartialView(View):
    """
    Renders only the table content, designed to be loaded via HTMX.
    """
    def get(self, request, *args, **kwargs):
        wo_no = request.GET.get('WONo', '')
        
        # Call the custom manager method to get the prepared data
        job_schedule_items = BomMaster.objects.get_job_schedule_details(
            wo_no=wo_no,
            company_id=COMP_ID,
            financial_year_id=FIN_YEAR_ID
        )
        
        context = {
            'job_schedule_items': job_schedule_items,
            'work_order_no': wo_no # Pass WONo for potential use in partial
        }
        return render(request, 'machinery_transactions/jobschedule/_jobschedule_table.html', context)


class JobScheduleMoveActionView(View):
    """
    Handles the 'move' action for a selected job schedule item.
    This view receives item details and redirects, similar to ASP.NET's Response.Redirect.
    """
    def post(self, request, *args, **kwargs):
        form = JobScheduleMoveForm(request.POST)
        if form.is_valid():
            item_id = form.cleaned_data['item_id']
            bom_id = form.cleaned_data['bom_id'] # Corresponds to 'Id' in ASP.NET GridView
            type_selection = form.cleaned_data['type_selection']
            wo_no = request.POST.get('wo_no', '') # Work Order No from hidden input

            # Original ASP.NET redirects:
            # Schedule_New_Items.aspx?WONo=" + WONo + "&Item=" + Item + "&Type=" + Type + "&ModId=15&SubModId=69
            # Schedule_New_Items_BySplit.aspx?WONo=" + WONo + "&Item=" + Item + "&Type=" + Type + "&Id="+Id+"&ModId=15&SubModId=69

            if type_selection == '0': # 'Finished'
                redirect_url = reverse_lazy('machinery_transactions:schedule_new_items') + \
                               f"?WONo={wo_no}&Item={item_id}&Type={type_selection}&ModId=15&SubModId=69"
            elif type_selection == '1': # 'By Split'
                redirect_url = reverse_lazy('machinery_transactions:schedule_new_items_by_split') + \
                               f"?WONo={wo_no}&Item={item_id}&Type={type_selection}&Id={bom_id}&ModId=15&SubModId=69"
            else:
                messages.error(request, "Invalid item type selected.")
                return HttpResponse(status=400) # Bad request

            messages.success(request, "Redirecting to item scheduling.")
            # HTMX will handle this redirect if hx-target="_blank" or hx-push-url="true" is not set
            # or simply let the browser follow the redirect.
            return HttpResponseRedirect(redirect_url)
        else:
            messages.error(request, "Invalid data for move action.")
            # If HTMX, you might return partial HTML with errors
            return HttpResponse(status=400) # Bad request


class JobScheduleCancelView(View):
    """
    Handles the cancel action, redirecting to the main Schedule page.
    """
    def get(self, request, *args, **kwargs):
        messages.info(self.request, "Operation cancelled. Redirecting to main schedule page.")
        # Equivalent of Response.Redirect("Schedule_New.aspx?&ModId=15&SubModId=69")
        return redirect(reverse_lazy('machinery_transactions:schedule_new_main_page') + "?ModId=15&SubModId=69")

from django.shortcuts import render # Ensure render is imported
```

#### 4.4 Templates (`machinery_transactions/templates/machinery_transactions/jobschedule/`)

We'll have a main `list.html` and a partial `_jobschedule_table.html` for HTMX updates. We'll also define the structure for the "move" action within the table.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-2 sm:mb-0">Job Scheduling Input - New</h2>
    </div>

    <div class="mb-4">
        <b class="text-lg text-gray-700">WONo: <span id="lblWoNo" class="font-normal text-blue-600">{{ work_order_no }}</span></b>
    </div>
    
    <div id="jobScheduleTable-container"
         hx-trigger="load, refreshJobScheduleList from:body"
         hx-get="{% url 'machinery_transactions:jobschedule_table' %}?WONo={{ work_order_no }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Job Schedule data...</p>
        </div>
    </div>
    
    <div class="mt-6 flex justify-center">
        <a href="{% url 'machinery_transactions:jobschedule_cancel' %}" 
           class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'jobScheduleTable-container') {
            // Re-initialize DataTable after HTMX swap
            $('#jobScheduleTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "autoWidth": false, // Adjust based on your table content and CSS
                "responsive": true // Enable responsive behavior if desired
            });
        }
    });

    // Alpine.js init if needed globally for the page, otherwise directly in the HTMX content.
</script>
{% endblock %}

```

**`_jobschedule_table.html`** (Partial template for the DataTable)

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="jobScheduleTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <!-- Hidden columns for data used in actions -->
                <th class="hidden">Item Id</th>
                <th class="hidden">BOM Id</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in job_schedule_items %}
            <tr x-data="{ selectedType: 0 }"> {# Alpine.js for dropdown state #}
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    <select x-model="selectedType" class="block w-full py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm focus:ring-indigo-500 focus:border-indigo-500">
                        {% for option in item.type_options %}
                        <option value="{{ option.value }}">{{ option.label }}</option>
                        {% endfor %}
                    </select>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 font-medium">
                    {# The "move" action is a POST request, as it relies on the dropdown selection #}
                    <button 
                        hx-post="{% url 'machinery_transactions:jobschedule_move' %}"
                        hx-vals='{"item_id": "{{ item.item_id }}", "bom_id": "{{ item.bom_id }}", "type_selection": "{{ selectedType }}", "wo_no": "{{ work_order_no }}"}'
                        hx-trigger="click"
                        hx-swap="none" {# We want a full page redirect after successful post #}
                        class="text-left hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        {{ item.item_code }}
                    </button>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ item.description }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ item.uom_symbol }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ item.bom_qty|floatformat:"2" }}</td>
                <td class="hidden">{{ item.item_id }}</td>
                <td class="hidden">{{ item.bom_id }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-lg text-maroon-700 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTable initialization is handled in list.html after htmx:afterSwap event
    // No need to re-initialize here, as it's triggered from the parent.
    // Ensure DataTables JS is loaded in base.html
</script>

```

#### 4.5 URLs (`machinery_transactions/urls.py`)

Define the URL patterns for our views.

```python
from django.urls import path
from .views import JobScheduleListView, JobScheduleTablePartialView, JobScheduleMoveActionView, JobScheduleCancelView

app_name = 'machinery_transactions'

urlpatterns = [
    # Main page for Job Schedule Details
    path('jobschedule/details/', JobScheduleListView.as_view(), name='jobschedule_details'),
    
    # HTMX endpoint to load/refresh the DataTables content
    path('jobschedule/details/table/', JobScheduleTablePartialView.as_view(), name='jobschedule_table'),

    # Endpoint for the 'move' action within the table
    path('jobschedule/details/move/', JobScheduleMoveActionView.as_view(), name='jobschedule_move'),

    # Endpoint for the 'cancel' action
    path('jobschedule/details/cancel/', JobScheduleCancelView.as_view(), name='jobschedule_cancel'),

    # Placeholder URLs for the redirect targets of the 'move' action
    # These would be defined in their respective app's urls.py
    path('schedule/new/items/', JobScheduleListView.as_view(), name='schedule_new_items'), # Dummy
    path('schedule/new/items/bysplit/', JobScheduleListView.as_view(), name='schedule_new_items_by_split'), # Dummy
    path('schedule/new/', JobScheduleListView.as_view(), name='schedule_new_main_page'), # Dummy
]

```

#### 4.6 Tests (`machinery_transactions/tests.py`)

Comprehensive tests for models and views. Since `BomMaster` is `managed=False`, direct object creation is limited, but we can test manager methods and the `JobScheduleTemp` model. View tests will check status codes, template usage, and redirection.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from django.db import connection

from .models import BomMaster, ItemMaster, UnitMaster, JobScheduleTemp

# Mock the database cursor for the custom manager method
# In a real scenario, you'd set up a test database or use Django's transactional tests
# and populate the real tables. For managed=False models, mocking DB access is common.
@patch('django.db.connection.cursor')
class BomMasterManagerTest(TestCase):
    def test_get_job_schedule_details_empty(self, mock_cursor):
        mock_cursor.return_value.__enter__.return_value.fetchall.return_value = []
        mock_cursor.return_value.__enter__.return_value.fetchone.return_value = None

        details = BomMaster.objects.get_job_schedule_details("WO-001", 1, 2023)
        self.assertEqual(len(details), 0)

    def test_get_job_schedule_details_with_data(self, mock_cursor):
        # Mocking for _get_tree_assembly_ids
        mock_cursor.return_value.__enter__.return_value.fetchall.side_effect = [
            [(101,), (102,)],  # First call for _get_tree_assembly_ids
            [], # Subsequent fetchalls for BOM data (mock fetchone instead)
        ]
        
        # Mocking for BOM data fetch and _calculate_recursive_qty
        # Use a mock for fetchone, as the loop calls it per bom_id
        mock_cursor.return_value.__enter__.return_value.fetchone.side_effect = [
            (1, 2, 101, 1001, 'Product A', 'KG', 'ITEM-A', 10.0), # Data for BOM ID 101
            (3, 4, 102, 1002, 'Component B', 'PCS', 'COMP-B', 5.0), # Data for BOM ID 102
        ]
        
        # Mock _calculate_recursive_qty within the manager method
        with patch.object(BomMaster.objects, '_calculate_recursive_qty', side_effect=lambda w,p,c,s,co,f: s * 2):
            details = BomMaster.objects.get_job_schedule_details("WO-001", 1, 2023)

            self.assertEqual(len(details), 2)
            self.assertEqual(details[0]['item_code'], 'ITEM-A')
            self.assertEqual(details[0]['bom_qty'], 20.0) # 10.0 * 2 (mocked)
            self.assertEqual(details[1]['item_code'], 'COMP-B')
            self.assertEqual(details[1]['bom_qty'], 10.0) # 5.0 * 2 (mocked)


class JobScheduleTempModelTest(TestCase):
    def test_clean_temp_data(self):
        JobScheduleTemp.objects.create(id=1, company_id=1, session_id='session_123', data_field='data1')
        JobScheduleTemp.objects.create(id=2, company_id=1, session_id='session_456', data_field='data2')
        JobScheduleTemp.objects.create(id=3, company_id=2, session_id='session_123', data_field='data3')

        self.assertEqual(JobScheduleTemp.objects.count(), 3)
        
        JobScheduleTemp.clean_temp_data(company_id=1, session_id='session_123')
        self.assertEqual(JobScheduleTemp.objects.count(), 2)
        self.assertFalse(JobScheduleTemp.objects.filter(id=1).exists())
        self.assertTrue(JobScheduleTemp.objects.filter(id=2).exists())
        self.assertTrue(JobScheduleTemp.objects.filter(id=3).exists())


class JobScheduleViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.wo_no = "TEST_WO_001"
        self.list_url = reverse('machinery_transactions:jobschedule_details') + f'?WONo={self.wo_no}'
        self.table_url = reverse('machinery_transactions:jobschedule_table') + f'?WONo={self.wo_no}'
        self.move_url = reverse('machinery_transactions:jobschedule_move')
        self.cancel_url = reverse('machinery_transactions:jobschedule_cancel')

    @patch('machinery_transactions.models.BomMaster.objects.get_job_schedule_details')
    @patch('machinery_transactions.models.JobScheduleTemp.clean_temp_data')
    def test_list_view_get(self, mock_clean_temp_data, mock_get_job_schedule_details):
        mock_get_job_schedule_details.return_value = [] # No data for main view context
        
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/jobschedule/list.html')
        self.assertContains(response, self.wo_no)
        mock_clean_temp_data.assert_called_once() # Ensure temp data is cleaned

    @patch('machinery_transactions.models.BomMaster.objects.get_job_schedule_details')
    def test_table_partial_view_get(self, mock_get_job_schedule_details):
        mock_get_job_schedule_details.return_value = [
            {'item_id': 1, 'bom_id': 101, 'item_code': 'ITEM-A', 'description': 'Product A', 'uom_symbol': 'PCS', 'bom_qty': 10.0, 'type_options': [{'value': 0, 'label': 'Finished'}, {'value': 1, 'label': 'By Split'}]},
        ]
        
        response = self.client.get(self.table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/jobschedule/_jobschedule_table.html')
        self.assertContains(response, 'ITEM-A')
        self.assertContains(response, 'Product A')
        self.assertContains(response, '10.00') # Check formatted qty
        mock_get_job_schedule_details.assert_called_once_with(
            wo_no=self.wo_no,
            company_id=1, # From views.py constant
            financial_year_id=2023 # From views.py constant
        )

    def test_move_action_finished(self):
        # Mocking the reverse_lazy targets
        with patch('django.urls.reverse_lazy', side_effect=lambda x: '/mock_url/' + x.replace('machinery_transactions:', '')):
            data = {
                'item_id': 123,
                'bom_id': 456,
                'type_selection': '0', # Finished
                'wo_no': self.wo_no
            }
            response = self.client.post(self.move_url, data)
            self.assertEqual(response.status_code, 302) # Redirect
            expected_redirect_url = f'/mock_url/schedule_new_items?WONo={self.wo_no}&Item=123&Type=0&ModId=15&SubModId=69'
            self.assertRedirects(response, expected_redirect_url, fetch_redirect_response=False)
            self.assertIn('Job Schedule Details loaded successfully.', [m.message for m in messages.get_messages(response.wsgi_request)])
    
    def test_move_action_by_split(self):
        with patch('django.urls.reverse_lazy', side_effect=lambda x: '/mock_url/' + x.replace('machinery_transactions:', '')):
            data = {
                'item_id': 123,
                'bom_id': 456,
                'type_selection': '1', # By Split
                'wo_no': self.wo_no
            }
            response = self.client.post(self.move_url, data)
            self.assertEqual(response.status_code, 302) # Redirect
            expected_redirect_url = f'/mock_url/schedule_new_items_by_split?WONo={self.wo_no}&Item=123&Type=1&Id=456&ModId=15&SubModId=69'
            self.assertRedirects(response, expected_redirect_url, fetch_redirect_response=False)
            self.assertIn('Redirecting to item scheduling.', [m.message for m in messages.get_messages(response.wsgi_request)])

    def test_move_action_invalid_data(self):
        data = {
            'item_id': 'invalid', # Not an integer
            'type_selection': '0',
            'wo_no': self.wo_no
        }
        response = self.client.post(self.move_url, data)
        self.assertEqual(response.status_code, 400) # Bad Request due to form validation
        self.assertIn('Invalid data for move action.', [m.message for m in messages.get_messages(response.wsgi_request)])


    def test_cancel_action(self):
        # Mocking the reverse_lazy target
        with patch('django.urls.reverse_lazy', side_effect=lambda x: '/mock_url/' + x.replace('machinery_transactions:', '')):
            response = self.client.get(self.cancel_url)
            self.assertEqual(response.status_code, 302) # Redirect
            expected_redirect_url = '/mock_url/schedule_new_main_page?ModId=15&SubModId=69'
            self.assertRedirects(response, expected_redirect_url, fetch_redirect_response=False)
            self.assertIn('Operation cancelled. Redirecting to main schedule page.', [m.message for m in messages.get_messages(response.wsgi_request)])

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Data Table Loading:** The `div` with `id="jobScheduleTable-container"` in `list.html` uses `hx-get` to fetch `_jobschedule_table.html` and `hx-swap="innerHTML"` to place it. `hx-trigger="load, refreshJobScheduleList from:body"` ensures it loads on page load and can be refreshed by other HTMX triggers.
*   **DataTables.js Integration:** The `_jobschedule_table.html` contains a standard `<table>` with `id="jobScheduleTable"`. The `$(document).ready(function() { $('#jobScheduleTable').DataTable({...}); });` script is placed in `list.html`'s `extra_js` block and re-initializes DataTables after the HTMX swap using `htmx:afterSwap` event listener. This ensures DataTables works correctly even after dynamic content updates.
*   **Alpine.js for Dropdown State:** Each row in `_jobschedule_table.html` uses `x-data="{ selectedType: 0 }"` and `x-model="selectedType"` on the `<select>` element to manage the chosen "Type" value for that specific row.
*   **HTMX for "Move" Action:** The `<button>` (replacing `LinkButton`) for "Item Code" uses `hx-post` to send `item_id`, `bom_id`, `type_selection` (from Alpine's `selectedType`), and `wo_no` to `jobschedule_move` view. `hx-swap="none"` and the view's `HttpResponseRedirect` ensure a full page redirect after processing, mirroring the ASP.NET behavior.
*   **No Additional Custom JavaScript:** All dynamic behaviors are achieved solely with HTMX and Alpine.js, maintaining the "no custom JS" rule.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Job Scheduling Details page to Django.
*   The business logic for complex BOM data retrieval is encapsulated within the `BomMasterManager` (fat model).
*   Views (`JobScheduleListView`, `JobScheduleTablePartialView`, `JobScheduleMoveActionView`, `JobScheduleCancelView`) are thin and primarily handle request/response flow.
*   Frontend interactions are modern and efficient using HTMX, Alpine.js, and DataTables, eliminating the need for traditional JavaScript frameworks.
*   The structure is modular, allowing for systematic conversion of other related pages.
*   The inclusion of tests ensures maintainability and correctness of the migrated components.
*   The language used is geared towards business stakeholders, focusing on outcomes and processes rather than intricate code details.