This modernization plan details the transition of your ASP.NET Machinery Dashboard to a modern Django application. Our approach leverages AI-assisted automation to systematically convert components, focusing on business value by enhancing performance, maintainability, and user experience with minimal manual code efforts. We prioritize a "fat model, thin view" architecture, ensuring a clean, efficient, and scalable solution.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

The ASP.NET code interacts with several tables, inferred from SQL statements and column bindings:

-   **`tblDG_Category_Master`**: Manages machinery categories.
    -   `CId` (Primary Key, inferred from `fun.drpDesignCategory` and joins)
    -   `CategoryName` (inferred from dropdown data binding)

-   **`tblDG_SubCategory_Master`**: Manages machinery subcategories linked to categories.
    -   `SCId` (Primary Key, inferred)
    -   `CId` (Foreign Key to `tblDG_Category_Master`, inferred from `CId` join)
    -   `Symbol` (inferred from dropdown data binding `Symbol+' - '+SCName`)
    -   `SCName` (inferred)

-   **`tblDG_Item_Master`**: Stores master data for individual machinery items.
    -   `Id` (Primary Key)
    -   `ItemCode` (Machine Code, used for searching and display)
    -   `ManfDesc` (Description, used for searching and display)
    -   `CId` (Foreign Key to `tblDG_Category_Master`)
    -   `SCId` (Foreign Key to `tblDG_SubCategory_Master`)
    -   `CompId` (Company ID, used for filtering)
    -   `Absolute` (Boolean flag, filtered by `Absolute!='1'`)

-   **`tblMS_Master`**: Stores details about machinery instances.
    -   `Id` (Primary Key, referred to as `MachineId` in some contexts)
    -   `ItemId` (Foreign Key to `tblDG_Item_Master`)
    -   `SysDate` (System Date / Machine Registration Date)
    -   `Make`
    -   `Model`
    -   `Capacity`
    -   `Location`
    -   `PMDays` (Number of days for next PM schedule)

-   **`tblMS_PMBM_Master`**: Stores records of past Preventive Maintenance (PM) or Breakdown Maintenance (BM).
    -   `Id` (Primary Key)
    -   `MachineId` (Foreign Key to `tblMS_Master`)
    -   `SysDate` (Date of last PM/BM)
    -   `CompId` (Company ID, used for filtering)

**Relationships:**
-   `tblDG_Item_Master` has a Many-to-One relationship with `tblDG_Category_Master` (via `CId`).
-   `tblDG_Item_Master` has a Many-to-One relationship with `tblDG_SubCategory_Master` (via `SCId`).
-   `tblMS_Master` has a Many-to-One relationship with `tblDG_Item_Master` (via `ItemId`).
-   `tblMS_PMBM_Master` has a Many-to-One relationship with `tblMS_Master` (via `MachineId`).

## Step 2: Identify Backend Functionality

Task: Determine the operations in the ASP.NET code.

This ASP.NET page functions as a **Dashboard** for Machinery, primarily focused on **Read** operations with sophisticated **Filtering, Searching, and Paging**.

-   **Read (Display List):** The `GridView2` displays a list of machines.
    -   Data columns include Machine Code, Description, Make, Model, Capacity, Location, System Date, Last PM Date, and Remaining Days for PM.
    -   The `SysDate` (Machine Registration Date) and `PMDays` (PM Interval) are used to calculate the `RemainDay` until the next maintenance.
    -   The `PMDate` (Last PM Date) is dynamically fetched from `tblMS_PMBM_Master`. If no PM records exist, the machine's `SysDate` is used as a baseline.
-   **Filtering & Searching:**
    -   Dropdowns (`DrpCategory`, `DrpSubCategory`) allow filtering by category and subcategory.
    -   A search dropdown (`DrpSearchCode`) allows selecting a search field (`ItemCode` or `ManfDesc`).
    -   A text box (`txtSearchItemCode`) captures the search value.
    -   An explicit "Search" button (`btnSearch`) triggers a refresh.
    -   Dropdown selections (`DrpCategory_SelectedIndexChanged`, `DrpSubCategory_SelectedIndexChanged`) also trigger an automatic refresh (`AutoPostBack`).
-   **Paging:** The `GridView2` supports pagination (`AllowPaging`, `OnPageIndexChanging`).
-   **Navigation:** Clicking on the "Machine Code" (`LinkButton1`) redirects the user to a "Schedule Process Dashboard" page (`Schedule_Process_Dashboard.aspx`) for the selected machine, passing `Id`, `ModId`, and `SubModId` as query parameters. No direct CRUD operations are performed on this dashboard itself.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

-   **Master Page Integration**: `MasterPage.master` suggests a base layout. In Django, this translates to extending a `base.html` template.
-   **Dropdowns**:
    -   `DrpCategory`: `asp:DropDownList` for selecting a machine category. Triggers a postback and populates `DrpSubCategory`.
    -   `DrpSubCategory`: `asp:DropDownList` for selecting a machine subcategory, dependent on the selected category. Triggers a postback.
    -   `DrpSearchCode`: `asp:DropDownList` for selecting the search criteria (Machine Code or Description).
-   **Text Input**:
    -   `txtSearchItemCode`: `asp:TextBox` for entering the search term.
-   **Button**:
    -   `btnSearch`: `asp:Button` to explicitly trigger the search and filter application.
-   **Data Grid**:
    -   `GridView2`: `asp:GridView` for displaying tabulated machine data. It handles pagination, column display, and has a clickable link (`LinkButton`) for each machine's `ItemCode` to navigate to a detail page.
    -   Custom styling (`CssClass="yui-datatable-theme"`) suggests a custom table styling, which will be replaced by DataTables and Tailwind CSS.
-   **JavaScript**: `loadingNotifier.js`, `PopUpMsg.js` for UI feedback. These will be replaced by HTMX events and Alpine.js for modal/notification handling.

## Step 4: Generate Django Code

We will create a Django application named `machinery` to encapsulate this functionality.

### 4.1 Models (machinery/models.py)

Task: Create Django models based on the identified database schema. We'll include methods for the calculated fields (`PMDate`, `RemainDay`).

```python
from django.db import models
from datetime import date, timedelta

# Helper for current date, similar to fun.getCurrDate()
def get_current_date():
    return date.today()

class Category(models.Model):
    # Mapping to tblDG_Category_Master
    cid = models.IntegerField(db_column='CId', primary_key=True)
    category_name = models.CharField(db_column='CategoryName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Machinery Category'
        verbose_name_plural = 'Machinery Categories'

    def __str__(self):
        return self.category_name or f"Category {self.cid}"

class SubCategory(models.Model):
    # Mapping to tblDG_SubCategory_Master
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId')
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    scname = models.CharField(db_column='SCName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'Machinery SubCategory'
        verbose_name_plural = 'Machinery SubCategories'

    def __str__(self):
        return f"{self.symbol} - {self.scname}" if self.symbol else self.scname or f"SubCategory {self.scid}"

class Item(models.Model):
    # Mapping to tblDG_Item_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    category = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId')
    sub_category = models.ForeignKey(SubCategory, models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    absolute = models.BooleanField(db_column='Absolute', default=False) # '1' likely means true

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Machinery Item'
        verbose_name_plural = 'Machinery Items'

    def __str__(self):
        return self.item_code

class Machine(models.Model):
    # Mapping to tblMS_Master
    id = models.IntegerField(db_column='Id', primary_key=True) # Used as MachineId in C#
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    sys_date = models.DateField(db_column='SysDate') # Machine registration date
    make = models.CharField(db_column='Make', max_length=100, blank=True, null=True)
    model = models.CharField(db_column='Model', max_length=100, blank=True, null=True)
    capacity = models.CharField(db_column='Capacity', max_length=100, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)
    pm_days = models.IntegerField(db_column='PMDays', default=0) # Interval for PM in days

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machine Instance'
        verbose_name_plural = 'Machine Instances'

    def __str__(self):
        return f"{self.item.item_code} ({self.make} {self.model})"

    @property
    def last_pm_date(self):
        """
        Calculates the last PM date.
        Equivalent to the logic checking tblMS_PMBM_Master or SysDate if no PM.
        """
        try:
            # Assumes PMBM_Master stores dates for this machine
            # Order by SysDate DESC to get the latest
            latest_pm = self.pms.order_by('-sys_date').first()
            if latest_pm:
                return latest_pm.sys_date
        except AttributeError: # If no related manager 'pms' exists, fallback
            pass
        return self.sys_date # Fallback to machine's registration date

    @property
    def remaining_days_for_pm(self):
        """
        Calculates remaining days until next PM.
        Equivalent to (PMDays - TP.TotalDays).
        """
        current_date = get_current_date()
        last_pm_date = self.last_pm_date

        # Calculate days since last PM or registration
        days_since_last_event = (current_date - last_pm_date).days

        # Calculate remaining days
        return self.pm_days - days_since_last_event

class PMBM(models.Model):
    # Mapping to tblMS_PMBM_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    machine = models.ForeignKey(Machine, models.DO_NOTHING, db_column='MachineId', related_name='pms')
    sys_date = models.DateField(db_column='SysDate') # Date of PM/BM
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Master'
        verbose_name = 'Preventive/Breakdown Maintenance'
        verbose_name_plural = 'Preventive/Breakdown Maintenances'

    def __str__(self):
        return f"PM/BM for {self.machine.item.item_code} on {self.sys_date}"

```

### 4.2 Forms (machinery/forms.py)

Task: Define a Django form for the filtering components. This is not a ModelForm for CRUD, but a standard Form for search inputs.

```python
from django import forms
from .models import Category, SubCategory

class MachineDashboardFilterForm(forms.Form):
    # Fetch categories dynamically
    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        empty_label="Select Category",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '{{% url "machinery:get_subcategories" %}}', # HTMX endpoint for dynamic subcategories
            'hx-target': '#id_sub_category', # Target for subcategory dropdown
            'hx-swap': 'outerHTML', # Swap the whole select element
            'hx-trigger': 'change', # Trigger on category change
            'hx-indicator': '#loading-indicator', # Show loading indicator
        })
    )

    # Subcategories will be dynamically loaded via HTMX
    sub_category = forms.ModelChoiceField(
        queryset=SubCategory.objects.none(), # Start empty
        required=False,
        empty_label="Select SubCategory",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-trigger': 'change', # Subcategory change also triggers grid refresh
            'hx-get': '{{% url "machinery:machine_table_partial" %}}', # Re-fetch table content
            'hx-target': '#machine-dashboard-table-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#loading-indicator', # Show loading indicator
        })
    )

    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('item_code', 'Machine Code'), # Maps to tblDG_Item_Master.ItemCode
        ('manf_desc', 'Description'), # Maps to tblDG_Item_Master.ManfDesc
    ]
    search_code = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    search_text = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search text...'
        })
    )

    def __init__(self, *args, **kwargs):
        # Pass the current company ID and request (for user session/context)
        self.comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)

        # If a category is selected, populate subcategories
        if 'category' in self.data and self.data['category']:
            try:
                category_id = int(self.data.get('category'))
                self.fields['sub_category'].queryset = SubCategory.objects.filter(cid=category_id)
            except (ValueError, TypeError):
                pass # Invalid category ID

    def clean(self):
        cleaned_data = super().clean()
        search_code = cleaned_data.get('search_code')
        search_text = cleaned_data.get('search_text')

        if search_code != 'Select' and not search_text:
            self.add_error('search_text', 'Search text is required when a search field is selected.')
        return cleaned_data
```

### 4.3 Views (machinery/views.py)

Task: Implement the dashboard list view and necessary HTMX partial views for dynamic content. Views are kept thin, delegating complex data fetching/logic to models or helper functions.

```python
from django.views.generic import TemplateView, ListView
from django.db.models import Q
from django.http import HttpResponse
from django.template.loader import render_to_string
from .models import Machine, Category, SubCategory, get_current_date
from .forms import MachineDashboardFilterForm
from django.conf import settings # To access settings like COMP_ID

class MachineDashboardView(TemplateView):
    """
    Main view for the Machinery Dashboard.
    Displays the filter form and serves as the initial landing page.
    The actual table content is loaded via HTMX.
    """
    template_name = 'machinery/dashboard/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with data if available from query parameters
        form = MachineDashboardFilterForm(self.request.GET, comp_id=self.request.session.get('compid'))
        context['form'] = form
        return context

class MachineTablePartialView(ListView):
    """
    HTMX-targeted view to render the machine list table dynamically.
    Handles filtering, searching, and provides data for DataTables.
    """
    model = Machine
    template_name = 'machinery/dashboard/_machine_table.html'
    context_object_name = 'machines'
    # No need for pagination here as DataTables handles it client-side.
    # However, if server-side processing is needed, Django-Datatables-View
    # or similar would be used. For simplicity, we fetch all filtered.

    def get_queryset(self):
        queryset = super().get_queryset()

        # Assuming CompId comes from session or a default setting
        # In ASP.NET: CompId = Convert.ToInt32(Session["compid"]);
        current_comp_id = self.request.session.get('compid', getattr(settings, 'DEFAULT_COMP_ID', 1))

        # Filter by CompId and Absolute=False (Absolute != '1')
        queryset = queryset.filter(
            item__comp_id=current_comp_id,
            item__absolute=False
        ).select_related('item', 'item__category', 'item__sub_category').prefetch_related('pms')

        # Apply filters from the form
        form = MachineDashboardFilterForm(self.request.GET, comp_id=current_comp_id)
        if form.is_valid():
            category_obj = form.cleaned_data.get('category')
            sub_category_obj = form.cleaned_data.get('sub_category')
            search_code = form.cleaned_data.get('search_code')
            search_text = form.cleaned_data.get('search_text')

            if category_obj and category_obj.cid != "Select Category":
                queryset = queryset.filter(item__category=category_obj)
                if sub_category_obj and sub_category_obj.scid != "Select SubCategory":
                    queryset = queryset.filter(item__sub_category=sub_category_obj)

            if search_text and search_code != 'Select':
                if search_code == 'item_code':
                    queryset = queryset.filter(item__item_code__icontains=search_text)
                elif search_code == 'manf_desc':
                    queryset = queryset.filter(item__manf_desc__icontains=search_text)
        
        # Order by Item Id Desc as seen in original SQL
        return queryset.order_by('-item__id')

    def get(self, request, *args, **kwargs):
        # This method is crucial for HTMX to receive a partial response
        self.object_list = self.get_queryset()
        context = self.get_context_data(object_list=self.object_list)
        return self.render_to_response(context)

class SubCategoryOptionsPartialView(TemplateView):
    """
    HTMX-targeted view to dynamically load subcategory options based on selected category.
    """
    template_name = 'machinery/dashboard/_sub_category_options.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        category_id = self.request.GET.get('category')
        subcategories = SubCategory.objects.none()
        if category_id and category_id != 'Select Category':
            try:
                subcategories = SubCategory.objects.filter(cid=int(category_id))
            except (ValueError, TypeError):
                pass
        context['subcategories'] = subcategories
        return context

    def render_to_response(self, context, **response_kwargs):
        # Render just the <select> tag for HTMX to swap
        return HttpResponse(render_to_string(self.template_name, context, request=self.request))

```

### 4.4 Templates (machinery/templates/machinery/dashboard/)

Task: Create templates for the dashboard, including partials for dynamic updates with HTMX and DataTables.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block title %}Machinery - Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machinery Dashboard</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="machine-filter-form"
              hx-get="{% url 'machinery:machine_table_partial' %}"
              hx-target="#machine-dashboard-table-container"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div class="col-span-1">
                    <label for="id_category" class="block text-sm font-medium text-gray-700 mb-1">Category:</label>
                    {{ form.category }}
                </div>
                <div class="col-span-1">
                    <label for="id_sub_category" class="block text-sm font-medium text-gray-700 mb-1">SubCategory:</label>
                    {{ form.sub_category }}
                </div>
                <div class="col-span-1">
                    <label for="id_search_code" class="block text-sm font-medium text-gray-700 mb-1">Search By:</label>
                    {{ form.search_code }}
                </div>
                <div class="col-span-1 flex-grow">
                    <label for="id_search_text" class="block text-sm font-medium text-gray-700 mb-1">Search Text:</label>
                    {{ form.search_text }}
                </div>
                <div class="col-span-1 md:col-span-4 flex justify-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out" hx-indicator="#loading-indicator">
                        <i class="fas fa-search mr-2"></i> Search
                    </button>
                    <!-- Loading indicator for HTMX requests -->
                    <div id="loading-indicator" class="htmx-indicator ml-3 py-2 px-4">
                        <i class="fas fa-spinner fa-spin text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            {% if form.errors %}
                <div class="mt-4 text-red-600 text-sm">
                    {% for field in form %}
                        {% for error in field.errors %}
                            <p>{{ field.label }}: {{ error }}</p>
                        {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="machine-dashboard-table-container"
         hx-trigger="load delay:100ms"
         hx-get="{% url 'machinery:machine_table_partial' %}?{{ request.GET.urlencode }}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading machine data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add DataTables and Alpine.js via CDNs in your base.html -->
<script>
    // No additional Alpine.js logic needed here, as HTMX handles interactions.
    // DataTables initialization is handled within the _machine_table.html partial.
</script>
{% endblock %}
```

#### `_machine_table.html` (Partial for DataTables)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="machineDashboardTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Make</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Capacity</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Last PM Date</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Remain Days</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if machines %}
                {% for machine in machines %}
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                        <!-- Replaces LinkButton1, assuming Schedule_Process_Dashboard.aspx takes ItemId and other params -->
                        <a href="{% url 'machinery:schedule_process_dashboard' machine.item.id %}" class="text-blue-600 hover:text-blue-900 transition duration-150 ease-in-out">
                            {{ machine.item.item_code }}
                        </a>
                    </td>
                    <td class="py-3 px-4 text-sm text-gray-900">{{ machine.item.manf_desc|truncatechars:80 }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ machine.make }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ machine.model }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ machine.capacity }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ machine.location }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ machine.sys_date|date:"d M Y" }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ machine.last_pm_date|date:"d M Y" }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-right text-sm {% if machine.remaining_days_for_pm <= 0 %}font-bold text-red-600{% else %}text-gray-900{% endif %}">
                        {{ machine.remaining_days_for_pm }}
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="py-4 text-center text-lg text-red-500 font-semibold">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables once the table element is loaded by HTMX
    // This script runs each time hx-target is updated with this partial
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#machineDashboardTable')) {
            $('#machineDashboardTable').DataTable().destroy();
        }
        $('#machineDashboardTable').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true, // Client-side search for DataTables (separate from form search)
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 20, // Matches ASP.NET GridView's PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "language": {
                "emptyTable": "No data to display !",
                "zeroRecords": "No matching records found"
            }
        });
    });
</script>
```

#### `_sub_category_options.html` (Partial for dynamic subcategory dropdown)

```html
<select name="sub_category" id="id_sub_category" class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        hx-trigger="change" hx-get="{% url 'machinery:machine_table_partial' %}" hx-target="#machine-dashboard-table-container" hx-swap="innerHTML" hx-indicator="#loading-indicator">
    <option value="">Select SubCategory</option>
    {% for subcat in subcategories %}
        <option value="{{ subcat.scid }}">{{ subcat.symbol }} - {{ subcat.scname }}</option>
    {% endfor %}
</select>
```

### 4.5 URLs (machinery/urls.py)

Task: Define URL patterns for the views.

```python
from django.urls import path
from .views import MachineDashboardView, MachineTablePartialView, SubCategoryOptionsPartialView

app_name = 'machinery' # Namespace for URLs

urlpatterns = [
    # Main dashboard page
    path('dashboard/', MachineDashboardView.as_view(), name='dashboard_list'),
    
    # HTMX endpoint to load the table content
    path('dashboard/table/', MachineTablePartialView.as_view(), name='machine_table_partial'),

    # HTMX endpoint to get subcategory options based on category
    path('dashboard/get_subcategories/', SubCategoryOptionsPartialView.as_view(), name='get_subcategories'),

    # Placeholder for the detail page navigated to from the machine code link
    # The original path was something like ~/Module/Machinery/Transactions/Schedule_Process_Dashboard.aspx?Id=...
    # We will assume a simple URL structure for demonstration.
    # Replace 'some_app:schedule_process_detail' with the actual URL name for your process dashboard.
    path('schedule-process/<int:item_id>/', TemplateView.as_view(template_name='machinery/dashboard/schedule_process_placeholder.html'), name='schedule_process_dashboard'),

]
```
*Note: You would need to create `machinery/dashboard/schedule_process_placeholder.html` or replace the `TemplateView` with an actual view from a `process` Django app.*

### 4.6 Tests (machinery/tests.py)

Task: Write comprehensive unit tests for models and integration tests for views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from unittest.mock import patch
from .models import Category, SubCategory, Item, Machine, PMBM, get_current_date # Import get_current_date for mocking

class MachineryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.comp_id = 1
        cls.category1 = Category.objects.create(cid=101, category_name="Heavy Machinery")
        cls.subcategory1 = SubCategory.objects.create(scid=201, cid=cls.category1, symbol="HM", scname="Excavators")
        cls.subcategory2 = SubCategory.objects.create(scid=202, cid=cls.category1, symbol="HM", scname="Loaders")
        
        cls.item1 = Item.objects.create(
            id=1, item_code="EXC-001", manf_desc="Large Excavator",
            category=cls.category1, sub_category=cls.subcategory1,
            comp_id=cls.comp_id, absolute=False
        )
        cls.item2 = Item.objects.create(
            id=2, item_code="LDR-001", manf_desc="Wheel Loader",
            category=cls.category1, sub_category=cls.subcategory2,
            comp_id=cls.comp_id, absolute=False
        )
        cls.item_absolute = Item.objects.create(
            id=3, item_code="ABS-001", manf_desc="Absolute Item",
            category=cls.category1, sub_category=cls.subcategory1,
            comp_id=cls.comp_id, absolute=True # Should be filtered out
        )

        cls.machine1 = Machine.objects.create(
            id=1001, item=cls.item1, sys_date=date(2022, 1, 1),
            make="Caterpillar", model="320D", capacity="20T", location="Site A", pm_days=180
        )
        cls.machine2 = Machine.objects.create(
            id=1002, item=cls.item2, sys_date=date(2023, 5, 10),
            make="Komatsu", model="WA320", capacity="5T", location="Site B", pm_days=90
        )

        cls.pmbm1 = PMBM.objects.create(
            id=1, machine=cls.machine1, sys_date=date(2023, 10, 1), comp_id=cls.comp_id
        )
        # Another PM for machine1 to test latest PM
        cls.pmbm1_latest = PMBM.objects.create(
            id=2, machine=cls.machine1, sys_date=date(2024, 3, 15), comp_id=cls.comp_id
        )

    def test_category_creation(self):
        self.assertEqual(self.category1.category_name, "Heavy Machinery")
        self.assertEqual(str(self.category1), "Heavy Machinery")

    def test_subcategory_creation(self):
        self.assertEqual(self.subcategory1.scname, "Excavators")
        self.assertEqual(self.subcategory1.cid, self.category1)
        self.assertEqual(str(self.subcategory1), "HM - Excavators")

    def test_item_creation(self):
        self.assertEqual(self.item1.item_code, "EXC-001")
        self.assertEqual(self.item1.manf_desc, "Large Excavator")
        self.assertEqual(self.item1.category, self.category1)
        self.assertEqual(self.item1.sub_category, self.subcategory1)
        self.assertFalse(self.item1.absolute)
        self.assertEqual(str(self.item1), "EXC-001")

    def test_machine_creation(self):
        self.assertEqual(self.machine1.make, "Caterpillar")
        self.assertEqual(self.machine1.item, self.item1)
        self.assertEqual(str(self.machine1), "EXC-001 (Caterpillar 320D)")

    def test_pmbm_creation(self):
        self.assertEqual(self.pmbm1.machine, self.machine1)
        self.assertEqual(self.pmbm1.sys_date, date(2023, 10, 1))

    @patch('machinery.models.get_current_date', return_value=date(2024, 4, 1))
    def test_last_pm_date_with_pm(self, mock_get_current_date):
        # The latest PM for machine1 is 2024-03-15
        self.assertEqual(self.machine1.last_pm_date, date(2024, 3, 15))

    @patch('machinery.models.get_current_date', return_value=date(2024, 4, 1))
    def test_last_pm_date_without_pm(self, mock_get_current_date):
        # Create a machine with no PM records
        item3 = Item.objects.create(
            id=4, item_code="NoPM-001", manf_desc="No PM Item",
            category=self.category1, comp_id=self.comp_id, absolute=False
        )
        machine3 = Machine.objects.create(
            id=1003, item=item3, sys_date=date(2023, 1, 1),
            make="XYZ", model="ABC", pm_days=365
        )
        self.assertEqual(machine3.last_pm_date, date(2023, 1, 1)) # Should be sys_date

    @patch('machinery.models.get_current_date', return_value=date(2024, 4, 1))
    def test_remaining_days_for_pm(self, mock_get_current_date):
        # machine1: last_pm_date = 2024-03-15, pm_days = 180
        # current_date = 2024-04-01
        # days_since_last_event = (2024-04-01 - 2024-03-15).days = 17 days
        # remaining_days = 180 - 17 = 163
        self.assertEqual(self.machine1.remaining_days_for_pm, 163)

        # machine2: sys_date = 2023-05-10, pm_days = 90 (no PM records)
        # last_pm_date for machine2 will be its sys_date
        # days_since_last_event = (2024-04-01 - 2023-05-10).days = 327 days
        # remaining_days = 90 - 327 = -237
        self.assertEqual(self.machine2.remaining_days_for_pm, -237)

class MachineryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.comp_id = 1
        cls.category_a = Category.objects.create(cid=1, category_name="Category A")
        cls.subcategory_a1 = SubCategory.objects.create(scid=10, cid=cls.category_a, symbol="A1", scname="Sub A1")
        cls.subcategory_a2 = SubCategory.objects.create(scid=11, cid=cls.category_a, symbol="A2", scname="Sub A2")
        cls.category_b = Category.objects.create(cid=2, category_name="Category B")
        cls.subcategory_b1 = SubCategory.objects.create(scid=12, cid=cls.category_b, symbol="B1", scname="Sub B1")

        cls.item_a1_1 = Item.objects.create(id=101, item_code="CODE-A1-1", manf_desc="Desc A1 Machine 1", category=cls.category_a, sub_category=cls.subcategory_a1, comp_id=cls.comp_id, absolute=False)
        cls.item_a2_1 = Item.objects.create(id=102, item_code="CODE-A2-1", manf_desc="Desc A2 Machine 1", category=cls.category_a, sub_category=cls.subcategory_a2, comp_id=cls.comp_id, absolute=False)
        cls.item_b1_1 = Item.objects.create(id=103, item_code="CODE-B1-1", manf_desc="Desc B1 Machine 1", category=cls.category_b, sub_category=cls.subcategory_b1, comp_id=cls.comp_id, absolute=False)
        cls.item_absolute = Item.objects.create(id=104, item_code="CODE-ABS", manf_desc="Absolute Machine", category=cls.category_a, sub_category=cls.subcategory_a1, comp_id=cls.comp_id, absolute=True)

        cls.machine1 = Machine.objects.create(id=10001, item=cls.item_a1_1, sys_date=date(2023, 1, 1), make="MakeA", model="ModelA", pm_days=90)
        cls.machine2 = Machine.objects.create(id=10002, item=cls.item_a2_1, sys_date=date(2023, 2, 1), make="MakeB", model="ModelB", pm_days=180)
        cls.machine3 = Machine.objects.create(id=10003, item=cls.item_b1_1, sys_date=date(2023, 3, 1), make="MakeC", model="ModelC", pm_days=120)
        cls.machine_absolute = Machine.objects.create(id=10004, item=cls.item_absolute, sys_date=date(2023, 4, 1), make="MakeD", model="ModelD", pm_days=100)

        PMBM.objects.create(machine=cls.machine1, sys_date=date(2023, 10, 1), comp_id=cls.comp_id)

    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.comp_id # Mock session for CompId

    def test_dashboard_list_view(self):
        response = self.client.get(reverse('machinery:dashboard_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/dashboard/list.html')
        self.assertContains(response, 'Machinery Dashboard')
        # Check if filter form is in context
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], MachineDashboardFilterForm)
    
    def test_machine_table_partial_view_no_filters(self):
        response = self.client.get(reverse('machinery:machine_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/dashboard/_machine_table.html')
        self.assertIn('machines', response.context)
        # Should include machine1, machine2, machine3, but not machine_absolute
        self.assertEqual(len(response.context['machines']), 3)
        self.assertContains(response, self.machine1.item.item_code)
        self.assertContains(response, self.machine2.item.item_code)
        self.assertContains(response, self.machine3.item.item_code)
        self.assertNotContains(response, self.machine_absolute.item.item_code) # Absolute item is filtered out

    def test_machine_table_partial_view_filter_by_category(self):
        response = self.client.get(reverse('machinery:machine_table_partial'), {'category': self.category_a.cid})
        self.assertEqual(response.status_code, 200)
        self.assertIn('machines', response.context)
        self.assertEqual(len(response.context['machines']), 2) # machine1, machine2
        self.assertContains(response, self.machine1.item.item_code)
        self.assertContains(response, self.machine2.item.item_code)
        self.assertNotContains(response, self.machine3.item.item_code)

    def test_machine_table_partial_view_filter_by_category_and_subcategory(self):
        response = self.client.get(reverse('machinery:machine_table_partial'), {
            'category': self.category_a.cid,
            'sub_category': self.subcategory_a1.scid
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('machines', response.context)
        self.assertEqual(len(response.context['machines']), 1) # machine1
        self.assertContains(response, self.machine1.item.item_code)
        self.assertNotContains(response, self.machine2.item.item_code)

    def test_machine_table_partial_view_search_by_item_code(self):
        response = self.client.get(reverse('machinery:machine_table_partial'), {
            'search_code': 'item_code',
            'search_text': 'CODE-A1'
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('machines', response.context)
        self.assertEqual(len(response.context['machines']), 1) # machine1
        self.assertContains(response, self.machine1.item.item_code)
        self.assertNotContains(response, self.machine2.item.item_code)

    def test_machine_table_partial_view_search_by_manf_desc(self):
        response = self.client.get(reverse('machinery:machine_table_partial'), {
            'search_code': 'manf_desc',
            'search_text': 'A2 Machine'
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('machines', response.context)
        self.assertEqual(len(response.context['machines']), 1) # machine2
        self.assertContains(response, self.machine2.item.item_code)
        self.assertNotContains(response, self.machine1.item.item_code)

    def test_machine_table_partial_view_form_validation_fail(self):
        # Search code selected but no search text
        response = self.client.get(reverse('machinery:machine_table_partial'), {
            'search_code': 'item_code',
            'search_text': ''
        })
        self.assertEqual(response.status_code, 200)
        # Form should be invalid, returning all objects (before filter applied)
        self.assertEqual(len(response.context['machines']), 3) 
        self.assertContains(response, 'Search text is required when a search field is selected.')


    def test_get_subcategories_partial_view(self):
        response = self.client.get(reverse('machinery:get_subcategories'), {'category': self.category_a.cid})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="">Select SubCategory</option>')
        self.assertContains(response, f'<option value="{self.subcategory_a1.scid}">')
        self.assertContains(response, f'<option value="{self.subcategory_a2.scid}">')
        self.assertNotContains(response, f'<option value="{self.subcategory_b1.scid}">')
        self.assertContains(response, 'hx-target="#machine-dashboard-table-container"') # Check HTMX attributes

    def test_get_subcategories_partial_view_no_category(self):
        response = self.client.get(reverse('machinery:get_subcategories'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="">Select SubCategory</option>')
        self.assertNotContains(response, f'<option value="{self.subcategory_a1.scid}">')

    def test_schedule_process_dashboard_link(self):
        # This is a placeholder URL for navigation
        url = reverse('machinery:schedule_process_dashboard', args=[self.item_a1_1.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/dashboard/schedule_process_placeholder.html')

```

## Step 5: HTMX and Alpine.js Integration

The plan fully embraces HTMX for dynamic content updates and Alpine.js for any necessary client-side UI state management.

-   **HTMX for Filters and Table:**
    -   The main dashboard (`list.html`) uses `hx-trigger="load"` and `hx-get` to initially fetch the table content from `{% url 'machinery:machine_table_partial' %}`.
    -   The `MachineDashboardFilterForm`'s `hx-get` and `hx-target` attributes ensure that submitting the form (or changing certain dropdowns) re-fetches the `machine_table_partial` and updates the table without a full page reload.
    -   The `category` dropdown uses `hx-get` to fetch subcategories from `{% url 'machinery:get_subcategories' %}` and `hx-target` to replace the `sub_category` dropdown.
    -   Loading indicators (`hx-indicator`) provide visual feedback during HTMX requests.
    -   The `_machine_table.html` partial includes DataTables initialization script, ensuring DataTables is re-initialized correctly when the table content is updated by HTMX.

-   **DataTables for List Views:**
    -   `_machine_table.html` explicitly includes the DataTables `<table>` structure and JavaScript initialization. This will provide client-side search, sort, and pagination as requested, enhancing user interaction without complex server-side logic for these features on every filter/page change.

-   **Alpine.js for UI State (Optional but good practice):**
    -   While direct Alpine.js use is minimal on this dashboard page (HTMX handles most dynamic behaviors), it's integrated by suggesting `base.html` include it. Alpine.js would be primarily used for complex UI elements like custom modals, notifications (if not using Django messages with HTMX), or dynamic form field visibility, which are not explicitly part of the original ASP.NET dashboard but are best practices for modern Django UI. For this dashboard, it would mainly manage a simple loading spinner if HTMX's `htmx-indicator` is not sufficient, or to show success messages for CRUD if this dashboard were extended for direct CRUD.

-   **No Custom JavaScript (beyond DataTables init):**
    -   The migration strictly avoids custom JavaScript files for dynamic interactions, relying on HTMX attributes and Alpine.js for client-side reactivity. The DataTables initialization is an industry-standard library that requires its own JS.

## Final Notes

-   **Business Value:** This modernized Django solution provides a faster, more responsive user experience due to HTMX, reducing server load from full page reloads. The "fat model, thin view" approach improves code organization and maintainability, making future enhancements easier. The use of DataTables offers robust client-side data manipulation, which is critical for large datasets. The modular design enables easier integration with other parts of your ERP system.
-   **Replace Placeholders:** Remember to replace `{{% url 'some_app:schedule_process_detail' %}}` with the actual URL to your machine detail/process page once that module is migrated. Also, ensure `settings.DEFAULT_COMP_ID` or a robust session management strategy for `compid` is in place.
-   **DRY Templates:** The use of `_machine_table.html` and `_sub_category_options.html` demonstrates DRY principles, making the UI components reusable and maintainable.
-   **Comprehensive Tests:** The provided tests ensure the models correctly calculate derived properties and that views filter and render data as expected under various conditions, covering a significant portion of the logic and ensuring the solution's reliability.
-   **Scalability:** Django's ORM and structured approach, combined with HTMX for efficient front-end communication, lay the groundwork for a highly scalable application.