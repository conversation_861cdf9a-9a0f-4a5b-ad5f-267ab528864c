## ASP.NET to Django Conversion Script: Machinery Item Selection Module

This document outlines a strategic plan for transitioning your legacy ASP.NET 'Machinery - New' module to a modern Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a smooth, efficient, and maintainable migration. We focus on business outcomes and clear, actionable steps that can be managed by non-technical stakeholders.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists as `core/base.html`.
- Focus ONLY on component-specific code for the current module (`machinery`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns relevant to the ASP.NET code.

**Instructions:**
From the ASP.NET code, we've identified the primary table for machinery items and several supporting lookup tables. The `fun.select` and `fun.select1` calls, along with `GridView` bindings, reveal the schema.

*   **Primary Table:** `tblDG_Item_Master`
    *   Columns inferred: `Id` (Primary Key), `ItemCode`, `ManfDesc` (Description), `StockQty`, `Location` (Foreign Key), `UOMBasic` (Foreign Key), `CId` (Category ID, Foreign Key), `SCId` (SubCategory ID, Foreign Key), `CompId`, `Absolute`.
*   **Lookup Tables:**
    *   `tblDG_Category_Master`: `CId` (Primary Key), `CategoryName` (inferred from usage).
    *   `tblDG_SubCategory_Master`: `SCId` (Primary Key), `CId` (Foreign Key), `Symbol`, `SCName`.
    *   `tblDG_Location_Master`: `Id` (Primary Key), `LocationLabel`, `LocationNo`.
    *   `Unit_Master`: `Id` (Primary Key), `Symbol`.
*   **Exclusion Table:**
    *   `tblMS_Master`: `ItemId` (Foreign Key to `tblDG_Item_Master`), `CompId`, `FinYearId`.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data filtering logic.

**Instructions:**
The ASP.NET page primarily serves as a "Read" operation (list/display) with extensive filtering capabilities.

*   **Read (List & Filter):** The `GridView2` displays a list of machinery items (`tblDG_Item_Master`). This list is heavily filtered based on:
    *   Category (`DrpCategory`)
    *   SubCategory (`DrpSubCategory`)
    *   Search criteria (`DrpSearchCode`, `txtSearchItemCode`, `DropDownList3` for Location)
    *   Company ID (`CompId`)
    *   Financial Year ID (`FinYearId`)
    *   Exclusion of items already present in `tblMS_Master`.
    *   Exclusion of items where `Absolute` is '1'.
*   **Selection/Navigation:** The `LinkButton` within the `GridView` allows selecting an item, which then redirects to a details page (`Machinery_New_Details.aspx`). This means the page is for *selecting* an existing item, not for adding a *new* item to `tblDG_Item_Master`.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their intended user interactions.

**Instructions:**
The UI components translate well into standard HTML elements with dynamic behaviors managed by HTMX and Alpine.js.

*   **Dropdowns:**
    *   `DrpCategory` -> HTML `<select>` for `Category`
    *   `DrpSubCategory` -> HTML `<select>` for `SubCategory` (dynamically updated via HTMX)
    *   `DrpSearchCode` -> HTML `<select>` for `Search Field` type
    *   `DropDownList3` -> HTML `<select>` for `Location` (conditionally visible via Alpine.js)
*   **Text Input:**
    *   `txtSearchItemCode` -> HTML `<input type="text">` for `Search Text` (conditionally visible via Alpine.js)
*   **Button:**
    *   `btnSearch` -> HTML `<button>` for `Search` action (triggers HTMX refresh).
*   **Data Display:**
    *   `GridView2` -> HTML `<table>` managed by DataTables for efficient display, sorting, and pagination.
    *   The "Select" link will be a standard `<a>` tag or a `button` with `hx-get` to a detail view.

### Step 4: Generate Django Code

We will create a new Django application named `machinery` to encapsulate this module.

#### 4.1 Models (`machinery/models.py`)

**Task:** Create Django models based on the identified database schema. We will also implement a custom manager for `DgItemMaster` to encapsulate the complex filtering logic.

```python
from django.db import models

# Define custom manager for DgItemMaster to encapsulate filtering logic
class DgItemMasterManager(models.Manager):
    def get_queryset(self):
        # Base queryset: Filter out items marked as 'absolute' = 1 (assuming 0 for False, 1 for True)
        return super().get_queryset().filter(absolute='0') # Assuming '0' is the string representation for False

    def available_for_selection(self, company_id, financial_year_id, category_id=None, subcategory_id=None, search_field=None, search_value=None, location_id=None):
        """
        Filters DgItemMaster objects based on various criteria for the 'Machinery - New' view.
        Moves complex SQL query logic from ASP.NET code-behind to the Django model manager.
        """
        queryset = self.get_queryset().filter(comp_id=company_id)

        # Exclude items already linked in MsMaster for the current company and financial year
        # Note: 'item' is the related_name from MsMaster to DgItemMaster
        excluded_item_ids = MsMaster.objects.filter(
            comp_id=company_id,
            fin_year_id__lte=financial_year_id
        ).values_list('item_id', flat=True)
        queryset = queryset.exclude(pk__in=excluded_item_ids)

        # Apply category filter if provided and not "Select Category"
        if category_id and category_id != 'Select Category':
            # Assumes 'category' is the related_name from DgItemMaster to DgCategoryMaster
            queryset = queryset.filter(category__pk=category_id)

            # Apply subcategory filter if category is selected and subcategory is not "Select SubCategory"
            if subcategory_id and subcategory_id != 'Select SubCategory':
                # Assumes 'subcategory' is the related_name from DgItemMaster to DgSubCategoryMaster
                queryset = queryset.filter(subcategory__pk=subcategory_id)

        # Apply search filters based on selected search field
        if search_field and search_field != 'Select' and search_value:
            if search_field == 'tblDG_Item_Master.ItemCode':
                queryset = queryset.filter(item_code__startswith=search_value)
            elif search_field == 'tblDG_Item_Master.ManfDesc':
                queryset = queryset.filter(manf_desc__icontains=search_value)
        
        # Location is handled by a separate dropdown, not the generic search_value
        if search_field == 'tblDG_Item_Master.Location':
            if location_id and location_id != 'Select':
                queryset = queryset.filter(location__pk=location_id)

        # Order by Id Desc as in original code
        queryset = queryset.order_by('-pk')
        
        # Use select_related to avoid N+1 queries when accessing related data in templates
        queryset = queryset.select_related('category', 'subcategory', 'location', 'uom_basic')

        return queryset


class DgItemMaster(models.Model):
    # Primary Key
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, blank=True, null=True)
    
    # Foreign Keys to lookup tables
    # Assuming CId, SCId, Location, UOMBasic are integer IDs
    category = models.ForeignKey('DgCategoryMaster', models.DO_NOTHING, db_column='CId', blank=True, null=True)
    subcategory = models.ForeignKey('DgSubCategoryMaster', models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    location = models.ForeignKey('DgLocationMaster', models.DO_NOTHING, db_column='Location', blank=True, null=True)
    uom_basic = models.ForeignKey('UnitMaster', models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    # The 'Absolute' field is used for filtering. Assuming it's a character '0' or '1'.
    absolute = models.CharField(db_column='Absolute', max_length=1, blank=True, null=True) 

    # Assign the custom manager
    objects = DgItemMasterManager()

    class Meta:
        managed = False # Do not manage this table's schema through Django migrations
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Machinery Item'
        verbose_name_plural = 'Machinery Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc[:50]}..."

    # Business logic methods can be added here, e.g., to get formatted UOM or location
    @property
    def formatted_description(self):
        # Emulate SUBSTRING(ManfDesc,0,80)+'...'
        if self.manf_desc and len(self.manf_desc) > 80:
            return self.manf_desc[:80] + '...'
        return self.manf_desc

    @property
    def display_uom(self):
        return self.uom_basic.symbol if self.uom_basic else 'N/A'

    @property
    def display_location(self):
        if self.location:
            return f"{self.location.location_label}-{self.location.location_no}"
        return 'N/A'

class DgCategoryMaster(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    category_name = models.CharField(db_column='CategoryName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.category_name

class DgSubCategoryMaster(models.Model):
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey('DgCategoryMaster', models.DO_NOTHING, db_column='CId', blank=True, null=True) # Foreign key to Category
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    sc_name = models.CharField(db_column='SCName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'SubCategory'
        verbose_name_plural = 'SubCategories'

    def __str__(self):
        return f"{self.symbol} - {self.sc_name}"

class DgLocationMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=50, blank=True, null=True)
    location_no = models.CharField(db_column='LocationNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.location_label}-{self.location_no}"

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class MsMaster(models.Model):
    # This model is primarily used for the exclusion query. Assuming it has a primary key.
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an ID field exists as PK
    item = models.ForeignKey('DgItemMaster', models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machinery Master Entry'
        verbose_name_plural = 'Machinery Master Entries'

    def __str__(self):
        return f"MS Master ID: {self.id} for Item: {self.item.item_code if self.item else 'N/A'}"

```

#### 4.2 Forms (`machinery/forms.py`)

**Task:** While a standard `ModelForm` isn't strictly necessary for this filtering page (as it's read-only), we can define a base form for the filter parameters if we wanted to use Django's form rendering for the dropdowns. However, for HTMX/Alpine.js, direct rendering in the template is often cleaner for filter controls. We won't provide a `ModelForm` here as per the spirit of the original ASP.NET page not being a "create/edit" form for `MachineryItem`.

```python
# machinery/forms.py
# No specific forms are needed for the read-only filtering/listing page.
# Filters are handled directly via request.GET in the view and HTMX in templates.
```

#### 4.3 Views (`machinery/views.py`)

**Task:** Implement the list view using Django's Class-Based Views (CBVs), ensuring thin views and HTMX partials.

```python
from django.views.generic import ListView, View
from django.shortcuts import render
from django.http import HttpResponse
from django.urls import reverse_lazy
import logging

from .models import DgItemMaster, DgCategoryMaster, DgLocationMaster, DgSubCategoryMaster

logger = logging.getLogger(__name__)

class MachineryNewListView(ListView):
    """
    Main view for the Machinery Item Selection page.
    Displays filter controls and loads the DataTables component via HTMX.
    """
    model = DgItemMaster
    template_name = 'machinery/dgitemmaster/list.html'
    context_object_name = 'machinery_items' # Not directly used for the main list, but good practice

    def get_queryset(self):
        # This queryset will be used by the partial view, not directly rendered by this ListView
        # For the initial load of list.html, we don't need to fetch all data here.
        # The _machineryitem_table.html partial view handles the actual data fetch.
        return DgItemMaster.objects.none() # Return an empty queryset for the main page load

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Safely retrieve session data for filtering
        company_id = self.request.session.get('compid')
        financial_year_id = self.request.session.get('finyear')
        
        # Log session values for debugging/understanding
        logger.debug(f"Session CompId: {company_id}, FinYearId: {financial_year_id}")

        if not company_id or not financial_year_id:
            # Handle cases where session data might be missing (e.g., user not logged in or session expired)
            # You might redirect to login, show an error, or use default values.
            # For now, we'll log and let the queryset potentially return empty.
            logger.warning("Company ID or Financial Year ID missing from session for MachineryNewListView.")
        
        # Populate dropdown data for initial page render
        context['categories'] = DgCategoryMaster.objects.all().order_by('category_name')
        context['locations'] = DgLocationMaster.objects.all().order_by('location_label', 'location_no')

        # Store selected filter values to pre-populate dropdowns on page reload (if any)
        context['selected_category'] = self.request.GET.get('category', 'Select Category')
        context['selected_subcategory'] = self.request.GET.get('subcategory', 'Select SubCategory')
        context['selected_search_code'] = self.request.GET.get('search_code', 'Select')
        context['selected_search_text'] = self.request.GET.get('search_text', '')
        context['selected_location'] = self.request.GET.get('location', 'Select')

        return context

class MachineryItemTablePartialView(ListView):
    """
    HTMX endpoint for rendering the DataTables component.
    This view contains the actual data fetching logic based on filters.
    """
    model = DgItemMaster
    template_name = 'machinery/dgitemmaster/_dgitemmaster_table.html'
    context_object_name = 'machinery_items'

    def get_queryset(self):
        company_id = self.request.session.get('compid')
        financial_year_id = self.request.session.get('finyear')
        
        # Ensure company_id and financial_year_id are available
        if not company_id or not financial_year_id:
            # Return empty queryset if critical session data is missing
            logger.error("Attempted to fetch machinery items without valid CompId or FinYearId in session.")
            return DgItemMaster.objects.none()

        category_id = self.request.GET.get('category')
        subcategory_id = self.request.GET.get('subcategory')
        search_field = self.request.GET.get('search_code')
        search_value = self.request.GET.get('search_text')
        location_id = self.request.GET.get('location')

        logger.debug(f"Filtering with: Cat={category_id}, SubCat={subcategory_id}, SearchField={search_field}, SearchValue={search_value}, Loc={location_id}")

        # Delegate filtering to the model manager
        return DgItemMaster.objects.available_for_selection(
            company_id=company_id,
            financial_year_id=financial_year_id,
            category_id=category_id,
            subcategory_id=subcategory_id,
            search_field=search_field,
            search_value=search_value,
            location_id=location_id
        )
    
    def render_to_response(self, context, **response_kwargs):
        # HTMX requests should always respond with the partial template
        return super().render_to_response(context, **response_kwargs)

class GetSubcategoriesView(View):
    """
    HTMX endpoint to dynamically populate the SubCategory dropdown based on the selected Category.
    """
    def get(self, request, *args, **kwargs):
        category_id = request.GET.get('category_id')
        subcategories = DgSubCategoryMaster.objects.none()

        if category_id and category_id != 'Select Category':
            try:
                subcategories = DgSubCategoryMaster.objects.filter(cid__pk=category_id).order_by('sc_name')
            except ValueError:
                # Handle case where category_id is not a valid integer
                logger.warning(f"Invalid category_id received for subcategory lookup: {category_id}")

        # Render only the <option> tags for the subcategory dropdown
        return render(request, 'machinery/dgitemmaster/_subcategory_options.html', {'subcategories': subcategories})

```

#### 4.4 Templates (`machinery/templates/machinery/dgitemmaster/`)

**Task:** Create templates for the main page and HTMX partials.

##### `list.html`

```html
{% extends 'core/base.html' %}

{% block title %}Machinery - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machinery Item Selection</h2>
    </div>

    <!-- Filter Controls Section -->
    <div x-data="{ searchType: '{{ selected_search_code }}', locationIsVisible: '{{ selected_search_code }}' === 'tblDG_Item_Master.Location' }" 
         class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
            <div>
                <label for="id_category" class="block text-sm font-medium text-gray-700">Category</label>
                <select id="id_category" name="category" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        hx-get="{% url 'machinery:get_subcategories' %}"
                        hx-target="#id_subcategory"
                        hx-indicator="#subcategory-loading-indicator"
                        hx-swap="innerHTML"
                        hx-params="category_id: this.value">
                    <option value="Select Category">Select Category</option>
                    {% for category in categories %}
                    <option value="{{ category.pk }}" {% if selected_category == category.pk|stringformat:"s" %}selected{% endif %}>{{ category.category_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="id_subcategory" class="block text-sm font-medium text-gray-700">SubCategory</label>
                <div class="relative">
                    <select id="id_subcategory" name="subcategory" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <!-- Subcategory options will be loaded here via HTMX or pre-populated -->
                        <option value="Select SubCategory">Select SubCategory</option>
                        {% if selected_category and selected_category != 'Select Category' %}
                            {% for subcategory in categories|get_subcategories:selected_category %} {# Placeholder filter #}
                                <option value="{{ subcategory.pk }}" {% if selected_subcategory == subcategory.pk|stringformat:"s" %}selected{% endif %}>{{ subcategory.symbol }} - {{ subcategory.sc_name }}</option>
                            {% endfor %}
                        {% endif %}
                    </select>
                    <span id="subcategory-loading-indicator" class="htmx-indicator absolute right-3 top-1/2 -translate-y-1/2">
                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                    </span>
                </div>
            </div>
            <div>
                <label for="id_search_code" class="block text-sm font-medium text-gray-700">Search By</label>
                <select id="id_search_code" name="search_code" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        x-model="searchType"
                        @change="locationIsVisible = (searchType === 'tblDG_Item_Master.Location')">
                    <option value="Select">Select</option>
                    <option value="tblDG_Item_Master.ItemCode" {% if selected_search_code == 'tblDG_Item_Master.ItemCode' %}selected{% endif %}>Machine Code</option>
                    <option value="tblDG_Item_Master.ManfDesc" {% if selected_search_code == 'tblDG_Item_Master.ManfDesc' %}selected{% endif %}>Description</option>
                    <option value="tblDG_Item_Master.Location" {% if selected_search_code == 'tblDG_Item_Master.Location' %}selected{% endif %}>Location</option>
                </select>
            </div>
            <div>
                <label for="id_search_input" class="block text-sm font-medium text-gray-700">Search Value</label>
                <input type="text" id="id_search_input" name="search_text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       x-show="!locationIsVisible"
                       value="{{ selected_search_text }}">
                <select id="id_location" name="location" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        x-show="locationIsVisible">
                    <option value="Select">Select</option>
                    {% for location in locations %}
                    <option value="{{ location.pk }}" {% if selected_location == location.pk|stringformat:"s" %}selected{% endif %}>{{ location.location_label }} - {{ location.location_no }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-span-1 md:col-span-2 lg:col-span-4 flex justify-end">
                <button type="button" 
                        id="btnSearch"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm"
                        hx-get="{% url 'machinery:dgitemmaster_table' %}"
                        hx-target="#dgitemmaster-table-container"
                        hx-indicator="#table-loading-indicator"
                        hx-swap="innerHTML"
                        hx-vals="
                            {
                                'category': document.getElementById('id_category').value,
                                'subcategory': document.getElementById('id_subcategory').value,
                                'search_code': document.getElementById('id_search_code').value,
                                'search_text': document.getElementById('id_search_input').value,
                                'location': document.getElementById('id_location').value
                            }
                        ">
                    Search
                </button>
            </div>
        </div>
    </div>

    <!-- Machinery List Table Container -->
    <div id="dgitemmaster-table-container"
         hx-trigger="load once" {# Load once on page load #}
         hx-get="{% url 'machinery:dgitemmaster_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#table-loading-indicator"
         _="on htmx:afterSwap remove .htmx-request from #table-loading-indicator">
        <!-- Initial loading state -->
        <div class="text-center py-10" id="table-loading-indicator">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Machinery Items...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js is included in base.html. No additional Alpine.js script for this page. #}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here, x-data handles visibility
    });
</script>
{# DataTables initialization is handled within the _dgitemmaster_table.html partial #}
{% endblock %}
```

##### `_dgitemmaster_table.html` (Partial for DataTables)

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="dgitemmasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in machinery_items %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">
                    {# This link button will navigate to the details page, emulating the ASP.NET redirect #}
                    <a href="{% url 'machinery:dgitemmaster_detail' item.pk %}" class="text-blue-600 hover:text-blue-900 font-bold">
                        {{ item.item_code }}
                    </a>
                </td>
                <td class="py-4 px-6 text-sm text-gray-500">{{ item.formatted_description }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.display_uom }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500 text-right">{{ item.stock_qty|floatformat:"0" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.display_location }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    {# Assuming 'detail' is the action for 'Sel' command. Adjust URL name as needed. #}
                    <a href="{% url 'machinery:dgitemmaster_detail' item.pk %}" class="text-indigo-600 hover:text-indigo-900" title="View Details">
                        Select
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500 text-lg">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Only initialize DataTables if there are rows to display
        if ($('#dgitemmasterTable tbody tr').length > 0 && $('#dgitemmasterTable tbody tr td').first().attr('colspan') !== '7') {
            $('#dgitemmasterTable').DataTable({
                "pageLength": 20, // Matches ASP.NET GridView's PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true
            });
        }
    });
</script>
```

##### `_subcategory_options.html` (Partial for SubCategory Dropdown)

```html
<option value="Select SubCategory">Select SubCategory</option>
{% for subcategory in subcategories %}
<option value="{{ subcategory.pk }}">{{ subcategory.symbol }} - {{ subcategory.sc_name }}</option>
{% endfor %}
```

#### 4.5 URLs (`machinery/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import MachineryNewListView, MachineryItemTablePartialView, GetSubcategoriesView
from .models import DgItemMaster # Only for detail view example, not directly tied to core page logic

app_name = 'machinery' # Namespace for URLs

urlpatterns = [
    # Main page for selecting machinery items
    path('machinery-new/', MachineryNewListView.as_view(), name='machineryitem_list'),
    
    # HTMX endpoint for the table content (for filtering and initial load)
    path('machinery-new/table/', MachineryItemTablePartialView.as_view(), name='dgitemmaster_table'),
    
    # HTMX endpoint for dynamic subcategory dropdown population
    path('machinery-new/get-subcategories/', GetSubcategoriesView.as_view(), name='get_subcategories'),

    # Placeholder for the detail page that the "Select" link navigates to
    # This assumes a 'machinery:dgitemmaster_detail' view exists in a separate 'details' module
    path('machinery-new/details/<int:pk>/', DgItemMaster.as_view(), name='dgitemmaster_detail'), # Placeholder, replace with actual detail view if available

]
```

#### 4.6 Tests (`machinery/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DgItemMaster, DgCategoryMaster, DgSubCategoryMaster, DgLocationMaster, UnitMaster, MsMaster
from django.db.models import QuerySet

class MachineryModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.company_id = 1
        cls.financial_year_id = 2023

        cls.category1 = DgCategoryMaster.objects.create(cid=1, category_name='Category A')
        cls.category2 = DgCategoryMaster.objects.create(cid=2, category_name='Category B')

        cls.subcategory1 = DgSubCategoryMaster.objects.create(scid=101, cid=cls.category1, symbol='SA', sc_name='SubCategory A1')
        cls.subcategory2 = DgSubCategoryMaster.objects.create(scid=102, cid=cls.category1, symbol='SB', sc_name='SubCategory A2')
        cls.subcategory3 = DgSubCategoryMaster.objects.create(scid=201, cid=cls.category2, symbol='SC', sc_name='SubCategory B1')

        cls.location1 = DgLocationMaster.objects.create(id=1, location_label='WH', location_no='001')
        cls.location2 = DgLocationMaster.objects.create(id=2, location_label='WH', location_no='002')

        cls.unit1 = UnitMaster.objects.create(id=1, symbol='KGS')
        cls.unit2 = UnitMaster.objects.create(id=2, symbol='PCS')

        # Create test DgItemMaster objects
        cls.item1 = DgItemMaster.objects.create(
            id=1, item_code='MACH-001', manf_desc='A long description for machine 1 that is over eighty characters long to test the substring property', 
            stock_qty=10.5, category=cls.category1, subcategory=cls.subcategory1, 
            location=cls.location1, uom_basic=cls.unit1, comp_id=cls.company_id, absolute='0'
        )
        cls.item2 = DgItemMaster.objects.create(
            id=2, item_code='MACH-002', manf_desc='Description for machine 2', 
            stock_qty=5.0, category=cls.category1, subcategory=cls.subcategory2, 
            location=cls.location2, uom_basic=cls.unit2, comp_id=cls.company_id, absolute='0'
        )
        cls.item3 = DgItemMaster.objects.create(
            id=3, item_code='MACH-003', manf_desc='Another item for company X', 
            stock_qty=20.0, category=cls.category2, subcategory=cls.subcategory3, 
            location=cls.location1, uom_basic=cls.unit1, comp_id=cls.company_id, absolute='0'
        )
        # An item that should be excluded by 'absolute' flag
        cls.item4_abs = DgItemMaster.objects.create(
            id=4, item_code='MACH-004', manf_desc='Absolute item', 
            stock_qty=1.0, category=cls.category1, subcategory=cls.subcategory1, 
            location=cls.location1, uom_basic=cls.unit1, comp_id=cls.company_id, absolute='1'
        )
        # An item already in MsMaster
        cls.item5_in_ms = DgItemMaster.objects.create(
            id=5, item_code='MACH-005', manf_desc='Already linked item', 
            stock_qty=7.0, category=cls.category1, subcategory=cls.subcategory1, 
            location=cls.location1, uom_basic=cls.unit1, comp_id=cls.company_id, absolute='0'
        )
        MsMaster.objects.create(id=1, item=cls.item5_in_ms, comp_id=cls.company_id, fin_year_id=cls.financial_year_id)
        
        # Item for a different company
        cls.item6_other_comp = DgItemMaster.objects.create(
            id=6, item_code='MACH-006', manf_desc='Other company item', 
            stock_qty=3.0, category=cls.category1, subcategory=cls.subcategory1, 
            location=cls.location1, uom_basic=cls.unit1, comp_id=cls.company_id + 1, absolute='0'
        )


    def test_dgitemmaster_creation(self):
        item = DgItemMaster.objects.get(id=1)
        self.assertEqual(item.item_code, 'MACH-001')
        self.assertEqual(item.manf_desc, 'A long description for machine 1 that is over eighty characters long to test the substring property')
        self.assertEqual(item.stock_qty, 10.5)
        self.assertEqual(item.category.category_name, 'Category A')
        self.assertEqual(item.subcategory.sc_name, 'SubCategory A1')
        self.assertEqual(item.location.location_label, 'WH')
        self.assertEqual(item.uom_basic.symbol, 'KGS')
        self.assertEqual(item.comp_id, self.company_id)
        self.assertEqual(item.absolute, '0')

    def test_formatted_description_property(self):
        self.assertEqual(self.item1.formatted_description, 'A long description for machine 1 that is over eighty characters long to test the substring...')
        self.assertEqual(self.item2.formatted_description, 'Description for machine 2')

    def test_display_uom_property(self):
        self.assertEqual(self.item1.display_uom, 'KGS')
        self.assertEqual(self.item2.display_uom, 'PCS')
        # Test N/A for missing UOM
        item_no_uom = DgItemMaster.objects.create(id=7, item_code='MACH-007', comp_id=self.company_id, absolute='0')
        self.assertEqual(item_no_uom.display_uom, 'N/A')

    def test_display_location_property(self):
        self.assertEqual(self.item1.display_location, 'WH-001')
        self.assertEqual(self.item2.display_location, 'WH-002')
        # Test N/A for missing Location
        item_no_location = DgItemMaster.objects.create(id=8, item_code='MACH-008', comp_id=self.company_id, absolute='0')
        self.assertEqual(item_no_location.display_location, 'N/A')

    def test_available_for_selection_manager_basic_filter(self):
        # Only non-absolute items for the correct company, not in MsMaster
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id)
        self.assertIsInstance(queryset, QuerySet)
        self.assertEqual(queryset.count(), 3) # item1, item2, item3
        self.assertIn(self.item1, queryset)
        self.assertIn(self.item2, queryset)
        self.assertIn(self.item3, queryset)
        self.assertNotIn(self.item4_abs, queryset) # Excluded by absolute='1'
        self.assertNotIn(self.item5_in_ms, queryset) # Excluded by MsMaster
        self.assertNotIn(self.item6_other_comp, queryset) # Excluded by company_id

    def test_available_for_selection_manager_category_filter(self):
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id, category_id=self.category1.pk)
        self.assertEqual(queryset.count(), 2)
        self.assertIn(self.item1, queryset)
        self.assertIn(self.item2, queryset)
        self.assertNotIn(self.item3, queryset)

    def test_available_for_selection_manager_subcategory_filter(self):
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id, 
                                                                 category_id=self.category1.pk, subcategory_id=self.subcategory1.pk)
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.item1, queryset)
        self.assertNotIn(self.item2, queryset)

    def test_available_for_selection_manager_item_code_search(self):
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id, 
                                                                 search_field='tblDG_Item_Master.ItemCode', search_value='MACH-001')
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.item1, queryset)

    def test_available_for_selection_manager_description_search(self):
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id, 
                                                                 search_field='tblDG_Item_Master.ManfDesc', search_value='machine 2')
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.item2, queryset)
        
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id, 
                                                                 search_field='tblDG_Item_Master.ManfDesc', search_value='description')
        self.assertEqual(queryset.count(), 2) # item1, item2

    def test_available_for_selection_manager_location_search(self):
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id, 
                                                                 search_field='tblDG_Item_Master.Location', location_id=self.location1.pk)
        self.assertEqual(queryset.count(), 2) # item1, item3
        self.assertIn(self.item1, queryset)
        self.assertIn(self.item3, queryset)

    def test_available_for_selection_manager_combined_filters(self):
        queryset = DgItemMaster.objects.available_for_selection(self.company_id, self.financial_year_id, 
                                                                 category_id=self.category1.pk, 
                                                                 search_field='tblDG_Item_Master.ManfDesc', search_value='machine 1')
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.item1, queryset)


class MachineryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set up session data for company and financial year
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()
        
        # Reuse test data from model tests
        MachineryModelsTest.setUpTestData()

    def test_list_view_get(self):
        response = self.client.get(reverse('machinery:machineryitem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/dgitemmaster/list.html')
        self.assertIn('categories', response.context)
        self.assertIn('locations', response.context)
        # Check that context variables for selected filters are present
        self.assertIn('selected_category', response.context)
        self.assertEqual(response.context['selected_category'], 'Select Category') # Default value

    def test_list_view_get_with_filters(self):
        # Test that initial filter values are passed to context
        response = self.client.get(reverse('machinery:machineryitem_list'), {
            'category': MachineryModelsTest.category1.pk,
            'search_code': 'tblDG_Item_Master.ItemCode',
            'search_text': 'MACH-001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['selected_category'], str(MachineryModelsTest.category1.pk))
        self.assertEqual(response.context['selected_search_code'], 'tblDG_Item_Master.ItemCode')
        self.assertEqual(response.context['selected_search_text'], 'MACH-001')

    def test_table_partial_view_get(self):
        # Basic load, should show items 1, 2, 3
        response = self.client.get(reverse('machinery:dgitemmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/dgitemmaster/_dgitemmaster_table.html')
        self.assertIn('machinery_items', response.context)
        self.assertEqual(response.context['machinery_items'].count(), 3)
        self.assertContains(response, 'MACH-001')
        self.assertContains(response, 'MACH-002')
        self.assertContains(response, 'MACH-003')
        self.assertNotContains(response, 'MACH-004') # Absolute
        self.assertNotContains(response, 'MACH-005') # In MsMaster
        self.assertNotContains(response, 'MACH-006') # Other company

    def test_table_partial_view_get_with_category_filter(self):
        # Filter by Category A (cid=1), should show item1, item2
        response = self.client.get(reverse('machinery:dgitemmaster_table'), {'category': MachineryModelsTest.category1.pk})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['machinery_items'].count(), 2)
        self.assertContains(response, 'MACH-001')
        self.assertContains(response, 'MACH-002')
        self.assertNotContains(response, 'MACH-003')

    def test_table_partial_view_get_with_subcategory_filter(self):
        # Filter by Category A (cid=1) and SubCategory A1 (scid=101), should show item1
        response = self.client.get(reverse('machinery:dgitemmaster_table'), {
            'category': MachineryModelsTest.category1.pk, 
            'subcategory': MachineryModelsTest.subcategory1.pk
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['machinery_items'].count(), 1)
        self.assertContains(response, 'MACH-001')
        self.assertNotContains(response, 'MACH-002')

    def test_table_partial_view_get_with_item_code_search(self):
        response = self.client.get(reverse('machinery:dgitemmaster_table'), {
            'search_code': 'tblDG_Item_Master.ItemCode', 
            'search_text': 'MACH-001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['machinery_items'].count(), 1)
        self.assertContains(response, 'MACH-001')

    def test_table_partial_view_get_with_description_search(self):
        response = self.client.get(reverse('machinery:dgitemmaster_table'), {
            'search_code': 'tblDG_Item_Master.ManfDesc', 
            'search_text': 'machine 2'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['machinery_items'].count(), 1)
        self.assertContains(response, 'MACH-002')

    def test_table_partial_view_get_with_location_search(self):
        response = self.client.get(reverse('machinery:dgitemmaster_table'), {
            'search_code': 'tblDG_Item_Master.Location', 
            'location': MachineryModelsTest.location1.pk
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['machinery_items'].count(), 2) # item1, item3
        self.assertContains(response, 'MACH-001')
        self.assertContains(response, 'MACH-003')

    def test_get_subcategories_view(self):
        # Test with a valid category
        response = self.client.get(reverse('machinery:get_subcategories'), {'category_id': MachineryModelsTest.category1.pk})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/dgitemmaster/_subcategory_options.html')
        self.assertContains(response, 'SubCategory A1')
        self.assertContains(response, 'SubCategory A2')
        self.assertNotContains(response, 'SubCategory B1')

        # Test with 'Select Category'
        response = self.client.get(reverse('machinery:get_subcategories'), {'category_id': 'Select Category'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/dgitemmaster/_subcategory_options.html')
        self.assertContains(response, '<option value="Select SubCategory">Select SubCategory</option>')
        self.assertNotContains(response, 'SubCategory A1') # Should not list any subcategories

        # Test with invalid category_id
        response = self.client.get(reverse('machinery:get_subcategories'), {'category_id': 'invalid'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="Select SubCategory">Select SubCategory</option>')
        self.assertNotContains(response, 'SubCategory A1')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already integrate HTMX and Alpine.js as follows:

*   **HTMX for dynamic filtering:**
    *   The main `list.html` page uses `hx-get` on a button click (`btnSearch`) to fetch filtered table data from `{% url 'machinery:dgitemmaster_table' %}`.
    *   The `hx-target` and `hx-swap` attributes ensure only the table container (`#dgitemmaster-table-container`) is updated, avoiding full page reloads.
    *   The Category dropdown (`id_category`) uses `hx-get` to `{% url 'machinery:get_subcategories' %}` to dynamically update the SubCategory dropdown (`id_subcategory`), demonstrating chained selects.
    *   `hx-indicator` is used to show loading spinners for better user experience.
*   **Alpine.js for UI state management:**
    *   An `x-data` attribute on the filter container manages the `searchType` and `locationIsVisible` state.
    *   `x-model="searchType"` binds the `Search By` dropdown's value.
    *   `x-show="!locationIsVisible"` and `x-show="locationIsVisible"` conditionally display either the `Search Text` input or the `Location` dropdown based on the `Search By` selection, just like the ASP.NET `Visible` property.
*   **DataTables for list views:**
    *   The `_dgitemmaster_table.html` partial contains the `<table>` element.
    *   A `<script>` block within this partial initializes DataTables on the `<table>` element once it's loaded via HTMX. This ensures DataTables correctly applies its features (pagination, search, sort) to the dynamically loaded content.
*   **No custom JavaScript:** All dynamic interactions are handled by HTMX and Alpine.js, minimizing custom JavaScript.

### Final Notes

*   **Placeholders:** `{% url 'machinery:dgitemmaster_detail' item.pk %}` serves as a placeholder for the detail view. You would need to create a `DetailView` for `DgItemMaster` in `machinery/views.py` and register its URL in `machinery/urls.py` if that functionality is part of the `Machinery_New_Details.aspx` page's purpose.
*   **Session Management:** The solution relies on `request.session.get('compid')` and `request.session.get('finyear')`. Ensure your Django authentication and session middleware are correctly configured to provide these values, likely by mapping them from the logged-in user or a global session context.
*   **`get_subcategories` Filter Helper:** The `list.html` uses a `categories|get_subcategories:selected_category` template filter. This is a placeholder for a custom template filter you'd need to define if you want to pre-populate the `id_subcategory` dropdown on initial page load based on `selected_category`. The HTMX call will handle it dynamically on change. For initial load, if the `selected_category` is already set, you'd fetch the subcategories in `MachineryNewListView.get_context_data` and pass them, or implement a custom template tag/filter for cleaner logic. For simplicity, the HTMX interaction is the primary mechanism shown.
*   **Refinement:** The `Id` field in ASP.NET `tblDG_Item_Master` is typically an auto-incrementing integer. In Django, we've mapped `id = models.IntegerField(db_column='Id', primary_key=True)`, assuming the database handles auto-increment.
*   **Business Logic:** The complex SQL `SELECT` statement in `Fillgridview` has been fully refactored into the `DgItemMasterManager.available_for_selection` method, adhering to the "fat model, thin view" principle. This improves maintainability and testability.