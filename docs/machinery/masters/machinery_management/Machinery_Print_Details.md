## ASP.NET to Django Conversion Script: Machinery Details Print

This modernization plan outlines the transition of your ASP.NET Machinery Print Details page to a modern Django application. The focus is on leveraging Django's robust ORM, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation, all while adhering to a "fat model, thin view" architecture and emphasizing automation-driven development.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

The ASP.NET code retrieves data from `tblMS_Master` as the primary entity, and enriches it by joining with `tblDG_Item_Master`, `Unit_Master`, `tblMM_Supplier_master`, and `tblHR_OfficeStaff`. This indicates a core `Machinery` entity with foreign key relationships to item details, units, suppliers, and employees.

**Inferred Database Schema:**

*   **Main Table:** `tblMS_Master`
    *   `Id` (int, Primary Key)
    *   `SysDate` (datetime)
    *   `CompId` (int) - Company ID
    *   `ItemId` (int, FK to `tblDG_Item_Master.Id`)
    *   `Make` (string)
    *   `Model` (string)
    *   `Capacity` (string)
    *   `PurchaseDate` (datetime)
    *   `SupplierName` (string/int, FK to `tblMM_Supplier_master.SupplierId`)
    *   `Cost` (decimal/double)
    *   `WarrantyExpiryDate` (datetime)
    *   `LifeDate` (datetime)
    *   `ReceivedDate` (datetime)
    *   `Insurance` (boolean/bit, 0 or 1)
    *   `InsuranceExpiryDate` (datetime)
    *   `Puttouse` (datetime) - Put to Use Date
    *   `Incharge` (string/int, FK to `tblHR_OfficeStaff.EmpId`)
    *   `Location` (string)
    *   `PMDays` (string/int) - Preventative Maintenance Days

*   **Related Tables:**
    *   `tblDG_Item_Master`
        *   `Id` (int, Primary Key)
        *   `ItemCode` (string)
        *   `ManfDesc` (string) - Manufacturing Description
        *   `UOMBasic` (int, FK to `Unit_Master.Id`)
        *   `CompId` (int)
    *   `Unit_Master`
        *   `Id` (int, Primary Key)
        *   `Symbol` (string)
    *   `tblMM_Supplier_master`
        *   `SupplierId` (int/string, Primary Key)
        *   `SupplierName` (string)
        *   `CompId` (int)
    *   `tblHR_OfficeStaff`
        *   `EmpId` (int/string, Primary Key)
        *   `EmployeeName` (string)
        *   `CompId` (int)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations from the ASP.NET code.

The provided ASP.NET page `Machinery_Print_Details.aspx` is exclusively a **Read** (Display/Reporting) operation for a single machinery item. It fetches detailed information about one specific machinery item based on its ID and renders it in a report format using Crystal Reports. There are no Create, Update, or Delete operations on this specific page. The "Cancel" button is purely for navigation.

For a complete Django modernization, we will also implement a **List View** for all machinery, from which a specific item's details can be accessed. This will fulfill the broader requirement for list views with DataTables and allow for the logical flow from a list to a detail page.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

*   `CrystalReportViewer`: This control is used to display a report generated by Crystal Reports. In Django, this will be replaced by a structured HTML template that displays the data, potentially in a printable format, or by a mechanism to generate and serve a PDF.
*   `asp:Button ID="Cancel"`: A simple button for navigation. In Django, this will be a link or button that redirects the user back to the machinery list page.

Given the prompt's strong emphasis on DataTables for list views and HTMX/Alpine.js for interactions, we will structure the solution to include a `Machinery` list page (using DataTables and HTMX for CRUD modals) and a `Machinery` detail/print page (displaying the comprehensive report data).

### Step 4: Generate Django Code

We will create a Django application named `machinery_master`.

#### 4.1 Models (`machinery_master/models.py`)

Task: Create Django models based on the database schema, including methods for report data retrieval.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# Helper functions for date formatting, replicating ASP.NET's fun.FromDateDMY
def format_date_dmy(date_obj):
    if isinstance(date_obj, datetime):
        return date_obj.strftime("%d/%m/%Y")
    return "NA" if not date_obj else str(date_obj) # Fallback for non-date types or empty strings

# Helper to simulate CompAdd, which would fetch company address details
# In a real ERP, this would likely be a separate Company model lookup.
def get_company_address(comp_id):
    # This is a placeholder. In a real system, you'd fetch this from a Company model.
    # For now, a static or lookup value based on comp_id.
    addresses = {
        1: "123 ERP Street, Tech City, Country",
        # Add more company IDs and addresses as needed
    }
    return addresses.get(comp_id, "Company Address Not Found")

# Define related models first, as they are foreign key targets
class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255) # Manufacturing Description
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class SupplierMaster(models.Model):
    # SupplierId could be string or int based on ASP.NET code. Assuming int for ForeignKey, but check DB.
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50) # Assuming it's a string/varchar from ASP.NET
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class OfficeStaff(models.Model):
    # EmpId could be string or int based on ASP.NET code. Assuming int for ForeignKey, but check DB.
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming it's a string/varchar from ASP.NET
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.employee_name} [{self.emp_id}]"

# Core Machinery Model
class Machinery(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate', null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='machineries')
    make = models.CharField(db_column='Make', max_length=255, null=True, blank=True)
    model = models.CharField(db_column='Model', max_length=255, null=True, blank=True)
    capacity = models.CharField(db_column='Capacity', max_length=255, null=True, blank=True)
    purchase_date = models.DateTimeField(db_column='PurchaseDate', null=True, blank=True)
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierName', related_name='machineries', null=True, blank=True) # Check if SupplierName in DB is ID or Name
    cost = models.DecimalField(db_column='Cost', max_digits=18, decimal_places=2, null=True, blank=True)
    warranty_expiry_date = models.DateTimeField(db_column='WarrantyExpiryDate', null=True, blank=True)
    life_date = models.DateTimeField(db_column='LifeDate', null=True, blank=True)
    received_date = models.DateTimeField(db_column='ReceivedDate', null=True, blank=True)
    insurance = models.BooleanField(db_column='Insurance', default=False)
    insurance_expiry_date = models.DateTimeField(db_column='InsuranceExpiryDate', null=True, blank=True)
    put_to_use_date = models.DateTimeField(db_column='Puttouse', null=True, blank=True)
    incharge = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='Incharge', related_name='machineries_incharge', null=True, blank=True) # Check if Incharge in DB is ID or Name
    location = models.CharField(db_column='Location', max_length=255, null=True, blank=True)
    pm_days = models.CharField(db_column='PMDays', max_length=50, null=True, blank=True) # Assuming string as per C# (could be int)

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machinery'
        verbose_name_plural = 'Machineries'

    def __str__(self):
        return f"{self.item.manf_desc} ({self.make} {self.model})"

    @classmethod
    def get_report_data(cls, item_id, comp_id, fin_year_id):
        """
        Replicates the data retrieval and transformation logic from the ASP.NET code-behind
        to generate a comprehensive report dictionary for a single machinery item.
        This method embodies the "fat model" principle for complex data assembly.
        """
        try:
            machinery = cls.objects.select_related('item', 'item__uom_basic', 'supplier', 'incharge').get(
                item__id=item_id, # This is `ItemId` in tblMS_Master linking to tblDG_Item_Master.Id
                comp_id=comp_id,
                # FinYearId logic is complex in ASP.NET: 'And FinYearId<=' + FinYearId + ''
                # This often implies a date range for financial year. For simplicity,
                # we'll assume it's just a filter for now or needs to be handled via date.
                # If FinYearId is a direct field on tblMS_Master, add it here.
                # Assuming FinYearId check is for context, not primary filter on *this* table.
            )
        except cls.DoesNotExist:
            return None # Or raise an exception, or return an empty dictionary

        # Replicating data transformations from ASP.NET code-behind
        report_data = {
            'Id': machinery.id,
            'SysDate': format_date_dmy(machinery.sys_date),
            'CompId': machinery.comp_id,
            'ItemCode': machinery.item.item_code if machinery.item else 'NA',
            'UOM': machinery.item.uom_basic.symbol if machinery.item and machinery.item.uom_basic else 'NA',
            'Name': machinery.item.manf_desc if machinery.item else 'NA',
            'Make': machinery.make,
            'Model': machinery.model,
            'Capacity': machinery.capacity,
            'PurchaseDate': format_date_dmy(machinery.purchase_date),
            'SupplierName': str(machinery.supplier) if machinery.supplier else 'NA', # Already formatted by __str__
            'Cost': float(machinery.cost) if machinery.cost is not None else 0.0,
            'WarrantyExpiryDate': format_date_dmy(machinery.warranty_expiry_date),
            'LifeDate': format_date_dmy(machinery.life_date),
            'ReceivedDate': format_date_dmy(machinery.received_date),
            'Insurance': "Yes" if machinery.insurance else "No",
            'InsuranceExpiryDate': format_date_dmy(machinery.insurance_expiry_date) if machinery.insurance_expiry_date else "NA",
            'Puttouse': format_date_dmy(machinery.put_to_use_date),
            'Incharge': str(machinery.incharge) if machinery.incharge else 'NA', # Already formatted by __str__
            'Location': machinery.location,
            'PMDays': machinery.pm_days,
            'CompanyAddress': get_company_address(comp_id), # Replicates report.SetParameterValue("Address", Address)
        }
        return report_data

```

#### 4.2 Forms (`machinery_master/forms.py`)

Task: Define Django forms for user input. This will be used for CRUD operations on the list view, not the specific "Print Details" page.

```python
from django import forms
from .models import Machinery, ItemMaster, SupplierMaster, OfficeStaff

class MachineryForm(forms.ModelForm):
    # Assuming ItemId, SupplierName, Incharge are selected from dropdowns/search in UI
    # The ForeignKey fields will render as select inputs by default
    # You might want to use ModelChoiceField with custom queryset or autocomplete widgets for large datasets.

    class Meta:
        model = Machinery
        fields = [
            'item', 'make', 'model', 'capacity', 'purchase_date',
            'supplier', 'cost', 'warranty_expiry_date', 'life_date',
            'received_date', 'insurance', 'insurance_expiry_date',
            'put_to_use_date', 'incharge', 'location', 'pm_days'
        ]
        widgets = {
            'item': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'make': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'model': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'capacity': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'purchase_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cost': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'warranty_expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'life_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'received_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'insurance': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'insurance_expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'put_to_use_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'incharge': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pm_days': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

```

#### 4.3 Views (`machinery_master/views.py`)

Task: Implement CRUD operations using CBVs for the list, and a `TemplateView` for the detail/print page.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import Machinery
from .forms import MachineryForm

# This view is for the "print details" functionality
class MachineryPrintDetailsView(TemplateView):
    template_name = 'machinery_master/machinery/print_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        machinery_id = self.kwargs.get('pk')
        
        # Simulating session data (comp_id, fin_year_id, username)
        # In a real app, these would come from request.user or actual session.
        # For demonstration, using hardcoded values.
        comp_id = self.request.session.get('compid', 1) # Default to 1
        fin_year_id = self.request.session.get('finyear', 2023) # Default to 2023
        username = self.request.session.get('username', 'admin')

        # Use the fat model method to get the comprehensive report data
        report_data = Machinery.get_report_data(machinery_id, comp_id, fin_year_id)

        if not report_data:
            raise Http404("Machinery not found or data incomplete.")

        context['report_data'] = report_data
        context['page_title'] = "Machinery - Print Details"
        return context

# CRUD Views for the main Machinery List page

class MachineryListView(ListView):
    model = Machinery
    template_name = 'machinery_master/machinery/list.html'
    context_object_name = 'machineries'

# HTMX partial for the table, allowing dynamic updates
class MachineryTablePartialView(ListView):
    model = Machinery
    template_name = 'machinery_master/machinery/_machinery_table.html'
    context_object_name = 'machineries'

    def get_queryset(self):
        # Prefetch related data to minimize DB queries for the list view
        return Machinery.objects.select_related('item', 'item__uom_basic', 'supplier', 'incharge')

class MachineryCreateView(CreateView):
    model = Machinery
    form_class = MachineryForm
    template_name = 'machinery_master/machinery/form.html'
    success_url = reverse_lazy('machinery_list') # Redirect to list view

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Machinery added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX it was successful
                headers={
                    'HX-Trigger': 'refreshMachineryList' # Custom HTMX trigger to refresh the list
                }
            )
        return response

class MachineryUpdateView(UpdateView):
    model = Machinery
    form_class = MachineryForm
    template_name = 'machinery_master/machinery/form.html'
    success_url = reverse_lazy('machinery_list') # Redirect to list view

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Machinery updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Trigger': 'refreshMachineryList' # Trigger refresh of the list
                }
            )
        return response

class MachineryDeleteView(DeleteView):
    model = Machinery
    template_name = 'machinery_master/machinery/confirm_delete.html'
    success_url = reverse_lazy('machinery_list') # Redirect to list view

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Machinery deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Trigger': 'refreshMachineryList' # Trigger refresh of the list
                }
            )
        return response

```

#### 4.4 Templates (`machinery_master/templates/machinery_master/machinery/`)

Task: Create templates for each view, including HTMX integration and DataTables for the list.

**`list.html`** (Main list page for Machineries)
```html
{% extends 'core/base.html' %}

{% block title %}Machinery List{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machinery List</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'machinery_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Machinery
        </button>
    </div>
    
    <div id="machineryTable-container"
         hx-trigger="load, refreshMachineryList from:body"
         hx-get="{% url 'machinery_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Machinery Data...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4"
             _="on htmx:afterOnLoad remove .is-active from #modal if htmx.target.id == 'modalContent' && htmx.response.status === 204">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initiated at document.ready or body load
    // Any specific Alpine.js components would be defined here if needed for this page.
</script>
{% endblock %}

```

**`_machinery_table.html`** (Partial template for DataTables)
```html
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <table id="machineryTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Name</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Make</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Model</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Supplier</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Location</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in machineries %}
            <tr class="hover:bg-gray-50">
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ obj.item.manf_desc|default:"N/A" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ obj.make|default:"N/A" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ obj.model|default:"N/A" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ obj.supplier.supplier_name|default:"N/A" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm">{{ obj.location|default:"N/A" }}</td>
                <td class="px-5 py-3 border-b border-gray-200 bg-white text-sm text-center">
                    <button 
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded-lg text-sm mr-2"
                        hx-get="{% url 'machinery_print_details' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Details
                    </button>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg text-sm mr-2"
                        hx-get="{% url 'machinery_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg text-sm"
                        hx-get="{% url 'machinery_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#machineryTable')) {
            $('#machineryTable').DataTable().destroy();
        }
        $('#machineryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "lengthMenu": "Show _MENU_ entries",
                "search": "Search:",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "infoEmpty": "Showing 0 to 0 of 0 entries",
                "infoFiltered": "(filtered from _MAX_ total entries)",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    });
</script>
```

**`print_details.html`** (Details/Report page for a single Machinery item, loaded via HTMX modal)
```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-xl font-bold text-gray-900 mb-6">Machinery - Print Details</h3>
    <div class="overflow-y-auto max-h-[80vh] pr-4 pb-4"> {# Scrollable area for content if too long #}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-gray-700">
            <div><strong class="text-gray-900">ID:</strong> {{ report_data.Id }}</div>
            <div><strong class="text-gray-900">System Date:</strong> {{ report_data.SysDate }}</div>
            <div><strong class="text-gray-900">Company ID:</strong> {{ report_data.CompId }}</div>
            <div><strong class="text-gray-900">Item Code:</strong> {{ report_data.ItemCode }}</div>
            <div><strong class="text-gray-900">UOM:</strong> {{ report_data.UOM }}</div>
            <div><strong class="text-gray-900">Name:</strong> {{ report_data.Name }}</div>
            <div><strong class="text-gray-900">Make:</strong> {{ report_data.Make }}</div>
            <div><strong class="text-gray-900">Model:</strong> {{ report_data.Model }}</div>
            <div><strong class="text-gray-900">Capacity:</strong> {{ report_data.Capacity }}</div>
            <div><strong class="text-gray-900">Purchase Date:</strong> {{ report_data.PurchaseDate }}</div>
            <div><strong class="text-gray-900">Supplier Name:</strong> {{ report_data.SupplierName }}</div>
            <div><strong class="text-gray-900">Cost:</strong> {{ report_data.Cost|floatformat:2 }}</div>
            <div><strong class="text-gray-900">Warranty Expiry Date:</strong> {{ report_data.WarrantyExpiryDate }}</div>
            <div><strong class="text-gray-900">Life Date:</strong> {{ report_data.LifeDate }}</div>
            <div><strong class="text-gray-900">Received Date:</strong> {{ report_data.ReceivedDate }}</div>
            <div><strong class="text-gray-900">Insurance:</strong> {{ report_data.Insurance }}</div>
            <div><strong class="text-gray-900">Insurance Expiry Date:</strong> {{ report_data.InsuranceExpiryDate }}</div>
            <div><strong class="text-gray-900">Put to Use Date:</strong> {{ report_data.Puttouse }}</div>
            <div><strong class="text-gray-900">Incharge:</strong> {{ report_data.Incharge }}</div>
            <div><strong class="text-gray-900">Location:</strong> {{ report_data.Location }}</div>
            <div><strong class="text-gray-900">PM Days:</strong> {{ report_data.PMDays }}</div>
            <div class="col-span-1 md:col-span-2"><strong class="text-gray-900">Company Address:</strong> {{ report_data.CompanyAddress }}</div>
        </div>
    </div>

    <div class="mt-6 flex justify-end">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Close
        </button>
        {# A button to trigger PDF generation (would need a separate view and library like WeasyPrint) #}
        {# <a href="{% url 'machinery_generate_pdf' report_data.Id %}" target="_blank" class="ml-4 bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">Generate PDF</a> #}
    </div>
</div>
```

**`form.html`** (Partial template for Add/Edit forms, loaded via HTMX modal)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Machinery</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
        {# Loading indicator for HTMX form submission #}
        <div id="form-indicator" class="htmx-indicator ml-3 text-blue-500">
            Saving...
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial template for Delete confirmation, loaded via HTMX modal)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the machinery: <strong>{{ object.item.manf_desc }} ({{ object.make }} {{ object.model }})</strong>?
        This action cannot be undone.
    </p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`machinery_master/urls.py`)

Task: Define URL patterns for the views.

```python
from django.urls import path
from .views import (
    MachineryListView, MachineryTablePartialView, 
    MachineryCreateView, MachineryUpdateView, MachineryDeleteView,
    MachineryPrintDetailsView
)

urlpatterns = [
    # Main list view for Machinery
    path('machinery/', MachineryListView.as_view(), name='machinery_list'),
    # HTMX endpoint for the machinery table partial
    path('machinery/table/', MachineryTablePartialView.as_view(), name='machinery_table'),
    # CRUD operations
    path('machinery/add/', MachineryCreateView.as_view(), name='machinery_add'),
    path('machinery/edit/<int:pk>/', MachineryUpdateView.as_view(), name='machinery_edit'),
    path('machinery/delete/<int:pk>/', MachineryDeleteView.as_view(), name='machinery_delete'),
    # Specific Print Details page (replaces the ASP.NET .aspx page)
    path('machinery/print-details/<int:pk>/', MachineryPrintDetailsView.as_view(), name='machinery_print_details'),
    # Future: path('machinery/generate-pdf/<int:pk>/', MachineryGeneratePdfView.as_view(), name='machinery_generate_pdf'),
]
```

#### 4.6 Tests (`machinery_master/tests.py`)

Task: Write comprehensive tests for the models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import Machinery, ItemMaster, UnitMaster, SupplierMaster, OfficeStaff, format_date_dmy, get_company_address

class MachineryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for testing
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item = ItemMaster.objects.create(id=101, item_code='MCH001', manf_desc='Excavator XYZ', uom_basic=cls.unit, comp_id=1)
        cls.supplier = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Builders Supply Co.', comp_id=1)
        cls.incharge_staff = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=1)

        # Create a test Machinery object
        cls.machinery = Machinery.objects.create(
            id=1,
            sys_date=timezone.now(),
            comp_id=1,
            item=cls.item,
            make='Caterpillar',
            model='320D',
            capacity='20 Tons',
            purchase_date=timezone.now() - timedelta(days=365),
            supplier=cls.supplier,
            cost=150000.00,
            warranty_expiry_date=timezone.now() + timedelta(days=365),
            life_date=timezone.now() + timedelta(days=1825), # 5 years
            received_date=timezone.now() - timedelta(days=370),
            insurance=True,
            insurance_expiry_date=timezone.now() + timedelta(days=300),
            put_to_use_date=timezone.now() - timedelta(days=360),
            incharge=cls.incharge_staff,
            location='Site A',
            pm_days='30'
        )
        cls.machinery_no_insurance = Machinery.objects.create(
            id=2,
            sys_date=timezone.now(),
            comp_id=1,
            item=cls.item,
            make='Komatsu',
            model='PC200',
            capacity='18 Tons',
            purchase_date=timezone.now() - timedelta(days=365),
            supplier=cls.supplier,
            cost=120000.00,
            warranty_expiry_date=timezone.now() + timedelta(days=365),
            life_date=timezone.now() + timedelta(days=1825),
            received_date=timezone.now() - timedelta(days=370),
            insurance=False,
            insurance_expiry_date=None, # No insurance expiry if no insurance
            put_to_use_date=timezone.now() - timedelta(days=360),
            incharge=cls.incharge_staff,
            location='Site B',
            pm_days='60'
        )
        
    def test_machinery_creation(self):
        obj = Machinery.objects.get(id=1)
        self.assertEqual(obj.make, 'Caterpillar')
        self.assertEqual(obj.item.manf_desc, 'Excavator XYZ')
        self.assertEqual(obj.supplier.supplier_name, 'Builders Supply Co.')
        self.assertTrue(obj.insurance)

    def test_machinery_str(self):
        obj = Machinery.objects.get(id=1)
        self.assertEqual(str(obj), 'Excavator XYZ (Caterpillar 320D)')

    def test_get_report_data_success(self):
        report_data = Machinery.get_report_data(self.machinery.item.id, self.machinery.comp_id, 2023)
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['Id'], self.machinery.id)
        self.assertEqual(report_data['ItemCode'], self.item.item_code)
        self.assertEqual(report_data['UOM'], self.unit.symbol)
        self.assertEqual(report_data['Insurance'], 'Yes')
        self.assertIn('Company Address', report_data)

    def test_get_report_data_no_insurance_expiry(self):
        report_data = Machinery.get_report_data(self.machinery_no_insurance.item.id, self.machinery_no_insurance.comp_id, 2023)
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['Insurance'], 'No')
        self.assertEqual(report_data['InsuranceExpiryDate'], 'NA')

    def test_get_report_data_not_found(self):
        report_data = Machinery.get_report_data(999, 1, 2023) # Non-existent ID
        self.assertIsNone(report_data)

    def test_format_date_dmy_with_date(self):
        dt = timezone.datetime(2023, 10, 26, 10, 30, 0)
        self.assertEqual(format_date_dmy(dt), '26/10/2023')

    def test_format_date_dmy_with_none(self):
        self.assertEqual(format_date_dmy(None), 'NA')
        
    def test_get_company_address(self):
        self.assertEqual(get_company_address(1), "123 ERP Street, Tech City, Country")
        self.assertEqual(get_company_address(99), "Company Address Not Found") # Test unknown ID


class MachineryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item = ItemMaster.objects.create(id=101, item_code='MCH001', manf_desc='Excavator XYZ', uom_basic=cls.unit, comp_id=1)
        cls.supplier = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Builders Supply Co.', comp_id=1)
        cls.incharge_staff = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=1)
        
        cls.machinery1 = Machinery.objects.create(
            id=1, sys_date=timezone.now(), comp_id=1, item=cls.item, make='Cat', model='D1', cost=100000.00,
            purchase_date=timezone.now(), supplier=cls.supplier, incharge=cls.incharge_staff
        )
        cls.machinery2 = Machinery.objects.create(
            id=2, sys_date=timezone.now(), comp_id=1, item=cls.item, make='Volvo', model='E2', cost=120000.00,
            purchase_date=timezone.now(), supplier=cls.supplier, incharge=cls.incharge_staff
        )
    
    def setUp(self):
        self.client = Client()
        # Simulate a logged-in user or session data if needed by the views
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session['username'] = 'testuser'
        session.save()

    # --- Print Details View Tests ---
    def test_machinery_print_details_view_get(self):
        response = self.client.get(reverse('machinery_print_details', args=[self.machinery1.item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_master/machinery/print_details.html')
        self.assertIn('report_data', response.context)
        self.assertEqual(response.context['report_data']['Id'], self.machinery1.id)
        self.assertContains(response, 'Machinery - Print Details')
        self.assertContains(response, self.machinery1.make)

    def test_machinery_print_details_view_not_found(self):
        response = self.client.get(reverse('machinery_print_details', args=[9999])) # Non-existent PK
        self.assertEqual(response.status_code, 404)

    # --- List View Tests ---
    def test_list_view(self):
        response = self.client.get(reverse('machinery_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_master/machinery/list.html')
        self.assertIn('machineries', response.context)
        self.assertEqual(len(response.context['machineries']), 2)

    def test_machinery_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machinery_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_master/machinery/_machinery_table.html')
        self.assertIn('machineries', response.context)
        self.assertContains(response, 'id="machineryTable"') # Check if DataTables structure is present

    # --- Create View Tests ---
    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machinery_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_master/machinery/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        new_machinery_id = 3
        data = {
            'id': new_machinery_id,
            'sys_date': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            'comp_id': 1,
            'item': self.item.id,
            'make': 'Hyundai',
            'model': 'R220',
            'capacity': '22 Tons',
            'purchase_date': (timezone.now() - timedelta(days=100)).strftime('%Y-%m-%d'),
            'supplier': self.supplier.supplier_id,
            'cost': 180000.00,
            'warranty_expiry_date': (timezone.now() + timedelta(days=200)).strftime('%Y-%m-%d'),
            'life_date': (timezone.now() + timedelta(days=1000)).strftime('%Y-%m-%d'),
            'received_date': (timezone.now() - timedelta(days=110)).strftime('%Y-%m-%d'),
            'insurance': True,
            'insurance_expiry_date': (timezone.now() + timedelta(days=150)).strftime('%Y-%m-%d'),
            'put_to_use_date': (timezone.now() - timedelta(days=90)).strftime('%Y-%m-%d'),
            'incharge': self.incharge_staff.emp_id,
            'location': 'Site C',
            'pm_days': '45'
        }
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machinery_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMachineryList')
        self.assertTrue(Machinery.objects.filter(id=new_machinery_id).exists())
        self.assertEqual(Machinery.objects.get(id=new_machinery_id).make, 'Hyundai')

    def test_create_view_post_invalid(self):
        data = {
            'id': 4, # Provide ID, but required fields are missing/invalid
            'make': 'Invalid'
            # Missing required fields like 'item'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machinery_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX keeps the form with errors
        self.assertTemplateUsed(response, 'machinery_master/machinery/form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)


    # --- Update View Tests ---
    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machinery_edit', args=[self.machinery1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_master/machinery/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.machinery1)

    def test_update_view_post_success(self):
        updated_make = 'Updated Cat'
        data = {
            'id': self.machinery1.pk, # PK is needed for ModelForm
            'sys_date': self.machinery1.sys_date.strftime('%Y-%m-%d %H:%M:%S'),
            'comp_id': self.machinery1.comp_id,
            'item': self.machinery1.item.id,
            'make': updated_make,
            'model': self.machinery1.model,
            'capacity': self.machinery1.capacity,
            'purchase_date': self.machinery1.purchase_date.strftime('%Y-%m-%d'),
            'supplier': self.machinery1.supplier.supplier_id,
            'cost': self.machinery1.cost,
            'warranty_expiry_date': self.machinery1.warranty_expiry_date.strftime('%Y-%m-%d'),
            'life_date': self.machinery1.life_date.strftime('%Y-%m-%d'),
            'received_date': self.machinery1.received_date.strftime('%Y-%m-%d'),
            'insurance': self.machinery1.insurance,
            'insurance_expiry_date': self.machinery1.insurance_expiry_date.strftime('%Y-%m-%d'),
            'put_to_use_date': self.machinery1.put_to_use_date.strftime('%Y-%m-%d'),
            'incharge': self.machinery1.incharge.emp_id,
            'location': self.machinery1.location,
            'pm_days': self.machinery1.pm_days,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machinery_edit', args=[self.machinery1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMachineryList')
        self.machinery1.refresh_from_db()
        self.assertEqual(self.machinery1.make, updated_make)

    # --- Delete View Tests ---
    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machinery_delete', args=[self.machinery1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_master/machinery/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.machinery1)

    def test_delete_view_post_success(self):
        machinery_count_before = Machinery.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machinery_delete', args=[self.machinery1.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMachineryList')
        self.assertEqual(Machinery.objects.count(), machinery_count_before - 1)
        self.assertFalse(Machinery.objects.filter(pk=self.machinery1.pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions for HTMX:**
*   The `list.html` template uses `hx-get="{% url 'machinery_table' %}" hx-swap="innerHTML"` to initially load and periodically refresh the DataTables content.
*   `hx-trigger="load, refreshMachineryList from:body"` ensures the table loads on page load and refreshes whenever the `refreshMachineryList` custom event is triggered (e.g., after a CRUD operation).
*   Buttons for Add, Edit, Delete, and Details use `hx-get` to fetch the form/details content into the `#modalContent` div.
*   `hx-target="#modalContent"` and `_="on click add .is-active to #modal"` (using Alpine.js `x-data` or `_` for simple DOM manipulation) are used to open the modal and load content.
*   Form submissions in `form.html` and `confirm_delete.html` use `hx-post="{{ request.path }}" hx-swap="none"`. The `hx-swap="none"` prevents HTMX from replacing the form, allowing the server to handle UI updates (e.g., closing the modal via `HX-Trigger`).
*   The `HX-Trigger` header `refreshMachineryList` is sent back from successful `form_valid` (Create/Update) and `delete` (Delete) methods in the Django views, ensuring the list table automatically updates.
*   Modal closing logic `_="on click if event.target.id == 'modal' remove .is-active from me"` ensures clicking outside the modal closes it. The "Cancel" button in forms also uses this.

**Instructions for Alpine.js:**
*   Alpine.js (or htmx's `_` syntax for simple DOM actions) is used for basic UI state management, primarily to show/hide the modal (`add .is-active to #modal` and `remove .is-active from #modal`).
*   No complex Alpine.js components are explicitly needed for this specific `Print_Details` migration, as HTMX handles the bulk of dynamic interactions. For more complex client-side state (e.g., dynamic form fields, interactive filters not handled by DataTables), Alpine.js would be integrated via `x-data` attributes directly in the HTML.

**Instructions for DataTables:**
*   The `_machinery_table.html` partial template includes the `<table id="machineryTable">` element.
*   A `script` block within this partial initializes DataTables: `$('#machineryTable').DataTable({...});`.
*   The `destroy()` method is used before re-initialization to handle HTMX reloading the table content cleanly.
*   DataTables provides built-in client-side searching, sorting, and pagination as required.

**Automated Process Summary:**
1.  **AI-driven Schema Extraction:** The AI analyzes ASP.NET data access patterns to identify tables and columns, then maps them to Django models with `managed=False`.
2.  **Logic Conversion to Fat Models:** Complex data assembly and transformation logic from the ASP.NET code-behind (e.g., multiple SQL queries, date formatting, conditional logic) is encapsulated within a `classmethod` (like `get_report_data`) on the Django model. This makes the model "fat" and keeps views "thin".
3.  **CBV Generation:** Standard CRUD CBVs are automatically generated for the list view, along with a `TemplateView` for the specialized report/details page.
4.  **HTMX & Template Scaffolding:** HTMX attributes are added to buttons and containers to enable modal loading and dynamic table refreshing. Alpine.js or HTMX's `_` directives handle simple modal visibility. DataTables initialization is embedded in the partial template, ready to be re-initialized on HTMX swaps.
5.  **Test Generation:** Comprehensive unit tests for models (including the `get_report_data` method) and integration tests for all views are generated, ensuring high coverage and stability during migration.

This structured approach ensures a robust, maintainable, and modern Django application, moving away from legacy ASP.NET patterns towards a highly interactive and efficient user experience.

---

### Final Notes

*   **Placeholders:** `[MODEL_NAME]`, `[APP_NAME]`, `[FIELD1]` etc., have been replaced with `Machinery`, `machinery_master`, and actual field names or inferred values where appropriate.
*   **DRY Templates:** The use of `_machinery_table.html`, `form.html`, and `confirm_delete.html` as partials ensures reusability and adherence to DRY principles. These are loaded dynamically into a single modal container (`#modalContent`).
*   **Business Logic:** All complex data retrieval and transformation logic from the ASP.NET `Page_Init` method is now contained within the `Machinery.get_report_data` method, adhering to the "fat model" philosophy. Views remain concise, focusing on dispatching requests and rendering responses.
*   **Tests:** The provided tests aim for high coverage, verifying model behavior, data integrity, and view functionality, including HTMX interactions.
*   **Missing Dependencies:** Ensure `django-crispy-forms` (or similar for form rendering), `htmx`, `alpinejs`, and `datatables` are correctly configured in your `settings.py` and included in `core/base.html` (e.g., via CDN links).
*   **Company/User Context:** The `comp_id`, `fin_year_id`, and `username` in the ASP.NET code implicitly come from the user's session. In Django, this would typically be `request.user.profile.comp_id` or similar. For demonstration, session variables are used, but they should be properly integrated with your Django user authentication system.
*   **Real Report Generation:** While the `print_details.html` displays all data, a true "print" or "PDF" functionality would require integrating a library like `WeasyPrint` or `ReportLab` and a separate view to generate and serve the PDF. This plan focuses on replicating the data display aspect directly on the web page.