## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET `Dashboard.aspx` and `Dashboard.aspx.cs` files are largely empty, serving mainly as placeholders for content within a master page and containing an empty `Page_Load` event. There is no explicit database interaction, UI controls (like `<PERSON>ridView`, `TextBoxes`), or business logic defined within these files.

Therefore, for this modernization plan, we will proceed by demonstrating how a typical "Dashboard" page, which might display a list of relevant "Dashboard Items" with basic CRUD functionality, would be migrated. This approach allows us to illustrate the full Django modernization process as requested, even with the limited initial input. We will infer a hypothetical `DashboardItem` entity to showcase the capabilities of Django, HTMX, and Alpine.js.

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the provided ASP.NET code contains no database interaction elements (like `SqlDataSource`, `GridView` bindings, or explicit ADO.NET calls in C#), we cannot extract a concrete database schema.

For demonstration purposes, we will assume the dashboard interacts with a table named `dashboard_items` to display various configurable items.

*   **[TABLE_NAME]**: `dashboard_items`
*   **Columns**:
    *   `id` (Primary Key, inferred)
    *   `name` (VARCHAR, inferred, for item name)
    *   `description` (TEXT, inferred, for item details)
    *   `status` (VARCHAR, inferred, e.g., 'Active', 'Inactive')

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code (`Dashboard.aspx` and its code-behind) does not contain any explicit CRUD (Create, Read, Update, Delete) operations. The `Page_Load` method is empty, and there are no UI controls to suggest data manipulation.

However, a typical dashboard would involve:
*   **Read**: Displaying a list of dashboard items.
*   **Create**: Adding new dashboard items.
*   **Update**: Modifying existing dashboard items.
*   **Delete**: Removing dashboard items.

We will implement these standard CRUD functionalities in Django.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The `Dashboard.aspx` file contains no specific ASP.NET UI controls (like `GridView`, `TextBox`, `Button`). It primarily uses `<asp:Content>` blocks to integrate with a `MasterPage.master`. The only identified client-side element is a `<script>` tag referencing `loadingNotifier.js`.

For the Django modernization, we will infer the following UI components to provide a full example of a dashboard that manages hypothetical `DashboardItem` records:

*   **List View**: A table (DataTables) to display `DashboardItem` records, including search, sort, and pagination.
*   **Action Buttons**: "Add New" button to open a modal for creating new items, and "Edit" / "Delete" buttons for each item row to open modals for updating or confirming deletion.
*   **Forms**: Input fields (text inputs, dropdowns) for creating and editing `DashboardItem` details.

### Step 4: Generate Django Code

We will create a new Django application, `materialcosting`, to house the modernized dashboard functionality, aligning with the `Module_MaterialCosting_Masters_Dashboard` namespace in the original ASP.NET code.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

We will create a `DashboardItem` model to represent items displayed on the dashboard.

```python
# materialcosting/models.py
from django.db import models
from django.utils import timezone

class DashboardItem(models.Model):
    id = models.AutoField(db_column='id', primary_key=True)
    name = models.CharField(db_column='name', max_length=255, verbose_name='Item Name')
    description = models.TextField(db_column='description', blank=True, null=True, verbose_name='Description')
    status = models.CharField(db_column='status', max_length=50, default='Active', verbose_name='Status')
    created_at = models.DateTimeField(db_column='created_at', auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(db_column='updated_at', auto_now=True, verbose_name='Last Updated')

    class Meta:
        managed = False  # Set to True if Django should manage table creation/alteration
        db_table = 'dashboard_items'
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_status_display(self):
        """Returns a more human-readable status."""
        return self.status.capitalize()

    def update_item_status(self, new_status):
        """
        Business logic to update the status of the dashboard item.
        Ensures status is one of allowed values.
        """
        allowed_statuses = ['Active', 'Inactive', 'Archived']
        if new_status in allowed_statuses:
            self.status = new_status
            self.save()
            return True
        return False

    def is_active(self):
        """Checks if the item is currently active."""
        return self.status == 'Active'
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

```python
# materialcosting/forms.py
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    class Meta:
        model = DashboardItem
        fields = ['name', 'description', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'status': forms.Select(choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Archived', 'Archived')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'name': 'Item Name',
            'description': 'Description',
            'status': 'Current Status',
        }
        
    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise forms.ValidationError("Name must be at least 3 characters long.")
        return name
        
    def clean_status(self):
        status = self.cleaned_data['status']
        allowed_statuses = ['Active', 'Inactive', 'Archived']
        if status not in allowed_statuses:
            raise forms.ValidationError(f"Invalid status. Must be one of: {', '.join(allowed_statuses)}")
        return status
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

We also add a `TablePartialView` to handle HTMX requests for refreshing the DataTables content.

```python
# materialcosting/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm
from django.template.loader import render_to_string

class DashboardItemListView(ListView):
    model = DashboardItem
    template_name = 'materialcosting/dashboarditem/list.html'
    context_object_name = 'dashboarditems'

class DashboardItemTablePartialView(ListView):
    model = DashboardItem
    template_name = 'materialcosting/dashboarditem/_dashboarditem_table.html'
    context_object_name = 'dashboarditems'
    
    def get_queryset(self):
        # Example of adding simple filtering if needed
        queryset = super().get_queryset()
        # You could add filtering logic here based on self.request.GET
        return queryset

class DashboardItemCreateView(CreateView):
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'materialcosting/dashboarditem/form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

class DashboardItemUpdateView(UpdateView):
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'materialcosting/dashboarditem/form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

class DashboardItemDeleteView(DeleteView):
    model = DashboardItem
    template_name = 'materialcosting/dashboarditem/confirm_delete.html'
    success_url = reverse_lazy('dashboarditem_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

Remember, `base.html` code is NOT included, only `extends` reference.

```html
{# materialcosting/dashboarditem/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Dashboard Items</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Dashboard Item
        </button>
    </div>
    
    <div id="dashboarditemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    // HTMX global event listeners for messages and modal closing
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.successful && evt.detail.xhr.status === 204) {
            // Close modal if request was successful and response is 204 (No Content)
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });
</script>
{% endblock %}
```

```html
{# materialcosting/dashboarditem/_dashboarditem_table.html #}
<table id="dashboarditemTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg">
    <thead>
        <tr class="text-left">
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in dashboarditems %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.description|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.status }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2 text-sm"
                    hx-get="{% url 'dashboarditem_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-sm"
                    hx-get="{% url 'dashboarditem_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 text-center text-gray-500">No dashboard items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#dashboarditemTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 4] } // Disable sorting on SN and Actions columns
        ]
    });
});
</script>
```

```html
{# materialcosting/dashboarditem/form.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# materialcosting/dashboarditem/confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Dashboard Item "{{ object.name }}"?</p>
    <form hx-post="{% url 'dashboarditem_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

```python
# materialcosting/urls.py
from django.urls import path
from .views import (
    DashboardItemListView, 
    DashboardItemCreateView, 
    DashboardItemUpdateView, 
    DashboardItemDeleteView,
    DashboardItemTablePartialView
)

urlpatterns = [
    path('dashboarditems/', DashboardItemListView.as_view(), name='dashboarditem_list'),
    path('dashboarditems/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('dashboarditems/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('dashboarditems/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
    # HTMX-specific endpoint for refreshing the table
    path('dashboarditems/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'),
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

```python       
# materialcosting/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardItem
from .forms import DashboardItemForm
from django.contrib.messages import get_messages

class DashboardItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.item1 = DashboardItem.objects.create(
            name='Test Item 1',
            description='Description for Test Item 1',
            status='Active'
        )
        cls.item2 = DashboardItem.objects.create(
            name='Test Item 2',
            description='Description for Test Item 2',
            status='Inactive'
        )
  
    def test_dashboard_item_creation(self):
        item = DashboardItem.objects.get(id=self.item1.id)
        self.assertEqual(item.name, 'Test Item 1')
        self.assertEqual(item.description, 'Description for Test Item 1')
        self.assertEqual(item.status, 'Active')
        self.assertIsNotNone(item.created_at)
        self.assertIsNotNone(item.updated_at)
        
    def test_name_label(self):
        item = DashboardItem.objects.get(id=self.item1.id)
        field_label = item._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'Item Name')

    def test_str_method(self):
        item = DashboardItem.objects.get(id=self.item1.id)
        self.assertEqual(str(item), item.name)
        
    def test_update_item_status_method(self):
        item = DashboardItem.objects.get(id=self.item1.id)
        # Test valid status update
        self.assertTrue(item.update_item_status('Inactive'))
        item.refresh_from_db()
        self.assertEqual(item.status, 'Inactive')
        
        # Test invalid status update
        self.assertFalse(item.update_item_status('InvalidStatus'))
        item.refresh_from_db()
        self.assertEqual(item.status, 'Inactive') # Should not have changed

    def test_is_active_method(self):
        item = DashboardItem.objects.get(id=self.item1.id)
        self.assertTrue(item.is_active())
        item.status = 'Inactive'
        item.save()
        self.assertFalse(item.is_active())

class DashboardItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.item = DashboardItem.objects.create(
            name='View Test Item',
            description='Description for view test',
            status='Active'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/dashboarditem/list.html')
        self.assertTrue('dashboarditems' in response.context)
        self.assertContains(response, 'View Test Item')
    
    def test_table_partial_view(self):
        response = self.client.get(reverse('dashboarditem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/dashboarditem/_dashboarditem_table.html')
        self.assertTrue('dashboarditems' in response.context)
        self.assertContains(response, 'View Test Item')
        self.assertContains(response, 'id="dashboarditemTable"') # Verify DataTables element
        
    def test_create_view_get(self):
        response = self.client.get(reverse('dashboarditem_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/dashboarditem/form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], DashboardItemForm)
        
    def test_create_view_post_success(self):
        data = {
            'name': 'New Dashboard Item',
            'description': 'A fresh item for the dashboard.',
            'status': 'Active',
        }
        response = self.client.post(reverse('dashboarditem_add'), data, follow=True)
        self.assertEqual(response.status_code, 200) # Should be 200 after redirect
        self.assertTrue(DashboardItem.objects.filter(name='New Dashboard Item').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item added successfully.')

    def test_create_view_post_htmx_success(self):
        data = {
            'name': 'HTMX New Item',
            'description': 'Added via HTMX.',
            'status': 'Active',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDashboardItemList')
        self.assertTrue(DashboardItem.objects.filter(name='HTMX New Item').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item added successfully.')
        
    def test_create_view_post_invalid(self):
        data = {
            'name': 'No', # Too short
            'description': 'Invalid item.',
            'status': 'Active',
        }
        response = self.client.post(reverse('dashboarditem_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-renders
        self.assertFalse(DashboardItem.objects.filter(name='No').exists())
        self.assertContains(response, "Name must be at least 3 characters long.")
        
    def test_update_view_get(self):
        response = self.client.get(reverse('dashboarditem_edit', args=[self.item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/dashboarditem/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.item)

    def test_update_view_post_success(self):
        data = {
            'name': 'Updated Item Name',
            'description': 'This item has been updated.',
            'status': 'Inactive',
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.id]), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, 'Updated Item Name')
        self.assertEqual(self.item.status, 'Inactive')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Dashboard Item updated successfully.')

    def test_update_view_post_htmx_success(self):
        data = {
            'name': 'HTMX Updated Item',
            'description': 'Updated via HTMX.',
            'status': 'Archived',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.id]), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDashboardItemList')
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, 'HTMX Updated Item')
        self.assertEqual(self.item.status, 'Archived')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Dashboard Item updated successfully.')
        
    def test_delete_view_get(self):
        response = self.client.get(reverse('dashboarditem_delete', args=[self.item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/dashboarditem/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.item)
        
    def test_delete_view_post_success(self):
        item_to_delete = DashboardItem.objects.create(name='Item to Delete', status='Active')
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete.id]), follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertFalse(DashboardItem.objects.filter(id=item_to_delete.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Dashboard Item deleted successfully.')

    def test_delete_view_post_htmx_success(self):
        item_to_delete = DashboardItem.objects.create(name='HTMX Delete Item', status='Active')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204) # No Content
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDashboardItemList')
        self.assertFalse(DashboardItem.objects.filter(id=item_to_delete.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Dashboard Item deleted successfully.')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The modernized Django application fully leverages HTMX and Alpine.js for dynamic, reactive user interfaces, ensuring a smooth, single-page application feel without the complexity of traditional JavaScript frameworks.

*   **HTMX for CRUD Interactions**:
    *   **List View Refresh**: The main `dashboarditem/list.html` uses `hx-trigger="load, refreshDashboardItemList from:body"` on the `dashboarditemTable-container` div. This ensures that the table content (`_dashboarditem_table.html` partial) is loaded initially and automatically reloaded whenever a `refreshDashboardItemList` custom event is triggered from anywhere on the page body.
    *   **Modal Forms**: "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch the respective form (`form.html` or `confirm_delete.html`) into the `#modalContent` div.
    *   **Form Submission**: All forms within the modals use `hx-post` to submit data back to the server. Upon successful submission (handled by the Django views returning `HttpResponse(status=204, headers={'HX-Trigger': 'refreshDashboardItemList'})`), HTMX receives the `204 No Content` status, which does not swap any content but triggers the `refreshDashboardItemList` event. This event then causes the main list view to reload its table content, effectively updating the data without a full page refresh.
    *   **Modal Closing**: After a successful HTMX submission that returns `204`, a global HTMX event listener (`htmx:afterRequest`) detects this and automatically removes the `is-active` class from the modal, closing it gracefully.

*   **Alpine.js for UI State Management**:
    *   The modal's visibility is managed by Alpine.js. The `hidden` class is toggled by `on click add .is-active to #modal` when a button is clicked, and `on click if event.target.id == 'modal' remove .is-active from me` allows closing the modal by clicking outside its content. This provides immediate client-side UI feedback and control.
    *   While not explicitly used for complex state in this simple CRUD, Alpine.js is ready for more intricate UI behaviors such as dynamic filtering, client-side form validation feedback, or tabbed interfaces if the dashboard grows.

*   **DataTables for List Views**:
    *   The `_dashboarditem_table.html` partial directly integrates DataTables. The `<script>` block within the partial ensures that DataTables is initialized on the `<table>` element (`#dashboarditemTable`) once the partial is loaded by HTMX. This provides immediate client-side search, sorting, and pagination capabilities for large datasets, offloading these operations from the server and improving user experience.
    *   The `columnDefs` in the DataTables initialization ensures that the 'SN' (serial number) and 'Actions' columns are not sortable, which is common practice for such columns.

*   **DRY Template Inheritance**:
    *   All templates explicitly `{% extends 'core/base.html' %}`. This means that all core assets (like CDN links for HTMX, Alpine.js, jQuery, DataTables, Tailwind CSS) are included only once in `base.html`, keeping individual component templates lean and focused.

## Final Notes

*   **Placeholders**: All placeholders (`[MODEL_NAME]`, `[FIELD1]`, etc.) have been replaced with `DashboardItem`, `name`, `description`, `status` to provide a concrete, runnable example based on the inferred requirements for a dashboard.
*   **DRY Principle**: Templates are designed to be partials and reusable. For instance, `form.html` serves both create and update operations, loaded dynamically via HTMX. The `_dashboarditem_table.html` ensures that only the table content needs to be refreshed.
*   **Fat Model, Thin View**: Business logic for `DashboardItem` (e.g., `update_item_status`, `is_active`) resides within the `DashboardItem` model, keeping the Django views (`DashboardItemCreateView`, `UpdateView`, `DeleteView`) concise and focused on handling HTTP requests and responses.
*   **Comprehensive Tests**: Unit tests cover model logic, and integration tests ensure all view functionalities (GET/POST for list, create, update, delete) work correctly, including specific tests for HTMX request/response headers. This ensures high test coverage and system reliability.
*   **Tailwind CSS**: Form widgets are configured with Tailwind CSS classes, demonstrating seamless styling integration without custom CSS. The overall layout leverages Tailwind's utility-first approach for responsive and modern UI.

This comprehensive plan provides a clear, actionable roadmap for modernizing the legacy ASP.NET Dashboard page into a robust, maintainable, and highly interactive Django application using modern web development best practices.