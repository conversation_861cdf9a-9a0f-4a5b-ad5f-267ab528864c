## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code utilizes `SqlDataSource` components to interact with the database.

*   `LocalSqlServer` references `tblDG_Material` for CRUD operations.
*   `SqlDataSource1` references `vw_Unit_Master` for the "Unit" dropdown, which likely corresponds to the `Unit_Master` table.

**Extracted Schema:**

*   **Table 1: `tblDG_Material`**
    *   `Id` (Primary Key, inferred `Int32`)
    *   `Material` (String, inferred `String`)
    *   `Unit` (Foreign Key to `Unit_Master.Id`, inferred `Int32`)
    *   `SysDate` (System Date, `String`)
    *   `SysTime` (System Time, `String`)
    *   `CompId` (Company ID, `Int32`)
    *   `FinYearId` (Financial Year ID, `Int32`)
    *   `SessionId` (Session/User ID, `String`)

*   **Table 2: `Unit_Master` (or `vw_Unit_Master`)**
    *   `Id` (Primary Key, `Int32`)
    *   `Symbol` (Unit Symbol, `String`)

### Step 2: Identify Backend Functionality

**Analysis:**
The `GridView1` control and its associated `SqlDataSource` handle all core CRUD functionalities.

*   **Create (Add):** Triggered by `btnInsert` in the `GridView` footer or `EmptyDataTemplate`. Handled by `GridView1_RowCommand` with `CommandName="Add"` or `CommandName="Add1"`. Inserts `Material` and `Unit`, along with `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId` from utility functions and session variables.
*   **Read (Select):** Handled by `GridView1`'s data binding to `LocalSqlServer`'s `SelectCommand`. Selects `Id`, `Material`, and `Unit_Master.Symbol` (aliased as `Unit`) from `tblDG_Material` joined with `Unit_Master`.
*   **Update (Edit):** Triggered by the `Edit` `CommandField`. Handled by `GridView1_RowCommand` with `CommandName="Update"`. Updates `Material` and `Unit`, along with `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`.
*   **Delete (Delete):** Triggered by the `Delete` `CommandField`. Handled by `GridView1_RowDeleted`. Deletes based on `Id`.

**Validation Logic:**
*   `RequiredFieldValidator` for `txtMaterial` and `txtMaterial1` ensures the `Material` field is not empty.

### Step 3: Infer UI Components

**Analysis:**
The page displays a list of material categories and allows inline editing, adding new entries, and deleting existing ones using a `GridView`.

*   **Main Display:** `asp:GridView` presenting `Material` and `Unit` (symbol).
*   **Input Fields:**
    *   `asp:TextBox` for "Material Name" (both for adding and editing).
    *   `asp:DropDownList` for "Unit" (both for adding and editing), populated from `SqlDataSource1` (Unit_Master).
*   **Action Buttons:**
    *   "Edit" and "Delete" `LinkButton`s generated by `asp:CommandField`.
    *   "Insert" `asp:Button` in the footer/empty template.
*   **Messages:** `asp:Label ID="lblMessage"` for feedback (e.g., "Record Updated", "Record Deleted", "Record Inserted").
*   **Client-side interactivity:** `OnClientClick="return confirmationAdd()"` etc., suggests pop-up confirmations which will be replaced by HTMX/Alpine.js modals.

---

## Step 4: Generate Django Code

### 4.1 Models
(File: `materialcosting/models.py`)

```python
from django.db import models
from django.utils import timezone # For SysDate, SysTime
import datetime # For SysDate, SysTime


class Unit(models.Model):
    """
    Represents the Unit_Master table or vw_Unit_Master view.
    Used for material units (e.g., 'KG', 'MTR').
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50) # Assuming max_length based on typical unit symbols

    class Meta:
        managed = False
        db_table = 'Unit_Master' # Or 'vw_Unit_Master' if it's strictly a view and no direct table exists
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class MaterialCategory(models.Model):
    """
    Represents the tblDG_Material table, storing material categories.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    material_name = models.CharField(db_column='Material', max_length=255, verbose_name='Material Name')
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit', verbose_name='Unit') # DO_NOTHING as per legacy DB behavior
    
    # Audit fields - mapped directly from legacy ASP.NET DB schema
    sys_date = models.CharField(db_column='SysDate', max_length=10, blank=True, null=True) # YYYY-MM-DD
    sys_time = models.CharField(db_column='SysTime', max_length=8, blank=True, null=True)  # HH:MM:SS
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Typically maps to username/user_id

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'tblDG_Material'
        verbose_name = 'Material Category'
        verbose_name_plural = 'Material Categories'
        ordering = ['-id'] # To match 'order by Id Desc' in original query

    def __str__(self):
        return self.material_name

    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('materialcategory_detail', kwargs={'pk': self.pk})

    # No specific business logic identified for MaterialCategory beyond CRUD,
    # so no complex methods are added here.
    # If complex validation or calculations were present in C#, they would go here.

```

### 4.2 Forms
(File: `materialcosting/forms.py`)

```python
from django import forms
from .models import MaterialCategory, Unit

class MaterialCategoryForm(forms.ModelForm):
    # The 'unit' field needs to be a ModelChoiceField to render as a dropdown
    # and correctly handle the foreign key relationship.
    unit = forms.ModelChoiceField(
        queryset=Unit.objects.all(),
        empty_label="Select Unit",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Unit" # Explicitly set label, as db_column name is 'Unit'
    )

    class Meta:
        model = MaterialCategory
        # Only include fields that are user-editable in the form.
        # Audit fields (sys_date, sys_time, comp_id, fin_year_id, session_id)
        # are handled in the view's form_valid method.
        fields = ['material_name', 'unit']
        widgets = {
            'material_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_material_name(self):
        material_name = self.cleaned_data.get('material_name')
        if not material_name:
            raise forms.ValidationError("Material Name is required.")
        return material_name

    # No other specific validation from ASP.NET beyond 'required' identified.
    # If there were unique constraints or other business rules, they would be added here.
```

### 4.3 Views
(File: `materialcosting/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from django.utils import timezone
import datetime

from .models import MaterialCategory, Unit
from .forms import MaterialCategoryForm

# This view renders the main page with the modal structure and HTMX placeholder for the table.
class MaterialCategoryListView(ListView):
    model = MaterialCategory
    template_name = 'materialcosting/materialcategory/list.html'
    context_object_name = 'materialcategories' # Matches the plural lower for template loop

    def get_queryset(self):
        # The actual data for the DataTable is fetched by MaterialCategoryTablePartialView
        # This view primarily sets up the page structure.
        return MaterialCategory.objects.none() # No initial data for main list view itself

# This view specifically renders the table partial for HTMX requests.
class MaterialCategoryTablePartialView(ListView):
    model = MaterialCategory
    template_name = 'materialcosting/materialcategory/_materialcategory_table.html'
    context_object_name = 'materialcategories'

    # The original ASP.NET was ordering by Id Desc.
    def get_queryset(self):
        return MaterialCategory.objects.select_related('unit').all().order_by('-id')

class MaterialCategoryCreateView(CreateView):
    model = MaterialCategory
    form_class = MaterialCategoryForm
    template_name = 'materialcosting/materialcategory/_materialcategory_form.html'
    success_url = reverse_lazy('materialcategory_list')

    def form_valid(self, form):
        # Auto-populate audit fields based on ASP.NET code's behavior
        # In a real system, these would likely come from context processors, middleware, or user session
        current_date = timezone.now().strftime("%Y-%m-%d")
        current_time = timezone.now().strftime("%H:%M:%S")
        
        # Mimicking ASP.NET session values. In Django, this would be:
        # self.request.user.username (for SessionId)
        # self.request.session.get('compid') or a Company model (for CompId)
        # self.request.session.get('finyearid') or a FinancialYear model (for FinYearId)
        
        form.instance.sys_date = current_date
        form.instance.sys_time = current_time
        form.instance.company_id = 1 # Placeholder for CompId, adjust as per actual system
        form.instance.financial_year_id = 2023 # Placeholder for FinYearId, adjust as per actual system
        form.instance.session_id = self.request.user.username if self.request.user.is_authenticated else "anonymous" # Placeholder for SessionId

        response = super().form_valid(form)
        messages.success(self.request, 'Material Category added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to prevent full page reload
            # and trigger a refresh of the table via HX-Trigger
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialCategoryList'
                }
            )
        return response

class MaterialCategoryUpdateView(UpdateView):
    model = MaterialCategory
    form_class = MaterialCategoryForm
    template_name = 'materialcosting/materialcategory/_materialcategory_form.html'
    success_url = reverse_lazy('materialcategory_list')

    def form_valid(self, form):
        # Update audit fields similarly to creation if desired, or leave as original
        current_date = timezone.now().strftime("%Y-%m-%d")
        current_time = timezone.now().strftime("%H:%M:%S")
        form.instance.sys_date = current_date
        form.instance.sys_time = current_time
        form.instance.company_id = 1 # Maintain consistency or update if business logic dictates
        form.instance.financial_year_id = 2023 # Maintain consistency or update if business logic dictates
        form.instance.session_id = self.request.user.username if self.request.user.is_authenticated else "anonymous"

        response = super().form_valid(form)
        messages.success(self.request, 'Material Category updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialCategoryList'
                }
            )
        return response

class MaterialCategoryDeleteView(DeleteView):
    model = MaterialCategory
    template_name = 'materialcosting/materialcategory/_materialcategory_confirm_delete.html'
    success_url = reverse_lazy('materialcategory_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialCategoryList'
                }
            )
        return response

```

### 4.4 Templates

**`materialcosting/materialcategory/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Material Categories{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Categories</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'materialcategory_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Material Category
        </button>
    </div>
    
    <div id="materialcategoryTable-container"
         hx-trigger="load, refreshMaterialCategoryList from:body"
         hx-get="{% url 'materialcategory_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Categories...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally if used for app-wide UI state.
    // For this modal example, the `hidden` class and `on click` are sufficient
    // though Alpine.js could manage modal state (x-data="{ isOpen: false }").
    // The `is-active` class is a helper to immediately show the modal for initial load.
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
        }
    });

    document.addEventListener('htmx:beforeRequest', function(evt) {
        // Show loading spinner inside modalContent or other areas if needed
        if (evt.detail.target && evt.detail.target.id === 'modalContent') {
            evt.detail.target.innerHTML = `
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading form...</p>
                </div>
            `;
        }
    });

    // Handle messages from Django
    document.body.addEventListener('htmx:afterRequest', function(event) {
        const messages = event.detail.xhr.getResponseHeader('HX-Trigger-After-Swap');
        if (messages) {
            try {
                const parsedMessages = JSON.parse(messages);
                if (parsedMessages.messages) {
                    parsedMessages.messages.forEach(msg => {
                        // Display Django messages (e.g., using a toast library or custom logic)
                        console.log(msg.message); // For demonstration
                        // Example: alert(msg.message);
                        // A more robust solution would integrate with a global message display system
                    });
                }
            } catch (e) {
                console.error("Failed to parse HX-Trigger-After-Swap messages:", e);
            }
        }
    });

</script>
{% endblock %}
```

**`materialcosting/materialcategory/_materialcategory_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="materialcategoryTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Material Name</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Unit</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in materialcategories %}
            <tr>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.material_name }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ obj.unit.symbol }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2"
                        hx-get="{% url 'materialcategory_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md"
                        hx-get="{% url 'materialcategory_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center text-gray-500">
                    No Material Categories found.
                    <button type="button" 
                        class="ml-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        hx-get="{% url 'materialcategory_add' %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Add New Material Category
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table has been loaded by HTMX
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#materialcategoryTable')) {
            $('#materialcategoryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>'
            });
        }
    });
</script>
```

**`materialcosting/materialcategory/_materialcategory_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Category</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) document.getElementById('modal').classList.remove('is-active');">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                Save
            </button>
        </div>
    </form>
</div>
```

**`materialcosting/materialcategory/_materialcategory_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Material Category: <strong>"{{ object.material_name }}"</strong>?</p>
    
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) document.getElementById('modal').classList.remove('is-active');">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs
(File: `materialcosting/urls.py`)

```python
from django.urls import path
from .views import (
    MaterialCategoryListView, 
    MaterialCategoryCreateView, 
    MaterialCategoryUpdateView, 
    MaterialCategoryDeleteView,
    MaterialCategoryTablePartialView
)

urlpatterns = [
    path('material-category/', MaterialCategoryListView.as_view(), name='materialcategory_list'),
    path('material-category/table/', MaterialCategoryTablePartialView.as_view(), name='materialcategory_table'), # For HTMX partial load
    path('material-category/add/', MaterialCategoryCreateView.as_view(), name='materialcategory_add'),
    path('material-category/edit/<int:pk>/', MaterialCategoryUpdateView.as_view(), name='materialcategory_edit'),
    path('material-category/delete/<int:pk>/', MaterialCategoryDeleteView.as_view(), name='materialcategory_delete'),
]

```

### 4.6 Tests
(File: `materialcosting/tests.py`)

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialCategory, Unit
from django.contrib.messages import get_messages
from unittest.mock import patch # For mocking timezone/datetime

class MaterialCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test Unit object for MaterialCategory to reference
        cls.unit_test = Unit.objects.create(id=101, symbol='KG')
        # Create test data for all tests
        cls.material_category_test = MaterialCategory.objects.create(
            id=1,
            material_name='Test Material',
            unit=cls.unit_test,
            sys_date='2023-01-01',
            sys_time='10:00:00',
            company_id=1,
            financial_year_id=2023,
            session_id='testuser'
        )
  
    def test_material_category_creation(self):
        obj = MaterialCategory.objects.get(id=1)
        self.assertEqual(obj.material_name, 'Test Material')
        self.assertEqual(obj.unit.symbol, 'KG')
        self.assertEqual(obj.unit.id, 101)
        self.assertEqual(obj.sys_date, '2023-01-01')
        self.assertEqual(obj.sys_time, '10:00:00')
        self.assertEqual(obj.company_id, 1)
        self.assertEqual(obj.financial_year_id, 2023)
        self.assertEqual(obj.session_id, 'testuser')
        
    def test_material_name_label(self):
        obj = MaterialCategory.objects.get(id=1)
        field_label = obj._meta.get_field('material_name').verbose_name
        self.assertEqual(field_label, 'Material Name')
        
    def test_unit_foreign_key_label(self):
        obj = MaterialCategory.objects.get(id=1)
        field_label = obj._meta.get_field('unit').verbose_name
        self.assertEqual(field_label, 'Unit')

    def test_str_representation(self):
        obj = MaterialCategory.objects.get(id=1)
        self.assertEqual(str(obj), 'Test Material')

    def test_unit_str_representation(self):
        obj = Unit.objects.get(id=101)
        self.assertEqual(str(obj), 'KG')

class MaterialCategoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test Unit object for MaterialCategory to reference
        cls.unit_test = Unit.objects.create(id=201, symbol='PCS') # Use a different ID for clarity in tests
        # Create test data for views
        cls.material_category_test = MaterialCategory.objects.create(
            id=1,
            material_name='Existing Material',
            unit=cls.unit_test,
            sys_date='2023-01-01',
            sys_time='10:00:00',
            company_id=1,
            financial_year_id=2023,
            session_id='setup_user'
        )
        
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
        # Mocking user login for session_id population in views
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(username='testuser', password='password')
        self.client.login(username='testuser', password='password')

    def test_list_view(self):
        response = self.client.get(reverse('materialcategory_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materialcategory/list.html')
        # The list view only renders the container, not the table data directly
        self.assertContains(response, '<div id="materialcategoryTable-container"')
        
    def test_table_partial_view_get(self):
        response = self.client.get(reverse('materialcategory_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materialcategory/_materialcategory_table.html')
        self.assertTrue('materialcategories' in response.context)
        self.assertContains(response, 'Existing Material')
        self.assertContains(response, 'PCS') # Check if unit symbol is displayed

    @patch('django.utils.timezone.now')
    def test_create_view_get(self, mock_now):
        # Mock timezone.now() for predictable audit field values
        mock_now.return_value = datetime.datetime(2024, 7, 1, 12, 30, 0, tzinfo=datetime.timezone.utc)
        response = self.client.get(reverse('materialcategory_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materialcategory/_materialcategory_form.html')
        self.assertTrue('form' in response.context)
        
    @patch('django.utils.timezone.now')
    def test_create_view_post_htmx(self, mock_now):
        # Mock timezone.now() for predictable audit field values
        mock_now.return_value = datetime.datetime(2024, 7, 1, 12, 30, 0, tzinfo=datetime.timezone.utc)
        
        new_unit = Unit.objects.create(id=301, symbol='MTR')
        data = {
            'material_name': 'New Material From HTMX',
            'unit': new_unit.id, # Use the ID of the Unit object
        }
        response = self.client.post(reverse('materialcategory_add'), data, HTTP_HX_REQUEST='true')
        # Expect 204 No Content for HTMX post
        self.assertEqual(response.status_code, 204)
        # Check HX-Trigger header for table refresh
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshMaterialCategoryList')
        # Verify object was created
        new_obj = MaterialCategory.objects.filter(material_name='New Material From HTMX').first()
        self.assertIsNotNone(new_obj)
        self.assertEqual(new_obj.unit, new_unit)
        self.assertEqual(new_obj.sys_date, '2024-07-01')
        self.assertEqual(new_obj.sys_time, '12:30:00')
        self.assertEqual(new_obj.session_id, 'testuser') # From mocked login

        messages = [str(m) for m in get_messages(response.wsgi_request)]
        self.assertIn('Material Category added successfully.', messages)

    @patch('django.utils.timezone.now')
    def test_update_view_get(self, mock_now):
        mock_now.return_value = datetime.datetime(2024, 7, 1, 12, 30, 0, tzinfo=datetime.timezone.utc)
        obj = MaterialCategory.objects.get(id=1)
        response = self.client.get(reverse('materialcategory_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materialcategory/_materialcategory_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    @patch('django.utils.timezone.now')
    def test_update_view_post_htmx(self, mock_now):
        mock_now.return_value = datetime.datetime(2024, 7, 1, 12, 30, 0, tzinfo=datetime.timezone.utc)
        
        obj = MaterialCategory.objects.get(id=1)
        updated_unit = Unit.objects.create(id=401, symbol='LBS')
        data = {
            'material_name': 'Updated Material',
            'unit': updated_unit.id,
        }
        response = self.client.post(reverse('materialcategory_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshMaterialCategoryList')
        obj.refresh_from_db()
        self.assertEqual(obj.material_name, 'Updated Material')
        self.assertEqual(obj.unit, updated_unit)
        self.assertEqual(obj.sys_date, '2024-07-01')
        self.assertEqual(obj.sys_time, '12:30:00')

        messages = [str(m) for m in get_messages(response.wsgi_request)]
        self.assertIn('Material Category updated successfully.', messages)
        
    def test_delete_view_get(self):
        obj = MaterialCategory.objects.get(id=1)
        response = self.client.get(reverse('materialcategory_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materialcategory/_materialcategory_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_htmx(self):
        obj = MaterialCategory.objects.create(
            id=2, material_name='Material To Delete', unit=self.unit_test,
            sys_date='2023-01-01', sys_time='10:00:00', company_id=1, financial_year_id=2023, session_id='testuser'
        )
        response = self.client.post(reverse('materialcategory_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshMaterialCategoryList')
        self.assertFalse(MaterialCategory.objects.filter(id=2).exists())

        messages = [str(m) for m in get_messages(response.wsgi_request)]
        self.assertIn('Material Category deleted successfully.', messages)

    def test_create_view_post_validation_htmx(self):
        data = {
            'material_name': '', # Empty material name
            'unit': self.unit_test.id,
        }
        response = self.client.post(reverse('materialcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertTemplateUsed(response, 'materialcosting/materialcategory/_materialcategory_form.html')
        self.assertContains(response, 'Material Name is required.')
        self.assertFalse(MaterialCategory.objects.filter(material_name='').exists())

```

---

## Step 5: HTMX and Alpine.js Integration

**Summary:**
*   **HTMX:**
    *   The `list.html` uses `hx-get` on a `div` with `hx-trigger="load, refreshMaterialCategoryList from:body"` to load the `_materialcategory_table.html` partial. This ensures the table loads on page load and refreshes whenever the `refreshMaterialCategoryList` custom event is triggered (e.g., after a successful CRUD operation).
    *   The "Add New", "Edit", and "Delete" buttons use `hx-get` to load the respective forms (`_materialcategory_form.html` or `_materialcategory_confirm_delete.html`) into the `#modalContent` div.
    *   Form submissions (POST requests) from `_materialcategory_form.html` and `_materialcategory_confirm_delete.html` use `hx-post` to their respective URLs.
    *   Crucially, these forms use `hx-swap="none"` and the view returns `HttpResponse(status=204, headers={'HX-Trigger': 'refreshMaterialCategoryList'})`. This design ensures no content is swapped on the modal, but the `HX-Trigger` causes the main table to reload, and a separate Alpine.js `on click` handles closing the modal.
*   **Alpine.js:**
    *   The `_` attribute `on click add .is-active to #modal` and `on click remove .is-active from #modal` are used directly on the buttons to toggle the modal's visibility (`hidden` class). This provides simple, local UI state management without a full Alpine component.
*   **DataTables:**
    *   The `_materialcategory_table.html` partial initializes `DataTables` using `$(document).ready()`. This ensures DataTables is applied to the table *after* it's loaded into the DOM via HTMX. The `if (!$.fn.DataTable.isDataTable(...))` check prevents re-initialization.

**Implementation Details:**
*   **Modal Activation/Deactivation:**
    *   When an "Add", "Edit", or "Delete" button is clicked, HTMX loads the form into `#modalContent`. The `_` (Alpine.js) attribute then adds the `is-active` class to `#modal` to make it visible.
    *   Upon successful form submission (HTMX 204 response), the `hx-on::after-request` event on the form removes the `is-active` class from `#modal`, closing it.
    *   The "Cancel" buttons also use `_` to remove `is-active`, providing a client-side way to close the modal.
    *   Clicking outside the modal (on the `#modal` div itself) also closes it via `_="on click if event.target.id == 'modal' remove .is-active from me"`.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Material Category module to a modern Django application. It adheres to the principles of fat models/thin views, leverages HTMX and Alpine.js for a dynamic user experience, incorporates DataTables for efficient data presentation, and includes robust testing to ensure functionality and maintainability. The focus on automation-driven approaches means that each section is designed for systematic conversion and integration into a larger Django project. The communication uses plain English to ensure all stakeholders can understand the value and process of this modernization.