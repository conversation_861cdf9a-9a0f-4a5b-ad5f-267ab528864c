## ASP.NET to Django Conversion Script: SPR Print Details

This document outlines the modernization plan for the ASP.NET `SPR_Print_Details.aspx` page to a modern Django application. The original page served as a report viewer for "Supplier Purchase Request" (SPR) details, utilizing Crystal Reports for data presentation. Our Django modernization will transform this into an interactive web page using Django's ORM, HTMX, Alpine.js, and DataTables, adhering to a fat model/thin view architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code fetches data by joining numerous tables to compile a report for an SPR. The core tables involved are `tblMM_SPR_Master` (for master SPR details), `tblMM_SPR_Details` (for line items), and several lookup/master tables like `tblDG_Item_Master`, `tblMM_Supplier_master`, `tblHR_OfficeStaff`, `Unit_Master`, `AccHead`, `tblCompany_master`, and `BusinessGroup`.

**Inferred Schema (Key Tables for this Report):**

*   **`tblMM_SPR_Master` (Main SPR Header)**
    *   `Id` (PK)
    *   `SPRNo` (string)
    *   `SysDate` (datetime)
    *   `CheckedBy` (FK to `tblHR_OfficeStaff.EmpId`)
    *   `ApprovedBy` (FK to `tblHR_OfficeStaff.EmpId`)
    *   `AuthorizedBy` (FK to `tblHR_OfficeStaff.EmpId`)
    *   `CheckedDate` (datetime)
    *   `ApproveDate` (datetime)
    *   `AuthorizeDate` (datetime)
    *   `CompId` (FK to `tblCompany_master.CompId`)
    *   `FinYearId` (int)
    *   `SessionId` (FK to `tblHR_OfficeStaff.EmpId` - likely the 'Intender' field in report)

*   **`tblMM_SPR_Details` (SPR Line Items)**
    *   `MId` (FK to `tblMM_SPR_Master.Id`)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `SupplierId` (FK to `tblMM_Supplier_master.SupplierId`)
    *   `DeptId` (FK to `BusinessGroup.Id`)
    *   `Qty` (decimal)
    *   `Rate` (decimal)
    *   `Discount` (decimal)
    *   `WONo` (string)
    *   `DelDate` (datetime)
    *   `AHId` (FK to `AccHead.Id`)
    *   `Remarks` (string)

*   **Supporting Tables (Simplified for demonstration):**
    *   `tblDG_Item_Master`: `Id`, `ItemCode`, `ManfDesc`, `UOMBasic` (FK to `Unit_Master.Id`)
    *   `Unit_Master`: `Id`, `Symbol`
    *   `tblMM_Supplier_master`: `SupplierId`, `SupplierName`
    *   `AccHead`: `Id`, `Symbol`
    *   `tblHR_OfficeStaff`: `EmpId`, `Title`, `EmployeeName`
    *   `tblCompany_master`: `CompId`, `CompName`, `CompAddress` (assumed fields for `getCompany`, `CompAdd`)
    *   `BusinessGroup`: `Id`, `Symbol`

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET code primarily focuses on **Read** operations for generating a report.
*   **Create:** No, this page does not create new SPRs.
*   **Read:** Yes, extensively. It retrieves master SPR details, associated line items, and various related lookup data (item descriptions, supplier names, employee details, company info, department/business group names). It then formats this data for display.
*   **Update:** No.
*   **Delete:** No.
*   **Validation Logic:** Simple data type conversions and null checks are performed (e.g., `Convert.ToInt32`, `DBNull.Value`).

The core logic involves a complex SQL join and subsequent iteration to transform and enrich the data. This entire data processing pipeline will be moved into Django models, adhering to the "fat model" principle.

### Step 3: Infer UI Components

**Analysis:** The original page uses `CR:CrystalReportViewer` to display the report. This is a proprietary control for displaying pre-designed reports. In Django, this will be replaced with a standard HTML structure that presents the report data directly.
*   **CrystalReportViewer:** Replaced by a custom HTML layout with static text for master details and a DataTables-powered table for line items.
*   **Panel with ScrollBars:** Standard HTML `div` with CSS styling for scrolling.
*   **`asp:Button` (Cancel):** Replaced by an HTML `<a>` tag or `button` with HTMX for redirection, or a simple form button if it were part of a form. Since it navigates away, a standard Django URL redirect will be used.

### Step 4: Generate Django Code

Given that the ASP.NET page is primarily a **report viewing** page rather than a standard CRUD interface, we will adapt the Django implementation to reflect this. We will still define models for the primary entities (`SprMaster`, `SprDetail`) and related lookup tables, but the view will be a `DetailView` or `TemplateView` specifically designed to render the report data, rather than a full `ListView`/`CreateView`/`UpdateView`/`DeleteView` suite for *this specific page's functionality*. However, we will include the `SprMasterForm` as it would be part of a complete SPR module.

#### 4.1 Models (`material_management/models.py`)

This section defines the Django models mapping to the existing database tables. The crucial part here is the `SprMaster.objects.get_spr_report_data` method, which encapsulates the complex data retrieval and processing logic from the original C# code.

```python
from django.db import models
from django.db.models import F, Value, Func
from django.utils import timezone
from datetime import datetime

# --- Mocking external 'clsFunctions' for demonstration purposes ---
# In a real migration, these would be proper ORM queries or utility functions.
# They are being mocked here to demonstrate the data processing logic in the model.
class MockClsFunctions:
    def Connection(self):
        return "mock_connection_string" # Not used in Django ORM
    
    def select(self, columns, tables, where_clause):
        # This is a highly simplified mock. In reality, this would query the DB.
        # For our purpose, we'll assume it fetches some data based on common patterns.
        # This part requires careful mapping of original SQL logic to Django ORM.
        
        # Example for tblHR_OfficeStaff lookup based on EmpId
        if "tblHR_OfficeStaff" in tables and "EmpId='" in where_clause:
            emp_id = where_clause.split("EmpId='")[1].split("'")[0]
            if emp_id == '1': # Assuming 1, 2, 3 are example EmpIds for Checked/Approved/AuthorizedBy
                return [{"Title": "Mr", "EmployeeName": "John Doe"}]
            elif emp_id == '2':
                return [{"Title": "Ms", "EmployeeName": "Jane Smith"}]
            elif emp_id == '3':
                return [{"Title": "Dr", "EmployeeName": "Alice Brown"}]
            return []
        
        # Example for BusinessGroup lookup based on Id
        elif "BusinessGroup" in tables and "Id='" in where_clause:
            bg_id = where_clause.split("Id='")[1].split("'")[0]
            if bg_id == '1': # Example BusinessGroup Id
                return [{"Symbol": "Sales Dept"}]
            elif bg_id == '2':
                return [{"Symbol": "Production Dept"}]
            return []
        
        return [] # Default empty if not matched

    def FromDateDMY(self, date_str):
        if not date_str:
            return ""
        try:
            # Handle various common datetime formats from SQL Server or strings
            for fmt in ["%Y-%m-%d %H:%M:%S.%f", "%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%m/%d/%Y %I:%M:%S %p", "%m/%d/%Y"]:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime("%d-%m-%Y")
                except ValueError:
                    continue
            return date_str # Return as is if format is unknown or invalid
        except TypeError: # date_str might not be a string
            return ""

    def FromDate(self, date_str):
        if not date_str:
            return ""
        try:
            for fmt in ["%Y-%m-%d %H:%M:%S.%f", "%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%m/%d/%Y %I:%M:%S %p", "%m/%d/%Y"]:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime("%d %b %Y") # Example: 01 Jan 2023
                except ValueError:
                    continue
            return date_str
        except TypeError:
            return ""

    def GetItemCode_PartNo(self, comp_id, item_id):
        # This would be a DB lookup or cached value in a real system
        return f"ITEM-{item_id:04d}-{comp_id:02d}"

    def getCompany(self, comp_id):
        # This would be a DB lookup for company name
        return f"Fictional Company Inc. (ID:{comp_id})"

    def CompAdd(self, comp_id):
        # This would be a DB lookup for company address
        return f"123 Main St, Anytown, State {comp_id}, Zip 12345"

mock_fun = MockClsFunctions()
# --- End of Mocking ---


# Define related lookup models first
class Employee(models.Model):
    EmpId = models.AutoField(db_column='EmpId', primary_key=True)
    Title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.Title or ''}. {self.EmployeeName or ''}".strip()

class Unit(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.Symbol or ''

class Item(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    ItemCode = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True) # Actual ItemCode field, not GetItemCode_PartNo output
    ManfDesc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    UOMBasic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.ManfDesc or ''

class Supplier(models.Model):
    SupplierId = models.AutoField(db_column='SupplierId', primary_key=True)
    SupplierName = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.SupplierName or ''

class AccountHead(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.Symbol or ''

class BusinessGroup(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.Symbol or ''

class Company(models.Model):
    CompId = models.AutoField(db_column='CompId', primary_key=True)
    CompName = models.CharField(db_column='CompName', max_length=255, blank=True, null=True)
    CompAddress = models.CharField(db_column='CompAddress', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
    
    def __str__(self):
        return self.CompName or ''


# Main models for SPR
class SprMasterManager(models.Manager):
    def get_spr_report_data(self, spr_no, master_id, company_id, fin_year_id):
        # This method replicates the complex data retrieval and processing
        # found in the original ASP.NET Page_Init.
        # It aggregates data from multiple sources into a single report object.
        
        report_data = {
            'master': None,
            'details': [],
            'parameters': {}
        }

        try:
            # 1. Fetch SPR Master Data
            spr_master = self.filter(
                SPRNo=spr_no, 
                CompId=company_id, 
                Id=master_id,
                FinYearId=fin_year_id # Assuming FinYearId filtering on master
            ).select_related(
                'checked_by_employee', 'approved_by_employee', 'authorized_by_employee',
                'intender_employee', 'company' # Assuming SessionId maps to intender
            ).first()

            if not spr_master:
                # Handle case where SPR Master is not found
                return report_data

            report_data['master'] = {
                'SPRNo': spr_master.SPRNo,
                'RegDate': mock_fun.FromDate(str(spr_master.SysDate)) if spr_master.SysDate else '',
                'CheckedBy': str(spr_master.checked_by_employee) if spr_master.checked_by_employee else '',
                'ApprovedBy': str(spr_master.approved_by_employee) if spr_master.approved_by_employee else '',
                'AuthorizedBy': str(spr_master.authorized_by_employee) if spr_master.authorized_by_employee else '',
                'CheckedDate': mock_fun.FromDate(str(spr_master.CheckedDate)) if spr_master.CheckedDate else '',
                'ApproveDate': mock_fun.FromDate(str(spr_master.ApproveDate)) if spr_master.ApproveDate else '',
                'AuthorizeDate': mock_fun.FromDate(str(spr_master.AuthorizeDate)) if spr_master.AuthorizeDate else '',
                'Company': mock_fun.getCompany(company_id),
                'Address': mock_fun.CompAdd(company_id),
                'Intender': str(spr_master.intender_employee) if spr_master.intender_employee else '',
            }

            # 2. Fetch SPR Details (Line Items)
            spr_details_queryset = SprDetail.objects.filter(
                spr_master=spr_master # Use direct ForeignKey relationship
            ).select_related(
                'item', 'item__uom_basic', 'supplier', 'account_head', 'department'
            )

            for detail in spr_details_queryset:
                item_code_part_no = mock_fun.GetItemCode_PartNo(company_id, detail.item.Id)
                
                # Logic for WONo vs. Business Group (DeptId)
                won_or_bg = ""
                dept_name = ""
                if detail.DeptId == 0 and detail.WONo: # DeptId == 0 implies WONo takes precedence for display
                    won_or_bg = f"WONo - {detail.WONo}"
                elif detail.department: # If DeptId is not 0 and department exists
                    dept_name = detail.department.Symbol
                    won_or_bg = f"BG Group - {detail.department.Symbol}"
                
                # Convert decimal fields to specific format (N3 for Qty, N2 for Rate)
                qty_formatted = f"{detail.Qty:.3f}" if detail.Qty is not None else ""
                rate_formatted = f"{detail.Rate:.2f}" if detail.Rate is not None else ""
                discount_formatted = f"{detail.Discount:.2f}" if detail.Discount is not None else ""


                report_data['details'].append({
                    'ItemCode': item_code_part_no,
                    'PurchDesc': detail.item.ManfDesc if detail.item else '',
                    'UomPurch': detail.item.uom_basic.Symbol if detail.item and detail.item.uom_basic else '',
                    'DelDate': mock_fun.FromDateDMY(str(detail.DelDate)) if detail.DelDate else '',
                    'SPRQTY': qty_formatted,
                    'Rate': rate_formatted,
                    'WONo_Or_BG': won_or_bg, # This combines WONo/BG Group for display
                    'SupplierName': f"{detail.supplier.SupplierName} [ {detail.supplier.SupplierId} ]" if detail.supplier else '',
                    'DeptName': dept_name, # Separate field for department name from BG Group
                    'AcHead': detail.account_head.Symbol if detail.account_head else '',
                    'Remarks': detail.Remarks or '',
                    'CompId': company_id,
                    'Intender': report_data['master'].get('Intender', ''), # Re-use intender from master
                    'Discount': discount_formatted,
                })
        except Exception as e:
            # Log the exception for debugging
            print(f"Error fetching SPR report data: {e}")
            report_data = {
                'master': None,
                'details': [],
                'parameters': {}
            }
        
        return report_data

class SprMaster(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)
    SPRNo = models.CharField(db_column='SPRNo', max_length=50)
    SysDate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    CheckedBy = models.IntegerField(db_column='CheckedBy', blank=True, null=True) # Stored as int, FK to EmpId
    ApprovedBy = models.IntegerField(db_column='ApprovedBy', blank=True, null=True) # Stored as int, FK to EmpId
    AuthorizedBy = models.IntegerField(db_column='AuthorizedBy', blank=True, null=True) # Stored as int, FK to EmpId
    CheckedDate = models.DateTimeField(db_column='CheckedDate', blank=True, null=True)
    ApproveDate = models.DateTimeField(db_column='ApproveDate', blank=True, null=True)
    AuthorizeDate = models.DateTimeField(db_column='AuthorizeDate', blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId') # Stored as int, FK to Company.CompId
    FinYearId = models.IntegerField(db_column='FinYearId')
    SessionId = models.IntegerField(db_column='SessionId', blank=True, null=True) # Intender

    # Explicit Django FKs for easier ORM lookups and `select_related`
    checked_by_employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='CheckedBy', related_name='spr_checked_by', blank=True, null=True)
    approved_by_employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='ApprovedBy', related_name='spr_approved_by', blank=True, null=True)
    authorized_by_employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='AuthorizedBy', related_name='spr_authorized_by', blank=True, null=True)
    intender_employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', related_name='spr_intender', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', related_name='spr_masters', blank=True, null=True)

    objects = SprMasterManager()

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.SPRNo

    # Example business logic method (if any, typically not for report models)
    def calculate_total_amount(self):
        # This would require fetching related SprDetail objects
        total = sum(
            (detail.Qty or 0) * (detail.Rate or 0) * (1 - (detail.Discount or 0) / 100)
            for detail in self.spr_details.all()
        )
        return total


class SprDetail(models.Model):
    # Assuming MId and ItemId form a composite primary key, or there's an implicit PK.
    # If no explicit PK, Django creates an 'id' field by default.
    # For this example, we'll assume Django's default 'id' PK is sufficient or it's implicitly a composite PK.
    # The original query indicates MId and ItemId are used in conditions.
    MId = models.IntegerField(db_column='MId') # Stored as int, FK to tblMM_SPR_Master.Id
    ItemId = models.IntegerField(db_column='ItemId') # Stored as int, FK to tblDG_Item_Master.Id
    SupplierId = models.IntegerField(db_column='SupplierId', blank=True, null=True) # Stored as int, FK to tblMM_Supplier_master.SupplierId
    DeptId = models.IntegerField(db_column='DeptId', blank=True, null=True) # Stored as int, FK to BusinessGroup.Id
    Qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    Rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2, blank=True, null=True)
    Discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, blank=True, null=True)
    WONo = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    DelDate = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    AHId = models.IntegerField(db_column='AHId', blank=True, null=True) # Stored as int, FK to AccHead.Id
    Remarks = models.CharField(db_column='Remarks', max_length=1000, blank=True, null=True)

    # Explicit Django FKs
    spr_master = models.ForeignKey(SprMaster, models.DO_NOTHING, db_column='MId', related_name='spr_details')
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId', related_name='spr_details')
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId', related_name='spr_details', blank=True, null=True)
    department = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', related_name='spr_details', blank=True, null=True)
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId', related_name='spr_details', blank=True, null=True)


    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'
        # If composite primary key, add: unique_together = (('MId', 'ItemId'),)

    def __str__(self):
        return f"Detail for SPR {self.MId} - Item {self.ItemId}"

```

#### 4.2 Forms (`material_management/forms.py`)

This specific ASP.NET page (`SPR_Print_Details.aspx`) is a *report view* and does not involve user input forms for creating or updating SPRs. Therefore, a form is not directly applicable to its functionality. However, in a full Django migration, you would typically have a form for `SprMaster` to create/edit SPRs. We will include a placeholder form here to adhere to the requested template, but it won't be used by the report view.

```python
from django import forms
from .models import SprMaster

class SprMasterForm(forms.ModelForm):
    class Meta:
        model = SprMaster
        # These fields are illustrative; they would correspond to actual editable fields
        # if this form were used for creating/updating an SPR.
        fields = ['SPRNo', 'SysDate', 'CheckedBy', 'ApprovedBy', 'AuthorizedBy'] 
        widgets = {
            'SPRNo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SysDate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'CheckedBy': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ApprovedBy': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'AuthorizedBy': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    # Add custom validation methods here if needed for SPR creation/update
    # def clean_SPRNo(self):
    #     spr_no = self.cleaned_data.get('SPRNo')
    #     # Example validation
    #     return spr_no

```

#### 4.3 Views (`material_management/views.py`)

The main view for this page will be a `TemplateView` or a custom `DetailView` that fetches the entire report data from the `SprMaster` model. An additional `TemplateView` will render just the DataTables portion of the report for HTMX partial updates, although for a static report, a single load is more typical.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.shortcuts import render, redirect
from django.http import HttpResponse, Http404
from django.contrib import messages
from .models import SprMaster, Employee, Item, Supplier, AccountHead, BusinessGroup, Unit, Company
from .forms import SprMasterForm # Included as per template, though not used by report view

class SprReportView(TemplateView):
    """
    Displays the detailed SPR report, replicating the functionality of SPR_Print_Details.aspx.
    This view fetches all necessary data via the fat model approach.
    """
    template_name = 'material_management/spr_report/detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters from query string (equivalent to Request.QueryString)
        # Note: In a real system, these would be robustly validated and handled.
        spr_no = self.request.GET.get('SPRNo')
        master_id_str = self.request.GET.get('Id')
        key = self.request.GET.get('Key') # Key is not directly used in Django rendering
        f_param = self.request.GET.get('f') # Parameter for Cancel button redirection

        # Extract parameters from session (equivalent to Session["compid"], Session["finyear"])
        # In Django, session data is accessed via self.request.session
        company_id = self.request.session.get('compid', 0) # Default to 0 or handle missing
        fin_year_id = self.request.session.get('finyear', 0) # Default to 0 or handle missing

        try:
            master_id = int(master_id_str) if master_id_str else None
        except ValueError:
            master_id = None
            messages.error(self.request, "Invalid Master ID provided.")
            # Or raise Http404("Invalid Master ID")
            
        if not (spr_no and master_id and company_id and fin_year_id):
            messages.error(self.request, "Missing required parameters to generate SPR report.")
            context['report_data'] = {'master': None, 'details': []}
            return context

        # Call the model's fat method to get all report data
        report_data = SprMaster.objects.get_spr_report_data(
            spr_no=spr_no, 
            master_id=master_id, 
            company_id=company_id, 
            fin_year_id=fin_year_id
        )

        context['report_data'] = report_data
        context['f_param'] = f_param # Pass 'f' for cancel button logic
        return context

# Optional: A separate view for the DataTables part if needed for dynamic loading
class SprReportDetailsTableView(View):
    """
    Renders only the SPR details table for HTMX requests.
    """
    def get(self, request, *args, **kwargs):
        spr_no = request.GET.get('SPRNo')
        master_id_str = request.GET.get('Id')
        company_id = request.session.get('compid', 0)
        fin_year_id = request.session.get('finyear', 0)

        try:
            master_id = int(master_id_str) if master_id_str else None
        except ValueError:
            master_id = None
        
        if not (spr_no and master_id and company_id and fin_year_id):
            return HttpResponse("Error: Missing parameters for details table.", status=400)

        report_data = SprMaster.objects.get_spr_report_data(
            spr_no=spr_no, 
            master_id=master_id, 
            company_id=company_id, 
            fin_year_id=fin_year_id
        )
        
        return render(request, 'material_management/spr_report/_spr_details_table.html', {
            'spr_details': report_data['details']
        })


def cancel_spr_report(request):
    """
    Handles the 'Cancel' button click, redirecting based on the 'f' query parameter.
    """
    f_param = request.GET.get('f')
    if f_param == '1':
        return redirect(reverse_lazy('spr_dashboard')) # Assumes a 'spr_dashboard' URL
    elif f_param == '2':
        return redirect(reverse_lazy('spr_print_list')) # Assumes a 'spr_print_list' URL for selection
    else:
        return redirect(reverse_lazy('home')) # Default redirect

```

#### 4.4 Templates (`material_management/spr_report/detail.html` and `_spr_details_table.html`)

We will create two templates: one for the full report page, and a partial template for the DataTables portion that can be loaded dynamically via HTMX.

**`material_management/spr_report/detail.html`**
This template displays the master SPR details and loads the item details table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">SPR - Print Details ({{ report_data.master.SPRNo|default:"N/A" }})</h2>
    </div>

    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ message }}</span>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if report_data.master %}
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">SPR Master Details</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <p><strong class="text-gray-600">Company:</strong> {{ report_data.master.Company }}</p>
                <p><strong class="text-gray-600">Address:</strong> {{ report_data.master.Address }}</p>
                <p><strong class="text-gray-600">SPR No:</strong> {{ report_data.master.SPRNo }}</p>
                <p><strong class="text-gray-600">Registration Date:</strong> {{ report_data.master.RegDate }}</p>
            </div>
            <div>
                <p><strong class="text-gray-600">Intender:</strong> {{ report_data.master.Intender }}</p>
                <p><strong class="text-gray-600">Checked By:</strong> {{ report_data.master.CheckedBy }} ({{ report_data.master.CheckedDate }})</p>
                <p><strong class="text-gray-600">Approved By:</strong> {{ report_data.master.ApprovedBy }} ({{ report_data.master.ApproveDate }})</p>
                <p><strong class="text-gray-600">Authorized By:</strong> {{ report_data.master.AuthorizedBy }} ({{ report_data.master.AuthorizeDate }})</p>
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">SPR Line Items</h3>
        <div id="sprDetailsTable-container"
             hx-trigger="load"
             hx-get="{% url 'spr_report_details_table' %}?SPRNo={{ report_data.master.SPRNo }}&Id={{ request.GET.Id }}&Key={{ request.GET.Key|urlencode }}"
             hx-swap="innerHTML">
            <!-- DataTables will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-500">Loading SPR details...</p>
            </div>
        </div>
    </div>
    {% else %}
    <div class="text-center text-gray-600 py-10">
        <p>No SPR data found or insufficient parameters provided.</p>
        <p>Please ensure SPR Number, Master ID, Company ID, and Financial Year are correctly set.</p>
    </div>
    {% endif %}

    <div class="mt-8 text-center">
        <a href="{% url 'cancel_spr_report' %}?f={{ f_param }}" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded inline-flex items-center">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
    // No specific Alpine.js component needed here unless for more complex UI states
    // but included as per general guidelines.
    document.addEventListener('alpine:init', () => {
        // Alpine.js global setup or data stores can go here
    });
</script>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

**`material_management/spr_report/_spr_details_table.html`**
This partial template contains only the HTML table for SPR line items, designed for DataTables.

```html
<div class="overflow-x-auto">
    <table id="sprDetailsTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Desc</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Del Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SPR Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No / BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acc Head</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in spr_details %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.ItemCode }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.PurchDesc }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.UomPurch }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.DelDate }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.SPRQTY }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.Rate }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.Discount }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.WONo_Or_BG }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.SupplierName }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.AcHead }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.Remarks }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-4 text-center text-gray-500">No SPR line items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        $('#sprDetailsTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true
        });
    });
</script>
```

#### 4.5 URLs (`material_management/urls.py`)

This file defines the URL patterns for accessing the SPR report and its partial components.

```python
from django.urls import path
from .views import SprReportView, SprReportDetailsTableView, cancel_spr_report
# If full CRUD was implemented for SprMaster:
# from .views import SprMasterListView, SprMasterCreateView, SprMasterUpdateView, SprMasterDeleteView

urlpatterns = [
    # URL for the main SPR report view
    path('spr/report/', SprReportView.as_view(), name='spr_report_detail'),
    
    # URL for the HTMX-loaded SPR details table partial
    path('spr/report/details_table/', SprReportDetailsTableView.as_view(), name='spr_report_details_table'),

    # URL for the Cancel button redirection
    path('spr/report/cancel/', cancel_spr_report, name='cancel_spr_report'),

    # Placeholder URLs if a full SPR Master CRUD was implemented:
    # path('spr/master/', SprMasterListView.as_view(), name='spr_master_list'),
    # path('spr/master/add/', SprMasterCreateView.as_view(), name='spr_master_add'),
    # path('spr/master/edit/<int:pk>/', SprMasterUpdateView.as_view(), name='spr_master_edit'),
    # path('spr/master/delete/<int:pk>/', SprMasterDeleteView.as_view(), name='spr_master_delete'),

    # Assuming these exist for redirection
    path('spr/dashboard/', TemplateView.as_view(template_name='material_management/spr_dashboard.html'), name='spr_dashboard'),
    path('spr/print-list/', TemplateView.as_view(template_name='material_management/spr_print_list.html'), name='spr_print_list'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests cover the model's data processing logic and the view's ability to render the report correctly.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import datetime
from .models import SprMaster, SprDetail, Employee, Item, Supplier, AccountHead, BusinessGroup, Unit, Company
# Ensure mock_fun is imported or recreated for tests if not directly used by models
from .models import mock_fun 

class SprReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal related data required for the report logic
        cls.company = Company.objects.create(CompId=1, CompName="TestCo", CompAddress="123 Test St")
        cls.checked_by_emp = Employee.objects.create(EmpId=1, Title="Mr", EmployeeName="Checker")
        cls.approved_by_emp = Employee.objects.create(EmpId=2, Title="Ms", EmployeeName="Approver")
        cls.authorized_by_emp = Employee.objects.create(EmpId=3, Title="Dr", EmployeeName="Authorizer")
        cls.intender_emp = Employee.objects.create(EmpId=4, Title="Mx", EmployeeName="Intender")
        cls.unit = Unit.objects.create(Id=1, Symbol="Kg")
        cls.item = Item.objects.create(Id=101, ItemCode="ITM-001", ManfDesc="Test Product A", UOMBasic=cls.unit)
        cls.supplier = Supplier.objects.create(SupplierId=501, SupplierName="Test Supplier Inc.")
        cls.account_head = AccountHead.objects.create(Id=601, Symbol="RAW_MAT")
        cls.business_group = BusinessGroup.objects.create(Id=701, Symbol="Production")

        # Create SPR Master instance
        cls.spr_master = SprMaster.objects.create(
            Id=1,
            SPRNo="SPR/2023/001",
            SysDate=datetime(2023, 1, 10, 10, 0, 0),
            CheckedBy=cls.checked_by_emp.EmpId,
            ApprovedBy=cls.approved_by_emp.EmpId,
            AuthorizedBy=cls.authorized_by_emp.EmpId,
            CheckedDate=datetime(2023, 1, 11, 9, 0, 0),
            ApproveDate=datetime(2023, 1, 12, 11, 0, 0),
            AuthorizeDate=datetime(2023, 1, 13, 14, 0, 0),
            CompId=cls.company.CompId,
            FinYearId=2023,
            SessionId=cls.intender_emp.EmpId
        )

        # Create SPR Detail instance
        cls.spr_detail = SprDetail.objects.create(
            MId=cls.spr_master.Id,
            ItemId=cls.item.Id,
            SupplierId=cls.supplier.SupplierId,
            DeptId=cls.business_group.Id,
            Qty=10.555,
            Rate=100.25,
            Discount=5.00,
            WONo="WO-XYZ",
            DelDate=datetime(2023, 2, 1, 0, 0, 0),
            AHId=cls.account_head.Id,
            Remarks="Urgent delivery required."
        )

        # Create another detail with DeptId=0 for WONo logic
        cls.spr_detail_wo = SprDetail.objects.create(
            MId=cls.spr_master.Id,
            ItemId=102, # Assume another item exists
            SupplierId=cls.supplier.SupplierId,
            DeptId=0, # DeptId 0 to trigger WONo logic
            Qty=5.000,
            Rate=200.00,
            Discount=0.00,
            WONo="WO-ABC",
            DelDate=datetime(2023, 2, 5, 0, 0, 0),
            AHId=cls.account_head.Id,
            Remarks="Standard order."
        )
        
        # Manually link the foreign keys because managed=False doesn't automatically.
        # In a real scenario, these would be managed by the database itself.
        # Here we mock for testing the model's access.
        cls.spr_master.checked_by_employee = cls.checked_by_emp
        cls.spr_master.approved_by_employee = cls.approved_by_emp
        cls.spr_master.authorized_by_employee = cls.authorized_by_emp
        cls.spr_master.intender_employee = cls.intender_emp
        cls.spr_master.company = cls.company

        cls.spr_detail.spr_master = cls.spr_master
        cls.spr_detail.item = cls.item
        cls.spr_detail.item.uom_basic = cls.unit
        cls.spr_detail.supplier = cls.supplier
        cls.spr_detail.account_head = cls.account_head
        cls.spr_detail.department = cls.business_group

        cls.spr_detail_wo.spr_master = cls.spr_master
        cls.spr_detail_wo.item = Item.objects.create(Id=102, ItemCode="ITM-002", ManfDesc="Test Product B", UOMBasic=cls.unit) # New item
        cls.spr_detail_wo.item.uom_basic = cls.unit
        cls.spr_detail_wo.supplier = cls.supplier
        cls.spr_detail_wo.account_head = cls.account_head
        cls.spr_detail_wo.department = None # DeptId 0, so no department object

    @patch('material_management.models.mock_fun') # Patch the mock_fun within the models module
    def test_get_spr_report_data(self, mock_fun_instance):
        # Configure mock_fun to return specific values for predictable tests
        mock_fun_instance.FromDate.side_effect = lambda x: datetime.strptime(x.split('.')[0], "%Y-%m-%d %H:%M:%S").strftime("%d %b %Y") if x else ""
        mock_fun_instance.FromDateDMY.side_effect = lambda x: datetime.strptime(x.split('.')[0], "%Y-%m-%d %H:%M:%S").strftime("%d-%m-%Y") if x else ""
        mock_fun_instance.GetItemCode_PartNo.side_effect = lambda cid, iid: f"ITEM-{iid:04d}-{cid:02d}"
        mock_fun_instance.getCompany.return_value = "Fictional Company Inc. (ID:1)"
        mock_fun_instance.CompAdd.return_value = "123 Main St, Anytown, State 1, Zip 12345"

        report_data = SprMaster.objects.get_spr_report_data(
            spr_no=self.spr_master.SPRNo,
            master_id=self.spr_master.Id,
            company_id=self.company.CompId,
            fin_year_id=self.spr_master.FinYearId
        )

        self.assertIsNotNone(report_data['master'])
        self.assertEqual(report_data['master']['SPRNo'], self.spr_master.SPRNo)
        self.assertEqual(report_data['master']['CheckedBy'], "Mr. Checker")
        self.assertEqual(report_data['master']['RegDate'], "10 Jan 2023")
        self.assertEqual(report_data['master']['Company'], "Fictional Company Inc. (ID:1)")

        self.assertEqual(len(report_data['details']), 2) # Both spr_detail and spr_detail_wo

        # Test first detail item (with Business Group)
        detail1 = report_data['details'][0]
        self.assertEqual(detail1['ItemCode'], f"ITEM-{self.item.Id:04d}-{self.company.CompId:02d}")
        self.assertEqual(detail1['PurchDesc'], "Test Product A")
        self.assertEqual(detail1['UomPurch'], "Kg")
        self.assertEqual(detail1['DelDate'], "01-02-2023")
        self.assertEqual(detail1['SPRQTY'], "10.555")
        self.assertEqual(detail1['Rate'], "100.25")
        self.assertEqual(detail1['Discount'], "5.00")
        self.assertEqual(detail1['WONo_Or_BG'], "BG Group - Production")
        self.assertEqual(detail1['SupplierName'], f"{self.supplier.SupplierName} [ {self.supplier.SupplierId} ]")
        self.assertEqual(detail1['AcHead'], "RAW_MAT")
        self.assertEqual(detail1['Remarks'], "Urgent delivery required.")

        # Test second detail item (with WONo)
        detail2 = report_data['details'][1]
        self.assertEqual(detail2['ItemCode'], f"ITEM-{self.spr_detail_wo.item.Id:04d}-{self.company.CompId:02d}")
        self.assertEqual(detail2['WONo_Or_BG'], "WONo - WO-ABC")
        self.assertEqual(detail2['SPRQTY'], "5.000")


class SprReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal related data required for the report logic
        cls.company = Company.objects.create(CompId=1, CompName="TestCo", CompAddress="123 Test St")
        cls.checked_by_emp = Employee.objects.create(EmpId=1, Title="Mr", EmployeeName="Checker")
        cls.approved_by_emp = Employee.objects.create(EmpId=2, Title="Ms", EmployeeName="Approver")
        cls.authorized_by_emp = Employee.objects.create(EmpId=3, Title="Dr", EmployeeName="Authorizer")
        cls.intender_emp = Employee.objects.create(EmpId=4, Title="Mx", EmployeeName="Intender")
        cls.unit = Unit.objects.create(Id=1, Symbol="Kg")
        cls.item = Item.objects.create(Id=101, ItemCode="ITM-001", ManfDesc="Test Product A", UOMBasic=cls.unit)
        cls.supplier = Supplier.objects.create(SupplierId=501, SupplierName="Test Supplier Inc.")
        cls.account_head = AccountHead.objects.create(Id=601, Symbol="RAW_MAT")
        cls.business_group = BusinessGroup.objects.create(Id=701, Symbol="Production")

        # Create SPR Master instance
        cls.spr_master = SprMaster.objects.create(
            Id=1,
            SPRNo="SPR/2023/001",
            SysDate=datetime(2023, 1, 10, 10, 0, 0),
            CheckedBy=cls.checked_by_emp.EmpId,
            ApprovedBy=cls.approved_by_emp.EmpId,
            AuthorizedBy=cls.authorized_by_emp.EmpId,
            CheckedDate=datetime(2023, 1, 11, 9, 0, 0),
            ApproveDate=datetime(2023, 1, 12, 11, 0, 0),
            AuthorizeDate=datetime(2023, 1, 13, 14, 0, 0),
            CompId=cls.company.CompId,
            FinYearId=2023,
            SessionId=cls.intender_emp.EmpId
        )

        # Create SPR Detail instance
        cls.spr_detail = SprDetail.objects.create(
            MId=cls.spr_master.Id,
            ItemId=cls.item.Id,
            SupplierId=cls.supplier.SupplierId,
            DeptId=cls.business_group.Id,
            Qty=10.555,
            Rate=100.25,
            Discount=5.00,
            WONo="WO-XYZ",
            DelDate=datetime(2023, 2, 1, 0, 0, 0),
            AHId=cls.account_head.Id,
            Remarks="Urgent delivery required."
        )

        # Manually link the foreign keys for ORM access within tests
        cls.spr_master.checked_by_employee = cls.checked_by_emp
        cls.spr_master.approved_by_employee = cls.approved_by_emp
        cls.spr_master.authorized_by_employee = cls.authorized_by_emp
        cls.spr_master.intender_employee = cls.intender_emp
        cls.spr_master.company = cls.company
        cls.spr_master.save() # Save the changes to the instance

        cls.spr_detail.spr_master = cls.spr_master
        cls.spr_detail.item = cls.item
        cls.spr_detail.item.uom_basic = cls.unit # Link UOMBasic on item
        cls.spr_detail.supplier = cls.supplier
        cls.spr_detail.account_head = cls.account_head
        cls.spr_detail.department = cls.business_group
        cls.spr_detail.save()

    def setUp(self):
        self.client = Client()
        # Set session variables required by the view
        session = self.client.session
        session['compid'] = self.company.CompId
        session['finyear'] = self.spr_master.FinYearId
        session.save()

    @patch('material_management.models.mock_fun')
    def test_spr_report_view_success(self, mock_fun_instance):
        # Configure mock_fun to return specific values for predictable tests
        mock_fun_instance.FromDate.side_effect = lambda x: datetime.strptime(x.split('.')[0], "%Y-%m-%d %H:%M:%S").strftime("%d %b %Y") if x else ""
        mock_fun_instance.FromDateDMY.side_effect = lambda x: datetime.strptime(x.split('.')[0], "%Y-%m-%d %H:%M:%S").strftime("%d-%m-%Y") if x else ""
        mock_fun_instance.GetItemCode_PartNo.side_effect = lambda cid, iid: f"ITEM-{iid:04d}-{cid:02d}"
        mock_fun_instance.getCompany.return_value = "Fictional Company Inc. (ID:1)"
        mock_fun_instance.CompAdd.return_value = "123 Main St, Anytown, State 1, Zip 12345"

        url = reverse('spr_report_detail') + f"?SPRNo={self.spr_master.SPRNo}&Id={self.spr_master.Id}&Key=testkey&f=1"
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_report/detail.html')
        self.assertIn('report_data', response.context)
        self.assertIsNotNone(response.context['report_data']['master'])
        self.assertEqual(response.context['report_data']['master']['SPRNo'], self.spr_master.SPRNo)
        self.assertGreater(len(response.context['report_data']['details']), 0)
        self.assertContains(response, self.spr_master.SPRNo)
        self.assertContains(response, self.item.ManfDesc)
        self.assertContains(response, self.supplier.SupplierName)
        self.assertContains(response, "Loading SPR details...") # HTMX placeholder

    def test_spr_report_view_missing_params(self):
        url = reverse('spr_report_detail') + "?SPRNo=MISSING" # Missing Id
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Missing required parameters to generate SPR report.")
        self.assertContains(response, "No SPR data found or insufficient parameters provided.")

    def test_spr_report_details_table_view(self):
        # This tests the HTMX partial view
        url = reverse('spr_report_details_table') + f"?SPRNo={self.spr_master.SPRNo}&Id={self.spr_master.Id}&Key=testkey"
        response = self.client.get(url, HTTP_HX_REQUEST='true') # Simulate HTMX request
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_report/_spr_details_table.html')
        self.assertIn('spr_details', response.context)
        self.assertGreater(len(response.context['spr_details']), 0)
        self.assertContains(response, "Item Code") # Check for table header
        self.assertContains(response, self.item.ManfDesc) # Check for item description

    def test_cancel_spr_report_f1(self):
        response = self.client.get(reverse('cancel_spr_report') + "?f=1")
        self.assertRedirects(response, reverse('spr_dashboard'))

    def test_cancel_spr_report_f2(self):
        response = self.client.get(reverse('cancel_spr_report') + "?f=2")
        self.assertRedirects(response, reverse('spr_print_list'))

    def test_cancel_spr_report_default(self):
        response = self.client.get(reverse('cancel_spr_report')) # No 'f' param
        self.assertRedirects(response, reverse('home')) # Assumes a 'home' URL

```

### Step 5: HTMX and Alpine.js Integration

The plan integrates HTMX and Alpine.js as follows:

*   **HTMX for DataTables Loading:**
    *   The `sprDetailsTable-container` `div` in `detail.html` uses `hx-get` to fetch the `_spr_details_table.html` partial. This loads the data table content dynamically after the main page loads, improving perceived performance.
    *   `hx-trigger="load"` ensures the content is fetched automatically on page load.
*   **Alpine.js for UI State Management:**
    *   Alpine.js is included in `base.html` (implicitly, as per instructions). While not strictly critical for this *specific* static report, it's available for any future client-side interactivity, like dynamic filtering or sorting toggles if DataTables' built-in features were insufficient or if custom UI elements were added.
*   **DataTables for List Views:**
    *   The `_spr_details_table.html` partial contains a standard `<table>` element.
    *   A JavaScript block within this partial initializes DataTables on `$('#sprDetailsTable')` after the HTML is inserted into the DOM by HTMX. This ensures proper client-side searching, sorting, and pagination for the line items.
*   **No Full Page Reloads:**
    *   Fetching the report details table via HTMX ensures that only the relevant portion of the page is updated, providing a smoother user experience without a full page reload.
    *   The "Cancel" button triggers a standard Django redirect, as its purpose is to navigate away from the report page, not to update part of it.
*   **DRY Templates:**
    *   The use of `_spr_details_table.html` as a partial is a key DRY principle.
    *   All templates extend `core/base.html` for consistent layout and CDN/script includes.

### Final Notes

*   This modernization plan effectively transforms a legacy Crystal Report viewer into a modern, interactive Django web page.
*   The complex data aggregation logic from the C# code-behind is encapsulated within the `SprMaster` model, adhering to the "fat model, thin view" principle.
*   The use of `managed = False` ensures that Django works with the existing database schema without attempting migrations on these tables.
*   The UI leverages modern frontend techniques (HTMX, Alpine.js, DataTables) for a responsive and dynamic user experience without heavy JavaScript frameworks.
*   Comprehensive tests ensure the reliability and correctness of both the data processing logic and the view rendering.
*   Placeholders for `spr_dashboard` and `spr_print_list` URLs are included, assuming these would be migrated as other parts of the system.
*   The `mock_fun` is crucial for testing the fat model's behavior accurately in a controlled environment without needing a full database setup during unit tests. In a live environment, the actual ORM queries would replace the need for such mocks.