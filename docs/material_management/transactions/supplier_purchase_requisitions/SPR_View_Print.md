## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code performs a complex SQL query joining several tables to generate the report data. The primary entities are `tblMM_SPR_Master` and `tblMM_SPR_Details`. Many other tables serve as lookup or related entities.

**Identified Tables and Key Columns:**

*   **`tblMM_SPR_Master` (SPR Master):**
    *   `Id` (Primary Key, inferred from `MId` parameter)
    *   `SPRNo` (String, used for filtering)
    *   `SysDate` (DateTime, 'Registration Date')
    *   `CheckedBy` (Int, Employee ID)
    *   `ApprovedBy` (Int, Employee ID)
    *   `AuthorizedBy` (Int, Employee ID)
    *   `CheckedDate` (DateTime)
    *   `ApproveDate` (DateTime)
    *   `AuthorizeDate` (DateTime)
    *   `CompId` (Int, Company ID, inferred from `Session["compid"]`)
    *   `SessionId` (Int, Employee ID, 'Intender')

*   **`tblMM_SPR_Details` (SPR Details):**
    *   `MId` (Foreign Key to `tblMM_SPR_Master.Id`)
    *   `SPRNo` (String, also used for filtering)
    *   `Discount` (Double)
    *   `ItemId` (Int, Foreign Key to `tblDG_Item_Master.Id`)
    *   `ManfDesc` (from Item Master, but referenced in details)
    *   `DelDate` (DateTime)
    *   `Qty` (Double)
    *   `Rate` (Double)
    *   `SupplierId` (Int, Foreign Key to `tblMM_Supplier_master.SupplierId`)
    *   `WONo` (String)
    *   `AHId` (Int, Foreign Key to `AccHead.Id`)
    *   `Remarks` (String)
    *   `DeptId` (Int, Foreign Key to `BusinessGroup.Id`)

*   **`tblDG_Item_Master` (Item Master):**
    *   `Id` (Primary Key)
    *   `ItemCode` (String)
    *   `ManfDesc` (String, Manufacturer Description / Purchase Description)
    *   `UOMBasic` (Int, Foreign Key to `Unit_Master.Id`)

*   **`Unit_Master` (Unit Master):**
    *   `Id` (Primary Key)
    *   `Symbol` (String, UOM Symbol)

*   **`tblMM_Supplier_master` (Supplier Master):**
    *   `SupplierId` (Primary Key)
    *   `SupplierName` (String)

*   **`AccHead` (Account Head):**
    *   `Id` (Primary Key)
    *   `Symbol` (String, Account Head Symbol)

*   **`tblHR_OfficeStaff` (Office Staff):**
    *   `EmpId` (Primary Key)
    *   `Title` (String)
    *   `EmployeeName` (String)

*   **`BusinessGroup` (Business Group):**
    *   `Id` (Primary Key)
    *   `Symbol` (String, Department Symbol/Name)

*   **`tblCompany_master` (Company Master):**
    *   `CompId` (Primary Key)
    *   (Details inferred from `fun.getCompany` and `fun.CompAdd` which are not directly in the query, but used for report parameters)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The ASP.NET page `SPR_View_Print.aspx` is exclusively designed for *viewing* and preparing data for a Crystal Report print.
*   **Read:** The core functionality is to retrieve and process a detailed dataset for a specific SPR (based on `SPRNo` and `MId`).
*   **No Create, Update, Delete:** There are no functionalities to add, modify, or delete SPR records or details on this page.
*   **Navigation:** A "Cancel" button redirects the user back to a `parentpage`, indicating a navigation/exit function.
*   **Data Transformation:** A significant part of the C# code involves fetching raw data, then transforming it into a specific `DataTable` structure (e.g., combining `Title` and `EmployeeName`, formatting dates, conditional logic for `WONo` vs `Dept`).

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Analysis:**
*   **`CR:CrystalReportViewer`**: This is the main component, responsible for rendering the Crystal Report within the web page. In Django, this will be replaced by a standard HTML table, styled with Tailwind CSS and enhanced with DataTables for interactivity.
*   **`asp:Button ID="Cancel"`**: A standard button for navigation. In Django, this will be a simple `<button>` or `<a>` tag that performs a redirect.
*   **Styling**: The ASP.NET page uses inline styles, `CssClass`, and a background image. In Django, this will be handled by Tailwind CSS classes and standard image paths.

### Step 4: Generate Django Code

Given the nature of the ASP.NET page (a report view), the Django modernization will focus on creating models to map the data, a single view to fetch and present the complex report data, and templates for display. We will use `material_management` as the Django app name.

#### 4.1 Models

Task: Create Django models based on the identified database schema. We will create simplified models for each table involved, focusing on the fields used in the report query. The complex data retrieval and transformation logic will be implemented as a method on `SPRMaster`'s manager.

**File: `material_management/models.py`**

```python
from django.db import models
from django.db.models import F, Value, CharField, Case, When
from django.db.models.functions import Concat, Cast
from django.utils import timezone
from datetime import datetime

class CompanyMaster(models.Model):
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True) # Assuming 'Address' field exists

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name or 'N/A'

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId exists

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name or ''}".strip()

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or 'N/A'

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or 'N/A'

class SupplierMaster(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or 'N/A'

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol or 'N/A'

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or 'N/A'

class SPRMasterManager(models.Manager):
    def get_spr_report_data(self, spr_no, master_id, company_id, financial_year_id):
        # This method replicates the complex SQL query and data processing from the ASP.NET code-behind.
        # It aggregates data from SPRMaster and its related SPRDetails, joining all necessary lookup tables.
        # This is the "fat model" approach, keeping complex data logic out of views.

        # Fetch SPR Master data
        spr_master_qs = self.filter(
            spr_no=spr_no,
            id=master_id,
            compid=company_id
        ).select_related(
            'intender', # tblHR_OfficeStaff
        ).annotate(
            reg_date_str=Cast(F('sys_date'), CharField()), # For raw date string if needed
            intender_name=Concat(F('intender__title'), Value('. '), F('intender__employee_name'), output_field=CharField()),
            company_name=F('comp__company_name'),
            company_address=F('comp__address'),
            # For checked_by, approved_by, authorized_by, we need separate lookups since they are not direct FKs to the same field
            # This is more complex in a single ORM query, better handled by additional specific lookups or a helper function.
            # We will fetch them separately in the view for clarity, similar to how C# did additional lookups.
        ).first()

        if not spr_master_qs:
            return None, [] # Master not found, return empty data

        # Fetch SPR Details data
        spr_details_qs = SPRDetail.objects.filter(
            spr_no=spr_no,
            master_id=master_id
        ).select_related(
            'item', 'item__uom_basic', 'supplier', 'account_head', 'department' # Note: 'department' is BusinessGroup
        ).annotate(
            item_code=F('item__item_code'),
            manf_desc=F('item__manf_desc'),
            uom_symbol=F('item__uom_basic__symbol'),
            del_date_str=Cast(F('del_date'), CharField()), # For raw date string if needed
            supplier_name=F('supplier__supplier_name'),
            account_head_symbol=F('account_head__symbol'),
            department_symbol=F('department__symbol'),
            # Replicate WONo vs Dept logic
            won_or_dept=Case(
                When(wo_no__isnull=False, then=Concat(Value('WONo - '), F('wo_no'))),
                default=Concat(Value('BG Group - '), F('department__symbol')),
                output_field=CharField()
            )
        ).values(
            'discount', 'item_code', 'manf_desc', 'uom_symbol', 'del_date_str',
            'qty', 'rate', 'won_or_dept', 'supplier_name', 'department_symbol',
            'account_head_symbol', 'remarks', 'won_no' # Keep original won_no for potential further logic
        )
        # Convert values query to a list of dicts for easier processing if needed later
        spr_details_data = list(spr_details_qs)

        # Handle specific employee lookups for CheckedBy, ApprovedBy, AuthorizedBy as C# did
        checked_by_name = OfficeStaff.objects.filter(
            emp_id=spr_master_qs.checked_by, compid=company_id
        ).annotate(
            full_name=Concat(F('title'), Value('. '), F('employee_name'))
        ).values_list('full_name', flat=True).first() if spr_master_qs.checked_by else ' '

        approved_by_name = OfficeStaff.objects.filter(
            emp_id=spr_master_qs.approved_by, compid=company_id
        ).annotate(
            full_name=Concat(F('title'), Value('. '), F('employee_name'))
        ).values_list('full_name', flat=True).first() if spr_master_qs.approved_by else ' '

        authorized_by_name = OfficeStaff.objects.filter(
            emp_id=spr_master_qs.authorized_by, compid=company_id
        ).annotate(
            full_name=Concat(F('title'), Value('. '), F('employee_name'))
        ).values_list('full_name', flat=True).first() if spr_master_qs.authorized_by else ' '

        # Format dates as per C# fun.FromDate/FromDateDMY
        def format_date_dmy(date_str):
            try:
                if date_str:
                    return datetime.strptime(date_str.split(' ')[0], '%Y-%m-%d').strftime('%d/%m/%Y')
            except (ValueError, TypeError):
                pass
            return ''

        def format_date_default(date_str):
            try:
                if date_str:
                    return datetime.strptime(date_str.split(' ')[0], '%Y-%m-%d').strftime('%d-%b-%Y')
            except (ValueError, TypeError):
                pass
            return ''

        formatted_master_data = {
            'spr_no': spr_master_qs.spr_no,
            'reg_date': format_date_default(spr_master_qs.sys_date.strftime('%Y-%m-%d %H:%M:%S') if spr_master_qs.sys_date else ''),
            'checked_by': checked_by_name,
            'approved_by': approved_by_name,
            'authorized_by': authorized_by_name,
            'checked_date': format_date_default(spr_master_qs.checked_date.strftime('%Y-%m-%d %H:%M:%S') if spr_master_qs.checked_date else ''),
            'approve_date': format_date_default(spr_master_qs.approve_date.strftime('%Y-%m-%d %H:%M:%S') if spr_master_qs.approve_date else ''),
            'authorize_date': format_date_default(spr_master_qs.authorize_date.strftime('%Y-%m-%d %H:%M:%S') if spr_master_qs.authorize_date else ''),
            'company_name': spr_master_qs.company_name,
            'company_address': spr_master_qs.company_address,
            'intender': spr_master_qs.intender_name,
        }

        # Format specific fields for details
        for detail in spr_details_data:
            detail['del_date'] = format_date_dmy(detail['del_date_str'])
            detail['qty'] = f"{detail['qty']:.3f}" if detail['qty'] is not None else ''
            detail['rate'] = f"{detail['rate']:.2f}" if detail['rate'] is not None else ''
            detail['discount'] = f"{detail['discount']:.2f}" if detail['discount'] is not None else ''


        return formatted_master_data, spr_details_data


class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=255)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    checked_by = models.IntegerField(db_column='CheckedBy', blank=True, null=True)
    approved_by = models.IntegerField(db_column='ApprovedBy', blank=True, null=True)
    authorized_by = models.IntegerField(db_column='AuthorizedBy', blank=True, null=True)
    checked_date = models.DateTimeField(db_column='CheckedDate', blank=True, null=True)
    approve_date = models.DateTimeField(db_column='ApproveDate', blank=True, null=True)
    authorize_date = models.DateTimeField(db_column='AuthorizeDate', blank=True, null=True)
    comp = models.ForeignKey(CompanyMaster, models.DO_NOTHING, db_column='CompId', blank=True, null=True) # Renamed to comp to avoid conflict
    intender = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='SessionId', blank=True, null=True)

    objects = SPRMasterManager()

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no or 'N/A'

class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an Id for details
    master = models.ForeignKey(SPRMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=255) # Redundant but kept for db_table mapping
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3, blank=True, null=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    del_date = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=3, blank=True, null=True)
    supplier = models.ForeignKey(SupplierMaster, models.DO_NOTHING, db_column='SupplierId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)
    department = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"Detail for SPR {self.spr_no} - Item {self.item.item_code if self.item else 'N/A'}"

```

#### 4.2 Forms

Task: Define a Django form for user input.

**Analysis:**
This page is for *viewing* a report, not for data input (Create/Update). Therefore, a Django `ModelForm` is not required for this specific conversion. If there were filter inputs, a simple `Form` could be created, but the original ASP.NET uses query strings.

*(No forms.py will be generated for this specific task)*

#### 4.3 Views

Task: Implement the report view. This will be a `TemplateView` that fetches all necessary data via the "fat model" manager method.

**File: `material_management/views.py`**

```python
from django.views.generic import TemplateView
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.http import HttpResponse, Http404
from django.template.loader import render_to_string
from django.contrib import messages
from .models import SPRMaster, CompanyMaster # Import CompanyMaster for independent lookup if needed

class SPRReportView(TemplateView):
    template_name = 'material_management/spr/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve parameters from query string and session
        spr_no = self.request.GET.get('SPRNo')
        master_id = self.request.GET.get('Id')
        parent_page = self.request.GET.get('parentpage', reverse('home')) # Default redirect
        
        # Assuming compid and finyear are stored in session (as in ASP.NET)
        company_id = self.request.session.get('compid')
        financial_year_id = self.request.session.get('finyear')

        if not all([spr_no, master_id, company_id, financial_year_id]):
            messages.error(self.request, "Missing required parameters for SPR Report.")
            raise Http404("Missing required report parameters.")

        # Use the fat model method to get all report data
        spr_master_data, spr_details_data = SPRMaster.objects.get_spr_report_data(
            spr_no=spr_no,
            master_id=master_id,
            company_id=company_id,
            financial_year_id=financial_year_id # Though not directly used by current get_spr_report_data
        )

        if not spr_master_data:
            messages.warning(self.request, f"No SPR found for number {spr_no} and ID {master_id}.")
            raise Http404("SPR not found.")
            
        context['spr_master'] = spr_master_data
        context['spr_details'] = spr_details_data
        context['parent_page_url'] = parent_page

        return context

# HTMX partial view for the DataTables table
class SPRDetailsTablePartialView(TemplateView):
    template_name = 'material_management/spr/_spr_details_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        spr_no = self.request.GET.get('SPRNo')
        master_id = self.request.GET.get('Id')
        company_id = self.request.session.get('compid')
        financial_year_id = self.request.session.get('finyear') # Not used in model method directly

        if not all([spr_no, master_id, company_id, financial_year_id]):
            # This case should ideally be handled before the HTMX call in the main view
            return context # Or raise Http404 as appropriate

        spr_master_data, spr_details_data = SPRMaster.objects.get_spr_report_data(
            spr_no=spr_no,
            master_id=master_id,
            company_id=company_id,
            financial_year_id=financial_year_id
        )
        context['spr_details'] = spr_details_data
        return context

```

#### 4.4 Templates

Task: Create templates for the report view and its partial DataTables component.

**File: `material_management/templates/material_management/spr/report.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold text-gray-800">SPR - Print (No: {{ spr_master.spr_no }})</h2>
            <a href="{{ parent_page_url }}" 
               class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                Cancel
            </a>
        </div>

        <!-- Master Data Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm mb-6">
            <div>
                <p class="font-semibold text-gray-700">Company:</p>
                <p class="text-gray-600">{{ spr_master.company_name }}</p>
                <p class="text-gray-600">{{ spr_master.company_address }}</p>
            </div>
            <div>
                <p class="font-semibold text-gray-700">SPR No:</p>
                <p class="text-gray-600">{{ spr_master.spr_no }}</p>
            </div>
            <div>
                <p class="font-semibold text-gray-700">Registration Date:</p>
                <p class="text-gray-600">{{ spr_master.reg_date }}</p>
            </div>
            <div>
                <p class="font-semibold text-gray-700">Intender:</p>
                <p class="text-gray-600">{{ spr_master.intender }}</p>
            </div>
            <!-- Approval/Authorization details -->
            <div>
                <p class="font-semibold text-gray-700">Checked By:</p>
                <p class="text-gray-600">{{ spr_master.checked_by }}</p>
                <p class="text-gray-600 text-xs">{{ spr_master.checked_date }}</p>
            </div>
            <div>
                <p class="font-semibold text-gray-700">Approved By:</p>
                <p class="text-gray-600">{{ spr_master.approved_by }}</p>
                <p class="text-gray-600 text-xs">{{ spr_master.approve_date }}</p>
            </div>
            <div>
                <p class="font-semibold text-gray-700">Authorized By:</p>
                <p class="text-gray-600">{{ spr_master.authorized_by }}</p>
                <p class="text-gray-600 text-xs">{{ spr_master.authorize_date }}</p>
            </div>
        </div>

        <h3 class="text-xl font-bold text-gray-800 mb-4">SPR Details</h3>
        <div id="sprDetailsTableContainer"
             hx-get="{% url 'spr_details_table_partial' %}?SPRNo={{ spr_master.spr_no }}&Id={{ spr_master.id }}"
             hx-trigger="load"
             hx-swap="innerHTML">
            <!-- Loading indicator for HTMX content -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading SPR details...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add DataTables CDN links in base.html as per DRY principle -->
<!-- Alpine.js is typically loaded in base.html as well -->
<script>
    // Alpine.js component if needed for local UI state
    document.addEventListener('alpine:init', () => {
        Alpine.data('sprReport', () => ({
            // Example:
            // showModal: false,
            // toggleModal() {
            //     this.showModal = !this.showModal;
            // }
        }));
    });

    // DataTables initialization (will be inside the HTMX loaded partial)
    // No direct DataTable init here as it's for partial.
</script>
{% endblock %}
```

**File: `material_management/templates/material_management/spr/_spr_details_table.html`**

```html
<div class="overflow-x-auto rounded-lg shadow-md">
    <table id="sprDetailsTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No / BG Group</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Head</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for detail in spr_details %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ detail.item_code }}</td>
                <td class="py-3 px-6 text-sm text-gray-700">{{ detail.manf_desc }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ detail.uom_symbol }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ detail.del_date }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ detail.qty }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ detail.rate }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ detail.won_or_dept }}</td>
                <td class="py-3 px-6 text-sm text-gray-700">{{ detail.supplier_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ detail.department_symbol }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ detail.account_head_symbol }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ detail.discount }}</td>
                <td class="py-3 px-6 text-sm text-gray-700">{{ detail.remarks }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="13" class="py-8 text-center text-gray-500">No SPR details found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization must happen after the table is loaded into the DOM
$(document).ready(function() {
    $('#sprDetailsTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "paging": true,
        "ordering": true,
        "info": true,
        "searching": true,
        "responsive": true // Make DataTables responsive
    });
});
</script>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

**File: `material_management/urls.py`**

```python
from django.urls import path
from .views import SPRReportView, SPRDetailsTablePartialView

urlpatterns = [
    path('spr-report/', SPRReportView.as_view(), name='spr_report'),
    path('spr-report-details-table/', SPRDetailsTablePartialView.as_view(), name='spr_details_table_partial'),
]

```
*(Note: You would also include these URLs in your project's main `urls.py`.)*

#### 4.6 Tests

Task: Write tests for the models and views.

**File: `material_management/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    SPRMaster, SPRDetail, CompanyMaster, OfficeStaff, UnitMaster,
    ItemMaster, SupplierMaster, AccountHead, BusinessGroup
)

class SPRModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related lookup tables first
        cls.company = CompanyMaster.objects.create(compid=1, company_name="Test Company", address="123 Test St")
        cls.intender_staff = OfficeStaff.objects.create(emp_id=101, title="Mr", employee_name="John Doe", compid=1)
        cls.checked_by_staff = OfficeStaff.objects.create(emp_id=102, title="Ms", employee_name="Jane Smith", compid=1)
        cls.approved_by_staff = OfficeStaff.objects.create(emp_id=103, title="Dr", employee_name="Robert Brown", compid=1)
        cls.authorized_by_staff = OfficeStaff.objects.create(emp_id=104, title="Mx", employee_name="Alex Green", compid=1)
        cls.unit = UnitMaster.objects.create(id=1, symbol="KG")
        cls.item = ItemMaster.objects.create(id=1, item_code="ITEM001", manf_desc="Test Item Desc", uom_basic=cls.unit)
        cls.supplier = SupplierMaster.objects.create(supplier_id=1, supplier_name="Test Supplier")
        cls.acc_head = AccountHead.objects.create(id=1, symbol="RAW_MAT")
        cls.business_group = BusinessGroup.objects.create(id=1, symbol="PROD_DEPT")

        # Create SPRMaster and SPRDetail instances
        cls.spr_master = SPRMaster.objects.create(
            id=1,
            spr_no="SPR001",
            sys_date=timezone.now() - timedelta(days=5),
            checked_by=cls.checked_by_staff.emp_id,
            approved_by=cls.approved_by_staff.emp_id,
            authorized_by=cls.authorized_by_staff.emp_id,
            checked_date=timezone.now() - timedelta(days=3),
            approve_date=timezone.now() - timedelta(days=2),
            authorize_date=timezone.now() - timedelta(days=1),
            comp=cls.company,
            intender=cls.intender_staff,
        )
        cls.spr_detail_1 = SPRDetail.objects.create(
            id=1,
            master=cls.spr_master,
            spr_no="SPR001",
            discount=5.0,
            item=cls.item,
            del_date=timezone.now() + timedelta(days=10),
            qty=10.0,
            rate=100.0,
            supplier=cls.supplier,
            wo_no="WO123",
            account_head=cls.acc_head,
            remarks="Detail 1 remarks",
            department=cls.business_group,
        )
        cls.spr_detail_2 = SPRDetail.objects.create(
            id=2,
            master=cls.spr_master,
            spr_no="SPR001",
            discount=2.0,
            item=ItemMaster.objects.create(id=2, item_code="ITEM002", manf_desc="Another Test Item", uom_basic=cls.unit),
            del_date=timezone.now() + timedelta(days=12),
            qty=5.0,
            rate=200.0,
            supplier=cls.supplier,
            wo_no=None, # Test case for null WO
            account_head=cls.acc_head,
            remarks="Detail 2 remarks",
            department=cls.business_group,
        )

    def test_spr_master_creation(self):
        self.assertEqual(self.spr_master.spr_no, "SPR001")
        self.assertEqual(self.spr_master.comp.company_name, "Test Company")
        self.assertEqual(self.spr_master.intender.employee_name, "John Doe")

    def test_spr_detail_creation(self):
        self.assertEqual(self.spr_detail_1.item.item_code, "ITEM001")
        self.assertEqual(self.spr_detail_1.supplier.supplier_name, "Test Supplier")
        self.assertEqual(self.spr_detail_2.wo_no, None) # Check None WO

    def test_get_spr_report_data_fat_model(self):
        # Mock session attributes for the manager method
        self.client.session['compid'] = self.company.compid
        self.client.session['finyear'] = 2023 # Dummy finyear

        spr_master_data, spr_details_data = SPRMaster.objects.get_spr_report_data(
            spr_no="SPR001",
            master_id=self.spr_master.id,
            company_id=self.company.compid,
            financial_year_id=2023 # Dummy
        )

        self.assertIsNotNone(spr_master_data)
        self.assertEqual(spr_master_data['spr_no'], "SPR001")
        self.assertEqual(spr_master_data['company_name'], "Test Company")
        self.assertEqual(spr_master_data['intender'], "Mr. John Doe")
        self.assertEqual(spr_master_data['checked_by'], "Ms. Jane Smith")
        self.assertEqual(spr_master_data['approved_by'], "Dr. Robert Brown")
        self.assertEqual(spr_master_data['authorized_by'], "Mx. Alex Green")

        self.assertEqual(len(spr_details_data), 2)
        
        detail_1 = next(d for d in spr_details_data if d['item_code'] == 'ITEM001')
        self.assertEqual(detail_1['qty'], "10.000") # Formatted string
        self.assertEqual(detail_1['rate'], "100.00") # Formatted string
        self.assertEqual(detail_1['won_or_dept'], "WONo - WO123")
        self.assertEqual(detail_1['department_symbol'], "PROD_DEPT")
        self.assertIn(datetime.now().strftime('%d/%m/%Y').split('/')[2], detail_1['del_date']) # Check year part

        detail_2 = next(d for d in spr_details_data if d['item_code'] == 'ITEM002')
        self.assertEqual(detail_2['won_or_dept'], "BG Group - PROD_DEPT")
        
        # Test case where master is not found
        master_data_none, details_data_none = SPRMaster.objects.get_spr_report_data(
            spr_no="NONEXISTENT", master_id=999, company_id=self.company.compid, financial_year_id=2023
        )
        self.assertIsNone(master_data_none)
        self.assertEqual(details_data_none, [])

class SPRViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related lookup tables first
        cls.company = CompanyMaster.objects.create(compid=1, company_name="Test Company", address="123 Test St")
        cls.intender_staff = OfficeStaff.objects.create(emp_id=101, title="Mr", employee_name="John Doe", compid=1)
        cls.checked_by_staff = OfficeStaff.objects.create(emp_id=102, title="Ms", employee_name="Jane Smith", compid=1)
        cls.approved_by_staff = OfficeStaff.objects.create(emp_id=103, title="Dr", employee_name="Robert Brown", compid=1)
        cls.authorized_by_staff = OfficeStaff.objects.create(emp_id=104, title="Mx", employee_name="Alex Green", compid=1)
        cls.unit = UnitMaster.objects.create(id=1, symbol="KG")
        cls.item = ItemMaster.objects.create(id=1, item_code="ITEM001", manf_desc="Test Item Desc", uom_basic=cls.unit)
        cls.supplier = SupplierMaster.objects.create(supplier_id=1, supplier_name="Test Supplier")
        cls.acc_head = AccountHead.objects.create(id=1, symbol="RAW_MAT")
        cls.business_group = BusinessGroup.objects.create(id=1, symbol="PROD_DEPT")

        cls.spr_master = SPRMaster.objects.create(
            id=1,
            spr_no="SPR001",
            sys_date=timezone.now(),
            checked_by=cls.checked_by_staff.emp_id,
            approved_by=cls.approved_by_staff.emp_id,
            authorized_by=cls.authorized_by_staff.emp_id,
            checked_date=timezone.now(),
            approve_date=timezone.now(),
            authorize_date=timezone.now(),
            comp=cls.company,
            intender=cls.intender_staff,
        )
        SPRDetail.objects.create(
            id=1,
            master=cls.spr_master,
            spr_no="SPR001",
            discount=5.0,
            item=cls.item,
            del_date=timezone.now() + timedelta(days=10),
            qty=10.0,
            rate=100.0,
            supplier=cls.supplier,
            wo_no="WO123",
            account_head=cls.acc_head,
            remarks="Detail 1 remarks",
            department=cls.business_group,
        )

    def setUp(self):
        self.client = Client()
        # Set session variables required by the view
        session = self.client.session
        session['compid'] = self.company.compid
        session['finyear'] = 2023 # Dummy
        session.save()

    def test_spr_report_view_success(self):
        response = self.client.get(reverse('spr_report'), {
            'SPRNo': "SPR001",
            'Id': self.spr_master.id,
            'parentpage': '/some/previous/page/'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/report.html')
        self.assertIn('spr_master', response.context)
        self.assertIn('spr_details', response.context)
        self.assertEqual(response.context['spr_master']['spr_no'], "SPR001")
        self.assertEqual(len(response.context['spr_details']), 1)

    def test_spr_report_view_missing_params(self):
        # Missing SPRNo
        with self.assertRaises(Http404):
            self.client.get(reverse('spr_report'), {
                'Id': self.spr_master.id,
                'parentpage': '/some/previous/page/'
            })
        
        # Missing Id
        with self.assertRaises(Http404):
            self.client.get(reverse('spr_report'), {
                'SPRNo': "SPR001",
                'parentpage': '/some/previous/page/'
            })
            
        # Missing session data (compid/finyear)
        session = self.client.session
        del session['compid']
        session.save()
        with self.assertRaises(Http404):
            self.client.get(reverse('spr_report'), {
                'SPRNo': "SPR001",
                'Id': self.spr_master.id,
                'parentpage': '/some/previous/page/'
            })

    def test_spr_report_view_spr_not_found(self):
        with self.assertRaises(Http404):
            self.client.get(reverse('spr_report'), {
                'SPRNo': "NONEXISTENT",
                'Id': 999,
                'parentpage': '/some/previous/page/'
            })
        self.assertIn("No SPR found", self.client.session['_messages'][0].message)


    def test_spr_details_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('spr_details_table_partial'), {
            'SPRNo': "SPR001",
            'Id': self.spr_master.id,
        }, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/_spr_details_table.html')
        self.assertIn('spr_details', response.context)
        self.assertEqual(len(response.context['spr_details']), 1)
        self.assertContains(response, "ITEM001")
        self.assertContains(response, "WO123")

    def test_cancel_button_redirect(self):
        # The cancel button is a simple <a> tag, not a POST request to a view
        # Its behavior is tested by ensuring the URL in context is correct.
        response = self.client.get(reverse('spr_report'), {
            'SPRNo': "SPR001",
            'Id': self.spr_master.id,
            'parentpage': '/custom/previous/path/'
        })
        self.assertEqual(response.context['parent_page_url'], '/custom/previous/path/')
        self.assertContains(response, '<a href="/custom/previous/path/"')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX:**
    *   The `SPRReportView` loads its initial content normally.
    *   The core detail table (`_spr_details_table.html`) is loaded asynchronously using `hx-get` and `hx-trigger="load"` into a container (`#sprDetailsTableContainer`) within `report.html`. This ensures that the DataTables script only runs after its target table is in the DOM.
    *   The `hx-trigger` mechanism is simple here as it's a static report view, but for interactive lists, `HX-Trigger` headers would be used by Create/Update/Delete views to refresh the list, triggering a `hx-get` on the table container.
*   **Alpine.js:**
    *   A minimal Alpine.js component boilerplate is included in `report.html` for future extensibility (e.g., if dynamic filtering or modals were added to the report view). For this particular conversion, its direct utility is limited as the primary interactions are DataTables and HTMX for loading.
*   **DataTables:**
    *   The `_spr_details_table.html` partial template includes the necessary `$(document).ready(function() { $('#sprDetailsTable').DataTable({...}); });` script block. This script will execute each time the partial is loaded via HTMX, correctly initializing DataTables on the dynamically added table.
    *   CDN links for DataTables and jQuery (which DataTables depends on) should be included once in `core/base.html` to follow DRY principles.
*   **No Full Page Reloads:** All dynamic interactions are designed to work via HTMX, replacing only parts of the page. The "Cancel" button, being a simple navigation, performs a standard redirect.

### Final Notes

This modernization plan provides a robust, maintainable, and modern Django solution for the ASP.NET SPR report viewing functionality. It leverages Django's ORM for database interaction, implements complex data transformation logic within the "fat model" for clear separation of concerns, and uses HTMX + DataTables for an interactive, modern user experience without traditional JavaScript frameworks. The comprehensive tests ensure reliability and adherence to code quality standards. Further enhancements could include PDF generation functionality as a separate feature, but the core display logic is now web-native.