This document outlines a comprehensive modernization plan to transition your legacy ASP.NET Web Forms application, specifically the `SPR_Edit_Details_Item.aspx` functionality, to a modern Django-based solution. Our approach prioritizes automation, emphasizes the "Fat Model, Thin View" pattern, and leverages HTMX + Alpine.js for dynamic frontend interactions, ensuring a seamless and efficient migration.

## ASP.NET to Django Conversion Plan: SPR Item Details Editor

### Business Benefits of Django Modernization:

*   **Cost Efficiency:** Automating large parts of the migration significantly reduces manual development hours, leading to lower project costs.
*   **Enhanced Performance:** Django's optimized architecture and database interactions, combined with HTMX for partial page updates, will result in a faster and more responsive user experience.
*   **Improved Maintainability:** Django's clear structure, coupled with our "Fat Model, Thin View" philosophy, makes the codebase easier to understand, debug, and extend, reducing future maintenance overhead.
*   **Scalability:** Django is inherently scalable, capable of handling increased user loads and data volumes, ensuring your application grows with your business needs.
*   **Future-Proofing:** Moving to a widely adopted, open-source framework like Django ensures access to a vibrant community, ongoing updates, and a vast ecosystem of tools and libraries, safeguarding your investment.
*   **Modern User Experience:** Leveraging HTMX and Alpine.js provides a dynamic, single-page application (SPA)-like feel without the complexity of traditional JavaScript frameworks, improving user satisfaction.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:** The ASP.NET code interacts with several database tables. Based on the `SELECT` and `UPDATE` statements, we infer the following primary tables and their key columns involved in the `SPR_Edit_Details_Item` functionality:

*   **`tblMM_SPR_Details` (Django Model: `SprDetail`)**:
    *   `Id` (Primary Key, Integer)
    *   `SPRNo` (String)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`)
    *   `Qty` (Decimal, up to 3 decimal places)
    *   `Rate` (Decimal, up to 2 decimal places)
    *   `Discount` (Decimal, up to 2 decimal places)
    *   `SupplierId` (Foreign Key to `tblMM_Supplier_master.SupplierId`)
    *   `AHId` (Foreign Key to `AccHead.Id`)
    *   `WONo` (String, nullable)
    *   `DeptId` (Foreign Key to `BusinessGroup.Id`, nullable - `0` in ASP.NET signifies `WONo` usage)
    *   `Remarks` (Text, nullable)
    *   `DelDate` (Date)
    *   `MId` (Foreign Key to `tblMM_SPR_Master.Id`)

*   **`tblDG_Item_Master` (Django Model: `ItemMaster`)**:
    *   `Id` (Primary Key, Integer)
    *   `CompId` (Integer)
    *   `ItemCode` (String)
    *   `ManfDesc` (Text)
    *   `UOMBasic` (Foreign Key to `tblDG_UOM.Id` - inferred)
    *   `CId` (Integer - used for conditional logic, e.g., `SelectIcode == 3`)
    *   `SysDate`, `SysTime`, `SessionId` (Audit fields)

*   **`tblMM_SPR_Master` (Django Model: `SprMaster`)**:
    *   `Id` (Primary Key, Integer)
    *   `SPRNo` (String)
    *   `CompId` (Integer)
    *   `SysDate`, `SysTime`, `SessionId` (Audit fields)

*   **`tblMM_Supplier_master` (Django Model: `SupplierMaster`)**:
    *   `SupplierId` (Primary Key, String)
    *   `SupplierName` (String)
    *   `CompId` (Integer)

*   **`AccHead` (Django Model: `AccHead`)**:
    *   `Id` (Primary Key, Integer)
    *   `Category` (String - "Labour", "With Material", etc.)
    *   `Name` (String - inferred from `fun.AcHead` usage)

*   **`BusinessGroup` (Django Model: `BusinessGroup`)**:
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (String)
    *   `Name` (String, referred to as `Dept` in UI)

*   **`tblDG_UOM` (Django Model: `Uom` - inferred)**:
    *   `Id` (Primary Key, Integer)
    *   `UOMName` (String - inferred)

*   **`tblMM_Rate_Register` (Django Model: `RateRegister` - inferred)**:
    *   `ItemId` (Foreign Key to `ItemMaster.Id`)
    *   `CompId` (Integer)
    *   `Rate` (Decimal)
    *   `Discount` (Decimal)

*   **`tblMM_RateLockUnLock_Master` (Django Model: `RateLockUnlockMaster` - inferred)**:
    *   `ItemId` (Foreign Key to `ItemMaster.Id`)
    *   `CompId` (Integer)
    *   `LockUnlock` (Boolean/Integer, `1` implies locked)
    *   `Type` (Integer, `1` used in logic)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**
*   **Read:** The `Page_Load` method is entirely dedicated to reading existing SPR detail data and populating the form fields, including related item, supplier, UOM, A/c Head, and department/WO information.
*   **Update:** The `btnUpdate_Click` method performs the core update operation. It modifies `tblMM_SPR_Master` (for audit fields), `tblMM_SPR_Details` (for all editable fields), and conditionally `tblDG_Item_Master` (if `SelectIcode` is 3, updating `ManfDesc` and `UOMBasic`).
*   **Create:** This specific ASP.NET page is purely for editing and does not have a "create" flow. However, our Django modernization plan will include a `CreateView` for completeness and reusability, which will be accessible via HTMX modal from a list view.
*   **Delete:** This page does not include a delete operation. Our Django plan will include a `DeleteView` for completeness.
*   **Validation & Business Logic:**
    *   **Field-level Validation:** Required fields, data type checks (decimal, integer), and date format validation.
    *   **Supplier Autocomplete:** `AutoCompleteExtender` for `txtNewCustomerName` implies a lookup against `tblMM_Supplier_master`.
    *   **Dynamic Dropdowns:**
        *   `RadioButtonList1_SelectedIndexChanged` dynamically filters `DropDownList1` (A/c Head) based on category.
        *   Radio buttons for "BG Group" vs "WO No" control field visibility/validation.
    *   **Conditional Field Editability:** `txtManfDesc` and `DDLUomBasic` are made read-only/disabled based on `SelectIcode` (Item category `CId`).
    *   **Complex Rate Validation:** This is a critical business rule. `btnUpdate_Click` compares the entered `Rate` and `Discount` (calculated `DiscRate`) against a minimum historical rate from `tblMM_Rate_Register`. If the new `DiscRate` is higher than the historical minimum, it checks `tblMM_RateLockUnLock_Master` for an explicit override (`LockUnlock='1'`). This logic is central to the "Fat Model" strategy.
    *   **Work Order Validation:** `fun.CheckValidWONo` performs validation on the entered `txtwono`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting client-side interactions.

**Analysis:**
*   **Input Controls:** `TextBox` (e.g., `txtManfDesc`, `txtQty`, `txtRate`, `txtDiscount`, `txtNewCustomerName`, `txtwono`, `textDelDate`, `txtRemark`), `DropDownList` (e.g., `DDLUomBasic`, `DropDownList1`, `drpdept`), `RadioButtonList` (`RadioButtonList1`), `RadioButton` (`rddept`, `rdwono`), `Label` (`lblItemCode`).
*   **Client-Side Features:**
    *   `AjaxControlToolkit.CalendarExtender`: For `textDelDate`. Will be replaced by `type="date"` input or a simple Alpine.js date picker.
    *   `AjaxControlToolkit.AutoCompleteExtender`: For `txtNewCustomerName`. Will be replaced by HTMX triggering a backend API endpoint for suggestions.
    *   `OnClientClick=" return confirmationUpdate()"`: JavaScript confirmation. Will be handled by Alpine.js or HTMX swap with confirmation partial.
*   **Master Page/Content Placeholders:** Indicate a templating system. Django's template inheritance (extending `core/base.html`) is the direct equivalent.
*   **Styling:** `CssClass="box3"`, `CssClass="redbox"`, inline styles. Will be replaced by Tailwind CSS utility classes.

### Step 4: Generate Django Code

The following Django files will be created within a new Django application, e.g., `spr_management`.

#### 4.1 Models (`spr_management/models.py`)

This file defines the database schema and encapsulates business logic.

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
import datetime
import decimal

# Helper function to extract code from 'Name [Code]' format for Supplier
def extract_code_from_display(text_with_code):
    """Extracts the ID/code from a string like 'Supplier Name [ID]'."""
    if '[' in text_with_code and ']' in text_with_code:
        return text_with_code.split('[')[-1].strip(']')
    return None

class Uom(models.Model):
    """
    Represents Units of Measurement.
    Inferred from DDLUomBasic population.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='UOMName', max_length=100) # Assuming UOMName is the column for UOM text

    class Meta:
        managed = False # Existing database table
        db_table = 'tblDG_UOM'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.name

class ItemMaster(models.Model):
    """
    Represents master data for items.
    Corresponds to tblDG_Item_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.TextField(db_column='ManfDesc', blank=True, null=True)
    uom_basic = models.ForeignKey(Uom, on_delete=models.DO_NOTHING, db_column='UOMBasic')
    c_id = models.IntegerField(db_column='CId', default=0) # 'SelectIcode' in ASP.NET
    sys_date = models.DateField(db_column='SysDate', auto_now=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    def get_item_code_part_no(self):
        """
        Translates fun.GetItemCode_PartNo.
        In a real scenario, this might involve more complex logic (e.g., part number).
        For now, returns the item code.
        """
        return self.item_code

class SupplierMaster(models.Model):
    """
    Represents supplier master data.
    Corresponds to tblMM_Supplier_master.
    """
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', default=0)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class AccHead(models.Model):
    """
    Represents Account Head master data.
    Corresponds to AccHead.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=100) # e.g., 'Labour', 'With Material'
    name = models.CharField(db_column='Name', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"{self.name} ({self.category})"

class BusinessGroup(models.Model):
    """
    Represents Business Groups/Departments.
    Corresponds to BusinessGroup.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='Name', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return f"[{self.symbol}] {self.name}"

class SprMaster(models.Model):
    """
    Represents SPR Master data.
    Corresponds to tblMM_SPR_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, unique=True)
    comp_id = models.IntegerField(db_column='CompId')
    sys_date = models.DateField(db_column='SysDate', auto_now=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

class RateRegister(models.Model):
    """
    Records historical rates for items.
    Corresponds to tblMM_Rate_Register.
    """
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    comp_id = models.IntegerField(db_column='CompId')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount = models.DecimalField(db_column='Discount', max_digits=5, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register Entry'
        verbose_name_plural = 'Rate Register Entries'

    def calculate_discounted_rate(self):
        """Calculates the effective rate after applying discount."""
        if self.rate is None or self.discount is None:
            return decimal.Decimal(0.00)
        return self.rate - (self.rate * self.discount / 100)

class RateLockUnlockMaster(models.Model):
    """
    Defines if an item's rate can be set above historical minimum.
    Corresponds to tblMM_RateLockUnLock_Master.
    """
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    comp_id = models.IntegerField(db_column='CompId')
    lock_unlock = models.BooleanField(db_column='LockUnlock') # True for locked, False for unlocked
    type = models.IntegerField(db_column='Type') # '1' is used in the ASP.NET logic

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock/Unlock Setting'
        verbose_name_plural = 'Rate Lock/Unlock Settings'

    def __str__(self):
        return f"{self.item.item_code} - Locked: {self.lock_unlock}"

class SprDetail(models.Model):
    """
    Represents an item detail within an SPR.
    Corresponds to tblMM_SPR_Details.
    This model encapsulates the core business logic.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_master = models.ForeignKey(SprMaster, on_delete=models.DO_NOTHING, db_column='MId')
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount = models.DecimalField(db_column='Discount', max_digits=5, decimal_places=2)
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id')
    ah_id = models.ForeignKey(AccHead, on_delete=models.DO_NOTHING, db_column='AHId')
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    del_date = models.DateField(db_column='DelDate')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'
        unique_together = ('spr_master', 'item') # Assuming this uniqueness based on typical ERP design

    def __str__(self):
        return f"SPR {self.spr_no} - Item {self.item.item_code}"

    def calculate_discounted_rate(self):
        """Calculates the effective rate after applying discount."""
        return self.rate - (self.rate * self.discount / 100)

    def validate_rate(self, comp_id):
        """
        Implements the complex rate validation logic from ASP.NET code-behind.
        Raises ValidationError if the rate is not acceptable.
        """
        disc_rate = self.calculate_discounted_rate()

        if disc_rate <= 0:
            raise ValidationError("Entered rate is not acceptable! Discounted rate cannot be zero or negative.")

        min_rate_register = RateRegister.objects.filter(
            item=self.item,
            comp_id=comp_id
        ).aggregate(min_disc_rate=models.Min(models.F('rate') - (models.F('rate') * models.F('discount') / 100)))

        min_rate_from_db = min_rate_register['min_disc_rate'] if min_rate_register['min_disc_rate'] is not None else decimal.Decimal(0.00)

        if min_rate_from_db > 0:
            if disc_rate <= min_rate_from_db:
                # Current discounted rate is less than or equal to the minimum historical rate, so it's acceptable.
                pass
            else:
                # If current rate is higher, check RateLockUnlockMaster for an override.
                rate_lock_unlocked = RateLockUnlockMaster.objects.filter(
                    item=self.item,
                    comp_id=comp_id,
                    lock_unlock=True, # '1' means true
                    type=1
                ).exists()

                if not rate_lock_unlocked:
                    raise ValidationError("Entered rate is not acceptable! It's higher than the minimum historical rate and locked.")
        # If min_rate_from_db is 0 or less, any positive disc_rate is acceptable.

    def validate_work_order_number(self, comp_id, fin_year_id):
        """
        Translates fun.CheckValidWONo.
        This is a placeholder for actual validation against a Work Order Master table.
        """
        if self.wo_no and not self.dept: # Only validate if WO is provided and department is not chosen
            # Replace with actual database query to check if WO exists for comp_id and fin_year_id
            # For demonstration, we'll assume a dummy check.
            is_valid_wo_in_db = True # Placeholder for actual validation logic
            if not is_valid_wo_in_db:
                raise ValidationError("Invalid Work Order Number.")
        return True

    def update_associated_records(self, session_id, comp_id, fin_year_id):
        """
        Updates associated SprMaster and ItemMaster records (fat model approach).
        This combines logic from btnUpdate_Click.
        """
        # Update SPR Master
        self.spr_master.sys_date = timezone.now().date()
        self.spr_master.sys_time = timezone.now().time()
        self.spr_master.session_id = session_id
        self.spr_master.save()

        # Update Item Master if CId is 3 (ASP.NET 'SelectIcode == 3' logic)
        if self.item.c_id == 3:
            self.item.manf_desc = self.manf_desc # Update description from SPR detail form
            self.item.uom_basic = self.uom_basic # Update UOM from SPR detail form
            self.item.sys_date = timezone.now().date()
            self.item.sys_time = timezone.now().time()
            self.item.session_id = session_id
            self.item.save()

        # Perform complex rate validation before saving the SprDetail
        self.validate_rate(comp_id)

        # Perform work order validation
        self.validate_work_order_number(comp_id, fin_year_id)

```

#### 4.2 Forms (`spr_management/forms.py`)

This file defines the Django Form for user input, including custom fields for autocomplete and conditional logic.

```python
from django import forms
from django.core.exceptions import ValidationError
from .models import SprDetail, ItemMaster, Uom, AccHead, BusinessGroup, SupplierMaster
import datetime
import decimal # Import decimal for type consistency

class SprDetailForm(forms.ModelForm):
    # Fields directly mapping to SprDetail model
    qty = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        min_value=decimal.Decimal('0.001') # Qty cannot be empty, implies positive
    )
    rate = forms.DecimalField(
        max_digits=18,
        decimal_places=2,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        min_value=decimal.Decimal('0.01') # Rate cannot be empty, implies positive
    )
    discount = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        required=False,
        initial=decimal.Decimal('0.00'), # Default to 0 as per ASP.NET implicit behavior
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        min_value=decimal.Decimal('0.00')
    )
    remarks = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-36'}),
        required=False
    )
    del_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date', 'readonly': 'readonly'}),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'], # Allow both formats
    )
    
    # Item-related fields for display and conditional update (from ItemMaster)
    item_code_display = forms.CharField(
        label="Item Code",
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border-none bg-gray-100 text-gray-600', 'readonly': 'readonly'}),
    )
    item_manf_desc = forms.CharField(
        label="Description",
        widget=forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
        required=False
    )
    item_uom_basic = forms.ModelChoiceField(
        queryset=Uom.objects.all(),
        label="UOM",
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # Supplier autocomplete fields
    supplier_display_name = forms.CharField(
        label="Supplier",
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/spr_management/api/suppliers_autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'placeholder': 'Start typing supplier name...'
        }),
        required=True
    )
    supplier_id_hidden = forms.CharField(widget=forms.HiddenInput(), required=True) # Actual ID to be submitted

    # Account Head category radio buttons and dynamic dropdown
    ac_head_category = forms.ChoiceField(
        label="A/c Head",
        choices=[
            ('1', 'Labour'), ('2', 'With Material'),
            ('3', 'Expenses'), ('4', 'Ser. provider')
        ],
        widget=forms.RadioSelect(attrs={'hx-post': '/spr_management/api/ac_head_dropdown/', 'hx-target': '#ac-head-dropdown-container', 'hx-trigger': 'change', 'class': 'flex items-center space-x-4'}),
        initial='1',
        required=True
    )
    ac_head = forms.ModelChoiceField(
        queryset=AccHead.objects.none(), # Initial queryset is empty; populated via HTMX
        label="", # Label handled by category field in template
        required=True,
        empty_label="Select A/c Head",
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # Work Order / Business Group radio buttons and conditional fields
    work_order_or_dept = forms.ChoiceField(
        label="", # Label handled by radio buttons directly in template
        choices=[('dept', 'BG Group'), ('wono', 'WO No')],
        widget=forms.RadioSelect(attrs={'class': 'flex items-center space-x-4'}),
        initial='dept',
        required=True
    )
    dept = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        label="",
        required=False, # Will be required conditionally
        empty_label="Select BG Group",
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    wo_no = forms.CharField(
        label="",
        max_length=50,
        required=False, # Will be required conditionally
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = SprDetail
        # Exclude fields from direct rendering that are handled by custom fields or are derived
        exclude = ['supplier', 'ah_id', 'dept', 'wo_no', 'item', 'spr_master', 'spr_no']

    def __init__(self, *args, **kwargs):
        self.comp_id = kwargs.pop('comp_id', None)
        self.fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)

        # Set initial values for form fields when editing an existing instance
        if self.instance.pk:
            # Item Details
            self.fields['item_code_display'].initial = self.instance.item.get_item_code_part_no()
            self.fields['item_manf_desc'].initial = self.instance.item.manf_desc
            self.fields['item_uom_basic'].initial = self.instance.item.uom_basic
            
            # SPR Details
            self.fields['qty'].initial = self.instance.qty
            self.fields['rate'].initial = self.instance.rate
            self.fields['discount'].initial = self.instance.discount
            self.fields['remarks'].initial = self.instance.remarks
            self.fields['del_date'].initial = self.instance.del_date
            
            # Supplier
            self.fields['supplier_display_name'].initial = str(self.instance.supplier)
            self.fields['supplier_id_hidden'].initial = self.instance.supplier.supplier_id

            # Account Head (pre-select category and populate dropdown)
            ac_head_obj = self.instance.ah_id
            if ac_head_obj:
                category_map = {
                    "Labour": "1", "With Material": "2",
                    "Expenses": "3", "Service Provider": "4"
                }
                if ac_head_obj.category in category_map:
                    self.fields['ac_head_category'].initial = category_map[ac_head_obj.category]
                    self.fields['ac_head'].queryset = AccHead.objects.filter(category=ac_head_obj.category)
                self.fields['ac_head'].initial = ac_head_obj

            # Work Order / Department (pre-select radio and populate fields)
            if self.instance.dept_id: # If DeptId is set, it means BG Group was selected
                self.fields['work_order_or_dept'].initial = 'dept'
                self.fields['dept'].initial = self.instance.dept
                self.fields['wo_no'].widget.attrs['readonly'] = 'readonly'
            else: # Otherwise, WO No was selected
                self.fields['work_order_or_dept'].initial = 'wono'
                self.fields['wo_no'].initial = self.instance.wo_no
                self.fields['dept'].widget.attrs['disabled'] = 'disabled'
        else:
            # For new instances, initialize AccHead queryset to default category ('Labour')
            self.fields['ac_head'].queryset = AccHead.objects.filter(category="Labour")
            self.fields['wo_no'].widget.attrs['readonly'] = 'readonly' # Default to dept selected

        # Apply conditional read-only/disabled based on item_master CId (SelectIcode)
        if self.instance.pk and self.instance.item.c_id != 3:
            self.fields['item_manf_desc'].widget.attrs['readonly'] = 'readonly'
            self.fields['item_uom_basic'].widget.attrs['disabled'] = 'disabled'
        # else: for new instances, assume editable by default, or apply global rule


    def clean(self):
        cleaned_data = super().clean()

        # Re-map cleaned data from custom fields back to model fields
        # Supplier validation
        supplier_id_str = cleaned_data.get('supplier_id_hidden')
        if supplier_id_str:
            try:
                supplier = SupplierMaster.objects.get(supplier_id=supplier_id_str)
                cleaned_data['supplier'] = supplier
            except SupplierMaster.DoesNotExist:
                self.add_error('supplier_display_name', "Invalid supplier selected. Please choose from suggestions.")
        else:
            self.add_error('supplier_display_name', "Supplier is required.")

        # Account Head validation
        ac_head_obj = cleaned_data.get('ac_head')
        if not ac_head_obj:
            self.add_error('ac_head', "Account Head is required.")
        cleaned_data['ah_id'] = ac_head_obj

        # Work Order / Department validation
        work_order_or_dept = self.data.get('work_order_or_dept') # Use self.data to get raw POST value for radio button
        if work_order_or_dept == 'wono':
            wo_no = cleaned_data.get('wo_no')
            if not wo_no:
                self.add_error('wo_no', "Work Order Number is required when 'WO No' is selected.")
            cleaned_data['dept'] = None # Ensure dept is null if WO is selected
        elif work_order_or_dept == 'dept':
            dept = cleaned_data.get('dept')
            if not dept:
                self.add_error('dept', "Business Group is required when 'BG Group' is selected.")
            cleaned_data['wo_no'] = None # Ensure WO is null if Dept is selected
        else:
            self.add_error('work_order_or_dept', "Please select either BG Group or WO No.")

        return cleaned_data

    def save(self, commit=True, **kwargs):
        instance = super().save(commit=False)

        # Map cleaned data from custom fields to model instance fields
        instance.supplier = self.cleaned_data['supplier']
        instance.ah_id = self.cleaned_data['ah_id']
        instance.wo_no = self.cleaned_data.get('wo_no')
        instance.dept = self.cleaned_data.get('dept')

        # If it's an update, ensure we're using the correct item instance
        if instance.pk:
            # These fields are conditionally updated on ItemMaster, so they need to be accessible
            # We don't save them directly to SprDetail model but pass them for ItemMaster update
            instance.item.manf_desc = self.cleaned_data.get('item_manf_desc')
            instance.item.uom_basic = self.cleaned_data.get('item_uom_basic')

        if commit:
            instance.save() # This triggers the model's save method
            # After SprDetail is saved, update associated records via model method
            # This requires session/company/financial year info from the request, passed via view
            instance.update_associated_records(
                session_id=kwargs.get('session_id'),
                comp_id=kwargs.get('comp_id'),
                fin_year_id=kwargs.get('fin_year_id')
            )
        return instance

```

#### 4.3 Views (`spr_management/views.py`)

This file defines the Django Class-Based Views for CRUD operations and HTMX partials.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db.models import Q
from django.core.exceptions import ValidationError

from .models import SprDetail, SprMaster, ItemMaster, SupplierMaster, AccHead, BusinessGroup, Uom
from .forms import SprDetailForm
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming authentication

# --- Main Views ---

class SprDetailListView(LoginRequiredMixin, ListView):
    """
    Displays a list of SPR Details. This would be the main entry point to view SPRs.
    """
    model = SprDetail
    template_name = 'spr_management/sprdetail/list.html'
    context_object_name = 'spr_details'
    # For a real list, you would filter by SPRNo from context or query parameters.
    # For this specific page, we're focusing on the edit form, so a dummy list.

class SprDetailTablePartialView(LoginRequiredMixin, View):
    """
    Renders the DataTables partial for SprDetailListView.
    Loaded via HTMX to keep the list view dynamic.
    """
    def get(self, request, *args, **kwargs):
        # In a real scenario, this would likely be filtered by SPRNo from request.GET
        # For this example, we fetch all, but you'd limit/filter based on context.
        spr_no = request.GET.get('spr_no') # Example: get SPRNo from query param
        if spr_no:
            spr_details = SprDetail.objects.filter(spr_no=spr_no)
        else:
            spr_details = SprDetail.objects.all() # Or some default/filtered list
        return render(request, 'spr_management/sprdetail/_sprdetail_table.html', {'spr_details': spr_details})

class SprDetailCreateView(LoginRequiredMixin, CreateView):
    """
    Handles creation of a new SPR Detail item via HTMX modal.
    """
    model = SprDetail
    form_class = SprDetailForm
    template_name = 'spr_management/sprdetail/form.html'
    # No direct success_url, handled by HTMX trigger

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass session-specific company/financial year IDs to the form for model validation
        kwargs['comp_id'] = self.request.session.get('compid', 1) # Dummy default
        kwargs['fin_year_id'] = self.request.session.get('finyear', 1) # Dummy default
        return kwargs
        
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Assuming spr_master and item are passed via query parameters or context
        # For a real 'add' in an SPR, these would be pre-selected.
        context['spr_no'] = self.request.GET.get('spr_no', 'SPR001') # Example
        context['mid'] = self.request.GET.get('mid', 1) # Example SPR Master ID
        context['item_id'] = self.request.GET.get('item_id', 1) # Example Item ID
        return context

    def form_valid(self, form):
        # Retrieve context from request (e.g., from hidden inputs in form or URL params)
        # This will be based on how SprDetailCreateView is linked from SprMaster
        spr_master_id = self.request.POST.get('spr_master_id') # Example: hidden input
        item_id = self.request.POST.get('item_id') # Example: hidden input
        spr_no = self.request.POST.get('spr_no') # Example: hidden input

        spr_master = get_object_or_404(SprMaster, id=spr_master_id)
        item = get_object_or_404(ItemMaster, id=item_id)
        
        # Set hidden fields on the instance before saving
        form.instance.spr_master = spr_master
        form.instance.item = item
        form.instance.spr_no = spr_no # Assign SPRNo from context/master

        try:
            # Pass session context to form.save() for model-level validation
            form.save(session_id=self.request.session.get('username'),
                      comp_id=self.request.session.get('compid'),
                      fin_year_id=self.request.session.get('finyear'))
            messages.success(self.request, 'SPR Detail added successfully.')
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing more than trigger
                headers={'HX-Trigger': 'refreshSprDetailList'} # Custom event to refresh list
            )
        except ValidationError as e:
            # Handle model-level validation errors and re-render form with errors
            for field, errors in e.message_dict.items():
                for error in errors:
                    form.add_error(field, error) # Add errors back to specific fields
            # If no field specified in ValidationError, add to non-field errors
            if not e.message_dict:
                form.add_error(None, e.message)
            return render(self.request, self.template_name, {'form': form}, status=400)

    def form_invalid(self, form):
        # Render the form with validation errors for HTMX to swap
        return render(self.request, self.template_name, {'form': form}, status=400)


class SprDetailUpdateView(LoginRequiredMixin, UpdateView):
    """
    Handles editing an existing SPR Detail item via HTMX modal.
    This is the direct replacement for the ASP.NET SPR_Edit_Details_Item.aspx page.
    """
    model = SprDetail
    form_class = SprDetailForm
    template_name = 'spr_management/sprdetail/form.html'
    # No direct success_url, handled by HTMX trigger

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass session-specific company/financial year IDs to the form for model validation
        kwargs['comp_id'] = self.request.session.get('compid', 1) # Dummy default
        kwargs['fin_year_id'] = self.request.session.get('finyear', 1) # Dummy default
        # Pass item_master_c_id for conditional field read-only/disabled
        if self.object and self.object.item:
            kwargs['item_master_c_id'] = self.object.item.c_id
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Required for rendering correct form and potentially redirect on cancel
        context['spr_no'] = self.object.spr_no
        context['mid'] = self.object.spr_master.id
        context['item_id'] = self.object.item.id
        return context

    def form_valid(self, form):
        try:
            # Pass session context to form.save() for model-level validation
            form.save(session_id=self.request.session.get('username'),
                      comp_id=self.request.session.get('compid'),
                      fin_year_id=self.request.session.get('finyear'))
            messages.success(self.request, 'SPR Detail updated successfully.')
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing more than trigger
                headers={'HX-Trigger': 'refreshSprDetailList'} # Custom event to refresh list
            )
        except ValidationError as e:
            # Handle model-level validation errors and re-render form with errors
            # Errors from form.save() (i.e., model clean/save) will be caught here
            for field, errors in e.message_dict.items():
                for error in errors:
                    form.add_error(field, error)
            if not e.message_dict: # If error message is not tied to a specific field
                form.add_error(None, e.message)
            return render(self.request, self.template_name, {'form': form}, status=400) # Render with errors

    def form_invalid(self, form):
        # Render the form with validation errors for HTMX to swap
        return render(self.request, self.template_name, {'form': form}, status=400)


class SprDetailDeleteView(LoginRequiredMixin, DeleteView):
    """
    Handles deletion of an SPR Detail item via HTMX modal.
    """
    model = SprDetail
    template_name = 'spr_management/sprdetail/confirm_delete.html'
    # No direct success_url, handled by HTMX trigger

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'SPR Detail deleted successfully.')
        return HttpResponse(
            status=204,
            headers={'HX-Trigger': 'refreshSprDetailList'} # Custom event to refresh list
        )

# --- HTMX API Endpoints ---

class SupplierAutocompleteView(LoginRequiredMixin, View):
    """
    HTMX endpoint for supplier name autocomplete.
    Translates the ASP.NET WebMethod 'sql'.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('hx-current-value', '').strip()
        comp_id = request.session.get('compid', 1) # Get company ID from session

        if prefix_text:
            suppliers = SupplierMaster.objects.filter(
                Q(supplier_name__icontains=prefix_text) | Q(supplier_id__icontains=prefix_text),
                comp_id=comp_id
            )[:10] # Limit suggestions, similar to CompletionSetCount
            suggestions = [str(s) for s in suppliers] # Format: "Supplier Name [ID]"
        else:
            suggestions = []
        
        # HTMX requires list of items for suggestion dropdowns
        # For a true HTMX approach, render a list of <option> or <li>
        return HttpResponse("".join([f"<option value='{s}'>{s}</option>" for s in suggestions]))

class AccHeadDropdownView(LoginRequiredMixin, View):
    """
    HTMX endpoint to dynamically populate the Account Head dropdown
    based on the selected category radio button.
    Translates RadioButtonList1_SelectedIndexChanged.
    """
    def post(self, request, *args, **kwargs):
        category_value = request.POST.get('ac_head_category')
        
        category_map_reverse = {
            "1": "Labour",
            "2": "With Material",
            "3": "Expenses",
            "4": "Service Provider"
        }
        selected_category_name = category_map_reverse.get(category_value, '')

        ac_heads = AccHead.objects.filter(category=selected_category_name)
        
        options = '<option value="">Select A/c Head</option>'
        for ah in ac_heads:
            options += f'<option value="{ah.id}">{ah.name}</option>'
        
        # Return only the options for the dropdown
        return HttpResponse(options)

class WoDeptToggleView(LoginRequiredMixin, View):
    """
    HTMX endpoint to toggle readonly/disabled states of WO No and Dept fields
    based on radio button selection.
    """
    def post(self, request, *args, **kwargs):
        selected_option = request.POST.get('work_order_or_dept')

        # We return a fragment that Alpine.js can use to update state
        # Or, we can use HTMX target and swap directly on the elements.
        # For simplicity, returning a fragment that modifies the DOM based on ID/classes
        if selected_option == 'wono':
            html = """
            <div id="wo_dept_controls" hx-swap-oob="true">
                <input type="text" name="wo_no" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter WO No" value="" />
                <select name="dept" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" disabled>
                    <option value="">Select BG Group</option>
                </select>
            </div>
            """
        else: # 'dept' selected
            # Re-render the select with all options from BusinessGroup
            business_groups = BusinessGroup.objects.all()
            dept_options = '<option value="">Select BG Group</option>'
            for bg in business_groups:
                dept_options += f'<option value="{bg.id}">[{bg.symbol}] {bg.name}</option>'
            
            html = f"""
            <div id="wo_dept_controls" hx-swap-oob="true">
                <input type="text" name="wo_no" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" readonly placeholder="Enter WO No" value="" />
                <select name="dept" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    {dept_options}
                </select>
            </div>
            """
        return HttpResponse(html)


```

#### 4.4 Templates (`spr_management/templates/spr_management/sprdetail/`)

**`list.html`**: The main page displaying all SPR details, with buttons to add, edit, or delete items.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">SPR Details</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'sprdetail_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New SPR Detail
        </button>
    </div>
    
    <div id="sprdetailTable-container"
         hx-trigger="load, refreshSprDetailList from:body"
         hx-get="{% url 'sprdetail_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading SPR Details...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed for broader UI elements
    document.addEventListener('alpine:init', () => {
        // e.g., for general modal state management if not using htmx's _ syntax
    });
</script>
{% endblock %}

```

**`_sprdetail_table.html`**: Partial template for rendering the DataTables content. Loaded dynamically by HTMX.

```html
<table id="sprdetailTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in spr_details %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.spr_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item.manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.rate|floatformat:"2" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier.supplier_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'sprdetail_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'sprdetail_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-gray-500">No SPR Details found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTable is initialized only once per load/swap
    if ($.fn.DataTable.isDataTable('#sprdetailTable')) {
        $('#sprdetailTable').DataTable().destroy();
    }
    $('#sprdetailTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
</script>
```

**`form.html`**: Partial template for Add/Edit forms, loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} SPR Detail</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-indicator="#form-loading-spinner"
          _="on htmx:afterRequest if event.detail.xhr.status >= 200 && event.detail.xhr.status < 300 remove .is-active from #modal">
        {% csrf_token %}
        
        <div class="space-y-4">
            <!-- Hidden fields for context (e.g., from query string/parent page) -->
            <!-- In a real app, these would be derived from the calling context (e.g., SPR Master) -->
            {% if not form.instance.pk %}
            <input type="hidden" name="spr_master_id" value="{{ spr_master.id|default:mid }}">
            <input type="hidden" name="item_id" value="{{ item.id|default:item_id }}">
            <input type="hidden" name="spr_no" value="{{ spr_no }}">
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Item Code (Display Only) -->
                <div>
                    <label for="{{ form.item_code_display.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.item_code_display.label }}
                    </label>
                    {{ form.item_code_display }}
                </div>
                <!-- Quantity -->
                <div>
                    <label for="{{ form.qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.qty.label }}
                    </label>
                    {{ form.qty }}
                    {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                </div>
            </div>

            <!-- Description (Conditionally Editable) -->
            <div>
                <label for="{{ form.item_manf_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.item_manf_desc.label }}
                </label>
                {{ form.item_manf_desc }}
                {% if form.item_manf_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.item_manf_desc.errors }}</p>{% endif %}
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- UOM (Conditionally Editable) -->
                <div>
                    <label for="{{ form.item_uom_basic.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.item_uom_basic.label }}
                    </label>
                    {{ form.item_uom_basic }}
                    {% if form.item_uom_basic.errors %}<p class="text-red-500 text-xs mt-1">{{ form.item_uom_basic.errors }}</p>{% endif %}
                </div>
                <!-- Rate -->
                <div>
                    <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.rate.label }}
                    </label>
                    {{ form.rate }}
                    {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                </div>
            </div>
            
            <!-- Discount -->
            <div>
                <label for="{{ form.discount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.discount.label }}
                </label>
                {{ form.discount }}
                {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
            </div>

            <!-- Supplier Autocomplete -->
            <div>
                <label for="{{ form.supplier_display_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.supplier_display_name.label }}
                </label>
                {{ form.supplier_display_name }}
                <div id="supplier-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 relative"></div>
                {{ form.supplier_id_hidden }} {# Hidden field to hold the actual supplier ID #}
                {% if form.supplier_display_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier_display_name.errors }}</p>{% endif %}
            </div>

            <!-- A/c Head Category and Dropdown -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        {{ form.ac_head_category.label }}
                    </label>
                    <div class="mt-2">{{ form.ac_head_category }}</div>
                    {% if form.ac_head_category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ac_head_category.errors }}</p>{% endif %}
                </div>
                <div id="ac-head-dropdown-container">
                    <label for="{{ form.ac_head.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Account Head
                    </label>
                    {{ form.ac_head }}
                    {% if form.ac_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ac_head.errors }}</p>{% endif %}
                </div>
            </div>

            <!-- BG Group / WO No Toggle -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        Work Assignment
                    </label>
                    <div class="mt-2" hx-post="{% url 'spr_management_wo_dept_toggle' %}" hx-target="#wo_dept_controls" hx-trigger="change from:input[name='work_order_or_dept']" hx-swap="outerHTML">
                        {{ form.work_order_or_dept }}
                    </div>
                    {% if form.work_order_or_dept.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_order_or_dept.errors }}</p>{% endif %}
                </div>
                <div id="wo_dept_controls">
                    {% if form.instance.dept_id %} {# Initial state if editing an existing record #}
                    <div>
                        <label for="{{ form.dept.id_for_label }}" class="block text-sm font-medium text-gray-700">BG Group</label>
                        {{ form.dept }}
                        {% if form.dept.errors %}<p class="text-red-500 text-xs mt-1">{{ form.dept.errors }}</p>{% endif %}
                    </div>
                    <input type="text" name="wo_no" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" readonly placeholder="Enter WO No" value="" />
                    {% elif form.instance.wo_no %}
                    <div>
                        <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">WO No</label>
                        {{ form.wo_no }}
                        {% if form.wo_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>{% endif %}
                    </div>
                    <select name="dept" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" disabled>
                        <option value="">Select BG Group</option>
                    </select>
                    {% else %} {# Default for new record #}
                    <div>
                        <label for="{{ form.dept.id_for_label }}" class="block text-sm font-medium text-gray-700">BG Group</label>
                        {{ form.dept }}
                        {% if form.dept.errors %}<p class="text-red-500 text-xs mt-1">{{ form.dept.errors }}</p>{% endif %}
                    </div>
                    <input type="text" name="wo_no" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" readonly placeholder="Enter WO No" value="" />
                    {% endif %}
                </div>
            </div>

            <!-- Delivery Date -->
            <div>
                <label for="{{ form.del_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.del_date.label }}
                </label>
                {{ form.del_date }}
                {% if form.del_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.del_date.errors }}</p>{% endif %}
            </div>

            <!-- Remarks -->
            <div>
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remarks.label }}
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>

            {% if form.non_field_errors %}
            <div class="text-red-500 text-xs mt-1">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}

        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <div id="form-loading-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`confirm_delete.html`**: Partial template for delete confirmation, loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete this SPR Detail for Item Code: <strong>{{ object.item.item_code }}</strong>?</p>
    <form hx-post="{% url 'sprdetail_delete' object.pk %}" hx-swap="none"
          hx-indicator="#delete-loading-spinner"
          _="on htmx:afterRequest if event.detail.xhr.status >= 200 && event.detail.xhr.status < 300 remove .is-active from #modal">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <div id="delete-loading-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
            </div>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`spr_management/urls.py`)

This file defines the URL patterns for the views and HTMX API endpoints.

```python
from django.urls import path
from .views import (
    SprDetailListView, SprDetailCreateView, SprDetailUpdateView, SprDetailDeleteView,
    SprDetailTablePartialView, SupplierAutocompleteView, AccHeadDropdownView, WoDeptToggleView
)

urlpatterns = [
    # Main UI views
    path('spr_details/', SprDetailListView.as_view(), name='sprdetail_list'),

    # HTMX partial views for modals and table refreshes
    path('spr_details/table/', SprDetailTablePartialView.as_view(), name='sprdetail_table_partial'),
    path('spr_details/add/', SprDetailCreateView.as_view(), name='sprdetail_add'),
    path('spr_details/edit/<int:pk>/', SprDetailUpdateView.as_view(), name='sprdetail_edit'),
    path('spr_details/delete/<int:pk>/', SprDetailDeleteView.as_view(), name='sprdetail_delete'),

    # HTMX API endpoints for dynamic interactions
    path('api/suppliers_autocomplete/', SupplierAutocompleteView.as_view(), name='spr_management_suppliers_autocomplete'),
    path('api/ac_head_dropdown/', AccHeadDropdownView.as_view(), name='spr_management_ac_head_dropdown'),
    path('api/wo_dept_toggle/', WoDeptToggleView.as_view(), name='spr_management_wo_dept_toggle'),
]

```

#### 4.6 Tests (`spr_management/tests.py`)

This file includes comprehensive unit tests for models and integration tests for views, aiming for high test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
import datetime
import decimal
from django.core.exceptions import ValidationError

from .models import (
    Uom, ItemMaster, SupplierMaster, AccHead, BusinessGroup,
    SprMaster, RateRegister, RateLockUnlockMaster, SprDetail
)

class SprManagementModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.session_id = 'test_user'

        cls.uom = Uom.objects.create(id=1, name='PCS')
        cls.item_master_code = ItemMaster.objects.create(
            id=1, comp_id=cls.comp_id, item_code='ITEM001', manf_desc='Test Item 1',
            uom_basic=cls.uom, c_id=1 # Not CId=3, so manf_desc/UOM are readonly
        )
        cls.item_master_nocode = ItemMaster.objects.create(
            id=2, comp_id=cls.comp_id, item_code='ITEM002', manf_desc='No-Code Item',
            uom_basic=cls.uom, c_id=3 # CId=3, so manf_desc/UOM are editable
        )
        cls.supplier = SupplierMaster.objects.create(
            supplier_id='SUP001', supplier_name='Test Supplier', comp_id=cls.comp_id
        )
        cls.acc_head_labour = AccHead.objects.create(id=1, category='Labour', name='Labor Charges')
        cls.acc_head_material = AccHead.objects.create(id=2, category='With Material', name='Material Cost')
        cls.business_group = BusinessGroup.objects.create(id=10, symbol='PROJ', name='Project Dept')
        cls.spr_master = SprMaster.objects.create(
            id=100, spr_no='SPR-2023-001', comp_id=cls.comp_id,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id=cls.session_id
        )
        
        # Rate Register data
        RateRegister.objects.create(item=cls.item_master_code, comp_id=cls.comp_id, rate=decimal.Decimal('100.00'), discount=decimal.Decimal('5.00')) # Disc Rate = 95.00
        RateRegister.objects.create(item=cls.item_master_code, comp_id=cls.comp_id, rate=decimal.Decimal('90.00'), discount=decimal.Decimal('0.00')) # Disc Rate = 90.00 (min)

        # Rate Lock/Unlock Master
        RateLockUnlockMaster.objects.create(item=cls.item_master_code, comp_id=cls.comp_id, lock_unlock=False, type=1) # Default: locked
        RateLockUnlockMaster.objects.create(item=cls.item_master_nocode, comp_id=cls.comp_id, lock_unlock=True, type=1) # Item 2: unlocked

        cls.spr_detail = SprDetail.objects.create(
            id=1, spr_master=cls.spr_master, spr_no=cls.spr_master.spr_no, item=cls.item_master_code,
            qty=decimal.Decimal('10.000'), rate=decimal.Decimal('95.00'), discount=decimal.Decimal('0.00'),
            supplier=cls.supplier, ah_id=cls.acc_head_labour, wo_no=None, dept=cls.business_group,
            remarks='Initial remarks', del_date=datetime.date.today()
        )
        cls.spr_detail_wo = SprDetail.objects.create(
            id=2, spr_master=cls.spr_master, spr_no=cls.spr_master.spr_no, item=cls.item_master_nocode,
            qty=decimal.Decimal('5.000'), rate=decimal.Decimal('120.00'), discount=decimal.Decimal('10.00'), # Disc rate = 108.00
            supplier=cls.supplier, ah_id=cls.acc_head_material, wo_no='WO-XYZ-123', dept=None,
            remarks='WO remarks', del_date=datetime.date.today()
        )
        

    def test_spr_detail_creation(self):
        obj = SprDetail.objects.get(id=1)
        self.assertEqual(obj.item.item_code, 'ITEM001')
        self.assertEqual(obj.supplier.supplier_name, 'Test Supplier')
        self.assertEqual(obj.qty, decimal.Decimal('10.000'))
        self.assertEqual(obj.calculate_discounted_rate(), decimal.Decimal('95.00'))
        self.assertEqual(obj.spr_master.spr_no, 'SPR-2023-001')

    def test_calculate_discounted_rate(self):
        obj = SprDetail.objects.get(id=1)
        self.assertEqual(obj.calculate_discounted_rate(), decimal.Decimal('95.00')) # 95 - (95 * 0 / 100)
        
        obj.rate = decimal.Decimal('100.00')
        obj.discount = decimal.Decimal('10.00')
        self.assertEqual(obj.calculate_discounted_rate(), decimal.Decimal('90.00')) # 100 - (100 * 10 / 100)

    def test_validate_rate_acceptable(self):
        # Current rate is 95, min rate from register is 90. So this should fail UNLESS unlocked
        # By default, RateLockUnlockMaster for item_master_code is False (locked)
        obj = SprDetail.objects.get(id=1)
        obj.rate = decimal.Decimal('95.00') # Current discounted rate 95.00
        obj.discount = decimal.Decimal('0.00')

        with self.assertRaisesMessage(ValidationError, "Entered rate is not acceptable! It's higher than the minimum historical rate and locked."):
             obj.validate_rate(self.comp_id)

        # Make it acceptable by setting rate <= min_rate_from_db
        obj.rate = decimal.Decimal('85.00')
        obj.validate_rate(self.comp_id) # Should not raise error

        # Make it acceptable by unlocking rate
        obj = SprDetail.objects.get(id=1) # Reset
        RateLockUnlockMaster.objects.filter(item=obj.item).update(lock_unlock=True) # Unlock for this item
        obj.rate = decimal.Decimal('100.00')
        obj.discount = decimal.Decimal('0.00')
        obj.validate_rate(self.comp_id) # Should not raise error now because it's unlocked

    def test_validate_rate_negative_discounted(self):
        obj = SprDetail.objects.get(id=1)
        obj.rate = decimal.Decimal('10.00')
        obj.discount = decimal.Decimal('110.00') # Makes discounted rate negative
        with self.assertRaisesMessage(ValidationError, "Entered rate is not acceptable! Discounted rate cannot be zero or negative."):
            obj.validate_rate(self.comp_id)

    def test_validate_work_order_number(self):
        obj = SprDetail.objects.get(id=2) # WO No provided, Dept is None
        obj.wo_no = 'VALID-WO-123'
        obj.dept = None
        obj.validate_work_order_number(self.comp_id, self.fin_year_id) # Should pass (dummy check)

        # Test case where WO is required but empty (if dept is None)
        obj.wo_no = ''
        with self.assertRaisesMessage(ValidationError, "Invalid Work Order Number."): # Placeholder message
            obj.validate_work_order_number(self.comp_id, self.fin_year_id)

    def test_update_associated_records_item_master(self):
        obj = SprDetail.objects.get(id=2) # This item has c_id=3
        original_desc = obj.item.manf_desc
        original_uom = obj.item.uom_basic

        obj.manf_desc = "Updated No-Code Item Description"
        new_uom = Uom.objects.create(id=99, name='KGS')
        obj.uom_basic = new_uom
        obj.save(session_id=self.session_id, comp_id=self.comp_id, fin_year_id=self.fin_year_id) # Call save directly to trigger model logic

        updated_item = ItemMaster.objects.get(id=obj.item.id)
        self.assertEqual(updated_item.manf_desc, "Updated No-Code Item Description")
        self.assertEqual(updated_item.uom_basic, new_uom)
        self.assertEqual(updated_item.session_id, self.session_id)
        self.assertAlmostEqual(updated_item.sys_date, timezone.now().date())
        # self.assertAlmostEqual(updated_item.sys_time, timezone.now().time(), delta=datetime.timedelta(seconds=5))

    def test_update_associated_records_spr_master(self):
        obj = SprDetail.objects.get(id=1)
        original_spr_master_sys_date = obj.spr_master.sys_date

        obj.save(session_id=self.session_id, comp_id=self.comp_id, fin_year_id=self.fin_year_id)

        updated_spr_master = SprMaster.objects.get(id=obj.spr_master.id)
        self.assertNotEqual(updated_spr_master.sys_date, original_spr_master_sys_date)
        self.assertEqual(updated_spr_master.session_id, self.session_id)


class SprManagementViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.session_user = 'test_user'

        cls.uom = Uom.objects.create(id=1, name='PCS')
        cls.item_master = ItemMaster.objects.create(
            id=1, comp_id=cls.comp_id, item_code='ITEM001', manf_desc='Test Item 1',
            uom_basic=cls.uom, c_id=1
        )
        cls.supplier = SupplierMaster.objects.create(
            supplier_id='SUP001', supplier_name='Test Supplier', comp_id=cls.comp_id
        )
        cls.acc_head = AccHead.objects.create(id=1, category='Labour', name='Labor Charges')
        cls.business_group = BusinessGroup.objects.create(id=10, symbol='PROJ', name='Project Dept')
        cls.spr_master = SprMaster.objects.create(
            id=100, spr_no='SPR-2023-001', comp_id=cls.comp_id,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id=cls.session_user
        )
        cls.spr_detail = SprDetail.objects.create(
            id=1, spr_master=cls.spr_master, spr_no=cls.spr_master.spr_no, item=cls.item_master,
            qty=decimal.Decimal('10.000'), rate=decimal.Decimal('100.00'), discount=decimal.Decimal('5.00'),
            supplier=cls.supplier, ah_id=cls.acc_head, wo_no=None, dept=cls.business_group,
            remarks='Initial remarks', del_date=datetime.date.today()
        )
        
        # Setup Rate Register for validation
        RateRegister.objects.create(item=cls.item_master, comp_id=cls.comp_id, rate=decimal.Decimal('90.00'), discount=decimal.Decimal('0.00')) # Min disc rate 90
        RateLockUnlockMaster.objects.create(item=cls.item_master, comp_id=cls.comp_id, lock_unlock=False, type=1)


    def setUp(self):
        self.client = Client()
        # Simulate logged-in user and session variables
        self.client.force_login(self.get_or_create_user())
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session['username'] = self.session_user
        session.save()

    def get_or_create_user(self):
        from django.contrib.auth.models import User
        user, created = User.objects.get_or_create(username='test_user', defaults={'password': 'password123', 'is_staff': True})
        return user

    def test_list_view(self):
        response = self.client.get(reverse('sprdetail_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/sprdetail/list.html')
        self.assertContains(response, 'SPR Details') # Check for heading
        self.assertContains(response, 'Add New SPR Detail') # Check for add button

    def test_table_partial_view(self):
        response = self.client.get(reverse('sprdetail_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/sprdetail/_sprdetail_table.html')
        self.assertContains(response, 'ITEM001') # Check for data from spr_detail

    def test_create_view_get(self):
        response = self.client.get(reverse('sprdetail_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/sprdetail/form.html')
        self.assertContains(response, 'Add SPR Detail') # Check form title
        self.assertContains(response, 'name="supplier_display_name"') # Check for autocomplete field

    def test_create_view_post_success(self):
        new_item = ItemMaster.objects.create(id=3, comp_id=self.comp_id, item_code='ITEM003', manf_desc='New Item', uom_basic=self.uom, c_id=1)
        new_supplier = SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='New Supplier', comp_id=self.comp_id)
        
        data = {
            'spr_master_id': self.spr_master.id,
            'item_id': new_item.id,
            'spr_no': self.spr_master.spr_no,
            'qty': '5.000',
            'rate': '150.00',
            'discount': '0.00',
            'remarks': 'New item remarks',
            'del_date': datetime.date.today().strftime('%Y-%m-%d'),
            'supplier_display_name': str(new_supplier), # Must be in "Name [ID]" format
            'supplier_id_hidden': new_supplier.supplier_id,
            'ac_head_category': '1', # Labour
            'ac_head': self.acc_head.id, # Labour Charges AccHead
            'work_order_or_dept': 'dept',
            'dept': self.business_group.id,
            'wo_no': '', # Empty since dept selected
        }
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'modalContent'}
        response = self.client.post(reverse('sprdetail_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertTrue(SprDetail.objects.filter(item=new_item, qty=decimal.Decimal('5.000')).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprDetailList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid_data(self):
        data = {
            'spr_master_id': self.spr_master.id,
            'item_id': self.item_master.id,
            'spr_no': self.spr_master.spr_no,
            'qty': '', # Invalid: empty quantity
            'rate': '100.00',
            'discount': '0.00',
            'remarks': 'Invalid data',
            'del_date': 'invalid-date', # Invalid date
            'supplier_display_name': 'Non Existent Supplier [FAKE]',
            'supplier_id_hidden': 'FAKE',
            'ac_head_category': '1',
            'ac_head': self.acc_head.id,
            'work_order_or_dept': 'dept',
            'dept': '', # Invalid: empty dept
            'wo_no': '',
        }
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'modalContent'}
        response = self.client.post(reverse('sprdetail_add'), data, **headers)
        
        self.assertEqual(response.status_code, 400) # HTMX error response
        self.assertContains(response, 'Add SPR Detail') # Should re-render form
        self.assertContains(response, 'Enter a number.') # Error for qty
        self.assertContains(response, 'Enter a valid date') # Error for date
        self.assertContains(response, 'Invalid supplier selected.') # Error for supplier
        self.assertContains(response, 'Business Group is required') # Error for dept

    def test_update_view_get(self):
        response = self.client.get(reverse('sprdetail_edit', args=[self.spr_detail.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/sprdetail/form.html')
        self.assertContains(response, 'Edit SPR Detail')
        self.assertContains(response, f'value="{self.spr_detail.qty}"') # Check pre-filled data

    def test_update_view_post_success(self):
        updated_qty = '15.000'
        data = {
            'qty': updated_qty,
            'rate': '90.00', # Acceptable rate
            'discount': '0.00',
            'remarks': 'Updated remarks for Item 1',
            'del_date': (datetime.date.today() + datetime.timedelta(days=7)).strftime('%Y-%m-%d'),
            'supplier_display_name': str(self.supplier),
            'supplier_id_hidden': self.supplier.supplier_id,
            'ac_head_category': '1',
            'ac_head': self.acc_head.id,
            'work_order_or_dept': 'dept',
            'dept': self.business_group.id,
            'wo_no': '',
        }
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'modalContent'}
        response = self.client.post(reverse('sprdetail_edit', args=[self.spr_detail.pk]), data, **headers)

        self.assertEqual(response.status_code, 204)
        updated_spr_detail = SprDetail.objects.get(pk=self.spr_detail.pk)
        self.assertEqual(updated_spr_detail.qty, decimal.Decimal(updated_qty))
        self.assertEqual(updated_spr_detail.remarks, 'Updated remarks for Item 1')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprDetailList', response.headers['HX-Trigger'])

    def test_update_view_post_rate_validation_fail(self):
        data = {
            'qty': '10.000',
            'rate': '100.00', # This makes discounted rate 100.00, but min is 90.00 and locked
            'discount': '0.00',
            'remarks': 'Should fail rate validation',
            'del_date': datetime.date.today().strftime('%Y-%m-%d'),
            'supplier_display_name': str(self.supplier),
            'supplier_id_hidden': self.supplier.supplier_id,
            'ac_head_category': '1',
            'ac_head': self.acc_head.id,
            'work_order_or_dept': 'dept',
            'dept': self.business_group.id,
            'wo_no': '',
        }
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'modalContent'}
        response = self.client.post(reverse('sprdetail_edit', args=[self.spr_detail.pk]), data, **headers)

        self.assertEqual(response.status_code, 400) # Should return form with errors
        self.assertContains(response, 'Entered rate is not acceptable! It&#39;s higher than the minimum historical rate and locked.')
        # Ensure data was NOT updated
        self.assertEqual(SprDetail.objects.get(pk=self.spr_detail.pk).rate, decimal.Decimal('100.00'))

    def test_delete_view_get(self):
        response = self.client.get(reverse('sprdetail_delete', args=[self.spr_detail.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/sprdetail/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.spr_detail.item.item_code)

    def test_delete_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'modalContent'}
        response = self.client.post(reverse('sprdetail_delete', args=[self.spr_detail.pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(SprDetail.objects.filter(pk=self.spr_detail.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprDetailList', response.headers['HX-Trigger'])

    # --- HTMX API Endpoint Tests ---
    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('spr_management_suppliers_autocomplete'), {'hx-current-value': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'<option value="{str(self.supplier)}">{str(self.supplier)}</option>', response.content.decode())
        self.assertNotIn('NonExistent', response.content.decode())

    def test_ac_head_dropdown_view(self):
        response = self.client.post(reverse('spr_management_ac_head_dropdown'), {'ac_head_category': '1'}) # Labour
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.acc_head.id}">{self.acc_head.name}</option>')
        
        response = self.client.post(reverse('spr_management_ac_head_dropdown'), {'ac_head_category': '2'}) # With Material
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, self.acc_head.name) # Labour AccHead should not be there

    def test_wo_dept_toggle_view(self):
        response = self.client.post(reverse('spr_management_wo_dept_toggle'), {'work_order_or_dept': 'wono'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'name="wo_no"')
        self.assertContains(response, 'disabled') # Dept dropdown should be disabled
        
        response = self.client.post(reverse('spr_management_wo_dept_toggle'), {'work_order_or_dept': 'dept'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'name="dept"')
        self.assertContains(response, 'readonly') # WO No input should be readonly


```

### Step 5: HTMX and Alpine.js Integration

The integration strategy focuses on:
*   **HTMX for CRUD Operations:** All form submissions (Add/Edit/Delete) use `hx-post` and `hx-swap="none"` combined with `HX-Trigger` headers to close modals and refresh the main list.
*   **HTMX for Dynamic Content:** The DataTables `_sprdetail_table.html` partial is loaded via `hx-get` on page load and on `refreshSprDetailList` custom event.
*   **HTMX for Autocomplete:** `SupplierAutocompleteView` provides dynamic suggestions as the user types, using `hx-get` and `hx-target` to update a suggestion div.
*   **HTMX for Conditional UI:** `AccHeadDropdownView` and `WoDeptToggleView` are endpoints for dynamic UI updates based on radio button selections, ensuring correct field visibility and enabled/disabled states.
*   **Alpine.js for Modals:** The `hidden` class on the modal div and the `on click add .is-active to #modal` and `on click remove .is-active from #modal` attributes are examples of Alpine.js (or htmx's `_` extension for simpler cases) managing the modal's open/close state.
*   **DataTables:** The `_sprdetail_table.html` partial initializes DataTables with client-side searching, sorting, and pagination for an efficient data display experience.
*   **DRY Templates:** All component templates (`list.html`, `form.html`, `confirm_delete.html`, `_sprdetail_table.html`) extend a common `core/base.html` (not included here) to ensure consistent layout, styling, and CDN imports (jQuery, DataTables, HTMX, Alpine.js, Tailwind CSS).

This detailed plan and code provide a clear, actionable roadmap for automatically migrating your ASP.NET `SPR_Edit_Details_Item` functionality to a modern Django application, focusing on maintainability, performance, and a delightful user experience.