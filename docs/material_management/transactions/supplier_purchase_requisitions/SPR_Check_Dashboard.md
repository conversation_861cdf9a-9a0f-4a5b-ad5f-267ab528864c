## ASP.NET to Django Conversion Script: SPR Check Dashboard

This modernization plan outlines the conversion of your ASP.NET `SPR_Check_Dashboard.aspx` application to a modern Django-based solution. Given the minimal code provided in your ASP.NET files, we will make reasonable assumptions about the underlying data and typical dashboard functionality to provide a comprehensive, automated migration strategy.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- **NEVER** include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code (`SPR_Check_Dashboard.aspx` and its code-behind) is very minimal and does not explicitly define database interactions or controls. Therefore, we must infer the database schema based on the page name, "SPR_Check_Dashboard," which suggests a dashboard for "SPR Checks" (likely referring to "Supplier Performance Review" or similar in a Material Management context).

**Inferred Database Schema:**

*   **Table Name:** `SPR_Checks`
*   **Columns:**
    *   `id` (Primary Key, integer, auto-incremented) - *Django's default PK*
    *   `spr_number` (string/varchar) - A unique identifier for each SPR check.
    *   `check_date` (date) - The date the check was performed.
    *   `status` (string/varchar) - The current status of the check (e.g., 'Pending', 'Completed', 'Rejected').
    *   `checked_by` (string/varchar) - The name of the person who performed the check.
    *   `notes` (text) - Additional notes related to the check.

#### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
No specific CRUD (Create, Read, Update, Delete) operations are evident from the provided ASP.NET code. The `Page_Load` method is empty, and there are no ASP.NET controls that would typically trigger data operations (like `GridView`, `SqlDataSource`, `Button` click handlers with data commands).

For a complete and modern dashboard solution, we will implement all standard CRUD operations in Django:
*   **Read (R):** Displaying a list of SPR Checks in a table (DataTables).
*   **Create (C):** Adding new SPR Checks via a form.
*   **Update (U):** Editing existing SPR Checks via a form.
*   **Delete (D):** Removing SPR Checks after confirmation.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The provided ASP.NET `.aspx` file contains only `asp:Content` tags and a script reference, with no explicit UI controls (like `GridView`, `TextBox`, `Button`). The inclusion of `loadingNotifier.js` suggests a visual indicator during asynchronous operations, which HTMX and Alpine.js can easily handle.

**Inferred Django UI Components:**

*   **Dashboard View:** A main page displaying a list of "SPR Checks" using a DataTables-powered HTML table for efficient search, sort, and pagination.
*   **Add/Edit Form:** A dynamic form, loaded via HTMX into a modal, for creating and updating "SPR Check" records.
*   **Delete Confirmation:** A dynamic confirmation dialog, loaded via HTMX into a modal, for deleting "SPR Check" records.
*   **Dynamic Interactions:** All form submissions, table refreshes, and modal interactions will be powered by HTMX, with Alpine.js managing modal visibility.

#### Step 4: Generate Django Code

We will structure the Django application within a new app called `sprcheck`.

##### 4.1 Models (`sprcheck/models.py`)

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `SprCheck` will represent the `SPR_Checks` table. We will use `managed = False` and `db_table = 'SPR_Checks'` to ensure it maps directly to your existing database table. We will also include a simple business logic method `is_completed` demonstrating the "fat model" approach.

```python
from django.db import models

class SprCheck(models.Model):
    # Django automatically creates an 'id' primary key if not specified
    spr_number = models.CharField(
        db_column='spr_number',
        max_length=50,
        unique=True,
        verbose_name='SPR Number'
    )
    check_date = models.DateField(
        db_column='check_date',
        verbose_name='Check Date'
    )
    STATUS_CHOICES = [
        ('P', 'Pending'),
        ('C', 'Completed'),
        ('R', 'Rejected'),
    ]
    status = models.CharField(
        db_column='status',
        max_length=1,
        choices=STATUS_CHOICES,
        default='P',
        verbose_name='Status'
    )
    checked_by = models.CharField(
        db_column='checked_by',
        max_length=100,
        verbose_name='Checked By'
    )
    notes = models.TextField(
        db_column='notes',
        blank=True,
        null=True,
        verbose_name='Notes'
    )

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'SPR_Checks' # Your existing table name
        verbose_name = 'SPR Check'
        verbose_name_plural = 'SPR Checks'
        ordering = ['-check_date', 'spr_number'] # Default ordering for lists

    def __str__(self):
        return f"{self.spr_number} - {self.get_status_display()} ({self.check_date})"

    def is_completed(self):
        """
        Business logic: Check if the SPR check status is 'Completed'.
        """
        return self.status == 'C'

    def update_status(self, new_status):
        """
        Business logic: Update the status of the SPR check.
        """
        if new_status in [choice[0] for choice in self.STATUS_CHOICES]:
            self.status = new_status
            self.save()
            return True
        return False
```

##### 4.2 Forms (`sprcheck/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**
We will create a `ModelForm` for the `SprCheck` model, including all fields and applying Tailwind CSS classes via widgets for consistent styling.

```python
from django import forms
from .models import SprCheck

class SprCheckForm(forms.ModelForm):
    class Meta:
        model = SprCheck
        fields = ['spr_number', 'check_date', 'status', 'checked_by', 'notes']
        widgets = {
            'spr_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'check_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'checked_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'notes': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        
    # No custom validation needed for these simple fields, but this is where it would go.
    # def clean_spr_number(self):
    #     spr_number = self.cleaned_data['spr_number']
    #     # Example validation: ensure SPR number format
    #     if not spr_number.startswith('SPR-'):
    #         raise forms.ValidationError("SPR Number must start with 'SPR-'.")
    #     return spr_number
```

##### 4.3 Views (`sprcheck/views.py`)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept "thin" (5-15 lines) by delegating business logic and complex processing to the models or utility functions. They primarily handle HTTP requests, render templates, and manage form processing. HTMX-specific headers are used for dynamic partial updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse

from .models import SprCheck
from .forms import SprCheckForm

class SprCheckListView(ListView):
    """
    Displays the main dashboard page for SPR Checks.
    The actual table content is loaded via HTMX by SprCheckTablePartialView.
    """
    model = SprCheck
    template_name = 'sprcheck/sprcheck_list.html'
    context_object_name = 'sprchecks'

class SprCheckTablePartialView(ListView):
    """
    Returns only the HTML table fragment for SPR Checks, used by HTMX to refresh the list.
    """
    model = SprCheck
    template_name = 'sprcheck/_sprcheck_table.html'
    context_object_name = 'sprchecks'

class SprCheckCreateView(CreateView):
    """
    Handles creation of new SPR Check records.
    Renders a form and processes its submission.
    Returns HTMX-specific response for successful creation.
    """
    model = SprCheck
    form_class = SprCheckForm
    template_name = 'sprcheck/_sprcheck_form.html' # This is a partial template for modal
    success_url = reverse_lazy('sprcheck_list') # Fallback, not used for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'SPR Check added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger client-side event
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshSprCheckList'}
            )
        return response

class SprCheckUpdateView(UpdateView):
    """
    Handles updating existing SPR Check records.
    Renders a pre-filled form and processes its submission.
    Returns HTMX-specific response for successful update.
    """
    model = SprCheck
    form_class = SprCheckForm
    template_name = 'sprcheck/_sprcheck_form.html' # This is a partial template for modal
    context_object_name = 'sprcheck'
    success_url = reverse_lazy('sprcheck_list') # Fallback, not used for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'SPR Check updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger client-side event
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshSprCheckList'}
            )
        return response

class SprCheckDeleteView(DeleteView):
    """
    Handles deletion of SPR Check records.
    Renders a confirmation dialog and processes deletion.
    Returns HTMX-specific response for successful deletion.
    """
    model = SprCheck
    template_name = 'sprcheck/_sprcheck_confirm_delete.html' # This is a partial template for modal
    context_object_name = 'sprcheck'
    success_url = reverse_lazy('sprcheck_list') # Fallback, not used for HTMX

    def delete(self, request, *args, **kwargs):
        # Business logic can be added here or in model's delete method (not overridden here for simplicity)
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'SPR Check deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger client-side event
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshSprCheckList'}
            )
        return response
```

##### 4.4 Templates (`sprcheck/templates/sprcheck/`)

**Task:** Create templates for each view.

**Instructions:**
Templates will be created for the main list view and partials for the table, form, and delete confirmation, leveraging HTMX and Alpine.js for dynamic interactions. `core/base.html` is assumed to exist and provide common structure, including CDN links for DataTables, HTMX, Alpine.js, and Tailwind CSS.

**`sprcheck/sprcheck_list.html`** (Main Dashboard View)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-900">SPR Checks Dashboard</h2>
        <button 
            class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out transform hover:scale-105"
            hx-get="{% url 'sprcheck_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New SPR Check
        </button>
    </div>
    
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div id="sprcheckTable-container"
             hx-trigger="load, refreshSprCheckList from:body"
             hx-get="{% url 'sprcheck_table' %}"
             hx-swap="innerHTML"
             class="p-6">
            <!-- Loading indicator for initial load/refresh -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
                <p class="mt-4 text-lg text-gray-600">Loading SPR Check data...</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit) and confirmation (Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full p-0 overflow-hidden">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init is usually done globally in base.html if x-data is used there
    // This block can be used for any specific JavaScript for this page
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            // After modal content is loaded, ensure the modal is visible via Alpine.js
            const modal = document.getElementById('modal');
            if (modal) {
                modal._x_dataStack[0].showModal = true;
            }
        }
    });

    // Handle HTMX triggers to close modal if form submission is successful
    document.body.addEventListener('refreshSprCheckList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal._x_dataStack[0].showModal = false; // Close modal using Alpine.js
        }
    });

    // For messages
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'messages' && event.detail.xhr.status === 200) {
            // Optional: Auto-hide messages after a few seconds
            setTimeout(() => {
                const messagesDiv = document.getElementById('messages');
                if (messagesDiv) messagesDiv.innerHTML = ''; // Clear messages
            }, 5000);
        }
    });
</script>
{% endblock %}
```

**`sprcheck/templates/sprcheck/_sprcheck_table.html`** (Partial for DataTables)

```html
<table id="sprcheckTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR Number</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Checked By</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in sprchecks %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-indigo-600">{{ obj.spr_number }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.check_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if obj.status == 'C' %}bg-green-100 text-green-800
                    {% elif obj.status == 'P' %}bg-yellow-100 text-yellow-800
                    {% elif obj.status == 'R' %}bg-red-100 text-red-800
                    {% endif %}">
                    {{ obj.get_status_display }}
                </span>
            </td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.checked_by }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                    class="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 ease-in-out mr-3"
                    title="Edit"
                    hx-get="{% url 'sprcheck_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <svg class="w-5 h-5 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.38-2.828-2.828z"></path></svg>
                </button>
                <button 
                    class="text-red-600 hover:text-red-900 transition-colors duration-200 ease-in-out"
                    title="Delete"
                    hx-get="{% url 'sprcheck_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <svg class="w-5 h-5 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No SPR Checks found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables after HTMX loads the table.
// Ensure jQuery and DataTables CDN are loaded in base.html.
$(document).ready(function() {
    $('#sprcheckTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers",
        "language": {
            "search": "Filter:"
        }
    });
});
</script>
```

**`sprcheck/templates/sprcheck/_sprcheck_form.html`** (Partial for Add/Edit Forms)

```html
<div class="p-8">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} SPR Check</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="mt-1 text-sm text-red-600">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <span id="form-spinner" class="htmx-indicator mr-2 inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                Save SPR Check
            </button>
        </div>
    </form>
</div>
```

**`sprcheck/templates/sprcheck/_sprcheck_confirm_delete.html`** (Partial for Delete Confirmation)

```html
<div class="p-8">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the SPR Check "<strong>{{ sprcheck.spr_number }}</strong>"?
        This action cannot be undone.
    </p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            hx-delete="{% url 'sprcheck_delete' sprcheck.pk %}"
            hx-swap="none"
            hx-confirm="This will permanently delete the record. Are you absolutely sure?">
            Delete
        </button>
    </div>
</div>
```

##### 4.5 URLs (`sprcheck/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
URL patterns are defined for each view, including the partial view used by HTMX to refresh the table. Consistent naming conventions are followed.

```python
from django.urls import path
from .views import (
    SprCheckListView,
    SprCheckTablePartialView,
    SprCheckCreateView,
    SprCheckUpdateView,
    SprCheckDeleteView
)

urlpatterns = [
    # Main dashboard page
    path('sprcheck/', SprCheckListView.as_view(), name='sprcheck_list'),

    # HTMX partial for refreshing the DataTables content
    path('sprcheck/table/', SprCheckTablePartialView.as_view(), name='sprcheck_table'),

    # CRUD operations, rendered in modals via HTMX
    path('sprcheck/add/', SprCheckCreateView.as_view(), name='sprcheck_add'),
    path('sprcheck/edit/<int:pk>/', SprCheckUpdateView.as_view(), name='sprcheck_edit'),
    path('sprcheck/delete/<int:pk>/', SprCheckDeleteView.as_view(), name='sprcheck_delete'),
]
```

##### 4.6 Tests (`sprcheck/tests.py`)

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for the `SprCheck` model and integration tests for all CRUD views are provided to ensure functionality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
import datetime

from .models import SprCheck
from .forms import SprCheckForm

class SprCheckModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test instance for all tests in this class
        SprCheck.objects.create(
            spr_number='SPR-2023-001',
            check_date='2023-01-15',
            status='P',
            checked_by='John Doe',
            notes='Initial review.'
        )
        SprCheck.objects.create(
            spr_number='SPR-2023-002',
            check_date='2023-01-20',
            status='C',
            checked_by='Jane Smith',
            notes='Completed without issues.'
        )
  
    def test_sprcheck_creation(self):
        obj = SprCheck.objects.get(spr_number='SPR-2023-001')
        self.assertEqual(obj.check_date, datetime.date(2023, 1, 15))
        self.assertEqual(obj.status, 'P')
        self.assertEqual(obj.checked_by, 'John Doe')
        self.assertEqual(obj.notes, 'Initial review.')
        
    def test_spr_number_label(self):
        obj = SprCheck.objects.get(spr_number='SPR-2023-001')
        field_label = obj._meta.get_field('spr_number').verbose_name
        self.assertEqual(field_label, 'SPR Number')
        
    def test_unique_spr_number(self):
        with self.assertRaises(IntegrityError):
            SprCheck.objects.create(
                spr_number='SPR-2023-001', # Duplicate
                check_date='2023-02-01',
                status='P',
                checked_by='Duplicate Tester'
            )

    def test_str_representation(self):
        obj = SprCheck.objects.get(spr_number='SPR-2023-001')
        self.assertEqual(str(obj), 'SPR-2023-001 - Pending (2023-01-15)')

    def test_is_completed_method(self):
        obj1 = SprCheck.objects.get(spr_number='SPR-2023-001') # Pending
        obj2 = SprCheck.objects.get(spr_number='SPR-2023-002') # Completed
        self.assertFalse(obj1.is_completed())
        self.assertTrue(obj2.is_completed())

    def test_update_status_method(self):
        obj = SprCheck.objects.get(spr_number='SPR-2023-001')
        self.assertTrue(obj.update_status('C'))
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'C')
        self.assertTrue(obj.is_completed())
        
        # Test invalid status
        self.assertFalse(obj.update_status('X'))
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'C') # Should not change

class SprCheckViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create test data for each test method to ensure isolation
        self.sprcheck1 = SprCheck.objects.create(
            spr_number='SPR-VIEW-001',
            check_date='2023-03-01',
            status='P',
            checked_by='Alice'
        )
        self.sprcheck2 = SprCheck.objects.create(
            spr_number='SPR-VIEW-002',
            check_date='2023-03-05',
            status='C',
            checked_by='Bob'
        )
    
    def test_list_view(self):
        response = self.client.get(reverse('sprcheck_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sprcheck/sprcheck_list.html')
        self.assertContains(response, 'SPR Checks Dashboard')
        # Check if the table container is present, which will be filled by HTMX
        self.assertContains(response, '<div id="sprcheckTable-container"')

    def test_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sprcheck_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sprcheck/_sprcheck_table.html')
        self.assertTrue('sprchecks' in response.context)
        self.assertContains(response, self.sprcheck1.spr_number)
        self.assertContains(response, self.sprcheck2.spr_number)
        self.assertContains(response, 'SPR Number') # Check table headers
        self.assertContains(response, '<table id="sprcheckTable"') # Verify DataTable element
        
    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sprcheck_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sprcheck/_sprcheck_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add SPR Check') # Check form title
        
    def test_create_view_post(self):
        # HTMX request
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'spr_number': 'SPR-NEW-003',
            'check_date': '2024-04-01',
            'status': 'P',
            'checked_by': 'Charlie',
            'notes': 'New check via form.'
        }
        response = self.client.post(reverse('sprcheck_add'), data, **headers)
        
        # Expect 204 No Content for HTMX form submission
        self.assertEqual(response.status_code, 204)
        # Verify HTMX-Trigger header
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSprCheckList')
        # Verify object was created
        self.assertTrue(SprCheck.objects.filter(spr_number='SPR-NEW-003').exists())
        self.assertEqual(SprCheck.objects.count(), 3) # Existing 2 + new 1

    def test_create_view_post_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'spr_number': 'SPR-VIEW-001', # Duplicate SPR number
            'check_date': '2024-04-01',
            'status': 'P',
            'checked_by': 'Charlie',
        }
        response = self.client.post(reverse('sprcheck_add'), data, **headers)
        # Expect 200 OK with form errors for HTMX requests
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sprcheck/_sprcheck_form.html')
        self.assertContains(response, 'already exists') # Check for error message
        self.assertEqual(SprCheck.objects.count(), 2) # No new object created

    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sprcheck_edit', args=[self.sprcheck1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sprcheck/_sprcheck_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit SPR Check')
        self.assertContains(response, self.sprcheck1.spr_number) # Check pre-filled data
        
    def test_update_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'spr_number': self.sprcheck1.spr_number, # Keep same
            'check_date': '2023-03-01',
            'status': 'C', # Change status
            'checked_by': 'Alice Updated',
            'notes': 'Notes updated.'
        }
        response = self.client.post(reverse('sprcheck_edit', args=[self.sprcheck1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSprCheckList')
        
        self.sprcheck1.refresh_from_db()
        self.assertEqual(self.sprcheck1.status, 'C')
        self.assertEqual(self.sprcheck1.checked_by, 'Alice Updated')

    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sprcheck_delete', args=[self.sprcheck1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sprcheck/_sprcheck_confirm_delete.html')
        self.assertTrue('sprcheck' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.sprcheck1.spr_number)

    def test_delete_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('sprcheck_delete', args=[self.sprcheck1.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSprCheckList')
        self.assertFalse(SprCheck.objects.filter(pk=self.sprcheck1.pk).exists())
        self.assertEqual(SprCheck.objects.count(), 1) # One less object
```

#### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The integration is already demonstrated within the generated Django templates and views:

*   **HTMX for Dynamic Updates:**
    *   The main `sprcheck_list.html` uses `hx-get="{% url 'sprcheck_table' %}"` with `hx-trigger="load, refreshSprCheckList from:body"` to dynamically load and refresh the DataTables content without a full page reload.
    *   "Add," "Edit," and "Delete" buttons use `hx-get` to fetch partial HTML (forms, confirmation dialogs) into a modal (`#modalContent`).
    *   Form submissions (Create/Update) use `hx-post` and `hx-swap="none"`. Upon successful submission, the views return a `204 No Content` HTTP status and include an `HX-Trigger` header (`refreshSprCheckList`). This tells HTMX to dispatch a custom event (`refreshSprCheckList`) on the `body`, which in turn triggers the `sprcheckTable-container` to reload its content.
    *   Delete operations use `hx-delete` with `hx-swap="none"` and similarly trigger a list refresh.
    *   Loading indicators are shown using `hx-indicator` attribute (e.g., `#form-spinner`).

*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses `x-data="{ showModal: false }"` and `x-show="showModal"` to control its visibility.
    *   Buttons to open the modal (Add, Edit, Delete) use `_ = "on click add .is-active to #modal"` (hyperscript syntax) or Alpine.js direct manipulation to control the modal's `showModal` state upon HTMX content loading.
    *   The `htmx:afterSwap` event listener in `sprcheck_list.html` ensures the Alpine.js `showModal` variable is set to `true` when modal content is successfully loaded, making the modal visible.
    *   The `refreshSprCheckList` event listener closes the modal using Alpine.js (`modal._x_dataStack[0].showModal = false;`).

*   **DataTables for List Views:**
    *   The `_sprcheck_table.html` partial contains a `<table>` with the ID `sprcheckTable`.
    *   A `<script>` block within this partial initializes DataTables on this table using `$(document).ready(function() { $('#sprcheckTable').DataTable(); });`. This ensures DataTables is re-initialized whenever the table content is refreshed via HTMX.

---

### Final Notes

This comprehensive plan provides a clear, actionable path to migrate your ASP.NET `SPR_Check_Dashboard` to a modern Django application, focusing on automation-ready steps and best practices. By adhering to the principles of "fat models, thin views," leveraging HTMX and Alpine.js for dynamic interfaces, and utilizing DataTables for efficient data presentation, your new Django application will be robust, maintainable, and provide a superior user experience. Remember to replace any remaining placeholders like `[APP_NAME]` if your Django app name differs (here, we used `sprcheck`).