This comprehensive Django modernization plan addresses the migration of the provided ASP.NET `PO_SPR_ItemSelect.aspx` page and its C# code-behind. The approach prioritizes Django 5.0+ best practices, including fat models, thin views, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for data presentation. The plan is structured to be actionable and understandable for both technical and non-technical stakeholders, focusing on automation-driven migration strategies.

---

## ASP.NET to Django Conversion Script: PO_SPR_ItemSelect

This document outlines the systematic conversion of your ASP.NET `PO_SPR_ItemSelect.aspx` module to a modern Django-based solution. Our focus is on leveraging AI-assisted automation to transform your existing application into a maintainable, high-performance, and scalable Django system.

### Business Value of Django Modernization:

Transitioning to Django offers significant business benefits:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET, simplifying maintenance and reducing reliance on outdated technologies.
2.  **Enhanced Scalability:** Django's robust architecture and Python's ecosystem provide better horizontal and vertical scaling capabilities.
3.  **Improved Developer Productivity:** Django's "batteries-included" philosophy and clear conventions accelerate new feature development and reduce time-to-market.
4.  **Modern User Experience:** Integration with HTMX and Alpine.js delivers dynamic, fast, and responsive user interfaces without complex JavaScript frameworks.
5.  **Lower Operational Costs:** Python's widespread adoption and active community reduce specialized hiring needs and support costs.
6.  **Future-Proofing:** Adopting a popular, actively maintained open-source framework ensures long-term viability and access to continuous innovation.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The core functionality of the `PO_SPR_ItemSelect.aspx` page is to facilitate the "Add" of a new item to a Purchase Order (PO) in a temporary staging table. This table is `tblMM_SPR_PO_Temp`. The page also heavily relies on data from various other master and transactional tables to pre-populate information and perform validations.

**Identified Primary Table & Columns for `tblMM_SPR_PO_Temp`:**

*   **Table Name:** `tblMM_SPR_PO_Temp`
*   **Columns (inferred from `INSERT` statement):**
    *   `CompId` (Integer)
    *   `SessionId` (String, mapping to Django user's session ID or username)
    *   `SPRNo` (String)
    *   `SPRId` (Integer, references `tblMM_SPR_Details.Id`)
    *   `Qty` (Decimal/Double)
    *   `Rate` (Decimal/Double)
    *   `Discount` (Decimal/Double)
    *   `AddDesc` (String)
    *   `PF` (Integer, Foreign Key to `tblPacking_Master.Id`)
    *   `VAT` (Integer, Foreign Key to `tblVAT_Master.Id`)
    *   `ExST` (Integer, Foreign Key to `tblExciseser_Master.Id`)
    *   `DelDate` (Date)
    *   `BudgetCode` (Integer, Foreign Key to `tblMIS_BudgetCode.Id`)

**Auxiliary Tables (for lookups and pre-population):**
The following tables are crucial for retrieving and displaying related data:

*   `tblMM_SPR_Master`
*   `tblMM_SPR_Details`
*   `tblMIS_BudgetCode`
*   `tblDG_Item_Master`
*   `Unit_Master`
*   `AccHead`
*   `BusinessGroup`
*   `tblPacking_Master`
*   `tblExciseser_Master`
*   `tblVAT_Master`
*   `tblMM_PO_Master`
*   `tblMM_PO_Details`
*   `tblMM_Rate_Register`
*   `tblMM_RateLockUnLock_Master`

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic from the ASP.NET code-behind.

**Instructions:**
The ASP.NET page primarily performs a "Create" operation for a temporary PO item, heavily supported by "Read" operations and complex business validations.

*   **Read Operations (Data Pre-population):**
    *   Retrieves comprehensive details about the Stock Purchase Request (SPR) item (SPR Number, Quantity, Work Order, Department, Item Code, Item Description, Unit of Measure, Rate, Discount, Account Head, Delivery Date) from various master and transactional tables (`tblMM_SPR_Master`, `tblMM_SPR_Details`, `tblDG_Item_Master`, `Unit_Master`, `AccHead`, `BusinessGroup`).
    *   Calculates the *remaining allowable quantity* for the PO item by deducting quantities already added to temporary POs (`tblMM_SPR_PO_Temp`) and confirmed POs (`tblMM_PO_Details`) from the original SPR quantity.
    *   Populates dropdowns with master data for Packing & Forwarding (`tblPacking_Master`), Excise/Service Tax (`tblExciseser_Master`), and VAT (`tblVAT_Master`).
    *   Dynamically populates the Budget Code dropdown (`tblMIS_BudgetCode`) based on whether a Work Order (WO) is associated with the SPR item.
    *   Fetches minimum rate information from `tblMM_Rate_Register` and rate override permissions from `tblMM_RateLockUnLock_Master` for sophisticated rate validation.

*   **Create Operation (Add Item):**
    *   When the "Add" button is clicked, a new record is inserted into `tblMM_SPR_PO_Temp`. This is the single "CRUD" operation implemented directly on this page.

*   **Validation Logic:**
    *   **Quantity:** Input must be numeric, non-empty, and should not exceed the calculated remaining SPR quantity.
    *   **Rate & Discount:** Must be numeric, non-empty, and positive. A complex business rule validates the entered net rate against a "minimum rate" from `tblMM_Rate_Register`. If the entered rate is higher, it checks a "rate lock/unlock" setting in `tblMM_RateLockUnLock_Master` to determine if such an override is permitted. If not, an error message is displayed.
    *   **Delivery Date:** Must be a valid date in `dd-MM-yyyy` format and cannot be a past date.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles in the user interface.

**Instructions:**
The ASP.NET page functions as a detailed input form for a single PO item.

*   **Read-only Data Display:** `asp:Label` controls are used to display pre-populated SPR details such as SPR No, SPR Qty, WO No, BG Group, Item Code, Item Description, Ac Head, and UOM. These will be rendered as static text within Django templates.
*   **User Input Fields:**
    *   `asp:TextBox` controls (`txtQty`, `txtRate`, `txtDiscount`, `txtAddDesc`, `txtDelDate`) allow users to input quantity, rate, discount, additional description, and delivery date. These will be mapped to appropriate Django form fields (e.g., `DecimalField`, `CharField`, `DateField`).
    *   `asp:DropDownList` controls (`DrpBudgetCode`, `DDLPF`, `DDLExcies`, `DDLVat`) provide selection options. These will be converted to Django `ModelChoiceField` or `ChoiceField` widgets.
*   **Action Buttons:** `asp:Button` controls (`btnProcide` for "Add", `btnCancel` for "Cancel") trigger the form submission and navigation. These will be mapped to standard HTML `<button>` elements with HTMX attributes to enable dynamic, AJAX-like interactions.
*   **Client-Side Enhancements:** The `AjaxControlToolkit:CalendarExtender` for the date input will be replaced with a modern JavaScript date picker library (e.g., Flatpickr) integrated via Alpine.js. Other legacy JavaScript (`PopUpMsg.js`, `loadingNotifier.js`) will be entirely replaced by HTMX's inherent capabilities for loading states and event-driven updates, coupled with Alpine.js for localized UI state management. Client-side validation from `RequiredFieldValidator` and `RegularExpressionValidator` will be primarily managed by Django's robust form validation framework on the server, with HTMX providing instant feedback by re-rendering form sections upon validation errors.

### Step 4: Generate Django Code

The Django application for this module will be named `material_management`.

#### 4.1 Models (material_management/models.py)

We will define Django models for `tblMM_SPR_PO_Temp` (renamed to `SprPoTempItem` for Django conventions) and essential auxiliary tables required for foreign key relationships and data lookups. For a complete system, all auxiliary tables would need full definition.

```python
# material_management/models.py

from django.db import models
from django.utils import timezone
from decimal import Decimal

# Placeholder models for lookup tables, mapped to existing database tables.
# In a full migration, these would be expanded with all relevant fields.
class PackingMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.terms

class ExciseMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Tax Term'
        verbose_name_plural = 'Excise/Service Tax Terms'

    def __str__(self):
        return self.terms

class VatMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'

    def __str__(self):
        return self.terms

class BudgetCode(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    # The original ASP.NET code concatenates Symbol and WONo, this suggests
    # BudgetCode might have a relation to WONo or be dynamically generated for display.

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return self.symbol

class SprMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

class SprDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(SprMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    item_id = models.IntegerField(db_column='ItemId') # Represents FK to ItemMaster
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5)
    won_o = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept_id = models.IntegerField(db_column='DeptId') # Represents FK to BusinessGroup
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=5)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)
    ah_id = models.IntegerField(db_column='AHId') # Represents FK to AccHead
    del_date = models.DateField(db_column='DelDate')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"SPR Detail for {self.spr_no} - Item ID: {self.item_id}"
    
    # Business logic to retrieve related display data (Fat Model principle)
    @property
    def item_code(self):
        try:
            item = ItemMaster.objects.get(id=self.item_id)
            return item.item_code
        except ItemMaster.DoesNotExist:
            return "N/A"

    @property
    def item_description(self):
        try:
            item = ItemMaster.objects.get(id=self.item_id)
            return item.manf_desc
        except ItemMaster.DoesNotExist:
            return "N/A"

    @property
    def uom_symbol(self):
        try:
            item = ItemMaster.objects.get(id=self.item_id)
            unit = UnitMaster.objects.get(id=item.uom_basic)
            return unit.symbol
        except (ItemMaster.DoesNotExist, UnitMaster.DoesNotExist):
            return "N/A"

    @property
    def ac_head_name(self):
        try:
            acc_head = AccHead.objects.get(id=self.ah_id)
            return f"[{acc_head.symbol}] {acc_head.description}"
        except AccHead.DoesNotExist:
            return "N/A"

    @property
    def department_name(self):
        try:
            dept = BusinessGroup.objects.get(id=self.dept_id)
            return f"[{dept.symbol}] {dept.name}"
        except BusinessGroup.DoesNotExist:
            return "N/A"

    def get_remaining_qty(self, session_id, comp_id):
        """
        Calculates the remaining quantity for this SPR detail item,
        considering quantities already added to temporary POs and confirmed POs.
        """
        spr_qty = self.qty

        # Quantity already added to temporary PO (by current session/user)
        temp_po_qty = SprPoTempItem.objects.filter(
            spr_no=self.spr_no, spr_id=self.id, session_id=session_id
        ).aggregate(models.Sum('qty'))['qty__sum'] or Decimal('0.00000')

        # Quantity already in confirmed POs linked to this SPR detail
        confirmed_po_qty = PoDetail.objects.filter(
            spr_id=self.id, m_id__comp_id=comp_id # m_id is FK to PoMaster
        ).aggregate(models.Sum('qty'))['qty__sum'] or Decimal('0.00000')

        remaining = spr_qty - confirmed_po_qty - temp_po_qty
        return max(Decimal('0.00000'), remaining) # Ensure non-negative result


class ItemMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.IntegerField(db_column='UOMBasic') # FK to Unit_Master
    comp_id = models.IntegerField(db_column='CId') # Original had CId for CompId
    ah_id = models.IntegerField(db_column='AHId') # FK to AccHead

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class UnitMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class AccHead(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}"
    
    @property
    def get_formatted_name(self):
        return f"[{self.symbol}] {self.description}"

class BusinessGroup(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='Name', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return f"[{self.symbol}] {self.name}"

class PoMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    # Add other fields as needed for PoMaster if used elsewhere

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

class PoDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(PoMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    spr_id = models.IntegerField(db_column='SPRId') # References SprDetail.id
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5)
    # Add other fields as needed for PoDetail

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

class RateRegister(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_id = models.IntegerField(db_column='ItemId') # References ItemMaster.id
    comp_id = models.IntegerField(db_column='CompId')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=5)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'
    
    @property
    def discounted_rate(self):
        """Calculates the net rate after discount."""
        return self.rate - (self.rate * (self.discount / Decimal('100.00')))

class RateLockUnlockMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_id = models.IntegerField(db_column='ItemId') # References ItemMaster.id
    comp_id = models.IntegerField(db_column='CompId')
    lock_unlock = models.BooleanField(db_column='LockUnlock') # 1 for locked/unlocked
    type = models.CharField(db_column='Type', max_length=10) # '2' for certain type

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock/Unlock Master'
        verbose_name_plural = 'Rate Lock/Unlock Masters'

class SprPoTempItem(models.Model):
    """
    Model representing an item temporarily added to a Purchase Order,
    corresponding to tblMM_SPR_PO_Temp.
    """
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Stores user session identifier
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    spr_id = models.IntegerField(db_column='SPRId') # References SprDetail.id
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=5)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)
    add_desc = models.TextField(db_column='AddDesc', blank=True, null=True)
    pf = models.ForeignKey(PackingMaster, on_delete=models.DO_NOTHING, db_column='PF', related_name='spr_po_temp_items')
    vat = models.ForeignKey(VatMaster, on_delete=models.DO_NOTHING, db_column='VAT', related_name='spr_po_temp_items')
    ex_st = models.ForeignKey(ExciseMaster, on_delete=models.DO_NOTHING, db_column='ExST', related_name='spr_po_temp_items')
    del_date = models.DateField(db_column='DelDate')
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', related_name='spr_po_temp_items', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_PO_Temp'
        verbose_name = 'Temporary PO Item'
        verbose_name_plural = 'Temporary PO Items'

    def __str__(self):
        return f"Temp PO Item for SPR {self.spr_no}, SPR ID: {self.spr_id}"

    @classmethod
    def can_proceed_with_rate(cls, item_id, comp_id, entered_rate, entered_discount):
        """
        Implements the complex rate validation logic from the ASP.NET code-behind.
        Determines if the entered rate (after discount) is acceptable based on:
        1. Minimum rate from RateRegister.
        2. Rate lock/unlock settings.
        Returns True if the rate is acceptable, False otherwise.
        """
        calculated_entered_rate = entered_rate - (entered_rate * (entered_discount / Decimal('100.00')))

        # Find the minimum discounted rate from the Rate Register for the item
        min_rate_entry = RateRegister.objects.filter(
            item_id=item_id, comp_id=comp_id
        ).annotate(
            discounted_rate_reg=models.functions.Cast(
                models.ExpressionWrapper(
                    models.F('rate') - (models.F('rate') * models.F('discount') / Decimal('100.00')),
                    output_field=models.DecimalField(max_digits=18, decimal_places=5)
                ),
                models.DecimalField(max_digits=18, decimal_places=2)
            )
        ).order_by('discounted_rate_reg').first()
        
        min_acceptable_rate = min_rate_entry.discounted_rate_reg if min_rate_entry else Decimal('0.00')

        if min_acceptable_rate > 0:
            if calculated_entered_rate < min_acceptable_rate:
                # Entered rate is lower or equal to the minimum acceptable rate, which is fine
                return True
            else: # Entered rate is higher than the minimum acceptable rate
                # Check if a higher rate is allowed by the RateLockUnLockMaster
                rate_lock_status = RateLockUnlockMaster.objects.filter(
                    item_id=item_id, comp_id=comp_id, lock_unlock=True, type='2' # '1' for lock_unlock means unlocked/allowed, '2' is a specific type
                ).exists()
                return rate_lock_status # True if allowed, False if not
        else:
            # If no minimum rate is defined (or it's zero), any positive rate is acceptable
            return True

```

#### 4.2 Forms (material_management/forms.py)

A Django `ModelForm` will be used to handle the input fields for `SprPoTempItem`. Custom validation methods will enforce the complex business rules identified.

```python
# material_management/forms.py

from django import forms
from .models import SprPoTempItem, PackingMaster, VatMaster, ExciseMaster, BudgetCode, SprDetail
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal

class SprPoTempItemForm(forms.ModelForm):
    # Form fields explicitly defined to provide custom widgets and validation rules
    qty = forms.DecimalField(
        label="PO Qty",
        max_digits=18,
        decimal_places=5,
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        min_value=Decimal('0.00001'), # Quantity must be positive
        required=True
    )
    rate = forms.DecimalField(
        label="Rate",
        max_digits=18,
        decimal_places=5,
        # Initially read-only, can be changed if logic permits
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'readonly': 'true'}),
        min_value=Decimal('0.00001'), # Rate must be positive
        required=True
    )
    discount = forms.DecimalField(
        label="Discount",
        max_digits=18,
        decimal_places=2,
        initial=Decimal('0.00'), # Default value as in original
        widget=forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        min_value=Decimal('0.00'),
        required=True
    )
    add_desc = forms.CharField(
        label="Additional Desc",
        required=False,
        widget=forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-14', 'rows': 3})
    )
    pf = forms.ModelChoiceField(
        queryset=PackingMaster.objects.all(),
        label="P & F",
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )
    ex_st = forms.ModelChoiceField(
        queryset=ExciseMaster.objects.all(),
        label="Excise / Service Tax",
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )
    vat = forms.ModelChoiceField(
        queryset=VatMaster.objects.all(),
        label="VAT",
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )
    del_date = forms.DateField(
        label="Del. Date",
        widget=forms.DateInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'data-controller': 'flatpickr'}),
        input_formats=['%d-%m-%Y'], # Matches original ASP.NET date format
        required=True
    )
    budget_code = forms.ModelChoiceField(
        queryset=BudgetCode.objects.all(),
        label="Budget Code",
        widget=forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False, # Original was optional, visible/hidden based on WO
        empty_label="Select Budget Code"
    )

    class Meta:
        model = SprPoTempItem
        # Define fields that are directly handled by the form's input
        fields = [
            'qty', 'rate', 'discount', 'add_desc', 'pf', 'ex_st', 'vat', 'del_date', 'budget_code'
        ]
        # Fields like comp_id, session_id, spr_no, spr_id are set in the view
        # or pre-filled based on context rather than direct user input.

    def __init__(self, *args, **kwargs):
        self.spr_detail = kwargs.pop('spr_detail', None)
        self.comp_id = kwargs.pop('comp_id', None)
        self.request = kwargs.pop('request', None) # Pass request for current user/session
        super().__init__(*args, **kwargs)

        # Logic to handle Budget Code visibility based on WONo in SPR Details
        if self.spr_detail and not self.spr_detail.won_o:
            self.fields['budget_code'].widget = forms.HiddenInput()
            self.fields['budget_code'].required = False
            self.fields['budget_code'].label = '' # Hide label if field is hidden
            
        # Set initial rate/discount from SPR detail if not provided via form submission
        if self.spr_detail and not self.is_bound:
            self.fields['rate'].initial = self.spr_detail.rate.quantize(Decimal('0.00001'))
            self.fields['discount'].initial = self.spr_detail.discount.quantize(Decimal('0.00'))

    def clean_qty(self):
        """Custom validation for quantity, checking against remaining SPR quantity."""
        qty = self.cleaned_data['qty']
        if self.spr_detail and self.comp_id and self.request and self.request.user.is_authenticated:
            session_id = self.request.user.username # Assuming username is used as SessionId
            remaining_qty = self.spr_detail.get_remaining_qty(session_id, self.comp_id)
            if qty > remaining_qty:
                raise ValidationError(f"Entered quantity exceeds remaining SPR quantity ({remaining_qty:.5f}).")
        return qty

    def clean_del_date(self):
        """Custom validation for delivery date, ensuring it's not in the past."""
        del_date = self.cleaned_data['del_date']
        if del_date < timezone.now().date():
            raise ValidationError("Delivery Date cannot be in the past!")
        return del_date
    
    def clean(self):
        """
        Overall form cleaning, including the complex rate validation.
        This method runs after individual field clean methods.
        """
        cleaned_data = super().clean()
        qty = cleaned_data.get('qty')
        rate = cleaned_data.get('rate')
        discount = cleaned_data.get('discount')
        spr_detail = self.spr_detail
        comp_id = self.comp_id

        # Only perform rate validation if all necessary fields are present and valid
        if spr_detail and comp_id and rate is not None and discount is not None:
            item_id = spr_detail.item_id
            
            if not SprPoTempItem.can_proceed_with_rate(item_id, comp_id, rate, discount):
                # Add a non-field error to indicate rate is not acceptable
                self.add_error(None, "Entered rate is not acceptable!")
        return cleaned_data

```

#### 4.3 Views (material_management/views.py)

This page is a specific "Add Item" form rather than a generic CRUD for `SprPoTempItem`. A `FormView` is suitable for handling its display and submission. The view will be thin, delegating complex calculations and validations to the model and form.

```python
# material_management/views.py

from django.views.generic import FormView, TemplateView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.db import transaction
from decimal import Decimal

from .models import (
    SprDetail, PackingMaster, VatMaster, ExciseMaster, BudgetCode,
    ItemMaster, UnitMaster, AccHead, BusinessGroup,
    SprPoTempItem
)
from .forms import SprPoTempItemForm


class PoSprItemSelectView(FormView):
    """
    View for selecting an SPR item and adding it as a temporary PO item.
    Handles data pre-population, form rendering, and submission with complex validations.
    """
    template_name = 'material_management/spr_po_item_select/form.html'
    form_class = SprPoTempItemForm
    success_url = reverse_lazy('material_management:po_spr_item_grid') # Redirects to the grid page

    def dispatch(self, request, *args, **kwargs):
        """
        Initial data retrieval for SPR details and related entities.
        Ensures necessary query parameters are present.
        """
        self.spr_id = self.request.GET.get('sprid')
        self.code = self.request.GET.get('Code')
        self.spr_no_from_qs = self.request.GET.get('sprno') # Used for lblSprno.Text in original
        
        # Company ID and Financial Year ID are typically stored in the session
        # or derived from the authenticated user's profile.
        # For demonstration, default to 1. Replace with actual session/user data.
        self.comp_id = self.request.session.get('compid', 1) 
        self.fin_year_id = self.request.session.get('finyear', 1)
        self.session_username = self.request.user.username if self.request.user.is_authenticated else 'anonymous_user'

        if not self.spr_id:
            raise Http404("SPR ID is missing from the query parameters.")

        try:
            # Retrieve the SPR Detail and related master data
            self.spr_detail = get_object_or_404(
                SprDetail.objects.select_related('m_id'), # Pre-fetch SprMaster
                id=self.spr_id, m_id__comp_id=self.comp_id
            )
            self.item_master = get_object_or_404(ItemMaster, id=self.spr_detail.item_id, comp_id=self.comp_id)
            self.unit_master = get_object_or_404(UnitMaster, id=self.item_master.uom_basic)
            self.acc_head = get_object_or_404(AccHead, id=self.spr_detail.ah_id)
            
            # Department/Business Group lookup
            if self.spr_detail.dept_id > 0:
                self.department = get_object_or_404(BusinessGroup, id=self.spr_detail.dept_id)
                self.budget_code_symbol = self.department.symbol
            else:
                self.department = None
                self.budget_code_symbol = "NA"

            # Dynamically populate Budget Code dropdown based on WONo
            if self.spr_detail.won_o:
                # The original SQL used Symbol + WONo. This means we filter BudgetCode
                # based on some relation to the SPR's WONo.
                # Assuming BudgetCode.symbol can be matched or part of it.
                # In a real scenario, this would be a more precise query based on DB schema.
                self.budget_codes_for_dropdown = BudgetCode.objects.filter(
                    # This is a simplification; actual logic may involve joining with other tables
                    # or a specific naming convention for symbols.
                    symbol__startswith=self.spr_detail.won_o # Example approximation
                ).annotate(
                    display_name=models.functions.Concat(
                        models.F('symbol'), models.Value(self.spr_detail.won_o), output_field=models.CharField()
                    )
                ).order_by('display_name')
            else:
                self.budget_codes_for_dropdown = BudgetCode.objects.none()

        except Http404 as e:
            messages.error(request, f"Required SPR or related item data not found: {e}")
            # Redirect to a safe page or show error if data is missing
            return redirect(reverse_lazy('material_management:po_spr_item_grid')) # Or an error page
        except Exception as e:
            messages.error(request, f"An unexpected error occurred: {e}")
            return redirect(reverse_lazy('material_management:po_spr_item_grid')) # Or an error page
            
        return super().dispatch(request, *args, **kwargs)

    def get_initial(self):
        """Provides initial data for the form fields."""
        initial = super().get_initial()
        
        remaining_qty = self.spr_detail.get_remaining_qty(self.session_username, self.comp_id)

        # Set initial form values, ensuring correct decimal precision
        initial['qty'] = remaining_qty.quantize(Decimal('0.00001'))
        initial['rate'] = self.spr_detail.rate.quantize(Decimal('0.00001'))
        initial['discount'] = self.spr_detail.discount.quantize(Decimal('0.00'))
        initial['del_date'] = self.spr_detail.del_date
        initial['add_desc'] = "" # Default empty
        initial['budget_code'] = None # Default empty; will be updated by form if WO exists and options are present

        return initial

    def get_context_data(self, **kwargs):
        """Adds extra context data required for template rendering."""
        context = super().get_context_data(**kwargs)
        # Pass SPR details and other display data to the template
        context['spr_detail'] = self.spr_detail
        context['item_master'] = self.item_master
        context['unit_master'] = self.unit_master
        context['acc_head'] = self.acc_head
        context['department'] = self.department
        context['budget_code_symbol'] = self.budget_code_symbol
        context['spr_qty_display'] = self.spr_detail.qty.quantize(Decimal('0.000')) # Format to N3
        context['spr_no_display'] = self.spr_detail.spr_no

        # Pass dropdown options to template
        context['packing_options'] = PackingMaster.objects.all()
        context['excise_options'] = ExciseMaster.objects.all()
        context['vat_options'] = VatMaster.objects.all()
        context['budget_code_options'] = self.budget_codes_for_dropdown # Dynamically filtered queryset

        # URL for the Rate Register print page
        context['rate_register_url'] = reverse_lazy(
            'material_management:rate_register_single_item_print', 
            args=[self.spr_detail.item_id, self.comp_id]
        )
        return context

    def get_form_kwargs(self):
        """Passes extra arguments to the form's __init__ method."""
        kwargs = super().get_form_kwargs()
        kwargs['spr_detail'] = self.spr_detail
        kwargs['comp_id'] = self.comp_id
        kwargs['request'] = self.request # Pass request for current user/session_id in form clean method
        return kwargs

    def form_valid(self, form):
        """
        Handles successful form submission.
        Creates and saves the new SprPoTempItem instance.
        """
        with transaction.atomic():
            new_temp_item = form.save(commit=False)
            new_temp_item.comp_id = self.comp_id
            new_temp_item.session_id = self.session_username
            new_temp_item.spr_no = self.spr_detail.spr_no
            new_temp_item.spr_id = self.spr_detail.id
            
            new_temp_item.save()

            messages.success(self.request, 'Item added to temporary PO successfully!')
            
            # HTMX specific response for dynamic redirection and event triggering
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204, # No Content response for HTMX success
                    headers={
                        'HX-Trigger': 'refreshPO_SPR_ItemGrid', # Custom event to tell the grid to refresh
                        'HX-Redirect': self.success_url # Full page redirect after HTMX operation
                    }
                )
            return super().form_valid(form) # Standard redirect for non-HTMX requests

    def form_invalid(self, form):
        """
        Handles unsuccessful form submission.
        Re-renders the form with validation errors.
        """
        # For HTMX requests, re-render the current template to display errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        
        # For non-HTMX requests, add an error message and let FormView handle re-rendering
        messages.error(self.request, 'Please correct the errors in the form.')
        return super().form_invalid(form)


# Placeholder for the target grid view (PO_SPR_ItemGrid.aspx equivalent)
class PoSprItemGridView(TemplateView):
    """
    Placeholder view for the PO SPR Item Grid.
    This would typically display a DataTable of `SprPoTempItem` entries
    for the current session/SPR, or actual confirmed PO items.
    """
    template_name = 'material_management/po_spr_item_grid/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetch temporary PO items related to the current session/user if needed
        # For demonstration, a placeholder list is provided.
        user_session_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous_user'
        comp_id = self.request.session.get('compid', 1)

        # In a real scenario, you'd fetch items for the current session/SPR
        # context['items'] = SprPoTempItem.objects.filter(session_id=user_session_id, comp_id=comp_id).order_by('-id')
        context['items'] = [] # Placeholder for actual data retrieval
        return context

# Placeholder view for the Rate Register print page
class RateRegisterSingleItemPrintView(TemplateView):
    """
    Placeholder for the rate register print report page.
    """
    template_name = 'material_management/reports/rate_register_single_item_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_id = self.kwargs['item_id']
        comp_id = self.kwargs['comp_id']
        context['item_id'] = item_id
        context['comp_id'] = comp_id
        # In a real application, this would query tblMM_Rate_Register
        # and display the rate history for the given item and company.
        return context

```

#### 4.4 Templates

**Directory Structure:**
`material_management/templates/material_management/spr_po_item_select/form.html`
`material_management/templates/material_management/po_spr_item_grid/list.html`
`material_management/templates/material_management/po_spr_item_grid/_table.html` (for HTMX partial)
`material_management/templates/material_management/reports/rate_register_single_item_print.html` (placeholder)

**`material_management/spr_po_item_select/form.html` (Main form template):**

```html
{% extends 'core/base.html' %}
{% load static %} {# For Rupee.JPG image #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Add Item to Purchase Order</h2>

        {# Display Django messages #}
        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            {# Left Column: SPR Details #}
            <div class="md:col-span-1 border-r md:pr-6">
                <table class="w-full text-sm text-gray-700">
                    <tr>
                        <td class="w-1/3 py-1 font-medium">SPR No</td>
                        <td class="py-1">: <span class="font-semibold">{{ spr_no_display }}</span></td>
                    </tr>
                    <tr>
                        <td class="w-1/3 py-1 font-medium">SPR Qty</td>
                        <td class="py-1">: <span class="font-semibold">{{ spr_qty_display }}</span></td>
                    </tr>
                    <tr>
                        <td class="w-1/3 py-1 font-medium">WO No</td>
                        <td class="py-1">: <span class="font-semibold">{{ spr_detail.won_o|default:"NA" }}</span></td>
                    </tr>
                    <tr>
                        <td class="w-1/3 py-1 font-medium">BG Group</td>
                        <td class="py-1">: <span class="font-semibold">{{ department.name|default:"NA" }}</span></td>
                    </tr>
                    <tr>
                        <td class="w-1/3 py-1 font-medium">Item Code</td>
                        <td class="py-1">: <span class="font-semibold">{{ item_master.item_code|default:"N/A" }}</span></td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-1 font-medium">Item Description</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-1">
                            <textarea class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-600"
                                      rows="5" readonly>{{ item_master.manf_desc|default:"N/A" }}</textarea>
                        </td>
                    </tr>
                </table>
            </div>

            {# Middle Column: PO Item Details Input Form #}
            <div class="md:col-span-1 border-r md:px-6">
                {# HTMX form submission: on success, triggers an event and redirects. On error, re-renders itself. #}
                <form method="post" hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="body">
                    {% csrf_token %}
                    <table class="w-full text-sm text-gray-700">
                        <tr>
                            <td class="w-1/3 py-1 font-medium">Ac Head</td>
                            <td class="py-1">: <span class="font-semibold">{{ acc_head.get_formatted_name|default:"N/A" }}</span></td>
                        </tr>
                        <tr>
                            <td class="w-1/3 py-1 font-medium">{{ form.qty.label }}</td>
                            <td class="py-1">: {{ form.qty }}
                                {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td class="w-1/3 py-1 font-medium">UOM</td>
                            <td class="py-1">: <span class="font-semibold">{{ unit_master.symbol|default:"N/A" }}</span></td>
                        </tr>
                        <tr>
                            <td class="w-1/3 py-1 font-medium">{{ form.rate.label }}</td>
                            <td class="py-1">: {{ form.rate }}
                                {# Link to Rate Register print page #}
                                &nbsp;<a href="{{ rate_register_url }}" target="_blank" class="inline-block align-middle ml-2"><img src="{% static 'images/Rupee.JPG' %}" alt="Rate Register" class="w-5 h-5"></a>
                                {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td class="w-1/3 py-1 font-medium">{{ form.discount.label }}</td>
                            <td class="py-1">: {{ form.discount }} %
                                {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td class="w-1/3 py-1 font-medium">{{ form.budget_code.label }}</td>
                            <td class="py-1">
                                {# Conditionally display dropdown or static text based on WONo #}
                                {% if spr_detail.won_o %}
                                    {# Render dropdown with dynamically filtered options #}
                                    <select name="{{ form.budget_code.name }}" id="{{ form.budget_code.id_for_label }}" class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        {% if not form.budget_code.field.required %}<option value="">Select Budget Code</option>{% endif %}
                                        {% for option in budget_code_options %}
                                            <option value="{{ option.id }}" {% if form.budget_code.value == option.id %}selected{% endif %}>{{ option.display_name }}</option>
                                        {% endfor %}
                                    </select>
                                {% else %}
                                    <span class="font-semibold">{{ budget_code_symbol }}</span>
                                    {# If dropdown is hidden, ensure its value is still passed (e.g., hidden input or handle in view) #}
                                    {{ form.budget_code }} {# Renders as hidden input due to form_class logic #}
                                {% endif %}
                                {% if form.budget_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.budget_code.errors }}</p>{% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td class="w-1/3 py-1 font-medium">{{ form.add_desc.label }}</td>
                            <td class="py-1">{{ form.add_desc }}
                                {% if form.add_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_desc.errors }}</p>{% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                {# Non-field errors for rate validation or other global issues #}
                                {% if form.non_field_errors %}<div class="text-red-500 text-xs mt-1">{{ form.non_field_errors }}</div>{% endif %}
                            </td>
                        </tr>
                    </table>
            </div>

            {# Right Column: Other Details & Actions #}
            <div class="md:col-span-1 md:pl-6">
                <table class="w-full text-sm text-gray-700">
                    <tr>
                        <td colspan="2" class="py-1 font-medium">{{ form.pf.label }}</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-1">{{ form.pf }}
                            {% if form.pf.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf.errors }}</p>{% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-1 font-medium">{{ form.ex_st.label }}</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-1">{{ form.ex_st }}
                            {% if form.ex_st.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ex_st.errors }}</p>{% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-1 font-medium">{{ form.vat.label }}</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-1">{{ form.vat }}
                            {% if form.vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vat.errors }}</p>{% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td class="w-1/3 py-1 font-medium">{{ form.del_date.label }}</td>
                        <td class="py-1">: {{ form.del_date }}
                            {% if form.del_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.del_date.errors }}</p>{% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" class="py-4 text-right">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 mr-3">
                                Add
                            </button>
                            <button type="button" onclick="window.location.href='{% url 'material_management:po_spr_item_grid' %}'" class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-5 rounded focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50">
                                Cancel
                            </button>
                        </td>
                    </tr>
                </table>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Include Flatpickr for the date input #}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('flatpickr', () => ({
            init() {
                flatpickr(this.$el, {
                    dateFormat: "d-m-Y", // Matching the original ASP.NET format
                    altInput: true,
                    altFormat: "d-m-Y",
                    minDate: "today", // Prevents selection of past dates
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`material_management/po_spr_item_grid/list.html` (Target page after successful Add):**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Purchase Order Item Grid</h2>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <p class="text-gray-600 mb-4">This page displays a comprehensive list of temporary PO items or confirmed PO items, dynamically loaded using HTMX and presented with DataTables for efficient searching, sorting, and pagination.</p>
        <p class="text-gray-600 mb-4">It can be automatically refreshed when new items are added from the 'Add Item to PO' page, providing a seamless user experience.</p>
        
        <div id="sprPoItemGrid-container"
             hx-trigger="load, refreshPO_SPR_ItemGrid from:body" {# Triggers on page load and on custom event #}
             hx-get="{% url 'material_management:po_spr_item_grid_table' %}" {# Fetches the table content via HTMX #}
             hx-swap="innerHTML">
            <!-- Loading indicator while DataTable content is fetched -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading PO Item Grid...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components specific to this list view, if any
    });
</script>
{% endblock %}

```

**`material_management/po_spr_item_grid/_table.html` (Partial for DataTables content):**

```html
<table id="sprPoItemGridTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR Item ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount (%)</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Del. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in items %} {# 'items' is the queryset passed from PoSprItemGridView's context #}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.spr_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.spr_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.qty }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.rate }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.discount }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.del_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                {# Add action buttons (e.g., edit, delete) for temp items here if needed #}
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-1 px-2 rounded text-xs opacity-50 cursor-not-allowed">Edit</button>
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-1 px-2 rounded text-xs opacity-50 cursor-not-allowed">Delete</button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No temporary PO items found for this session.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is properly initialized, destroying any existing instance first
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#sprPoItemGridTable')) {
            $('#sprPoItemGridTable').DataTable().destroy();
        }
        $('#sprPoItemGridTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "paging": true,
            "info": true,
            "columnDefs": [
                { "orderable": false, "targets": -1 } // Disable sorting on the Actions column
            ]
        });
    });
</script>
```

**`material_management/reports/rate_register_single_item_print.html` (Placeholder for report page):**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Rate Register for Item: {{ item_id }} (Comp ID: {{ comp_id }})</h2>
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <p class="text-gray-600 mb-4">This page would present a detailed report of historical rates for the selected item, retrieved from `tblMM_Rate_Register`.</p>
        <p class="text-gray-600">The report would include dates, rates, discounts, and any other relevant pricing information to aid in PO item rate decisions.</p>
        {# Add actual report content here, e.g., a DataTables display of rates #}
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (material_management/urls.py)

Django URL patterns define the routes to your views.

```python
# material_management/urls.py

from django.urls import path
from .views import (
    PoSprItemSelectView, 
    PoSprItemGridView, 
    RateRegisterSingleItemPrintView
)

app_name = 'material_management' # Defines the application namespace for URLs

urlpatterns = [
    # URL for the primary "Add Item to PO" page (equivalent to PO_SPR_ItemSelect.aspx)
    # Query parameters (sprid, Code, sprno) will be accessed via request.GET
    path('po-spr-item-select/', PoSprItemSelectView.as_view(), name='po_spr_item_select'),

    # URL for the PO SPR Item Grid page (equivalent to PO_SPR_ItemGrid.aspx)
    path('po-spr-item-grid/', PoSprItemGridView.as_view(), name='po_spr_item_grid'),
    
    # HTMX endpoint to fetch only the DataTable content for the grid, allowing dynamic refreshes
    path('po-spr-item-grid/table/', PoSprItemGridView.as_view(template_name='material_management/po_spr_item_grid/_table.html'), name='po_spr_item_grid_table'),

    # URL for the Rate Register print report, accepting item_id and comp_id as path parameters
    path('reports/rate-register-single-item-print/<int:item_id>/<int:comp_id>/', 
         RateRegisterSingleItemPrintView.as_view(), name='rate_register_single_item_print'),

    # Note: No separate generic CRUD URLs for SprPoTempItem are defined here
    # as the page's primary function is a specialized "Add" operation, not generic list/edit/delete.
    # If a full CRUD for SprPoTempItem was required, additional URLs would be added.
]

```

#### 4.6 Tests (material_management/tests.py)

Comprehensive tests ensure the reliability and correctness of the migrated application. This includes unit tests for model business logic and integration tests for views and form submissions, covering both successful and erroneous scenarios.

```python
# material_management/tests.py

from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from decimal import Decimal
from django.contrib.messages import get_messages
from django.contrib.auth import get_user_model

# Import all models to ensure they are testable
from .models import (
    PackingMaster, ExciseMaster, VatMaster, BudgetCode,
    SprMaster, SprDetail, ItemMaster, UnitMaster, AccHead, BusinessGroup,
    PoMaster, PoDetail, RateRegister, RateLockUnlockMaster,
    SprPoTempItem
)

# Helper function to create common related objects required for tests
def create_common_related_objects():
    """Ensures necessary foreign key related objects exist for testing."""
    packing = PackingMaster.objects.create(id=1, terms='Standard Packing')
    excise = ExciseMaster.objects.create(id=1, terms='GST 18%')
    vat = VatMaster.objects.create(id=1, terms='VAT 5%')
    budget_code = BudgetCode.objects.create(id=101, symbol='BUDGET101_WO123') # Ensure symbol matches WO for test
    unit = UnitMaster.objects.create(id=1, symbol='NOS')
    acc_head = AccHead.objects.create(id=1, symbol='AH1', description='General Expenses')
    business_group = BusinessGroup.objects.create(id=1, symbol='BG1', name='Business Group One')
    
    # Create SPR Master and Detail
    spr_master = SprMaster.objects.create(id=1, spr_no='SPR001', comp_id=1)
    item_master = ItemMaster.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Description for SPR001', uom_basic=1, comp_id=1, ah_id=1)
    
    spr_detail_data = {
        'id': 1,
        'm_id': spr_master,
        'spr_no': 'SPR001',
        'item_id': 1,
        'qty': Decimal('100.000'),
        'won_o': 'WO123', # Example Work Order
        'dept_id': 1,
        'rate': Decimal('50.000'),
        'discount': Decimal('5.00'),
        'ah_id': 1,
        'del_date': date.today() + timedelta(days=10) # Future date
    }
    spr_detail = SprDetail.objects.create(**spr_detail_data)

    return {
        'packing': packing, 'excise': excise, 'vat': vat, 'budget_code': budget_code,
        'unit': unit, 'acc_head': acc_head, 'business_group': business_group,
        'spr_master': spr_master, 'item_master': item_master, 'spr_detail': spr_detail
    }

class SprPoTempItemModelTest(TestCase):
    """
    Unit tests for the SprPoTempItem model and its associated business logic methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create common related objects once for all model tests
        cls.common_data = create_common_related_objects()
        cls.spr_detail = cls.common_data['spr_detail']
        cls.item_master = cls.common_data['item_master']
        cls.comp_id = cls.spr_detail.m_id.comp_id
        cls.session_username = 'testuser_model' # Unique username for this test context

        # Create a temp item for remaining quantity calculation test
        SprPoTempItem.objects.create(
            comp_id=cls.comp_id,
            session_id=cls.session_username,
            spr_no=cls.spr_detail.spr_no,
            spr_id=cls.spr_detail.id,
            qty=Decimal('10.000'),
            rate=Decimal('45.00'),
            discount=Decimal('0.00'),
            pf=cls.common_data['packing'],
            vat=cls.common_data['vat'],
            ex_st=cls.common_data['excise'],
            del_date=date.today() + timedelta(days=5),
            budget_code=cls.common_data['budget_code']
        )
        
        # Create a confirmed PO item for remaining quantity calculation test
        po_master = PoMaster.objects.create(id=1, comp_id=cls.comp_id)
        PoDetail.objects.create(
            id=1,
            m_id=po_master,
            spr_id=cls.spr_detail.id,
            qty=Decimal('20.000')
        )
        
        # Create a Rate Register entry (minimum acceptable rate)
        RateRegister.objects.create(
            item_id=cls.item_master.id,
            comp_id=cls.comp_id,
            rate=Decimal('40.00'),
            discount=Decimal('0.00') # Discounted rate will be 40.00
        )
        # Create a Rate Lock/Unlock entry (allows higher rates)
        RateLockUnlockMaster.objects.create(
            item_id=cls.item_master.id,
            comp_id=cls.comp_id,
            lock_unlock=True, # '1' means allowed/unlocked in original context
            type='2' # Specific type '2' as in original
        )

    def test_spr_detail_remaining_qty_calculation(self):
        """Tests the get_remaining_qty method of SprDetail."""
        # Initial SPR Qty: 100.000
        # Temp PO Qty (by testuser_model): 10.000
        # Confirmed PO Qty: 20.000
        # Expected remaining: 100 - 10 - 20 = 70.000
        remaining = self.spr_detail.get_remaining_qty(self.session_username, self.comp_id)
        self.assertEqual(remaining, Decimal('70.00000')) # Ensure precision matches

        # Test case where remaining qty is 0 or negative
        temp_spr_detail = SprDetail.objects.create(
            m_id=self.common_data['spr_master'], spr_no='SPR002', item_id=self.item_master.id,
            qty=Decimal('25.000'), won_o='WO124', dept_id=1, rate=Decimal('10.00'), discount=Decimal('0.00'), ah_id=1, del_date=date.today() + timedelta(days=5)
        )
        SprPoTempItem.objects.create(comp_id=self.comp_id, session_id=self.session_username, spr_no='SPR002', spr_id=temp_spr_detail.id, qty=Decimal('10.000'), rate=Decimal('5.00'), discount=Decimal('0.00'), pf=self.common_data['packing'], vat=self.common_data['vat'], ex_st=self.common_data['excise'], del_date=date.today() + timedelta(days=1), budget_code=self.common_data['budget_code'])
        PoDetail.objects.create(m_id=PoMaster.objects.get(id=1), spr_id=temp_spr_detail.id, qty=Decimal('15.000'))
        
        remaining_zero = temp_spr_detail.get_remaining_qty(self.session_username, self.comp_id)
        self.assertEqual(remaining_zero, Decimal('0.00000')) # Should not go negative

    def test_can_proceed_with_rate_acceptable_lower_or_equal(self):
        """Tests rate validation when entered rate is acceptable (lower or equal to min)."""
        # Entered rate 35, Discount 0. Min rate 40. This should be acceptable.
        can_proceed = SprPoTempItem.can_proceed_with_rate(
            self.item_master.id, self.comp_id, Decimal('35.00'), Decimal('0.00')
        )
        self.assertTrue(can_proceed)

    def test_can_proceed_with_rate_higher_allowed_by_lock(self):
        """Tests rate validation when entered rate is higher but allowed by lock settings."""
        # Entered rate 45, Discount 0. Min rate 40. Higher but allowed by 'lock_unlock=True'.
        can_proceed = SprPoTempItem.can_proceed_with_rate(
            self.item_master.id, self.comp_id, Decimal('45.00'), Decimal('0.00')
        )
        self.assertTrue(can_proceed)

    def test_can_proceed_with_rate_higher_not_allowed(self):
        """Tests rate validation when entered rate is higher and not allowed (no lock entry or lock_unlock=False)."""
        # Delete the rate lock/unlock entry to simulate not allowed
        RateLockUnlockMaster.objects.filter(item_id=self.item_master.id, comp_id=self.comp_id).delete()
        # Entered rate 45, Discount 0. Min rate 40. Higher and now not allowed.
        can_proceed = SprPoTempItem.can_proceed_with_rate(
            self.item_master.id, self.comp_id, Decimal('45.00'), Decimal('0.00')
        )
        self.assertFalse(can_proceed)

    def test_can_proceed_with_rate_no_min_rate_entry(self):
        """Tests rate validation when no minimum rate is defined in RateRegister."""
        # Delete all rate register entries and lock/unlock entries
        RateRegister.objects.all().delete()
        RateLockUnlockMaster.objects.all().delete()
        # Any positive rate should be acceptable
        can_proceed = SprPoTempItem.can_proceed_with_rate(
            self.item_master.id, self.comp_id, Decimal('100.00'), Decimal('0.00')
        )
        self.assertTrue(can_proceed)

    def test_model_properties_from_spr_detail(self):
        """Tests properties on SprDetail that fetch related data."""
        self.assertEqual(self.spr_detail.item_code, self.item_master.item_code)
        self.assertEqual(self.spr_detail.item_description, self.item_master.manf_desc)
        self.assertEqual(self.spr_detail.uom_symbol, self.common_data['unit'].symbol)
        self.assertEqual(self.spr_detail.ac_head_name, self.common_data['acc_head'].get_formatted_name)
        self.assertEqual(self.spr_detail.department_name, self.common_data['business_group'].get_formatted_name)


class PoSprItemSelectViewTest(TestCase):
    """
    Integration tests for the PoSprItemSelectView, covering GET and POST requests,
    including validation, form submission, and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common data required for views tests
        cls.common_data = create_common_related_objects()
        cls.spr_detail = cls.common_data['spr_detail']
        cls.comp_id = cls.spr_detail.m_id.comp_id
        
        # Setup test user for session data (required for login and session_username)
        User = get_user_model()
        cls.user = User.objects.create_user(username='testuser', password='password123')
        
        # Create rate register entry to enable rate validation (min rate is 40)
        RateRegister.objects.create(
            item_id=cls.common_data['item_master'].id,
            comp_id=cls.comp_id,
            rate=Decimal('40.00'),
            discount=Decimal('0.00')
        )
        # Create rate lock/unlock entry (allows higher rates)
        RateLockUnlockMaster.objects.create(
            item_id=cls.common_data['item_master'].id,
            comp_id=cls.comp_id,
            lock_unlock=True,
            type='2'
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123') # Log in the test user
        self.session = self.client.session
        self.session['compid'] = self.comp_id # Set company ID in session
        self.session['finyear'] = 1 # Set financial year in session
        self.session.save() # Save the session changes

        # Construct the URL with required query parameters
        self.url = reverse('material_management:po_spr_item_select') + f'?sprid={self.spr_detail.id}&Code=TEST&sprno={self.spr_detail.spr_no}'

    def test_get_request_success(self):
        """Tests that a GET request to the view renders successfully and pre-populates data."""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_po_item_select/form.html')
        self.assertIn('form', response.context)
        
        # Verify pre-populated values in the form
        self.assertContains(response, f"value=\"{self.spr_detail.rate.quantize(Decimal('0.00001'))}\"")
        self.assertContains(response, f"value=\"{self.spr_detail.discount.quantize(Decimal('0.00'))}\"")
        self.assertContains(response, f"value=\"{(date.today() + timedelta(days=10)).strftime('%d-%m-%Y')}\"")
        
        # Verify display data
        self.assertContains(response, self.spr_detail.spr_no)
        self.assertContains(response, self.spr_detail.item_code)
        self.assertContains(response, self.common_data['unit'].symbol)


    def test_get_request_missing_sprid(self):
        """Tests handling of missing SPR ID in query parameters."""
        response = self.client.get(reverse('material_management:po_spr_item_select'), follow=True)
        self.assertEqual(response.status_code, 200) # Should redirect due to Http404 handling in dispatch
        self.assertRedirects(response, reverse('material_management:po_spr_item_grid'))
        messages = list(get_messages(response.request))
        self.assertEqual(len(messages), 1)
        self.assertTrue("SPR ID is missing" in str(messages[0]))


    def test_post_request_success(self):
        """Tests successful form submission and creation of a new SprPoTempItem."""
        data = {
            'qty': Decimal('10.000'),
            'rate': Decimal('50.000'),  # Higher than min_rate (40), but allowed by lock=True
            'discount': Decimal('0.00'),
            'add_desc': 'Additional description for test',
            'pf': self.common_data['packing'].id,
            'ex_st': self.common_data['excise'].id,
            'vat': self.common_data['vat'].id,
            'del_date': (date.today() + timedelta(days=15)).strftime('%d-%m-%Y'), # Future date
            'budget_code': self.common_data['budget_code'].id, # Since WO exists
        }
        response = self.client.post(self.url, data, follow=True) # Follows the redirect
        
        self.assertEqual(response.status_code, 200) 
        self.assertRedirects(response, reverse('material_management:po_spr_item_grid'))
        
        # Verify that the object was created in the database
        self.assertTrue(SprPoTempItem.objects.filter(spr_id=self.spr_detail.id, qty=Decimal('10.000')).exists())
        
        # Verify success message
        messages = list(get_messages(response.request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item added to temporary PO successfully!')

    def test_post_request_invalid_qty_exceeds_remaining(self):
        """Tests quantity validation when entered quantity exceeds remaining SPR quantity."""
        # Temporarily modify spr_detail qty to trigger validation (assuming initial 100 - 10 - 20 = 70 remaining)
        SprDetail.objects.filter(id=self.spr_detail.id).update(qty=Decimal('25.000')) # Make remaining 25-10-20 = -5 (or 0)
        self.spr_detail.refresh_from_db() # Reload the modified object for accurate remaining calculation

        data = {
            'qty': Decimal('10.000'), # This will exceed actual remaining (which is now 0)
            'rate': Decimal('50.000'),
            'discount': Decimal('0.00'),
            'add_desc': '',
            'pf': self.common_data['packing'].id,
            'ex_st': self.common_data['excise'].id,
            'vat': self.common_data['vat'].id,
            'del_date': (date.today() + timedelta(days=15)).strftime('%d-%m-%Y'),
            'budget_code': self.common_data['budget_code'].id,
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertFalse(SprPoTempItem.objects.filter(spr_id=self.spr_detail.id, qty=Decimal('10.000')).exists())
        self.assertContains(response, "Entered quantity exceeds remaining SPR quantity")
        messages = list(get_messages(response.request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors in the form.')


    def test_post_request_invalid_rate_not_acceptable(self):
        """Tests rate validation when entered rate is higher and not allowed by lock settings."""
        # Remove rate lock/unlock entry, so higher rates are no longer allowed
        RateLockUnlockMaster.objects.filter(item_id=self.common_data['item_master'].id, comp_id=self.comp_id).delete()

        data = {
            'qty': Decimal('10.000'),
            'rate': Decimal('50.000'), # Higher than min_rate (40), and now not allowed
            'discount': Decimal('0.00'),
            'add_desc': '',
            'pf': self.common_data['packing'].id,
            'ex_st': self.common_data['excise'].id,
            'vat': self.common_data['vat'].id,
            'del_date': (date.today() + timedelta(days=15)).strftime('%d-%m-%Y'),
            'budget_code': self.common_data['budget_code'].id,
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertFalse(SprPoTempItem.objects.filter(spr_id=self.spr_detail.id, qty=Decimal('10.000')).exists())
        self.assertContains(response, "Entered rate is not acceptable!") # Check for non-field error
        messages = list(get_messages(response.request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors in the form.')


    def test_post_request_past_delivery_date(self):
        """Tests delivery date validation for past dates."""
        data = {
            'qty': Decimal('10.000'),
            'rate': Decimal('50.000'),
            'discount': Decimal('0.00'),
            'add_desc': '',
            'pf': self.common_data['packing'].id,
            'ex_st': self.common_data['excise'].id,
            'vat': self.common_data['vat'].id,
            'del_date': (date.today() - timedelta(days=1)).strftime('%d-%m-%Y'), # Past date
            'budget_code': self.common_data['budget_code'].id,
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, 200)
        self.assertFalse(SprPoTempItem.objects.filter(spr_id=self.spr_detail.id, qty=Decimal('10.000')).exists())
        self.assertContains(response, "Delivery Date cannot be in the past!") # Check for specific field error
        messages = list(get_messages(response.request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors in the form.')


    def test_post_request_htmx_success(self):
        """Tests successful HTMX form submission with correct HTMX headers."""
        data = {
            'qty': Decimal('10.000'),
            'rate': Decimal('50.000'),
            'discount': Decimal('0.00'),
            'add_desc': 'HTMX test submission',
            'pf': self.common_data['packing'].id,
            'ex_st': self.common_data['excise'].id,
            'vat': self.common_data['vat'].id,
            'del_date': (date.today() + timedelta(days=15)).strftime('%d-%m-%Y'),
            'budget_code': self.common_data['budget_code'].id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'} # Indicate an HTMX request
        response = self.client.post(self.url, data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTTP 204 No Content for HTMX success
        self.assertEqual(response['HX-Trigger'], 'refreshPO_SPR_ItemGrid') # Verify custom event trigger
        self.assertEqual(response['HX-Redirect'], reverse('material_management:po_spr_item_grid')) # Verify full page redirect
        self.assertTrue(SprPoTempItem.objects.filter(spr_id=self.spr_detail.id, qty=Decimal('10.000')).exists())


    def test_post_request_htmx_invalid(self):
        """Tests invalid HTMX form submission and re-rendering of the form with errors."""
        # Invalid quantity to trigger form validation error
        # Assuming original SPR qty 100, 10 temp, 20 confirmed -> 70 remaining
        data = {
            'qty': Decimal('80.000'), # Exceeds remaining qty
            'rate': Decimal('50.000'),
            'discount': Decimal('0.00'),
            'add_desc': '',
            'pf': self.common_data['packing'].id,
            'ex_st': self.common_data['excise'].id,
            'vat': self.common_data['vat'].id,
            'del_date': (date.today() + timedelta(days=15)).strftime('%d-%m-%Y'),
            'budget_code': self.common_data['budget_code'].id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.url, data, **headers)
        
        self.assertEqual(response.status_code, 200) # Renders the form partial with errors
        self.assertContains(response, "Entered quantity exceeds remaining SPR quantity")
        self.assertTemplateUsed(response, 'material_management/spr_po_item_select/form.html')
        
        # Crucially, HX-Trigger or HX-Redirect should NOT be set on invalid HTMX form submission
        self.assertNotIn('HX-Trigger', response)
        self.assertNotIn('HX-Redirect', response)
        self.assertFalse(SprPoTempItem.objects.filter(spr_id=self.spr_detail.id, qty=Decimal('80.000')).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The modernization emphasizes a "hypermedia-driven" approach, minimizing client-side JavaScript frameworks to HTMX and Alpine.js.

*   **HTMX for Form Submission:** The `form.html` template uses `hx-post`, `hx-swap="outerHTML"`, and `hx-target="body"` to submit the form via AJAX. On successful submission, the Django view sends `HX-Trigger` to fire a custom event (`refreshPO_SPR_ItemGrid`) on the `body` and `HX-Redirect` to navigate the browser to the main grid page. If validation fails, the view re-renders the `form.html` template, which HTMX then swaps back into the DOM, displaying the validation errors.
*   **HTMX for Table Refresh:** The `list.html` template for the `po_spr_item_grid` includes a `div` with `hx-get` to load the `_table.html` partial. It uses `hx-trigger="load, refreshPO_SPR_ItemGrid from:body"` to ensure the table is loaded on page initial load and automatically refreshed when the `refreshPO_SPR_ItemGrid` event is triggered by the `po_spr_item_select` view after a successful item addition.
*   **Alpine.js for Date Picker:** The `txtDelDate` input is given a `data-controller="flatpickr"` attribute. An Alpine.js component `flatpickr` is defined in `extra_js` block. This component initializes the Flatpickr date picker library on the input element, providing the user-friendly calendar interface and enforcing `minDate: "today"` for future dates.
*   **DataTables for List Views:** The `_table.html` partial for the grid view includes a `script` block that initializes DataTables on the rendered `<table>`. It includes logic to `destroy()` any existing DataTable instance before re-initializing to handle HTMX swaps gracefully, ensuring proper client-side searching, sorting, and pagination.
*   **Minimal Custom JavaScript:** All dynamic interactions are managed by HTMX, with Alpine.js handling specific UI element behaviors like the date picker. This adheres to the principle of "HTMX-only interactions" and avoids complex custom JavaScript.

---

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names and values derived from the ASP.NET analysis.
*   **DRY Templates:** The `_table.html` partial demonstrates the DRY principle by providing a reusable component for the DataTables content. The `core/base.html` template is assumed to exist and manage global CDN links for HTMX, Alpine.js, and DataTables.
*   **Fat Model, Thin View:** Business logic, such as quantity calculation and complex rate validation, is encapsulated within model methods (`SprDetail.get_remaining_qty`, `SprPoTempItem.can_proceed_with_rate`). Views remain concise, primarily orchestrating the request/response flow and delegating heavy lifting to models and forms.
*   **Comprehensive Tests:** The included unit and integration tests ensure high coverage of the new Django codebase, validating model logic, form validation, and view behavior under various scenarios, including HTMX-driven interactions.
*   **Tailwind CSS:** The provided HTML templates include class attributes consistent with Tailwind CSS, supporting a modern and responsive design without requiring custom CSS rules.