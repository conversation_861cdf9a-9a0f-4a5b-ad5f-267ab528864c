## ASP.NET to Django Conversion Script:

This modernization plan outlines the strategic transition of your ASP.NET application, specifically the `PO_SPR_ItemGrid` module, to a modern Django-based solution. Our approach prioritizes automation, leveraging Django's robust ORM, HTMX, and Alpine.js to deliver a highly efficient and maintainable system. We focus on clear, non-technical language to ensure all stakeholders understand the value and process.

### Business Benefits of Django Modernization:

*   **Cost Reduction:** Moving away from proprietary Microsoft technologies reduces licensing costs and reliance on specialized skill sets.
*   **Enhanced Performance:** Django's optimized ORM and Caching mechanisms, combined with HTMX for partial page updates, will significantly improve application responsiveness and user experience.
*   **Scalability:** Django is inherently scalable, capable of handling increased user loads and data volumes with ease, supporting your business growth.
*   **Improved Maintainability:** Adhering to the "Fat Model, Thin View" paradigm, along with strict separation of concerns, makes the codebase easier to understand, debug, and extend. This reduces future development and maintenance costs.
*   **Modern User Experience:** Integration of HTMX and Alpine.js provides a dynamic, single-page application (SPA)-like feel without the complexity of traditional JavaScript frameworks, leading to happier users.
*   **Future-Proofing:** Adopting an open-source, widely supported framework like Django ensures your application remains relevant and adaptable to future technological advancements.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis of ASP.NET Code:**
The C# code-behind's `LoadSPRData` function interacts with multiple SQL Server tables to assemble the grid data. The primary table being queried for its details is `tblMM_SPR_Details`, which is joined with `tblMM_SPR_Master`. Additional lookups are performed against `AccHead`, `tblHR_OfficeStaff`, `tblDG_Item_Master`, `Unit_Master`, `BusinessGroup`, `tblFinancial_master`. Crucially, there's a check against `tblMM_SPR_PO_Temp` and a sum from `tblMM_PO_Details` to calculate `POQty` and `RemainQty`.

**Identified Tables and Key Fields:**

*   **`tblMM_SPR_Master`:**
    *   `Id` (PK)
    *   `SPRNo` (unique identifier)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `SysDate` (System Date)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff`)
    *   `CompId` (Company ID)
    *   `Authorize` (Status flag, likely boolean)
*   **`tblMM_SPR_Details`:**
    *   `Id` (PK)
    *   `MId` (Foreign Key to `tblMM_SPR_Master.Id`)
    *   `SPRNo` (Redundant, but present for joins)
    *   `SupplierId` (used for filtering)
    *   `WONo` (Work Order No.)
    *   `DeptId` (Foreign Key to `BusinessGroup`)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master`)
    *   `AHId` (Foreign Key to `AccHead`)
    *   `Qty` (SPR Quantity)
    *   `DelDate` (Delivery Date)
*   **`tblMM_PO_Master`:**
    *   `Id` (PK)
    *   `CompId`
*   **`tblMM_PO_Details`:**
    *   `MId` (Foreign Key to `tblMM_PO_Master.Id`)
    *   `SPRId` (Foreign Key to `tblMM_SPR_Details.Id`)
    *   `Qty` (PO Quantity)
*   **`tblMM_SPR_PO_Temp`:**
    *   `CompId`
    *   `SPRNo`
    *   `SPRId` (Foreign Key to `tblMM_SPR_Details.Id`)
*   **`AccHead`:**
    *   `Id` (PK)
    *   `Symbol`
*   **`tblHR_OfficeStaff`:**
    *   `EmpId` (PK)
    *   `EmployeeName`
    *   `CompId`
*   **`tblDG_Item_Master`:**
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc` (Manufacturer Description)
    *   `UOMBasic` (Foreign Key to `Unit_Master`)
    *   `CompId`
*   **`Unit_Master`:**
    *   `Id` (PK)
    *   `Symbol`
*   **`BusinessGroup`:**
    *   `Id` (PK)
    *   `Symbol`
*   **`tblFinancial_master`:**
    *   `FinYearId` (PK)
    *   `FinYear`
    *   `CompId`

### Step 2: Identify Backend Functionality

The ASP.NET page primarily performs a **Read** operation to display a filtered list of SPR (Store Purchase Requisition) items suitable for creating a Purchase Order.
The core logic involves:
*   **Filtering:** Based on `SupplierId`, `FinancialYearId`, `CompanyId`, `Authorized` status, remaining quantity (`RemQty > 0`), and not being present in `tblMM_SPR_PO_Temp`.
*   **Data Aggregation/Calculation:** Calculating `POQty` and `RemainQty` by summing quantities from related PO details and subtracting from the SPR quantity.
*   **Lookups:** Retrieving descriptive names (e.g., Account Head, Department Name, Item Description, UOM) from various lookup tables.
*   **Selection:** The `LinkButton` triggers a "select" command which redirects to `PO_SPR_ItemSelect.aspx` with selected SPR `sprno` and `sprid`. This is not a CRUD operation on the `SprItem` itself but a trigger for a subsequent action (PO creation).

No direct Create, Update, or Delete operations are performed on the displayed `SprItem` records within this specific ASP.NET page.

### Step 3: Infer UI Components

*   **`asp:GridView`:** This maps directly to a **Django template displaying an HTML table**, enhanced with **DataTables.js** for client-side search, sort, and pagination.
*   **`asp:TemplateField` with `asp:Label`:** These are table cells displaying data, which will be rendered as simple `<td>` elements in Django templates.
*   **`asp:TemplateField` with `asp:LinkButton`:** The "Select" `LinkButton` will be replaced by an HTML `<button>` or `<a>` tag utilizing **HTMX** to trigger a `hx-get` or `hx-post` request to a Django view, simulating the redirection or opening a modal for subsequent PO creation.
*   **`EmptyDataTemplate`:** This will be handled by simple Django template `{% if not objects %}` checks.
*   **Client-side JS:** The `loadingNotifier.js` and `yui-datatable.css` suggest client-side enhancements. We will use **HTMX** for dynamic loading and **DataTables.js** for table functionality, along with **Alpine.js** for any simple UI state management (like modals), and **Tailwind CSS** for modern styling.

### Step 4: Generate Django Code

We will create a new Django app, e.g., `material_management`, to house this functionality.

#### 4.1 Models (`material_management/models.py`)

Given the complex data assembly in C#, we'll define lightweight models for all relevant tables, and a main `SprItem` model that intelligently pulls and processes the required data through properties and a custom manager. This adheres to the "Fat Model" principle by centralizing business logic.

```python
from django.db import models
from django.db.models import Sum, F, OuterRef, Subquery
from django.db.models.functions import Coalesce
from django.utils import timezone
from datetime import datetime

# Helper models for related tables, assuming existing database structure
# Set managed=False as they map to existing tables
class FinancialMaster(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class AccHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=250)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', related_name='item_uom_basic')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class SprMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, unique=True)
    fin_year_id = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId', related_name='spr_masters')
    sys_date = models.DateTimeField(db_column='SysDate')
    session_id = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='SessionId', related_name='spr_masters_generated')
    comp_id = models.IntegerField(db_column='CompId')
    authorize = models.BooleanField(db_column='Authorize') # Assuming 1 is true

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

class PoMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

    def __str__(self):
        return f"PO Master {self.id}"

class PoDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming PK here for simplicity, otherwise use unique_together
    m_id = models.ForeignKey(PoMaster, models.DO_NOTHING, db_column='MId', related_name='po_details')
    spr_id = models.IntegerField(db_column='SPRId') # This links to tblMM_SPR_Details.Id
    qty = models.FloatField(db_column='Qty')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Detail {self.id} for SPR {self.spr_id}"


class SprPoTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming PK here for simplicity
    comp_id = models.IntegerField(db_column='CompId')
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    spr_id = models.IntegerField(db_column='SPRId') # Links to tblMM_SPR_Details.Id

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_PO_Temp'
        verbose_name = 'SPR PO Temp'
        verbose_name_plural = 'SPR PO Temps'
        # Add a unique_together constraint if SPRNo and SPRId form a unique key
        # unique_together = (('comp_id', 'spr_no', 'spr_id'),)

    def __str__(self):
        return f"SPR Temp {self.spr_id} - {self.spr_no}"

class SprItemQuerySet(models.QuerySet):
    """
    Custom QuerySet to encapsulate the complex data retrieval and calculation logic
    from the original ASP.NET LoadSPRData function.
    """
    def get_filterable_spr_items(self, comp_id, fin_year_id, sup_code):
        # 1. Base filter on SPR Master and Details
        qs = self.filter(
            supplier_id=sup_code,
            spr_master__authorize=True,
            spr_master__fin_year_id__fin_year_id__lte=fin_year_id, # Use FK's fin_year_id
            spr_master__comp_id=comp_id
        ).select_related(
            'spr_master', 'item', 'ac_head', 'department', 'item__uom_basic',
            'spr_master__session_id', 'spr_master__fin_year_id'
        )

        # 2. Annotate with sum of PO Quantity for each SPR item
        # This subquery replicates: Sum(tblMM_PO_Details.Qty)As TotPoQty
        po_qty_subquery = PoDetail.objects.filter(
            spr_id=OuterRef('id'), # Link PoDetail to current SprItem (tblMM_SPR_Details.Id)
            m_id__comp_id=comp_id # Filter POs by company
        ).values('spr_id').annotate(
            total_qty=Sum('qty')
        ).values('total_qty')[:1] # Take only the first result to avoid multiple rows

        qs = qs.annotate(
            calculated_po_qty=Coalesce(Subquery(po_qty_subquery, output_field=models.FloatField()), 0.0)
        )

        # 3. Annotate with check for existence in tblMM_SPR_PO_Temp
        # This subquery replicates: tblMM_SPR_PO_Temp.Count (HasRows)
        temp_check_subquery = SprPoTemp.objects.filter(
            spr_id=OuterRef('id'),
            spr_no=OuterRef('spr_master__spr_no'),
            comp_id=comp_id
        ).values('pk')[:1]

        qs = qs.annotate(
            is_in_temp=Subquery(temp_check_subquery, output_field=models.IntegerField())
        )

        # 4. Filter based on Remaining Quantity and tblMM_SPR_PO_Temp check
        # RemainQty = Math.Round((SPRQty - PoQty), 5)
        # if (RemQty > 0 && DSCheck.HasRows == false)
        qs = qs.annotate(
            calculated_remain_qty=F('qty') - F('calculated_po_qty')
        ).filter(
            calculated_remain_qty__gt=0,
            is_in_temp__isnull=True # True if not found in temp table
        )

        return qs

class SprItem(models.Model):
    # Directly mapping columns from tblMM_SPR_Details
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(SprMaster, models.DO_NOTHING, db_column='MId', related_name='spr_details')
    spr_no = models.CharField(db_column='SPRNo', max_length=50) # Denormalized from master, but used for joins
    supplier_id = models.CharField(db_column='SupplierId', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept_id = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', related_name='spr_items_by_dept')
    item_id = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', related_name='spr_items')
    ah_id = models.ForeignKey(AccHead, models.DO_NOTHING, db_column='AHId', related_name='spr_items_by_achead')
    qty = models.FloatField(db_column='Qty')
    del_date = models.DateTimeField(db_column='DelDate')

    # Custom manager
    objects = SprItemQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Item'
        verbose_name_plural = 'SPR Items'

    def __str__(self):
        return f"SPR Detail {self.spr_no} - Item {self.item.item_code}"

    # Properties to replicate derived fields from ASP.NET
    @property
    def financial_year(self):
        return self.spr_master.fin_year_id.fin_year if hasattr(self, 'spr_master') and self.spr_master else 'N/A'

    @property
    def request_date(self):
        return self.spr_master.sys_date.strftime('%d/%m/%Y') if hasattr(self, 'spr_master') and self.spr_master else 'N/A'

    @property
    def item_code_display(self):
        # Replicates fun.GetItemCode_PartNo, assuming it's ItemMaster.item_code
        return self.item.item_code if hasattr(self, 'item') and self.item else 'N/A'

    @property
    def purch_description(self):
        return self.item.manf_desc if hasattr(self, 'item') and self.item else 'N/A'

    @property
    def uom_purchase(self):
        return self.item.uom_basic.symbol if hasattr(self, 'item') and self.item and self.item.uom_basic else 'N/A'

    @property
    def ac_head_symbol(self):
        return self.ac_head.symbol if hasattr(self, 'ac_head') and self.ac_head else 'N/A'

    @property
    def delivery_date_formatted(self):
        return self.del_date.strftime('%d/%m/%Y') if self.del_date else 'N/A'

    @property
    def generated_by(self):
        return self.spr_master.session_id.employee_name if hasattr(self, 'spr_master') and self.spr_master and self.spr_master.session_id else 'N/A'

    @property
    def work_order_no_display(self):
        return self.wo_no if self.wo_no else 'NA'

    @property
    def department_name_display(self):
        return self.department.symbol if hasattr(self, 'department') and self.department else 'NA'

    @property
    def po_quantity(self):
        # Access the annotated value if available, otherwise calculate
        if hasattr(self, 'calculated_po_qty'):
            return round(self.calculated_po_qty, 5)
        # Fallback calculation if not annotated (less efficient, mainly for non-list contexts)
        sum_qty = PoDetail.objects.filter(spr_id=self.id).aggregate(total=Sum('qty'))['total']
        return round(sum_qty, 5) if sum_qty is not None else 0.0

    @property
    def remaining_quantity(self):
        # Access the annotated value if available, otherwise calculate
        if hasattr(self, 'calculated_remain_qty'):
            return round(self.calculated_remain_qty, 5)
        # Fallback calculation if not annotated
        return round(self.qty - self.po_quantity, 5)

    @property
    def is_available_for_selection(self):
        # Check against tblMM_SPR_PO_Temp and remaining quantity
        if hasattr(self, 'is_in_temp') and self.is_in_temp is not None:
            return False # Already in temp table
        return self.remaining_quantity > 0
```

#### 4.2 Forms (`material_management/forms.py`)

The original ASP.NET page is for listing and selection, not for creating or updating `SprItem` objects. Therefore, a form for `SprItem` itself is not directly applicable to this specific page's functionality. We will omit a `SprItemForm` for this conversion to focus on the exact functionality being migrated. If "PO_SPR_ItemSelect.aspx" were part of the request, a form for PO creation would be relevant there.

```python
# No form needed for this specific page, as it's a display/selection grid.
# If "PO_SPR_ItemSelect.aspx" were migrated, a form for Purchase Order creation
# would be placed here.
```

#### 4.3 Views (`material_management/views.py`)

We'll use a `ListView` for the main page and a `TemplateView` (or a simple `View` with `render()`) for the HTMX-loaded table partial.

```python
from django.views.generic import ListView, View, TemplateView
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect
from django.contrib import messages
from .models import SprItem, SprPoTemp # Assuming SprPoTemp is relevant for checks

class SprItemListView(ListView):
    """
    Displays the main page containing the SPR Items grid.
    The actual table data will be loaded via HTMX from SprItemTablePartialView.
    """
    model = SprItem
    template_name = 'material_management/spritem/list.html'
    context_object_name = 'spr_items' # Not directly used in template, as table loads via HTMX

    # This view primarily serves the base HTML structure.
    # The actual data fetching happens in SprItemTablePartialView.
    def get_queryset(self):
        # No actual queryset needed for the initial page load, it's for context if needed.
        # HTMX will fetch the table content.
        return SprItem.objects.none() # Return an empty queryset for initial page load

class SprItemTablePartialView(TemplateView):
    """
    Renders only the SPR Items table, designed to be loaded via HTMX.
    This view contains the complex filtering and data preparation logic.
    """
    template_name = 'material_management/spritem/_spritem_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Replicate ASP.NET's session and querystring parameters
        # In a real app, these would come from authentication/session management and form inputs.
        # For demonstration, using dummy values or getting from request parameters.
        comp_id = self.request.session.get('compid', 1) # Example default
        fin_year_id = self.request.session.get('finyear', 2024) # Example default
        sup_code = self.request.GET.get('Code', 'SUP001') # Example default from QueryString

        # Use the custom manager method to get the filtered and annotated data
        try:
            spr_items = SprItem.objects.get_filterable_spr_items(
                comp_id=comp_id,
                fin_year_id=fin_year_id,
                sup_code=sup_code
            ).order_by('spr_no', 'item__item_code') # Order as needed
            context['spr_items'] = spr_items
        except Exception as e:
            # Handle database errors or data inconsistencies gracefully
            messages.error(self.request, f"Error loading SPR data: {e}")
            context['spr_items'] = SprItem.objects.none() # Ensure empty list on error
            
        return context

class SprItemSelectView(View):
    """
    Handles the 'Select' action for an SPR item.
    This view simulates the redirection behavior of the original ASP.NET application.
    In a full migration, this would redirect to or render a PO creation form.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            spr_item = SprItem.objects.get(pk=pk)
            # Replicate the original ASP.NET redirection parameters
            sprno = spr_item.spr_no
            sprid = spr_item.id
            sup_code = spr_item.supplier_id # Or get from session/query string if consistent
            
            # This is where you would integrate with the PO creation module.
            # Option 1: Redirect to a new Django view for PO creation (simulates ASP.NET behavior)
            # return redirect(reverse_lazy('po_creation_form') + f'?sprno={sprno}&sprid={sprid}&Code={sup_code}&ModId=6&SubModId=35')

            # Option 2: Return a 204 response with an HX-Redirect header for client-side navigation
            # This is good for HTMX driven full-page navigation
            response = HttpResponse(status=204)
            response['HX-Redirect'] = reverse_lazy('po_creation_placeholder') + f'?sprno={sprno}&sprid={sprid}&Code={sup_code}&ModId=6&SubModId=35'
            messages.success(request, f"Selected SPR: {sprno} (ID: {sprid}) for PO creation.")
            return response
            
            # Option 3: Return a JSON response if you wanted to open a modal client-side with this data
            # return JsonResponse({'status': 'success', 'spr_no': sprno, 'spr_id': sprid, 'supplier_code': sup_code})

        except SprItem.DoesNotExist:
            messages.error(request, "Selected SPR Item not found.")
            return HttpResponse(status=404) # Or redirect back to list
        except Exception as e:
            messages.error(request, f"An error occurred during selection: {e}")
            return HttpResponse(status=500)

# Placeholder view for the "PO creation" module that the original page redirected to
class POCreationPlaceholderView(TemplateView):
    template_name = 'material_management/po_creation_placeholder.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['spr_no'] = self.request.GET.get('sprno')
        context['spr_id'] = self.request.GET.get('sprid')
        context['supplier_code'] = self.request.GET.get('Code')
        return context
```

#### 4.4 Templates (`material_management/templates/material_management/spritem/`)

**`list.html`** (Main page, will load table via HTMX)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block title %}SPR Items for PO{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Available SPR Items for Purchase Order</h2>
    </div>
    
    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-4 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="sprItemTable-container"
         hx-trigger="load, refreshSprItemList from:body"
         hx-get="{% url 'material_management:spritem_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Initial loading state -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading SPR Items...</p>
        </div>
    </div>
    
    <!-- Modals for any future form interactions (not directly used by this page) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me" x-data="{ open: false }">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1HCxiniDQYffcQj6j/hR23xK6I7d7t2y7k5fN+h3a7xJp4z5X1KzZ5E6W6q3+T/" crossorigin="anonymous"></script>
<script src="https://unpkg.com/alpinejs@3.13.0/dist/cdn.min.js" defer></script>
<!-- DataTables CDN -->
<script src="https://cdn.datatables.net/2.0.0/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.0/css/dataTables.dataTables.min.css">
{% endblock %}
```

**`_spritem_table.html`** (Partial template for the DataTables grid, loaded via HTMX)

```html
<div class="p-4">
    {% if spr_items %}
    <table id="sprItemDataTable" class="min-w-full bg-white border border-gray-300">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SPR No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">SPR Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">PO Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Remain Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">A/c Head</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Del. Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Gen. By</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Action</th>
            </tr>
        </thead>
        <tbody>
            {% for item in spr_items %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.financial_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.spr_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.work_order_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.department_name_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.request_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.item_code_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ item.purch_description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.uom_purchase }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ item.qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ item.po_quantity|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ item.remaining_quantity|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.ac_head_symbol }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ item.delivery_date_formatted }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ item.generated_by }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                        hx-get="{% url 'material_management:spritem_select' item.pk %}"
                        hx-target="body" hx-swap="none"
                        hx-trigger="click"
                        {% if not item.is_available_for_selection %}disabled{% endif %}
                        >
                        Select
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center p-6">
        <p class="text-lg text-gray-600 font-semibold">No data to display !</p>
        <p class="text-gray-500 mt-2">There are no SPR items matching the current criteria that are available for selection.</p>
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#sprItemDataTable').DataTable({
            "pageLength": 17, // Match original PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": 0 }, // SN column not orderable
                { "orderable": false, "targets": -1 } // Action column not orderable
            ]
        });
    });
</script>
```

**`po_creation_placeholder.html`** (A simple placeholder for where the selection would lead)

```html
{% extends 'core/base.html' %}

{% block title %}PO Creation{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Create Purchase Order</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <p class="text-lg text-green-600 mb-4">Successfully selected SPR Item for PO creation!</p>
        <p class="text-gray-700">This is a placeholder for the actual PO creation form.</p>
        <p class="text-gray-700 mt-2">Details passed: </p>
        <ul class="list-disc list-inside ml-4 mt-2 text-gray-600">
            <li><strong>SPR No:</strong> {{ spr_no }}</li>
            <li><strong>SPR ID:</strong> {{ spr_id }}</li>
            <li><strong>Supplier Code:</strong> {{ supplier_code }}</li>
        </ul>
        <div class="mt-6">
            <a href="{% url 'material_management:spritem_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded inline-flex items-center">
                &larr; Back to SPR Item List
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`material_management/urls.py`)

```python
from django.urls import path
from .views import SprItemListView, SprItemTablePartialView, SprItemSelectView, POCreationPlaceholderView

app_name = 'material_management' # Namespace for the app

urlpatterns = [
    # Main list page for SPR Items
    path('spr-items/', SprItemListView.as_view(), name='spritem_list'),
    
    # HTMX endpoint to load just the table content
    path('spr-items/table/', SprItemTablePartialView.as_view(), name='spritem_table'),
    
    # Endpoint to handle the "Select" action for an SPR item
    # This will typically lead to a new form (e.g., PO creation)
    path('spr-items/select/<int:pk>/', SprItemSelectView.as_view(), name='spritem_select'),

    # Placeholder for where the "select" action leads
    path('po-creation-form/', POCreationPlaceholderView.as_view(), name='po_creation_placeholder'),
]

```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests for models, custom manager, and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
import datetime
from .models import (
    SprItem, SprMaster, FinancialMaster, AccHead, OfficeStaff, UnitMaster,
    ItemMaster, BusinessGroup, PoMaster, PoDetail, SprPoTemp
)

class SprItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for testing
        cls.fin_year_2024 = FinancialMaster.objects.create(fin_year_id=2024, fin_year="2024-2025", comp_id=1)
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=2023, fin_year="2023-2024", comp_id=1)
        cls.acc_head_1 = AccHead.objects.create(id=1, symbol="RAW_MAT")
        cls.staff_1 = OfficeStaff.objects.create(emp_id=1, employee_name="John Doe", comp_id=1)
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol="EA")
        cls.item_1 = ItemMaster.objects.create(id=1, item_code="ITEM001", manf_desc="Product X", uom_basic=cls.unit_ea, comp_id=1)
        cls.dept_1 = BusinessGroup.objects.create(id=1, symbol="PRODUCTION")

        cls.spr_master_1 = SprMaster.objects.create(
            id=1, spr_no="SPR001", fin_year_id=cls.fin_year_2024, sys_date=timezone.now(),
            session_id=cls.staff_1, comp_id=1, authorize=True
        )
        cls.spr_master_2 = SprMaster.objects.create(
            id=2, spr_no="SPR002", fin_year_id=cls.fin_year_2024, sys_date=timezone.now(),
            session_id=cls.staff_1, comp_id=1, authorize=True
        )
        cls.spr_master_3_unauth = SprMaster.objects.create(
            id=3, spr_no="SPR003", fin_year_id=cls.fin_year_2024, sys_date=timezone.now(),
            session_id=cls.staff_1, comp_id=1, authorize=False
        )
        cls.spr_master_4_old_year = SprMaster.objects.create(
            id=4, spr_no="SPR004", fin_year_id=cls.fin_year_2023, sys_date=timezone.now(),
            session_id=cls.staff_1, comp_id=1, authorize=True
        )


        # Create SPR Item for testing
        cls.spr_item_1 = SprItem.objects.create(
            id=1, m_id=cls.spr_master_1, spr_no="SPR001", supplier_id="SUP001", wo_no="WO123",
            dept_id=cls.dept_1, item_id=cls.item_1, ah_id=cls.acc_head_1, qty=100.0,
            del_date=timezone.now() + timedelta(days=7)
        )
        cls.spr_item_2_with_po = SprItem.objects.create(
            id=2, m_id=cls.spr_master_2, spr_no="SPR002", supplier_id="SUP001", wo_no="WO124",
            dept_id=cls.dept_1, item_id=cls.item_1, ah_id=cls.acc_head_1, qty=50.0,
            del_date=timezone.now() + timedelta(days=10)
        )
        cls.spr_item_3_in_temp = SprItem.objects.create(
            id=3, m_id=cls.spr_master_1, spr_no="SPR001", supplier_id="SUP001", wo_no="WO125",
            dept_id=cls.dept_1, item_id=cls.item_1, ah_id=cls.acc_head_1, qty=75.0,
            del_date=timezone.now() + timedelta(days=14)
        )
        cls.spr_item_4_zero_remain = SprItem.objects.create(
            id=4, m_id=cls.spr_master_1, spr_no="SPR001", supplier_id="SUP001", wo_no="WO126",
            dept_id=cls.dept_1, item_id=cls.item_1, ah_id=cls.acc_head_1, qty=20.0,
            del_date=timezone.now() + timedelta(days=20)
        )
        
        # Add PO details for spr_item_2_with_po
        cls.po_master_1 = PoMaster.objects.create(id=1, comp_id=1)
        PoDetail.objects.create(id=1, m_id=cls.po_master_1, spr_id=cls.spr_item_2_with_po.id, qty=30.0)

        # Add entry to SprPoTemp for spr_item_3_in_temp
        SprPoTemp.objects.create(id=1, comp_id=1, spr_no=cls.spr_item_3_in_temp.spr_no, spr_id=cls.spr_item_3_in_temp.id)
        
        # Add a PO detail that makes spr_item_4_zero_remain have 0 remaining qty
        cls.po_master_2 = PoMaster.objects.create(id=2, comp_id=1)
        PoDetail.objects.create(id=2, m_id=cls.po_master_2, spr_id=cls.spr_item_4_zero_remain.id, qty=20.0)


    def test_spr_item_creation(self):
        self.assertEqual(SprItem.objects.count(), 4)
        self.assertEqual(self.spr_item_1.qty, 100.0)
        self.assertEqual(self.spr_item_1.spr_master.spr_no, "SPR001")

    def test_related_properties(self):
        # Test direct FK relationships
        self.assertEqual(self.spr_item_1.item.item_code, "ITEM001")
        self.assertEqual(self.spr_item_1.ac_head.symbol, "RAW_MAT")
        self.assertEqual(self.spr_item_1.department.symbol, "PRODUCTION")

        # Test derived properties
        self.assertEqual(self.spr_item_1.financial_year, "2024-2025")
        self.assertEqual(self.spr_item_1.generated_by, "John Doe")
        self.assertEqual(self.spr_item_1.item_code_display, "ITEM001")
        self.assertEqual(self.spr_item_1.purch_description, "Product X")
        self.assertEqual(self.spr_item_1.uom_purchase, "EA")
        self.assertEqual(self.spr_item_1.ac_head_symbol, "RAW_MAT")
        self.assertEqual(self.spr_item_1.work_order_no_display, "WO123")
        self.assertEqual(self.spr_item_1.department_name_display, "PRODUCTION")
        self.assertIsInstance(datetime.datetime.strptime(self.spr_item_1.request_date, '%d/%m/%Y'), datetime.datetime)
        self.assertIsInstance(datetime.datetime.strptime(self.spr_item_1.delivery_date_formatted, '%d/%m/%Y'), datetime.datetime)

    def test_po_quantity_and_remaining_quantity(self):
        # spr_item_1 has no POs
        self.assertEqual(self.spr_item_1.po_quantity, 0.0)
        self.assertEqual(self.spr_item_1.remaining_quantity, 100.0)

        # spr_item_2_with_po has 30.0 POs
        self.assertEqual(self.spr_item_2_with_po.po_quantity, 30.0)
        self.assertEqual(self.spr_item_2_with_po.remaining_quantity, 20.0) # 50 - 30

        # spr_item_4_zero_remain has 20.0 POs
        self.assertEqual(self.spr_item_4_zero_remain.po_quantity, 20.0)
        self.assertEqual(self.spr_item_4_zero_remain.remaining_quantity, 0.0) # 20 - 20

    def test_is_available_for_selection(self):
        self.assertTrue(self.spr_item_1.is_available_for_selection)
        self.assertTrue(self.spr_item_2_with_po.is_available_for_selection)
        
        # spr_item_3_in_temp should not be available
        self.assertFalse(self.spr_item_3_in_temp.is_available_for_selection)
        
        # spr_item_4_zero_remain should not be available (remaining qty = 0)
        self.assertFalse(self.spr_item_4_zero_remain.is_available_for_selection)

    def test_get_filterable_spr_items_queryset(self):
        # Test with the specific filters
        qs = SprItem.objects.get_filterable_spr_items(
            comp_id=1,
            fin_year_id=2024,
            sup_code="SUP001"
        )
        
        # Expected items: spr_item_1, spr_item_2_with_po
        # Not expected: spr_item_3_in_temp (in temp), spr_item_4_zero_remain (0 remain)
        # Not expected: spr_master_3_unauth (unauthorized)
        # Not expected: spr_master_4_old_year (old financial year)
        
        self.assertEqual(qs.count(), 2)
        self.assertIn(self.spr_item_1, qs)
        self.assertIn(self.spr_item_2_with_po, qs)
        self.assertNotIn(self.spr_item_3_in_temp, qs)
        self.assertNotIn(self.spr_item_4_zero_remain, qs)
        
        # Verify annotated values on the queryset results
        item_from_qs = qs.get(id=self.spr_item_2_with_po.id)
        self.assertEqual(item_from_qs.calculated_po_qty, 30.0)
        self.assertEqual(item_from_qs.calculated_remain_qty, 20.0)
        self.assertIsNone(item_from_qs.is_in_temp) # Not in temp table

        item_from_qs = qs.get(id=self.spr_item_1.id)
        self.assertEqual(item_from_qs.calculated_po_qty, 0.0)
        self.assertEqual(item_from_qs.calculated_remain_qty, 100.0)
        self.assertIsNone(item_from_qs.is_in_temp) # Not in temp table

        # Test filtering with a non-existent supplier
        qs_no_supplier = SprItem.objects.get_filterable_spr_items(
            comp_id=1,
            fin_year_id=2024,
            sup_code="NONEXISTENT"
        )
        self.assertEqual(qs_no_supplier.count(), 0)

        # Test filtering with a different financial year
        qs_other_year = SprItem.objects.get_filterable_spr_items(
            comp_id=1,
            fin_year_id=2023,
            sup_code="SUP001"
        )
        self.assertEqual(qs_other_year.count(), 0) # No items from 2023 should have positive remaining qty

class SprItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects and an SPR Item for testing views
        cls.fin_year_2024 = FinancialMaster.objects.create(fin_year_id=2024, fin_year="2024-2025", comp_id=1)
        cls.acc_head_1 = AccHead.objects.create(id=1, symbol="RAW_MAT")
        cls.staff_1 = OfficeStaff.objects.create(emp_id=1, employee_name="John Doe", comp_id=1)
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol="EA")
        cls.item_1 = ItemMaster.objects.create(id=1, item_code="ITEM001", manf_desc="Product X", uom_basic=cls.unit_ea, comp_id=1)
        cls.dept_1 = BusinessGroup.objects.create(id=1, symbol="PRODUCTION")

        cls.spr_master_1 = SprMaster.objects.create(
            id=1, spr_no="SPR001", fin_year_id=cls.fin_year_2024, sys_date=timezone.now(),
            session_id=cls.staff_1, comp_id=1, authorize=True
        )
        cls.spr_item_1 = SprItem.objects.create(
            id=1, m_id=cls.spr_master_1, spr_no="SPR001", supplier_id="SUP001", wo_no="WO123",
            dept_id=cls.dept_1, item_id=cls.item_1, ah_id=cls.acc_head_1, qty=100.0,
            del_date=timezone.now() + timedelta(days=7)
        )
        
    def setUp(self):
        self.client = Client()
        # Set session variables as they are used in LoadSPRData equivalent
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('material_management:spritem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spritem/list.html')
        self.assertContains(response, "Loading SPR Items...") # Initial loading state for HTMX

    def test_table_partial_view_get(self):
        # Simulates HTMX request for the table content
        response = self.client.get(reverse('material_management:spritem_table'), {'Code': 'SUP001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spritem/_spritem_table.html')
        self.assertContains(response, "SPR001") # Check if data is rendered
        self.assertContains(response, "ITEM001")
        self.assertContains(response, "Select") # Check if action button is present
        self.assertContains(response, "id=\"sprItemDataTable\"") # Check if DataTables element is present

    def test_table_partial_view_get_no_data(self):
        # Delete all SPR items to test empty state
        SprItem.objects.all().delete()
        response = self.client.get(reverse('material_management:spritem_table'), {'Code': 'SUP001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spritem/_spritem_table.html')
        self.assertContains(response, "No data to display !")

    def test_spr_item_select_view_get_success(self):
        response = self.client.get(reverse('material_management:spritem_select', args=[self.spr_item_1.pk]))
        self.assertEqual(response.status_code, 204) # HTTP 204 No Content for HTMX
        self.assertIn('HX-Redirect', response.headers)
        self.assertRegex(response.headers['HX-Redirect'], r'/po-creation-form/\?sprno=SPR001&sprid=1&Code=SUP001&ModId=6&SubModId=35')

    def test_spr_item_select_view_get_not_found(self):
        response = self.client.get(reverse('material_management:spritem_select', args=[999])) # Non-existent PK
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, "Selected SPR Item not found.", status_code=404)

    def test_po_creation_placeholder_view(self):
        response = self.client.get(
            reverse('material_management:po_creation_placeholder'),
            {'sprno': 'SPR001', 'sprid': '1', 'Code': 'SUP001'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_creation_placeholder.html')
        self.assertContains(response, "Create Purchase Order")
        self.assertContains(response, "SPR No: SPR001")
        self.assertContains(response, "SPR ID: 1")
        self.assertContains(response, "Supplier Code: SUP001")

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic content:**
    *   The main `list.html` uses `hx-get` to fetch the `_spritem_table.html` partial and `hx-swap="innerHTML"` to place it into the `sprItemTable-container` div.
    *   `hx-trigger="load, refreshSprItemList from:body"` ensures the table loads on page load and refreshes if a custom `refreshSprItemList` event is triggered (e.g., after a CRUD operation in another module, which would not be implemented here but is a good practice).
    *   The "Select" button uses `hx-get` to `spritem_select` endpoint, which returns an `HX-Redirect` header to navigate the client to the PO creation placeholder. This simulates the ASP.NET `Response.Redirect`.
*   **Alpine.js for UI state:**
    *   A simple `x-data="{ open: false }"` is included on the modal placeholder in `list.html` as an example of how Alpine.js would manage modal visibility if an add/edit form were to be rendered in a modal. It's not directly used for this list view's functionality but is present for future expansion.
*   **DataTables for list views:**
    *   The `_spritem_table.html` partial includes a JavaScript snippet that initializes DataTables on `$('#sprItemDataTable')`. This provides out-of-the-box search, sort, and pagination as requested, replacing the ASP.NET GridView's functionality.
    *   CDN links for HTMX, Alpine.js, and DataTables are placed in `extra_js` block within `list.html`, assuming `base.html` includes a `{% block extra_js %}`.
*   **No custom JavaScript requirements:** All dynamic interactions are handled by HTMX. Alpine.js is declarative HTML-based, requiring no custom JS logic beyond its attributes.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET `PO_SPR_ItemGrid` module to a modern Django application. By focusing on automated approaches, clear separation of concerns, and leveraging modern frontend techniques, we ensure a high-quality, performant, and maintainable solution. The detailed Django code examples, including models, views, templates, and tests, provide a ready-to-implement foundation that can be scaled and adapted for your organization's needs.