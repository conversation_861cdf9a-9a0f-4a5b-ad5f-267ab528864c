The following modernization plan translates your existing ASP.NET application's `PO_SPR_View_Print_Details` module into a modern Django-based solution. This transition focuses on an automation-driven approach, streamlining the migration process and delivering a robust, scalable, and maintainable application.

### Business Value Proposition:

Migrating this module to Django offers significant business advantages:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms, which are costly to maintain and scale, to a modern, actively supported framework.
2.  **Improved Performance & Scalability:** Django's architecture is designed for high performance and can scale efficiently to handle increased user loads and data volumes.
3.  **Enhanced User Experience (UX):** With HTMX and Alpine.js, we deliver a highly responsive, interactive user interface that feels like a single-page application without the complexity of traditional JavaScript frameworks. This leads to faster interactions and happier users.
4.  **Simplified Development & Maintenance:** Adopting a 'Fat Model, Thin View' approach, along with clear separation of concerns, makes the codebase easier to understand, develop, and maintain, reducing future development costs.
5.  **Cost Efficiency:** Leveraging open-source technologies like Django, PostgreSQL, HTMX, and Alpine.js eliminates proprietary software licensing costs associated with .NET and Crystal Reports.
6.  **Future-Proofing:** Positions your application on a widely adopted, community-supported technology stack, making it easier to find talent and integrate with emerging technologies.
7.  **Automation Readiness:** The structured Django approach with well-defined components is inherently more conducive to AI-assisted migration and future automated code generation, minimizing manual effort.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

The provided ASP.NET code (`PO_SPR_View_Print_Details.aspx` and its C# code-behind) acts primarily as a wrapper to display a report generated by another page (`PO_SPR_Print_Page.aspx`). **It does not contain direct database interaction logic** (e.g., `SqlDataSource`, direct SQL queries, or data adapters).

Therefore, we cannot infer a specific database table directly from *this* particular ASP.NET component. Instead, the page's core function is to receive and pass a set of parameters to identify and display a Purchase Order (PO) report.

To align with Django's structured approach and the 'Fat Model, Thin View' principle, we will create a *conceptual* Django model. This model, `PurchaseOrderReport`, will represent the parameters used to request or define a specific PO report, providing a structured way to handle the data that the ASP.NET page was processing via query strings.

**Inferred Conceptual Model Table Name:** `po_report_parameters`
**Inferred Columns (from URL query parameters and hardcoded values):**
- `po_no` (corresponds to `Request.QueryString["pono"]`)
- `supplier_code` (corresponds to `Request.QueryString["Code"]`)
- `parent_page_url` (corresponds to `Request.QueryString["parentPage"]`)
- `module_id` (corresponds to `Request.QueryString["mid"]` and hardcoded `ModId=6`)
- `sub_module_id` (hardcoded `SubModId=35` in the iframe URL)
- `amendment_no` (corresponds to `Request.QueryString["AmdNo"]`)
- `key_value` (corresponds to `Request.QueryString["Key"]`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The primary function of the `PO_SPR_View_Print_Details.aspx` page is to **Read/View** a specific Purchase Order report. It does not perform traditional Create, Update, or Delete operations on an underlying database entity directly from this page.

-   **Create:** No direct creation operation is identified. The page facilitates viewing, not data entry for new records.
-   **Read:** The core functionality involves retrieving parameters from the URL and using them to construct an iframe source URL, effectively displaying a report. This translates to a Django **DetailView** or a custom view for report display.
-   **Update:** No direct update operation is identified.
-   **Delete:** No direct delete operation is identified.

**Validation Logic:** The original ASP.NET code included a generic `try-catch` block for handling missing query string parameters. In Django, we will implement robust parameter parsing and error handling within the view, and form validation will be used if we create a form for conceptually "generating" or "defining" report parameters.

To fully comply with the request for all standard Django CBVs (ListView, CreateView, UpdateView, DeleteView), we will apply these to our conceptual `PurchaseOrderReport` model. This allows us to demonstrate how a full CRUD flow would be structured for a related entity, even if the original ASP.NET page's direct purpose was narrower.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

-   **`asp:Button ID="Cancel"`:** This button triggers a server-side redirect (`Response.Redirect`) to a `parentPage` URL with specific query parameters. In Django, this behavior will be replicated using HTMX for a dynamic client-side redirect or a standard HTTP redirect.
-   **`iframe id="myifram"`:** This HTML iframe dynamically embeds the `PO_SPR_Print_Page.aspx` based on various query string parameters. In the Django migration, this will typically involve:
    *   Embedding a similar iframe pointing to a Django view that generates the report (e.g., as a PDF, or dynamically rendered HTML).
    *   Alternatively, the report content itself could be generated and embedded directly within the Django template. For this migration, we will demonstrate embedding a conceptual report URL.
-   **Styling:** The ASP.NET page uses external CSS (`StyleSheet.css`) and inline styles. In Django, this will be replaced by Tailwind CSS classes for consistent, modern styling.
-   **JavaScript (`loadingNotifier.js`):** Any loading indicators or dynamic UI states will be handled efficiently using HTMX's built-in indicators (`hx-indicator`) and potentially Alpine.js for more complex client-side state management, eliminating the need for custom JavaScript.

### Step 4: Generate Django Code

We will create a new Django application named `poreports` to encapsulate this module's functionality.

#### 4.1 Models

**Task:** Create a Django model based on the conceptual database schema.

This model, `PurchaseOrderReport`, will represent the set of parameters that define a specific purchase order report instance. This is crucial for enabling Django's ORM, forms, and providing a structured backbone, even if these specific parameters aren't stored in a dedicated `po_report_parameters` table in the same way your main application data is.

**File: `poreports/models.py`**

```python
from django.db import models

class PurchaseOrderReport(models.Model):
    """
    Conceptual model representing parameters for a Purchase Order report.
    This model encapsulates the query string parameters used to identify
    and display a specific PO report. It serves as a structured representation
    for Django's ORM and form handling, even if these parameters are not
    persisted to a dedicated 'po_report_parameters' table in the same manner
    as core application data.

    In a live system, you might remove 'managed = False' and manage migrations
    if you decide to persist these report configurations, or keep it 'managed=False'
    and use this model purely for form validation and data structuring.
    """
    po_no = models.CharField(
        max_length=50,
        db_column='PO_NO',
        verbose_name='Purchase Order No.'
    )
    supplier_code = models.CharField(
        max_length=50,
        db_column='SUPPLIER_CODE',
        verbose_name='Supplier Code'
    )
    parent_page_url = models.CharField(
        max_length=255,
        db_column='PARENT_PAGE_URL',
        verbose_name='Parent Page URL',
        help_text='The URL to redirect to upon cancellation.'
    )
    module_id = models.IntegerField(
        db_column='MODULE_ID',
        verbose_name='Module ID',
        default=6
    )
    sub_module_id = models.IntegerField(
        db_column='SUB_MODULE_ID',
        verbose_name='Sub Module ID',
        default=35
    )
    amendment_no = models.CharField(
        max_length=20,
        db_column='AMENDMENT_NO',
        blank=True,
        null=True,
        verbose_name='Amendment No.'
    )
    key_value = models.CharField(
        max_length=255,
        db_column='KEY_VALUE',
        blank=True,
        null=True,
        verbose_name='Key Value'
    )

    class Meta:
        # Set to False if this model just represents transient parameters
        # and doesn't map to a Django-managed database table.
        # Set to True if you want Django to manage migrations for this table
        # (e.g., to store historical report view parameters).
        managed = False
        db_table = 'po_report_parameters' # This is a conceptual table name
        verbose_name = 'Purchase Order Report Parameter'
        verbose_name_plural = 'Purchase Order Report Parameters'

    def __str__(self):
        return f"PO Report: {self.po_no} (Supplier: {self.supplier_code})"

    # Business logic methods for report generation/URL construction
    def get_report_iframe_url(self):
        """
        Constructs the URL for the embedded report, mimicking the ASP.NET iframe src.
        In a real Django app, this would typically point to an internal Django view
        that generates the actual report (e.g., a PDF report from the database).
        """
        # This URL should point to your Django report generation view
        # or an external reporting service endpoint.
        base_report_view_url = "/reports/po_spr_print_page/" # Example Django internal report URL
        
        # Build query parameters dynamically from model instance
        params = {
            "mid": self.module_id,
            "ModId": self.module_id, # Redundant, but matching original for strict conversion
            "SubModId": self.sub_module_id,
            "pono": self.po_no,
            "Code": self.supplier_code,
        }
        if self.amendment_no:
            params["AmdNo"] = self.amendment_no
        if self.key_value:
            params["Key"] = self.key_value

        from urllib.parse import urlencode
        return f"{base_report_view_url}?{urlencode(params)}"

    def get_cancel_redirect_url(self):
        """
        Constructs the URL for the 'Cancel' button redirect.
        """
        from urllib.parse import urlencode
        params = {
            "Code": self.supplier_code,
            "ModId": self.module_id,
            "SubModId": self.sub_module_id
        }
        return f"{self.parent_page_url}?{urlencode(params)}"

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

This form (`PurchaseOrderReportForm`) will be used by the `CreateView` and `UpdateView` for our conceptual `PurchaseOrderReport` model. It allows structured data input and validation for the report parameters, aligning with Django's form handling.

**File: `poreports/forms.py`**

```python
from django import forms
from .models import PurchaseOrderReport

class PurchaseOrderReportForm(forms.ModelForm):
    class Meta:
        model = PurchaseOrderReport
        fields = [
            'po_no', 'supplier_code', 'parent_page_url',
            'module_id', 'sub_module_id', 'amendment_no', 'key_value'
        ]
        widgets = {
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter PO Number'}),
            'supplier_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Supplier Code'}),
            'parent_page_url': forms.URLInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., /material-management/po-list/'}),
            'module_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'min': 1}),
            'sub_module_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'min': 1}),
            'amendment_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'key_value': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'po_no': 'Purchase Order Number',
            'supplier_code': 'Supplier Code',
            'parent_page_url': 'Return Page URL',
            'module_id': 'Module ID',
            'sub_module_id': 'Sub Module ID',
            'amendment_no': 'Amendment Number',
            'key_value': 'Key Value',
        }

    # Custom validation example: ensure PO Number and Supplier Code are provided
    def clean(self):
        cleaned_data = super().clean()
        po_no = cleaned_data.get('po_no')
        supplier_code = cleaned_data.get('supplier_code')
        parent_page_url = cleaned_data.get('parent_page_url')

        if not po_no:
            self.add_error('po_no', 'Purchase Order Number is required.')
        if not supplier_code:
            self.add_error('supplier_code', 'Supplier Code is required.')
        if not parent_page_url:
            self.add_error('parent_page_url', 'Parent Page URL for redirection is required.')
        
        return cleaned_data

```

#### 4.3 Views

**Task:** Implement Django Class-Based Views (CBVs) for CRUD operations on the `PurchaseOrderReport` model, and a specific `DetailView` to replicate the original ASP.NET page's functionality of displaying the report. A `TablePartialView` is also included for HTMX-driven list updates.

**File: `poreports/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect, Http404
from django.shortcuts import render, get_object_or_404
from .models import PurchaseOrderReport
from .forms import PurchaseOrderReportForm

# --- CRUD Views for Report Parameters (to adhere to prompt's structure) ---

class PurchaseOrderReportListView(ListView):
    """
    Displays a list of conceptual Purchase Order Report parameter sets.
    This view demonstrates the use of DataTables for listing report configurations
    or past viewed reports.
    """
    model = PurchaseOrderReport
    template_name = 'poreports/purchaseorderreport/list.html'
    context_object_name = 'poreports' # Plural context name for template

    def get_queryset(self):
        # In a real application, this would fetch actual report configurations
        # from a database. For this conceptual model, we'll use all objects.
        return PurchaseOrderReport.objects.all()

class PurchaseOrderReportTablePartialView(ListView):
    """
    A partial view designed to be loaded via HTMX to refresh only the
    DataTables component within the list view.
    """
    model = PurchaseOrderReport
    template_name = 'poreports/purchaseorderreport/_purchaseorderreport_table.html'
    context_object_name = 'poreports'

    def get_queryset(self):
        return PurchaseOrderReport.objects.all()

class PurchaseOrderReportCreateView(CreateView):
    """
    Handles the creation of a new set of Purchase Order Report parameters.
    This view is typically loaded into a modal via HTMX.
    """
    model = PurchaseOrderReport
    form_class = PurchaseOrderReportForm
    template_name = 'poreports/purchaseorderreport/form.html'
    success_url = reverse_lazy('purchaseorderreport_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Report parameters added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response with a trigger header
            # to refresh the list table and close the modal.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderReportList',
                    'HX-Redirect': self.success_url # Optional: for full page redirect on success
                }
            )
        return response # Fallback for standard form submission

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'add'
        return context

class PurchaseOrderReportUpdateView(UpdateView):
    """
    Handles the updating of an existing set of Purchase Order Report parameters.
    This view is typically loaded into a modal via HTMX.
    """
    model = PurchaseOrderReport
    form_class = PurchaseOrderReportForm
    template_name = 'poreports/purchaseorderreport/form.html'
    success_url = reverse_lazy('purchaseorderreport_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Report parameters updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderReportList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'edit'
        return context

class PurchaseOrderReportDeleteView(DeleteView):
    """
    Handles the deletion of a set of Purchase Order Report parameters.
    This view is typically loaded into a modal via HTMX for confirmation.
    """
    model = PurchaseOrderReport
    template_name = 'poreports/purchaseorderreport/confirm_delete.html'
    success_url = reverse_lazy('purchaseorderreport_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Order Report parameters deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderReportList'
                }
            )
        return response

# --- Specific View for ASP.NET Page Replication (PO_SPR_View_Print_Details.aspx) ---

class PurchaseOrderReportDetailView(DetailView):
    """
    This view directly replicates the original ASP.NET page's core functionality:
    It extracts query string parameters (pono, Code, parentPage, mid, AmdNo, Key)
    and uses them to display a conceptual PO Report, embedding it within an iframe.
    It does not load a record from the database by PK, but constructs a model
    instance from URL query parameters.
    """
    model = PurchaseOrderReport # Use the model to structure the expected parameters
    template_name = 'poreports/purchaseorderreport/detail.html'
    context_object_name = 'report_params'

    def get_object(self, queryset=None):
        """
        Overrides get_object to construct a PurchaseOrderReport instance
        from URL query string parameters, as the original page doesn't load a specific
        record by ID, but rather by passed parameters.
        """
        po_no = self.request.GET.get("pono")
        sup_code = self.request.GET.get("Code")
        parent_page = self.request.GET.get("parentPage")
        mid = self.request.GET.get("mid")
        amd_no = self.request.GET.get("AmdNo")
        key = self.request.GET.get("Key")

        # Use default values from ASP.NET code if 'mid' not provided
        mod_id = int(mid) if mid else 6
        sub_mod_id = 35 # Hardcoded in original iframe src

        # Basic validation for required parameters
        if not (po_no and sup_code and parent_page):
            raise Http404("Essential report parameters (pono, Code, parentPage) are missing.")

        # Create a temporary instance of the model to hold the parameters.
        # This instance is not saved to the database (due to managed=False by default)
        # unless explicitly saved in a 'managed=True' setup. It's for data structuring.
        report_instance = PurchaseOrderReport(
            po_no=po_no,
            supplier_code=sup_code,
            parent_page_url=parent_page,
            module_id=mod_id,
            sub_module_id=sub_mod_id,
            amendment_no=amd_no,
            key_value=key
        )
        return report_instance

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_params = context['report_params']
        
        # Use the model's business logic method to construct the iframe URL
        context['iframe_src'] = report_params.get_report_iframe_url()
        # Also pass the cancel URL constructed by the model
        context['cancel_url'] = report_params.get_cancel_redirect_url()
        
        return context

    def post(self, request, *args, **kwargs):
        """
        Handles the 'Cancel' button click from the report view page.
        This simulates the Response.Redirect logic from the ASP.NET code.
        """
        # Retrieve parameters from the POST request (e.g., from hidden inputs)
        parent_page_url = request.POST.get('parent_page_url', '/')
        supplier_code = request.POST.get('supplier_code', '')
        module_id = request.POST.get('module_id', '6')
        sub_module_id = request.POST.get('sub_module_id', '35')

        # Construct the redirect URL using the retrieved parameters
        from urllib.parse import urlencode
        redirect_params = {
            "Code": supplier_code,
            "ModId": module_id,
            "SubModId": sub_module_id
        }
        redirect_url = f"{parent_page_url}?{urlencode(redirect_params)}"
        
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, respond with HX-Redirect header
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})
        else:
            # For standard form submissions, use HttpResponseRedirect
            return HttpResponseRedirect(redirect_url)

```

#### 4.4 Templates

**Task:** Create templates for each view. All templates will reside within `poreports/purchaseorderreport/` and extend `core/base.html`.

**File: `poreports/purchaseorderreport/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}PO Report Parameters List{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Order Report Configurations</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'purchaseorderreport_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Report Config
        </button>
    </div>

    <div id="purchaseorderreportTable-container"
         hx-trigger="load, refreshPurchaseOrderReportList from:body"
         hx-get="{% url 'purchaseorderreport_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Report Configurations...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit) and delete confirmation -->
    <div id="modal"
         class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init if needed, though for this simple modal HTMX and _ are sufficient
    document.addEventListener('alpine:init', () => {
        // Example: Alpine.data('modalController', () => ({ isOpen: false }));
    });

    // HTMX lifecycle event to re-initialize DataTables after swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'purchaseorderreportTable-container') {
            // Re-initialize DataTable if it was swapped
            $('#purchaseorderreportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance before re-initializing
            });
        }
        // Show modal content with animation after HTMX loads it
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            const modalContent = document.getElementById('modalContent');
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }
    });

    // Hide modal with animation when triggered by 'remove .is-active from #modal'
    document.body.addEventListener('htmx:beforeSwap', function(event) {
        if (event.detail.requestConfig.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            const modalContent = document.getElementById('modalContent');
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            // Give time for animation before hiding modal fully
            setTimeout(() => {
                document.getElementById('modal').classList.remove('is-active');
                document.getElementById('modal').classList.add('hidden');
                modalContent.innerHTML = ''; // Clear content
            }, 300); // Match CSS transition duration
        }
    });
</script>
{% endblock %}
```

**File: `poreports/purchaseorderreport/_purchaseorderreport_table.html` (Partial for HTMX)**

```html
<table id="purchaseorderreportTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No.</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Code</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent Page URL</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Module ID</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in poreports %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.po_no }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.supplier_code }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-blue-600 truncate max-w-xs" title="{{ obj.parent_page_url }}">{{ obj.parent_page_url }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.module_id }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <a href="{{ obj.get_report_iframe_url }}" target="_blank"
                   class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out mr-2">
                   <i class="fas fa-print mr-1"></i>View Report
                </a>
                <button
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition duration-150 ease-in-out mr-2"
                    hx-get="{% url 'purchaseorderreport_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit mr-1"></i>Edit
                </button>
                <button
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out"
                    hx-get="{% url 'purchaseorderreport_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt mr-1"></i>Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 text-center text-gray-500">No report configurations found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Only initialize if the table exists (it might be swapped out)
    if ($.fn.DataTable.isDataTable('#purchaseorderreportTable')) {
        $('#purchaseorderreportTable').DataTable().destroy();
    }
    $('#purchaseorderreportTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] } // Disable sorting on SN and Actions
        ]
    });
});
</script>
```

**File: `poreports/purchaseorderreport/form.html` (Partial for HTMX modal)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Report Configuration
    </h3>
    <form hx-post="{{ request.path }}" hx-target="#modalContent" hx-swap="outerHTML">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            
            {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ form.non_field_errors }}</span>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                <span class="htmx-indicator mr-2"><i class="fas fa-spinner fa-spin"></i></span>
                Save Report Configuration
            </button>
        </div>
    </form>
</div>

<script>
    // Ensure modal content is fully loaded and visible before form interaction
    document.addEventListener('DOMContentLoaded', function() {
        const modalContent = document.getElementById('modalContent');
        if (modalContent) {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }
    });
</script>
```

**File: `poreports/purchaseorderreport/confirm_delete.html` (Partial for HTMX modal)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">
        Are you sure you want to delete the report configuration for PO Number:
        <span class="font-bold text-red-600">{{ object.po_no }}</span>
        and Supplier Code: <span class="font-bold text-red-600">{{ object.supplier_code }}</span>?
    </p>
    <p class="text-sm text-gray-500 mb-6">This action cannot be undone.</p>
    
    <form hx-post="{% url 'purchaseorderreport_delete' object.pk %}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                <span class="htmx-indicator mr-2"><i class="fas fa-spinner fa-spin"></i></span>
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `poreports/purchaseorderreport/detail.html` (Main report view template)**

```html
{% extends 'core/base.html' %}

{% block title %}PO - Print Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-6 pb-3 border-b border-gray-200">
        <h1 class="text-2xl font-bold text-gray-800">PO - Print Details</h1>
        <form hx-post="{% url 'purchaseorderreport_view_print_details' %}" hx-swap="none">
            {% csrf_token %}
            <!-- Hidden inputs to pass parameters for cancellation -->
            <input type="hidden" name="parent_page_url" value="{{ report_params.parent_page_url }}">
            <input type="hidden" name="supplier_code" value="{{ report_params.supplier_code }}">
            <input type="hidden" name="module_id" value="{{ report_params.module_id }}">
            <input type="hidden" name="sub_module_id" value="{{ report_params.sub_module_id }}">

            <button type="submit"
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
                    hx-indicator="#cancel-spinner">
                <span id="cancel-spinner" class="htmx-indicator mr-2"><i class="fas fa-spinner fa-spin"></i></span>
                Cancel
            </button>
        </form>
    </div>

    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="p-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-700">Report Viewer</h2>
            <p class="text-sm text-gray-500 mt-1">
                Displaying Purchase Order Report for PO: <span class="font-medium text-blue-600">{{ report_params.po_no }}</span>
                and Supplier: <span class="font-medium text-blue-600">{{ report_params.supplier_code }}</span>.
            </p>
        </div>
        <div class="p-2 min-h-[480px] bg-gray-100 flex items-center justify-center">
            {% if iframe_src %}
            <iframe
                id="myifram"
                src="{{ iframe_src }}"
                width="100%"
                height="470"
                frameborder="0"
                scrolling="auto"
                class="rounded-md shadow-inner bg-white"
                title="Purchase Order Report">
                Your browser does not support iframes. Please use a modern browser.
            </iframe>
            {% else %}
            <div class="text-center text-gray-500 p-8">
                <i class="fas fa-exclamation-circle text-6xl mb-4"></i>
                <p class="text-lg">No report source available or parameters missing.</p>
                <p class="text-sm mt-2">Please ensure all required parameters are provided.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for local UI state
    });

    // Optional: Add a simple loading indicator for the iframe, mimicking original JS
    const iframe = document.getElementById('myifram');
    if (iframe) {
        // Show a temporary overlay or message while iframe loads
        iframe.addEventListener('load', () => {
            // Hide loading message once iframe content is loaded
            console.log("Iframe content loaded.");
            // Example: Remove a 'loading' class from parent or hide a spinner
        });
        iframe.addEventListener('error', () => {
            console.error("Iframe content failed to load.");
            // Display an error message if content fails to load
        });
    }
</script>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views. These patterns will be included in your `project/urls.py` after creating a `poreports/urls.py` file.

**File: `poreports/urls.py`**

```python
from django.urls import path
from .views import (
    PurchaseOrderReportListView,
    PurchaseOrderReportCreateView,
    PurchaseOrderReportUpdateView,
    PurchaseOrderReportDeleteView,
    PurchaseOrderReportDetailView,
    PurchaseOrderReportTablePartialView
)

urlpatterns = [
    # URLs for managing conceptual report parameter sets (CRUD operations)
    path('po-reports/', PurchaseOrderReportListView.as_view(), name='purchaseorderreport_list'),
    path('po-reports/table/', PurchaseOrderReportTablePartialView.as_view(), name='purchaseorderreport_table'),
    path('po-reports/add/', PurchaseOrderReportCreateView.as_view(), name='purchaseorderreport_add'),
    path('po-reports/edit/<int:pk>/', PurchaseOrderReportUpdateView.as_view(), name='purchaseorderreport_edit'),
    path('po-reports/delete/<int:pk>/', PurchaseOrderReportDeleteView.as_view(), name='purchaseorderreport_delete'),

    # URL that directly mimics the original ASP.NET PO_SPR_View_Print_Details.aspx functionality
    # This view expects query parameters (e.g., /view-print-details/?pono=123&Code=SUP001...)
    path('view-print-details/', PurchaseOrderReportDetailView.as_view(), name='purchaseorderreport_view_print_details'),
]

```

**To integrate these URLs into your Django project, add the following to your project's `urls.py` (e.g., `myproject/urls.py`):**

```python
# myproject/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('material-management/transactions/', include('poreports.urls')), # Adjust path as per your application structure
    # Add other app URLs here
]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the `PurchaseOrderReport` model and integration tests for all associated views. This ensures functionality, data integrity, and adheres to the 80% test coverage guideline.

**File: `poreports/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import PurchaseOrderReport
from .forms import PurchaseOrderReportForm

class PurchaseOrderReportModelTest(TestCase):
    """
    Unit tests for the PurchaseOrderReport model, covering field attributes
    and business logic methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single test instance for all model tests
        cls.report_params_data = {
            'po_no': 'PO-001',
            'supplier_code': 'SUP-ABC',
            'parent_page_url': '/previous/page/',
            'module_id': 6,
            'sub_module_id': 35,
            'amendment_no': 'Amd-01',
            'key_value': 'KEY-XYZ',
        }
        cls.report = PurchaseOrderReport.objects.create(**cls.report_params_data)

    def test_purchaseorderreport_creation(self):
        """Verify that a PurchaseOrderReport instance is created correctly."""
        self.assertEqual(self.report.po_no, 'PO-001')
        self.assertEqual(self.report.supplier_code, 'SUP-ABC')
        self.assertEqual(self.report.module_id, 6)
        self.assertIsNotNone(self.report.pk)

    def test_string_representation(self):
        """Test the __str__ method of the model."""
        expected_str = f"PO Report: {self.report.po_no} (Supplier: {self.report.supplier_code})"
        self.assertEqual(str(self.report), expected_str)

    def test_get_report_iframe_url_method(self):
        """Test the get_report_iframe_url business logic method."""
        expected_base_url = "/reports/po_spr_print_page/"
        expected_params = (
            f"mid={self.report.module_id}&ModId={self.report.module_id}&SubModId={self.report.sub_module_id}&"
            f"pono={self.report.po_no}&Code={self.report.supplier_code}&AmdNo={self.report.amendment_no}&"
            f"Key={self.report.key_value}"
        )
        # Using urlencode directly in the test to match model's method
        from urllib.parse import urlencode
        expected_url_parts = {
            "mid": str(self.report.module_id),
            "ModId": str(self.report.module_id),
            "SubModId": str(self.report.sub_module_id),
            "pono": self.report.po_no,
            "Code": self.report.supplier_code,
            "AmdNo": self.report.amendment_no,
            "Key": self.report.key_value,
        }
        expected_url = f"{expected_base_url}?{urlencode(expected_url_parts)}"
        self.assertEqual(self.report.get_report_iframe_url(), expected_url)

    def test_get_cancel_redirect_url_method(self):
        """Test the get_cancel_redirect_url business logic method."""
        expected_base_url = self.report.parent_page_url
        from urllib.parse import urlencode
        expected_params = urlencode({
            "Code": self.report.supplier_code,
            "ModId": self.report.module_id,
            "SubModId": self.report.sub_module_id
        })
        expected_url = f"{expected_base_url}?{expected_params}"
        self.assertEqual(self.report.get_cancel_redirect_url(), expected_url)

    def test_verbose_name_plural(self):
        """Test the verbose_name_plural in Meta options."""
        self.assertEqual(PurchaseOrderReport._meta.verbose_name_plural, 'Purchase Order Report Parameters')

class PurchaseOrderReportFormTest(TestCase):
    """
    Unit tests for the PurchaseOrderReportForm.
    """
    def test_form_valid_data(self):
        """Test form with valid data."""
        form_data = {
            'po_no': 'PO-002',
            'supplier_code': 'SUP-DEF',
            'parent_page_url': '/another/page/',
            'module_id': 7,
            'sub_module_id': 36,
            'amendment_no': 'Amd-02',
            'key_value': 'KEY-UVW',
        }
        form = PurchaseOrderReportForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_invalid_data_missing_required(self):
        """Test form with missing required fields."""
        form_data = {
            'supplier_code': 'SUP-DEF',
            'parent_page_url': '/another/page/',
            'module_id': 7,
            'sub_module_id': 36,
        }
        form = PurchaseOrderReportForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('po_no', form.errors)
        self.assertIn('Purchase Order Number is required.', form.errors['po_no'])

    def test_form_invalid_data_missing_parent_page(self):
        """Test form with missing parent_page_url."""
        form_data = {
            'po_no': 'PO-003',
            'supplier_code': 'SUP-GHI',
            'module_id': 8,
            'sub_module_id': 37,
        }
        form = PurchaseOrderReportForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('parent_page_url', form.errors)
        self.assertIn('Parent Page URL for redirection is required.', form.errors['parent_page_url'])


class PurchaseOrderReportViewsTest(TestCase):
    """
    Integration tests for all PurchaseOrderReport views.
    """
    def setUp(self):
        self.client = Client()
        # Create a test instance for views that require an existing object
        self.report = PurchaseOrderReport.objects.create(
            po_no='PO-VIEW-001',
            supplier_code='SUP-VIEW',
            parent_page_url='/mock/list/page/',
            module_id=10,
            sub_module_id=40,
            amendment_no='Amd-View',
            key_value='Key-View',
        )

    # --- Test PurchaseOrderReportDetailView (mimics ASP.NET original page) ---
    def test_detail_view_get_success(self):
        """Test accessing the detail view with all required query parameters."""
        url = reverse('purchaseorderreport_view_print_details')
        params = {
            'pono': 'PO-Test',
            'Code': 'SUP-Test',
            'parentPage': '/test/parent/',
            'mid': '12',
            'AmdNo': 'Amd-X',
            'Key': 'Key-Y'
        }
        response = self.client.get(url, params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/detail.html')
        self.assertIn('report_params', response.context)
        self.assertEqual(response.context['report_params'].po_no, 'PO-Test')
        self.assertEqual(response.context['report_params'].supplier_code, 'SUP-Test')
        self.assertIn('iframe_src', response.context)
        self.assertContains(response, 'PO - Print Details')
        self.assertContains(response, '<iframe')

    def test_detail_view_get_missing_parameters(self):
        """Test accessing the detail view with missing required query parameters."""
        url = reverse('purchaseorderreport_view_print_details')
        # Missing 'pono'
        params = {
            'Code': 'SUP-Test',
            'parentPage': '/test/parent/',
        }
        response = self.client.get(url, params)
        self.assertEqual(response.status_code, 404) # Expects Http404

    def test_detail_view_post_cancel_htmx(self):
        """Test the POST request for the 'Cancel' button with HTMX."""
        url = reverse('purchaseorderreport_view_print_details')
        post_data = {
            'parent_page_url': '/mock/cancel/destination/',
            'supplier_code': 'SUP-CANCEL',
            'module_id': '20',
            'sub_module_id': '50',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, post_data, **headers)
        self.assertEqual(response.status_code, 204)
        expected_redirect_url = '/mock/cancel/destination/?Code=SUP-CANCEL&ModId=20&SubModId=50'
        self.assertEqual(response['HX-Redirect'], expected_redirect_url)

    def test_detail_view_post_cancel_no_htmx(self):
        """Test the POST request for the 'Cancel' button without HTMX (standard form submission)."""
        url = reverse('purchaseorderreport_view_print_details')
        post_data = {
            'parent_page_url': '/mock/cancel/destination/',
            'supplier_code': 'SUP-CANCEL',
            'module_id': '20',
            'sub_module_id': '50',
        }
        response = self.client.post(url, post_data)
        self.assertEqual(response.status_code, 302) # Should redirect
        expected_redirect_url = '/mock/cancel/destination/?Code=SUP-CANCEL&ModId=20&SubModId=50'
        self.assertEqual(response.url, expected_redirect_url)

    # --- Test PurchaseOrderReportListView ---
    def test_list_view(self):
        """Test the list view displays correctly."""
        response = self.client.get(reverse('purchaseorderreport_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/list.html')
        self.assertIn('poreports', response.context)
        self.assertContains(response, 'Purchase Order Report Configurations')
        # Check if the created report is in the context
        self.assertIn(self.report, response.context['poreports'])

    # --- Test PurchaseOrderReportTablePartialView ---
    def test_table_partial_view(self):
        """Test the HTMX-loaded table partial view."""
        response = self.client.get(reverse('purchaseorderreport_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/_purchaseorderreport_table.html')
        self.assertIn('poreports', response.context)
        self.assertContains(response, self.report.po_no) # Check if data is present

    # --- Test PurchaseOrderReportCreateView ---
    def test_create_view_get(self):
        """Test GET request for the create form."""
        response = self.client.get(reverse('purchaseorderreport_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Report Configuration')

    def test_create_view_post_success(self):
        """Test POST request for creating a new report config."""
        create_data = {
            'po_no': 'PO-NEW-001',
            'supplier_code': 'SUP-NEW',
            'parent_page_url': '/new/list/',
            'module_id': 15,
            'sub_module_id': 45,
        }
        response = self.client.post(reverse('purchaseorderreport_add'), create_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertTrue(PurchaseOrderReport.objects.filter(po_no='PO-NEW-001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderReportList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """Test POST request for creating with invalid data."""
        invalid_data = {
            'supplier_code': 'SUP-INVALID', # Missing po_no, parent_page_url
        }
        response = self.client.post(reverse('purchaseorderreport_add'), invalid_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX swaps form back with errors
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertContains(response, 'Purchase Order Number is required.')

    # --- Test PurchaseOrderReportUpdateView ---
    def test_update_view_get(self):
        """Test GET request for the update form."""
        response = self.client.get(reverse('purchaseorderreport_edit', args=[self.report.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, self.report.po_no) # Check if initial data is loaded

    def test_update_view_post_success(self):
        """Test POST request for updating an existing report config."""
        update_data = {
            'po_no': 'PO-UPDATED',
            'supplier_code': self.report.supplier_code,
            'parent_page_url': self.report.parent_page_url,
            'module_id': self.report.module_id,
            'sub_module_id': self.report.sub_module_id,
            'amendment_no': 'Updated-Amd',
            'key_value': self.report.key_value,
        }
        response = self.client.post(reverse('purchaseorderreport_edit', args=[self.report.pk]), update_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.report.refresh_from_db()
        self.assertEqual(self.report.po_no, 'PO-UPDATED')
        self.assertEqual(self.report.amendment_no, 'Updated-Amd')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderReportList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        """Test POST request for updating with invalid data."""
        update_data = {
            'po_no': '', # Invalid data: empty PO No
            'supplier_code': self.report.supplier_code,
            'parent_page_url': self.report.parent_page_url,
            'module_id': self.report.module_id,
            'sub_module_id': self.report.sub_module_id,
        }
        response = self.client.post(reverse('purchaseorderreport_edit', args=[self.report.pk]), update_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertContains(response, 'Purchase Order Number is required.')

    # --- Test PurchaseOrderReportDeleteView ---
    def test_delete_view_get(self):
        """Test GET request for delete confirmation form."""
        response = self.client.get(reverse('purchaseorderreport_delete', args=[self.report.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'poreports/purchaseorderreport/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.report)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post_success(self):
        """Test POST request for deleting a report config."""
        response = self.client.post(reverse('purchaseorderreport_delete', args=[self.report.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(PurchaseOrderReport.objects.filter(pk=self.report.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderReportList', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

HTMX and Alpine.js are integral to the modern Django frontend experience, ensuring dynamic and responsive interactions without complex JavaScript frameworks.

-   **HTMX for All Dynamic Interactions:**
    -   **List View (`list.html`):** The `purchaseorderreportTable-container` `div` uses `hx-trigger="load, refreshPurchaseOrderReportList from:body"` and `hx-get="{% url 'purchaseorderreport_table' %}"` to dynamically load the DataTables content on page load and whenever a `refreshPurchaseOrderReportList` custom event is triggered (e.g., after a CRUD operation).
    -   **CRUD Modals (`list.html`, `form.html`, `confirm_delete.html`):**
        -   "Add New", "Edit", "Delete" buttons in `list.html` use `hx-get` to load the respective form/confirmation partials into the `#modalContent` target.
        -   Form submissions (`form.html`, `confirm_delete.html`) use `hx-post` to send data back to the server. On successful submission, the server responds with `status=204` (No Content) and an `HX-Trigger: refreshPurchaseOrderReportList` header. This tells HTMX to trigger the refresh event, which in turn causes the table to reload and update, closing the modal in the process.
        -   The "Cancel" button in forms (and the modal's `on click` outside) uses Alpine.js-like `_` syntax (`on click remove .is-active from #modal`) to simply hide the modal without a server roundtrip.
    -   **`PurchaseOrderReportDetailView` Cancel Button:** The "Cancel" button in `detail.html` uses `hx-post` to send relevant parameters back to the server. The server responds with `HX-Redirect` header, which HTMX interprets to perform a client-side page navigation to the specified URL.
    -   **Loading Indicators:** `htmx-indicator` classes are used (e.g., `<span class="htmx-indicator">...</span>`) to show a loading spinner automatically when an HTMX request is in progress, improving user feedback.

-   **Alpine.js for UI State Management:**
    -   The core modal behavior in `list.html` leverages Alpine.js-like `_` syntax directly within the HTML (`_="on click add .is-active to #modal"` for showing, and `_="on click if event.target.id == 'modal' remove .is-active from me"` for hiding on outside click). This keeps simple UI interactions self-contained and efficient.
    -   While full Alpine.js `x-data` components are not explicitly used for this simple modal, the pattern is set. For more complex client-side state (e.g., dynamic form fields, interactive filters beyond DataTables), Alpine.js would be fully integrated.

-   **DataTables for List Views:**
    -   The `_purchaseorderreport_table.html` partial uses `id="purchaseorderreportTable"`.
    -   A `script` block within this partial initializes DataTables on this table `$('#purchaseorderreportTable').DataTable(...)`. Crucially, `destroy: true` is added to ensure that when HTMX re-swaps the table content, the old DataTable instance is properly cleaned up before a new one is initialized.
    -   This provides client-side searching, sorting, and pagination for the list of report configurations.

-   **DRY Template Inheritance:**
    -   All templates (`list.html`, `detail.html`, `form.html`, `confirm_delete.html`, `_purchaseorderreport_table.html`) start with `{% extends 'core/base.html' %}`. This ensures that the global layout, CDN links for Tailwind CSS, HTMX, Alpine.js, and DataTables (as per `base.html` in your setup) are consistently applied without duplication.
    -   `{% block content %}` and `{% block extra_js %}` are used to inject module-specific content and JavaScript/Alpine.js initialization without altering the base structure.

### Final Notes

-   **Placeholders:** `[APP_NAME]` is `poreports`, `[MODEL_NAME]` is `PurchaseOrderReport`, `[MODEL_NAME_LOWER]` is `purchaseorderreport`, `[MODEL_NAME_PLURAL_LOWER]` is `poreports`.
-   **Business Logic in Models:** The `PurchaseOrderReport` model now contains methods like `get_report_iframe_url()` and `get_cancel_redirect_url()`, centralizing the logic for constructing dynamic URLs, keeping views 'thin' and focused on handling requests and responses.
-   **Comprehensive Tests:** The provided `tests.py` includes unit tests for the model's methods and integration tests for all view types (list, create, update, delete, and the specific detail/report view), ensuring high code quality and confidence in the migration.
-   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes (e.g., `bg-blue-600`, `px-4`, `rounded-md`, `shadow-md`) for modern, utility-first styling.
-   **Future Enhancements:** The `iframe` approach is a direct translation. In a more advanced Django system, you might consider generating the report content (e.g., PDF) directly within a Django view (using libraries like `reportlab` or `WeasyPrint`) and serving it, or integrating with a dedicated report generation service, eliminating the need for an external "report page."