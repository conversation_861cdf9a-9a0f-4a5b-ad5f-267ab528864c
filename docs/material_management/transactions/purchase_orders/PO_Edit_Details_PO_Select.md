This document outlines a comprehensive modernization plan to transition your legacy ASP.NET application, specifically the PO Detail editing module, to a modern Django-based solution. Our approach prioritizes automation, leverages the strengths of Django's architecture, and focuses on delivering a user-friendly, efficient, and maintainable system.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The ASP.NET page `PO_Edit_Details_PO_Select.aspx` allows a user to view an existing Purchase Order (PO) detail and submit an *amendment* to a temporary table (`tblMM_PO_Amd_Temp`). It's not a standard direct update. We will treat this as an "Amendment Creation" form, pre-filled with existing PO details.

### Step 1: Extract Database Schema

**Business Interpretation:** This step focuses on understanding how your existing business information is stored. We're looking at the 'tables' where data like Purchase Orders, items, and related terms are kept. This helps us map your current data structure to the new system.

**Instructions:**
From the ASP.NET code, we identify the following SQL Server tables and their implied relationships and columns. Note that many fields are used for displaying information from related tables, while only a subset is editable.

**Main Data Source (for displaying current PO Detail):**
*   **`tblMM_PO_Details`** (Primary focus of the amendment)
    *   `Id` (PK)
    *   `PONo`
    *   `PRNo`
    *   `PRId`
    *   `SPRNo`
    *   `SPRId`
    *   `Qty` (Current Quantity)
    *   `Rate` (Current Rate)
    *   `Discount` (Current Discount)
    *   `PF` (Foreign Key to `tblPacking_Master.Id`)
    *   `ExST` (Foreign Key to `tblExciseser_Master.Id`)
    *   `VAT` (Foreign Key to `tblVAT_Master.Id`)
    *   `AddDesc` (Current Additional Description)
    *   `DelDate` (Current Delivery Date)
    *   `BudgetCode` (Current Budget Code, FK to `tblMIS_BudgetCode.Id`)
    *   `MId` (Foreign Key to `tblMM_PO_Master.Id`)

**Target Table (for saving amendments):**
*   **`tblMM_PO_Amd_Temp`** (Where the *amended* data is saved)
    *   `Id` (PK, auto-incremented)
    *   `CompId` (Company ID, inferred from Session)
    *   `SessionId` (User session ID, inferred from Session)
    *   `PONo`
    *   `POId` (Master PO ID, i.e., `MId` from URL)
    *   `Qty` (Amended Quantity)
    *   `Rate` (Amended Rate)
    *   `Discount` (Amended Discount)
    *   `AddDesc` (Amended Additional Description)
    *   `PF` (Amended Packing & Forwarding)
    *   `VAT` (Amended VAT)
    *   `ExST` (Amended Excise/Service Tax)
    *   `AHId` (Account Head ID, inferred from `LoadData`)
    *   `DelDate` (Amended Delivery Date)
    *   `BudgetCode` (Amended Budget Code)
    *   `PODId` (PO Detail ID, i.e., `Id` from `tblMM_PO_Details`)
    *   `RateFlag` (Optional flag '1' if rate was overridden despite lock)

**Supporting Lookup/Related Tables (used for display or dropdowns):**
*   **`tblMM_PO_Master`**
    *   `Id` (PK)
    *   `PONo`
    *   `PRSPRFlag` (`0` for PR, `1` for SPR)
    *   `CompId`
*   **`tblMM_PR_Details`**
    *   `Id` (PK)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `WONo`
    *   `AHId` (FK to `AccHead.Id`)
    *   `PRNo`
    *   `MId` (FK to `tblMM_PR_Master.Id`)
*   **`tblMM_PR_Master`**
    *   `Id` (PK)
    *   `PRNo`
    *   `CompId`
*   **`tblMM_SPR_Details`**
    *   `Id` (PK)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `WONo`
    *   `DeptId` (FK to `BusinessGroup.Id`)
    *   `AHId` (FK to `AccHead.Id`)
    *   `SPRNo`
    *   `MId` (FK to `tblMM_SPR_Master.Id`)
*   **`tblMM_SPR_Master`**
    *   `Id` (PK)
    *   `SPRNo`
    *   `CompId`
*   **`tblDG_Item_Master`**
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc`
    *   `UOMBasic` (FK to `Unit_Master.Id`)
    *   `CompId`
*   **`Unit_Master`**
    *   `Id` (PK)
    *   `Symbol`
*   **`tblMIS_BudgetCode`**
    *   `Id` (PK)
    *   `Symbol`
*   **`BusinessGroup`**
    *   `Id` (PK)
    *   `Symbol`
    *   `Name`
*   **`AccHead`**
    *   `Id` (PK)
    *   `Symbol`
    *   `Description`
*   **`tblPacking_Master`**
    *   `Id` (PK)
    *   `Terms`
*   **`tblExciseser_Master`**
    *   `Id` (PK)
    *   `Terms`
*   **`tblVAT_Master`**
    *   `Id` (PK)
    *   `Terms`
*   **`tblMM_Rate_Register`**
    *   `ItemId`
    *   `CompId`
    *   `Rate`
    *   `Discount`
*   **`tblMM_RateLockUnLock_Master`**
    *   `ItemId`
    *   `CompId`
    *   `LockUnlock`
    *   `Type`

### Step 2: Identify Backend Functionality

**Business Interpretation:** This step helps us understand the 'actions' your application performs, like saving changes or retrieving information. We're looking at what happens behind the scenes when a user interacts with the page.

**Instructions:**
The primary functionality is to **Retrieve** existing Purchase Order Detail data and related information to populate the form. The "Add" button's action is to **Create** a new record in a *temporary amendment table*, not directly update the original PO detail. This is a "staging" or "amendment proposal" functionality.

*   **Read (Load Data):**
    *   Fetches a specific `tblMM_PO_Details` record based on `pono`, `mid`, `poid`, and `CompId` from URL query parameters.
    *   Conditionally fetches additional details from `tblMM_PR_Details`/`tblMM_PR_Master` (if `PRSPRFlag` is '0') or `tblMM_SPR_Details`/`tblMM_SPR_Master` (if `PRSPRFlag` is '1'). This determines fields like `ItemCode`, `Item Description`, `WO No`, `Dept`, `Account Head`.
    *   Retrieves dropdown options for `P&F`, `Excise/Service Tax`, `VAT`, and `Budget Code` from their respective master tables.
    *   Retrieves minimum discounted rate from `tblMM_Rate_Register` for rate validation.

*   **Create (Amend Detail):**
    *   The `btnProcide_Click` handler, despite being on an "Edit" page, performs an `INSERT` operation into `tblMM_PO_Amd_Temp`. This suggests a workflow where amendments are first recorded in a temporary holding area before potentially being applied to the main `tblMM_PO_Details`.
    *   This action involves data validation:
        *   Required fields: Quantity, Rate, Discount, Delivery Date.
        *   Format validation: Numeric for Qty, Rate, Discount; Date format for Delivery Date.
        *   Business logic: Checks the entered `Rate` against a minimum `Rate` stored in `tblMM_Rate_Register` and `tblMM_RateLockUnLock_Master` to enforce pricing policies. If the entered rate is lower than the minimum allowed (and not unlocked), it rejects the submission.

*   **Navigation:**
    *   On successful amendment or cancellation, the page redirects to `PO_Edit_Details_PO_Grid.aspx`.

### Step 3: Infer UI Components

**Business Interpretation:** This step focuses on understanding how users interact with the application. We look at the different parts of the screen—like text boxes, dropdowns, and buttons—to see how they gather or display information.

**Instructions:**
The ASP.NET page is primarily an "edit" form for a single PO detail record.

*   **Display Fields (Labels):**
    *   `PR No`, `SPR No`, `WO No`, `Dept`, `Item Code`, `Item Description`, `Ac Head`, `Qty` (current), `UOM`. These are read-only and display information fetched from the database.
*   **Input Fields (Textboxes):**
    *   `Qty` (for entering amended quantity)
    *   `Rate` (for entering amended rate)
    *   `Discount` (for entering amended discount)
    *   `Additional Desc` (for entering additional notes)
    *   `Del. Date` (for entering amended delivery date, with a date picker).
*   **Dropdowns:**
    *   `Budget Code` (for selecting an amended budget code, conditionally visible/editable)
    *   `P & F` (Packing and Forwarding terms)
    *   `Excies / Service Tax`
    *   `VAT`
*   **Action Buttons:**
    *   `Add` (to submit the amendment to the temporary table)
    *   `Cancel` (to abandon changes and return to the grid).
*   **Client-Side Validation:** The page uses ASP.NET validators for required fields and regular expressions. These will be translated to Django form validation and potentially Alpine.js for real-time feedback.
*   **Rate Register Link:** A small image link (`rt`) to a "Rate Register" report, which is context-sensitive to the item being edited.

### Step 4: Generate Django Code

**Business Interpretation:** This is where we translate the blueprint into a working application. We're creating the digital 'forms' (models), the 'rules' for handling data (forms), the 'actions' that happen when you click a button (views), and what the user 'sees' (templates). All these parts work together to create the new system.

**App Name:** `material_management` (following the ASP.NET module structure)

#### 4.1 Models (`material_management/models.py`)

**Business Interpretation:** These are the 'blueprints' for how information is structured in our new database. Each model represents a type of data, like a Purchase Order or an Item, and defines what pieces of information it contains.

```python
from django.db import models

# Base Model for common fields like Company ID, if needed across many tables
# Assuming a Company model exists, or just use IntegerField for CompId
class Company(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=255)
    # Add other company-related fields
    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Example table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
    def __str__(self):
        return self.name

# Supporting Lookup/Related Models (read-only for dropdowns and display)
class UnitMaster(models.Model):
    id = models.IntegerField(primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'
    def __str__(self):
        return self.symbol

class BudgetCode(models.Model):
    id = models.IntegerField(primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    # Inferred: could have a 'description' or 'name' field if Symbol + WONo is not the only source
    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'
    def __str__(self):
        return self.symbol # Or combined with WO No in a property if possible

class BusinessGroup(models.Model):
    id = models.IntegerField(primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='Name', max_length=255) # Department Name
    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'
    def __str__(self):
        return f"[{self.symbol}] {self.name}"

class AccountHead(models.Model):
    id = models.IntegerField(primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)
    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'
    def __str__(self):
        return f"[{self.symbol}] {self.description}"

class PackingTerm(models.Model):
    id = models.IntegerField(primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'
    def __str__(self):
        return self.terms

class ExciseServiceTaxTerm(models.Model):
    id = models.IntegerField(primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service Tax Term'
        verbose_name_plural = 'Excise Service Tax Terms'
    def __str__(self):
        return self.terms

class VATTerm(models.Model):
    id = models.IntegerField(primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'
    def __str__(self):
        return self.terms

class ItemMaster(models.Model):
    id = models.IntegerField(primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.TextField(db_column='ManfDesc')
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'
    def __str__(self):
        return self.item_code

class PurchaseRequestMaster(models.Model):
    id = models.IntegerField(primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=255)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'Purchase Request Master'
        verbose_name_plural = 'Purchase Request Masters'
    def __str__(self):
        return self.pr_no

class PurchaseRequestDetail(models.Model):
    id = models.IntegerField(primary_key=True)
    master = models.ForeignKey(PurchaseRequestMaster, models.DO_NOTHING, db_column='MId')
    pr_no = models.CharField(db_column='PRNo', max_length=255)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    wo_no = models.CharField(db_column='WONo', max_length=255)
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId')
    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'Purchase Request Detail'
        verbose_name_plural = 'Purchase Request Details'
    def __str__(self):
        return f"{self.pr_no} - {self.item.item_code}"

class StoreRequisitionMaster(models.Model):
    id = models.IntegerField(primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=255)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'Store Requisition Master'
        verbose_name_plural = 'Store Requisition Masters'
    def __str__(self):
        return self.spr_no

class StoreRequisitionDetail(models.Model):
    id = models.IntegerField(primary_key=True)
    master = models.ForeignKey(StoreRequisitionMaster, models.DO_NOTHING, db_column='MId')
    spr_no = models.CharField(db_column='SPRNo', max_length=255)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    wo_no = models.CharField(db_column='WONo', max_length=255)
    department = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', null=True, blank=True)
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId')
    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'Store Requisition Detail'
        verbose_name_plural = 'Store Requisition Details'
    def __str__(self):
        return f"{self.spr_no} - {self.item.item_code}"

class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=255)
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1) # '0' or '1'
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'
    def __str__(self):
        return self.po_no

class PurchaseOrderDetail(models.Model):
    # This model represents the existing PO Detail data being displayed/amended
    id = models.IntegerField(primary_key=True)
    po_master = models.ForeignKey(PurchaseOrderMaster, models.DO_NOTHING, db_column='MId')
    po_no = models.CharField(db_column='PONo', max_length=255)
    pr_no = models.CharField(db_column='PRNo', max_length=255, null=True, blank=True)
    pr_id = models.IntegerField(db_column='PRId', null=True, blank=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=255, null=True, blank=True)
    spr_id = models.IntegerField(db_column='SPRId', null=True, blank=True)
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=5)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)
    packing_forwarding = models.ForeignKey(PackingTerm, models.DO_NOTHING, db_column='PF', null=True, blank=True)
    excise_service_tax = models.ForeignKey(ExciseServiceTaxTerm, models.DO_NOTHING, db_column='ExST', null=True, blank=True)
    vat = models.ForeignKey(VATTerm, models.DO_NOTHING, db_column='VAT', null=True, blank=True)
    additional_description = models.TextField(db_column='AddDesc', null=True, blank=True)
    delivery_date = models.DateField(db_column='DelDate', null=True, blank=True)
    budget_code_fk = models.ForeignKey(BudgetCode, models.DO_NOTHING, db_column='BudgetCode', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"PO No: {self.po_no}, Item: {self.id}" # Placeholder, as actual item link is indirect

    @classmethod
    def get_full_po_detail_data(cls, po_master_id, po_detail_id, company_id):
        """
        Retrieves comprehensive PO detail data similar to ASP.NET LoadData.
        This method encapsulates the complex data fetching logic.
        """
        try:
            po_detail = cls.objects.select_related(
                'po_master', 'packing_forwarding', 'excise_service_tax', 'vat', 'budget_code_fk'
            ).get(
                id=po_detail_id,
                po_master__id=po_master_id,
                po_master__company_id=company_id
            )

            context_data = {
                'po_detail': po_detail,
                'pr_no': 'NA',
                'spr_no': 'NA',
                'item_code': '',
                'item_description': '',
                'uom_symbol': '',
                'wo_no': '',
                'department_name': '',
                'account_head_display': '',
                'item_id': None, # To pass to rate register link
                'budget_code_options': [],
                'budget_code_label': ''
            }

            # Mimic PRSPRFlag logic
            if po_detail.po_master.pr_spr_flag == '0': # PR related
                pr_details = PurchaseRequestDetail.objects.select_related(
                    'item__uom_basic', 'item__company', 'account_head'
                ).filter(
                    pr_no=po_detail.pr_no,
                    master__id=po_detail.pr_id, # This seems incorrect based on original code,
                                                 # original was based on PRId, not MId for PR_Details
                                                 # Correcting based on typical FK usage:
                    master__pr_no=po_detail.pr_no,
                    master__company_id=company_id
                ).first()

                if pr_details:
                    context_data['item_id'] = pr_details.item.id
                    context_data['pr_no'] = pr_details.pr_no
                    context_data['wo_no'] = pr_details.wo_no
                    context_data['account_head_display'] = str(pr_details.account_head)
                    context_data['item_code'] = pr_details.item.item_code
                    context_data['item_description'] = pr_details.item.manf_desc
                    context_data['uom_symbol'] = pr_details.item.uom_basic.symbol
                    context_data['department_name'] = 'NA'

                    # Fetch budget codes based on WO No for PR
                    budget_codes = BudgetCode.objects.filter(
                        purchaserequestdetail__wo_no=pr_details.wo_no,
                        purchaserequestdetail__master__company_id=company_id
                    ).distinct().values('id', 'symbol')
                    context_data['budget_code_options'] = [{'id': bc['id'], 'symbol': f"{bc['symbol']}{pr_details.wo_no}"} for bc in budget_codes]
                    context_data['budget_code_label'] = '' # Dropdown visible

            elif po_detail.po_master.pr_spr_flag == '1': # SPR related
                spr_details = StoreRequisitionDetail.objects.select_related(
                    'item__uom_basic', 'item__company', 'department', 'account_head'
                ).filter(
                    spr_no=po_detail.spr_no,
                    master__id=po_detail.spr_id, # Similar correction for SPR_Details
                    master__spr_no=po_detail.spr_no,
                    master__company_id=company_id
                ).first()

                if spr_details:
                    context_data['item_id'] = spr_details.item.id
                    context_data['spr_no'] = spr_details.spr_no
                    context_data['account_head_display'] = str(spr_details.account_head)
                    context_data['item_code'] = spr_details.item.item_code
                    context_data['item_description'] = spr_details.item.manf_desc
                    context_data['uom_symbol'] = spr_details.item.uom_basic.symbol

                    if spr_details.department_id == 0 or spr_details.department_id is None: # WO based
                        context_data['wo_no'] = spr_details.wo_no
                        context_data['department_name'] = 'NA'
                        # Fetch budget codes based on WO No for SPR
                        budget_codes = BudgetCode.objects.filter(
                            storerequisitiondetail__wo_no=spr_details.wo_no,
                            storerequisitiondetail__master__company_id=company_id
                        ).distinct().values('id', 'symbol')
                        context_data['budget_code_options'] = [{'id': bc['id'], 'symbol': f"{bc['symbol']}{spr_details.wo_no}"} for bc in budget_codes]
                        context_data['budget_code_label'] = '' # Dropdown visible
                    else: # Department based
                        context_data['department_name'] = str(spr_details.department)
                        context_data['wo_no'] = 'NA'
                        context_data['budget_code_label'] = spr_details.department.symbol # Label visible, dropdown hidden
                        context_data['budget_code_options'] = [] # No dropdown options

            return context_data
        except cls.DoesNotExist:
            return None # Or raise Http404

class RateRegister(models.Model):
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=5)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)
    # Add other fields as needed
    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'
    def __str__(self):
        return f"Item: {self.item.item_code}, Rate: {self.rate}"

    @property
    def discounted_rate(self):
        return self.rate - (self.rate * self.discount / 100)

    @classmethod
    def get_min_discounted_rate(cls, item_id, company_id):
        """Calculates the minimum discounted rate for an item."""
        min_rate = cls.objects.filter(
            item_id=item_id,
            company_id=company_id
        ).annotate(
            dis_rate=models.ExpressionWrapper(
                models.F('Rate') - (models.F('Rate') * models.F('Discount') / 100),
                output_field=models.DecimalField(max_digits=18, decimal_places=2)
            )
        ).aggregate(min_dis_rate=models.Min('dis_rate'))['min_dis_rate']
        return min_rate if min_rate is not None else 0.0


class RateLockUnlock(models.Model):
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    lock_unlock = models.BooleanField(db_column='LockUnlock') # True for unlocked, False for locked
    type = models.CharField(db_column='Type', max_length=10) # '2' for specific type
    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock/Unlock'
        verbose_name_plural = 'Rate Locks/Unlocks'
    def __str__(self):
        return f"Item: {self.item.item_code}, Locked: {not self.lock_unlock}"

    @classmethod
    def is_rate_unlocked(cls, item_id, company_id, type_code='2'):
        """Checks if the rate for a given item is unlocked."""
        return cls.objects.filter(
            item_id=item_id,
            company_id=company_id,
            lock_unlock=True,
            type=type_code
        ).exists()

class PurchaseOrderAmendmentTemp(models.Model):
    # This model is specifically for the form's target submission
    # ID is auto-incremented, so we don't declare it if it's identity in DB
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Or ForeignKey to User model if user is linked
    po_no = models.CharField(db_column='PONo', max_length=255)
    po_master_id = models.IntegerField(db_column='POId') # Master PO ID (MId from original query string)
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=5)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)
    additional_description = models.TextField(db_column='AddDesc', null=True, blank=True)
    packing_forwarding = models.ForeignKey(PackingTerm, models.DO_NOTHING, db_column='PF', null=True, blank=True)
    vat = models.ForeignKey(VATTerm, models.DO_NOTHING, db_column='VAT', null=True, blank=True)
    excise_service_tax = models.ForeignKey(ExciseServiceTaxTerm, models.DO_NOTHING, db_column='ExST', null=True, blank=True)
    account_head_id = models.IntegerField(db_column='AHId') # Account Head ID (integer directly from LoadData)
    delivery_date = models.DateField(db_column='DelDate')
    budget_code = models.ForeignKey(BudgetCode, models.DO_NOTHING, db_column='BudgetCode', null=True, blank=True)
    po_detail_id = models.IntegerField(db_column='PODId') # Refers to PurchaseOrderDetail.id (poid from original query string)
    rate_flag = models.CharField(db_column='RateFlag', max_length=1, null=True, blank=True) # '1' if overridden

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Amd_Temp'
        verbose_name = 'Purchase Order Amendment Temp'
        verbose_name_plural = 'Purchase Order Amendment Temps'

    def __str__(self):
        return f"Temp Amendment for PO Detail {self.po_detail_id}"
```

#### 4.2 Forms (`material_management/forms.py`)

**Business Interpretation:** This is like creating the actual paper forms for your business. It defines what fields a user can fill in (like quantity or rate), what kind of information is expected in each field, and ensures that the data meets certain rules (like making sure a number is actually a number).

```python
from django import forms
from .models import PurchaseOrderAmendmentTemp, PackingTerm, ExciseServiceTaxTerm, VATTerm, BudgetCode
from django.core.validators import RegexValidator
from decimal import Decimal, InvalidOperation

class PurchaseOrderDetailAmendmentForm(forms.Form):
    # These fields are for user input, matching the ASP.NET editable fields
    quantity = forms.DecimalField(
        max_digits=18, decimal_places=5, required=True,
        widget=forms.TextInput(attrs={'class': 'box3', 'type': 'number', 'step': 'any'}),
        validators=[RegexValidator(r'^\d{1,15}(\.\d{0,5})?$', 'Quantity must be a number with up to 15 digits and 5 decimal places.')]
    )
    rate = forms.DecimalField(
        max_digits=18, decimal_places=5, required=True,
        widget=forms.TextInput(attrs={'class': 'box3', 'type': 'number', 'step': 'any'}),
        validators=[RegexValidator(r'^\d{1,15}(\.\d{0,5})?$', 'Rate must be a number with up to 15 digits and 5 decimal places.')]
    )
    discount = forms.DecimalField(
        max_digits=18, decimal_places=2, required=True, initial=Decimal('0.00'),
        widget=forms.TextInput(attrs={'class': 'box3', 'type': 'number', 'step': 'any'}),
        validators=[RegexValidator(r'^\d{1,15}(\.\d{0,2})?$', 'Discount must be a number with up to 15 digits and 2 decimal places.')]
    )
    additional_description = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'cols': 30}),
        required=False
    )
    packing_forwarding = forms.ModelChoiceField(
        queryset=PackingTerm.objects.all(),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    excise_service_tax = forms.ModelChoiceField(
        queryset=ExciseServiceTaxTerm.objects.all(),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    vat = forms.ModelChoiceField(
        queryset=VATTerm.objects.all(),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )
    delivery_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'box3', 'type': 'date'}), # HTML5 date input
        required=True,
        # ASP.NET regex for date (dd-MM-yyyy): ^([1-9]|0[1-9]|[12][0-9]|3[01])[- /.]([1-9]|0[1-9]|1[012])[- /.][0-9]{4}$
        # Django's DateInput provides better validation for type="date"
    )
    budget_code = forms.ModelChoiceField(
        queryset=BudgetCode.objects.all(),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'})
    )

    # Hidden fields to carry context from the original PO Detail
    po_no = forms.CharField(widget=forms.HiddenInput(), required=True)
    po_master_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    po_detail_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    account_head_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=True) # Used for rate register logic
    company_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)

    def __init__(self, *args, **kwargs):
        self.min_allowed_rate = None
        self.is_rate_unlocked = False
        self.budget_code_dropdown_visible = True
        self.budget_code_label_text = ''
        super().__init__(*args, **kwargs)
        # Apply Tailwind CSS classes to all fields
        for field_name, field in self.fields.items():
            if field_name not in ['po_no', 'po_master_id', 'po_detail_id', 'account_head_id', 'item_id', 'company_id']:
                current_classes = field.widget.attrs.get('class', '')
                field.widget.attrs['class'] = f"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm {current_classes}"

    def clean(self):
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity')
        rate = cleaned_data.get('rate')
        discount = cleaned_data.get('discount')
        item_id = cleaned_data.get('item_id')
        company_id = cleaned_data.get('company_id')

        # Business logic for Rate Validation (as in ASP.NET code-behind)
        # If rate, discount, item_id, company_id are present, perform the check
        if all([rate is not None, discount is not None, item_id is not None, company_id is not None]):
            try:
                calculated_rate = rate - (rate * discount / Decimal(100))
                self.min_allowed_rate = PurchaseOrderDetail.get_min_discounted_rate(item_id, company_id)
                self.is_rate_unlocked = PurchaseOrderDetail.is_rate_unlocked(item_id, company_id)

                if self.min_allowed_rate is not None and self.min_allowed_rate > 0:
                    if calculated_rate < self.min_allowed_rate:
                        if not self.is_rate_unlocked:
                            self.add_error('rate', 'Entered rate is not acceptable!')
                elif calculated_rate <= 0: # If min_allowed_rate is 0 or no rate registered, check if entered rate is positive
                    self.add_error('rate', 'Entered rate must be positive!')

            except InvalidOperation:
                self.add_error('rate', 'Invalid rate or discount value.')

        return cleaned_data

    def save_amendment(self, request_user, **kwargs):
        """
        Creates and saves a new PurchaseOrderAmendmentTemp record based on form data.
        This mimics the ASP.NET btnProcide_Click functionality inserting into tblMM_PO_Amd_Temp.
        """
        if not self.is_valid():
            raise ValueError("Form is not valid, cannot save amendment.")

        cleaned_data = self.cleaned_data

        rate_flag_value = None
        # Determine RateFlag if rate was overridden and unlocked
        if self.min_allowed_rate is not None and self.min_allowed_rate > 0:
            calculated_rate = cleaned_data['rate'] - (cleaned_data['rate'] * cleaned_data['discount'] / Decimal(100))
            if calculated_rate < self.min_allowed_rate and self.is_rate_unlocked:
                rate_flag_value = '1'

        amendment = PurchaseOrderAmendmentTemp(
            company_id=cleaned_data['company_id'],
            session_id=str(request_user.username), # Using username as SessionId
            po_no=cleaned_data['po_no'],
            po_master_id=cleaned_data['po_master_id'],
            quantity=cleaned_data['quantity'],
            rate=cleaned_data['rate'],
            discount=cleaned_data['discount'],
            additional_description=cleaned_data['additional_description'],
            packing_forwarding=cleaned_data['packing_forwarding'],
            vat=cleaned_data['vat'],
            excise_service_tax=cleaned_data['excise_service_tax'],
            account_head_id=cleaned_data['account_head_id'],
            delivery_date=cleaned_data['delivery_date'],
            budget_code=cleaned_data['budget_code'],
            po_detail_id=cleaned_data['po_detail_id'],
            rate_flag=rate_flag_value
        )
        amendment.save()
        return amendment
```

#### 4.3 Views (`material_management/views.py`)

**Business Interpretation:** Views are the 'actions' that happen when a user requests a page. For this page, it's about fetching the right PO details to display and then handling the submission of new amendment data. We keep these actions simple and efficient, relying on the 'models' for the complex business logic.

```python
from django.views.generic import FormView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import redirect
from .models import PurchaseOrderDetail, PackingTerm, ExciseServiceTaxTerm, VATTerm, BudgetCode, Company
from .forms import PurchaseOrderDetailAmendmentForm

# This view is for the "Edit Details" page which is an amendment creation form
class PurchaseOrderDetailAmendmentView(FormView):
    template_name = 'material_management/purchaseorderdetail_amendment_form.html'
    form_class = PurchaseOrderDetailAmendmentForm

    def get_success_url(self):
        # Redirect back to the PO_Edit_Details_PO_Grid.aspx equivalent
        # Assuming the grid URL needs these parameters: mid, code, pono
        mid = self.kwargs.get('mid')
        code = self.kwargs.get('code')
        pono = self.kwargs.get('pono')
        return reverse_lazy('material_management:po_details_grid', kwargs={'mid': mid, 'code': code, 'pono': pono})

    def get_initial(self):
        initial = super().get_initial()
        # Extract parameters from URL
        po_master_id = self.kwargs.get('mid')
        po_detail_id = self.kwargs.get('poid')
        po_no = self.kwargs.get('pono')
        company_id = self.request.session.get('compid') # Assuming compid is in session

        if not all([po_master_id, po_detail_id, po_no, company_id]):
            raise Http404("Missing required URL parameters or session company ID.")

        # Fetch all necessary data using the fat model method
        full_data = PurchaseOrderDetail.get_full_po_detail_data(
            po_master_id=po_master_id,
            po_detail_id=po_detail_id,
            company_id=company_id
        )

        if not full_data:
            raise Http404("Purchase Order Detail not found.")

        po_detail = full_data['po_detail']

        initial.update({
            'quantity': po_detail.quantity,
            'rate': po_detail.rate,
            'discount': po_detail.discount,
            'additional_description': po_detail.additional_description,
            'packing_forwarding': po_detail.packing_forwarding,
            'excise_service_tax': po_detail.excise_service_tax,
            'vat': po_detail.vat,
            'delivery_date': po_detail.delivery_date,
            'budget_code': po_detail.budget_code_fk,
            # Hidden context fields for amendment
            'po_no': po_no,
            'po_master_id': po_master_id,
            'po_detail_id': po_detail_id,
            'account_head_id': full_data['account_head_display'].split('[')[1].split(']')[0] if full_data['account_head_display'] else None, # Extract ID from symbol
            'item_id': full_data['item_id'],
            'company_id': company_id,
        })
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetch display data again, or pass from get_initial for consistency
        po_master_id = self.kwargs.get('mid')
        po_detail_id = self.kwargs.get('poid')
        po_no = self.kwargs.get('pono')
        company_id = self.request.session.get('compid')

        full_data = PurchaseOrderDetail.get_full_po_detail_data(
            po_master_id=po_master_id,
            po_detail_id=po_detail_id,
            company_id=company_id
        )
        if not full_data:
            raise Http404("Purchase Order Detail not found for context.")

        context.update(full_data) # Add all fetched data to context
        context['po_no_from_url'] = po_no # The actual PO no from URL, might be different from po_detail.po_no
        context['mid_from_url'] = po_master_id
        context['code_from_url'] = self.kwargs.get('code') # Pass 'code' for redirection
        context['poid_from_url'] = po_detail_id

        # Pass budget code display logic to template
        context['budget_code_dropdown_visible'] = self.form.budget_code_dropdown_visible # Assuming form is initialized
        context['budget_code_label_text'] = self.form.budget_code_label_text

        # For the rate register link
        context['rate_register_url'] = reverse_lazy(
            'material_management:rate_register_print',
            kwargs={'item_id': full_data['item_id'], 'company_id': company_id}
        )
        return context

    def form_valid(self, form):
        # The form's save_amendment method handles the insertion into the temp table
        try:
            form.save_amendment(self.request.user)
            messages.success(self.request, 'PO Detail amendment added successfully.')
            # HX-Redirect for full page redirect on success
            return HttpResponse(
                status=200,
                headers={'HX-Redirect': self.get_success_url()}
            )
        except Exception as e:
            messages.error(self.request, f"Error saving amendment: {e}")
            return self.form_invalid(form) # Re-render form with errors

    def form_invalid(self, form):
        # If the form is invalid, re-render the template with errors
        messages.error(self.request, 'Please correct the errors below.')
        return super().form_invalid(form)


# A dummy view for the grid redirect target
class PODetailsGridView(FormView): # Using FormView as a placeholder, could be ListView
    template_name = 'material_management/po_details_grid.html'
    def get(self, request, *args, **kwargs):
        messages.info(request, "This is a placeholder for the PO Details Grid.")
        return super().get(request, *args, **kwargs)

# Dummy view for Rate Register Print
class RateRegisterPrintView(FormView):
    template_name = 'material_management/rate_register_print.html'
    def get(self, request, *args, **kwargs):
        item_id = self.kwargs.get('item_id')
        company_id = self.kwargs.get('company_id')
        messages.info(request, f"This is a placeholder for Rate Register Print for Item ID: {item_id}, Company ID: {company_id}.")
        return super().get(request, *args, **kwargs)

```

#### 4.4 Templates (`material_management/templates/material_management/`)

**Business Interpretation:** These are the visual layouts for your web pages. They determine how information is arranged and presented to the user, ensuring a clean and consistent look across the application. We're also making them 'smart' so they can update parts of the page without reloading everything, making the system feel faster and more responsive.

*   `purchaseorderdetail_amendment_form.html` (main template for the edit page)
*   `_budget_code_field.html` (partial for conditional budget code display)
*   `po_details_grid.html` (placeholder for the target grid page)
*   `rate_register_print.html` (placeholder for the rate register report)

**`purchaseorderdetail_amendment_form.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Edit Purchase Order Detail (Amendment)</h2>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 rounded-md text-sm {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Left Column: Display fields -->
            <div class="space-y-3">
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">PR No</div>
                    <div class="w-2/3 text-gray-800">: {{ pr_no }}</div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">SPR No</div>
                    <div class="w-2/3 text-gray-800">: {{ spr_no }}</div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">WO No</div>
                    <div class="w-2/3 text-gray-800">: {{ wo_no }}</div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Dept</div>
                    <div class="w-2/3 text-gray-800">: {{ department_name }}</div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Item Code</div>
                    <div class="w-2/3 text-gray-800">: {{ item_code }}</div>
                </div>
                <div>
                    <div class="text-gray-600 font-medium mb-1">Item Description</div>
                    <textarea class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-800 sm:text-sm" rows="4" readonly>{{ item_description }}</textarea>
                </div>
            </div>

            <!-- Middle Column: Form fields -->
            <div class="space-y-4">
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Ac Head</div>
                    <div class="w-2/3 text-gray-800">: {{ account_head_display }}</div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Qty</div>
                    <div class="w-2/3 text-gray-800">
                        :<span class="text-gray-800">{{ po_detail.quantity|floatformat:3 }}</span>
                        {{ form.quantity }}
                        {% if form.quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>{% endif %}
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">UOM</div>
                    <div class="w-2/3 text-gray-800">: {{ uom_symbol }}</div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Rate</div>
                    <div class="w-2/3 text-gray-800">
                        :{{ form.rate }}
                        <a href="{{ rate_register_url }}" target="_blank" class="inline-block align-middle ml-1">
                            <img alt="Rate Register" src="https://via.placeholder.com/20x20?text=₹" border="0" class="w-5 h-5"/> {# Placeholder for Rupee.JPG #}
                        </a>
                        {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Discount</div>
                    <div class="w-2/3 text-gray-800">
                        :{{ form.discount }}<span class="ml-1">%</span>
                        {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Budget Code</div>
                    <div class="w-2/3 text-gray-800">
                        {% include 'material_management/_budget_code_field.html' with form=form budget_code_dropdown_visible=budget_code_dropdown_visible budget_code_label_text=budget_code_label_text %}
                        {% if form.budget_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.budget_code.errors }}</p>{% endif %}
                    </div>
                </div>
                <div>
                    <label for="{{ form.additional_description.id_for_label }}" class="block text-sm font-medium text-gray-700">Additional Desc</label>
                    {{ form.additional_description }}
                    {% if form.additional_description.errors %}<p class="text-red-500 text-xs mt-1">{{ form.additional_description.errors }}</p>{% endif %}
                </div>
            </div>

            <!-- Right Column: Form fields and buttons -->
            <div class="space-y-4">
                <div>
                    <label for="{{ form.packing_forwarding.id_for_label }}" class="block text-sm font-medium text-gray-700">P & F</label>
                    {{ form.packing_forwarding }}
                    {% if form.packing_forwarding.errors %}<p class="text-red-500 text-xs mt-1">{{ form.packing_forwarding.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.excise_service_tax.id_for_label }}" class="block text-sm font-medium text-gray-700">Excise / Service Tax</label>
                    {{ form.excise_service_tax }}
                    {% if form.excise_service_tax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.excise_service_tax.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.vat.id_for_label }}" class="block text-sm font-medium text-gray-700">VAT</label>
                    {{ form.vat }}
                    {% if form.vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vat.errors }}</p>{% endif %}
                </div>
                <div class="flex items-center">
                    <div class="w-1/3 text-gray-600 font-medium">Del. Date</div>
                    <div class="w-2/3 text-gray-800">
                        :{{ form.delivery_date }}
                        {% if form.delivery_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.delivery_date.errors }}</p>{% endif %}
                    </div>
                </div>
                <div class="pt-6 flex justify-end space-x-4">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md"
                            hx-post="." hx-swap="outerHTML" hx-target="#form-container">
                        Add Amendment
                    </button>
                    <a href="{% url 'material_management:po_details_grid' mid=mid_from_url code=code_from_url pono=po_no_from_url %}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-md">
                        Cancel
                    </a>
                </div>
            </div>
        </div>
        
        <form id="form-container" hx-post="." hx-swap="outerHTML">
            {% csrf_token %}
            {{ form.po_no }}
            {{ form.po_master_id }}
            {{ form.po_detail_id }}
            {{ form.account_head_id }}
            {{ form.item_id }}
            {{ form.company_id }}
            
            {# Form fields are rendered directly in the layout above #}
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js for any specific UI state management not covered by HTMX, if needed
    // Example: For date picker (flatpickr or similar)
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof flatpickr !== 'undefined') {
            flatpickr(document.querySelector('input[type="date"]'), {
                dateFormat: "Y-m-d", // Django expects YYYY-MM-DD
                altInput: true,
                altFormat: "d-m-Y", // Display as DD-MM-YYYY
            });
        }
    });

    document.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize datepickers after HTMX swap, if form is re-rendered
        if (event.detail.target.id === 'form-container') {
             if (typeof flatpickr !== 'undefined') {
                flatpickr(event.detail.target.querySelector('input[type="date"]'), {
                    dateFormat: "Y-m-d",
                    altInput: true,
                    altFormat: "d-m-Y",
                });
            }
        }
    });
</script>
{% endblock %}
```

**`_budget_code_field.html`** (Partial template for conditional budget code display)

```html
{% comment %}
    This partial template handles the conditional display of the Budget Code field,
    either as a read-only label or a dropdown, based on the business logic.
    It expects `form`, `budget_code_dropdown_visible`, and `budget_code_label_text` in context.
{% endcomment %}

{% if budget_code_dropdown_visible %}
    {{ form.budget_code }}
{% else %}
    <span class="text-gray-800">{{ budget_code_label_text }}</span>
    {{ form.budget_code.as_hidden }} {# Keep hidden field for form data consistency #}
{% endif %}
```

**`po_details_grid.html`** (Placeholder for the grid page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">PO Details Grid (Placeholder)</h2>
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 rounded-md text-sm {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
    <div class="bg-white p-6 rounded-lg shadow">
        <p class="text-gray-700">This page would display a DataTables grid of PO details for the current PO.</p>
        <p class="text-gray-700 mt-2">Parameters: mid={{ view.kwargs.mid }}, code={{ view.kwargs.code }}, pono={{ view.kwargs.pono }}</p>
        <a href="#" onclick="window.history.back();" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Go Back</a>
    </div>
</div>
{% endblock %}
```

**`rate_register_print.html`** (Placeholder for rate register report)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Rate Register Print (Placeholder)</h2>
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 rounded-md text-sm {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
    <div class="bg-white p-6 rounded-lg shadow">
        <p class="text-gray-700">This page would display the rate register for the selected item.</p>
        <p class="text-gray-700 mt-2">Item ID: {{ view.kwargs.item_id }}, Company ID: {{ view.kwargs.company_id }}</p>
        <a href="#" onclick="window.close();" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Close Window</a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`material_management/urls.py`)

**Business Interpretation:** This is like the 'address book' for your web application. It defines the unique web addresses for each page and action, making sure that when a user types in an address or clicks a link, they land on the correct part of the application.

```python
from django.urls import path
from .views import PurchaseOrderDetailAmendmentView, PODetailsGridView, RateRegisterPrintView

app_name = 'material_management'

urlpatterns = [
    # URL for editing/amending a specific PO Detail
    path(
        'po-amend/<int:mid>/<str:code>/<str:pono>/<int:poid>/',
        PurchaseOrderDetailAmendmentView.as_view(),
        name='po_detail_amendment'
    ),
    # Placeholder URL for the PO Details Grid
    path(
        'po-details-grid/<int:mid>/<str:code>/<str:pono>/',
        PODetailsGridView.as_view(),
        name='po_details_grid'
    ),
    # Placeholder URL for Rate Register Print
    path(
        'rate-register-print/<int:item_id>/<int:company_id>/',
        RateRegisterPrintView.as_view(),
        name='rate_register_print'
    ),
]
```

#### 4.6 Tests (`material_management/tests.py`)

**Business Interpretation:** This step is crucial for ensuring the new system works flawlessly. We write automated 'checks' for every part of the application – from how data is stored to how users interact with the pages. This helps us catch and fix any issues quickly, ensuring the system is reliable and performs as expected.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from decimal import Decimal
import datetime

# Import models for testing
from .models import (
    Company, UnitMaster, BudgetCode, BusinessGroup, AccountHead,
    PackingTerm, ExciseServiceTaxTerm, VATTerm, ItemMaster,
    PurchaseRequestMaster, PurchaseRequestDetail, StoreRequisitionMaster,
    StoreRequisitionDetail, PurchaseOrderMaster, PurchaseOrderDetail,
    RateRegister, RateLockUnlock, PurchaseOrderAmendmentTemp
)
from .forms import PurchaseOrderDetailAmendmentForm

class BaseTestData(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy company for all tests
        cls.company = Company.objects.create(id=1, name='Test Company')

        # Create dummy lookup data
        cls.unit = UnitMaster.objects.create(id=1, symbol='MTR')
        cls.budget_code_obj = BudgetCode.objects.create(id=1, symbol='BGT')
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='DEPT', name='Test Department')
        cls.account_head = AccountHead.objects.create(id=1, symbol='AH', description='Test Account Head')
        cls.packing_term = PackingTerm.objects.create(id=1, terms='By Air')
        cls.excise_tax = ExciseServiceTaxTerm.objects.create(id=1, terms='10% Excise')
        cls.vat_term = VATTerm.objects.create(id=1, terms='5% VAT')

        cls.item = ItemMaster.objects.create(
            id=101, item_code='ITEM001', manf_desc='Test Item Description',
            uom_basic=cls.unit, company=cls.company
        )

        # Create dummy PR/SPR data for conditional loading
        cls.pr_master = PurchaseRequestMaster.objects.create(id=1, pr_no='PR001', company=cls.company)
        cls.pr_detail = PurchaseRequestDetail.objects.create(
            id=1, master=cls.pr_master, pr_no='PR001', item=cls.item, wo_no='WO001',
            account_head=cls.account_head
        )

        cls.spr_master = StoreRequisitionMaster.objects.create(id=1, spr_no='SPR001', company=cls.company)
        cls.spr_detail_wo = StoreRequisitionDetail.objects.create(
            id=1, master=cls.spr_master, spr_no='SPR001', item=cls.item, wo_no='WO002',
            account_head=cls.account_head, department=None # WO based
        )
        cls.spr_detail_dept = StoreRequisitionDetail.objects.create(
            id=2, master=cls.spr_master, spr_no='SPR001', item=cls.item, wo_no='NA',
            account_head=cls.account_head, department=cls.business_group # Dept based
        )

        # Create dummy PO Master and Detail
        cls.po_master_pr = PurchaseOrderMaster.objects.create(
            id=1, po_no='PO001', pr_spr_flag='0', company=cls.company
        )
        cls.po_detail_pr = PurchaseOrderDetail.objects.create(
            id=1, po_master=cls.po_master_pr, po_no='PO001', pr_no='PR001', pr_id=cls.pr_detail.id,
            quantity=Decimal('10.00000'), rate=Decimal('100.00000'), discount=Decimal('5.00'),
            packing_forwarding=cls.packing_term, excise_service_tax=cls.excise_tax, vat=cls.vat_term,
            additional_description='Initial PR item description', delivery_date=datetime.date(2024, 12, 31),
            budget_code_fk=cls.budget_code_obj
        )

        cls.po_master_spr = PurchaseOrderMaster.objects.create(
            id=2, po_no='PO002', pr_spr_flag='1', company=cls.company
        )
        cls.po_detail_spr = PurchaseOrderDetail.objects.create(
            id=2, po_master=cls.po_master_spr, po_no='PO002', spr_no='SPR001', spr_id=cls.spr_detail_wo.id,
            quantity=Decimal('20.00000'), rate=Decimal('200.00000'), discount=Decimal('10.00'),
            packing_forwarding=cls.packing_term, excise_service_tax=cls.excise_tax, vat=cls.vat_term,
            additional_description='Initial SPR item description', delivery_date=datetime.date(2024, 11, 15),
            budget_code_fk=cls.budget_code_obj
        )

        # Rate Register and Lock/Unlock
        cls.rate_register_entry = RateRegister.objects.create(
            item=cls.item, company=cls.company, rate=Decimal('90.00'), discount=Decimal('0.00')
        )
        cls.rate_unlock_entry = RateLockUnlock.objects.create(
            item=cls.item, company=cls.company, lock_unlock=True, type='2'
        )
        cls.rate_lock_entry = RateLockUnlock.objects.create(
            item=cls.item, company=cls.company, lock_unlock=False, type='2'
        )

class PurchaseOrderDetailModelTest(BaseTestData):
    def test_get_min_discounted_rate(self):
        # Test case where a min rate exists
        min_rate = RateRegister.get_min_discounted_rate(self.item.id, self.company.id)
        self.assertEqual(min_rate, Decimal('90.00'))

        # Test case where no rate exists
        with patch('material_management.models.RateRegister.objects.filter') as mock_filter:
            mock_filter.return_value.annotate.return_value.aggregate.return_value = {'min_dis_rate': None}
            no_rate = RateRegister.get_min_discounted_rate(999, self.company.id)
            self.assertEqual(no_rate, 0.0)

    def test_is_rate_unlocked(self):
        # Test case for unlocked
        self.assertTrue(RateLockUnlock.is_rate_unlocked(self.item.id, self.company.id, '2'))
        # Test case for locked
        self.assertFalse(RateLockUnlock.is_rate_unlocked(self.item.id, self.company.id, '3')) # Different type
        self.assertFalse(RateLockUnlock.is_rate_unlocked(self.item.id, self.company.id, type_code='2'))
        # Test with locked entry (need to temporarily change lock_unlock)
        self.rate_unlock_entry.lock_unlock = False
        self.rate_unlock_entry.save()
        self.assertFalse(RateLockUnlock.is_rate_unlocked(self.item.id, self.company.id, '2'))
        self.rate_unlock_entry.lock_unlock = True # Revert
        self.rate_unlock_entry.save()


    def test_get_full_po_detail_data_pr_flag_0(self):
        data = PurchaseOrderDetail.get_full_po_detail_data(
            po_master_id=self.po_master_pr.id,
            po_detail_id=self.po_detail_pr.id,
            company_id=self.company.id
        )
        self.assertIsNotNone(data)
        self.assertEqual(data['po_detail'], self.po_detail_pr)
        self.assertEqual(data['pr_no'], 'PR001')
        self.assertEqual(data['spr_no'], 'NA')
        self.assertEqual(data['item_code'], 'ITEM001')
        self.assertEqual(data['item_description'], 'Test Item Description')
        self.assertEqual(data['uom_symbol'], 'MTR')
        self.assertEqual(data['wo_no'], 'WO001')
        self.assertEqual(data['department_name'], 'NA')
        self.assertEqual(data['account_head_display'], '[AH] Test Account Head')
        self.assertIsNotNone(data['item_id'])
        self.assertIn({'id': self.budget_code_obj.id, 'symbol': 'BGTWO001'}, data['budget_code_options'])
        self.assertEqual(data['budget_code_label'], '')

    def test_get_full_po_detail_data_spr_flag_1_wo_based(self):
        # Need to ensure SPR_Detail has no department_id to test WO based
        self.spr_detail_wo.department = None
        self.spr_detail_wo.save()
        data = PurchaseOrderDetail.get_full_po_detail_data(
            po_master_id=self.po_master_spr.id,
            po_detail_id=self.po_detail_spr.id,
            company_id=self.company.id
        )
        self.assertIsNotNone(data)
        self.assertEqual(data['po_detail'], self.po_detail_spr)
        self.assertEqual(data['pr_no'], 'NA')
        self.assertEqual(data['spr_no'], 'SPR001')
        self.assertEqual(data['item_code'], 'ITEM001')
        self.assertEqual(data['item_description'], 'Test Item Description')
        self.assertEqual(data['uom_symbol'], 'MTR')
        self.assertEqual(data['wo_no'], 'WO002')
        self.assertEqual(data['department_name'], 'NA')
        self.assertEqual(data['account_head_display'], '[AH] Test Account Head')
        self.assertIsNotNone(data['item_id'])
        self.assertIn({'id': self.budget_code_obj.id, 'symbol': 'BGTWO002'}, data['budget_code_options'])
        self.assertEqual(data['budget_code_label'], '')

    def test_get_full_po_detail_data_spr_flag_1_dept_based(self):
        # Use the SPR_Detail with a department
        po_detail_spr_dept_based = PurchaseOrderDetail.objects.create(
            id=3, po_master=self.po_master_spr, po_no='PO003', spr_no='SPR001', spr_id=self.spr_detail_dept.id,
            quantity=Decimal('30.00000'), rate=Decimal('300.00000'), discount=Decimal('0.00'),
            packing_forwarding=self.packing_term, excise_service_tax=self.excise_tax, vat=self.vat_term,
            additional_description='Initial SPR dept item description', delivery_date=datetime.date(2024, 10, 1),
            budget_code_fk=None # No budget code from dropdown
        )
        data = PurchaseOrderDetail.get_full_po_detail_data(
            po_master_id=self.po_master_spr.id,
            po_detail_id=po_detail_spr_dept_based.id,
            company_id=self.company.id
        )
        self.assertIsNotNone(data)
        self.assertEqual(data['po_detail'], po_detail_spr_dept_based)
        self.assertEqual(data['wo_no'], 'NA')
        self.assertEqual(data['department_name'], '[DEPT] Test Department')
        self.assertEqual(data['budget_code_options'], [])
        self.assertEqual(data['budget_code_label'], 'DEPT')

    def test_get_full_po_detail_data_not_found(self):
        data = PurchaseOrderDetail.get_full_po_detail_data(
            po_master_id=999, po_detail_id=999, company_id=self.company.id
        )
        self.assertIsNone(data)

class PurchaseOrderDetailAmendmentFormTest(BaseTestData):
    def setUp(self):
        super().setUp()
        self.form_data = {
            'quantity': '12.345',
            'rate': '150.75',
            'discount': '2.50',
            'additional_description': 'Amended notes.',
            'packing_forwarding': self.packing_term.id,
            'excise_service_tax': self.excise_tax.id,
            'vat': self.vat_term.id,
            'delivery_date': '2025-01-01',
            'budget_code': self.budget_code_obj.id,
            'po_no': 'PO001',
            'po_master_id': self.po_master_pr.id,
            'po_detail_id': self.po_detail_pr.id,
            'account_head_id': self.account_head.id,
            'item_id': self.item.id,
            'company_id': self.company.id,
        }

    def test_form_valid(self):
        form = PurchaseOrderDetailAmendmentForm(data=self.form_data)
        self.assertTrue(form.is_valid())

    def test_form_invalid_required_fields(self):
        data = self.form_data.copy()
        data['quantity'] = '' # Missing required field
        form = PurchaseOrderDetailAmendmentForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)

    def test_form_invalid_regex(self):
        data = self.form_data.copy()
        data['quantity'] = 'abc' # Invalid format
        form = PurchaseOrderDetailAmendmentForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)

    def test_form_rate_validation_acceptable(self):
        # Min rate 90, entered rate 150 (after 2.5% discount = 146.25). Acceptable.
        form = PurchaseOrderDetailAmendmentForm(data=self.form_data)
        self.assertTrue(form.is_valid())
        self.assertNotIn('rate', form.errors)

    def test_form_rate_validation_not_acceptable_locked(self):
        # Set entered rate lower than min rate (e.g., 80) and lock
        data = self.form_data.copy()
        data['rate'] = '80.00'
        # Temporarily set RateLockUnlock to locked state for this item
        self.rate_unlock_entry.lock_unlock = False
        self.rate_unlock_entry.save()

        form = PurchaseOrderDetailAmendmentForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('rate', form.errors)
        self.assertEqual(form.errors['rate'][0], 'Entered rate is not acceptable!')

        # Revert changes
        self.rate_unlock_entry.lock_unlock = True
        self.rate_unlock_entry.save()

    def test_form_rate_validation_acceptable_unlocked(self):
        # Set entered rate lower than min rate (e.g., 80) but unlocked
        data = self.form_data.copy()
        data['rate'] = '80.00'
        # RateLockUnlock is True by default for setup data, so it's unlocked

        form = PurchaseOrderDetailAmendmentForm(data=data)
        self.assertTrue(form.is_valid()) # Should be valid because it's unlocked
        self.assertNotIn('rate', form.errors)
        # Should set rate_flag_value to '1' in save_amendment

    def test_form_save_amendment(self):
        form = PurchaseOrderDetailAmendmentForm(data=self.form_data)
        self.assertTrue(form.is_valid())
        mock_user = MagicMock(username='testuser')
        amendment = form.save_amendment(mock_user)

        self.assertIsInstance(amendment, PurchaseOrderAmendmentTemp)
        self.assertEqual(amendment.quantity, Decimal('12.345'))
        self.assertEqual(amendment.rate, Decimal('150.75'))
        self.assertEqual(amendment.discount, Decimal('2.50'))
        self.assertEqual(amendment.delivery_date, datetime.date(2025, 1, 1))
        self.assertEqual(amendment.po_detail_id, self.po_detail_pr.id)
        self.assertEqual(amendment.session_id, 'testuser')
        self.assertEqual(PurchaseOrderAmendmentTemp.objects.count(), 1)


class PurchaseOrderDetailAmendmentViewTest(BaseTestData):
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.session['compid'] = self.company.id # Mock session compid
        # Define URL parameters for the view
        self.view_kwargs = {
            'mid': self.po_master_pr.id,
            'code': 'samplecode',
            'pono': 'PO001',
            'poid': self.po_detail_pr.id,
        }
        self.url = reverse('material_management:po_detail_amendment', kwargs=self.view_kwargs)
        self.success_url = reverse('material_management:po_details_grid', kwargs={
            'mid': self.po_master_pr.id, 'code': 'samplecode', 'pono': 'PO001'
        })

    def test_view_get(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderdetail_amendment_form.html')
        self.assertIsInstance(response.context['form'], PurchaseOrderDetailAmendmentForm)
        self.assertEqual(response.context['form'].initial['quantity'], Decimal('10.00000'))
        self.assertEqual(response.context['po_detail'], self.po_detail_pr)
        self.assertEqual(response.context['pr_no'], 'PR001')
        self.assertEqual(response.context['rate_register_url'], reverse('material_management:rate_register_print', kwargs={'item_id': self.item.id, 'company_id': self.company.id}))


    def test_view_post_valid(self):
        post_data = {
            'quantity': '15.000',
            'rate': '110.00',
            'discount': '1.00',
            'additional_description': 'New notes',
            'packing_forwarding': self.packing_term.id,
            'excise_service_tax': self.excise_tax.id,
            'vat': self.vat_term.id,
            'delivery_date': '2025-02-01',
            'budget_code': self.budget_code_obj.id,
            'po_no': 'PO001',
            'po_master_id': str(self.po_master_pr.id), # Forms.py expects str from POST
            'po_detail_id': str(self.po_detail_pr.id),
            'account_head_id': str(self.account_head.id),
            'item_id': str(self.item.id),
            'company_id': str(self.company.id),
        }
        response = self.client.post(self.url, data=post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX success, not redirect
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], self.success_url)
        self.assertTrue(PurchaseOrderAmendmentTemp.objects.filter(po_detail_id=self.po_detail_pr.id, quantity=Decimal('15.000')).exists())
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'PO Detail amendment added successfully.')


    def test_view_post_invalid(self):
        post_data = {
            'quantity': 'invalid_qty', # Invalid data
            'rate': '50.00', # Lower than min rate, and not unlocked
            'discount': '0.00',
            'delivery_date': '2024-06-01',
            'po_no': 'PO001',
            'po_master_id': str(self.po_master_pr.id),
            'po_detail_id': str(self.po_detail_pr.id),
            'account_head_id': str(self.account_head.id),
            'item_id': str(self.item.id),
            'company_id': str(self.company.id),
        }
        # Temporarily set RateLockUnlock to locked state for this item
        self.rate_unlock_entry.lock_unlock = False
        self.rate_unlock_entry.save()

        response = self.client.post(self.url, data=post_data)
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'material_management/purchaseorderdetail_amendment_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('quantity', response.context['form'].errors)
        self.assertIn('rate', response.context['form'].errors)
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

        # Revert changes
        self.rate_unlock_entry.lock_unlock = True
        self.rate_unlock_entry.save()

    def test_view_get_404_missing_params(self):
        # Missing poid
        url_incomplete = reverse('material_management:po_detail_amendment', kwargs={
            'mid': self.po_master_pr.id, 'code': 'samplecode', 'pono': 'PO001', 'poid': 99999
        })
        response = self.client.get(url_incomplete)
        self.assertEqual(response.status_code, 404)

    def test_po_details_grid_view(self):
        grid_url = reverse('material_management:po_details_grid', kwargs={
            'mid': self.po_master_pr.id, 'code': 'samplecode', 'pono': 'PO001'
        })
        response = self.client.get(grid_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_details_grid.html')
        messages = list(response.context['messages'])
        self.assertEqual(str(messages[0]), 'This is a placeholder for the PO Details Grid.')

    def test_rate_register_print_view(self):
        print_url = reverse('material_management:rate_register_print', kwargs={
            'item_id': self.item.id, 'company_id': self.company.id
        })
        response = self.client.get(print_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/rate_register_print.html')
        messages = list(response.context['messages'])
        self.assertEqual(str(messages[0]), f'This is a placeholder for Rate Register Print for Item ID: {self.item.id}, Company ID: {self.company.id}.')
```

### Step 5: HTMX and Alpine.js Integration

**Business Interpretation:** This step focuses on making the application feel fast and dynamic for users. HTMX allows parts of the page to update without needing to reload the entire page, while Alpine.js helps manage small interactive elements directly in the browser, creating a smoother experience.

**Instructions:**
*   **HTMX for Form Submission:** The `purchaseorderdetail_amendment_form.html` uses `hx-post="." hx-swap="outerHTML" hx-target="#form-container"` on the submit button. This tells HTMX to submit the form data to the current URL, and upon receiving a response, replace the *entire form container* with the new content.
    *   On successful submission, the view returns an `HX-Redirect` header which HTMX will process to perform a full page redirect to the grid page.
    *   On invalid submission, the view re-renders the form with errors, and HTMX swaps the content, showing the errors.
*   **HTMX for Navigation:** The "Cancel" button is a standard `<a>` tag linking directly to the grid URL, resulting in a full page load, mimicking the ASP.NET `Response.Redirect`.
*   **Alpine.js:** While not extensively used in this specific form (as HTMX handles most of the dynamic updates), Alpine.js would be ideal for client-side UI states like toggling visibility of elements, client-side date picker initialization (as demonstrated in the `extra_js` block with a `flatpickr` example), or custom alert messages. The provided `_budget_code_field.html` snippet could be enhanced with Alpine.js if its visibility needs client-side toggling based on user input, rather than just initial load state.
*   **DataTables:** Although this specific page is an edit form, the target "grid" page (`po_details_grid.html`) would prominently feature DataTables. The concept for list views would be to load the table content via HTMX to a `_table.html` partial, and then initialize DataTables on that content, similar to the general list template example provided in the prompt.

---

## Final Notes

*   **Placeholders:** Ensure all placeholders like `[APP_NAME]` (`material_management`), `[MODEL_NAME]` (`PurchaseOrderDetail`), `[FIELD1]` (`quantity`), `[TEST_VALUE1]` etc. are replaced with the actual values derived from the ASP.NET analysis.
*   **DRY Principles:** We've applied DRY by creating reusable partial templates (`_budget_code_field.html`) and encapsulating complex data fetching logic within model methods.
*   **Fat Model, Thin View:** The `PurchaseOrderDetail.get_full_po_detail_data` method demonstrates moving significant data retrieval and conditional logic into the model layer. The `PurchaseOrderDetailAmendmentForm` handles field-level and business validation. The `PurchaseOrderDetailAmendmentView` remains concise, primarily coordinating data fetching for initial display and delegating form processing and saving to the form itself.
*   **Test Coverage:** The provided tests cover model methods, form validation, and view interactions, including HTMX specific responses, aiming for high code coverage.
*   **Database Connection:** The `managed = False` in models assumes you are connecting to an existing SQL Server database (or a replica of it) using Django's database settings. This is a common approach for phased migrations where the database is not immediately converted.
*   **Session Management:** The `CompId` and `SessionId` (`request.user.username`) are assumed to be managed by Django's authentication system and potentially a custom middleware for company context.
*   **Missing Features:** Some ASP.NET features like `PopUpMsg.js` or `loadingNotifier.js` would be replaced by HTMX events (e.g., `htmx:indicating`) and Tailwind CSS for loading spinners, or a dedicated notification system for messages (Django messages framework is used). The ASP.NET `fun` helper class's specific date/number validation methods are replaced by Django form fields and custom `clean` methods, which are more robust.