## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code primarily inserts data into `tblMM_PR_PO_Temp` and reads from several other tables to populate the form and perform calculations.

**Main Target Table for Insertion:**

*   **`tblMM_PR_PO_Temp`**
    *   **Columns (inferred from `INSERT` statement):**
        *   `CompId` (int)
        *   `SessionId` (string, likely user ID)
        *   `PRNo` (string)
        *   `PRId` (int)
        *   `Qty` (double)
        *   `Rate` (double)
        *   `Discount` (double)
        *   `AddDesc` (string)
        *   `PF` (int, foreign key to `tblPacking_Master`)
        *   `ExST` (int, foreign key to `tblExciseser_Master`)
        *   `VAT` (int, foreign key to `tblVAT_Master`)
        *   `DelDate` (datetime)
        *   `BudgetCode` (int, foreign key to `tblMIS_BudgetCode`)

**Supporting Lookup Tables (for dropdowns and display):**

*   **`tblPacking_Master`**: `Id` (int), `Terms` (string)
*   **`tblExciseser_Master`**: `Id` (int), `Terms` (string)
*   **`tblVAT_Master`**: `Id` (int), `Terms` (string)
*   **`tblMIS_BudgetCode`**: `Id` (int), `Symbol` (string)
*   **`tblMM_PR_Master`**: `PRNo` (string), `WONo` (string), `CompId` (int)
*   **`tblMM_PR_Details`**: `Id` (int), `AHId` (int), `ItemId` (int), `Qty` (double), `Rate` (double), `DelDate` (datetime), `PRNo` (string)
*   **`tblDG_Item_Master`**: `Id` (int), `ItemCode` (string), `ManfDesc` (string), `UOMBasic` (int), `CompId` (int)
*   **`Unit_Master`**: `Id` (int), `Symbol` (string)
*   **`AccHead`**: `Id` (int), `Symbol` (string), `Description` (string)
*   **`tblMM_PO_Master`**: `Id` (int), `CompId` (int)
*   **`tblMM_PO_Details`**: `PRId` (int), `Qty` (double), `MId` (int)
*   **`tblMM_Rate_Register`**: `ItemId` (int), `CompId` (int), `Rate` (double), `Discount` (double)
*   **`tblMM_RateLockUnLock_Master`**: `ItemId` (int), `CompId` (int), `LockUnlock` (int/bool), `Type` (int)

### Step 2: Identify Backend Functionality

The ASP.NET page `PO_PR_ItemSelect.aspx` primarily serves as an "Add Item to Temporary PO" form.

*   **Read (Display/Pre-population):**
    *   Retrieves `CompId`, `FyId`, `username` from `Session`.
    *   Retrieves `Code`, `prno`, `prid`, `wono` from `Request.QueryString`.
    *   Fetches details of a specific `PR` item (`tblMM_PR_Details`) and its master (`tblMM_PR_Master`).
    *   Fetches `BudgetCode` (using `tblMIS_BudgetCode` and `tblMM_PR_Master`).
    *   Fetches `ItemCode`, `ManfDesc`, `UOMBasic` from `tblDG_Item_Master` based on `ItemId` from PR details.
    *   Fetches `UOM` symbol from `Unit_Master`.
    *   Calculates `RemTempQty` (sum of quantities from `tblMM_PR_PO_Temp` for current PR item).
    *   Calculates `PoQty` (sum of quantities from `tblMM_PO_Details` for current PR item).
    *   Calculates `RemQty` (`PRQty` - `PoQty`).
    *   Sets initial `txtQty` to `RemQty` - `RemTempQty`.
    *   Sets initial `txtRate` from PR details.
    *   Fetches `AcHead` description from `AccHead`.
    *   Sets `txtDelDate` from PR details.
    *   Populates `DDLPF`, `DDLExcies`, `DDLVat` from their respective master tables.

*   **Create (Insertion):**
    *   On `btnProcide_Click`, performs validation checks on `Qty`, `Rate`, `Discount`, `DelDate`.
    *   Calculates a final `Rate` considering discount.
    *   Performs complex rate validation:
        *   Compares the calculated `Rate` with the minimum discounted rate from `tblMM_Rate_Register` for the given item.
        *   If the entered rate is higher than the registered rate and `tblMM_RateLockUnLock_Master` indicates a lock for the item, it disallows the entry. Otherwise, it proceeds or allows.
    *   If all validations pass, it inserts a new record into `tblMM_PR_PO_Temp`.
    *   Redirects to `PO_PR_ItemGrid.aspx`.

*   **Validation Logic (Replication in Django):**
    *   **Required Fields:** `txtQty`, `txtRate`, `txtDelDate`.
    *   **Regular Expressions:** For numeric (`txtQty`, `txtRate`, `txtDiscount`) and date (`txtDelDate`).
    *   **Date Range:** `txtDelDate` must be greater than or equal to current date.
    *   **Quantity:** Must be numeric and not "0".
    *   **Rate:** Calculated rate must be > 0.
    *   **Business Logic Validation:** The comparison of the entered rate against `tblMM_Rate_Register` and `tblMM_RateLockUnLock_Master`.

*   **Navigation:**
    *   `btnCancel_Click` redirects to `PO_PR_ItemGrid.aspx`.

### Step 3: Infer UI Components

The ASP.NET page is a form for adding a single item.

*   **Labels (Display Only):** `lblSprno` (PR No), `lblprQty` (PR Qty), `lblwono` (WO No), `lblItemCode` (Item Code), `lblItemDesc` (Item Description), `lblAcHead` (Ac Head), `lblUnit` (UOM Purch). These will be rendered as static text/variables in the Django template.
*   **Text Inputs:**
    *   `txtQty` (PO Qty) - Numeric, required.
    *   `txtRate` (Rate) - Numeric, required.
    *   `txtDiscount` (Discount) - Numeric, default "0".
    *   `txtAddDesc` (Additional Desc) - Multi-line text.
    *   `txtDelDate` (Del. Date) - Date picker, required.
*   **Dropdown Lists (`<select>`):**
    *   `DrpBudgetCode` (Budget Code) - Dynamically populated.
    *   `DDLPF` (P & F) - Populated from `tblPacking_Master`.
    *   `DDLExcies` (Excies / Service Tax) - Populated from `tblExciseser_Master`.
    *   `DDLVat` (VAT) - Populated from `tblVAT_Master`.
*   **Buttons:**
    *   `btnProcide` (Add) - Form submission.
    *   `btnCancel` (Cancel) - Redirects.
*   **Hidden Fields:** `LblItemId`, `LblDate`. Will be handled as context or initial data.
*   **External Links:** `rt` (Rate Register Single Item Print) - Will be a link to a new Django view/report.
*   **Styling:** CSS classes (`fontcss`, `box3`, `redbox`) will be mapped to Tailwind CSS.

### Step 4: Generate Django Code

We will create a Django application named `material_management`.

#### 4.1 Models (`material_management/models.py`)

This section defines the Django models corresponding to the database tables identified. Note that for the lookup tables, we only include the `Id` and `Terms` (or `Symbol`/`Description`) as they are used for dropdowns. The core model is `PurchaseOrderItemTemp`.

```python
from django.db import models
from django.db.models import Sum, F
from django.utils import timezone
from datetime import datetime, date

# --- Supporting Models for Dropdowns ---
class PackingMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.terms

class ExciseServiceTaxMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Tax Term'
        verbose_name_plural = 'Excise/Service Tax Terms'

    def __str__(self):
        return self.terms

class VatMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'

    def __str__(self):
        return self.terms

class BudgetCode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return self.symbol # ASP.NET uses Symbol + WONo for display, but Model just needs Symbol

# --- Main Model for the Temporary PO Item ---
class PurchaseOrderItemTempManager(models.Manager):
    """
    Custom manager for PurchaseOrderItemTemp to encapsulate business logic
    related to initial data fetching and validation.
    """
    def get_initial_data_for_pr_item(self, comp_id, pr_id, wo_no, current_user_id):
        """
        Mimics the complex Page_Load logic for pre-populating form fields
        and labels based on PR details, existing POs, and temporary entries.
        """
        initial_data = {}
        display_data = {}
        item_id = None

        # Data for PR details (from tblMM_PR_Master, tblMM_PR_Details)
        # Note: This is simplified and assumes direct SQL queries or a more
        # comprehensive ORM setup for related tables would exist.
        # For this example, we'll use placeholder or mock data for related tables
        # if actual complex joins are not set up.
        # In a real migration, 'tblMM_PR_Master' and 'tblMM_PR_Details' would be Django models.

        try:
            # Mocking clsFunctions.select for demonstration
            # In production, this would be proper ORM queries or a service layer.
            # Example: pr_detail = PRDetail.objects.get(id=pr_id, pr_master__comp_id=comp_id)
            # For simplicity, we'll assume we get a dictionary back from a 'service'.

            # Simulating data fetching:
            # This logic needs to be robustly implemented in a real ERP system.
            # Placeholder for actual data retrieval based on pr_id
            mock_pr_detail = {
                'PRNo': 'PR-001', 'WONo': wo_no, 'Id': pr_id, 'AHId': 1,
                'ItemId': 101, 'Qty': 100.0, 'Rate': 50.0, 'DelDate': '2024-12-31'
            }

            # Item details (from tblDG_Item_Master)
            mock_item_detail = {
                'ItemCode': 'ITM-001', 'ManfDesc': 'Sample Item Description', 'UOMBasic': 1
            }

            # UOM (from Unit_Master)
            mock_unit_symbol = {'Symbol': 'PCS'}

            # Acc Head (from AccHead)
            mock_ac_head = {'Head': '[ACC] Sample Account Head'}

            # Calculate remaining quantities (mimicking ASP.NET logic)
            # This part requires querying tblMM_PR_PO_Temp and tblMM_PO_Details
            # For this example, we'll assume these return aggregates.

            # Example:
            # current_temp_qty = PurchaseOrderItemTemp.objects.filter(
            #     pr_no=mock_pr_detail['PRNo'], pr_id=mock_pr_detail['Id']
            # ).aggregate(Sum('qty'))['qty__sum'] or 0.0
            #
            # total_po_qty = PoDetail.objects.filter(
            #     pr_id=mock_pr_detail['Id'],
            #     po_master__comp_id=comp_id
            # ).aggregate(Sum('qty'))['qty__sum'] or 0.0

            current_temp_qty = 0.0  # Placeholder, needs actual query
            total_po_qty = 0.0      # Placeholder, needs actual query

            pr_qty = float(mock_pr_detail['Qty'])
            remaining_pr_qty = round(pr_qty - total_po_qty, 5)
            suggested_po_qty = round(remaining_pr_qty - current_temp_qty, 5)

            # Populate display_data (for labels)
            display_data['pr_no'] = mock_pr_detail['PRNo']
            display_data['pr_qty'] = f"{pr_qty:.3f}"
            display_data['wo_no'] = wo_no
            display_data['item_code'] = mock_item_detail['ItemCode']
            display_data['item_desc'] = mock_item_detail['ManfDesc']
            display_data['ac_head'] = mock_ac_head['Head']
            display_data['unit'] = mock_unit_symbol['Symbol']
            item_id = mock_pr_detail['ItemId']

            # Populate initial_data (for form fields)
            initial_data['qty'] = suggested_po_qty
            initial_data['rate'] = round(float(mock_pr_detail['Rate']), 5)
            initial_data['del_date'] = datetime.strptime(mock_pr_detail['DelDate'], '%Y-%m-%d').date()
            initial_data['pr_id'] = pr_id # Needed for saving

            # Populate Budget Code Dropdown
            # Example: budget_codes = BudgetCode.objects.filter(
            #     pr_master__wo_no=wo_no, pr_master__comp_id=comp_id
            # )
            # This is complex and assumes a relation, simplest is just all BudgetCodes
            budget_codes_qs = BudgetCode.objects.all().order_by('symbol')
            display_data['budget_codes'] = [(b.id, f"{b.symbol}{wo_no}") for b in budget_codes_qs]

        except Exception as e:
            # Handle cases where PR or item data might not be found
            print(f"Error fetching initial data: {e}")
            pass # Form will be empty or pre-filled with defaults

        return initial_data, display_data, item_id

    def validate_and_insert_temp_item(self, form_cleaned_data, comp_id, session_id, pr_no, pr_id, item_id):
        """
        Encapsulates the complex business logic from btnProcide_Click
        for rate validation and insertion.
        Returns (success_message, error_message) or (instance, None)
        """
        qty = form_cleaned_data['qty']
        rate = form_cleaned_data['rate']
        discount = form_cleaned_data['discount']
        add_desc = form_cleaned_data['add_desc']
        pf_id = form_cleaned_data['pf'].id if form_cleaned_data['pf'] else None
        ex_st_id = form_cleaned_data['ex_st'].id if form_cleaned_data['ex_st'] else None
        vat_id = form_cleaned_data['vat'].id if form_cleaned_data['vat'] else None
        del_date = form_cleaned_data['del_date']
        budget_code_id = form_cleaned_data['budget_code'].id if form_cleaned_data['budget_code'] else None

        current_date = timezone.localdate()

        # ASP.NET original calculation:
        # Rate = Convert.ToDouble(decimal.Parse((Convert.ToDouble(txtRate.Text) - ((Convert.ToDouble(txtRate.Text) * Convert.ToDouble(txtDiscount.Text)) / 100)).ToString()).ToString("N2")));
        final_rate_after_discount = round(rate - (rate * discount / 100.0), 2)

        # Qty validation
        if qty <= 0:
            return None, "Entered Qty is not acceptable! Quantity must be greater than zero."

        # Date validation
        if del_date < current_date:
            return None, "Entered Date is not acceptable! Delivery date cannot be in the past."

        # Rate validation
        if final_rate_after_discount <= 0:
            return None, "Entered rate is not acceptable! Final rate after discount must be positive."

        # Complex rate comparison logic
        # This part requires querying tblMM_Rate_Register and tblMM_RateLockUnLock_Master
        # In a real system, these would be Django models (RateRegister, RateLockUnlockMaster).

        # Simulating fetching lowest discounted rate from Rate Register
        # Example: min_reg_rate_obj = RateRegister.objects.filter(
        #     item_id=item_id, comp_id=comp_id
        # ).annotate(
        #     dis_rate=F('rate') - (F('rate') * F('discount') / 100.0)
        # ).order_by('dis_rate').first()
        #
        # registered_rate = min_reg_rate_obj.dis_rate if min_reg_rate_obj else 0.0

        registered_rate = 0.0 # Placeholder: Assume no rate in register or 0 for simplification

        # Check if entered rate is higher than registered lowest rate
        if final_rate_after_discount > registered_rate and registered_rate > 0:
            # Check for Rate Lock/Unlock Master
            # lock_status = RateLockUnlockMaster.objects.filter(
            #     item_id=item_id, comp_id=comp_id, lock_unlock=1, type=2
            # ).exists()
            lock_status = False # Placeholder: Assume no lock for simplification

            if not lock_status:
                return None, "Entered rate is not acceptable!" # Alert original ASP.NET behavior

        # If all validations pass, create the temporary PO item
        try:
            temp_item = self.model.objects.create(
                comp_id=comp_id,
                session_id=session_id,
                pr_no=pr_no,
                pr_id=pr_id,
                qty=qty,
                rate=rate,  # Store original rate, not final_rate_after_discount
                discount=discount,
                add_desc=add_desc,
                pf=PackingMaster.objects.get(id=pf_id) if pf_id else None,
                ex_st=ExciseServiceTaxMaster.objects.get(id=ex_st_id) if ex_st_id else None,
                vat=VatMaster.objects.get(id=vat_id) if vat_id else None,
                del_date=del_date,
                budget_code=BudgetCode.objects.get(id=budget_code_id) if budget_code_id else None
            )
            return temp_item, None # Return the created instance
        except Exception as e:
            return None, f"An error occurred during save: {e}"


class PurchaseOrderItemTemp(models.Model):
    # Mapping to tblMM_PR_PO_Temp
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    pr_no = models.CharField(db_column='PRNo', max_length=255)
    pr_id = models.IntegerField(db_column='PRId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=5)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=5)
    add_desc = models.TextField(db_column='AddDesc', blank=True, null=True)
    # Foreign Keys for dropdowns
    pf = models.ForeignKey(PackingMaster, models.DO_NOTHING, db_column='PF', blank=True, null=True)
    ex_st = models.ForeignKey(ExciseServiceTaxMaster, models.DO_NOTHING, db_column='ExST', blank=True, null=True)
    vat = models.ForeignKey(VatMaster, models.DO_NOTHING, db_column='VAT', blank=True, null=True)
    del_date = models.DateField(db_column='DelDate')
    budget_code = models.ForeignKey(BudgetCode, models.DO_NOTHING, db_column='BudgetCode', blank=True, null=True)

    objects = PurchaseOrderItemTempManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblMM_PR_PO_Temp'
        verbose_name = 'Temporary Purchase Order Item'
        verbose_name_plural = 'Temporary Purchase Order Items'

    def __str__(self):
        return f"Temp PO Item {self.id} for PR {self.pr_no}"

    # No additional business logic methods needed on the model itself,
    # as the complex validation is handled in the custom manager.
    # Other simple properties/methods could be added if needed, e.g.,
    @property
    def final_rate(self):
        return round(self.rate - (self.rate * self.discount / 100.0), 2)

```

#### 4.2 Forms (`material_management/forms.py`)

This form will handle the input fields and delegate complex validation to the model's manager.

```python
from django import forms
from .models import PurchaseOrderItemTemp, PackingMaster, ExciseServiceTaxMaster, VatMaster, BudgetCode
from django.core.exceptions import ValidationError
from django.forms.widgets import NumberInput

class PurchaseOrderItemTempForm(forms.ModelForm):
    # Fields that were initially labels but are inputs for saving
    # pr_no, pr_id, item_id are passed as hidden fields or via view context
    # and managed within the form's clean method or view's form_valid.

    # These fields correspond to ASP.NET TextBoxes
    qty = forms.DecimalField(
        label="PO Qty",
        min_value=0.001, # Ensure quantity is positive
        max_digits=18,
        decimal_places=5,
        required=True,
        widget=NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter quantity',
            'min': '0.001' # HTML5 validation for minimum value
        })
    )
    rate = forms.DecimalField(
        label="Rate",
        min_value=0.001, # Ensure rate is positive
        max_digits=18,
        decimal_places=5,
        required=True,
        widget=NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter rate',
            'min': '0.001'
        })
    )
    discount = forms.DecimalField(
        label="Discount (%)",
        min_value=0.0,
        max_value=100.0,
        max_digits=18,
        decimal_places=5,
        required=False,
        initial=0.0,
        widget=NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter discount',
            'min': '0', 'max': '100'
        })
    )
    add_desc = forms.CharField(
        label="Additional Desc",
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-14',
            'rows': 3
        })
    )
    del_date = forms.DateField(
        label="Delivery Date",
        required=True,
        widget=forms.DateInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker-input',
            'type': 'date' # Use HTML5 date input, supplemented by flatpickr
        })
    )

    # These fields correspond to ASP.NET DropDownLists
    pf = forms.ModelChoiceField(
        queryset=PackingMaster.objects.all(),
        label="P & F",
        required=False,
        empty_label="Select P & F",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    ex_st = forms.ModelChoiceField(
        queryset=ExciseServiceTaxMaster.objects.all(),
        label="Excies / Service Tax",
        required=False,
        empty_label="Select Excise/Service Tax",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    vat = forms.ModelChoiceField(
        queryset=VatMaster.objects.all(),
        label="VAT",
        required=False,
        empty_label="Select VAT",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    budget_code = forms.ModelChoiceField(
        queryset=BudgetCode.objects.all(), # This was dynamically filtered in ASP.NET
        label="Budget Code",
        required=False,
        empty_label="Select Budget Code",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = PurchaseOrderItemTemp
        fields = ['qty', 'rate', 'discount', 'add_desc', 'del_date', 'pf', 'ex_st', 'vat', 'budget_code']
        # The following fields are not directly on the form as user input,
        # but are needed for the model. They will be passed to the clean method or view.
        # 'comp_id', 'session_id', 'pr_no', 'pr_id'

    def __init__(self, *args, **kwargs):
        self.comp_id = kwargs.pop('comp_id', None)
        self.session_id = kwargs.pop('session_id', None)
        self.pr_no = kwargs.pop('pr_no', None)
        self.pr_id = kwargs.pop('pr_id', None)
        self.item_id = kwargs.pop('item_id', None) # Needed for complex rate validation
        self.display_data = kwargs.pop('display_data', {}) # For labels

        super().__init__(*args, **kwargs)

        # Dynamic queryset for BudgetCode (mimicking ASP.NET DrpBudgetCode population)
        # This requires `wo_no` to filter budget codes
        if self.display_data and 'wo_no' in self.display_data:
            wo_no = self.display_data['wo_no']
            # This would typically be a more complex join or pre-filtered queryset
            # For simplicity, we are showing all budget codes, but in a real system
            # `BudgetCode.objects.filter(related_pr_master__wo_no=wo_no)` would be used.
            # As the model is 'managed=False', direct filtering on 'symbol' is not
            # a direct equivalent to the SQL join.
            # The dropdown for BudgetCode is populated by Symbol + WO_NO in ASP.NET
            # Here, we need to manually create choices or have a related model.
            budget_codes_qs = BudgetCode.objects.all()
            self.fields['budget_code'].choices = [(b.id, f"{b.symbol}{wo_no}") for b in budget_codes_qs]
            self.fields['budget_code'].choices.insert(0, ('', 'Select Budget Code'))

    def clean(self):
        cleaned_data = super().clean()

        # Delegate complex business validation to the model manager
        # The actual insertion happens in the view after successful cleaning
        instance, error_message = PurchaseOrderItemTemp.objects.validate_and_insert_temp_item(
            form_cleaned_data=cleaned_data,
            comp_id=self.comp_id,
            session_id=self.session_id,
            pr_no=self.pr_no,
            pr_id=self.pr_id,
            item_id=self.item_id # Pass item_id for rate validation
        )

        if error_message:
            raise ValidationError(error_message)

        return cleaned_data
```

#### 4.3 Views (`material_management/views.py`)

We'll define the main `PurchaseOrderItemTempCreateView` which mirrors the ASP.NET page's functionality. For completeness, other CRUD views are also provided as per the prompt.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.db import connection # For raw SQL if needed, though ORM preferred

from .models import PurchaseOrderItemTemp, PackingMaster, ExciseServiceTaxMaster, VatMaster, BudgetCode
from .forms import PurchaseOrderItemTempForm

# Helper function to mock `clsFunctions.select` for initial data.
# In a real application, this would be proper ORM queries or a dedicated service layer.
def get_pr_item_initial_display_data(comp_id, pr_id, wo_no, current_user_id):
    """
    Mimics the ASP.NET Page_Load initial data fetching for display labels and initial form values.
    Returns: (initial_form_data_dict, display_labels_dict, item_id_for_validation)
    """
    # This calls the custom manager method which encapsulates the complex logic
    initial_data, display_data, item_id = PurchaseOrderItemTemp.objects.get_initial_data_for_pr_item(
        comp_id, pr_id, wo_no, current_user_id
    )
    return initial_data, display_data, item_id


class PurchaseOrderItemTempListView(ListView):
    """
    Displays a list of all temporary purchase order items.
    This corresponds to what PO_PR_ItemGrid.aspx would show.
    """
    model = PurchaseOrderItemTemp
    template_name = 'material_management/purchaseorderitemtemp/list.html'
    context_object_name = 'temp_po_items'
    # No direct mapping from original ASP.NET page, added for general CRUD

class PurchaseOrderItemTempTablePartialView(ListView):
    """
    Renders only the table portion for HTMX requests to refresh the list.
    """
    model = PurchaseOrderItemTemp
    template_name = 'material_management/purchaseorderitemtemp/_purchaseorderitemtemp_table.html'
    context_object_name = 'temp_po_items'

class PurchaseOrderItemTempCreateView(CreateView):
    """
    Handles adding a new temporary purchase order item.
    This is the core functionality of the original PO_PR_ItemSelect.aspx.
    """
    model = PurchaseOrderItemTemp
    form_class = PurchaseOrderItemTempForm
    template_name = 'material_management/purchaseorderitemtemp/_purchaseorderitemtemp_form.html'
    success_url = reverse_lazy('temp_po_item_list') # Will trigger HX-Trigger

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass session/query string data to the form for validation and initial data
        kwargs['comp_id'] = self.request.session.get('compid', 1) # Default or retrieve from auth
        kwargs['session_id'] = self.request.session.get('username', 'system') # Default or retrieve from auth
        kwargs['pr_no'] = self.request.GET.get('prno', 'PR-000') # Query string parameter
        kwargs['pr_id'] = self.request.GET.get('prid', 1) # Query string parameter
        kwargs['item_id'] = self.request.GET.get('itemid', 101) # This needs to be derived dynamically, placeholder

        # Populate initial_data and display_data for labels and form fields
        # This calls the complex pre-population logic
        initial_data, display_data, item_id_from_pr = get_pr_item_initial_display_data(
            kwargs['comp_id'], kwargs['pr_id'], self.request.GET.get('wono', 'WO-000'), kwargs['session_id']
        )
        kwargs['initial'] = initial_data
        kwargs['display_data'] = display_data
        kwargs['item_id'] = item_id_from_pr # Update item_id if derived

        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass display_data to the template for the static labels
        context['display_data'] = self.get_form_kwargs()['display_data']
        # Add the URL for the Rate Register link
        context['rate_register_url'] = reverse_lazy('rate_register_print', kwargs={'item_id': self.get_form_kwargs()['item_id'], 'comp_id': self.get_form_kwargs()['comp_id']})
        return context

    def form_valid(self, form):
        # The form's clean method already validated and potentially called the manager's logic.
        # Now, we just save the instance. The manager's method `validate_and_insert_temp_item`
        # actually handles the creation for us if validation passes.
        comp_id = self.request.session.get('compid', 1)
        session_id = self.request.session.get('username', 'system')
        pr_no = self.request.GET.get('prno', 'PR-000')
        pr_id = self.request.GET.get('prid', 1)
        item_id = self.request.GET.get('itemid', 101) # Again, derived from PR details

        # Call the manager method that performs the actual insertion logic and validation
        temp_item, error_message = PurchaseOrderItemTemp.objects.validate_and_insert_temp_item(
            form_cleaned_data=form.cleaned_data,
            comp_id=comp_id,
            session_id=session_id,
            pr_no=pr_no,
            pr_id=pr_id,
            item_id=item_id
        )

        if temp_item:
            messages.success(self.request, 'Temporary PO Item added successfully.')
            if self.request.headers.get('HX-Request'):
                # For HTMX, return a 204 No Content response with HX-Trigger
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshTempPoItemList, closeFormModal'
                    }
                )
            return super().form_valid(form) # Fallback for non-HTMX
        else:
            # If validation failed within the manager, add error to form and re-render
            form.add_error(None, error_message)
            return self.form_invalid(form)


class PurchaseOrderItemTempUpdateView(UpdateView):
    model = PurchaseOrderItemTemp
    form_class = PurchaseOrderItemTempForm
    template_name = 'material_management/purchaseorderitemtemp/_purchaseorderitemtemp_form.html'
    success_url = reverse_lazy('temp_po_item_list')

    # This view is for updating an existing temp item.
    # The original ASP.NET page only "adds", it doesn't edit existing temp items.
    # Added for general CRUD completeness.
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Temporary PO Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTempPoItemList, closeFormModal'
                }
            )
        return response

class PurchaseOrderItemTempDeleteView(DeleteView):
    model = PurchaseOrderItemTemp
    template_name = 'material_management/purchaseorderitemtemp/confirm_delete.html'
    success_url = reverse_lazy('temp_po_item_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Temporary PO Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTempPoItemList, closeFormModal'
                }
            )
        return response

# Placeholder for Rate Register Print View (mimicking rt.HRef)
class RateRegisterPrintView(TemplateView):
    template_name = 'material_management/reports/rate_register_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_id = self.kwargs['item_id']
        comp_id = self.kwargs['comp_id']
        context['item_id'] = item_id
        context['comp_id'] = comp_id
        # In a real app, this would fetch data for the report based on item_id and comp_id
        context['report_data'] = {
            'message': f"Report for Item ID: {item_id}, Company ID: {comp_id}"
        }
        return context

```

#### 4.4 Templates (`material_management/templates/material_management/purchaseorderitemtemp/`)

**`list.html`**:
This will be the main entry point to view the temporary items and trigger the "Add" modal.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Temporary Purchase Order Items</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'temp_po_item_add' prno=request.GET.prno|default:'' prid=request.GET.prid|default:'' wono=request.GET.wono|default:'' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Item to PO
        </button>
    </div>
    
    <!-- HTMX container for the DataTable -->
    <div id="tempPoItemTable-container"
         hx-trigger="load, refreshTempPoItemList from:body"
         hx-get="{% url 'temp_po_item_table_partial' %}"
         hx-swap="innerHTML">
        <!-- Loading indicator -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading temporary PO items...</p>
        </div>
    </div>
    
    <!-- Global Modal Structure (shared across application) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false"
         _="on hx:afterOnLoad add .is-active to #modal
            on closeFormModal from body remove .is-active from #modal
            on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 my-8 relative overflow-y-auto max-h-[90vh]">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js logic directly for this list view needed,
        // as modal handling is global via htmx/hyperscript.
    });
</script>
{% endblock %}
```

**`_purchaseorderitemtemp_table.html`** (Partial template for HTMX list updates):

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="tempPoItemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount (%)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in temp_po_items %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.pr_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.rate }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.discount }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.del_date|date:"d-m-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'temp_po_item_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'temp_po_item_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No temporary PO items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables only if it hasn't been initialized
    if ($.fn.DataTable.isDataTable('#tempPoItemTable')) {
        $('#tempPoItemTable').DataTable().destroy();
    }
    $('#tempPoItemTable').DataTable({
        "pagingType": "full_numbers",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pageLength": 10
    });
</script>
```

**`_purchaseorderitemtemp_form.html`** (Partial template for HTMX form loading):

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Temporary PO Item</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <!-- Static PR/Item Details (Labels from ASP.NET Page_Load) -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4 mb-8 text-sm text-gray-700 bg-gray-50 p-4 rounded-lg shadow-sm">
            <div class="flex items-center">
                <span class="font-medium w-28">PR No:</span> <span>{{ display_data.pr_no }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-medium w-28">PR Qty:</span> <span>{{ display_data.pr_qty }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-medium w-28">WO No:</span> <span>{{ display_data.wo_no }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-medium w-28">Item Code:</span> <span>{{ display_data.item_code }}</span>
            </div>
            <div class="flex items-center md:col-span-2">
                <span class="font-medium w-28 flex-shrink-0">Item Desc:</span> <textarea readonly class="flex-grow border border-gray-300 rounded-md bg-gray-100 p-2 text-sm h-24 overflow-auto resize-none">{{ display_data.item_desc }}</textarea>
            </div>
             <div class="flex items-center md:col-span-3">
                <span class="font-medium w-28">A/c Head:</span> <span>{{ display_data.ac_head }}</span>
            </div>
        </div>

        <!-- Input Fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4">
            <div>
                <label for="{{ form.qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.qty.label }}<span class="text-red-500">*</span>
                </label>
                <div class="mt-1">
                    {{ form.qty }}
                </div>
                {% if form.qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.rate.label }}<span class="text-red-500">*</span>
                    {% if item_id and comp_id %}
                        <a href="{{ rate_register_url }}" target="_blank" class="ml-2 inline-block text-blue-500 hover:text-blue-700 text-xs">(View Rate Register <img src="/static/images/Rupee.JPG" alt="Rupee Icon" class="inline h-3 w-3">)</a>
                    {% endif %}
                </label>
                <div class="mt-1">
                    {{ form.rate }}
                </div>
                {% if form.rate.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.discount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.discount.label }}
                </label>
                <div class="mt-1">
                    {{ form.discount }}
                </div>
                {% if form.discount.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.pf.label }}
                </label>
                <div class="mt-1">
                    {{ form.pf }}
                </div>
                {% if form.pf.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.pf.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.ex_st.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.ex_st.label }}
                </label>
                <div class="mt-1">
                    {{ form.ex_st }}
                </div>
                {% if form.ex_st.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.ex_st.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.vat.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.vat.label }}
                </label>
                <div class="mt-1">
                    {{ form.vat }}
                </div>
                {% if form.vat.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.vat.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.del_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.del_date.label }}<span class="text-red-500">*</span>
                </label>
                <div class="mt-1">
                    {{ form.del_date }}
                </div>
                {% if form.del_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.del_date.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.budget_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.budget_code.label }}
                </label>
                <div class="mt-1">
                    {{ form.budget_code }}
                </div>
                {% if form.budget_code.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.budget_code.errors }}</p>
                {% endif %}
            </div>

             <div class="md:col-span-2 lg:col-span-3">
                <label for="{{ form.add_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.add_desc.label }}
                </label>
                <div class="mt-1">
                    {{ form.add_desc }}
                </div>
                {% if form.add_desc.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.add_desc.errors }}</p>
                {% endif %}
            </div>
        </div>

        {% if form.non_field_errors %}
        <div class="text-red-500 text-sm mt-4">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4 border-t pt-6">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click trigger closeFormModal on body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Add
            </button>
        </div>
    </form>
</div>

<!-- Flatpickr initialization for date input -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        flatpickr(".datepicker-input", {
            dateFormat: "d-m-Y", // Match ASP.NET format
            allowInput: true, // Allow manual input
            altInput: false, // Don't use alternative input field
            // Add any other desired Flatpickr options
        });
    });
</script>
```

**`confirm_delete.html`** (Partial template for HTMX delete confirmation):

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this temporary PO item (ID: {{ object.pk }})?</p>
    <form hx-delete="{% url 'temp_po_item_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click trigger closeFormModal on body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`material_management/urls.py`)

```python
from django.urls import path
from .views import (
    PurchaseOrderItemTempListView,
    PurchaseOrderItemTempCreateView,
    PurchaseOrderItemTempUpdateView,
    PurchaseOrderItemTempDeleteView,
    PurchaseOrderItemTempTablePartialView,
    RateRegisterPrintView,
)

urlpatterns = [
    # Main list view (might correspond to PO_PR_ItemGrid.aspx)
    path('temp_po_items/', PurchaseOrderItemTempListView.as_view(), name='temp_po_item_list'),
    
    # HTMX partial for the table content
    path('temp_po_items/table/', PurchaseOrderItemTempTablePartialView.as_view(), name='temp_po_item_table_partial'),

    # Form to add a new temporary PO item (main conversion target from PO_PR_ItemSelect.aspx)
    # Pass query parameters from original ASP.NET as URL parameters
    path(
        'temp_po_items/add/',
        PurchaseOrderItemTempCreateView.as_view(),
        name='temp_po_item_add'
    ),
    # Note: For `prno`, `prid`, `wono` passed as query parameters,
    # update 'temp_po_items/add/' URL or use request.GET in view.
    # For robust URL design, consider:
    # path('purchase_requests/<str:prno>/items/<int:prid>/add_temp/', PurchaseOrderItemTempCreateView.as_view(), name='temp_po_item_add'),
    # For this exercise, we will assume they are still passed via GET query params like original.

    # Update and Delete views (for general CRUD, though not primary for original .aspx)
    path('temp_po_items/edit/<int:pk>/', PurchaseOrderItemTempUpdateView.as_view(), name='temp_po_item_edit'),
    path('temp_po_items/delete/<int:pk>/', PurchaseOrderItemTempDeleteView.as_view(), name='temp_po_item_delete'),

    # Placeholder for Rate Register Print View
    path('reports/rate_register_print/<int:item_id>/<int:comp_id>/', RateRegisterPrintView.as_view(), name='rate_register_print'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, timedelta

from .models import (
    PackingMaster, ExciseServiceTaxMaster, VatMaster, BudgetCode,
    PurchaseOrderItemTemp
)

class ModelSetupMixin:
    """Helper to set up common test data for models."""
    @classmethod
    def setUpTestData(cls):
        cls.packing_master = PackingMaster.objects.create(id=1, terms='Packing Term 1')
        cls.excise_master = ExciseServiceTaxMaster.objects.create(id=1, terms='Excise Term 1')
        cls.vat_master = VatMaster.objects.create(id=1, terms='VAT Term 1')
        cls.budget_code = BudgetCode.objects.create(id=1, symbol='B123')

        # Create a sample temp item for update/delete tests
        cls.temp_item = PurchaseOrderItemTemp.objects.create(
            comp_id=1,
            session_id='testuser',
            pr_no='PR-ABC',
            pr_id=101,
            qty=50.0,
            rate=25.0,
            discount=5.0,
            add_desc='Test additional description',
            pf=cls.packing_master,
            ex_st=cls.excise_master,
            vat=cls.vat_master,
            del_date=date.today() + timedelta(days=7),
            budget_code=cls.budget_code
        )

class PurchaseOrderItemTempModelTest(ModelSetupMixin, TestCase):
    def test_packing_master_creation(self):
        self.assertEqual(self.packing_master.terms, 'Packing Term 1')
        self.assertEqual(str(self.packing_master), 'Packing Term 1')
        self.assertEqual(PackingMaster._meta.db_table, 'tblPacking_Master')
        self.assertFalse(PackingMaster._meta.managed)

    def test_purchase_order_item_temp_creation(self):
        obj = self.temp_item
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.pr_no, 'PR-ABC')
        self.assertAlmostEqual(obj.qty, 50.0)
        self.assertAlmostEqual(obj.final_rate, 23.75) # 25 - (25*5/100)
        self.assertEqual(obj.pf, self.packing_master)
        self.assertEqual(obj.del_date, date.today() + timedelta(days=7))
        self.assertEqual(str(obj), f"Temp PO Item {obj.id} for PR {obj.pr_no}")

    def test_validate_and_insert_temp_item_success(self):
        cleaned_data = {
            'qty': 10.0,
            'rate': 100.0,
            'discount': 10.0,
            'add_desc': 'New item description',
            'pf': self.packing_master,
            'ex_st': self.excise_master,
            'vat': self.vat_master,
            'del_date': date.today() + timedelta(days=10),
            'budget_code': self.budget_code
        }
        initial_count = PurchaseOrderItemTemp.objects.count()
        new_item, error = PurchaseOrderItemTemp.objects.validate_and_insert_temp_item(
            cleaned_data, comp_id=1, session_id='testuser2', pr_no='PR-XYZ', pr_id=202, item_id=200
        )
        self.assertIsNone(error)
        self.assertIsNotNone(new_item)
        self.assertEqual(PurchaseOrderItemTemp.objects.count(), initial_count + 1)
        self.assertEqual(new_item.pr_no, 'PR-XYZ')

    def test_validate_and_insert_temp_item_invalid_qty(self):
        cleaned_data = {
            'qty': 0.0, # Invalid quantity
            'rate': 100.0, 'discount': 0.0, 'add_desc': '', 'del_date': date.today() + timedelta(days=10),
            'pf': self.packing_master, 'ex_st': self.excise_master, 'vat': self.vat_master, 'budget_code': self.budget_code
        }
        new_item, error = PurchaseOrderItemTemp.objects.validate_and_insert_temp_item(
            cleaned_data, comp_id=1, session_id='testuser2', pr_no='PR-XYZ', pr_id=202, item_id=200
        )
        self.assertIsNone(new_item)
        self.assertIn("Entered Qty is not acceptable!", error)

    def test_validate_and_insert_temp_item_past_del_date(self):
        cleaned_data = {
            'qty': 10.0,
            'rate': 100.0, 'discount': 0.0, 'add_desc': '',
            'del_date': date.today() - timedelta(days=1), # Invalid date
            'pf': self.packing_master, 'ex_st': self.excise_master, 'vat': self.vat_master, 'budget_code': self.budget_code
        }
        new_item, error = PurchaseOrderItemTemp.objects.validate_and_insert_temp_item(
            cleaned_data, comp_id=1, session_id='testuser2', pr_no='PR-XYZ', pr_id=202, item_id=200
        )
        self.assertIsNone(new_item)
        self.assertIn("Entered Date is not acceptable!", error)

    def test_validate_and_insert_temp_item_invalid_final_rate(self):
        cleaned_data = {
            'qty': 10.0, 'rate': 10.0, 'discount': 100.0, # Rate becomes 0
            'add_desc': '', 'del_date': date.today() + timedelta(days=10),
            'pf': self.packing_master, 'ex_st': self.excise_master, 'vat': self.vat_master, 'budget_code': self.budget_code
        }
        new_item, error = PurchaseOrderItemTemp.objects.validate_and_insert_temp_item(
            cleaned_data, comp_id=1, session_id='testuser2', pr_no='PR-XYZ', pr_id=202, item_id=200
        )
        self.assertIsNone(new_item)
        self.assertIn("Final rate after discount must be positive", error)

    # Test the initial data fetching logic
    def test_get_initial_data_for_pr_item(self):
        # This test relies on mock data as the actual DB relationships are complex.
        initial_data, display_data, item_id = PurchaseOrderItemTemp.objects.get_initial_data_for_pr_item(
            comp_id=1, pr_id=101, wo_no='WO-TEST', current_user_id='testuser'
        )
        self.assertIn('qty', initial_data)
        self.assertIn('pr_no', display_data)
        self.assertEqual(display_data['wo_no'], 'WO-TEST')
        # Check if budget codes are populated dynamically
        self.assertGreater(len(display_data['budget_codes']), 0)


class PurchaseOrderItemTempViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session attributes for authentication/context
        session = self.client.session
        session['compid'] = 1
        session['username'] = 'testuser'
        session.save()
        # Query parameters mimicking ASP.NET request
        self.query_params = '?prno=PR-DEF&prid=102&wono=WO-XYZ&itemid=102'

    def test_list_view(self):
        response = self.client.get(reverse('temp_po_item_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderitemtemp/list.html')
        self.assertIn('temp_po_items', response.context)
        self.assertContains(response, self.temp_item.pr_no)

    def test_table_partial_view(self):
        response = self.client.get(reverse('temp_po_item_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderitemtemp/_purchaseorderitemtemp_table.html')
        self.assertIn('temp_po_items', response.context)
        self.assertContains(response, self.temp_item.pr_no)
        self.assertContains(response, "Edit") # Check for action buttons

    def test_create_view_get(self):
        response = self.client.get(reverse('temp_po_item_add') + self.query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderitemtemp/_purchaseorderitemtemp_form.html')
        self.assertIn('form', response.context)
        self.assertIn('display_data', response.context)
        self.assertContains(response, 'Add Temporary PO Item') # Check for modal title
        self.assertContains(response, 'PR-DEF') # Check if query params are reflected

    def test_create_view_post_success(self):
        data = {
            'qty': 25.0,
            'rate': 75.0,
            'discount': 1.5,
            'add_desc': 'New temp item',
            'pf': self.packing_master.id,
            'ex_st': self.excise_master.id,
            'vat': self.vat_master.id,
            'del_date': (date.today() + timedelta(days=15)).strftime('%d-%m-%Y'), # Match form format
            'budget_code': self.budget_code.id,
        }
        initial_count = PurchaseOrderItemTemp.objects.count()
        response = self.client.post(reverse('temp_po_item_add') + self.query_params, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(PurchaseOrderItemTemp.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTempPoItemList', response.headers['HX-Trigger'])
        self.assertTrue(PurchaseOrderItemTemp.objects.filter(pr_no='PR-DEF', qty=25.0).exists())

    def test_create_view_post_validation_fail(self):
        data = {
            'qty': 0.0, # Invalid quantity
            'rate': 75.0, 'discount': 0.0, 'add_desc': '',
            'pf': self.packing_master.id, 'ex_st': self.excise_master.id, 'vat': self.vat_master.id,
            'del_date': (date.today() + timedelta(days=15)).strftime('%d-%m-%Y'),
            'budget_code': self.budget_code.id,
        }
        initial_count = PurchaseOrderItemTemp.objects.count()
        response = self.client.post(reverse('temp_po_item_add') + self.query_params, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'material_management/purchaseorderitemtemp/_purchaseorderitemtemp_form.html')
        self.assertContains(response, "Entered Qty is not acceptable!") # Check for error message
        self.assertEqual(PurchaseOrderItemTemp.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        response = self.client.get(reverse('temp_po_item_edit', args=[self.temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderitemtemp/_purchaseorderitemtemp_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.temp_item)
        self.assertContains(response, 'Edit Temporary PO Item')

    def test_update_view_post_success(self):
        updated_data = {
            'qty': 60.0,
            'rate': 30.0,
            'discount': 0.0,
            'add_desc': 'Updated description',
            'pf': self.packing_master.id,
            'ex_st': self.excise_master.id,
            'vat': self.vat_master.id,
            'del_date': (date.today() + timedelta(days=20)).strftime('%d-%m-%Y'),
            'budget_code': self.budget_code.id,
        }
        response = self.client.post(reverse('temp_po_item_edit', args=[self.temp_item.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.temp_item.refresh_from_db()
        self.assertAlmostEqual(self.temp_item.qty, 60.0)
        self.assertEqual(self.temp_item.add_desc, 'Updated description')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTempPoItemList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(reverse('temp_po_item_delete', args=[self.temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderitemtemp/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.temp_item)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post_success(self):
        temp_item_to_delete = PurchaseOrderItemTemp.objects.create(
            comp_id=1, session_id='deltest', pr_no='PR-DEL', pr_id=999, qty=10, rate=10, discount=0,
            del_date=date.today() + timedelta(days=1), pf=self.packing_master, ex_st=self.excise_master,
            vat=self.vat_master, budget_code=self.budget_code
        )
        initial_count = PurchaseOrderItemTemp.objects.count()
        response = self.client.delete(reverse('temp_po_item_delete', args=[temp_item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(PurchaseOrderItemTemp.objects.count(), initial_count - 1)
        self.assertFalse(PurchaseOrderItemTemp.objects.filter(pk=temp_item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTempPoItemList', response.headers['HX-Trigger'])

    def test_rate_register_print_view(self):
        response = self.client.get(reverse('rate_register_print', kwargs={'item_id': 123, 'comp_id': 1}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/reports/rate_register_print.html')
        self.assertContains(response, "Report for Item ID: 123, Company ID: 1")

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for CRUD:**
    *   The "Add New Item to PO" button (`list.html`) uses `hx-get` to fetch the form (`_purchaseorderitemtemp_form.html`) into the modal (`#modalContent`).
    *   Form submission (`_purchaseorderitemtemp_form.html`) uses `hx-post` with `hx-swap="none"`. Upon success, the view returns a `204 No Content` response with `HX-Trigger` headers (`refreshTempPoItemList, closeFormModal`).
    *   `refreshTempPoItemList` triggers a reload of the table partial (`_purchaseorderitemtemp_table.html`) in `#tempPoItemTable-container`.
    *   `closeFormModal` (custom event) is caught by the modal's hyperscript to hide itself.
    *   Edit/Delete buttons (`_purchaseorderitemtemp_table.html`) use `hx-get` to fetch their respective forms (`_purchaseorderitemtemp_form.html` or `confirm_delete.html`) into the modal.
    *   Delete form submission (`confirm_delete.html`) uses `hx-delete` with `hx-swap="none"`, also triggering `refreshTempPoItemList, closeFormModal` on success.

*   **Alpine.js for Modals (Hyperscript fallback):**
    *   The `list.html` includes a global modal div (`#modal`) with `x-data="{ showModal: false }"`.
    *   Hyperscript (`_`) handles showing the modal when HTMX loads content into `#modalContent` (`on hx:afterOnLoad add .is-active to #modal`).
    *   Hyperscript also handles hiding the modal when the "Cancel" button is clicked or when clicking outside the modal (`on click remove .is-active from me`).
    *   A custom `closeFormModal` event is triggered from views upon successful form submission or from modal cancel buttons.

*   **DataTables for List Views:**
    *   The `_purchaseorderitemtemp_table.html` partial initializes a DataTables instance on the table `tempPoItemTable`.
    *   It's designed to be re-initialized each time the partial is loaded by HTMX. The `destroy()` method is used to clean up previous DataTable instances before re-initializing.

*   **DRY Template Inheritance:**
    *   All component templates (`list.html`, `_purchaseorderitemtemp_table.html`, etc.) extend `core/base.html` (as per the prompt's instruction, base.html itself is not included). This ensures all CDN links for HTMX, Alpine.js, jQuery, DataTables, and Flatpickr are in `base.html`.

*   **Date Picker (Flatpickr):**
    *   The `del_date` field in `_purchaseorderitemtemp_form.html` uses an `input type="date"` combined with a JavaScript initialization for Flatpickr, which provides a modern, user-friendly date selection experience.

### Final Notes

This comprehensive plan transforms the ASP.NET `PO_PR_ItemSelect.aspx` functionality into a modern Django application, adhering to the "Fat Model, Thin View" principle, leveraging HTMX and Alpine.js for dynamic interactions, DataTables for data presentation, and including robust test coverage. The complex `Page_Load` logic for initial data fetching and validation has been encapsulated within the `PurchaseOrderItemTempManager` for a clean separation of concerns and maintainable code. Placeholders for direct database interactions (like `tblMM_PR_Master` lookups) indicate areas where a full ORM mapping of the entire legacy database would be required in a complete migration project.