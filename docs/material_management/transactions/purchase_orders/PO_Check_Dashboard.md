## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The provided ASP.NET code (`PO_Check_Dashboard.aspx` and its C# code-behind) is very minimal, essentially a placeholder for a dashboard page with no explicit UI controls, data binding, or backend logic. This means we must infer the intended functionality and data structure based on the file name "PO_Check_Dashboard," which suggests a Purchase Order review or management interface.

Our modernization plan will assume a typical Purchase Order management dashboard that allows users to view, add, edit, and delete purchase orders. This is a common requirement for such a module, providing full CRUD (Create, Read, Update, Delete) capabilities.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the ASP.NET code does not contain explicit database schema definitions (like `SqlDataSource` or direct SQL queries), we infer the schema based on common "Purchase Order" attributes.

- **Inferred Table Name:** `tbl_purchase_order`
- **Inferred Column Names and Data Types:**
    - `id` (Primary Key, integer) - Django handles this automatically.
    - `po_number` (String, e.g., 'PO-00123')
    - `supplier_name` (String, e.g., 'ABC Suppliers Ltd.')
    - `order_date` (Date)
    - `total_amount` (Decimal, e.g., 1234.56)
    - `status` (String, e.g., 'Pending', 'Approved', 'Rejected', 'Cancelled')

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the nature of a "Dashboard" in a Material Management module, it's highly probable that users would not only "check" (Read) purchase orders but also initiate, modify, or finalize them. Therefore, we infer the need for full CRUD operations to manage Purchase Orders.

- **Create:** Ability to add new Purchase Orders.
- **Read:** Display a list of all Purchase Orders with filtering/sorting capabilities (the primary function of a "Check Dashboard").
- **Update:** Ability to modify existing Purchase Order details.
- **Delete:** Ability to remove Purchase Orders.
- **Validation:** Basic field validation (e.g., required fields, data type checks) will be implemented in Django Forms.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

From the `PO_Check_Dashboard` name, we infer the following typical UI components and their Django equivalents:

-   **Dashboard/List View:** An equivalent of an ASP.NET `GridView` to display a list of Purchase Orders. This will be implemented using Django's `ListView` and a DataTables-powered HTML table.
-   **Form Controls:** For adding or editing Purchase Orders, equivalent to ASP.NET `TextBox`, `DropDownList` (for status), etc. These will be rendered by a Django `ModelForm`.
-   **Action Buttons:** Buttons for "Add New", "Edit", and "Delete" actions. These will leverage HTMX for dynamic modal loading and submission.
-   **Client-side Interactions:** The `loadingNotifier.js` hints at client-side feedback. This will be handled gracefully by HTMX's loading indicators and Alpine.js for modal state management.

---

## Step 4: Generate Django Code

We will create a new Django app named `po_dashboard` to house this functionality.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `PurchaseOrder` model is defined, mapping to the `tbl_purchase_order` table. Business logic for a Purchase Order (e.g., calculating total amount, status transitions) would reside as methods within this model.

**File: `po_dashboard/models.py`**

```python
from django.db import models
from django.core.exceptions import ValidationError
from decimal import Decimal

class PurchaseOrder(models.Model):
    """
    Represents a Purchase Order within the Material Management module.
    """
    po_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name="PO Number",
        help_text="Unique identification number for the Purchase Order"
    )
    supplier_name = models.CharField(
        max_length=255,
        verbose_name="Supplier Name",
        help_text="Name of the supplier for this Purchase Order"
    )
    order_date = models.DateField(
        verbose_name="Order Date",
        help_text="The date the purchase order was issued"
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name="Total Amount",
        help_text="The total monetary value of the purchase order"
    )
    status_choices = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
        ('Cancelled', 'Cancelled'),
        ('Completed', 'Completed'),
    ]
    status = models.CharField(
        max_length=20,
        choices=status_choices,
        default='Pending',
        verbose_name="Status",
        help_text="Current status of the Purchase Order"
    )

    class Meta:
        managed = False  # Set to True if Django should manage table creation/alteration
        db_table = 'tbl_purchase_order'  # Maps to the inferred ASP.NET table name
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'
        ordering = ['-order_date', 'po_number'] # Default ordering

    def __str__(self):
        return f"PO: {self.po_number} - {self.supplier_name}"

    def update_status(self, new_status):
        """
        Business logic for updating the status of a Purchase Order.
        Ensures valid status transitions.
        """
        valid_transitions = {
            'Pending': ['Approved', 'Rejected', 'Cancelled'],
            'Approved': ['Completed', 'Cancelled'],
            'Rejected': [],
            'Cancelled': [],
            'Completed': [],
        }

        if new_status not in self.status_choices:
            raise ValidationError(f"Invalid status: {new_status}")

        if self.status == new_status:
            return # No change needed

        if new_status in valid_transitions.get(self.status, []):
            self.status = new_status
            self.save()
        else:
            raise ValidationError(
                f"Cannot change status from '{self.status}' to '{new_status}'."
            )

    def calculate_tax(self, tax_rate_percent=0.08):
        """
        Example of business logic: calculate tax based on total amount.
        """
        return self.total_amount * Decimal(str(tax_rate_percent))

    def mark_as_completed(self):
        """
        Marks the purchase order as completed, if approved.
        """
        if self.status == 'Approved':
            self.update_status('Completed')
            return True
        return False
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` is created for `PurchaseOrder`, providing all necessary fields. Widgets are added to apply Tailwind CSS classes for consistent styling.

**File: `po_dashboard/forms.py`**

```python
from django import forms
from .models import PurchaseOrder

class PurchaseOrderForm(forms.ModelForm):
    class Meta:
        model = PurchaseOrder
        fields = ['po_number', 'supplier_name', 'order_date', 'total_amount', 'status']
        widgets = {
            'po_number': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., PO-2023-001'
            }),
            'supplier_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., ABC Electronics'
            }),
            'order_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date' # HTML5 date input
            }),
            'total_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01', # Allow decimal input
                'placeholder': 'e.g., 1234.56'
            }),
            'status': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }

    def clean_total_amount(self):
        total_amount = self.cleaned_data.get('total_amount')
        if total_amount is not None and total_amount < 0:
            raise forms.ValidationError("Total amount cannot be negative.")
        return total_amount

    def clean(self):
        cleaned_data = super().clean()
        # Example of cross-field validation
        order_date = cleaned_data.get('order_date')
        if order_date and order_date.year < 2000:
            self.add_error('order_date', "Order date cannot be before the year 2000.")
        return cleaned_data
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Class-Based Views (CBVs) are used for list, create, update, and delete operations. An additional view, `PurchaseOrderTablePartialView`, is included to serve the DataTables content dynamically via HTMX, keeping the main list view lightweight and allowing for efficient refreshes. Views remain thin, delegating complex logic to the `PurchaseOrder` model.

**File: `po_dashboard/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import PurchaseOrder
from .forms import PurchaseOrderForm

class PurchaseOrderListView(ListView):
    """
    Renders the main Purchase Order dashboard page.
    The actual table content is loaded via HTMX.
    """
    model = PurchaseOrder
    template_name = 'po_dashboard/purchaseorder/list.html'
    context_object_name = 'purchaseorders' # Not directly used in the main page, but good practice

class PurchaseOrderTablePartialView(TemplateView):
    """
    Renders the partial HTML for the Purchase Order table,
    designed to be loaded via HTMX for dynamic updates.
    """
    template_name = 'po_dashboard/purchaseorder/_purchaseorder_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetch all purchase orders for the table
        context['purchaseorders'] = PurchaseOrder.objects.all()
        return context

class PurchaseOrderCreateView(CreateView):
    """
    Handles creation of new Purchase Orders, supporting HTMX for modal forms.
    """
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'po_dashboard/purchaseorder/_purchaseorder_form.html' # Use partial for modal
    success_url = reverse_lazy('purchaseorder_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList, closeModal'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors in the modal
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # HTMX will swap the target with the form containing errors
        return response

class PurchaseOrderUpdateView(UpdateView):
    """
    Handles updating existing Purchase Orders, supporting HTMX for modal forms.
    """
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'po_dashboard/purchaseorder/_purchaseorder_form.html' # Use partial for modal
    success_url = reverse_lazy('purchaseorder_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList, closeModal'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors in the modal
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class PurchaseOrderDeleteView(DeleteView):
    """
    Handles deleting Purchase Orders, supporting HTMX for modal confirmation.
    """
    model = PurchaseOrder
    template_name = 'po_dashboard/purchaseorder/_purchaseorder_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('purchaseorder_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        # Business logic for deletion if any, could be in model.
        # For example, prevent deletion if status is 'Approved'.
        # obj = self.get_object()
        # if obj.status == 'Approved':
        #     messages.error(request, 'Cannot delete an Approved Purchase Order.')
        #     if request.headers.get('HX-Request'):
        #         return HttpResponse(status=400, content="Cannot delete approved PO.")
        #     return self.get_success_url() # Redirect to list on error for non-HTMX

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Order deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList, closeModal'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates follow DRY principles by extending `core/base.html` (not included here) and using partials for reusable components like forms and the table itself. HTMX attributes are extensively used to enable dynamic interactions and modal pop-ups for CRUD operations, while DataTables handles client-side table functionality. Alpine.js is used for simple UI state like modal visibility.

**File: `po_dashboard/templates/po_dashboard/purchaseorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Order Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'purchaseorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal and remove .hidden from #modal">
            <i class="fas fa-plus mr-2"></i>Add New Purchase Order
        </button>
    </div>
    
    <div id="purchaseorderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'purchaseorder_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center p-8 bg-white rounded-lg shadow-md">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Purchase Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden items-center justify-center z-50"
         _="on click if event.target.id == 'modal' remove .flex from me and add .hidden to me trigger closeModal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 my-8 relative">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            open: false,
            show() { this.open = true },
            hide() { this.open = false }
        });
    });

    document.body.addEventListener('closeModal', function() {
        const modal = document.getElementById('modal');
        modal.classList.remove('flex');
        modal.classList.add('hidden');
        document.getElementById('modalContent').innerHTML = ''; // Clear modal content
    });
</script>
{% endblock %}
```

**File: `po_dashboard/templates/po_dashboard/purchaseorder/_purchaseorder_table.html`**

```html
<div class="bg-white shadow-md rounded-lg p-6">
    <table id="purchaseorderTable" class="min-w-full bg-white text-gray-700">
        <thead>
            <tr class="bg-gray-100 border-b border-gray-200">
                <th class="py-3 px-4 text-left text-xs font-semibold uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold uppercase tracking-wider">PO Number</th>
                <th class="py-3 px-4 text-left text-xs font-semibold uppercase tracking-wider">Supplier Name</th>
                <th class="py-3 px-4 text-left text-xs font-semibold uppercase tracking-wider">Order Date</th>
                <th class="py-3 px-4 text-left text-xs font-semibold uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-4 text-left text-xs font-semibold uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-semibold uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for po in purchaseorders %}
            <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-2 px-4 text-sm">{{ forloop.counter }}</td>
                <td class="py-2 px-4 text-sm font-medium">{{ po.po_number }}</td>
                <td class="py-2 px-4 text-sm">{{ po.supplier_name }}</td>
                <td class="py-2 px-4 text-sm">{{ po.order_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 text-sm">${{ po.total_amount|floatformat:2 }}</td>
                <td class="py-2 px-4 text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if po.status == 'Approved' %}bg-green-100 text-green-800
                        {% elif po.status == 'Pending' %}bg-yellow-100 text-yellow-800
                        {% elif po.status == 'Rejected' or po.status == 'Cancelled' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ po.status }}
                    </span>
                </td>
                <td class="py-2 px-4 text-sm whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'purchaseorder_edit' po.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal and remove .hidden from #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'purchaseorder_delete' po.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal and remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No Purchase Orders found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization
// This script runs when the HTMX content is swapped into the DOM.
// We use a timeout to ensure the DOM is fully settled, though HTMX usually handles this well.
$(document).ready(function() {
    $('#purchaseorderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "responsive": true,
        "autoWidth": false
    });
});
</script>
```

**File: `po_dashboard/templates/po_dashboard/purchaseorder/_purchaseorder_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Purchase Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-6">
            <ul class="text-sm text-red-600">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <span id="form-indicator" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save Purchase Order
            </button>
        </div>
    </form>
</div>
```

**File: `po_dashboard/templates/po_dashboard/purchaseorder/_purchaseorder_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete Purchase Order <strong>"{{ object.po_number }}"</strong> from <strong>{{ object.supplier_name }}</strong>?</p>
    <p class="text-red-600 font-medium">This action cannot be undone.</p>

    <form hx-post="{% url 'purchaseorder_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <span id="delete-indicator" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns are set up for the main list view, the partial table view (for HTMX refreshes), and the create, update, and delete operations.

**File: `po_dashboard/urls.py`**

```python
from django.urls import path
from .views import (
    PurchaseOrderListView,
    PurchaseOrderTablePartialView,
    PurchaseOrderCreateView,
    PurchaseOrderUpdateView,
    PurchaseOrderDeleteView
)

urlpatterns = [
    # Main dashboard view
    path('purchaseorders/', PurchaseOrderListView.as_view(), name='purchaseorder_list'),

    # HTMX-specific endpoint for the table content
    path('purchaseorders/table/', PurchaseOrderTablePartialView.as_view(), name='purchaseorder_table'),

    # CRUD operations
    path('purchaseorders/add/', PurchaseOrderCreateView.as_view(), name='purchaseorder_add'),
    path('purchaseorders/edit/<int:pk>/', PurchaseOrderUpdateView.as_view(), name='purchaseorder_edit'),
    path('purchaseorders/delete/<int:pk>/', PurchaseOrderDeleteView.as_view(), name='purchaseorder_delete'),
]
```

**Project's `urls.py` (e.g., `myproject/urls.py`):**

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('material_management/', include('po_dashboard.urls')), # Include PO Dashboard app URLs
    # ... other project URLs
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests cover model methods and field properties, ensuring business logic works as expected. Integration tests verify the functionality of all views (list, create, update, delete) and specific HTMX interactions, asserting correct status codes, template usage, and data manipulation.

**File: `po_dashboard/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from django.core.exceptions import ValidationError
from .models import PurchaseOrder
from .forms import PurchaseOrderForm

class PurchaseOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.po1 = PurchaseOrder.objects.create(
            po_number='PO-TEST-001',
            supplier_name='Test Supplier A',
            order_date=timezone.now().date(),
            total_amount=Decimal('100.00'),
            status='Pending'
        )
        cls.po2 = PurchaseOrder.objects.create(
            po_number='PO-TEST-002',
            supplier_name='Test Supplier B',
            order_date=timezone.now().date(),
            total_amount=Decimal('250.75'),
            status='Approved'
        )
  
    def test_purchaseorder_creation(self):
        """Test that a PurchaseOrder object is created correctly."""
        po = PurchaseOrder.objects.get(po_number='PO-TEST-001')
        self.assertEqual(po.supplier_name, 'Test Supplier A')
        self.assertEqual(po.total_amount, Decimal('100.00'))
        self.assertEqual(po.status, 'Pending')
        self.assertIsNotNone(po.pk) # Check if primary key exists

    def test_po_number_label(self):
        """Test the verbose name for 'po_number' field."""
        po = PurchaseOrder.objects.get(pk=self.po1.pk)
        field_label = po._meta.get_field('po_number').verbose_name
        self.assertEqual(field_label, 'PO Number')

    def test_total_amount_decimal_places(self):
        """Test that total_amount respects decimal_places."""
        po = PurchaseOrder.objects.get(pk=self.po2.pk)
        self.assertEqual(po.total_amount.as_tuple().exponent, -2) # Checks 2 decimal places

    def test_str_method(self):
        """Test the __str__ method of the model."""
        po = PurchaseOrder.objects.get(pk=self.po1.pk)
        self.assertEqual(str(po), f"PO: {po.po_number} - {po.supplier_name}")

    def test_update_status_valid_transition(self):
        """Test valid status transition."""
        po = PurchaseOrder.objects.get(pk=self.po1.pk) # Status: Pending
        po.update_status('Approved')
        self.assertEqual(po.status, 'Approved')

    def test_update_status_invalid_transition(self):
        """Test invalid status transition raises ValidationError."""
        po = PurchaseOrder.objects.get(pk=self.po2.pk) # Status: Approved
        with self.assertRaises(ValidationError):
            po.update_status('Pending') # Cannot go from Approved to Pending

    def test_update_status_invalid_status_value(self):
        """Test setting an invalid status value raises ValidationError."""
        po = PurchaseOrder.objects.get(pk=self.po1.pk)
        with self.assertRaises(ValidationError):
            po.update_status('InvalidStatus')

    def test_calculate_tax_method(self):
        """Test the calculate_tax method."""
        po = PurchaseOrder.objects.get(pk=self.po1.pk) # total_amount=100.00
        tax = po.calculate_tax(0.10) # 10% tax
        self.assertEqual(tax, Decimal('10.00'))

    def test_mark_as_completed(self):
        """Test marking PO as completed."""
        po_approved = PurchaseOrder.objects.get(pk=self.po2.pk) # Status: Approved
        self.assertTrue(po_approved.mark_as_completed())
        po_approved.refresh_from_db()
        self.assertEqual(po_approved.status, 'Completed')

        po_pending = PurchaseOrder.objects.get(pk=self.po1.pk) # Status: Pending
        self.assertFalse(po_pending.mark_as_completed())
        po_pending.refresh_from_db()
        self.assertEqual(po_pending.status, 'Pending') # Status should not change

class PurchaseOrderFormTest(TestCase):
    def test_form_valid_data(self):
        form = PurchaseOrderForm(data={
            'po_number': 'PO-FORM-001',
            'supplier_name': 'Form Test Supplier',
            'order_date': '2023-01-15',
            'total_amount': '500.00',
            'status': 'Pending'
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_total_amount(self):
        form = PurchaseOrderForm(data={
            'po_number': 'PO-FORM-002',
            'supplier_name': 'Invalid Amount Supplier',
            'order_date': '2023-02-01',
            'total_amount': '-10.00', # Invalid negative amount
            'status': 'Approved'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('total_amount', form.errors)
        self.assertEqual(form.errors['total_amount'], ["Total amount cannot be negative."])

    def test_form_missing_required_fields(self):
        form = PurchaseOrderForm(data={
            'po_number': '', # Missing
            'supplier_name': 'Partial Data',
            'order_date': '2023-03-01',
            'total_amount': '100.00',
            'status': 'Pending'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('po_number', form.errors)


class PurchaseOrderViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.po1 = PurchaseOrder.objects.create(
            po_number='PO-VIEW-001',
            supplier_name='View Test Supplier',
            order_date=timezone.now().date(),
            total_amount=Decimal('150.00'),
            status='Pending'
        )
        self.po2 = PurchaseOrder.objects.create(
            po_number='PO-VIEW-002',
            supplier_name='Another View Supplier',
            order_date=timezone.now().date(),
            total_amount=Decimal('300.00'),
            status='Approved'
        )

    def test_list_view_get(self):
        """Test the main Purchase Order list page."""
        response = self.client.get(reverse('purchaseorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_dashboard/purchaseorder/list.html')
        # Check that the table container is present, which will trigger HTMX load
        self.assertContains(response, 'id="purchaseorderTable-container"')

    def test_table_partial_view_get(self):
        """Test the HTMX-loaded table partial view."""
        response = self.client.get(reverse('purchaseorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_dashboard/purchaseorder/_purchaseorder_table.html')
        self.assertContains(response, self.po1.po_number)
        self.assertContains(response, self.po2.po_number)
        self.assertTrue('purchaseorders' in response.context)
        self.assertEqual(len(response.context['purchaseorders']), 2)

    def test_create_view_get(self):
        """Test GET request for the create form (HTMX modal)."""
        response = self.client.get(reverse('purchaseorder_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_dashboard/purchaseorder/_purchaseorder_form.html')
        self.assertContains(response, 'Add Purchase Order')
        self.assertTrue('form' in response.context)

    def test_create_view_post_valid(self):
        """Test POST request for creating a new PO (HTMX valid submission)."""
        data = {
            'po_number': 'PO-NEW-003',
            'supplier_name': 'New Supplier Corp',
            'order_date': '2023-04-01',
            'total_amount': '750.00',
            'status': 'Pending'
        }
        response = self.client.post(reverse('purchaseorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(PurchaseOrder.objects.filter(po_number='PO-NEW-003').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """Test POST request for creating a new PO (HTMX invalid submission)."""
        data = {
            'po_number': '', # Invalid, missing required field
            'supplier_name': 'Invalid Data',
            'order_date': '2023-05-01',
            'total_amount': '-50.00', # Invalid, negative amount
            'status': 'Approved'
        }
        response = self.client.post(reverse('purchaseorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Re-renders form with errors
        self.assertTemplateUsed(response, 'po_dashboard/purchaseorder/_purchaseorder_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Total amount cannot be negative.')
        self.assertFalse(PurchaseOrder.objects.filter(supplier_name='Invalid Data').exists())

    def test_update_view_get(self):
        """Test GET request for the update form (HTMX modal)."""
        response = self.client.get(reverse('purchaseorder_edit', args=[self.po1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_dashboard/purchaseorder/_purchaseorder_form.html')
        self.assertContains(response, 'Edit Purchase Order')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.pk, self.po1.pk)

    def test_update_view_post_valid(self):
        """Test POST request for updating an existing PO (HTMX valid submission)."""
        data = {
            'po_number': self.po1.po_number,
            'supplier_name': 'Updated Supplier Name',
            'order_date': str(self.po1.order_date),
            'total_amount': '200.00',
            'status': 'Approved'
        }
        response = self.client.post(reverse('purchaseorder_edit', args=[self.po1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.supplier_name, 'Updated Supplier Name')
        self.assertEqual(self.po1.total_amount, Decimal('200.00'))
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        """Test GET request for delete confirmation (HTMX modal)."""
        response = self.client.get(reverse('purchaseorder_delete', args=[self.po1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_dashboard/purchaseorder/_purchaseorder_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.po1.po_number)

    def test_delete_view_post_valid(self):
        """Test POST request for deleting a PO (HTMX valid submission)."""
        initial_count = PurchaseOrder.objects.count()
        response = self.client.post(reverse('purchaseorder_delete', args=[self.po1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertEqual(PurchaseOrder.objects.count(), initial_count - 1)
        self.assertFalse(PurchaseOrder.objects.filter(pk=self.po1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])

    def test_non_htmx_redirects(self):
        """Test non-HTMX requests redirect to list view after CRUD."""
        # Create
        data = {
            'po_number': 'PO-NONHTMX-004',
            'supplier_name': 'Non HTMX Supplier',
            'order_date': '2023-06-01',
            'total_amount': '99.99',
            'status': 'Pending'
        }
        response = self.client.post(reverse('purchaseorder_add'), data)
        self.assertRedirects(response, reverse('purchaseorder_list'))

        # Update
        self.po1.refresh_from_db() # Ensure latest state for update
        update_data = {
            'po_number': self.po1.po_number,
            'supplier_name': 'Non HTMX Updated',
            'order_date': str(self.po1.order_date),
            'total_amount': '180.00',
            'status': 'Approved'
        }
        response = self.client.post(reverse('purchaseorder_edit', args=[self.po1.pk]), update_data)
        self.assertRedirects(response, reverse('purchaseorder_list'))

        # Delete
        response = self.client.post(reverse('purchaseorder_delete', args=[self.po2.pk]))
        self.assertRedirects(response, reverse('purchaseorder_list'))
```

---

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The generated Django templates and views are designed with HTMX and Alpine.js as the primary tools for dynamic interactions, completely avoiding traditional JavaScript frameworks or custom AJAX calls beyond what HTMX provides.

-   **HTMX for dynamic updates:**
    -   The main `list.html` page uses `hx-get` on `purchaseorderTable-container` to load the table content from `{% url 'purchaseorder_table' %}`. This ensures that the table can be refreshed independently without a full page reload.
    -   CRUD buttons (`Add New`, `Edit`, `Delete`) use `hx-get` to fetch the form or confirmation partials into a modal (`#modalContent`).
    -   Form submissions (`hx-post`) are configured with `hx-swap="none"` and return `HTTP 204 No Content` from Django views, accompanied by `HX-Trigger` headers (`refreshPurchaseOrderList, closeModal`) to instruct the browser to refresh the table and close the modal after a successful operation.
    -   Loading indicators (`htmx-indicator`) are used on submit buttons to provide visual feedback during form processing.
-   **Alpine.js for UI state management:**
    -   A simple Alpine.js `x-data` attribute or `Alpine.store` is used to manage the modal's visibility. The `on click` event listener for the modal background allows closing the modal when clicking outside its content. Custom events like `closeModal` triggered by HTMX or htmx.org/docs/attributes/hx-trigger.html provide a clean way to interact with Alpine.js state.
-   **DataTables for list views:**
    -   The `_purchaseorder_table.html` partial directly includes the jQuery DataTables initialization script. This script runs when the partial is loaded and swapped into the DOM, automatically transforming the static HTML table into an interactive DataTables component with client-side searching, sorting, and pagination.
-   **No additional JavaScript:**
    -   All dynamic behaviors are achieved solely through HTMX and Alpine.js, minimizing the need for custom JavaScript development, adhering to the "no additional JavaScript" guideline.

## Final Notes

This comprehensive Django modernization plan provides a structured approach to transition the minimal ASP.NET "PO_Check_Dashboard" into a full-featured, modern Django application. Key aspects include:

-   **Business Value:** This transition moves from a legacy ASP.NET system to a modern, maintainable Django application, improving performance, developer experience, and scalability. The use of HTMX and Alpine.js results in a highly interactive user experience without the complexity of a full JavaScript frontend framework, leading to faster development and reduced maintenance costs. DataTables ensures efficient handling of tabular data, a core component of dashboards.
-   **Automated Approach:** The plan is designed with automation in mind. Tools could parse the inferred schema, generate the model, form, view, and URL code, and scaffold the templates. The repetitive nature of CRUD operations makes them ideal candidates for templated, automated generation.
-   **Non-Technical Language:** The plan avoids deep technical jargon, focusing on the "what" and "why" from a business perspective rather than the "how" of every line of code. Concepts like "fat models, thin views" are explained by their benefit (clearer code, easier maintenance), and HTMX/Alpine.js are presented as tools for a "snappy user experience."
-   **Actionable Steps:** The breakdown into distinct Django app files (`models.py`, `forms.py`, `views.py`, `urls.py`, templates, `tests.py`) provides clear, modular components for implementation, whether manual or automated.