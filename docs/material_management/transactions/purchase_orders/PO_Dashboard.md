## ASP.NET to Django Conversion Script: PO Dashboard

This document outlines the modernization plan for your ASP.NET PO Dashboard application, transitioning it to a robust and scalable Django solution. We will leverage modern Django 5.0+ patterns, focusing on efficient data handling, dynamic user interfaces with HTMX and Alpine.js, and comprehensive testing to ensure reliability.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   **NEVER include `base.html` template code in your output** - assume it already exists and is extended by component templates.
-   Focus ONLY on component-specific code for the current module.
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

### AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several database tables to retrieve and display Purchase Order (PO) information. Based on the SQL queries and data bindings, we've identified the following key tables and their corresponding fields.

-   **`tblMM_PO_Master`**: This is the central table for Purchase Order details.
    -   `Id` (Primary Key, integer)
    -   `PONo` (string)
    -   `SysDate` (date/datetime, used for "Date")
    -   `SysTime` (time, used for "Time")
    -   `SessionId` (string, refers to the employee who generated the PO)
    -   `Checked` (integer, e.g., 0 for no, 1 for yes)
    -   `CheckedDate` (date/datetime)
    -   `Approve` (integer, e.g., 0 for no, 1 for yes)
    -   `ApproveDate` (date/datetime)
    -   `Authorize` (integer, e.g., 0 for no, 1 for yes) - **Crucial filter: Only `Authorize='0'` POs are shown on the dashboard.**
    -   `AuthorizeDate` (date/datetime)
    -   `SupplierId` (string, refers to the supplier)
    -   `FinYearId` (string, refers to the financial year)
    -   `AmendmentNo` (string)
    -   `PRSPRFlag` (string, '0' or '1', determines redirect target for "View" action)
    -   `CompId` (integer, Company ID, used for filtering data per company)

-   **`tblHR_OfficeStaff`**: Contains employee details.
    -   `EmpId` (Primary Key, string)
    -   `EmployeeName` (string)
    -   `Title` (string, e.g., "Mr.", "Ms.")
    -   `CompId` (integer)

-   **`tblMM_Supplier_master`**: Stores supplier information.
    -   `SupplierId` (Primary Key, string)
    -   `SupplierName` (string)
    -   `CompId` (integer)

-   **`tblFinancial_master`**: Manages financial year details.
    -   `FinYearId` (Primary Key, string)
    -   `FinYear` (string, e.g., "2023-2024")
    -   `CompId` (integer)

*(Note: Temporary tables `tblMM_PR_PO_Temp` and `tblMM_SPR_PO_Temp` are used for transient session data and will not require direct Django models for this dashboard's functionality, as their purpose seems to be cleanup rather than persistent data display.)*

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
The primary functionality of this ASP.NET page is to display a filtered list of Purchase Orders, with search capabilities and a navigation link to detailed views.

-   **Read (Displaying POs):**
    -   The `makegrid` method in the C# code-behind is responsible for fetching PO data.
    -   It queries `tblMM_PO_Master` and joins it with `tblHR_OfficeStaff` (for employee names), `tblMM_Supplier_master` (for supplier names), and `tblFinancial_master` (for financial year display).
    -   Data is filtered by `CompId` (Company ID) and `FinYearId`.
    -   A key filter ensures only Purchase Orders with `Authorize='0'` (not yet authorized) are displayed on the dashboard.
    -   Users can search POs by `PONo` (PO Number) or `Supplier` name.
-   **Autocomplete for Supplier Search:**
    -   The `GetCompletionList2` WebMethod provides real-time suggestions for supplier names as the user types, fetching from `tblMM_Supplier_master`.
-   **Navigation to Detail Pages:**
    -   Clicking "View" for a PO redirects the user to either `PO_PR_View_Print_Details.aspx` or `PO_SPR_View_Print_Details.aspx` based on the `PRSPRFlag` value associated with the PO. This implies separate pages for different types of PO details.
-   **Session Management:**
    -   The application relies on `Session` variables (`compid`, `username`, `finyear`) for context, which will be handled by Django's session framework.
-   **UI State Management:**
    -   The visibility of the "Supplier" and "PO No" search textboxes changes dynamically based on the selected search type dropdown.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in the user interface.

**Instructions:**
The ASP.NET page uses standard Web Forms controls to build the PO dashboard.

-   **Search/Filter Section:**
    -   **`drpfield` (DropDownList):** Allows users to select the search criterion: "Supplier" (Value="0") or "PO No" (Value="1"). `AutoPostBack="True"` indicates a server-side refresh on change.
    -   **`txtSupplier` (TextBox):** Input field for supplier name, enhanced with `AutoCompleteExtender` for search suggestions. Initially visible for "Supplier" search.
    -   **`txtPONo` (TextBox):** Input field for PO number. Initially hidden, becomes visible for "PO No" search.
    -   **`Button1` (Button):** "Search" button to trigger data filtering.
-   **Data Display Grid:**
    -   **`GridView2` (GridView):** The main component for displaying PO data in a tabular format.
        -   It features pagination (`AllowPaging="True"`, `PageSize="20"`).
        -   Displays columns for SN (serial number), PO No, Date, Time, Generated By, Supplier, Supplier Code, Checked Date, Approved Date, and Authorized Date.
        -   Includes a "View" `LinkButton` in each row for navigating to detail pages.
        -   Has an `EmptyDataTemplate` to show a message when no POs are found.

### Step 4: Generate Django Code

We will structure the Django application, named `material_management`, with the following files.

#### 4.1 Models (`material_management/models.py`)

We define Django models that map directly to the existing database tables. The `PoMasterManager` will encapsulate the complex data retrieval and filtering logic from the ASP.NET `makegrid` method, ensuring our models handle the business rules ("fat model" approach).

```python
from django.db import models
from django.utils import timezone
from datetime import date, time

# Custom Manager for PO_Master to encapsulate complex data retrieval logic
class PoMasterManager(models.Manager):
    """
    Manager for PO_Master to handle dashboard-specific data retrieval,
    including joins and filtering logic from the original ASP.NET 'makegrid' function.
    """
    def get_dashboard_data(self, comp_id: int, fin_year_id: str, search_type: str = None, search_value: str = None) -> list[dict]:
        """
        Retrieves and formats PO dashboard data based on company ID, financial year,
        and optional search criteria (supplier or PO number).
        """
        # Base query for PO_Master records, filtering by company, financial year, and authorization status
        # 'Authorize='0'' in ASP.NET means `authorize=0` in Django model.
        queryset = self.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # Replicates `FinYearId<=` from ASP.NET
            authorize=0 # Only non-authorized POs are displayed on the dashboard
        ).order_by('-id') # Order by ID Desc, matching ASP.NET's default order

        # Apply search filters based on search_type and search_value
        if search_type == '1' and search_value: # Search by PO No
            queryset = queryset.filter(po_no=search_value)
        elif search_type == '0' and search_value: # Search by Supplier Name
            # Find the supplier_id corresponding to the provided supplier name
            try:
                supplier_obj = MMSupplierMaster.objects.get(supplier_name=search_value, comp_id=comp_id)
                queryset = queryset.filter(supplier_id=supplier_obj.supplier_id)
            except MMSupplierMaster.DoesNotExist:
                # If supplier not found, no POs will match this criteria
                return [] # Return empty list directly if supplier not found

        # Process queryset to format data as required by the dashboard grid
        results = []
        for po in queryset:
            gen_by_name = ''
            try:
                # Retrieve employee name for 'Gen By' column
                emp = HROfficeStaff.objects.get(emp_id=po.session_id, comp_id=comp_id)
                gen_by_name = f"{emp.title}. {emp.employee_name}"
            except HROfficeStaff.DoesNotExist:
                pass # Employee not found, leave 'Gen By' empty

            supplier_name = ''
            supplier_code = ''
            try:
                # Retrieve supplier name and code
                supplier = MMSupplierMaster.objects.get(supplier_id=po.supplier_id, comp_id=comp_id)
                supplier_name = supplier.supplier_name
                supplier_code = supplier.supplier_id
            except MMSupplierMaster.DoesNotExist:
                pass # Supplier not found, leave supplier details empty

            fin_year_display = ''
            try:
                # Retrieve financial year display name
                fin_year_obj = FinancialMaster.objects.get(fin_year_id=po.fin_year_id, comp_id=comp_id)
                fin_year_display = fin_year_obj.fin_year
            except FinancialMaster.DoesNotExist:
                pass # Financial year not found

            # Append formatted data for each PO to the results list
            results.append({
                'id': po.id,
                'po_no': po.po_no,
                'date': po.sys_date.strftime('%d/%m/%Y') if po.sys_date else '', # Format date D/M/Y
                'time': po.sys_time.strftime('%H:%M:%S') if po.sys_time else '', # Format time H:M:S
                'gen_by': gen_by_name,
                'checked_date': po.checked_date.strftime('%d/%m/%Y') if po.checked == 1 and po.checked_date else '',
                'approved_date': po.approve_date.strftime('%d/%m/%Y') if po.approve == 1 and po.approve_date else '',
                'authorized_date': po.authorize_date.strftime('%d/%m/%Y') if po.authorize == 1 and po.authorize_date else '',
                'supplier': supplier_name,
                'code': supplier_code,
                'fin_year': fin_year_display,
                'amendment_no': po.amendment_no,
                'pr_spr_flag': po.pr_spr_flag # Flag used for redirecting to correct detail page
            })
        return results

class PO_Master(models.Model):
    """
    Django model for the tblMM_PO_Master database table.
    Configured to use an existing table (managed=False) and custom column names.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50) # Corresponds to EmpId in tblHR_OfficeStaff
    checked = models.IntegerField(db_column='Checked')
    checked_date = models.DateField(db_column='CheckedDate', null=True, blank=True)
    approve = models.IntegerField(db_column='Approve')
    approve_date = models.DateField(db_column='ApproveDate', null=True, blank=True)
    authorize = models.IntegerField(db_column='Authorize')
    authorize_date = models.DateField(db_column='AuthorizeDate', null=True, blank=True)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # Corresponds to SupplierId in tblMM_Supplier_master
    fin_year_id = models.CharField(db_column='FinYearId', max_length=50) # Corresponds to FinYearId in tblFinancial_master
    amendment_no = models.CharField(db_column='AmendmentNo', max_length=50)
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1) # '0' for PR, '1' for SPR
    comp_id = models.IntegerField(db_column='CompId')

    # Assign the custom manager
    objects = PoMasterManager()

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_PO_Master' # Explicitly specifies the table name
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

    # Properties to derive related data, supporting the Fat Model principle
    @property
    def full_gen_by_name(self) -> str:
        """Returns the formatted name of the employee who generated the PO."""
        try:
            emp = HROfficeStaff.objects.get(emp_id=self.session_id, comp_id=self.comp_id)
            return f"{emp.title}. {emp.employee_name}"
        except HROfficeStaff.DoesNotExist:
            return ""

    @property
    def supplier_details(self) -> dict:
        """Returns a dictionary with supplier name and code."""
        try:
            supplier = MMSupplierMaster.objects.get(supplier_id=self.supplier_id, comp_id=self.comp_id)
            return {'name': supplier.supplier_name, 'code': supplier.supplier_id}
        except MMSupplierMaster.DoesNotExist:
            return {'name': '', 'code': ''}

    @property
    def financial_year_display(self) -> str:
        """Returns the display name for the financial year."""
        try:
            fin_year_obj = FinancialMaster.objects.get(fin_year_id=self.fin_year_id, comp_id=self.comp_id)
            return fin_year_obj.fin_year
        except FinancialMaster.DoesNotExist:
            return ""

class HROfficeStaff(models.Model):
    """
    Django model for the tblHR_OfficeStaff database table.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"

class MMSupplierMaster(models.Model):
    """
    Django model for the tblMM_Supplier_master database table.
    """
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.supplier_name

class FinancialMaster(models.Model):
    """
    Django model for the tblFinancial_master database table.
    """
    fin_year_id = models.CharField(db_column='FinYearId', primary_key=True, max_length=50)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year Master'
        verbose_name_plural = 'Financial Year Masters'

    def __str__(self):
        return self.fin_year

```

#### 4.2 Forms (`material_management/forms.py`)

We create a non-model form to handle the search inputs, managing the choice between searching by supplier or PO number.

```python
from django import forms

class POSearchForm(forms.Form):
    """
    Form for the PO Dashboard search functionality.
    Handles the search type dropdown and associated text inputs.
    """
    SEARCH_CHOICES = [
        ('0', 'Supplier'), # Corresponds to ASP.NET ListItem Value="0"
        ('1', 'PO No'),    # Corresponds to ASP.NET ListItem Value="1"
    ]
    
    # Dropdown for selecting search field
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3',
            'x-model': 'searchType', # Alpine.js variable to control visibility
            'hx-post': '.', # POST to current URL (po-dashboard/)
            'hx-trigger': 'change', # Trigger POST on change
            'hx-target': '#poDashboardForm', # Replace the entire search form section
            'hx-swap': 'outerHTML', # Swap the entire element
            'id': 'drpfield' # Match original ID for clarity
        }),
        label="" # Label is implicitly handled by the choice text in UI
    )
    
    # Textbox for Supplier search, hidden/shown by Alpine.js
    txt_supplier = forms.CharField(
        max_length=350,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[350px]',
            'placeholder': 'Enter Supplier Name',
            'x-bind:class': "{ 'hidden': searchType !== '0' }", # Alpine.js controls visibility
            'x-ref': 'supplierInput', # Alpine.js reference for autocomplete
            'id': 'txtSupplier' # Match original ID
        }),
        label=""
    )
    
    # Textbox for PO No search, hidden/shown by Alpine.js
    txt_po_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter PO Number',
            'x-bind:class': "{ 'hidden': searchType !== '1' }", # Alpine.js controls visibility
            'x-ref': 'poNoInput', # Alpine.js reference
            'id': 'txtPONo' # Match original ID
        }),
        label=""
    )

    # No specific custom validation needed beyond default field validation,
    # as the complex data filtering logic is in the PO_Master manager.

```

#### 4.3 Views (`material_management/views.py`)

We define three key views:
1.  `PODashboardView`: Renders the main dashboard page, including the search form.
2.  `POTablePartialView`: An HTMX endpoint that renders only the table, handling search queries. This keeps views "thin" by delegating data retrieval to the model's custom manager.
3.  `SupplierAutoCompleteView`: Provides JSON data for the supplier autocomplete feature.
4.  `PODetailRedirectView`: Handles the redirection to specific PO detail pages based on the `PRSPRFlag`.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy, reverse
from django.shortcuts import render, redirect
from django.contrib import messages
from urllib.parse import urlencode # For building URL query parameters
from django.utils.crypto import get_random_string # For generating random key, mirroring ASP.NET's fun.GetRandomAlphaNumeric()

from .models import PO_Master, MMSupplierMaster # Import necessary models
from .forms import POSearchForm # Import the search form

class PODashboardView(TemplateView):
    """
    Main view for the PO Dashboard page.
    Renders the initial page with the search form.
    Handles the POST request from the search dropdown to dynamically update the form.
    """
    template_name = 'material_management/po_dashboard/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form with GET data or defaults
        context['search_form'] = POSearchForm(self.request.GET or None)
        return context

    def post(self, request, *args, **kwargs):
        # This POST request is triggered by HTMX when the search dropdown changes (AutoPostBack behavior).
        # It updates the search form's state (which input is visible) and triggers a table refresh.
        form = POSearchForm(request.POST)
        if request.headers.get('HX-Request'):
            # Render the form partial with updated state for Alpine.js
            response = render(request, 'material_management/po_dashboard/_search_form.html', {'search_form': form})
            # Trigger a refresh of the main PO table after the form update
            response.headers['HX-Trigger'] = 'refreshPOTable'
            return response
        return super().get(request, *args, **kwargs) # Fallback for non-HTMX requests


class POTablePartialView(View):
    """
    HTMX endpoint to render just the PO list table.
    Fetches data using the PO_Master's custom manager and filters based on GET parameters.
    """
    def get(self, request, *args, **kwargs):
        # Retrieve session context variables (CompId, FinYearId, Username)
        # In a real app, ensure these are properly set via authentication/middleware.
        comp_id = request.session.get('compid')
        fin_year_id = request.session.get('finyear')

        if not comp_id or not fin_year_id:
            # Handle cases where critical session data is missing
            return HttpResponse("<p class='text-red-500'>Error: Session data missing. Please ensure you are logged in.</p>", status=401)

        # Extract search parameters from GET request
        search_type = request.GET.get('search_field', '0') # Default to Supplier search ('0')
        search_value = ''
        if search_type == '0':
            search_value = request.GET.get('txt_supplier', '')
        elif search_type == '1':
            search_value = request.GET.get('txt_po_no', '')

        # Use the PO_Master's custom manager to get filtered and formatted data
        po_dashboard_data = PO_Master.objects.get_dashboard_data(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_type=search_type,
            search_value=search_value
        )
        
        # Prepare context for rendering the table partial
        context = {
            'po_list': po_dashboard_data,
        }
        return render(request, 'material_management/po_dashboard/_po_table.html', context)


class SupplierAutoCompleteView(View):
    """
    HTMX-compatible endpoint for supplier autocomplete suggestions.
    Returns a JSON array of formatted supplier names.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '') # 'term' is the standard parameter for jQuery UI Autocomplete
        comp_id = request.session.get('compid')

        if not comp_id:
            return JsonResponse([], safe=False)

        # Filter supplier master based on prefix_text and company ID
        # Using __icontains for case-insensitive partial match
        suppliers = MMSupplierMaster.objects.filter(
            comp_id=comp_id,
            supplier_name__icontains=prefix_text
        ).order_by('supplier_name')[:10] # Limit results for performance, similar to ASP.NET

        # Format results as "SupplierName [SupplierId]" as expected by ASP.NET's AutocompleteExtender
        results = [f"{s.supplier_name} [{s.supplier_id}]" for s in suppliers]
        
        return JsonResponse(results, safe=False)


class PODetailRedirectView(View):
    """
    Handles the 'View' action for a PO.
    Redirects to the appropriate detail page (PR or SPR) based on the PO's PRSPRFlag.
    """
    def get(self, request, pk: int, *args, **kwargs):
        comp_id = request.session.get('compid')
        fin_year_id = request.session.get('finyear')

        try:
            # Retrieve the specific PO_Master record
            po_master = PO_Master.objects.get(id=pk, comp_id=comp_id, fin_year_id__lte=fin_year_id)
        except PO_Master.DoesNotExist:
            messages.error(request, "Purchase Order not found or you are not authorized to view it.")
            return redirect(reverse_lazy('po_dashboard_list')) # Redirect back to dashboard on error

        # Prepare common query parameters for the redirect URL
        query_params = {
            'mid': po_master.id,
            'pono': po_master.po_no,
            'Code': po_master.supplier_id,
            'AmdNo': po_master.amendment_no,
            'Key': get_random_string(length=32), # Generates a random key, mirroring ASP.NET
            'ModId': '6',
            'SubModId': '35',
            'parentpage': 'PO_Dashboard.aspx' # Can be dynamically set to Django URL name if needed
        }

        # Determine the target URL based on PRSPRFlag
        if po_master.pr_spr_flag == '0': # Original ASP.NET redirection logic for PR
            target_url_name = 'po_pr_view_details' # Placeholder for actual PR detail URL
            query_params['Trans'] = '' # 'Trans' parameter present only for PR
        else: # Original ASP.NET redirection logic for SPR
            target_url_name = 'po_spr_view_details' # Placeholder for actual SPR detail URL

        # Build the final redirect URL
        redirect_url = f"{reverse(target_url_name)}?{urlencode(query_params)}"
        return redirect(redirect_url)

```

#### 4.4 Templates (`material_management/templates/material_management/po_dashboard/`)

We'll create the main dashboard template (`list.html`) and two partial templates (`_search_form.html`, `_po_table.html`) for dynamic content updates via HTMX.

**`list.html`**: (Main dashboard page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Order Dashboard</h2>
    </div>

    <!-- The search form section, managed by Alpine.js for input visibility -->
    <div id="poDashboardForm" x-data="{ searchType: '{{ search_form.search_field.value|default:'0' }}' }">
        {% include 'material_management/po_dashboard/_search_form.html' %}
    </div>

    <!-- Container for the PO list table, loaded and refreshed by HTMX -->
    <div id="poTable-container"
         hx-trigger="load, refreshPOTable from:body" {# Loads on initial page load, and on custom event #}
         hx-get="{% url 'po_dashboard_table' %}" {# Fetches table content from this URL #}
         hx-include="#poDashboardForm" {# Includes form fields for search parameters #}
         hx-swap="innerHTML">
        <!-- Initial loading state displayed while HTMX fetches data -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Purchase Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# jQuery and jQuery UI (for autocomplete) are typically included in base.html. #}
{# If not, ensure they are here or via CDN. #}
{# Example CDNs, assume they are in base.html as per instructions:
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
#}

<script>
    // Re-initialize autocomplete when the search form is swapped by HTMX
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'poDashboardForm') {
            initAutocomplete();
        }
    });

    // Initialize autocomplete on initial DOM load and after HTMX settles (for subsequent swaps)
    document.addEventListener('DOMContentLoaded', initAutocomplete);
    document.body.addEventListener('htmx:afterSettle', initAutocomplete);

    function initAutocomplete() {
        // Target the supplier input field using its x-ref attribute
        const supplierInput = document.querySelector('[x-ref="supplierInput"]');
        if (supplierInput && !$(supplierInput).data('ui-autocomplete')) {
            $(supplierInput).autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: "{% url 'po_dashboard_autocomplete_supplier' %}",
                        dataType: "json",
                        data: {
                            term: request.term
                        },
                        success: function(data) {
                            response(data);
                        },
                        error: function(xhr, status, error) {
                            console.error("Autocomplete error:", status, error);
                            response([]); // Return empty array on error
                        }
                    });
                },
                minLength: 1, // Match ASP.NET MinimumPrefixLength
                appendTo: "#poDashboardForm", // Append dropdown to form for better positioning
                open: function() {
                    // Adjust position of autocomplete dropdown if needed, or apply specific styling
                    $(this).autocomplete("widget").addClass("z-50"); // Ensure dropdown is on top
                }
            });
        }
    }
</script>
{% endblock %}
```

**`_search_form.html`**: (Partial for the search form)

```html
<table align="center" cellpadding="0" cellspacing="0" class="w-full">
    <tr>
        <td height="20" align="left" valign="middle" scope="col"
            class="fontcsswhite bg-[url('/static/images/hdbg.JPG')] px-4 py-2 text-white font-bold">
            &nbsp;PO {# Background image path updated assuming static files #}
        </td>
    </tr>
    <tr>
        <td height="25" class="py-2 flex items-center space-x-2">
            &nbsp;
            {{ search_form.search_field }} {# Dropdown for 'Supplier' or 'PO No' #}
            &nbsp;
            {{ search_form.txt_supplier }} {# Supplier search input, x-bind:class handles visibility #}
            {{ search_form.txt_po_no }} {# PO No search input, x-bind:class handles visibility #}
            &nbsp;
            <button type="button"
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                    hx-get="{% url 'po_dashboard_table' %}" {# Triggers a GET request to refresh the table #}
                    hx-target="#poTable-container" {# Targets the table container #}
                    hx-trigger="click" {# On button click #}
                    hx-include="#poDashboardForm" {# Includes all form fields in the request #}
                    hx-swap="innerHTML">
                Search
            </button>
        </td>
    </tr>
</table>
```

**`_po_table.html`**: (Partial for the DataTables grid)

```html
{# Required DataTables CSS and JS (assuming loaded in core/base.html)
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
#}

<table id="poDashboardTable" class="min-w-full bg-white yui-datatable-theme shadow-md rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Checked</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if po_list %}
            {% for po in po_list %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.time }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.gen_by }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.supplier }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.checked_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.approved_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.authorized_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <a href="{% url 'po_dashboard_view_details' po.id %}"
                       class="bg-blue-500 hover:bg-blue-700 text-white text-sm font-bold py-1 px-3 rounded">
                        View
                    </a>
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="11" class="py-4 text-center text-maroon text-lg font-bold text-red-700">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    // Ensure jQuery is loaded before DataTables.
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#poDashboardTable')) {
            $('#poDashboardTable').DataTable().destroy();
        }
        $('#poDashboardTable').DataTable({
            "pageLength": 20, // Match ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "order": [], // Disable initial sorting
            "pagingType": "full_numbers", // Example for full pagination controls
            "language": { // Optional: Customize language strings
                "emptyTable": "No data to display !"
            }
            // Add other DataTables configurations as needed for search, sorting, etc.
        });
    });
</script>

```

#### 4.5 URLs (`material_management/urls.py`)

This file defines the URL patterns that map to our Django views, ensuring clear and consistent routing.

```python
from django.urls import path
from .views import PODashboardView, POTablePartialView, SupplierAutoCompleteView, PODetailRedirectView

urlpatterns = [
    # Main dashboard page
    path('po-dashboard/', PODashboardView.as_view(), name='po_dashboard_list'),
    
    # HTMX endpoint for dynamically loading/refreshing the PO table
    path('po-dashboard/table/', POTablePartialView.as_view(), name='po_dashboard_table'),
    
    # Endpoint for supplier autocomplete suggestions
    path('po-dashboard/autocomplete-supplier/', SupplierAutoCompleteView.as_view(), name='po_dashboard_autocomplete_supplier'),
    
    # Endpoint for handling the 'View' action, redirecting to specific PO detail pages
    path('po-dashboard/view-po/<int:pk>/', PODetailRedirectView.as_view(), name='po_dashboard_view_details'),

    # Placeholder URLs for the actual PO detail pages.
    # In a real application, these would point to views in their respective modules (e.g., 'po_pr_app').
    # These are necessary for the `reverse` lookup in `PODetailRedirectView`.
    path('po-pr-details/', PODetailRedirectView.as_view(), name='po_pr_view_details'), # Example: Actual URL for PR details
    path('po-spr-details/', PODetailRedirectView.as_view(), name='po_spr_view_details'), # Example: Actual URL for SPR details
]

```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests cover model logic, data retrieval, and view interactions, ensuring the migrated application behaves as expected.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import JsonResponse
from datetime import date, time
from unittest.mock import patch
import json

from .models import PO_Master, HROfficeStaff, MMSupplierMaster, FinancialMaster

class ModelTest(TestCase):
    """
    Unit tests for the Django models, focusing on data integrity and custom manager logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up shared test data for all model tests
        cls.comp_id = 1
        cls.fin_year_id = '2023'
        
        # Create related master data first
        FinancialMaster.objects.create(fin_year_id=cls.fin_year_id, fin_year='2023-2024', comp_id=cls.comp_id)
        HROfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr', comp_id=cls.comp_id)
        MMSupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.comp_id)
        MMSupplierMaster.objects.create(supplier_id='SUP002', supplier_name='Supplier B', comp_id=cls.comp_id)

        # Create PO_Master records based on ASP.NET analysis
        # POs with authorize=0 should appear on dashboard
        PO_Master.objects.create(
            id=1, po_no='PO001', sys_date=date(2023, 1, 1), sys_time=time(10, 0, 0),
            session_id='EMP001', checked=0, approve=0, authorize=0, # This PO is NOT authorized
            supplier_id='SUP001', fin_year_id=cls.fin_year_id, amendment_no='0', pr_spr_flag='0',
            comp_id=cls.comp_id
        )
        # POs with authorize=1 should NOT appear on dashboard
        PO_Master.objects.create(
            id=2, po_no='PO002', sys_date=date(2023, 1, 2), sys_time=time(11, 0, 0),
            session_id='EMP001', checked=1, checked_date=date(2023,1,3), approve=1, approve_date=date(2023,1,4), authorize=1, authorize_date=date(2023,1,5), # This PO IS authorized
            supplier_id='SUP002', fin_year_id=cls.fin_year_id, amendment_no='0', pr_spr_flag='1',
            comp_id=cls.comp_id
        )
        PO_Master.objects.create(
            id=3, po_no='PO003', sys_date=date(2023, 1, 3), sys_time=time(12, 0, 0),
            session_id='EMP001', checked=0, approve=0, authorize=0, # This PO is NOT authorized
            supplier_id='SUP001', fin_year_id=cls.fin_year_id, amendment_no='1', pr_spr_flag='0',
            comp_id=cls.comp_id
        )

    def test_po_master_creation(self):
        """Verify basic PO_Master object creation and field values."""
        po = PO_Master.objects.get(id=1)
        self.assertEqual(po.po_no, 'PO001')
        self.assertEqual(po.supplier_id, 'SUP001')
        self.assertEqual(po.comp_id, self.comp_id)
        self.assertEqual(po.sys_date, date(2023, 1, 1))

    def test_po_master_properties(self):
        """Test derived properties (fat model methods) for correct data retrieval."""
        po = PO_Master.objects.get(id=1)
        self.assertEqual(po.full_gen_by_name, 'Mr. John Doe')
        self.assertEqual(po.supplier_details['name'], 'Supplier A')
        self.assertEqual(po.supplier_details['code'], 'SUP001')
        self.assertEqual(po.financial_year_display, '2023-2024')

        # Test dates for checked/approved/authorized PO
        po_checked = PO_Master.objects.get(id=2)
        self.assertEqual(po_checked.checked_date, date(2023,1,3))
        self.assertEqual(po_checked.approve_date, date(2023,1,4))
        self.assertEqual(po_checked.authorize_date, date(2023,1,5))

    def test_po_master_manager_get_dashboard_data(self):
        """Test the custom manager's data retrieval and filtering logic."""
        # Test initial load (no search criteria), should only show non-authorized POs
        data = PO_Master.objects.get_dashboard_data(self.comp_id, self.fin_year_id, None, None)
        self.assertEqual(len(data), 2) # PO001 and PO003 are authorize=0
        self.assertEqual(data[0]['po_no'], 'PO003') # Ordered by ID Desc
        self.assertEqual(data[1]['po_no'], 'PO001')

        # Test PO No search
        data = PO_Master.objects.get_dashboard_data(self.comp_id, self.fin_year_id, '1', 'PO001')
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['po_no'], 'PO001')

        # Test Supplier search for 'Supplier A'
        data = PO_Master.objects.get_dashboard_data(self.comp_id, self.fin_year_id, '0', 'Supplier A')
        self.assertEqual(len(data), 2) # Both PO001 and PO003 are for Supplier A and authorize=0
        self.assertEqual(data[0]['po_no'], 'PO003')
        self.assertEqual(data[1]['po_no'], 'PO001')

        # Test no matching supplier
        data = PO_Master.objects.get_dashboard_data(self.comp_id, self.fin_year_id, '0', 'NonExistent Supplier')
        self.assertEqual(len(data), 0)
        
        # Test search with empty PO No input (should revert to all non-authorized POs)
        data = PO_Master.objects.get_dashboard_data(self.comp_id, self.fin_year_id, '1', '')
        self.assertEqual(len(data), 2)


class PODashboardViewsTest(TestCase):
    """
    Integration tests for the Django views, covering page rendering, HTMX interactions,
    and redirection logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up shared test data for all view tests
        cls.comp_id = 1
        cls.fin_year_id = '2023'
        
        # Create necessary master data
        FinancialMaster.objects.create(fin_year_id=cls.fin_year_id, fin_year='2023-2024', comp_id=cls.comp_id)
        HROfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr', comp_id=cls.comp_id)
        MMSupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.comp_id)
        MMSupplierMaster.objects.create(supplier_id='SUP002', supplier_name='Supplier B', comp_id=cls.comp_id)

        # Create PO_Master records for view testing
        PO_Master.objects.create(
            id=1, po_no='PO001', sys_date=date(2023, 1, 1), sys_time=time(10, 0, 0),
            session_id='EMP001', checked=0, approve=0, authorize=0, # Not authorized
            supplier_id='SUP001', fin_year_id=cls.fin_year_id, amendment_no='0', pr_spr_flag='0',
            comp_id=cls.comp_id
        )
        PO_Master.objects.create(
            id=2, po_no='PO002', sys_date=date(2023, 1, 2), sys_time=time(11, 0, 0),
            session_id='EMP001', checked=1, checked_date=date(2023,1,3), approve=1, approve_date=date(2023,1,4), authorize=1, authorize_date=date(2023,1,5), # Authorized
            supplier_id='SUP002', fin_year_id=cls.fin_year_id, amendment_no='0', pr_spr_flag='1',
            comp_id=cls.comp_id
        )
        PO_Master.objects.create(
            id=3, po_no='PO003', sys_date=date(2023, 1, 3), sys_time=time(12, 0, 0),
            session_id='EMP001', checked=0, approve=0, authorize=0, # Not authorized
            supplier_id='SUP001', fin_year_id=cls.fin_year_id, amendment_no='1', pr_spr_flag='0',
            comp_id=cls.comp_id
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables that the views expect
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session['username'] = 'testuser'
        session.save()

    def test_po_dashboard_list_view_get(self):
        """Test the main dashboard page rendering."""
        response = self.client.get(reverse('po_dashboard_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_dashboard/list.html')
        self.assertTrue('search_form' in response.context) # Check if form is in context
        self.assertContains(response, 'Purchase Order Dashboard')

    def test_po_dashboard_list_view_post_htmx_form_update(self):
        """Test POST request for form updates via HTMX (e.g., dropdown change)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'search_field': '1'} # Simulate selecting 'PO No'
        response = self.client.post(reverse('po_dashboard_list'), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_dashboard/_search_form.html')
        # Check if HX-Trigger header is set to refresh the table
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshPOTable')
        # Verify Alpine.js x-model reflects new choice
        self.assertContains(response, "searchType: '1'")

    def test_po_dashboard_table_partial_view_get_initial(self):
        """Test initial loading of the table partial (no search)."""
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX request
        response = self.client.get(reverse('po_dashboard_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_dashboard/_po_table.html')
        self.assertTrue('po_list' in response.context)
        self.assertEqual(len(response.context['po_list']), 2) # Only PO001 & PO003 are authorize=0
        self.assertContains(response, 'PO001')
        self.assertContains(response, 'PO003')
        self.assertNotContains(response, 'PO002') # PO002 is authorized (authorize=1)

    def test_po_dashboard_table_partial_view_get_search_po_no(self):
        """Test table partial with PO Number search."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('po_dashboard_table'), {'search_field': '1', 'txt_po_no': 'PO001'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_dashboard/_po_table.html')
        self.assertEqual(len(response.context['po_list']), 1)
        self.assertContains(response, 'PO001')
        self.assertNotContains(response, 'PO003')

    def test_po_dashboard_table_partial_view_get_search_supplier(self):
        """Test table partial with Supplier Name search."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('po_dashboard_table'), {'search_field': '0', 'txt_supplier': 'Supplier A'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_dashboard/_po_table.html')
        self.assertEqual(len(response.context['po_list']), 2) # PO001 and PO003 are for Supplier A
        self.assertContains(response, 'PO001')
        self.assertContains(response, 'PO003')
        self.assertContains(response, 'Supplier A')

    def test_supplier_autocomplete_view(self):
        """Test the supplier autocomplete JSON endpoint."""
        response = self.client.get(reverse('po_dashboard_autocomplete_supplier'), {'term': 'sup'})
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, JsonResponse)
        data = json.loads(response.content)
        self.assertIn('Supplier A [SUP001]', data)
        self.assertIn('Supplier B [SUP002]', data)
        self.assertEqual(len(data), 2)

        response = self.client.get(reverse('po_dashboard_autocomplete_supplier'), {'term': 'supplier a'})
        data = json.loads(response.content)
        self.assertIn('Supplier A [SUP001]', data)
        self.assertEqual(len(data), 1)

        response = self.client.get(reverse('po_dashboard_autocomplete_supplier'), {'term': 'xyz'})
        data = json.loads(response.content)
        self.assertEqual(len(data), 0)

    # Use patch to mock the reverse_lazy for placeholder URLs, as they don't have actual views
    @patch('django.urls.reverse', side_effect=lambda url_name: f"/{url_name}/")
    def test_po_detail_redirect_view_pr_flag_0(self, mock_reverse):
        """Test redirection for PR flag 0 (PO_PR_View_Print_Details.aspx equivalent)."""
        response = self.client.get(reverse('po_dashboard_view_details', args=[1])) # PO001 has pr_spr_flag='0'
        self.assertEqual(response.status_code, 302) # Expect redirect
        self.assertTrue(response.url.startswith('/po_pr_view_details/')) # Check target URL
        self.assertIn('mid=1', response.url)
        self.assertIn('pono=PO001', response.url)
        self.assertIn('Code=SUP001', response.url)
        self.assertIn('AmdNo=0', response.url)
        self.assertIn('Trans=', response.url) # 'Trans' parameter should be present for PR

    @patch('django.urls.reverse', side_effect=lambda url_name: f"/{url_name}/")
    def test_po_detail_redirect_view_pr_flag_1(self, mock_reverse):
        """Test redirection for PR flag 1 (PO_SPR_View_Print_Details.aspx equivalent)."""
        response = self.client.get(reverse('po_dashboard_view_details', args=[2])) # PO002 has pr_spr_flag='1'
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.startswith('/po_spr_view_details/')) # Check target URL
        self.assertIn('mid=2', response.url)
        self.assertIn('pono=PO002', response.url)
        self.assertIn('Code=SUP002', response.url)
        self.assertIn('AmdNo=0', response.url)
        self.assertNotIn('Trans=', response.url) # 'Trans' parameter should NOT be present for SPR

    def test_po_detail_redirect_view_not_found(self):
        """Test redirection when a PO is not found or unauthorized."""
        response = self.client.get(reverse('po_dashboard_view_details', args=[999])) # Non-existent ID
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse('po_dashboard_list')) # Should redirect back to dashboard
        messages_list = list(response.wsgi_request._messages) # Check for messages
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "Purchase Order not found or you are not authorized to view it.")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The modernization leverages HTMX and Alpine.js to create a highly dynamic and responsive user experience without full page reloads, mimicking and improving upon the ASP.NET PostBack model.

-   **Dynamic Search Form (`drpfield` behavior):**
    -   The `drpfield` (Django `search_field` dropdown) has `hx-post="."` and `hx-trigger="change"`. When its value changes, HTMX sends a POST request to the current URL (`/po-dashboard/`).
    -   The `PODashboardView` handles this POST, re-renders the `_search_form.html` partial, and returns it.
    -   `hx-target="#poDashboardForm"` and `hx-swap="outerHTML"` ensures the entire search form block is replaced, effectively updating the visibility of `txt_supplier` or `txt_po_no`.
    -   **Alpine.js (`x-data`, `x-model`, `x-bind:class`):** `x-data="{ searchType: ... }"` on `#poDashboardForm` initializes an Alpine.js component. `x-model="searchType"` binds the dropdown's value to the `searchType` variable. `x-bind:class="{ 'hidden': searchType !== '0' }"` on the input fields dynamically toggles their visibility (adds/removes `hidden` class) based on the `searchType` value, ensuring a smooth client-side transition.
-   **Dynamic Table Loading/Refreshing:**
    -   The `div` with `id="poTable-container"` has `hx-trigger="load, refreshPOTable from:body"`.
        -   `load`: Ensures the table content is fetched and displayed immediately when the page loads.
        -   `refreshPOTable from:body`: A custom HTMX event. This event is triggered by the `PODashboardView` after a search form update (`hx-trigger: 'refreshPOTable'` header) and by the "Search" button click, prompting the table to reload its data.
    -   `hx-get="{% url 'po_dashboard_table' %}"`: Specifies the URL to fetch the table content.
    -   `hx-include="#poDashboardForm"`: Ensures that all form fields within `#poDashboardForm` (search type, supplier name, PO number) are included as query parameters in the GET request to `po_dashboard_table`. This allows `POTablePartialView` to apply the correct filters.
    -   `hx-swap="innerHTML"`: Replaces only the inner HTML of `poTable-container`, ensuring a seamless update of the table without affecting other parts of the page.
-   **DataTables Integration:**
    -   The `_po_table.html` partial contains the `<table id="poDashboardTable">`.
    -   A `<script>` block within this partial ensures that `$(document).ready(function() { $('#poDashboardTable').DataTable({...}); });` is executed *every time* the partial is loaded by HTMX. This correctly initializes DataTables on the dynamically loaded content. An `if ($.fn.DataTable.isDataTable('#poDashboardTable'))` check is added to destroy existing instances before re-initializing, preventing errors.
-   **Supplier Autocomplete (`txtSupplier`):**
    -   We use jQuery UI's Autocomplete widget, integrated with HTMX.
    -   The `initAutocomplete()` JavaScript function is called on `DOMContentLoaded` and `htmx:afterSettle` to ensure it's applied correctly after HTMX swaps.
    -   It makes AJAX calls to `{% url 'po_dashboard_autocomplete_supplier' %}` with the `term` parameter, which the `SupplierAutoCompleteView` handles, returning JSON.
    -   The autocomplete provides suggestions in the format "SupplierName [SupplierId]", consistent with the original ASP.NET behavior.

### Final Notes

This comprehensive modernization plan provides a clear roadmap for transforming your ASP.NET PO Dashboard into a modern, maintainable Django application. By adhering to the principles of "Fat Model, Thin View," leveraging HTMX and Alpine.js for dynamic interfaces, implementing DataTables for efficient data presentation, and maintaining strict separation of concerns, the resulting solution will be highly performant and scalable. The included unit and integration tests ensure the migrated functionality is robust and reliable, significantly reducing the risk associated with such a transition.