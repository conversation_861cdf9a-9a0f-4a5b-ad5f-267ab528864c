## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a strategic plan to modernize your existing ASP.NET application, specifically the "PO - Print Supplier" module, into a robust and maintainable Django-based solution. Our approach prioritizes automation, leverages modern web technologies, and ensures a clean architecture for long-term scalability and reduced operational costs.

### Business Value Proposition:

Migrating to Django offers significant advantages:

*   **Cost Efficiency:** Open-source nature eliminates licensing fees, and Python's widespread adoption leads to a larger talent pool and faster development.
*   **Enhanced Performance & Scalability:** Django's optimized ORM and efficient request handling, combined with modern frontend techniques like HTMX, provide a snappier user experience and the ability to scale to meet growing demands.
*   **Improved Maintainability & Development Speed:** Adherence to "Fat Model, Thin View" and clear separation of concerns makes the codebase easier to understand, debug, and extend. Standardized patterns accelerate feature development.
*   **Modern User Experience:** HTMX and Alpine.js provide dynamic, interactive interfaces without the complexity of traditional JavaScript frameworks, offering a seamless user experience similar to single-page applications but with the simplicity of server-rendered HTML.
*   **Reduced Technical Debt:** Moving away from legacy ASP.NET Web Forms eliminates frameworks that are no longer actively developed, reducing security vulnerabilities and compatibility issues.
*   **Automation-Ready:** The structured nature of Django and our prescribed patterns lend themselves perfectly to AI-assisted code generation and automated testing, drastically reducing manual development effort and errors during migration and future enhancements.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   **NEVER include `base.html` template code in your output** - assume it already exists and provides the core layout, CDN links (jQuery, DataTables, HTMX, Alpine.js, Tailwind CSS), and common elements.
*   Focus **ONLY on component-specific code** for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

### AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**

The ASP.NET code primarily interacts with two tables: `tblMM_Supplier_master` and `tblMM_PO_Master`.

*   **`tblMM_Supplier_master`**: This table holds supplier information.
    *   Columns inferred from `fun.select("SupplierId,SupplierName", "tblMM_Supplier_master", ...)` and GridView bindings:
        *   `SupplierId`: Likely a string representing a unique supplier code. (Used as `POCode` in GridView, and for `fun.getCode`)
        *   `SupplierName`: String, the full name of the supplier.
        *   `CompId`: Integer, representing the company ID (from `Session["compid"]`).

*   **`tblMM_PO_Master`**: This table holds Purchase Order master information.
    *   Columns inferred from `fun.select("*", "tblMM_PO_Master", ...)` for counting:
        *   `SupplierId`: String, foreign key to `tblMM_Supplier_master`.
        *   `CompId`: Integer, company ID.
        *   `FinYearId`: String, financial year ID (from `Session["finyear"]`, used with `<=`).
        *   (Implicit Primary Key): A unique identifier for the PO, though not explicitly used or exposed on this page. We'll assume `POId` as an auto-incrementing integer.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core logic in the ASP.NET code.

**Analysis:**

This ASP.NET page is primarily a **Read** operation with a **Search** and a **Navigation** component.

*   **Read (List View):**
    *   The `LoadData` method fetches a list of suppliers and, for each supplier, counts the number of associated Purchase Orders (`tblMM_PO_Master`) for the current company (`CompId`) and up to the current financial year (`FinYearId`).
    *   Only suppliers with one or more POs are displayed.
    *   The data is then bound to `GridView5`, displaying Supplier Name, Supplier Code (mislabelled as `POCode`), and the count of POs.
    *   Pagination is handled by `GridView5_PageIndexChanging`.
*   **Search:**
    *   The `txtSearchSupplier` textbox and `btnSearch` button allow filtering the supplier list.
    *   `btnSearch_Click` uses `fun.getCode(txtSearchSupplier.Text)` to resolve the search input (which could be a full "Name [ID]" string from autocomplete) into a `SupplierId` for filtering.
    *   The `sql` WebMethod provides autocomplete suggestions for `txtSearchSupplier`, returning `SupplierName [SupplierId]` for matching suppliers.
*   **Navigation:**
    *   The "Select" `LinkButton` in the GridView, visible only if `No. Of PO > 0`, triggers `GridView5_RowCommand`.
    *   This command redirects to `PO_Print.aspx` passing the `POCode` (which is the `SupplierId`) as a query parameter.
*   **No direct Create, Update, or Delete** operations for Supplier or PO records are present on this specific page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Search Input:** `txtSearchSupplier` (`asp:TextBox`) with `cc1:AutoCompleteExtender`. This will be a standard HTML `<input type="text">` managed by HTMX for fetching autocomplete suggestions and Alpine.js for displaying the dropdown.
*   **Search Button:** `btnSearch` (`asp:Button`). This will be an HTML `<button>` triggering an HTMX POST request to refresh the data table.
*   **Data Grid:** `GridView5` (`asp:GridView`). This will be an HTML `<table>` transformed into a DataTables instance on the client-side.
    *   **Columns:**
        *   `SN`: Row counter (`{{ forloop.counter }}`).
        *   `Select`: `asp:LinkButton` -> HTML `<button>` or `<a>` to navigate to the PO print detail page. Its visibility depends on `No. Of PO` (POItems).
        *   `Supplier`: `asp:Label` bound to `POSupplier` -> Django model field `supplier_name`.
        *   `Code`: `asp:Label` bound to `POCode` -> Django model field `supplier_id`.
        *   `No. Of PO`: `asp:Label` bound to `POItems` -> Calculated `po_items` annotation in Django model manager.
*   **Styling & Assets:** `yui-datatable.css`, `StyleSheet.css`, `loadingNotifier.js`. These will be replaced by Tailwind CSS for styling and client-side DataTables/HTMX/Alpine.js functionality, with `base.html` handling global CDN links.

---

### Step 4: Generate Django Code

We will create a new Django application, `material_management`, to house this module.

#### 4.1 Models (`material_management/models.py`)

**Task:** Create Django models based on the database schema, including a custom manager for the `SupplierMaster` to encapsulate the complex PO counting logic.

**Instructions:**

*   `SupplierMaster` model: Represents `tblMM_Supplier_master`. `supplier_id` will be the primary key.
*   `POMaster` model: Represents `tblMM_PO_Master`. We'll infer a primary key `po_id`.
*   A custom manager `SupplierMasterManager` for `SupplierMaster` to replicate the `LoadData` logic, fetching suppliers and annotating them with filtered PO counts.
*   **Assumption for Financial Year (`FinYearId`):** The original ASP.NET code uses `FinYearId<=` which implies `FinYearId` is a string that can be numerically compared (e.g., "2023", "2024"). For direct migration, we will keep it as a `CharField` and use Django's `__lte` lookup. In a new design, this would ideally be an integer field or a dedicated `FinancialYear` model.

```python
# material_management/models.py

from django.db import models
from django.db.models import Count, F, OuterRef, Subquery

class SupplierMasterManager(models.Manager):
    """
    Custom manager for SupplierMaster to handle the business logic of
    fetching suppliers and annotating them with the count of purchase orders
    that match specific criteria (company, financial year, and optional search).
    """
    def get_suppliers_with_po_count(self, comp_id, fin_year_id, search_supplier_id=None):
        # Base queryset for suppliers belonging to the current company
        queryset = self.filter(comp_id=comp_id)

        # Apply supplier search filter if a specific supplier ID is provided
        if search_supplier_id:
            queryset = queryset.filter(supplier_id=search_supplier_id)

        # Subquery to count POs for each supplier based on company and financial year.
        # The original ASP.NET code used 'FinYearId<=', which is mimicked here.
        # This assumes 'FinYearId' is a string that can be numerically compared (e.g., "2023").
        po_count_subquery = POMaster.objects.filter(
            supplier=OuterRef('supplier_id'), # Link to the supplier_id of the outer query
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # Mimic original C# 'FinYearId<=' comparison
        ).values('supplier').annotate(po_items=Count('po_id')).values('po_items')

        # Annotate suppliers with their PO count. If no POs are found by the subquery, default to 0.
        queryset = queryset.annotate(
            po_items=Subquery(po_count_subquery, output_field=models.IntegerField(default=0))
        )

        # The original ASP.NET code only adds rows to the DataTable if POs.Count > 0.
        # We replicate this by filtering out suppliers that have 0 POs.
        queryset = queryset.filter(po_items__gt=0)

        # Order by supplier ID descending, as per original 'Order by SupId Desc' for the supplier master.
        return queryset.order_by('-supplier_id')

class SupplierMaster(models.Model):
    """
    Maps to tblMM_Supplier_master table.
    Contains core supplier information like ID, Name, and Company ID.
    """
    supplier_id = models.CharField(max_length=50, db_column='SupplierId', primary_key=True, verbose_name="Supplier Code")
    supplier_name = models.CharField(max_length=255, db_column='SupplierName', verbose_name="Supplier Name")
    comp_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")

    # Assign the custom manager
    objects = SupplierMasterManager()

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblMM_Supplier_master' # Specifies the actual table name in the database
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class POMaster(models.Model):
    """
    Maps to tblMM_PO_Master table.
    Contains master information for Purchase Orders.
    """
    # Assuming POId is the primary key and an auto-incrementing integer.
    # The original ASP.NET code does not explicitly expose a primary key for PO_Master.
    po_id = models.AutoField(db_column='POId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")
    
    # ForeignKey to SupplierMaster. The 'db_column' matches the actual column name.
    # 'to_field' specifies that 'SupplierId' in POMaster links to 'supplier_id' (PK) in SupplierMaster.
    # 'related_name' allows accessing related POs from a SupplierMaster instance.
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id', related_name='purchase_orders', verbose_name="Supplier")
    
    fin_year_id = models.CharField(max_length=10, db_column='FinYearId', verbose_name="Financial Year ID")

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_PO_Master' # Specifies the actual table name
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO {self.po_id} for {self.supplier.supplier_name} ({self.fin_year_id})"

```

#### 4.2 Forms (`material_management/forms.py`)

**Task:** Define a Django form for the supplier search input.

**Instructions:**

*   Create a simple `forms.Form` for the search query.
*   Add a `CharField` with appropriate widgets and HTMX attributes for autocomplete functionality.

```python
# material_management/forms.py

from django import forms

class SupplierSearchForm(forms.Form):
    """
    Form for capturing the supplier search query.
    Includes HTMX attributes for autocomplete functionality.
    """
    search_query = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Type to search supplier...',
            'id': 'txtSearchSupplier', # Matches original ASP.NET ID
            'name': 'search_query',
            # HTMX attributes for fetching autocomplete suggestions
            'hx-get': '/material-management/po-print-supplier/autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup after delay, or on search event
            'hx-target': '#autocomplete-results', # Target div for suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser's native autocomplete
        })
    )

```

#### 4.3 Views (`material_management/views.py`)

**Task:** Implement the necessary views for listing suppliers, handling the table partial updates, and serving autocomplete suggestions.

**Instructions:**

*   `get_user_session_context`: A helper function to simulate fetching company and financial year from session, which is crucial for replicating the original logic. In a real system, this would typically come from the authenticated user's profile or a centralized configuration.
*   `SupplierPOListView`: Renders the initial page with the search form and a container for the data table.
*   `SupplierPOTablePartialView`: An HTMX-targetable view that fetches and renders the data table content based on search criteria. This is where the `LoadData` C# logic is executed via the `SupplierMasterManager`.
*   `SupplierAutocompleteView`: An HTMX-targetable view that provides supplier name and ID suggestions for the autocomplete input.

```python
# material_management/views.py

from django.views.generic import ListView, View
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin # Ensures user is logged in
from .models import SupplierMaster, POMaster
from .forms import SupplierSearchForm

# Helper function to simulate fetching session context (company_id, financial_year_id)
# In a real Django application, this data would typically be associated with the
# authenticated user (e.g., via a UserProfile model) or a global settings object.
def get_user_session_context(request):
    # This is a placeholder. Replace with actual logic to retrieve company and financial year
    # from the authenticated user's profile or session.
    # Example for UserProfile:
    # try:
    #     user_profile = request.user.userprofile
    #     return {
    #         'comp_id': user_profile.company.id, # Assuming ForeignKey to Company model
    #         'fin_year_id': user_profile.current_financial_year,
    #         'username': request.user.username
    #     }
    # except AttributeError: # User might not have a profile, or not logged in
    #     pass
    
    # For migration demonstration, we use static values or mock session data.
    # Assuming 'compid' and 'finyear' are available from a user's session or profile.
    # Hardcoded values for testing purposes if no auth system is fully migrated yet:
    return {
        'comp_id': 1, # Placeholder: Replace with actual logic to get company ID for the user
        'fin_year_id': '2023', # Placeholder: Replace with actual logic to get financial year
        'username': request.user.username if request.user.is_authenticated else 'anonymous'
    }

class SupplierPOListView(LoginRequiredMixin, ListView):
    """
    Main view to display the PO Print Supplier page.
    Renders the base template containing the search form and the container
    for the HTMX-loaded supplier PO table.
    """
    model = SupplierMaster
    template_name = 'material_management/po_print_supplier/list.html'
    context_object_name = 'suppliers_with_po_count' # Though actual data loaded via HTMX
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the search form to the template
        context['search_form'] = SupplierSearchForm()
        return context

    # The actual list data is populated by SupplierPOTablePartialView via HTMX,
    # so get_queryset is not strictly needed for this initial view, but ListView
    # requires it. It will be ignored for the main data display via HTMX.
    def get_queryset(self):
        # Return an empty queryset for the initial page load, as data will be loaded via HTMX
        return SupplierMaster.objects.none()

class SupplierPOTablePartialView(LoginRequiredMixin, View):
    """
    HTMX-targetable view to render only the supplier PO data table.
    Handles search filtering and applies the business logic for counting POs.
    """
    def get(self, request, *args, **kwargs):
        # Get user context for company and financial year
        user_context = get_user_session_context(request)
        comp_id = user_context['comp_id']
        fin_year_id = user_context['fin_year_id']
        
        # Get search query from GET parameters (for initial HTMX load)
        search_query = request.GET.get('search_query', '').strip()
        sup_code = None

        # Replicate ASP.NET's fun.getCode logic for parsing the search query.
        # The autocomplete sends 'SupplierName [SupplierId]', so we extract SupplierId.
        if search_query:
            if '[' in search_query and ']' in search_query:
                # Extract SupplierId from 'SupplierName [SupplierId]' format
                parts = search_query.split('[')
                sup_code = parts[-1].rstrip(']')
            else:
                # If only a name is typed without autocomplete selection,
                # attempt to find a supplier by exact name to get their ID.
                # This mimics a heuristic in fun.getCode if it tried to resolve names.
                try:
                    supplier = SupplierMaster.objects.filter(
                        comp_id=comp_id, 
                        supplier_name__iexact=search_query
                    ).first()
                    if supplier:
                        sup_code = supplier.supplier_id
                except SupplierMaster.DoesNotExist:
                    sup_code = None # No matching supplier found for the name
        
        # Use the custom manager method to fetch suppliers with their PO counts
        suppliers = SupplierMaster.objects.get_suppliers_with_po_count(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_supplier_id=sup_code
        )
        
        context = {
            'suppliers_with_po_count': suppliers,
            # URL for the PO Print detail page, used in the 'Select' button link
            'po_print_detail_base_url': reverse_lazy('material_management:po_print_detail'),
        }
        # Render the partial template containing only the table
        return render(request, 'material_management/po_print_supplier/_supplier_po_table.html', context)

    # If the search button triggers a POST, we handle it similarly to GET for HTMX.
    def post(self, request, *args, **kwargs):
        # For HTMX POST requests, data is in request.POST
        search_query = request.POST.get('search_query', '').strip()
        
        user_context = get_user_session_context(request)
        comp_id = user_context['comp_id']
        fin_year_id = user_context['fin_year_id']

        sup_code = None
        if search_query:
            if '[' in search_query and ']' in search_query:
                parts = search_query.split('[')
                sup_code = parts[-1].rstrip(']')
            else:
                try:
                    supplier = SupplierMaster.objects.filter(
                        comp_id=comp_id, 
                        supplier_name__iexact=search_query
                    ).first()
                    if supplier:
                        sup_code = supplier.supplier_id
                except SupplierMaster.DoesNotExist:
                    sup_code = None

        suppliers = SupplierMaster.objects.get_suppliers_with_po_count(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_supplier_id=sup_code
        )
        
        context = {
            'suppliers_with_po_count': suppliers,
            'po_print_detail_base_url': reverse_lazy('material_management:po_print_detail'),
        }
        return render(request, 'material_management/po_print_supplier/_supplier_po_table.html', context)


class SupplierAutocompleteView(LoginRequiredMixin, View):
    """
    HTMX-targetable view that provides supplier suggestions for the autocomplete.
    Mimics the ASP.NET WebMethod 'sql'.
    Returns HTML list items for HTMX to swap into the autocomplete results div.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('search_query', '')
        user_context = get_user_session_context(request)
        comp_id = user_context['comp_id']

        if not prefix_text:
            return HttpResponse('') # Return empty string if no search query

        # Filter suppliers by name starting with prefix_text and belonging to the company ID
        suppliers = SupplierMaster.objects.filter(
            comp_id=comp_id,
            supplier_name__istartswith=prefix_text
        ).order_by('supplier_name')[:10] # Limit results to 10, as implied by original code

        # Generate HTML list of suggestions
        html = '<ul class="py-1 max-h-60 overflow-y-auto">'
        for s in suppliers:
            display_text = f"{s.supplier_name} [{s.supplier_id}]"
            html += f'<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" data-suggestion="{display_text}" data-supplier-id="{s.supplier_id}">{display_text}</li>'
        html += '</ul>'
        
        return HttpResponse(html)

```

#### 4.4 Templates (`material_management/po_print_supplier/`)

**Task:** Create templates for the list view and the data table partial.

**Instructions:**

*   `list.html`: The main page template that extends `core/base.html`. It includes the search form and a container for the HTMX-loaded table. Alpine.js is used to manage the autocomplete dropdown visibility.
*   `_supplier_po_table.html`: A partial template containing only the `<table>` structure, dynamically loaded by HTMX. It includes DataTables initialization.

```html
<!-- material_management/po_print_supplier/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">PO - Print (Supplier)</h2>
        
        <div class="flex items-center space-x-4 mb-6">
            <label for="txtSearchSupplier" class="font-bold text-gray-700 whitespace-nowrap">Supplier:</label>
            
            <!-- Alpine.js component for autocomplete functionality -->
            <div class="relative flex-grow" x-data="{ 
                open: false, 
                currentSearchQuery: '', 
                selectSuggestion(displayText, supplierId) {
                    this.currentSearchQuery = displayText; // Update x-model bound input
                    document.getElementById('txtSearchSupplier').value = displayText; // Ensure actual input value is updated
                    this.open = false; // Close the dropdown
                },
                init() {
                    // Initialize currentSearchQuery from input's initial value if present
                    this.currentSearchQuery = document.getElementById('txtSearchSupplier').value;

                    // Listen for clicks on the autocomplete results div to select a suggestion
                    this.$refs.autocompleteResultsDiv.addEventListener('click', (e) => {
                        const li = e.target.closest('li');
                        if (li && li.dataset.suggestion && li.dataset.supplierId) {
                            this.selectSuggestion(li.dataset.suggestion, li.dataset.supplierId);
                        }
                    });
                }
            }" @click.outside="open = false" @keyup.escape="open = false">
                
                <!-- Search input field, bound to Alpine.js for reactivity and HTMX for fetching results -->
                <input type="text" 
                       name="search_query"
                       id="txtSearchSupplier"
                       class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       placeholder="Type to search supplier..."
                       hx-get="{% url 'material_management:supplier_autocomplete' %}"
                       hx-trigger="keyup changed delay:500ms"
                       hx-target="#autocomplete-results"
                       hx-swap="innerHTML"
                       autocomplete="off"
                       x-model="currentSearchQuery"
                       @focus="open = true"
                       @input="open = true"> {# Keep dropdown open on input if there's text #}
                
                <!-- Autocomplete results container, content injected by HTMX, visibility managed by Alpine.js -->
                <div id="autocomplete-results"
                     x-ref="autocompleteResultsDiv"
                     class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"
                     x-show="open && currentSearchQuery.length > 0"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95">
                     <!-- Autocomplete results (HTML list items) will be injected here by HTMX -->
                </div>
            </div>
            
            <!-- Search button, triggers an HTMX POST request to refresh the table -->
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox"
                hx-post="{% url 'material_management:po_print_supplier_table' %}"
                hx-include="[name='search_query']" {# Send the current value of the search input #}
                hx-target="#supplierPOTable-container"
                hx-swap="innerHTML"
                hx-indicator="#loading-spinner">
                Search
            </button>
            <span id="loading-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>
        
        <!-- Container for the supplier PO data table, loaded and refreshed by HTMX -->
        <div id="supplierPOTable-container"
             hx-trigger="load, refreshSupplierList from:body" {# Initial load and custom event trigger #}
             hx-get="{% url 'material_management:po_print_supplier_table' %}"
             hx-swap="innerHTML">
            <!-- Loading indicator while HTMX fetches the table content -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading supplier PO data...</p>
            </div>
        </div>
    </div>

    <!-- Modal placeholder (not directly used by this specific page's functionality,
         but included as part of the standard CRUD template structure) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .hidden from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS (assuming jQuery is loaded in base.html) -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.3/js/dataTables.tailwindcss.min.js"></script>

<script>
    // Initialize DataTables when the table content is loaded/reloaded via HTMX.
    // htmx:afterSwap event fires after content is swapped into the DOM.
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'supplierPOTable-container') {
            // Destroy any existing DataTable instance before re-initializing
            // This prevents errors if the table is loaded multiple times.
            if ($.fn.DataTable.isDataTable('#supplierPOTable')) {
                $('#supplierPOTable').DataTable().destroy();
            }
            $('#supplierPOTable').DataTable({
                "pageLength": 20, // Match original ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "dom": 'lfrtip', // Standard DataTables controls: Length, Filter, Table, Info, Paging
                "order": [] // Disable initial sorting if desired, DataTables can sort by first column by default.
            });
        }
    });
</script>
{% endblock %}

```

```html
<!-- material_management/po_print_supplier/_supplier_po_table.html -->
<!-- This partial template contains only the table content, loaded by HTMX -->

<table id="supplierPOTable" class="min-w-full bg-white yui-datatable-theme">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No. Of PO</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for supplier in suppliers_with_po_count %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right align-top">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left align-top">{{ supplier.supplier_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center align-top">{{ supplier.supplier_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right align-top">{{ supplier.po_items }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center align-top">
                {% if supplier.po_items > 0 %}
                    <a href="{{ po_print_detail_base_url }}?Code={{ supplier.supplier_id }}&ModId=6&SubModId=35"
                       class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs">
                        Select
                    </a>
                {% else %}
                    <span class="text-gray-400 text-xs italic">No POs</span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <!-- Empty data template, matching original ASP.NET behavior -->
        <tr>
            <td colspan="5" class="py-4 text-center font-bold text-maroon-700 text-lg">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- DataTables initialization script will be run via htmx:afterSwap event in list.html -->
```

#### 4.5 URLs (`material_management/urls.py`)

**Task:** Define URL patterns for the views within the `material_management` app.

**Instructions:**

*   Define URLs for the main list view, the HTMX-loaded table partial, and the autocomplete endpoint.
*   Include a placeholder URL for the `PO_Print.aspx` redirect target.
*   Use `app_name` for URL namespacing.

```python
# material_management/urls.py

from django.urls import path
from .views import SupplierPOListView, SupplierPOTablePartialView, SupplierAutocompleteView, View # Import View for placeholder

app_name = 'material_management' # Defines the application namespace for URLs

urlpatterns = [
    # URL for the main PO Print Supplier list page
    path('po-print-supplier/', SupplierPOListView.as_view(), name='po_print_supplier_list'),
    
    # URL for the HTMX-loaded partial table content (for search and refresh)
    path('po-print-supplier/table/', SupplierPOTablePartialView.as_view(), name='po_print_supplier_table'),
    
    # URL for the supplier autocomplete suggestions endpoint
    path('po-print-supplier/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
    
    # Placeholder URL for the PO Print detail page (target of the 'Select' link)
    # The original ASP.NET code redirects to PO_Print.aspx?Code=...
    # This URL will handle the redirection with the supplier code.
    # The actual view for this page (PO_Print.aspx equivalent) would be implemented separately.
    path('po-print/<str:code>/', View.as_view(), name='po_print_detail'), # 'View.as_view()' is a generic placeholder
    
    # NOTE: 'ModId' and 'SubModId' from original redirect are typically part of 
    # a navigation or permissions system. In Django, this could be handled by 
    # specific views/permissions logic, or passed as additional query parameters 
    # if required by the target page.
]

```

#### 4.6 Tests (`material_management/tests.py`)

**Task:** Write comprehensive unit tests for the models and integration tests for the views.

**Instructions:**

*   **Model Tests:** Verify the `SupplierMaster` and `POMaster` models, especially the custom manager's `get_suppliers_with_po_count` method to ensure it accurately replicates the ASP.NET `LoadData` logic.
*   **View Tests:** Test the `SupplierPOListView` (initial page load), `SupplierPOTablePartialView` (HTMX table load with and without search), and `SupplierAutocompleteView` (autocomplete suggestions).
*   Mock user session context for `comp_id` and `fin_year_id` to ensure consistent testing.
*   Simulate HTMX requests by adding `HTTP_HX_REQUEST: 'true'` to client requests where applicable.

```python
# material_management/tests.py

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from unittest.mock import patch

from .models import SupplierMaster, POMaster

class SupplierMasterModelTest(TestCase):
    """
    Tests for the SupplierMaster model and its custom manager.
    Focuses on the accuracy of PO counting logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all model tests
        cls.comp_id = 1
        cls.fin_year_id = '2023' # Matches the session finyear used in views

        # Create supplier instances for the current company
        cls.supplier1 = SupplierMaster.objects.create(
            supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.comp_id
        )
        cls.supplier2 = SupplierMaster.objects.create(
            supplier_id='SUP002', supplier_name='Supplier B', comp_id=cls.comp_id
        )
        # Create a supplier for a different company (should not be included in default queries)
        cls.supplier3 = SupplierMaster.objects.create(
            supplier_id='SUP003', supplier_name='Supplier C (Other Co)', comp_id=2
        )
        # Create a supplier with no POs (should be filtered out by get_suppliers_with_po_count)
        cls.supplier4 = SupplierMaster.objects.create(
            supplier_id='SUP004', supplier_name='Supplier D (No POs)', comp_id=cls.comp_id
        )

        # Create Purchase Order instances
        # POs for Supplier A (2 POs within current FY and older FY)
        POMaster.objects.create(
            supplier=cls.supplier1, comp_id=cls.comp_id, fin_year_id='2023'
        )
        POMaster.objects.create(
            supplier=cls.supplier1, comp_id=cls.comp_id, fin_year_id='2022' # This should be counted (<=)
        )

        # POs for Supplier B (1 PO within current FY, 1 PO for future FY)
        POMaster.objects.create(
            supplier=cls.supplier2, comp_id=cls.comp_id, fin_year_id='2023'
        )
        POMaster.objects.create(
            supplier=cls.supplier2, comp_id=cls.comp_id, fin_year_id='2024' # This should NOT be counted (<=)
        )
        
        # PO for Supplier C (different company)
        POMaster.objects.create(
            supplier=cls.supplier3, comp_id=2, fin_year_id='2023'
        )

    def test_supplier_master_creation(self):
        """Verify SupplierMaster object creation and attributes."""
        supplier = SupplierMaster.objects.get(supplier_id='SUP001')
        self.assertEqual(supplier.supplier_name, 'Supplier A')
        self.assertEqual(supplier.comp_id, self.comp_id)
        self.assertEqual(str(supplier), 'Supplier A [SUP001]')

    def test_po_master_creation(self):
        """Verify POMaster object creation and attributes."""
        po = POMaster.objects.filter(supplier=self.supplier1, fin_year_id='2023').first()
        self.assertIsNotNone(po)
        self.assertEqual(po.supplier, self.supplier1)
        self.assertEqual(po.fin_year_id, '2023')
        self.assertEqual(po.comp_id, self.comp_id)
        self.assertIn(f"PO {po.po_id} for Supplier A (2023)", str(po))

    def test_get_suppliers_with_po_count_all(self):
        """
        Test the custom manager method to ensure it correctly counts POs
        and filters based on company, financial year (<=), and presence of POs.
        """
        suppliers = SupplierMaster.objects.get_suppliers_with_po_count(
            self.comp_id, self.fin_year_id
        )
        # Should include Supplier A and Supplier B
        # Should exclude Supplier C (different comp_id) and Supplier D (no POs)
        self.assertEqual(len(suppliers), 2)
        
        # Check Supplier A's PO count
        supplier_a = next(s for s in suppliers if s.supplier_id == 'SUP001')
        self.assertEqual(supplier_a.po_items, 2) # 2023 and 2022 POs <= 2023

        # Check Supplier B's PO count
        supplier_b = next(s for s in suppliers if s.supplier_id == 'SUP002')
        self.assertEqual(supplier_b.po_items, 1) # Only 2023 PO <= 2023

    def test_get_suppliers_with_po_count_with_search(self):
        """Test search functionality in the custom manager method."""
        # Search for a specific supplier by ID (as parsed from autocomplete)
        suppliers = SupplierMaster.objects.get_suppliers_with_po_count(
            self.comp_id, self.fin_year_id, search_supplier_id='SUP001'
        )
        self.assertEqual(len(suppliers), 1)
        self.assertEqual(suppliers[0].supplier_id, 'SUP001')
        self.assertEqual(suppliers[0].po_items, 2)
        
        # Search for a non-existent supplier
        suppliers_no_match = SupplierMaster.objects.get_suppliers_with_po_count(
            self.comp_id, self.fin_year_id, search_supplier_id='NONEXISTENT'
        )
        self.assertEqual(len(suppliers_no_match), 0)

class SupplierPOViewsTest(TestCase):
    """
    Integration tests for the Django views related to PO Print Supplier module.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test user required for LoginRequiredMixin
        cls.user = User.objects.create_user(username='testuser', password='password123')
        
        # Setup common test data for views
        cls.comp_id = 1
        cls.fin_year_id = '2023'
        
        cls.supplier1 = SupplierMaster.objects.create(
            supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.comp_id
        )
        cls.supplier2 = SupplierMaster.objects.create(
            supplier_id='SUP002', supplier_name='Supplier B', comp_id=cls.comp_id
        )
        cls.supplier_no_po = SupplierMaster.objects.create(
            supplier_id='SUP005', supplier_name='Supplier E (No PO)', comp_id=cls.comp_id
        )

        POMaster.objects.create(
            supplier=cls.supplier1, comp_id=cls.comp_id, fin_year_id='2023'
        )
        POMaster.objects.create(
            supplier=cls.supplier1, comp_id=cls.comp_id, fin_year_id='2022'
        )
        POMaster.objects.create(
            supplier=cls.supplier2, comp_id=cls.comp_id, fin_year_id='2023'
        )
        
    def setUp(self):
        # Login the test client before each test
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        
        # Patch the `get_user_session_context` to control session data for tests
        # This ensures consistent `comp_id` and `fin_year_id` values during testing
        self.patcher = patch('material_management.views.get_user_session_context')
        self.mock_get_user_session_context = self.patcher.start()
        self.mock_get_user_session_context.return_value = {
            'comp_id': self.comp_id,
            'fin_year_id': self.fin_year_id,
            'username': 'testuser'
        }
        self.addCleanup(self.patcher.stop) # Ensure the patch is stopped after the test

    def test_supplier_po_list_view_get(self):
        """Test the main list page loads correctly."""
        response = self.client.get(reverse('material_management:po_print_supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_print_supplier/list.html')
        self.assertContains(response, 'PO - Print (Supplier)')
        self.assertContains(response, 'id="supplierPOTable-container"') # Check for HTMX container
        self.assertTrue('search_form' in response.context) # Check if search form is passed

    def test_supplier_po_table_partial_view_get_initial_load(self):
        """Test the HTMX-loaded table partial for initial data display."""
        response = self.client.get(reverse('material_management:po_print_supplier_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_print_supplier/_supplier_po_table.html')
        
        # Check if expected suppliers and their PO counts are present
        self.assertContains(response, 'Supplier A')
        self.assertContains(response, 'SUP001')
        self.assertContains(response, '2') # PO count for SUP001
        
        self.assertContains(response, 'Supplier B')
        self.assertContains(response, 'SUP002')
        self.assertContains(response, '1') # PO count for SUP002
        
        # Supplier E should not be present as it has no POs
        self.assertNotContains(response, 'Supplier E (No PO)')
        self.assertContains(response, '<a href="/material-management/po-print/SUP001/?Code=SUP001&ModId=6&SubModId=35" class="bg-green-500') # Check 'Select' link
        self.assertContains(response, 'No POs') # Check for the 'No POs' text for filtered out suppliers

    def test_supplier_po_table_partial_view_post_search(self):
        """Test search functionality triggered by HTMX POST request."""
        # Simulate searching for 'Supplier A [SUP001]' (from autocomplete selection)
        response = self.client.post(
            reverse('material_management:po_print_supplier_table'), 
            {'search_query': 'Supplier A [SUP001]'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_print_supplier/_supplier_po_table.html')
        self.assertContains(response, 'Supplier A')
        self.assertNotContains(response, 'Supplier B')
        self.assertNotContains(response, 'Supplier E (No PO)') # Should still be filtered out

        # Simulate searching by name (if fun.getCode resolves name to ID)
        response_name_search = self.client.post(
            reverse('material_management:po_print_supplier_table'), 
            {'search_query': 'Supplier B'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_name_search.status_code, 200)
        self.assertContains(response_name_search, 'Supplier B')
        self.assertNotContains(response_name_search, 'Supplier A')
        
        # Simulate searching for a non-existent supplier
        response_no_results = self.client.post(
            reverse('material_management:po_print_supplier_table'), 
            {'search_query': 'XYZ Company'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_no_results.status_code, 200)
        self.assertContains(response_no_results, 'No data to display !')
        self.assertNotContains(response_no_results, 'Supplier A')

    def test_supplier_autocomplete_view_get(self):
        """Test the autocomplete endpoint for supplier suggestions."""
        # Test with a partial query
        response = self.client.get(
            reverse('material_management:supplier_autocomplete'), 
            {'search_query': 'Suppli'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        # Check for HTML content of suggestions
        self.assertContains(response, '<ul class="py-1 max-h-60 overflow-y-auto">')
        self.assertContains(response, 'Supplier A [SUP001]')
        self.assertContains(response, 'Supplier B [SUP002]')
        # Supplier C (other company) and Supplier E (no POs but same company) should not be suggested by default
        self.assertNotContains(response, 'Supplier C') 
        self.assertNotContains(response, 'Supplier E') # Autocomplete is based on name, not PO count filter

        # Test with an exact query
        response_exact = self.client.get(
            reverse('material_management:supplier_autocomplete'), 
            {'search_query': 'Supplier A'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_exact.status_code, 200)
        self.assertContains(response_exact, 'Supplier A [SUP001]')
        self.assertNotContains(response_exact, 'Supplier B')

        # Test with no query (should return empty HTML)
        response_empty_query = self.client.get(
            reverse('material_management:supplier_autocomplete'), 
            {'search_query': ''}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_empty_query.status_code, 200)
        self.assertEqual(response_empty_query.content.decode(), '')

        # Test with a query that yields no matches
        response_no_match = self.client.get(
            reverse('material_management:supplier_autocomplete'), 
            {'search_query': 'NonExistent'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_no_match.status_code, 200)
        self.assertContains(response_no_match, '<ul></ul>') # Should be an empty ul
```

---

### Step 5: HTMX and Alpine.js Integration

**Task:** Ensure all frontend interactions are handled by HTMX and Alpine.js.

**Instructions:**

*   **Initial Table Load & Refresh:** The `list.html` template uses `hx-get` on `#supplierPOTable-container` with `hx-trigger="load, refreshSupplierList from:body"` to fetch `_supplier_po_table.html`. This ensures the table loads immediately and can be refreshed by a custom event.
*   **Search Functionality:** The "Search" button uses `hx-post` to send the `search_query` to `po_print_supplier_table/`, targeting `#supplierPOTable-container` to update the table in place.
*   **Autocomplete:**
    *   The `txtSearchSupplier` input has `hx-get` to `supplier_autocomplete/` with `hx-trigger="keyup changed delay:500ms"`.
    *   `hx-target="#autocomplete-results"` ensures the HTML suggestions are loaded directly into the dedicated div.
    *   Alpine.js manages the `open` state of the autocomplete dropdown (`x-show` on `#autocomplete-results`) and the `currentSearchQuery` model.
    *   Alpine.js also handles updating the input field and closing the dropdown when a suggestion is clicked, using `data-suggestion` and `data-supplier-id` attributes on the `<li>` elements.
*   **DataTables Integration:** The `_supplier_po_table.html` partial contains the `<table>`. A JavaScript event listener `htmx:afterSwap` in `list.html` detects when this table is loaded into `#supplierPOTable-container` and then initializes/re-initializes DataTables on it, ensuring client-side pagination, sorting, and filtering work correctly.
*   **"Select" Link:** A standard `<a>` tag with the appropriate Django URL. Its visibility is conditionally rendered using `{% if supplier.po_items > 0 %}` in the template.

---

### Final Notes

*   **Base Template (`core/base.html`):** Remember that `core/base.html` should exist and contain the necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS.
*   **Session Management:** The `get_user_session_context` helper is a critical point for migration. It currently uses placeholders. In a full migration, you'd replace this with your actual Django authentication and user profile logic to retrieve `comp_id` and `fin_year_id` from the logged-in user's data.
*   **`PO_Print.aspx` Target:** The `po_print_detail` URL is a placeholder. You would need to implement the Django view and template equivalent of `PO_Print.aspx` in your `material_management` app or another relevant app.
*   **Error Handling:** The original C# code had `try-catch` blocks that often swallowed exceptions. The Django solution implicitly handles some errors (e.g., 404 for non-existent objects), but robust error logging and user-friendly error messages should be added for production readiness.
*   **Security:** Ensure proper Django security practices like CSRF protection are enabled (handled automatically by `{% csrf_token %}` in forms). `LoginRequiredMixin` is used for view protection.
*   **Scalability:** This architecture supports high scalability. For very large datasets, server-side processing for DataTables could be considered, but for most applications, client-side DataTables with HTMX partial updates is efficient.