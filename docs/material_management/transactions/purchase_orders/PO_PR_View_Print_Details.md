The ASP.NET application you've provided, `PO_PR_View_Print_Details.aspx`, is designed to display a print preview or report by embedding another ASP.NET page (`PO_PR_Print_Page.aspx`) within an `iframe`. It primarily handles URL query parameters for report identification (like Purchase Order Number, Supplier Code, Material ID, Amendment Number) and provides a "Cancel" button to navigate back to a "parent" page. This specific component does *not* perform direct database Create, Read, Update, or Delete (CRUD) operations on its own.

Our Django modernization plan will reflect this by focusing on:
1.  **Parameter Handling:** Accurately parsing the URL parameters.
2.  **Report Embedding/Linking:** Replacing the ASP.NET `iframe` with a modern Django equivalent that links to a Python-based PDF generation view.
3.  **Navigation:** Replicating the "Cancel" button's redirection logic.
4.  **Modern Stack:** Ensuring all new components use Django 5.0+, HTMX, Alpine.js, and Tailwind CSS.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The core business value of migrating this component to Django is to:
1.  **Centralize Reporting:** Integrate existing report viewing capabilities into the modern Django application, providing a unified user experience.
2.  **Modernize Reporting:** Transition from legacy Crystal Reports (implied by the original `CrystalDecisions.Web` registration) to modern, Python-based PDF generation solutions, reducing reliance on outdated technologies.
3.  **Enhance User Experience:** Ensure seamless navigation and clear presentation of critical purchase order and material request details within the new system.
4.  **Reduce Technical Debt:** Eliminate ASP.NET dependencies, moving closer to a fully modernized, open-source technology stack.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code (`PO_PR_View_Print_Details.aspx` and its code-behind) does not contain any direct database interaction logic (e.g., `SqlDataSource` definitions, explicit `SELECT` queries, or direct table mappings for data manipulation). This page primarily extracts various identifying parameters (`pono`, `Code`, `mid`, `AmdNo`, `parentPage`, `Key`, `Trans`, `Swto`) from the URL's query string and then uses them to construct a URL for an embedded "print" page.

Therefore, no new database schema can be directly inferred or extracted *from this specific file* for a new Django model that performs CRUD operations. The parameters, however, strongly suggest that this page works with existing data entities, likely a `PurchaseOrder` or `MaterialRequest`, which would be managed by other parts of the application. For the purpose of demonstrating how these parameters would relate to data and to provide a robust testing framework, we will *assume* the existence of a `PurchaseOrder` model in our conceptual Django application, as it's implied by parameters like `pono` and `AmdNo`.

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Based on the analysis of `PO_PR_View_Print_Details.aspx.cs`, there are no explicit Create, Read, Update, or Delete (CRUD) operations performed directly by this page.

*   **Create:** Not present.
*   **Read:** This page 'reads' query string parameters from the URL but does not query a database for data display or modification.
*   **Update:** Not present.
*   **Delete:** Not present.

The primary backend functionalities identified are:
1.  **Parameter Parsing:** Extracting various identifying parameters (e.g., `pono`, `Code`, `mid`, `AmdNo`) from the URL's query string.
2.  **URL Construction:** Building a new URL for an embedded "print" report page (originally `PO_PR_Print_Page.aspx`, which will be a separate Django PDF generation view in the modernized solution).
3.  **Redirection:** Handling the "Cancel" button click, which redirects the user back to a `parentPage` along with specific query parameters.

Error handling in the original code is a generic `try-catch` block, which would be replaced by more specific Django error handling and logging.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The user interface for `PO_PR_View_Print_Details.aspx` is straightforward:

*   **Header:** A `table` element displaying "PO - Print" as the page title, styled with a background image.
*   **Button:** An `asp:Button` with `ID="Cancel"`, `Text="Cancel"`, and an `onclick` event handler `Cancel_Click`. This button is responsible for navigating the user away from the current page.
*   **iFrame:** An `iframe` with `id="myiframe"` that dynamically loads another ASP.NET page (`PO_PR_Print_Page.aspx`) using the parsed URL parameters. This `iframe` is the central display area where the actual report or printable document is shown.

**Field Mappings:**
*   The `iframe`'s `src` attribute is dynamically constructed using URL query parameters: `pono`, `Code`, `mid`, `AmdNo`, `Key`. These parameters are passed to the `PO_PR_Print_Page.aspx` (which will be a Django PDF generation view in the new system).
*   The `Cancel` button's redirect URL also uses `parentPage`, `SupCode`, `SwithctTo`, `Trans` from the original URL.

**JavaScript:** The original page included `loadingNotifier.js`. In the Django context, dynamic loading indicators or UI state could be managed efficiently using HTMX's built-in capabilities or Alpine.js.

## Step 4: Generate Django Code

Given that the ASP.NET page primarily acts as a wrapper for a print view and does not directly perform CRUD operations, the Django implementation will focus on routing, parameter handling, and embedding/linking to a separate PDF generation view. We will *not* generate a full set of CRUD views (`CreateView`, `UpdateView`, `DeleteView`) as they are not applicable to the direct functionality of this specific ASP.NET file. Instead, we will generate a `TemplateView` to handle the display and navigation, and a placeholder for the print view.

### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
As established, this specific ASP.NET page does not define a new model for CRUD. However, the query parameters (`pono`, `mid`, `AmdNo`, `Code`) clearly point to a `PurchaseOrder` entity. To provide a complete and testable example, we will define a conceptual `PurchaseOrder` model. This model represents the type of data that the "print page" would ultimately display and retrieve from the database.

**File:** `po_pr_transactions/models.py`

```python
from django.db import models

class PurchaseOrder(models.Model):
    """
    Conceptual model representing a Purchase Order in the legacy database.
    This model is inferred from the query parameters (pono, AmdNo, mid)
    and serves as a placeholder for data that would be used by the print report.
    It assumes mapping to an existing database table for migration purposes.
    """
    # Maps to 'pono' query string parameter
    po_no = models.CharField(max_length=50, unique=True, db_column='PO_NO', verbose_name='Purchase Order Number')
    # Maps to 'Code' (Supplier Code) query string parameter
    supplier_code = models.CharField(max_length=50, db_column='SUPPLIER_CODE', verbose_name='Supplier Code')
    # Maps to 'mid' (Material ID) query string parameter
    material_id = models.CharField(max_length=50, db_column='MATERIAL_ID', verbose_name='Material ID')
    # Maps to 'AmdNo' (Amendment Number) query string parameter
    amendment_no = models.CharField(max_length=50, db_column='AMD_NO', blank=True, null=True, verbose_name='Amendment Number')
    
    # Additional fields that would typically exist in a Purchase Order header table
    po_date = models.DateField(db_column='PO_DATE', verbose_name='PO Date', null=True, blank=True)
    total_amount = models.DecimalField(max_digits=18, decimal_places=2, db_column='TOTAL_AMOUNT', null=True, blank=True)
    status = models.CharField(max_length=20, db_column='STATUS', default='Draft', verbose_name='Status')

    class Meta:
        # Crucial for mapping to existing legacy database tables
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tbl_PO_Header' # Placeholder: **Replace with your actual legacy Purchase Order header table name**
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        """Returns a user-friendly string representation of the Purchase Order."""
        return f"PO No: {self.po_no} (Amd: {self.amendment_no or 'N/A'})"
        
    def get_full_report_data(self):
        """
        Business logic to gather comprehensive data for a detailed print report.
        This method would typically query related models (e.g., PO line items,
        supplier details, approval history) to compile all information needed
        for a complex report. This exemplifies the 'fat model' principle.
        """
        report_data = {
            'po_number': self.po_no,
            'supplier_info': {
                'code': self.supplier_code,
                'name': 'Example Supplier Co.', # Placeholder: Fetch from Supplier model
                'address': '123 Main St, Anytown',
            },
            'order_date': self.po_date.strftime('%Y-%m-%d') if self.po_date else 'N/A',
            'amendment_number': self.amendment_no if self.amendment_no else 'None',
            'total_amount': f"{self.total_amount:.2f}" if self.total_amount is not None else 'N/A',
            'status': self.status,
            'line_items': [ # Placeholder for line items associated with this PO
                {'item': 'Item A', 'qty': 10, 'unit': 'Pcs', 'price': 50.00, 'total': 500.00},
                {'item': 'Item B', 'qty': 5, 'unit': 'Packs', 'price': 120.00, 'total': 600.00},
            ],
            'notes': 'All terms and conditions apply as per standard contract.',
            'generated_on': models.DateTimeField(auto_now_add=True).to_python(datetime.datetime.now()),
        }
        return report_data
```

### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
No specific forms are needed for the `PO_PR_View_Print_Details` page itself, as its role is purely to display an embedded report and provide navigation. It's not an interface for data entry or modification. Therefore, we do not generate a Django form for this particular component. Forms would typically be created for data management of the `PurchaseOrder` model (e.g., `PurchaseOrderForm` for `CreateView`/`UpdateView`), but that functionality was not present in the analyzed ASP.NET file.

### 4.3 Views

**Task:** Implement the display and navigation logic using Django Class-Based Views.

**Instructions:**
We will use a `TemplateView` to handle the parsing of query parameters, constructing the URL for the print content, and rendering the main page. A separate `View` class will handle the redirection for the "Cancel" action. We'll also define a placeholder `View` for the actual "print page" (e.g., PDF generation).

**File:** `po_pr_transactions/views.py`

```python
import datetime
import logging

from django.views.generic import TemplateView, View
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.shortcuts import render
from django.contrib import messages
from django.db.models import ObjectDoesNotExist # Import for error handling
from .models import PurchaseOrder # Import the conceptual model

logger = logging.getLogger(__name__)

class POPRViewPrintDetailsView(TemplateView):
    """
    Mimics the ASP.NET PO_PR_View_Print_Details.aspx.cs functionality.
    It parses URL parameters and constructs a URL for the embedded print report.
    This view itself does not perform database CRUD operations, but prepares
    context for a view that will display the report.
    """
    template_name = 'po_pr_transactions/po_pr_view_print_details.html'

    def get_context_data(self, **kwargs):
        """
        Retrieves query parameters from the URL and constructs the URL
        for the embedded print report.
        """
        context = super().get_context_data(**kwargs)
        
        # Extract query parameters mirroring ASP.NET Request.QueryString
        po_no = self.request.GET.get('pono', '')
        sup_code = self.request.GET.get('Code', '')
        parent_page = self.request.GET.get('parentPage', '')
        material_id = self.request.GET.get('mid', '')
        amd_no = self.request.GET.get('AmdNo', '')
        key = self.request.GET.get('Key', '')
        trans = self.request.GET.get('Trans', '')
        switch_to = self.request.GET.get('Swto', '') # Note: 'Swto' from original, 'SwitchTo' in Cancel_Click

        # Construct the URL for the actual print page (e.g., PDF generation view).
        # This mirrors the ASP.NET 'myiframe.Attributes.Add("src", ...)' logic.
        # 'po_pr_transactions:po_pr_print_page' would be a view generating a PDF or report.
        print_page_url = reverse('po_pr_transactions:po_pr_print_page')
        print_page_url += f"?mid={material_id}&ModId=6&SubModId=35&pono={po_no}&Code={sup_code}&AmdNo={amd_no}&Key={key}"

        context.update({
            'po_no': po_no,
            'sup_code': sup_code,
            'parent_page': parent_page,
            'material_id': material_id,
            'amd_no': amd_no,
            'key': key,
            'trans': trans,
            'switch_to': switch_to,
            'print_page_url': print_page_url,
        })
        
        logger.info(f"PO_PR_View_Print_Details: Page loaded for PO:{po_no}, Sup:{sup_code}, Parent:{parent_page}")
        return context

    # Views should remain thin (5-15 lines per method).
    # Complex data fetching or business logic related to the print report itself
    # would reside in the `PurchaseOrder` model (e.g., `get_full_report_data`)
    # or a dedicated reporting service/utility.

class POPRCancelView(View):
    """
    Handles the "Cancel" button click, redirecting to the parent page.
    This mirrors the ASP.NET Cancel_Click event functionality.
    """
    def get(self, request, *args, **kwargs):
        """
        Processes the GET request from the cancel button and redirects.
        """
        # Retrieve parameters from the current request query string for redirection
        sup_code = request.GET.get('Code', '')
        # Note: 'SwitchTo' here matches the ASP.NET Redirect; 'Swto' was for iframe src
        switch_to = request.GET.get('SwitchTo', '') 
        trans = request.GET.get('Trans', '')
        parent_page = request.GET.get('parentPage', '/') # Default to application root if parentPage is not provided

        # Construct the redirect URL.
        # In a fully modernized Django app, `parent_page` would ideally map to a named URL
        # using `reverse()`, ensuring robust navigation. Here, we're mimicking the
        # ASP.NET behavior of using a direct path from the query string.
        redirect_url = f"{parent_page}?Code={sup_code}&SwitchTo={switch_to}&Trans={trans}&ModId=6&SubModId=35"
        
        messages.info(request, "Navigation cancelled. Returning to previous page.")
        logger.info(f"PO_PR_View_Print_Details: Redirecting to {redirect_url}")
        return HttpResponseRedirect(redirect_url)

class POPRPrintPageView(View):
    """
    Placeholder for the actual print/report generation page.
    In a real application, this view would use a library like ReportLab, WeasyPrint,
    or xhtml2pdf to generate a PDF based on the query parameters (pono, mid, AmdNo, etc.)
    and return it as an HttpResponse with content_type 'application/pdf'.
    For this example, it renders a simple HTML page that simulates report content.
    """
    def get(self, request, *args, **kwargs):
        """
        Generates the content for the embedded print report.
        Fetches data using the fat model approach.
        """
        po_no = request.GET.get('pono', 'N/A')
        amd_no = request.GET.get('AmdNo', None)

        po_data = None
        try:
            # Attempt to fetch data using the conceptual PurchaseOrder model
            # In a real scenario, you'd find by primary key or unique identifier
            po_instance = PurchaseOrder.objects.get(po_no=po_no, amendment_no=amd_no)
            po_data = po_instance.get_full_report_data() # Call fat model method for report data
        except ObjectDoesNotExist:
            logger.warning(f"PO_PR_Print_Page: Purchase Order {po_no} (Amd: {amd_no or 'N/A'}) not found.")
            messages.error(request, f"Purchase Order {po_no} not found or invalid details provided.")
            # Provide default/empty data or redirect to an error page
            po_data = {
                'po_number': po_no,
                'amendment_number': amd_no or 'N/A',
                'status': 'Not Found',
                'line_items': [],
                'notes': 'Could not retrieve detailed information for this Purchase Order.',
                'generated_on': datetime.datetime.now(),
            }
        except Exception as e:
            logger.error(f"PO_PR_Print_Page: Error fetching PO {po_no} (Amd: {amd_no or 'N/A'}): {e}")
            messages.error(request, f"An error occurred while loading PO details: {e}")
            po_data = {
                'po_number': po_no,
                'amendment_number': amd_no or 'N/A',
                'status': 'Error',
                'line_items': [],
                'notes': 'An unexpected error occurred.',
                'generated_on': datetime.datetime.now(),
            }

        # In a production scenario, you would render a template to PDF:
        # from django.template.loader import render_to_string
        # from weasyprint import HTML
        # html_string = render_to_string('po_pr_transactions/po_print_report.html', {'po_data': po_data})
        # pdf_file = HTML(string=html_string).write_pdf()
        # response = HttpResponse(pdf_file, content_type='application/pdf')
        # response['Content-Disposition'] = f'inline; filename="PO_{po_no}_Amd{amd_no}.pdf"'
        # return response

        # For this example, we render an HTML template directly
        return render(request, 'po_pr_transactions/po_print_page.html', {
            'po_data': po_data,
        })
```

### 4.4 Templates

**Task:** Create templates for the display and print views.

**Instructions:**
We'll create one main template for the `POPRViewPrintDetailsView` and a separate HTML template that represents the content that would be generated by `POPRPrintPageView` (simulating the `iframe` content). DataTables are not applicable here as this is a single record display/print view, not a tabular list of records. HTMX and Alpine.js are included for overall UI consistency, although direct HTMX interactions are minimal due to the redirection and `iframe` embedding.

**File:** `po_pr_transactions/templates/po_pr_transactions/po_pr_view_print_details.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h2 class="text-2xl font-bold text-gray-800">PO - Print Preview</h2>
            {# The 'Cancel' button links directly to the cancel view, mimicking ASP.NET Response.Redirect #}
            <a href="{% url 'po_pr_transactions:po_pr_cancel_view' %}?parentPage={{ parent_page|urlencode }}&Code={{ sup_code|urlencode }}&SwitchTo={{ switch_to|urlencode }}&Trans={{ trans|urlencode }}&ModId=6&SubModId=35"
               class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out">
                Cancel
            </a>
        </div>

        <div class="mt-4" style="min-height: 500px;">
            <p class="text-gray-600 mb-2">Loading print preview for Purchase Order: <span class="font-semibold">{{ po_no|default:'N/A' }}</span> (Amendment: <span class="font-semibold">{{ amd_no|default:'N/A' }}</span>)</p>
            
            {# The iframe embeds the content generated by the 'po_pr_print_page' view #}
            <iframe 
                src="{{ print_page_url }}" 
                width="100%" 
                height="470" 
                frameborder="0" 
                class="border border-gray-300 rounded-md shadow-inner bg-gray-50">
                Your browser does not support iframes. Please click <a href="{{ print_page_url }}" target="_blank" class="text-blue-600 hover:underline">here</a> to view the print page directly.
            </iframe>
        </div>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components specific to this page can be initialized here.
        // For instance, managing a custom loading spinner for the iframe,
        // although browsers typically handle iframe loading indicators natively.
    });
</script>
{% endblock extra_js %}
```

**File:** `po_pr_transactions/templates/po_pr_transactions/po_print_page.html` (Simulates content of the `iframe`)

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Order Printout - Details</title>
    <!-- Tailwind CSS (for quick styling within this simulated print page) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: sans-serif; margin: 20px; }
        @media print {
            body { margin: 0; }
        }
    </style>
</head>
<body class="bg-gray-50 p-6">
    <div class="bg-white rounded-lg shadow-md p-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-6 border-b pb-4">Purchase Order Printout</h1>
        <p class="text-gray-700 mb-4">This is a simulated print page for Purchase Order details. In a real application, this content would be dynamically generated from your database and formatted as a PDF report using tools like WeasyPrint or ReportLab.</p>
        
        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
                <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left border-b border-gray-200">Detail</th>
                    <th class="py-3 px-6 text-left border-b border-gray-200">Value</th>
                </tr>
            </thead>
            <tbody class="text-gray-700 text-sm font-light">
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left whitespace-nowrap font-medium">PO Number:</td>
                    <td class="py-3 px-6 text-left">{{ po_data.po_number }}</td>
                </tr>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left whitespace-nowrap font-medium">Amendment No:</td>
                    <td class="py-3 px-6 text-left">{{ po_data.amendment_number }}</td>
                </tr>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left whitespace-nowrap font-medium">Order Date:</td>
                    <td class="py-3 px-6 text-left">{{ po_data.order_date }}</td>
                </tr>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left whitespace-nowrap font-medium">Supplier:</td>
                    <td class="py-3 px-6 text-left">{{ po_data.supplier_info.name }} ({{ po_data.supplier_info.code }})</td>
                </tr>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left whitespace-nowrap font-medium">Total Amount:</td>
                    <td class="py-3 px-6 text-left">{{ po_data.total_amount }}</td>
                </tr>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left whitespace-nowrap font-medium">Status:</td>
                    <td class="py-3 px-6 text-left">{{ po_data.status }}</td>
                </tr>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left whitespace-nowrap font-medium">Generated On:</td>
                    <td class="py-3 px-6 text-left">{{ po_data.generated_on|date:"Y-m-d H:i:s" }}</td>
                </tr>
            </tbody>
        </table>

        <h3 class="text-xl font-bold text-gray-800 mt-8 mb-4 border-b pb-2">Line Items</h3>
        {% if po_data.line_items %}
        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
                <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left border-b border-gray-200">Item</th>
                    <th class="py-3 px-6 text-left border-b border-gray-200">Qty</th>
                    <th class="py-3 px-6 text-left border-b border-gray-200">Unit</th>
                    <th class="py-3 px-6 text-left border-b border-gray-200">Price</th>
                    <th class="py-3 px-6 text-left border-b border-gray-200">Total</th>
                </tr>
            </thead>
            <tbody class="text-gray-700 text-sm font-light">
                {% for item in po_data.line_items %}
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left">{{ item.item }}</td>
                    <td class="py-3 px-6 text-left">{{ item.qty }}</td>
                    <td class="py-3 px-6 text-left">{{ item.unit }}</td>
                    <td class="py-3 px-6 text-left">{{ item.price|floatformat:2 }}</td>
                    <td class="py-3 px-6 text-left">{{ item.total|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p class="text-gray-600">No line items available for this Purchase Order.</p>
        {% endif %}

        <h3 class="text-xl font-bold text-gray-800 mt-8 mb-4 border-b pb-2">Notes</h3>
        <p class="text-gray-600">{{ po_data.notes }}</p>
        
        <div class="mt-8 flex justify-end">
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m0 0l4 4m4-4l4 4m-4-4v-9a2 2 0 00-2-2H9a2 2 0 00-2 2v9m-4 7h14a2 2 0 002-2v-3a2 2 0 00-2-2H3a2 2 0 00-2 2v3a2 2 0 002 2z"></path></svg>
                Print this page
            </button>
        </div>
    </div>
</body>
</html>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
We'll define URLs for the main view, the cancel action, and the simulated print page within the `po_pr_transactions` app namespace.

**File:** `po_pr_transactions/urls.py`

```python
from django.urls import path
from .views import POPRViewPrintDetailsView, POPRCancelView, POPRPrintPageView

app_name = 'po_pr_transactions' # Namespace for this Django app

urlpatterns = [
    # Main view for displaying the PO/PR print details page with embedded report
    path('po_pr_view_print_details/', POPRViewPrintDetailsView.as_view(), name='po_pr_view_print_details'),
    
    # URL for the "Cancel" action, which performs a redirect
    path('po_pr_cancel_view/', POPRCancelView.as_view(), name='po_pr_cancel_view'),

    # Placeholder URL for the actual print page (e.g., a PDF generation view)
    path('po_pr_print_page/', POPRPrintPageView.as_view(), name='po_pr_print_page'),
]
```

**Note for project `urls.py` (e.g., `myproject/urls.py`):**
You would need to include these URLs in your main Django project's `urls.py` to make them accessible:

```python
# myproject/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    # Include the URLs from our new app under a suitable path prefix
    path('material_management/', include('po_pr_transactions.urls')), 
    # ... other paths for your Django project
]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
We will include comprehensive unit tests for the conceptual `PurchaseOrder` model's properties and methods. For views, we'll write integration tests to verify correct URL parameter parsing, context population, URL generation for the embedded print page, and the redirection logic of the "Cancel" button.

**File:** `po_pr_transactions/tests.py`

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.contrib.messages import get_messages # To check messages framework
import datetime

from .models import PurchaseOrder # Import the conceptual model

class PurchaseOrderModelTest(TestCase):
    """
    Unit tests for the conceptual PurchaseOrder model.
    These tests ensure the model correctly represents inferred data
    and its fat model methods behave as expected.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.po_instance_1 = PurchaseOrder.objects.create(
            po_no='PO-2023-001',
            supplier_code='SUP-001',
            material_id='MAT-001',
            amendment_no='0',
            po_date=datetime.date(2023, 1, 15),
            total_amount=1250.75,
            status='Approved'
        )
        cls.po_instance_2 = PurchaseOrder.objects.create(
            po_no='PO-2023-002',
            supplier_code='SUP-002',
            material_id='MAT-002',
            amendment_no=None, # Test case for null/blank amendment number
            po_date=datetime.date(2023, 2, 20),
            total_amount=300.00,
            status='Pending'
        )
  
    def test_purchase_order_creation(self):
        """Verify PurchaseOrder instance creation and field values."""
        self.assertEqual(self.po_instance_1.po_no, 'PO-2023-001')
        self.assertEqual(self.po_instance_1.supplier_code, 'SUP-001')
        self.assertEqual(self.po_instance_1.material_id, 'MAT-001')
        self.assertEqual(self.po_instance_1.amendment_no, '0')
        self.assertEqual(self.po_instance_1.po_date, datetime.date(2023, 1, 15))
        self.assertEqual(self.po_instance_1.total_amount, 1250.75)
        self.assertEqual(self.po_instance_1.status, 'Approved')

        self.assertIsNone(self.po_instance_2.amendment_no)
        self.assertEqual(self.po_instance_2.total_amount, 300.00)

    def test_purchase_order_str_representation(self):
        """Test the __str__ method for proper string representation."""
        self.assertEqual(str(self.po_instance_1), 'PO No: PO-2023-001 (Amd: 0)')
        self.assertEqual(str(self.po_instance_2), 'PO No: PO-2023-002 (Amd: N/A)') # Check handling of None for AmdNo

    def test_get_full_report_data_method(self):
        """Test the fat model method for generating report data."""
        report_data = self.po_instance_1.get_full_report_data()
        self.assertIsInstance(report_data, dict)
        self.assertEqual(report_data['po_number'], 'PO-2023-001')
        self.assertEqual(report_data['supplier_info']['code'], 'SUP-001')
        self.assertEqual(report_data['amendment_number'], '0')
        self.assertEqual(report_data['total_amount'], '1250.75')
        self.assertIsInstance(report_data['line_items'], list)
        self.assertTrue(len(report_data['line_items']) > 0)
        self.assertIn('generated_on', report_data)

    def test_meta_options(self):
        """Verify Django Meta options like db_table and managed."""
        self.assertEqual(PurchaseOrder._meta.db_table, 'tbl_PO_Header')
        self.assertFalse(PurchaseOrder._meta.managed) # Crucial for legacy database integration
        self.assertEqual(PurchaseOrder._meta.verbose_name, 'Purchase Order')
        self.assertEqual(PurchaseOrder._meta.verbose_name_plural, 'Purchase Orders')

class POPRViewsTest(TestCase):
    """
    Integration tests for PO_PR_View_Print_Details and related views.
    These tests ensure correct URL parsing, context preparation,
    and redirection behavior.
    """
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
        self.test_params = {
            'pono': 'PO-TEST-001',
            'Code': 'SUP-ABC',
            'parentPage': '/material_management/dashboard/',
            'mid': 'MAT-XYZ',
            'AmdNo': '1',
            'Key': 'PRINT',
            'Trans': 'PO',
            'Swto': 'Detail', # This is 'Swto' from ASP.NET QueryString for iframe src
        }
        # Create a dummy PurchaseOrder for the print view to find
        PurchaseOrder.objects.create(
            po_no=self.test_params['pono'],
            supplier_code=self.test_params['Code'],
            material_id=self.test_params['mid'],
            amendment_no=self.test_params['AmdNo'],
            po_date=datetime.date(2023, 10, 26),
            total_amount=999.99,
            status='Completed'
        )
    
    def test_view_print_details_get(self):
        """
        Tests the POPRViewPrintDetailsView for correct rendering and context.
        Verifies that query parameters are correctly parsed and passed to the template.
        """
        url = reverse('po_pr_transactions:po_pr_view_print_details')
        response = self.client.get(url, self.test_params)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_pr_transactions/po_pr_view_print_details.html')

        # Check if context variables are correctly populated from query params
        context = response.context
        self.assertEqual(context['po_no'], self.test_params['pono'])
        self.assertEqual(context['sup_code'], self.test_params['Code'])
        self.assertEqual(context['parent_page'], self.test_params['parentPage'])
        self.assertEqual(context['material_id'], self.test_params['mid'])
        self.assertEqual(context['amd_no'], self.test_params['AmdNo'])
        self.assertEqual(context['key'], self.test_params['Key'])
        self.assertEqual(context['trans'], self.test_params['Trans'])
        self.assertEqual(context['switch_to'], self.test_params['Swto'])

        # Check if print_page_url is correctly constructed as expected
        expected_print_url = reverse('po_pr_transactions:po_pr_print_page')
        expected_print_url += f"?mid={self.test_params['mid']}&ModId=6&SubModId=35&pono={self.test_params['pono']}&Code={self.test_params['Code']}&AmdNo={self.test_params['AmdNo']}&Key={self.test_params['Key']}"
        self.assertEqual(context['print_page_url'], expected_print_url)

        # Verify that the iframe's src in the rendered HTML matches the constructed URL
        self.assertContains(response, f'iframe src="{expected_print_url}"')

    def test_view_print_details_get_missing_params(self):
        """
        Tests the view with missing query parameters to ensure graceful handling
        (defaults to empty strings for missing parameters).
        """
        url = reverse('po_pr_transactions:po_pr_view_print_details')
        response = self.client.get(url) # No query parameters provided

        self.assertEqual(response.status_code, 200)
        context = response.context
        self.assertEqual(context['po_no'], '') # Should be empty string if missing
        self.assertEqual(context['parent_page'], '') # Should be empty string if missing

    def test_cancel_view_redirect(self):
        """
        Tests the POPRCancelView for correct redirection logic and message.
        Ensures the redirect URL matches the ASP.NET behavior.
        """
        url = reverse('po_pr_transactions:po_pr_cancel_view')
        # Use 'SwitchTo' for redirection, mirroring original ASP.NET Cancel_Click
        params_for_redirect = {
            'Code': self.test_params['Code'],
            'SwitchTo': 'List', # Different from 'Swto' to match original redirect
            'Trans': self.test_params['Trans'],
            'parentPage': self.test_params['parentPage']
        }
        response = self.client.get(url, params_for_redirect)

        # Check for correct redirect status code (302 Found)
        self.assertEqual(response.status_code, 302) 
        
        # Construct the expected redirect URL precisely as in ASP.NET's Cancel_Click
        expected_redirect_url = (
            f"{self.test_params['parentPage']}?Code={self.test_params['Code']}"
            f"&SwitchTo=List&Trans={self.test_params['Trans']}&ModId=6&SubModId=35"
        )
        self.assertEqual(response.url, expected_redirect_url)

        # Check for the presence of a success message after redirection
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Navigation cancelled. Returning to previous page.")

    def test_cancel_view_redirect_default_parent_page(self):
        """
        Tests the cancel view when no 'parentPage' is provided,
        ensuring it defaults to the application root.
        """
        url = reverse('po_pr_transactions:po_pr_cancel_view')
        # Only provide essential parameters for the redirect
        params = {'Code': 'ABC', 'SwitchTo': 'List', 'Trans': 'PO'}
        response = self.client.get(url, params)
        self.assertEqual(response.status_code, 302)
        # Verify redirect URL defaults to '/' if parentPage is missing
        expected_redirect_url = "/?Code=ABC&SwitchTo=List&Trans=PO&ModId=6&SubModId=35"
        self.assertEqual(response.url, expected_redirect_url)


    def test_po_pr_print_page_view(self):
        """
        Tests the POPRPrintPageView (simulated PDF generation).
        Checks if the correct template is used and if data is passed to it.
        """
        url = reverse('po_pr_transactions:po_pr_print_page')
        response = self.client.get(url, self.test_params)

        self.assertEqual(response.status_code, 200)
        # Verify that the correct template for the print page is used
        self.assertTemplateUsed(response, 'po_pr_transactions/po_print_page.html')
        
        # Check for presence of key data from the 'po_data' context variable
        # (which comes from the fat model's get_full_report_data method)
        self.assertContains(response, self.test_params['pono'])
        self.assertContains(response, self.test_params['AmdNo'])
        self.assertContains(response, "Purchase Order Printout")
        self.assertContains(response, "Example Supplier Co.") # From conceptual model data

    def test_po_pr_print_page_view_po_not_found(self):
        """
        Tests the POPRPrintPageView when the specified PO is not found.
        Ensures graceful handling and appropriate messages.
        """
        url = reverse('po_pr_transactions:po_pr_print_page')
        # Provide a PO number that does not exist in our test data
        params = self.test_params.copy()
        params['pono'] = 'NON-EXISTENT-PO'
        
        response = self.client.get(url, params)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_pr_transactions/po_print_page.html')
        # Check if the "not found" message or related content is present
        self.assertContains(response, "Could not retrieve detailed information")
        self.assertContains(response, "Status: Not Found")
        
        # Verify an error message is added to messages framework
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("not found or invalid details provided", str(messages[0]))

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

While the core functionality of `PO_PR_View_Print_Details.aspx` is primarily displaying an embedded `iframe` and handling a simple redirect, HTMX and Alpine.js are integrated for:

*   **Consistent Frontend Stack:** Ensuring all new components adhere to the modern frontend strategy (HTMX + Alpine.js + Tailwind CSS) for future scalability and uniformity across the entire migrated application.
*   **Progressive Enhancement:** While not strictly needed for this specific `iframe` embedding, using HTMX for other dynamic interactions (e.g., loading indicators, dynamic content sections, form submissions) in related modules will simplify future development and improve user experience without full page reloads.
*   **Simplified UI State Management:** Alpine.js offers a clean and declarative way to manage client-side UI state, such as showing/hiding modals, managing dynamic input fields, or displaying loading indicators, if more complex interactions are introduced on this page in the future.

**Implementation Details:**

*   **No DataTables:** DataTables is not applicable to this page as it presents a single "report" view, not a tabular list of records requiring client-side sorting, searching, or pagination.
*   **HTMX for General UI:** The main template `po_pr_view_print_details.html` implicitly relies on HTMX and Alpine.js being loaded from `core/base.html` for general application behavior (e.g., message display, modal functionality for other parts of the app if applicable). For this specific page, HTMX's direct interactions are minimal as the "Cancel" button performs a standard full page redirect (mimicking the original ASP.NET `Response.Redirect`).
*   **Alpine.js for Client-Side Reactivity:** The `extra_js` block in `po_pr_view_print_details.html` is provided as the entry point for any page-specific Alpine.js components. For a simple `iframe` embed, complex Alpine.js is not strictly necessary, but it maintains consistency for future enhancements (e.g., controlling a loading spinner that disappears once the `iframe` content is fully loaded).
*   **DRY Template Inheritance:** The `po_pr_transactions/po_pr_view_print_details.html` template extends `core/base.html`. This ensures that all necessary CDN links for Tailwind CSS, HTMX, Alpine.js, and any other global assets are inherited automatically, preventing duplication and ensuring a consistent look and feel across the application.

## Final Notes

*   **Placeholder Replacement:** It is crucial to replace placeholders like `tbl_PO_Header` with your actual legacy database table names and ensure field mappings (`db_column`) are accurate based on your existing schema. This is a manual verification step during the automated migration process.
*   **Business Logic in Models:** The `PurchaseOrder` model's `get_full_report_data` method exemplifies the "fat model" principle. In a real application, this method would encapsulate all complex business logic required to aggregate and format data from various related models for a comprehensive print report, keeping the view logic minimal.
*   **Print Page Implementation:** The `POPRPrintPageView` currently renders an HTML template. For actual PDF generation, this view would be enhanced to use Python libraries like `WeasyPrint` or `ReportLab` to convert HTML content into a PDF document, returning it with the `application/pdf` content type for direct display or download.
*   **Systemic Navigation:** The `parentPage` redirection is designed to match the original ASP.NET behavior. As part of a larger modernization effort, it is highly recommended to map these `parentPage` values to named Django URLs where possible, allowing for robust and maintainable navigation using Django's `reverse()` function.
*   **Comprehensive Testing:** The provided unit and integration tests are designed to cover the core functionalities, including data modeling, URL parameter handling, context preparation, and redirection logic, ensuring high test coverage as per best practices.

This modernization plan systematically breaks down the ASP.NET component into its Django equivalents, emphasizing automation-driven approaches, a modern technology stack, and clear, non-technical communication suitable for business stakeholders.