## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, particularly the `makegrid` function and `GetCompletionList` method, we've identified the following database tables and their approximate columns. Note that `SysDate` and `SysTime` appear to be stored as strings in the database, necessitating conversion logic within the Django model for proper handling.

**Table: `tblMM_PR_Master`**
-   **Id**: Primary Key (Integer).
-   **SessionId**: Foreign Key (Integer) to `tblHR_OfficeStaff.EmpId`.
-   **FinYearId**: Foreign Key (Integer) to `tblFinancial_master.FinYearId`.
-   **PRNo**: String (e.g., `VARCHAR`).
-   **WONo**: String (e.g., `VARCHAR`, nullable).
-   **SysDate**: String (e.g., `VARCHAR`), representing a date like 'MM-DD-YYYY' or 'DD-MM-YYYY'.
-   **SysTime**: String (e.g., `VARCHAR`), representing a time like 'HH:MM'.
-   **CompId**: Integer, represents the Company ID.

**Table: `tblHR_OfficeStaff`**
-   **EmpId**: Primary Key (Integer).
-   **EmployeeName**: String (e.g., `VARCHAR`).
-   **Title**: String (e.g., `VARCHAR`, nullable).
-   **CompId**: Integer.

**Table: `tblFinancial_master`**
-   **FinYearId**: Primary Key (Integer).
-   **FinYear**: String (e.g., `VARCHAR`), representing the financial year (e.g., '2023-24').
-   **CompId**: Integer.

### Step 2: Identify Backend Functionality

The ASP.NET `PR_Edit.aspx` page serves as a **list and search interface** for Purchase Requisitions (PRs).

*   **Read (List View):** Displays a paginated list of PRs from `tblMM_PR_Master` in a `GridView`. This will be replaced by a DataTables-powered HTML table.
*   **Search/Filtering:**
    *   Allows users to select a search criterion ("Employee Name" or "PR No") via a dropdown.
    *   Provides text input fields for the selected criterion.
    *   A "Search" button initiates the filtering process, which dynamically reloads the grid.
    *   An autocomplete feature is present for "Employee Name" search.
*   **Navigation to Details:** Each PR record has a "Select" link that directs to a separate `PR_Edit_Details.aspx` page for individual PR management. This page itself does not perform direct Create, Update, or Delete (CRUD) operations on PR records.

### Step 3: Infer UI Components

The UI components in ASP.NET map to standard HTML elements with Django templating, HTMX, and Alpine.js for dynamic behavior.

*   **`DropDownList` (`drpfield`):** Will be a standard HTML `<select>` element rendered by a Django form. HTMX will be used for auto-updating input field visibility based on selection.
*   **`TextBox` (`txtEmpName`, `txtprNo`):** Will be HTML `<input type="text">` fields. `txtEmpName` will integrate with Alpine.js and HTMX for autocomplete suggestions. Visibility will be controlled by Alpine.js based on the dropdown selection, or HTMX can swap the input fields.
*   **`Button` (`Button1`):** A standard HTML `<button>` element. It will trigger an HTMX request to refresh the PR list table.
*   **`GridView` (`GridView2`):** This data display component will be replaced by a simple HTML `<table>` element, styled with Tailwind CSS, and enhanced with the DataTables JavaScript library for client-side pagination, sorting, and searching.
*   **`AutoCompleteExtender`:** Replaced by a combination of HTMX for fetching suggestions and Alpine.js for managing the UI state of the autocomplete dropdown.

### Step 4: Generate Django Code

We'll structure the Django application under a hypothetical `material_management` app.

#### 4.1 Models (`material_management/models.py`)

Models are defined with `managed = False` to connect to the existing database tables. Properties are added to handle string-based date/time conversions, and a class method `get_filtered_pr_masters` encapsulates the search logic, adhering to the "fat model" principle.

```python
# material_management/models.py
from django.db import models
from datetime import datetime, time

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff.
    Represents an employee in the system.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name} [{self.emp_id}]"

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master.
    Represents a financial year.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class PrMaster(models.Model):
    """
    Maps to tblMM_PR_Master.
    Represents a Purchase Requisition master record.
    Business logic for filtering is included here.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Foreign keys with DO_NOTHING to prevent Django from creating cascade delete constraints
    session = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', related_name='pr_masters')
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', related_name='pr_masters')
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    
    # Storing dates and times as CharFields if they areVARCHAR in DB,
    # and providing properties for structured access.
    sys_date_str = models.CharField(db_column='SysDate', max_length=20) # e.g., '10-25-2023'
    sys_time_str = models.CharField(db_column='SysTime', max_length=10) # e.g., '14:30'
    
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'
        ordering = ['-id'] # Matches 'Order by Id Desc' in original query

    def __str__(self):
        return self.pr_no

    @property
    def formatted_sys_date(self):
        """Converts string date (e.g., 'MM-DD-YYYY' or 'DD-MM-YYYY') to 'DD-MM-YYYY' for display."""
        try:
            # Attempt to parse as MM-DD-YYYY
            date_obj = datetime.strptime(self.sys_date_str, '%m-%d-%Y').date()
        except ValueError:
            try:
                # Fallback to DD-MM-YYYY
                date_obj = datetime.strptime(self.sys_date_str, '%d-%m-%Y').date()
            except ValueError:
                return self.sys_date_str # Return original string if parsing fails
        return date_obj.strftime('%d-%m-%Y')

    @property
    def formatted_sys_time(self):
        """Converts string time (e.g., 'HH:MM') to 'HH:MM AM/PM' for display."""
        try:
            time_obj = datetime.strptime(self.sys_time_str, '%H:%M').time()
        except ValueError:
            return self.sys_time_str
        return time_obj.strftime('%I:%M %p') # e.g., "02:30 PM"
    
    @classmethod
    def get_filtered_pr_masters(cls, comp_id, fin_year_id, search_by_field, search_text):
        """
        Retrieves filtered PR Master records based on company, financial year, and search criteria.
        This method encapsulates the business logic for data retrieval and filtering.
        It uses select_related to optimize queries for related employee and financial year data.
        """
        # Start with base queryset, filtering by company and financial year
        # Assuming FinYearId is an integer and comparison is <= as per original logic.
        queryset = cls.objects.select_related('session', 'fin_year').filter(
            comp_id=comp_id, 
            fin_year__fin_year_id__lte=fin_year_id
        )

        if search_text:
            if search_by_field == '1': # Search by PR No
                queryset = queryset.filter(pr_no__icontains=search_text)
            elif search_by_field == '0': # Search by Employee Name (SessionId)
                # The ASP.NET autocomplete returns 'Employee Name [EmpId]'.
                # We extract the EmpId for a precise lookup if available, otherwise fallback to name contains.
                if '[' in search_text and search_text.endswith(']'):
                    try:
                        emp_id_str = search_text.split('[')[-1][:-1]
                        emp_id = int(emp_id_str)
                        queryset = queryset.filter(session__emp_id=emp_id)
                    except ValueError:
                        # If ID extraction fails (e.g., not a valid integer), fallback to name search
                        name_only = search_text.split('[')[0].strip()
                        queryset = queryset.filter(session__employee_name__icontains=name_only)
                else:
                    # If only employee name is passed (e.g., from manual typing)
                    queryset = queryset.filter(session__employee_name__icontains=search_text)

        return queryset
```

#### 4.2 Forms (`material_management/forms.py`)

A non-ModelForm is used for the search interface, as there are no direct CRUD operations for `PrMaster` on this page. HTMX attributes are added to the form fields to enable dynamic interactions.

```python
# material_management/forms.py
from django import forms
from django.forms.widgets import TextInput

class PrSearchForm(forms.Form):
    """
    Form for handling PR search criteria.
    Includes logic to control visibility of search input fields.
    """
    SEARCH_CHOICES = [
        ('0', 'Employee Name'), # Maps to original drpfield value "0"
        ('1', 'PR No'),         # Maps to original drpfield value "1"
    ]
    
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/material_management/pr_edit/pr_search_form_partial/', # HTMX to update form partial
            'hx-target': '#searchFormContainer', # Target to swap the entire form container
            'hx-swap': 'outerHTML'
        }),
        label="Search By"
    )
    
    employee_name = forms.CharField(
        required=False,
        widget=TextInput(attrs={
            'class': 'box3 w-[350px] block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'x-model': 'searchText' # Alpine.js for autocomplete
        }),
        label="Employee Name"
    )
    
    pr_no = forms.CharField(
        required=False,
        widget=TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter PR Number'
        }),
        label="PR Number"
    )

    def __init__(self, *args, **kwargs):
        """
        Initializes the form and sets initial visibility of search input fields.
        """
        initial_search_by = kwargs.pop('initial_search_by', '0')
        super().__init__(*args, **kwargs)
        self.fields['search_by'].initial = initial_search_by
        
        # Add 'hidden' class to control visibility based on the selected search_by field.
        # Alpine.js will then use x-show based on these classes.
        if initial_search_by == '1': # If PR No is selected
            self.fields['employee_name'].widget.attrs['class'] += ' hidden'
        else: # Default or Employee Name selected
            self.fields['pr_no'].widget.attrs['class'] += ' hidden'

```

#### 4.3 Views (`material_management/views.py`)

Views are kept thin, delegating business logic to the `PrMaster` model. HTMX requests are handled to serve partial templates for dynamic updates.

```python
# material_management/views.py
from django.views.generic import ListView, View
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.db.models import Q # For potential complex queries

from .models import PrMaster, Employee, FinancialYear
from .forms import PrSearchForm

class PrListView(ListView):
    """
    Main view for displaying the PR list page.
    Handles the initial load and renders the search form.
    """
    model = PrMaster
    template_name = 'material_management/pr_edit/list.html'
    context_object_name = 'pr_masters'
    
    def get_queryset(self):
        """
        Retrieves the filtered queryset using the model's business logic.
        Accesses session variables for company and financial year.
        """
        comp_id = self.request.session.get('compid', 0)
        fin_year_id = self.request.session.get('finyear', '') 
        
        # Get search parameters from GET request (when form is submitted)
        search_by_field = self.request.GET.get('search_by', '0')
        search_text = self.request.GET.get('employee_name', '') if search_by_field == '0' else self.request.GET.get('pr_no', '')

        # Delegate filtering logic to the model
        queryset = PrMaster.get_filtered_pr_masters(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_by_field=search_by_field,
            search_text=search_text
        )
        return queryset

    def get_context_data(self, **kwargs):
        """
        Adds the search form to the template context, populating it with current search criteria.
        """
        context = super().get_context_data(**kwargs)
        
        # Get current search parameters for form initialization
        search_by_field = self.request.GET.get('search_by', '0')
        employee_name_text = self.request.GET.get('employee_name', '')
        pr_no_text = self.request.GET.get('pr_no', '')
        
        # Initialize the form with current values and pass the 'search_by' for initial field visibility
        context['form'] = PrSearchForm(
            initial={
                'search_by': search_by_field,
                'employee_name': employee_name_text,
                'pr_no': pr_no_text,
            },
            initial_search_by=search_by_field
        )
        return context

class PrTablePartialView(PrListView):
    """
    HTMX endpoint for loading/refreshing only the PR list table.
    Reuses the get_queryset from PrListView.
    """
    template_name = 'material_management/pr_edit/_pr_table.html'
    # No need for get_context_data as parent's get_queryset provides 'pr_masters'

class PrSearchFormPartialView(View):
    """
    HTMX endpoint to dynamically render the search form partial,
    adjusting input field visibility based on 'search_by' selection.
    """
    def post(self, request, *args, **kwargs):
        search_by = request.POST.get('search_by', '0')
        form = PrSearchForm(initial_search_by=search_by)
        return render(request, 'material_management/pr_edit/_pr_search_form.html', {'form': form})

class EmployeeAutoCompleteView(View):
    """
    HTMX/JSON endpoint for providing employee name autocomplete suggestions.
    Filters employees based on company ID and prefix text.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('query', '').strip()
        comp_id = request.session.get('compid', 0)

        if not prefix_text:
            return JsonResponse([], safe=False)

        # Query employees, limit results, and format them as 'Name [ID]'
        employees = Employee.objects.filter(
            comp_id=comp_id,
            employee_name__icontains=prefix_text
        ).values_list('employee_name', 'emp_id')[:10] # Limit results for performance

        results = [f"{name} [{emp_id}]" for name, emp_id in employees]
        results.sort() # Ensure consistent sorting

        return JsonResponse(results, safe=False)
```

#### 4.4 Templates (`material_management/templates/material_management/pr_edit/`)

Templates are designed for HTMX partial updates. `list.html` acts as the main page, loading partials for the search form and the data table. Alpine.js manages the autocomplete UI.

```html
{# material_management/pr_edit/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">PR List</h2>
        <!-- The original page was for editing/listing, not direct creation.
             No "Add New PR" button on this specific page. -->
    </div>
    
    {# Search Form Section #}
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">Search PRs</h3>
        <form hx-get="{% url 'pr_master_table' %}" hx-target="#prMasterTable-container" hx-swap="innerHTML">
            {% csrf_token %}
            <div id="searchFormContainer" class="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                {# This partial will be swapped by HTMX when search_by dropdown changes.
                   It contains the search_by dropdown and dynamically visible input fields. #}
                {% include 'material_management/pr_edit/_pr_search_form.html' with form=form %}
            </div>
            <div class="mt-4 text-right">
                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
            </div>
        </form>
    </div>

    {# PR List Table Section #}
    <div id="prMasterTable-container"
         hx-trigger="load, refreshPrMasterList from:body" {# Loads on page load, and on custom event #}
         hx-get="{% url 'pr_master_table' %}" {# Fetches the table content #}
         hx-swap="innerHTML" {# Replaces the content inside this div #}
         class="bg-white shadow-md rounded-lg p-6 overflow-x-auto">
        <!-- Initial loading state -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading PR data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for employee name autocomplete
    document.addEventListener('alpine:init', () => {
        Alpine.data('employeeAutocomplete', () => ({
            suggestions: [],
            showSuggestions: false,
            searchText: '',

            init() {
                // Watch for changes in searchText to trigger suggestion fetching
                this.$watch('searchText', (value) => {
                    if (value.length > 0) {
                        this.fetchSuggestions(value);
                    } else {
                        this.suggestions = [];
                        this.showSuggestions = false;
                    }
                });
            },
            
            fetchSuggestions(query) {
                fetch(`{% url 'employee_autocomplete' %}?query=${encodeURIComponent(query)}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        this.suggestions = data;
                        this.showSuggestions = data.length > 0;
                    })
                    .catch(error => console.error('Error fetching autocomplete suggestions:', error));
            },

            selectSuggestion(suggestion) {
                this.searchText = suggestion;
                this.showSuggestions = false;
                // Ensure the form field's value is updated for submission
                document.getElementById('id_employee_name').value = suggestion;
            }
        }));
    });

    // HTMX event listener for DataTables initialization
    // This runs AFTER hx-swap has completed, ensuring the table exists in the DOM
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'prMasterTable-container') {
            // Check if DataTable is already initialized and destroy it to re-initialize
            if ($.fn.DataTable.isDataTable('#prMasterTable')) {
                $('#prMasterTable').DataTable().destroy();
            }
            $('#prMasterTable').DataTable({
                "pageLength": 20, // Matches original ASP.NET GridView PageSize
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true, // Make table responsive
                "columnDefs": [ // Disable sorting for SN and Actions column
                    { "orderable": false, "targets": [0, 7] } 
                ]
            });
        }
    });
</script>
{% endblock %}
```

```html
{# material_management/pr_edit/_pr_search_form.html #}
{# This partial is swapped by HTMX on search_by dropdown change to dynamically show/hide inputs. #}
<div id="searchFormContainer"> {# Important: This div needs the ID for hx-target #}
    <div class="mb-4">
        <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.search_by.label }}
        </label>
        {{ form.search_by }}
    </div>

    {# Employee Name input with Alpine.js for autocomplete #}
    <div x-data="employeeAutocomplete" 
         x-show="!{{ 'true' if 'hidden' in form.fields.employee_name.widget.attrs.get('class', '') else 'false' }}"
         class="{{ 'hidden' if 'hidden' in form.fields.employee_name.widget.attrs.get('class', '') else '' }} mb-4">
        <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.employee_name.label }}
        </label>
        <div class="relative">
            <input type="text" id="{{ form.employee_name.id_for_label }}" name="{{ form.employee_name.html_name }}"
                   x-model="searchText"
                   @focus="showSuggestions = searchText.length > 0" @click.away="showSuggestions = false"
                   class="{{ form.employee_name.widget.attrs.get('class') }}" placeholder="{{ form.employee_name.widget.attrs.get('placeholder') }}"
                   value="{{ form.employee_name.value|default:'' }}">
            
            <ul x-show="showSuggestions && suggestions.length > 0"
                class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto mt-1">
                <template x-for="suggestion in suggestions" :key="suggestion">
                    <li x-text="suggestion" @click="selectSuggestion(suggestion)"
                        class="px-4 py-2 cursor-pointer hover:bg-gray-100"></li>
                </template>
            </ul>
        </div>
    </div>

    {# PR No input #}
    <div x-show="!{{ 'true' if 'hidden' in form.fields.pr_no.widget.attrs.get('class', '') else 'false' }}"
         class="{{ 'hidden' if 'hidden' in form.fields.pr_no.widget.attrs.get('class', '') else '' }} mb-4">
        <label for="{{ form.pr_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.pr_no.label }}
        </label>
        <input type="text" id="{{ form.pr_no.id_for_label }}" name="{{ form.pr_no.html_name }}"
               value="{{ form.pr_no.value|default:'' }}"
               class="{{ form.pr_no.widget.attrs.get('class') }}" placeholder="{{ form.pr_no.widget.attrs.get('placeholder') }}">
    </div>
</div>
```

```html
{# material_management/pr_edit/_pr_table.html #}
{# This partial contains only the table content, loaded by HTMX. #}
<table id="prMasterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for pr_master in pr_masters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pr_master.fin_year.fin_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pr_master.pr_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pr_master.wo_no|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pr_master.formatted_sys_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pr_master.formatted_sys_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200 w-1/3">{{ pr_master.session.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {# Mimicking the HyperLinkField from ASP.NET for navigation to details #}
                <a href="{% url 'pr_master_detail' pr_master.pr_no pr_master.wo_no pr_master.id %}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                    Select
                </a>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 text-center text-lg text-red-600">
                No data to display !
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

#### 4.5 URLs (`material_management/urls.py`)

URL patterns are defined for the main list page, the HTMX partials, and the autocomplete endpoint.

```python
# material_management/urls.py
from django.urls import path
from .views import (
    PrListView, 
    PrTablePartialView, 
    PrSearchFormPartialView, 
    EmployeeAutoCompleteView,
)
from django.http import HttpResponse # For placeholder detail view

urlpatterns = [
    # Main PR List page: Serves the initial page content including the search form and table container
    path('pr_edit/', PrListView.as_view(), name='pr_master_list'),
    
    # HTMX endpoint to load/refresh the PR list table (results of search/filter)
    path('pr_edit/table/', PrTablePartialView.as_view(), name='pr_master_table'),

    # HTMX endpoint for dynamic search form (to toggle visibility of search input fields)
    path('pr_edit/pr_search_form_partial/', PrSearchFormPartialView.as_view(), name='pr_search_form_partial'),
    
    # HTMX endpoint for employee name autocomplete suggestions
    path('autocomplete/employee/', EmployeeAutoCompleteView.as_view(), name='employee_autocomplete'),
    
    # Placeholder URL for the 'Select' hyperlink from the original ASP.NET page.
    # This would typically link to a separate detail page/view for editing a specific PR.
    path('pr_edit/details/<str:pr_no>/<str:wo_no>/<int:pk>/', 
         lambda request, pr_no, wo_no, pk: HttpResponse(f"PR Detail for PRNo: {pr_no}, WONo: {wo_no}, ID: {pk}"), 
         name='pr_master_detail'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive unit tests for models ensure data integrity and proper method behavior. Integration tests for views verify correct rendering, context, and HTMX interactions.

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from datetime import date, time

from .models import PrMaster, Employee, FinancialYear
from .forms import PrSearchForm
from .views import PrListView, PrTablePartialView, PrSearchFormPartialView, EmployeeAutoCompleteView

# Mixin to set up common test data for models and views
class ModelTestSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Define common company and financial year for tests
        cls.comp_id = 1
        cls.fin_year_id = 2023 
        
        # Create dummy Employee records
        cls.employee1 = Employee.objects.create(
            emp_id=101, employee_name='Alice Smith', title='Ms', comp_id=cls.comp_id
        )
        cls.employee2 = Employee.objects.create(
            emp_id=102, employee_name='Bob Johnson', title='Mr', comp_id=cls.comp_id
        )
        # Create dummy FinancialYear records
        cls.fin_year_current = FinancialYear.objects.create(
            fin_year_id=cls.fin_year_id, fin_year='2023-24', comp_id=cls.comp_id
        )
        cls.fin_year_previous = FinancialYear.objects.create(
            fin_year_id=cls.fin_year_id - 1, fin_year='2022-23', comp_id=cls.comp_id
        )
        # Create dummy PrMaster records
        cls.pr_master1 = PrMaster.objects.create(
            id=1, session=cls.employee1, fin_year=cls.fin_year_current, 
            pr_no='PR001', wo_no='WO001', sys_date_str='10-25-2023', 
            sys_time_str='14:30', comp_id=cls.comp_id
        )
        cls.pr_master2 = PrMaster.objects.create(
            id=2, session=cls.employee2, fin_year=cls.fin_year_current, 
            pr_no='PR002', wo_no='WO002', sys_date_str='11-01-2023', 
            sys_time_str='09:00', comp_id=cls.comp_id
        )
        cls.pr_master3_old_fin_year = PrMaster.objects.create(
            id=3, session=cls.employee1, fin_year=cls.fin_year_previous, 
            pr_no='PR003', wo_no='WO003', sys_date_str='09-15-2022', 
            sys_time_str='16:45', comp_id=cls.comp_id
        )
        # PR from a different company, should not appear in default queries
        cls.pr_master_other_comp = PrMaster.objects.create(
            id=4, session=cls.employee1, fin_year=cls.fin_year_current, 
            pr_no='PR004', wo_no='WO004', sys_date_str='12-05-2023', 
            sys_time_str='10:00', comp_id=cls.comp_id + 1
        )

class EmployeeModelTest(ModelTestSetupMixin, TestCase):
    """Unit tests for the Employee model."""
    def test_employee_creation(self):
        self.assertEqual(self.employee1.employee_name, 'Alice Smith')
        self.assertEqual(self.employee1.emp_id, 101)
        self.assertEqual(self.employee1.comp_id, self.comp_id)

    def test_employee_str_representation(self):
        self.assertEqual(str(self.employee1), 'Ms. Alice Smith [101]')
        # Test with no title
        employee_no_title = Employee.objects.create(emp_id=103, employee_name='John Doe', title=None, comp_id=self.comp_id)
        self.assertEqual(str(employee_no_title), '. John Doe [103]')

class FinancialYearModelTest(ModelTestSetupMixin, TestCase):
    """Unit tests for the FinancialYear model."""
    def test_fin_year_creation(self):
        self.assertEqual(self.fin_year_current.fin_year, '2023-24')
        self.assertEqual(self.fin_year_current.fin_year_id, self.fin_year_id)
        self.assertEqual(self.fin_year_current.comp_id, self.comp_id)

    def test_fin_year_str_representation(self):
        self.assertEqual(str(self.fin_year_current), '2023-24')

class PrMasterModelTest(ModelTestSetupMixin, TestCase):
    """Unit tests for the PrMaster model, including business logic methods."""
    def test_pr_master_creation(self):
        self.assertEqual(self.pr_master1.pr_no, 'PR001')
        self.assertEqual(self.pr_master1.session, self.employee1)
        self.assertEqual(self.pr_master1.fin_year, self.fin_year_current)
        self.assertEqual(self.pr_master1.sys_date_str, '10-25-2023')
        self.assertEqual(self.pr_master1.sys_time_str, '14:30')
        self.assertEqual(self.pr_master1.comp_id, self.comp_id)

    def test_pr_master_str_representation(self):
        self.assertEqual(str(self.pr_master1), 'PR001')

    def test_formatted_sys_date_property(self):
        # Test MM-DD-YYYY format
        self.assertEqual(self.pr_master1.formatted_sys_date, '25-10-2023')
        # Test DD-MM-YYYY format (if it was stored that way)
        pr_ddmmyyyy = PrMaster.objects.create(
            id=5, session=self.employee1, fin_year=self.fin_year_current,
            pr_no='PR005', wo_no='WO005', sys_date_str='01-01-2024',
            sys_time_str='10:00', comp_id=self.comp_id
        )
        self.assertEqual(pr_ddmmyyyy.formatted_sys_date, '01-01-2024')
        # Test invalid format
        pr_invalid_date = PrMaster.objects.create(
            id=6, session=self.employee1, fin_year=self.fin_year_current,
            pr_no='PR006', wo_no='WO006', sys_date_str='Invalid Date',
            sys_time_str='10:00', comp_id=self.comp_id
        )
        self.assertEqual(pr_invalid_date.formatted_sys_date, 'Invalid Date')

    def test_formatted_sys_time_property(self):
        self.assertEqual(self.pr_master1.formatted_sys_time, '02:30 PM')
        self.assertEqual(self.pr_master2.formatted_sys_time, '09:00 AM')
        # Test invalid time format
        pr_invalid_time = PrMaster.objects.create(
            id=7, session=self.employee1, fin_year=self.fin_year_current,
            pr_no='PR007', wo_no='WO007', sys_date_str='01-01-2024',
            sys_time_str='Invalid Time', comp_id=self.comp_id
        )
        self.assertEqual(pr_invalid_time.formatted_sys_time, 'Invalid Time')

    def test_get_filtered_pr_masters_no_filter(self):
        """Test default queryset retrieval."""
        queryset = PrMaster.get_filtered_pr_masters(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, 
            search_by_field='', search_text=''
        )
        # Should include PR001, PR002 (current fin year, same company)
        self.assertEqual(queryset.count(), 2) 
        self.assertIn(self.pr_master1, queryset)
        self.assertIn(self.pr_master2, queryset)
        # Should not include PR003 (older fin year) or PR004 (other company)
        self.assertNotIn(self.pr_master3_old_fin_year, queryset)
        self.assertNotIn(self.pr_master_other_comp, queryset)

    def test_get_filtered_pr_masters_by_pr_no(self):
        """Test filtering by PR number."""
        queryset = PrMaster.get_filtered_pr_masters(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, 
            search_by_field='1', search_text='PR001'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.pr_master1, queryset)
        self.assertNotIn(self.pr_master2, queryset)

    def test_get_filtered_pr_masters_by_employee_name_with_id(self):
        """Test filtering by employee name when autocomplete provides ID."""
        queryset = PrMaster.get_filtered_pr_masters(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, 
            search_by_field='0', search_text='Alice Smith [101]'
        )
        self.assertEqual(queryset.count(), 1) 
        self.assertIn(self.pr_master1, queryset)
        self.assertNotIn(self.pr_master2, queryset)

    def test_get_filtered_pr_masters_by_employee_name_without_id_partial(self):
        """Test filtering by partial employee name without ID."""
        queryset = PrMaster.get_filtered_pr_masters(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, 
            search_by_field='0', search_text='bob'
        )
        self.assertEqual(queryset.count(), 1) 
        self.assertIn(self.pr_master2, queryset)
        self.assertNotIn(self.pr_master1, queryset)

    def test_get_filtered_pr_masters_by_employee_name_invalid_id_format(self):
        """Test filtering by employee name when ID format is invalid."""
        queryset = PrMaster.get_filtered_pr_masters(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, 
            search_by_field='0', search_text='Alice Smith [ABC]' # Invalid ID
        )
        self.assertEqual(queryset.count(), 1) 
        self.assertIn(self.pr_master1, queryset)
        self.assertNotIn(self.pr_master2, queryset)

class PrSearchFormTest(TestCase):
    """Unit tests for the PrSearchForm."""
    def test_form_fields_present(self):
        form = PrSearchForm()
        self.assertIn('search_by', form.fields)
        self.assertIn('employee_name', form.fields)
        self.assertIn('pr_no', form.fields)
        self.assertFalse(form.fields['employee_name'].required)
        self.assertFalse(form.fields['pr_no'].required)

    def test_form_initial_visibility_employee_name(self):
        """Default: Employee Name visible, PR No hidden."""
        form = PrSearchForm(initial_search_by='0') 
        self.assertNotIn('hidden', form.fields['employee_name'].widget.attrs.get('class', ''))
        self.assertIn('hidden', form.fields['pr_no'].widget.attrs.get('class', ''))

    def test_form_initial_visibility_pr_no(self):
        """PR No selected: PR No visible, Employee Name hidden."""
        form = PrSearchForm(initial_search_by='1')
        self.assertIn('hidden', form.fields['employee_name'].widget.attrs.get('class', ''))
        self.assertNotIn('hidden', form.fields['pr_no'].widget.attrs.get('class', ''))

class PrViewsTest(ModelTestSetupMixin, TestCase):
    """Integration tests for PR related views."""
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Set session variables as they are used in get_queryset
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_pr_list_view_get(self):
        """Test initial load of the main PR list page."""
        response = self.client.get(reverse('pr_master_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_edit/list.html')
        self.assertIn('pr_masters', response.context)
        self.assertEqual(response.context['pr_masters'].count(), 2) # Only current comp & fin year PRs
        self.assertContains(response, 'PR001')
        self.assertContains(response, 'Alice Smith')
        self.assertIsInstance(response.context['form'], PrSearchForm)
        # Check initial form visibility (employee name should be visible)
        self.assertContains(response, 'name="employee_name"')
        self.assertContains(response, 'name="pr_no" class="box3 hidden"') # Check hidden class

    def test_pr_table_partial_view_no_search(self):
        """Test HTMX request for the table partial without search filters."""
        response = self.client.get(reverse('pr_master_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_edit/_pr_table.html')
        self.assertIn('pr_masters', response.context)
        self.assertEqual(response.context['pr_masters'].count(), 2)
        self.assertContains(response, 'PR001')
        self.assertContains(response, 'PR002')

    def test_pr_table_partial_view_search_by_pr_no(self):
        """Test HTMX request for table partial with PR No filter."""
        response = self.client.get(
            reverse('pr_master_table'), 
            {'search_by': '1', 'pr_no': 'PR001'}, # Note: 'pr_no' for the input field
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PR001')
        self.assertNotContains(response, 'PR002')
        self.assertEqual(response.context['pr_masters'].count(), 1)

    def test_pr_table_partial_view_search_by_employee_name(self):
        """Test HTMX request for table partial with Employee Name filter (with ID)."""
        response = self.client.get(
            reverse('pr_master_table'), 
            {'search_by': '0', 'employee_name': 'Alice Smith [101]'}, # Note: 'employee_name' for the input field
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PR001')
        self.assertContains(response, 'Alice Smith')
        self.assertNotContains(response, 'PR002')
        self.assertEqual(response.context['pr_masters'].count(), 1)
        
    def test_pr_table_partial_view_search_by_employee_name_partial(self):
        """Test HTMX request for table partial with partial Employee Name filter."""
        response = self.client.get(
            reverse('pr_master_table'), 
            {'search_by': '0', 'employee_name': 'bob'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PR002')
        self.assertContains(response, 'Bob Johnson')
        self.assertNotContains(response, 'PR001')
        self.assertEqual(response.context['pr_masters'].count(), 1)

    def test_pr_search_form_partial_view_post_pr_no_selected(self):
        """Test HTMX post to update search form for PR No selection."""
        response = self.client.post(
            reverse('pr_search_form_partial'), 
            {'search_by': '1'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_edit/_pr_search_form.html')
        # Check if employee_name field is hidden and pr_no is visible
        self.assertContains(response, 'name="employee_name"') # The name attribute should still be present
        self.assertContains(response, 'class="box3 w-[350px] hidden"') # But it should have the hidden class
        self.assertContains(response, 'name="pr_no" class="box3"') # PR No should be visible

    def test_pr_search_form_partial_view_post_employee_name_selected(self):
        """Test HTMX post to update search form for Employee Name selection."""
        response = self.client.post(
            reverse('pr_search_form_partial'), 
            {'search_by': '0'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_edit/_pr_search_form.html')
        self.assertContains(response, 'name="employee_name" class="box3 w-[350px]"') # Employee Name should be visible
        self.assertContains(response, 'name="pr_no" class="box3 hidden"') # PR No should be hidden

    def test_employee_autocomplete_view_with_query(self):
        """Test autocomplete endpoint with a valid query."""
        response = self.client.get(
            reverse('employee_autocomplete'), 
            {'query': 'ali'}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Alice Smith [101]', data)
        self.assertNotIn('Bob Johnson [102]', data) # Should not contain others

    def test_employee_autocomplete_view_no_query(self):
        """Test autocomplete endpoint with no query."""
        response = self.client.get(
            reverse('employee_autocomplete'), 
            {'query': ''}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(data, []) # Should return empty list

    def test_pr_master_detail_placeholder_view(self):
        """Test the placeholder view for the detail page link."""
        response = self.client.get(
            reverse('pr_master_detail', args=['PR001', 'WO001', 1])
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PR Detail for PRNo: PR001, WONo: WO001, ID: 1')

```