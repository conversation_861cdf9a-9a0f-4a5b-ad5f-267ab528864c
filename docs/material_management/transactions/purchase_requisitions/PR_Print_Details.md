This document outlines a comprehensive plan for modernizing the given ASP.NET application, specifically the `PR_Print_Details.aspx` page, to a modern Django-based solution. Our approach prioritizes automated conversion, clean architecture, and modern web technologies for an efficient and scalable system.

The original ASP.NET page is primarily a reporting view that displays the details of a Purchase Requisition (PR) by dynamically fetching data from multiple database tables and presenting it via Crystal Reports. In the Django ecosystem, this functionality will be replaced by direct database queries via the Django ORM, rendering the data in a rich HTML template using HTMX and Alpine.js for a responsive user experience.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:** The C# code-behind uses a complex SQL `SELECT` query joining several tables to retrieve PR details.

**Identified Tables and Key Columns:**

*   **`tblMM_PR_Master` (Django Model: `PRMaster`)**:
    *   `PRNo` (Purchase Requisition Number, used in query filter)
    *   `CompId` (Company ID, used in query filter)
    *   `SysDate` (System Date, PR creation date)
    *   `WONo` (Work Order Number, used for lookup in `SD_Cust_WorkOrder_Master`)
    *   `Id` (Primary Key, linked to `tblMM_PR_Details.MId`)
    *   `SessionId` (Employee Session ID, linked to `tblHR_OfficeStaff.EmpId`)
*   **`tblMM_PR_Details` (Django Model: `PRDetail`)**:
    *   `MId` (Master ID, Foreign Key to `tblMM_PR_Master.Id`)
    *   `ItemId` (Item ID, Foreign Key to `tblDG_Item_Master.Id`)
    *   `Qty` (Quantity)
    *   `Rate` (Rate per unit)
    *   `DelDate` (Delivery Date)
    *   `SupplierId` (Supplier ID, Foreign Key to `tblMM_Supplier_master.SupplierId`)
    *   `AHId` (Account Head ID, Foreign Key to `AccHead.Id`)
    *   `Remarks` (Remarks/Notes)
    *   `Discount` (Discount percentage/amount)
*   **`tblDG_Item_Master` (Django Model: `ItemMaster`)**:
    *   `Id` (Primary Key, linked to `tblMM_PR_Details.ItemId`)
    *   `ItemCode` (Item Code)
    *   `ManfDesc` (Manufacturer Description/Item Name)
    *   `UOMBasic` (Unit of Measure ID, Foreign Key to `Unit_Master.Id`)
*   **`Unit_Master` (Django Model: `UnitMaster`)**:
    *   `Id` (Primary Key, linked to `tblDG_Item_Master.UOMBasic`)
    *   `Symbol` (Unit Symbol, e.g., "KG", "PCS")
*   **`tblMM_Supplier_master` (Django Model: `SupplierMaster`)**:
    *   `SupplierId` (Primary Key, linked to `tblMM_PR_Details.SupplierId`)
    *   `SupplierName` (Supplier Name)
*   **`AccHead` (Django Model: `AccountHead`)**:
    *   `Id` (Primary Key, linked to `tblMM_PR_Details.AHId`)
    *   `Symbol` (Account Head Symbol/Name)
*   **`tblHR_OfficeStaff` (Django Model: `Employee`)**:
    *   `EmpId` (Primary Key, linked to `tblMM_PR_Master.SessionId`)
    *   `Title` (Title, e.g., "Mr.", "Ms.")
    *   `EmployeeName` (Employee Name)
*   **`tblCompany_master` (Django Model: `CompanyMaster`)**:
    *   `CompId` (Primary Key, linked to `tblMM_PR_Master.CompId`)
    *   Other fields for Company Name and Address (inferred from `fun.getCompany`, `fun.CompAdd`)
*   **`SD_Cust_WorkOrder_Master` (Django Model: `WorkOrderMaster`)**:
    *   `WONo` (Work Order Number, used in lookup)
    *   `TaskProjectTitle` (Task/Project Title)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:** The ASP.NET page performs a **Read** operation only. It fetches detailed information for a specific Purchase Requisition and displays it. There are no Create, Update, or Delete operations on this page. The "reporting" aspect involves joining data from multiple tables and applying formatting and lookups.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
*   **`CrystalReportViewer`**: This is the main component, used for rendering a report. In Django, this will be replaced by a custom HTML table structure.
*   **`asp:Button ID="Cancel"`**: A button to navigate back. In Django, this will be a simple HTML anchor tag or an HTMX-driven navigation.
*   The page structure indicates a header area (`PR - Print`) and a main content area for the report viewer.

**Django Equivalent Strategy:**
*   A single HTML template will be created to display the PR header details and a list of PR items.
*   For the list of PR items, we will use a DataTables enabled `<table>` as per the guidelines, even though it's a detail view, to fulfill the "DataTables for all list views and data presentation" requirement for tabular data.
*   Basic styling will be applied using Tailwind CSS.

### Step 4: Generate Django Code

We will create a new Django application, e.g., `material_management`, to house these components.

#### 4.1 Models (`material_management/models.py`)

This section defines the Django models corresponding to the identified database tables. We will implement a custom manager method on `PRMaster` to encapsulate the complex data retrieval and processing logic, adhering to the "Fat Model" principle.

```python
from django.db import models, connections
from datetime import datetime

# Helper function to get company details (simulating fun.getCompany and fun.CompAdd)
# In a real scenario, this would ideally be a method on CompanyMaster or a dedicated service.
def get_company_details(comp_id):
    """Retrieves company name and address based on CompId."""
    try:
        with connections['default'].cursor() as cursor: # Assuming 'default' is your legacy DB connection
            cursor.execute(f"SELECT CompanyName, Address FROM tblCompany_master WHERE CompId = {comp_id}")
            row = cursor.fetchone()
            if row:
                return {'name': row[0], 'address': row[1]}
    except Exception as e:
        # Log the error, return default or raise as appropriate
        print(f"Error fetching company details: {e}")
    return {'name': 'Company Name Not Found', 'address': 'Address Not Found'}

# Helper function to get item code (simulating fun.GetItemCode_PartNo)
# This could also be a method on ItemMaster if the logic is simple.
def get_item_code_part_no(comp_id, item_id):
    """Retrieves ItemCode/PartNo based on CompId and ItemId."""
    try:
        return ItemMaster.objects.using('default').get(pk=item_id).item_code
    except ItemMaster.DoesNotExist:
        return 'N/A'
    except Exception as e:
        print(f"Error fetching item code: {e}")
        return 'N/A'

class PRMasterManager(models.Manager):
    def get_pr_details_for_print(self, pr_no, master_id, comp_id):
        """
        Retrieves comprehensive Purchase Requisition details for printing,
        mimicking the complex data retrieval and processing logic from the ASP.NET code-behind.
        """
        # Ensure correct types for filtering
        pr_no = str(pr_no)
        master_id = int(master_id)
        comp_id = int(comp_id)

        # Main PR Master record
        try:
            pr_master = self.select_related(
                'employee', # SessionId links to tblHR_OfficeStaff
                'company'   # CompId links to tblCompany_master
            ).get(pr_no=pr_no, pk=master_id, comp_id=comp_id) # Using pk for PRMaster.Id
        except PRMaster.DoesNotExist:
            return None, [] # Return None for master, empty list for details

        # Retrieve related PR Details
        pr_details_queryset = pr_master.pr_details.select_related(
            'item', 'item__uom', 'supplier', 'account_head'
        ).filter(pr_master_id=master_id) # Explicitly filter by pr_master_id which is PRDetail.MId

        # Process details for the final output
        detailed_items = []
        for detail in pr_details_queryset:
            # Simulate fun.GetItemCode_PartNo
            item_code_part_no = get_item_code_part_no(comp_id, detail.item.pk)

            # Simulate lookup for TaskProjectTitle
            task_project_title = "NA"
            if pr_master.wo_no:
                try:
                    work_order = WorkOrderMaster.objects.using('default').get(
                        comp_id=comp_id, wo_no=pr_master.wo_no
                    )
                    task_project_title = work_order.task_project_title
                except WorkOrderMaster.DoesNotExist:
                    pass # Keep "NA"

            detailed_items.append({
                'item_code': item_code_part_no,
                'purch_desc': detail.item.manf_desc,
                'uom_purch': detail.item.uom.symbol if detail.item.uom else '',
                'del_date': detail.del_date.strftime('%d/%m/%Y') if detail.del_date else '', # Simulate fun.FromDateDMY
                'qty': float(detail.qty),
                'rate': float(detail.rate),
                'wo_no': pr_master.wo_no if pr_master.wo_no else "NA",
                'supplier_name': f"{detail.supplier.supplier_name} [ {detail.supplier.supplier_id} ]" if detail.supplier else '',
                'ac_head': detail.account_head.symbol if detail.account_head else '',
                'remarks': detail.remarks,
                'comp_id': comp_id,
                'intender': f"{pr_master.employee.title}.{pr_master.employee.employee_name}" if pr_master.employee else '',
                'task_project_title': task_project_title,
                'discount': float(detail.discount),
            })
        
        # Simulate report parameters
        company_details = get_company_details(comp_id)
        report_parameters = {
            'reg_date': pr_master.sys_date.strftime('%d/%m/%Y') if pr_master.sys_date else '', # Simulate fun.FromDate
            'pr_no': pr_master.pr_no,
            'company': company_details['name'],
            'address': company_details['address'],
        }

        return report_parameters, detailed_items

class PRMaster(models.Model):
    # This ID field is implicit in Django's Model, but explicitly defined for clarity if db_table has 'Id'
    # It links to tblMM_PR_Details.MId
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    sys_date = models.DateField(db_column='SysDate', null=True, blank=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, null=True, blank=True)
    session_id = models.IntegerField(db_column='SessionId', null=True, blank=True)

    # Relationships (Django automatically adds related_name for reverse relationships)
    employee = models.ForeignKey('Employee', on_delete=models.DO_NOTHING, db_column='SessionId', related_name='pr_masters', null=True, blank=True)
    company = models.ForeignKey('CompanyMaster', on_delete=models.DO_NOTHING, db_column='CompId', related_name='pr_masters_by_comp', null=True, blank=True)

    objects = PRMasterManager()

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'Purchase Requisition Master'
        verbose_name_plural = 'Purchase Requisition Masters'

    def __str__(self):
        return f"PR: {self.pr_no} (ID: {self.id})"

class PRDetail(models.Model):
    # MId is the foreign key to PRMaster.Id
    pr_master_id = models.IntegerField(db_column='MId')
    item_id = models.IntegerField(db_column='ItemId')
    qty = models.FloatField(db_column='Qty')
    rate = models.FloatField(db_column='Rate')
    del_date = models.DateField(db_column='DelDate', null=True, blank=True)
    supplier_id = models.IntegerField(db_column='SupplierId')
    ah_id = models.IntegerField(db_column='AHId')
    remarks = models.TextField(db_column='Remarks', null=True, blank=True)
    discount = models.FloatField(db_column='Discount')

    # Relationships
    pr_master = models.ForeignKey(PRMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='pr_details', primary_key=False) # MId acts as FK
    item = models.ForeignKey('ItemMaster', on_delete=models.DO_NOTHING, db_column='ItemId', related_name='pr_details', null=True, blank=True)
    supplier = models.ForeignKey('SupplierMaster', on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='pr_details', null=True, blank=True)
    account_head = models.ForeignKey('AccountHead', on_delete=models.DO_NOTHING, db_column='AHId', related_name='pr_details', null=True, blank=True)
    
    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'Purchase Requisition Detail'
        verbose_name_plural = 'Purchase Requisition Details'
        unique_together = (('pr_master', 'item_id', 'qty'),) # Example composite primary key if exists, adjust as needed

    def __str__(self):
        return f"PR Item: {self.item.item_code if self.item else 'N/A'} (Qty: {self.qty})"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.IntegerField(db_column='UOMBasic')

    uom = models.ForeignKey('UnitMaster', on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class SupplierMaster(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.supplier_name

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol

class Employee(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}.{self.employee_name}"

class CompanyMaster(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255, null=True, blank=True)
    address = models.TextField(db_column='Address', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

    def __str__(self):
        return self.company_name or f"Company {self.comp_id}"

class WorkOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the PK for consistency
    wo_no = models.CharField(db_column='WONo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.wo_no

```

#### 4.2 Forms

**Analysis:** This page is a read-only "report" view. There are no user input fields requiring a form. Therefore, no Django forms (`forms.py`) are necessary for this specific functionality.

#### 4.3 Views (`material_management/views.py`)

This view will retrieve the processed PR details using the `PRMasterManager` method and pass them to the template.

```python
from django.views.generic import TemplateView
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.contrib import messages # For future use if success/error messages are needed
from .models import PRMaster # Ensure all models are imported if needed in view logic

class PRPrintDetailView(TemplateView):
    template_name = 'material_management/pr_print_details/detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        pr_no = self.request.GET.get('PRNo') # From Request.QueryString["PRNo"]
        master_id = self.request.GET.get('Id') # From Request.QueryString["Id"]
        
        # Assuming compid comes from session or authenticated user. For now, hardcode or fetch from a dummy location.
        # In a real app, this would likely be request.user.profile.company_id or similar.
        comp_id = self.request.session.get('compid', 1) # Fallback to 1 if not in session

        if not pr_no or not master_id:
            # Handle missing parameters, maybe redirect or show an error
            messages.error(self.request, "Missing Purchase Requisition number or ID.")
            return context # Or raise Http404

        report_parameters, pr_items = PRMaster.objects.get_pr_details_for_print(
            pr_no, master_id, comp_id
        )

        if not report_parameters and not pr_items:
            messages.warning(self.request, "No details found for the specified Purchase Requisition.")
            # Optionally redirect back or render an empty state
            return context
            
        context['report_parameters'] = report_parameters
        context['pr_items'] = pr_items
        return context

# Note: The Cancel button functionality is a simple redirect.
# This could be handled directly in the template as an <a> tag
# or by HTMX if the list view is loaded via HTMX into a parent container.
# For simplicity, we assume a direct URL redirect or a browser back button.
# If a specific redirect URL needs to be constructed dynamically,
# a dedicated view (function-based) might be preferred, but a simple <a> is often enough.
# For example, if it's always redirecting to a specific list page:
# from django.http import HttpResponseRedirect
# class PRPrintCancelView(View):
#     def get(self, request, *args, **kwargs):
#         return HttpResponseRedirect(reverse('pr_list_page')) # Replace 'pr_list_page' with actual URL name

```

#### 4.4 Templates (`material_management/templates/material_management/pr_print_details/detail.html`)

This template will display the PR header and the line items in a table, with DataTables enabled for the items list.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl bg-white shadow-lg rounded-lg print-area">
    <div class="flex justify-between items-center mb-6 border-b pb-4">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Requisition - Print Details</h2>
        <a href="{% url 'material_management:pr_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded" hx-boost="true">
            Cancel
        </a>
    </div>

    {% if report_parameters %}
    <div class="space-y-3 text-sm text-gray-700 mb-8">
        <div class="grid grid-cols-2 gap-x-4">
            <div><span class="font-semibold">Company:</span> {{ report_parameters.company }}</div>
            <div><span class="font-semibold">Address:</span> {{ report_parameters.address }}</div>
            <div><span class="font-semibold">PR No:</span> {{ report_parameters.pr_no }}</div>
            <div><span class="font-semibold">Reg. Date:</span> {{ report_parameters.reg_date }}</div>
        </div>
    </div>
    {% endif %}

    {% if pr_items %}
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Requisition Items</h3>
    <div class="overflow-x-auto">
        <table id="prDetailTable" class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <th class="py-3 px-4 border-b">S.N.</th>
                    <th class="py-3 px-4 border-b">Item Code</th>
                    <th class="py-3 px-4 border-b">Description</th>
                    <th class="py-3 px-4 border-b">UOM</th>
                    <th class="py-3 px-4 border-b">Qty</th>
                    <th class="py-3 px-4 border-b">Rate</th>
                    <th class="py-3 px-4 border-b">Discount</th>
                    <th class="py-3 px-4 border-b">Supplier</th>
                    <th class="py-3 px-4 border-b">Del. Date</th>
                    <th class="py-3 px-4 border-b">WO No</th>
                    <th class="py-3 px-4 border-b">Project Title</th>
                    <th class="py-3 px-4 border-b">Account Head</th>
                    <th class="py-3 px-4 border-b">Intender</th>
                    <th class="py-3 px-4 border-b">Remarks</th>
                </tr>
            </thead>
            <tbody>
                {% for item in pr_items %}
                <tr class="hover:bg-gray-50 text-sm text-gray-700">
                    <td class="py-3 px-4 border-b">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 border-b">{{ item.item_code }}</td>
                    <td class="py-3 px-4 border-b">{{ item.purch_desc }}</td>
                    <td class="py-3 px-4 border-b">{{ item.uom_purch }}</td>
                    <td class="py-3 px-4 border-b text-right">{{ item.qty|floatformat:3 }}</td>
                    <td class="py-3 px-4 border-b text-right">{{ item.rate|floatformat:2 }}</td>
                    <td class="py-3 px-4 border-b text-right">{{ item.discount|floatformat:2 }}</td>
                    <td class="py-3 px-4 border-b">{{ item.supplier_name }}</td>
                    <td class="py-3 px-4 border-b">{{ item.del_date }}</td>
                    <td class="py-3 px-4 border-b">{{ item.wo_no }}</td>
                    <td class="py-3 px-4 border-b">{{ item.task_project_title }}</td>
                    <td class="py-3 px-4 border-b">{{ item.ac_head }}</td>
                    <td class="py-3 px-4 border-b">{{ item.intender }}</td>
                    <td class="py-3 px-4 border-b">{{ item.remarks }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <p class="text-center text-gray-600">No PR details found for the specified requisition.</p>
    {% endif %}

    <div class="mt-6 flex justify-end">
        <button onclick="window.print()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded print-hidden">
            Print PR
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS and CSS must be included in base.html via CDN -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        if ($.fn.DataTable.isDataTable('#prDetailTable')) {
            $('#prDetailTable').DataTable().destroy();
        }
        $('#prDetailTable').DataTable({
            "paging": true,
            "ordering": true,
            "info": true,
            "searching": true,
            "responsive": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            // Disable DataTables for printing
            "dom": '<"print-hidden"lfBip><"clear">rt<"print-hidden"ip>',
            "buttons": ['copy', 'csv', 'excel', 'pdf', 'print']
        });
    });

    // Simple Alpine.js for any future UI state (e.g., loading spinners)
    document.addEventListener('alpine:init', () => {
        Alpine.data('prDetails', () => ({
            // Add state as needed, e.g., loading state
            // isLoading: false,
            // init() { /* ... */ }
        }));
    });
</script>
{% endblock %}

```

#### 4.5 URLs (`material_management/urls.py`)

This defines the URL pattern for the PR Print Details view.

```python
from django.urls import path
from .views import PRPrintDetailView
# Assuming 'pr_list' is the URL name for the PR listing page for the 'Cancel' button
# from .views import PRPrintCancelView # If a specific view is needed for cancel

app_name = 'material_management' # Namespace for this app

urlpatterns = [
    # URL for displaying PR print details, taking PRNo and Id as query parameters
    path('pr-print-details/', PRPrintDetailView.as_view(), name='pr_print_details'),
    # Example for PR listing page (for the Cancel button to navigate back)
    path('pr-list/', PRPrintDetailView.as_view(), name='pr_list'), # Placeholder, replace with actual PR list view
]

```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive unit tests for the models and integration tests for the view are essential to ensure functionality and data integrity.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date
from .models import (
    PRMaster, PRDetail, ItemMaster, UnitMaster, SupplierMaster,
    AccountHead, Employee, CompanyMaster, WorkOrderMaster
)

# Mock database connections for tests that don't need actual DB hits
# This is crucial for isolated unit tests
@patch('material_management.models.connections')
@patch('material_management.models.ItemMaster.objects')
class PRMasterManagerTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related objects first
        cls.company = CompanyMaster.objects.create(comp_id=1, company_name="Test Co", address="123 Test St")
        cls.employee = Employee.objects.create(emp_id=101, title="Mr", employee_name="John Doe")
        cls.uom = UnitMaster.objects.create(id=1, symbol="PCS")
        cls.item = ItemMaster.objects.create(id=201, item_code="ITEM001", manf_desc="Test Item Desc", uom_basic=cls.uom.id)
        cls.supplier = SupplierMaster.objects.create(supplier_id=301, supplier_name="Test Supplier")
        cls.acc_head = AccountHead.objects.create(id=401, symbol="RAW_MAT")
        cls.work_order = WorkOrderMaster.objects.create(id=501, wo_no="WO001", comp_id=1, task_project_title="Project Alpha")

        cls.pr_master = PRMaster.objects.create(
            id=1, pr_no="PR001", comp_id=cls.company.comp_id,
            sys_date=date(2023, 1, 15), wo_no="WO001", session_id=cls.employee.emp_id
        )
        cls.pr_detail = PRDetail.objects.create(
            pr_master_id=cls.pr_master.id, item_id=cls.item.id, qty=10.5, rate=100.0,
            del_date=date(2023, 2, 1), supplier_id=cls.supplier.supplier_id, ah_id=cls.acc_head.id,
            remarks="Urgent", discount=5.0
        )

    def test_get_pr_details_success(self, mock_item_manager, mock_connections):
        # Mocking the `get_company_details` and `get_item_code_part_no` helper functions
        # This is important for unit testing the manager method in isolation
        mock_item_manager.get.return_value = self.item
        
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = ("Test Co", "123 Test St")
        mock_connections.__getitem__.return_value.cursor.return_value.__enter__.return_value = mock_cursor

        report_params, pr_items = PRMaster.objects.get_pr_details_for_print(
            pr_no="PR001", master_id=1, comp_id=1
        )

        self.assertIsNotNone(report_params)
        self.assertIsInstance(pr_items, list)
        self.assertEqual(len(pr_items), 1)

        self.assertEqual(report_params['pr_no'], "PR001")
        self.assertEqual(report_params['company'], "Test Co")

        item_data = pr_items[0]
        self.assertEqual(item_data['item_code'], "ITEM001")
        self.assertEqual(item_data['qty'], 10.5)
        self.assertEqual(item_data['task_project_title'], "Project Alpha") # WO001 linked

    def test_get_pr_details_no_pr_master_found(self, mock_item_manager, mock_connections):
        report_params, pr_items = PRMaster.objects.get_pr_details_for_print(
            pr_no="PR999", master_id=999, comp_id=1
        )
        self.assertIsNone(report_params)
        self.assertEqual(pr_items, [])

    def test_get_pr_details_no_wo_no(self, mock_item_manager, mock_connections):
        # Create a PR Master without a WO_No
        pr_master_no_wo = PRMaster.objects.create(
            id=2, pr_no="PR002", comp_id=self.company.comp_id,
            sys_date=date(2023, 3, 1), wo_no=None, session_id=self.employee.emp_id
        )
        PRDetail.objects.create(
            pr_master_id=pr_master_no_wo.id, item_id=self.item.id, qty=5, rate=50.0,
            del_date=date(2023, 3, 15), supplier_id=self.supplier.supplier_id, ah_id=self.acc_head.id,
            remarks="Normal", discount=0.0
        )
        
        mock_item_manager.get.return_value = self.item
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = ("Test Co", "123 Test St")
        mock_connections.__getitem__.return_value.cursor.return_value.__enter__.return_value = mock_cursor

        report_params, pr_items = PRMaster.objects.get_pr_details_for_print(
            pr_no="PR002", master_id=2, comp_id=1
        )
        self.assertIsNotNone(report_params)
        self.assertEqual(len(pr_items), 1)
        self.assertEqual(pr_items[0]['task_project_title'], "NA")

class PRPrintDetailViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related objects for the view test
        cls.company = CompanyMaster.objects.create(comp_id=1, company_name="Test Co", address="123 Test St")
        cls.employee = Employee.objects.create(emp_id=101, title="Mr", employee_name="John Doe")
        cls.uom = UnitMaster.objects.create(id=1, symbol="PCS")
        cls.item = ItemMaster.objects.create(id=201, item_code="ITEM001", manf_desc="Test Item Desc", uom_basic=cls.uom.id)
        cls.supplier = SupplierMaster.objects.create(supplier_id=301, supplier_name="Test Supplier")
        cls.acc_head = AccountHead.objects.create(id=401, symbol="RAW_MAT")
        cls.work_order = WorkOrderMaster.objects.create(id=501, wo_no="WO001", comp_id=1, task_project_title="Project Alpha")

        cls.pr_master = PRMaster.objects.create(
            id=1, pr_no="PR001", comp_id=cls.company.comp_id,
            sys_date=date(2023, 1, 15), wo_no="WO001", session_id=cls.employee.emp_id
        )
        cls.pr_detail = PRDetail.objects.create(
            pr_master_id=cls.pr_master.id, item_id=cls.item.id, qty=10.5, rate=100.0,
            del_date=date(2023, 2, 1), supplier_id=cls.supplier.supplier_id, ah_id=cls.acc_head.id,
            remarks="Urgent", discount=5.0
        )

    def setUp(self):
        self.client = Client()
        # Set a dummy session for compid for view tests
        session = self.client.session
        session['compid'] = 1
        session.save()

    @patch('material_management.models.get_company_details', return_value={'name': 'Test Co', 'address': '123 Test St'})
    @patch('material_management.models.get_item_code_part_no', return_value='ITEM001')
    def test_pr_print_detail_view_success(self, mock_get_item_code, mock_get_company_details):
        response = self.client.get(reverse('material_management:pr_print_details'), {'PRNo': 'PR001', 'Id': 1})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_print_details/detail.html')
        self.assertIn('report_parameters', response.context)
        self.assertIn('pr_items', response.context)
        self.assertEqual(response.context['report_parameters']['pr_no'], "PR001")
        self.assertEqual(len(response.context['pr_items']), 1)
        self.assertContains(response, "PR001")
        self.assertContains(response, "Test Item Desc")
        self.assertContains(response, "10.500") # check formatting

    @patch('material_management.models.get_company_details', return_value={'name': 'Test Co', 'address': '123 Test St'})
    @patch('material_management.models.get_item_code_part_no', return_value='ITEM001')
    def test_pr_print_detail_view_no_params(self, mock_get_item_code, mock_get_company_details):
        response = self.client.get(reverse('material_management:pr_print_details'))
        self.assertEqual(response.status_code, 200) # Should still return 200 but show error message
        self.assertTemplateUsed(response, 'material_management/pr_print_details/detail.html')
        messages = list(response._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Missing Purchase Requisition number or ID.")
        self.assertIn('report_parameters', response.context) # Context will be empty for these
        self.assertIn('pr_items', response.context)
        self.assertIsNone(response.context['report_parameters']) # Should be None or empty
        self.assertEqual(len(response.context['pr_items']), 0)


    @patch('material_management.models.get_company_details', return_value={'name': 'Test Co', 'address': '123 Test St'})
    @patch('material_management.models.get_item_code_part_no', return_value='ITEM001')
    def test_pr_print_detail_view_pr_not_found(self, mock_get_item_code, mock_get_company_details):
        response = self.client.get(reverse('material_management:pr_print_details'), {'PRNo': 'PRXYZ', 'Id': 999})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_print_details/detail.html')
        messages = list(response._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "No details found for the specified Purchase Requisition.")
        self.assertIsNone(response.context['report_parameters'])
        self.assertEqual(len(response.context['pr_items']), 0)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX:** For this specific page, HTMX's primary role is to enable `hx-boost="true"` for cleaner navigation (e.g., the Cancel button will use HTMX to load the new page without a full browser refresh, if the target page is also HTMX-enabled). If this detail view were part of a larger dashboard loaded dynamically, it would be loaded via `hx-get`. The provided template already includes `hx-boost` for the `Cancel` button.
*   **Alpine.js:** Currently, Alpine.js is primarily for UI state management (like showing/hiding modals, managing dynamic inputs). For this read-only detail page, its use is minimal beyond its general setup within `base.html`. It's included in the `extra_js` block for future extensibility, allowing easy addition of client-side logic without needing jQuery or other heavy JS libraries.
*   **DataTables:** The `prDetailTable` is configured to use DataTables, providing client-side search, sort, and pagination for the list of PR items. The DataTables initialization script is placed in the `extra_js` block to ensure it runs after the DOM is loaded. It also includes `dom` options to hide pagination controls when printing.
*   **No custom JavaScript:** All dynamic interactions are managed by HTMX or handled by DataTables, adhering to the "no additional JavaScript" preference.
*   **DRY Template Inheritance:** The template `material_management/pr_print_details/detail.html` extends `core/base.html`, ensuring all common elements, CDN links (including HTMX, Alpine.js, DataTables, Tailwind CSS), and overall page structure are inherited without duplication.

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with the specific names identified from the ASP.NET code analysis.
*   **Thin Views, Fat Models:** The `PRPrintDetailView` is kept concise, primarily responsible for fetching parameters and calling the `PRMasterManager` method. The complex data fetching, joining, and processing logic resides within the `PRMasterManager.get_pr_details_for_print` method, demonstrating the fat model approach.
*   **Test Coverage:** Comprehensive unit tests are provided for the custom manager method within `PRMaster` and integration tests for the `PRPrintDetailView`, aiming for high test coverage.
*   **Adaptability:** The Django structure is highly adaptable. Should future requirements emerge (e.g., in-place editing of PR items), HTMX and Alpine.js can be readily extended to implement such dynamic functionalities without significant refactoring.
*   **External Dependencies:** Ensure that your Django `settings.py` is configured to connect to the legacy database (using `DATABASES` setting) and that `hx-request` is handled correctly if you're using `django-htmx`. For DataTables, ensure the necessary CDN links are in your `core/base.html`.