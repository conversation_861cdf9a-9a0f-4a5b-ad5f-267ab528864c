## ASP.NET to Django Conversion Script: PR Item Management

This document outlines a comprehensive plan to modernize the existing ASP.NET PR (Purchase Request) Item Management module into a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation to systematically transform the legacy codebase, focusing on modern Django patterns, a 'Fat Model, Thin View' architecture, and dynamic frontend interactions using HTMX and Alpine.js.

### Business Benefits of Django Modernization:

*   **Improved Performance & Scalability:** Django's efficient ORM and modular design allow for faster data processing and easier scaling to handle increased user loads and data volumes, directly impacting operational efficiency.
*   **Enhanced Maintainability & Code Quality:** By enforcing strict separation of concerns (business logic in models, minimal view logic), the new system will be easier to understand, debug, and extend, reducing future development costs.
*   **Modern User Experience:** The use of HTMX and Alpine.js delivers highly interactive, responsive interfaces without the complexity of traditional JavaScript frameworks, providing a smoother and faster experience for users.
*   **Reduced Development Costs & Time-to-Market:** Leveraging AI-assisted automation tools for initial code conversion, combined with Django's rapid development capabilities, significantly reduces manual coding effort and accelerates the delivery of new features.
*   **Increased Security:** Django's built-in security features (e.g., CSRF protection, SQL injection prevention via ORM) inherently reduce common web vulnerabilities present in older ASP.NET applications.
*   **Future-Proofing:** Moving to a modern, open-source framework like Django ensures the application remains compatible with evolving web standards and technologies, extending its lifespan and reducing technical debt.

### Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables to manage Purchase Request items. Below are the primary tables and their inferred columns, along with relationships. Note that ASP.NET code often uses string-based identifiers for relationships (e.g., `SupplierId` string instead of an integer foreign key), which we will normalize in Django where appropriate.

**Key Tables Identified:**

1.  **`tblMM_PR_Temp`** (Purchase Request Temporary Items)
    *   `Id` (Primary Key, inferred auto-incrementing integer)
    *   `SessionId` (String) - Represents the user session.
    *   `CompId` (Integer) - Company ID.
    *   `WONo` (String) - Work Order Number.
    *   `PId` (Integer) - Parent ID (related to BOM structure).
    *   `CId` (Integer) - Child ID (related to BOM structure).
    *   `ItemId` (Integer) - Foreign key to `tblDG_Item_Master`.
    *   `Qty` (Double) - Requested Quantity.
    *   `SupplierId` (String) - Foreign key to `tblMM_Supplier_master` (represented as string code).
    *   `Rate` (Double) - Item Rate.
    *   `AHId` (Integer) - Foreign key to `AccHead` (Account Head ID).
    *   `DelDate` (Date/String) - Delivery Date.
    *   `Remarks` (String) - Remarks.
    *   `Type` (Integer) - Item type (e.g., 0: Finish Items, 1: Components, 3: Processing Items).

2.  **`tblMM_PR_Master`** (Purchase Request Master Header)
    *   `Id` (Primary Key, Integer)
    *   `PRNo` (String) - Purchase Request Number.
    *   `SysDate` (Date) - System Date of creation.
    *   `SysTime` (Time) - System Time of creation.
    *   `SessionId` (String) - User session.
    *   `CompId` (Integer) - Company ID.
    *   `FinYearId` (Integer) - Financial Year ID.
    *   `WONo` (String) - Work Order Number.

3.  **`tblMM_PR_Details`** (Purchase Request Detail Items)
    *   `Id` (Primary Key, Integer, inferred)
    *   `MId` (Integer) - Foreign key to `tblMM_PR_Master.Id`.
    *   `PRNo` (String) - Foreign key to `tblMM_PR_Master.PRNo`.
    *   `PId` (Integer)
    *   `CId` (Integer)
    *   `ItemId` (Integer)
    *   `Qty` (Double)
    *   `SupplierId` (String)
    *   `Rate` (Double)
    *   `AHId` (Integer)
    *   `DelDate` (Date/String)
    *   `Remarks` (String)
    *   `Type` (Integer)

**Lookup Tables (Indirectly used for display/validation):**

*   `tblDG_Item_Master`: Item details (`ItemCode`, `ManfDesc`, `UOMBasic`).
*   `tblMM_Supplier_master`: Supplier details (`SupplierId`, `SupplierName`).
*   `AccHead`: Account head details (`Id`, `Symbol`).
*   `Unit_Master`: Unit of Measurement details (`Id`, `Symbol`).
*   `tblMM_Rate_Register`: Item rate information.
*   `tblMM_RateLockUnLock_Master`: Rate lock/unlock status.
*   Various `tblMP_Material_*` and `tblInv_*` tables for complex quantity calculations (`TotQty`, `PRQty`, `MINQty`, `WISQty`).

### Step 2: Identify Backend Functionality

The ASP.NET code implements the following core functionalities:

*   **Read (R):**
    *   Displaying a list of available items (from `tblMP_Material_Master`, `tblMP_Material_RawMaterial`, `tblMP_Material_Process` with complex BOM/inventory calculations) in `GridView2`. This list is filtered by Work Order Number (`WONo`), Company ID (`CompId`), Item Type (`c`), and optionally by `SupplierId`.
    *   Displaying a list of "selected" PR items (from `tblMM_PR_Temp`) in `GridView3`.
*   **Create (C):**
    *   Adding an item from the "List of Items" (`GridView2`) to the temporary PR list (`tblMM_PR_Temp`) via the "Add" button, capturing `Req. Qty`, `Supplier`, `Rate`, `A/c Head`, `Del. Date`, and `Remarks`. This involves validation and rate comparisons.
*   **Update (U):** Implicitly, if an item is re-added after deletion from temp, or if `Req. Qty` in the list view influences the add operation. The current code doesn't show an explicit "edit existing temp item" functionality on `GridView3`, only delete.
*   **Delete (D):** Removing an item from the temporary PR list (`tblMM_PR_Temp`) via the "Delete" link button in `GridView3`.
*   **Finalization:** "Generate PR" button saves all items from `tblMM_PR_Temp` into `tblMM_PR_Master` and `tblMM_PR_Details`, then clears `tblMM_PR_Temp`.
*   **Auxiliary Functions:**
    *   Supplier auto-completion.
    *   Navigation (Cancel button).
    *   Complex quantity calculations based on BOM, existing PRs, and inventory issues (MIN/WIS).
    *   Date and number formatting/validation.

### Step 3: Infer UI Components

The UI is built with ASP.NET Web Forms controls, particularly `GridView` for tabular data, `TextBox` for input, and `Button`/`LinkButton` for actions.

*   **Tabbed Interface:** `AjaxControlToolkit.TabContainer` for "List of Items" and "Selected Items." In Django, this will be implemented using HTMX for dynamic content loading, possibly with Alpine.js for tab state.
*   **Item Listing (`GridView2`):**
    *   Supplier search box (`txtSupplierId`) with auto-completion.
    *   Table displaying `SN`, `Item Code`, `Description`, `UOM`, `BOM Qty`, `PR Qty`, `WIS Qty`, `MIN Qty`, `Req. Qty` (editable), `Supplier` (editable with auto-completion), `Rate` (editable), `A/c Head` (dropdown), `Del. Date` (editable with calendar extender), `Remarks` (editable), and an "Add" button.
    *   Pagination for `GridView2`.
*   **Selected Items Listing (`GridView3`):**
    *   Table displaying `SN`, `Delete` link, `Item Code`, `Description`, `UOM`, `Req. Qty`, `Supplier`, `Rate`, `A/c Head`, `Del. Date`, `Remarks`.
    *   Pagination for `GridView3`.
*   **Action Buttons:** "Search," "Generate PR," "Cancel."
*   **Validation:** Client-side and server-side validation for input fields.

### Step 4: Generate Django Code

We will create a new Django application, `material_management`, to house the modernized PR module.

#### 4.1 Models (`material_management/models.py`)

We will define Django models corresponding to the identified database tables. The `PrTempItem` will be the central model for this specific module, with references to other tables as needed. For the complex BOM/inventory logic (`BOMRecurQty`, `getMINQty`, `getWISQty`), we will include placeholder methods that would integrate with a larger ERP system's data models.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# --- Placeholder Models for relationships (assuming these exist in the ERP) ---

class Company(models.Model):
    id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255, db_column='CompanyName', blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'CompanyMaster' # Example table name
        verbose_name_plural = 'Companies'

class FinancialYear(models.Model):
    id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_name = models.CharField(max_length=50, db_column='FinYearName')
    class Meta:
        managed = False
        db_table = 'FinancialYearMaster' # Example table name
        verbose_name_plural = 'Financial Years'

class ItemMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='items')
    item_code = models.CharField(max_length=100, db_column='ItemCode')
    manf_desc = models.CharField(max_length=255, db_column='ManfDesc', verbose_name='Description')
    uom_basic = models.ForeignKey('UnitMaster', on_delete=models.DO_NOTHING, db_column='UOMBasic') # FK to Unit_Master
    
    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class UnitMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class SupplierMaster(models.Model):
    supplier_id = models.CharField(primary_key=True, max_length=50, db_column='SupplierId')
    supplier_name = models.CharField(max_length=255, db_column='SupplierName')
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='suppliers')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class AccHead(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=100, db_column='Symbol', verbose_name='Account Head')

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol

class PrMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    pr_no = models.CharField(max_length=50, db_column='PRNo')
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(max_length=255, db_column='SessionId')
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    wo_no = models.CharField(max_length=100, db_column='WONo')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no

class PrDetail(models.Model):
    # No explicit PK in ASP.NET, Django will add one. Mapping is by MId and PRNo.
    mid = models.ForeignKey(PrMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pr_no = models.CharField(max_length=50, db_column='PRNo') # Duplicates PRNo from master, but exists in schema
    pid = models.IntegerField(db_column='PId')
    cid = models.IntegerField(db_column='CId')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    qty = models.FloatField(db_column='Qty')
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id')
    rate = models.FloatField(db_column='Rate')
    ah = models.ForeignKey(AccHead, on_delete=models.DO_NOTHING, db_column='AHId')
    del_date = models.DateField(db_column='DelDate')
    remarks = models.CharField(max_length=500, db_column='Remarks', blank=True, null=True)
    item_type = models.IntegerField(db_column='Type', choices=[(0, 'Finish Item'), (1, 'Component'), (3, 'Processing Item')])

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR {self.pr_no} - {self.item.item_code}"

# --- Main Model for the PR_Items module ---

class PrTempItem(models.Model):
    # Assuming Id is an auto-incrementing PK as in ASP.NET
    session_id = models.CharField(max_length=255, db_column='SessionId')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    wo_no = models.CharField(max_length=100, db_column='WONo')
    parent_id = models.IntegerField(db_column='PId') # Corresponds to PId
    child_id = models.IntegerField(db_column='CId') # Corresponds to CId
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    quantity = models.FloatField(db_column='Qty', verbose_name='Required Quantity')
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id')
    rate = models.FloatField(db_column='Rate')
    account_head = models.ForeignKey(AccHead, on_delete=models.DO_NOTHING, db_column='AHId', verbose_name='A/c Head')
    delivery_date = models.DateField(db_column='DelDate', verbose_name='Delivery Date')
    remarks = models.CharField(max_length=500, db_column='Remarks', blank=True, null=True)
    item_type = models.IntegerField(db_column='Type', choices=[(0, 'Finish Item'), (1, 'Component'), (3, 'Processing Item')], verbose_name='Item Type')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_PR_Temp'
        verbose_name = 'Temporary PR Item'
        verbose_name_plural = 'Temporary PR Items'
        unique_together = ('session_id', 'company', 'wo_no', 'item', 'parent_id', 'child_id') # Prevents duplicate adds

    def __str__(self):
        return f"{self.item.item_code} (WO: {self.wo_no})"

    # --- Business Logic Methods (Fat Model) ---

    @staticmethod
    def get_max_pr_no(company_id, fin_year_id):
        """Retrieves the next PR number."""
        try:
            latest_pr_no = PrMaster.objects.filter(
                compid_id=company_id, finyearid_id=fin_year_id
            ).order_by('-pr_no').first() # Assumes PRNo is sortable as string or needs conversion
            if latest_pr_no:
                # Assuming PRNo is like '0001', '0002'
                next_num = int(latest_pr_no.pr_no) + 1
                return f"{next_num:04d}"
            return "0001"
        except Exception as e:
            # Log error or handle gracefully
            return "0001" # Default to 0001 if error

    @classmethod
    def generate_pr_from_temp(cls, session_id, company_id, wo_no, user, fin_year_id):
        """
        Transfers items from temporary PR table to master and detail tables.
        This is the core "Generate PR" functionality.
        """
        temp_items = cls.objects.filter(session_id=session_id, company_id=company_id, wo_no=wo_no)
        if not temp_items.exists():
            raise ValueError("No temporary PR items found to generate.")

        next_pr_no = cls.get_max_pr_no(company_id, fin_year_id)

        # Create PR Master entry
        pr_master = PrMaster.objects.create(
            pr_no=next_pr_no,
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id=session_id, # Or use user.username / user.id
            compid_id=company_id,
            finyearid_id=fin_year_id,
            wo_no=wo_no
        )

        # Create PR Details entries
        for temp_item in temp_items:
            PrDetail.objects.create(
                mid=pr_master,
                pr_no=next_pr_no, # Redundant, but matching ASP.NET schema
                pid=temp_item.parent_id,
                cid=temp_item.child_id,
                item=temp_item.item,
                qty=temp_item.quantity,
                supplier=temp_item.supplier,
                rate=temp_item.rate,
                ah=temp_item.account_head,
                del_date=temp_item.delivery_date,
                remarks=temp_item.remarks,
                item_type=temp_item.item_type
            )
        
        # Clear temporary items
        temp_items.delete()
        return pr_master

    @classmethod
    def get_filtered_pr_list_items(cls, company_id, wo_no, item_type_code, supplier_id_filter=None):
        """
        Simulates the complex 'fillGrid' logic from ASP.NET code-behind.
        This method should retrieve items eligible for PR based on BOM,
        existing PRs, MIN/WIS quantities, and supplier filter.
        
        NOTE: The actual complex SQL queries from ASP.NET (involving tblMP_Material_Master,
              tblInv_MaterialIssue_Details, etc., and BOMRecurQty) are highly specific
              to the ERP's data model. For this migration, we provide a simplified
              structure and placeholders. In a real scenario, this would be
              implemented with Django's ORM or custom SQL queries/stored procedures
              wrapped in a service layer.
        """
        # Placeholder for complex BOM/inventory logic
        # In a real ERP, this would involve:
        # 1. Querying tblMP_Material_Master, tblMP_Material_RawMaterial, tblMP_Material_Process
        #    based on wo_no and item_type_code.
        # 2. Calling a BOM calculation function (like fun.BOMRecurQty)
        # 3. Querying tblMM_PR_Details for already requested quantities (PRQty)
        # 4. Querying tblInv_MaterialIssue/WIS tables for MINQty/WISQty
        # 5. Calculating ReqQty = TotQty - (PRQty + MINQty + WISQty)
        # 6. Filtering by supplier and ensuring ReqQty > 0 and not already in PrTempItem.

        # For demonstration, we'll return a simplified list of potential items.
        # This part requires significant domain knowledge and mapping from the ASP.NET
        # SQL logic to Django ORM or raw SQL.
        
        eligible_items = []
        
        # Simulate fetching some items that could be added to PR
        # Replace this with actual database queries based on your ERP structure
        items_from_erp_logic = ItemMaster.objects.filter(
            compid_id=company_id
        ).exclude(
            # Exclude items already in temporary PR for this WO and session
            id__in=cls.objects.filter(
                company_id=company_id, wo_no=wo_no
            ).values_list('item_id', flat=True)
        )[:10] # Limiting for example

        for item in items_from_erp_logic:
            # Simulate quantities and rates
            tot_qty = PrTempItem.get_bom_recur_qty(wo_no, item.id, 1, 1, company_id, 1) # Placeholder
            pr_qty = PrTempItem.get_pr_qty(company_id, wo_no, item.id, 1, 1) # Placeholder
            min_qty = PrTempItem.get_min_qty(company_id, wo_no, item.id) # Placeholder
            wis_qty = PrTempItem.get_wis_qty(company_id, wo_no, item.id) # Placeholder
            
            # This is the core calculation from ASP.NET: TotQty - (PRQty + MINQty + WISQty)
            req_qty_calculated = tot_qty - (pr_qty + min_qty + wis_qty)

            if req_qty_calculated <= 0:
                continue

            # Simulate supplier and rate lookup
            supplier_obj = SupplierMaster.objects.filter(compid_id=company_id).order_by('?').first() # Random supplier
            item_rate = PrTempItem.get_item_min_rate(item.id, company_id) # Placeholder for Rate Register lookup
            
            # Apply supplier filter if provided
            if supplier_id_filter and (not supplier_obj or supplier_obj.supplier_id != supplier_id_filter):
                continue

            eligible_items.append({
                'item_id': item.id,
                'item_code': item.item_code,
                'description': item.manf_desc,
                'uom': item.uom_basic.symbol if item.uom_basic else 'N/A',
                'bom_qty': tot_qty, # Total quantity from BOM
                'pr_qty': pr_qty, # Already PR'd quantity
                'wis_qty': wis_qty, # Issued from WIS
                'min_qty': min_qty, # Issued from MIN
                'req_qty_calculated': round(req_qty_calculated, 3), # Initial suggested required quantity
                'supplier_obj': supplier_obj,
                'rate': round(item_rate, 2),
                'delivery_date': timezone.localdate() + timezone.timedelta(days=7), # Default 7 days from now
                'parent_id': 0, # Placeholder
                'child_id': 0,  # Placeholder
                'item_type': item_type_code,
                'remarks': ''
            })
        
        return eligible_items
        
    @staticmethod
    def get_bom_recur_qty(wo_no, p_id, c_id, assembly_type, company_id, fin_year_id):
        """
        Placeholder for fun.BOMRecurQty. This would typically involve recursive
        queries into BOM structures (e.g., tblMP_Material_Master, tblMP_Material_RawMaterial).
        """
        return 100.0 # Simulate a quantity

    @staticmethod
    def get_pr_qty(company_id, wo_no, item_id, p_id, c_id):
        """
        Placeholder for fetching already PR'd quantity for an item/WO.
        """
        # This would query tblMM_PR_Master and tblMM_PR_Details
        return 20.0 # Simulate quantity already PR'd

    @staticmethod
    def get_min_qty(company_id, wo_no, item_id):
        """
        Placeholder for fetching Material Issue Note (MIN) quantity.
        """
        # This would query tblInv_MaterialIssue_Details, tblInv_MaterialIssue_Master, etc.
        return 5.0 # Simulate quantity issued via MIN

    @staticmethod
    def get_wis_qty(company_id, wo_no, item_id):
        """
        Placeholder for fetching Work In Progress Issue Slip (WIS) quantity.
        """
        # This would query tblInv_WIS_Master, tblInv_WIS_Details
        return 15.0 # Simulate quantity issued via WIS

    @staticmethod
    def get_item_min_rate(item_id, company_id):
        """
        Placeholder for fetching min rate from tblMM_Rate_Register after discount.
        """
        # In a real scenario, this would query tblMM_Rate_Register
        return 50.0 # Simulate a rate

    @staticmethod
    def is_rate_locked(item_id, company_id):
        """
        Placeholder for checking if item rate is locked.
        """
        # This would query tblMM_RateLockUnLock_Master
        return False # Simulate no lock

    @staticmethod
    def get_supplier_details_by_name_or_code(query, company_id):
        """
        Helper to find a supplier by name or ID, similar to fun.getCode/getSupplierName.
        """
        try:
            # Try exact match by supplier_id first
            supplier = SupplierMaster.objects.filter(supplier_id=query, compid_id=company_id).first()
            if not supplier:
                # Then try exact match by supplier_name
                supplier = SupplierMaster.objects.filter(supplier_name=query, compid_id=company_id).first()
            if not supplier:
                # If still not found, try partial match on name (for autocomplete)
                supplier = SupplierMaster.objects.filter(supplier_name__icontains=query, compid_id=company_id).first()
            return supplier
        except Exception:
            return None

```

#### 4.2 Forms (`material_management/forms.py`)

A Django ModelForm will be used for the `PrTempItem`. This form will handle the input fields for `Req. Qty`, `Supplier`, `Rate`, `A/c Head`, `Del. Date`, and `Remarks`, with appropriate styling and validation.

```python
from django import forms
from .models import PrTempItem, AccHead, SupplierMaster
from django.core.exceptions import ValidationError
from django.utils import timezone
import re

class PrTempItemForm(forms.ModelForm):
    # Hidden fields to pass context
    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    parent_id = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
    child_id = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
    item_type = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    wo_no = forms.CharField(widget=forms.HiddenInput(), required=True)
    company_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)

    # Fields that need user input
    quantity = forms.FloatField(
        label='Req. Qty',
        widget=forms.NumberInput(attrs={'class': 'box3 w-20', 'step': '0.001'}),
        min_value=0.001 # Quantity must be positive
    )
    supplier_display = forms.CharField(
        label='Supplier',
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Search supplier...',
            'hx-post': '/material-management/pr-temp-items/supplier-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'name': 'supplier_display' # Custom name for non-model field
        }),
        required=True
    )
    # The actual supplier_id that will be saved to the model
    supplier = forms.CharField(widget=forms.HiddenInput(), required=True)

    rate = forms.FloatField(
        label='Rate',
        widget=forms.NumberInput(attrs={'class': 'box3 w-20', 'step': '0.01'}),
        min_value=0.0 # Rate can be 0 or positive
    )
    delivery_date = forms.DateField(
        label='Del. Date',
        widget=forms.DateInput(attrs={'class': 'box3 w-32', 'type': 'date'}),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'] # Allow both formats
    )
    remarks = forms.CharField(
        label='Remarks',
        widget=forms.TextInput(attrs={'class': 'box3 w-full'}),
        required=False
    )
    account_head = forms.ModelChoiceField(
        queryset=AccHead.objects.all(),
        label='A/c Head',
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        empty_label="Select Account Head",
        required=True
    )

    # Initial quantity calculated from backend for validation
    max_req_qty_allowed = forms.FloatField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = PrTempItem
        fields = [
            'item_id', 'parent_id', 'child_id', 'item_type', 'wo_no', 'company_id',
            'quantity', 'supplier', 'rate', 'account_head', 'delivery_date', 'remarks'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate supplier_display if editing an existing instance
        if self.instance.pk:
            self.fields['supplier_display'].initial = self.instance.supplier.supplier_name
            self.fields['supplier'].initial = self.instance.supplier.supplier_id
        
        # Adjust queryset for account_head dynamically if needed (e.g., by company)
        if 'company_id' in self.initial:
            # Assuming AccHead is NOT company-specific based on ASP.NET SQLDataSource1
            # If it were, you would filter here:
            # self.fields['account_head'].queryset = AccHead.objects.filter(company_id=self.initial['company_id'])
            pass

    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        max_req_qty_allowed = self.cleaned_data.get('max_req_qty_allowed')

        if max_req_qty_allowed is not None and quantity > max_req_qty_allowed:
            # This validation mirrors 'Entered required qty exceed the limit!'
            raise ValidationError(f"Entered required quantity ({quantity}) exceeds the allowed limit ({max_req_qty_allowed:.3f}).")
        return quantity

    def clean_supplier_display(self):
        # This field is for display; the actual supplier ID is in 'supplier' hidden field
        supplier_name = self.cleaned_data['supplier_display']
        company_id = self.cleaned_data.get('company_id')
        if not company_id:
             raise ValidationError("Company ID is missing for supplier validation.")
        
        # Ensure the supplier name matches a valid supplier in the database
        supplier_obj = SupplierMaster.objects.filter(supplier_name=supplier_name, compid_id=company_id).first()
        if not supplier_obj:
            raise ValidationError("Invalid supplier selected.")
        self.cleaned_data['supplier'] = supplier_obj.supplier_id # Set the actual supplier_id
        return supplier_name

    def clean_rate(self):
        rate = self.cleaned_data['rate']
        item_id = self.cleaned_data.get('item_id')
        company_id = self.cleaned_data.get('company_id')

        if item_id and company_id:
            min_system_rate = PrTempItem.get_item_min_rate(item_id, company_id) # Call model static method
            is_locked = PrTempItem.is_rate_locked(item_id, company_id) # Call model static method

            if min_system_rate > 0 and rate > min_system_rate and not is_locked:
                # Mirrors 'Entered rate is not acceptable!' if it exceeds min_system_rate and not locked
                raise ValidationError("Entered rate is not acceptable (higher than system's min rate and not unlocked).")
            # If min_system_rate is 0 or no rate registered, any rate might be acceptable based on ASP.NET logic
            # This is complex in ASP.NET, often allowing higher rates if "LockUnlock" is 1 (unlocked)
        
        return rate
    
    def clean_delivery_date(self):
        delivery_date = self.cleaned_data['delivery_date']
        # The ASP.NET regex validation is for format, Django DateField handles this.
        # We can add business logic for future dates, etc. if needed.
        if delivery_date < timezone.localdate():
            raise ValidationError("Delivery date cannot be in the past.")
        return delivery_date

```

#### 4.3 Views (`material_management/views.py`)

We'll use Class-Based Views for clear structure. The `fillGrid` and `Fill_PR_Temp_Grid` logic will be handled by dedicated list views or partial view methods that prepare data for DataTables.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import render, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction

from .models import PrTempItem, ItemMaster, SupplierMaster, AccHead, Company, FinancialYear
from .forms import PrTempItemForm

# Helper to get current company and financial year (simulating ASP.NET Session)
# In a real app, this would come from UserProfile or request.session after login.
def get_user_context_data(request):
    # Dummy data for demonstration. Replace with actual user/session logic.
    company_id = request.session.get('compid', 1) # Default to 1
    fin_year_id = request.session.get('finyear', 1) # Default to 1
    session_id = request.session.session_key # Django's session ID
    wo_no = request.GET.get('wono', 'WO-001') # Get from query string
    item_type_code = int(request.GET.get('c', 1)) # Get from query string (1, 2, or 3)

    # Ensure dummy Company/FinancialYear exist for FKs if not migrating actual data
    Company.objects.get_or_create(id=company_id, defaults={'name': f'Company {company_id}'})
    FinancialYear.objects.get_or_create(id=fin_year_id, defaults={'year_name': f'FY {fin_year_id}'})

    return {
        'company_id': company_id,
        'fin_year_id': fin_year_id,
        'session_id': session_id,
        'wo_no': wo_no,
        'item_type_code': item_type_code,
        'item_type_label': {1: 'Finish Items', 2: 'Components', 3: 'Processing Items'}.get(item_type_code, 'Unknown Type')
    }

class PrItemsDashboardView(TemplateView):
    """
    Main dashboard view for PR items, showing both lists in tabs.
    Corresponds to PR_Items.aspx
    """
    template_name = 'material_management/pr_items/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_context = get_user_context_data(self.request)
        context.update(user_context)
        return context

class PrListItemsPartialView(View):
    """
    Renders the 'List of Items' table (GridView2 equivalent) for HTMX requests.
    This handles the fillGrid logic.
    """
    def get(self, request, *args, **kwargs):
        user_context = get_user_context_data(request)
        company_id = user_context['company_id']
        wo_no = user_context['wo_no']
        item_type_code = user_context['item_type_code']
        supplier_search_id = request.GET.get('supplier_id_filter') # Filter from search box

        eligible_pr_items = PrTempItem.get_filtered_pr_list_items(
            company_id=company_id,
            wo_no=wo_no,
            item_type_code=item_type_code,
            supplier_id_filter=supplier_search_id
        )
        
        context = {
            'eligible_pr_items': eligible_pr_items,
            'wo_no': wo_no,
            'item_type_label': user_context['item_type_label'],
            'company_id': company_id,
            'session_id': user_context['session_id'],
            'item_type_code': item_type_code,
        }
        return render(request, 'material_management/pr_items/_list_items_table.html', context)

class PrSelectedItemsPartialView(View):
    """
    Renders the 'Selected Items' table (GridView3 equivalent) for HTMX requests.
    This handles the Fill_PR_Temp_Grid logic.
    """
    def get(self, request, *args, **kwargs):
        user_context = get_user_context_data(request)
        company_id = user_context['company_id']
        wo_no = user_context['wo_no']
        session_id = user_context['session_id']

        selected_pr_items = PrTempItem.objects.filter(
            session_id=session_id,
            company_id=company_id,
            wo_no=wo_no
        ).order_by('id') # Ensure consistent order

        context = {
            'selected_pr_items': selected_pr_items,
        }
        return render(request, 'material_management/pr_items/_selected_items_table.html', context)

class PrTempItemAddView(View):
    """
    Handles adding a temporary PR item. Used for HTMX form submission.
    Corresponds to GridView2_RowCommand (AddMe).
    """
    def get(self, request, *args, **kwargs):
        # This GET request is likely for loading the form into a modal
        # We need to pass initial data for hidden fields from the "List of Items" row
        user_context = get_user_context_data(request)
        
        initial_data = {
            'item_id': request.GET.get('item_id'),
            'parent_id': request.GET.get('parent_id'),
            'child_id': request.GET.get('child_id'),
            'item_type': request.GET.get('item_type'),
            'wo_no': user_context['wo_no'],
            'company_id': user_context['company_id'],
            'quantity': request.GET.get('req_qty_calculated'), # Pre-fill with suggested qty
            'max_req_qty_allowed': request.GET.get('req_qty_calculated'), # For validation
            'rate': request.GET.get('rate'),
            'delivery_date': request.GET.get('delivery_date') # Pre-fill delivery date
        }
        # Pre-fill supplier_display and supplier_id
        initial_supplier_id = request.GET.get('supplier_id')
        if initial_supplier_id:
            supplier_obj = SupplierMaster.objects.filter(supplier_id=initial_supplier_id, compid_id=user_context['company_id']).first()
            if supplier_obj:
                initial_data['supplier_display'] = supplier_obj.supplier_name
                initial_data['supplier'] = supplier_obj.supplier_id

        form = PrTempItemForm(initial=initial_data)
        context = {'form': form, 'form_action_url': request.path}
        return render(request, 'material_management/pr_items/_pr_temp_item_form.html', context)

    def post(self, request, *args, **kwargs):
        user_context = get_user_context_data(request)
        
        # Merge form data with hidden context data for saving
        post_data = request.POST.copy()
        post_data['session_id'] = user_context['session_id']
        post_data['company'] = user_context['company_id'] # Map to FK
        post_data['wo_no'] = user_context['wo_no']
        post_data['item'] = post_data.get('item_id') # Map to FK
        post_data['parent_id'] = post_data.get('parent_id', 0)
        post_data['child_id'] = post_data.get('child_id', 0)
        post_data['item_type'] = post_data.get('item_type')
        post_data['account_head'] = post_data.get('account_head') # Map to FK
        post_data['delivery_date'] = post_data.get('delivery_date')

        form = PrTempItemForm(post_data) # Use post_data for form

        if form.is_valid():
            try:
                # Ensure it's not a duplicate entry for this WO, session, item, PId, CId
                # This is handled by unique_together in the model's Meta.
                form.save()
                messages.success(request, 'Item added to temporary PR list successfully.')
                return HttpResponse(
                    status=204, # No content, tells HTMX to do nothing more than trigger
                    headers={
                        'HX-Trigger': 'refreshPrSelectedItemsList, refreshPrListItemsList, closeModal'
                    }
                )
            except Exception as e:
                # Handle unique_together constraint violation or other save errors
                messages.error(request, f"Error saving item: {e}")
                # Re-render form with errors if it's an HTMX request
                return render(request, 'material_management/pr_items/_pr_temp_item_form.html', {'form': form}, status=400)
        else:
            # Form is not valid, re-render the form with errors
            return render(request, 'material_management/pr_items/_pr_temp_item_form.html', {'form': form}, status=400)

class PrTempItemDeleteView(View):
    """
    Handles deleting a temporary PR item. Used for HTMX form submission.
    Corresponds to GridView3_RowCommand (AddToMaster - actual delete).
    """
    def get(self, request, pk, *args, **kwargs):
        # Render confirmation modal for HTMX
        item = PrTempItem.objects.get(pk=pk)
        context = {'object': item}
        return render(request, 'material_management/pr_items/_pr_temp_item_confirm_delete.html', context)

    def post(self, request, pk, *args, **kwargs):
        try:
            item = PrTempItem.objects.get(pk=pk)
            user_context = get_user_context_data(request)
            # Add security check: ensure the item belongs to the current session/company/WO
            if (item.session_id != user_context['session_id'] or
                item.company_id != user_context['company_id'] or
                item.wo_no != user_context['wo_no']):
                messages.error(request, 'Unauthorized deletion attempt.')
                return HttpResponse(status=403) # Forbidden
            
            item.delete()
            messages.success(request, 'Item removed from temporary PR list.')
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPrSelectedItemsList, refreshPrListItemsList, closeModal'
                }
            )
        except PrTempItem.DoesNotExist:
            messages.error(request, 'Item not found.')
            return HttpResponse(status=404)
        except Exception as e:
            messages.error(request, f"Error deleting item: {e}")
            return HttpResponse(status=500)

class GeneratePrView(View):
    """
    Handles generating the final PR from temporary items.
    Corresponds to btnGenerate_Click.
    """
    def post(self, request, *args, **kwargs):
        user_context = get_user_context_data(request)
        try:
            with transaction.atomic():
                pr_master = PrTempItem.generate_pr_from_temp(
                    session_id=user_context['session_id'],
                    company_id=user_context['company_id'],
                    wo_no=user_context['wo_no'],
                    user=request.user, # Pass actual user object if authenticated
                    fin_year_id=user_context['fin_year_id']
                )
            messages.success(request, f'Purchase Request {pr_master.pr_no} generated successfully!')
            # Redirect to PR_New.aspx equivalent, which would be a list of PR Masters
            return HttpResponse(status=204, headers={'HX-Redirect': reverse_lazy('pr_master_list')})
            # Or, if staying on the same page, trigger refreshes
            # return HttpResponse(status=204, headers={'HX-Trigger': 'refreshPrSelectedItemsList, refreshPrListItemsList'})

        except ValueError as e:
            messages.error(request, str(e))
            return HttpResponse(status=400) # Bad Request
        except Exception as e:
            messages.error(request, f"Error generating PR: {e}")
            return HttpResponse(status=500) # Internal Server Error

class SupplierAutocompleteView(View):
    """
    Provides supplier suggestions for the autocomplete text box.
    Corresponds to GetCompletionList WebMethod.
    """
    def post(self, request, *args, **kwargs):
        prefix_text = request.POST.get('supplier_display', '')
        company_id = get_user_context_data(request)['company_id']
        
        if not prefix_text:
            return HttpResponse('') # Return empty if no text

        # Filter suppliers by name starting with prefix_text
        suppliers = SupplierMaster.objects.filter(
            compid_id=company_id,
            supplier_name__istartswith=prefix_text
        ).values_list('supplier_name', 'supplier_id')[:10] # Limit results

        # Format for display: "SupplierName [SupplierId]"
        suggestions = [f"{name} [{id_val}]" for name, id_val in suppliers]

        # Render a simple list for HTMX to inject
        html_suggestions = ''.join([f'<div class="p-2 hover:bg-gray-200 cursor-pointer" hx-on:click="this.closest(\'div\').previousElementSibling.value = \'{name}\'; this.closest(\'div\').previousElementSibling.nextElementSibling.value = \'{id_val}\'; this.closest(\'div\').innerHTML = \'\'">{suggestion}</div>' for name, id_val, suggestion in [(s.split(' [')[0], s.split(' [')[1][:-1], s) for s in suggestions]])
        
        # Or, a simpler list:
        # html_suggestions = ''.join([f'<div class="p-2 hover:bg-gray-200 cursor-pointer" onclick="document.getElementById(\'id_supplier_display\').value=\'{s}\'; document.getElementById(\'id_supplier_hidden\').value=\'{s.split(" [")[1][:-1]}\'; document.getElementById(\'supplier-suggestions\').innerHTML=\'\';">{s}</div>' for s in suggestions])
        
        return HttpResponse(f'<div class="border border-gray-300 bg-white shadow-lg mt-1 absolute z-10 w-full" id="supplier-suggestions-list">{html_suggestions}</div>')

    def get(self, request, *args, **kwargs):
        # Also support GET for initial load or if hx-get is used
        return self.post(request, *args, **kwargs)

```

#### 4.4 Templates (`material_management/templates/material_management/pr_items/`)

We'll define the main dashboard, and partial templates for the dynamic content (tables, forms, modals).

**`dashboard.html`** (Main page, replaces `PR_Items.aspx` layout):

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-4 rounded-t-lg mb-4 flex justify-between items-center">
        <h1 class="text-xl font-bold">PR - New &nbsp;&nbsp;&nbsp;&nbsp; WO No: <span id="lblwo">{{ wo_no }}</span>&nbsp;&nbsp;&nbsp;&nbsp;<span id="lbltype">{{ item_type_label }}</span></h1>
    </div>

    <div x-data="{ activeTab: 'list_items' }" class="bg-white shadow rounded-lg p-6">
        <!-- Tabs Header -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button 
                    @click="activeTab = 'list_items'" 
                    :class="activeTab === 'list_items' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ease-in-out"
                    hx-get="{% url 'pr_list_items_table' %}"
                    hx-target="#list-items-content"
                    hx-trigger="click once"
                    hx-swap="innerHTML">
                    List of Items
                </button>
                <button 
                    @click="activeTab = 'selected_items'" 
                    :class="activeTab === 'selected_items' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ease-in-out"
                    hx-get="{% url 'pr_selected_items_table' %}"
                    hx-target="#selected-items-content"
                    hx-trigger="click once"
                    hx-swap="innerHTML">
                    Selected Items
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="mt-4">
            <div x-show="activeTab === 'list_items'" id="list-items-content"
                 hx-trigger="load, refreshPrListItemsList from:body"
                 hx-get="{% url 'pr_list_items_table' %}"
                 hx-swap="innerHTML"
                 hx-indicator="#list-items-loading">
                <!-- List of Items table will be loaded here via HTMX -->
                <div id="list-items-loading" class="htmx-indicator text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading List of Items...</p>
                </div>
            </div>

            <div x-show="activeTab === 'selected_items'" id="selected-items-content"
                 hx-trigger="load, refreshPrSelectedItemsList from:body"
                 hx-get="{% url 'pr_selected_items_table' %}"
                 hx-swap="innerHTML"
                 hx-indicator="#selected-items-loading">
                <!-- Selected Items table will be loaded here via HTMX -->
                <div id="selected-items-loading" class="htmx-indicator text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Selected Items...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Global Modal for Forms -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden"
         x-data="{ showModal: false }" x-show="showModal"
         @close-modal.window="showModal = false"
         @open-modal.window="showModal = true"
         @click.self="showModal = false"
         _="on closeModal remove .is-active from me then remove .hidden from me.querySelector('#modalContent') then trigger refreshPrSelectedItemsList, refreshPrListItemsList">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto"
             _="on htmx:afterSwap remove .hidden from me">
            <!-- Content loaded via HTMX -->
        </div>
    </div>

    <div class="text-right mt-6">
        <button 
            type="button" 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'generate_pr' %}"
            hx-confirm="Are you sure you want to Generate the Purchase Request?"
            hx-indicator="#generate-loading">
            Generate PR
        </button>
        <span id="generate-loading" class="htmx-indicator ml-2">
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </span>
        <a href="{% url 'pr_master_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded ml-2">
            Cancel
        </a>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script>
    // Alpine.js setup (if not already done by base.html)
    document.addEventListener('alpine:init', () => {
        Alpine.data('prItemsDashboard', () => ({
            // If any specific Alpine state needed for the dashboard
        }));
    });

    // Custom event listener for closing modal
    document.body.addEventListener('closeModal', function() {
        document.getElementById('modal').classList.remove('is-active'); // For compatibility with older styles
        document.getElementById('modal').classList.add('hidden'); // Ensure it's hidden
        document.getElementById('modalContent').innerHTML = ''; // Clear content
    });

    // Custom event listener for opening modal
    document.body.addEventListener('openModal', function() {
        document.getElementById('modal').classList.add('is-active');
        document.getElementById('modal').classList.remove('hidden');
    });

</script>
{% endblock %}
```

**`_list_items_table.html`** (Partial for 'List of Items' tab, replaces `GridView2` content):

```html
<div class="mb-4">
    <div class="flex items-center space-x-2">
        <label for="id_search_supplier_display" class="font-bold text-sm">Supplier:</label>
        <div class="relative flex-grow">
            <input type="text" id="id_search_supplier_display" name="supplier_search_display"
                   class="box3 w-80 px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                   placeholder="Search supplier..."
                   hx-get="{% url 'pr_list_items_table' %}"
                   hx-trigger="keyup changed delay:500ms from:#id_search_supplier_display"
                   hx-target="#list-items-table-container"
                   hx-indicator="#list-items-loading"
                   hx-include="[name='supplier_search_id']"
                   autocomplete="off"
                   _="on keyup debounce 500ms set my #supplier-search-suggestions innerHTML to ''">
            <input type="hidden" id="id_search_supplier_id" name="supplier_id_filter" value="">
            <div id="supplier-search-suggestions" class="absolute bg-white border border-gray-300 w-full z-10 shadow-lg mt-1 max-h-60 overflow-y-auto"></div>
        </div>
        <button id="btnSearch" type="button"
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'pr_list_items_table' %}"
                hx-target="#list-items-table-container"
                hx-indicator="#list-items-loading"
                hx-include="[name='supplier_search_id']"
                hx-swap="innerHTML">
            Search
        </button>
    </div>
</div>

<div id="list-items-table-container">
    <table id="listItemsTable" class="min-w-full divide-y divide-gray-200 border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PR Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">WIS Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">MIN Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req. Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Del. Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if eligible_pr_items %}
                {% for item in eligible_pr_items %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ item.item_code }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">{{ item.description }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ item.uom }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.bom_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.pr_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.wis_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.min_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">
                        <input type="number" step="0.001" name="req_qty_{{ forloop.counter }}" 
                               id="req_qty_{{ forloop.counter }}"
                               value="{{ item.req_qty_calculated|floatformat:3 }}" class="box3 w-20 text-right">
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">
                        <div class="relative">
                            <input type="text" name="supplier_display_{{ forloop.counter }}" 
                                   id="supplier_display_{{ forloop.counter }}" 
                                   value="{{ item.supplier_obj.supplier_name|default:'' }}"
                                   class="box3 w-full" placeholder="Type to search..."
                                   hx-post="{% url 'supplier_autocomplete' %}"
                                   hx-trigger="keyup changed delay:500ms"
                                   hx-target="#supplier_suggestions_{{ forloop.counter }}"
                                   hx-swap="innerHTML"
                                   autocomplete="off"
                                   _="on keyup debounce 500ms set my nextElementSibling.nextElementSibling.innerHTML to ''">
                            <input type="hidden" name="supplier_id_{{ forloop.counter }}" 
                                   id="supplier_id_{{ forloop.counter }}" 
                                   value="{{ item.supplier_obj.supplier_id|default:'' }}">
                            <div id="supplier_suggestions_{{ forloop.counter }}" class="absolute bg-white border border-gray-300 w-full z-10 shadow-lg mt-1 max-h-60 overflow-y-auto"></div>
                        </div>
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">
                        <input type="number" step="0.01" name="rate_{{ forloop.counter }}" 
                               id="rate_{{ forloop.counter }}" 
                               value="{{ item.rate|floatformat:2 }}" class="box3 w-20 text-right">
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">
                        <input type="date" name="delivery_date_{{ forloop.counter }}" 
                               id="delivery_date_{{ forloop.counter }}" 
                               value="{{ item.delivery_date|date:'Y-m-d' }}" class="box3 w-32">
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">
                        <input type="text" name="remarks_{{ forloop.counter }}" 
                               id="remarks_{{ forloop.counter }}" 
                               value="{{ item.remarks|default:'' }}" class="box3 w-32">
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">
                        <button 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                            hx-get="{% url 'pr_temp_item_add' %}?item_id={{ item.item_id }}&parent_id={{ item.parent_id }}&child_id={{ item.child_id }}&item_type={{ item.item_type }}&req_qty_calculated={{ item.req_qty_calculated|floatformat:3 }}&rate={{ item.rate|floatformat:2 }}&delivery_date={{ item.delivery_date|date:'Y-m-d' }}&supplier_id={{ item.supplier_obj.supplier_id|default:'' }}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click send openModal to body then wait for event openModal then log 'Modal opened, swapping form content' then wait for end of htmx:afterSwap on #modalContent"
                            hx-swap="innerHTML">
                            Add
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="14" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Initialize DataTables on the main table
        $('#listItemsTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });

        // Delegate event for supplier autocomplete suggestions
        // This is necessary because HTMX re-renders parts of the DOM
        $('#list-items-table-container').on('click', '.supplier-suggestion-item', function() {
            var inputId = $(this).data('input-id'); // e.g., 'supplier_display_1'
            var hiddenId = $(this).data('hidden-id'); // e.g., 'supplier_id_1'
            var supplierName = $(this).text().split(' [')[0];
            var supplierId = $(this).data('supplier-id');

            $('#' + inputId).val(supplierName);
            $('#' + hiddenId).val(supplierId);
            $(this).parent().empty(); // Clear suggestions
        });
    });
</script>

```
**`_selected_items_table.html`** (Partial for 'Selected Items' tab, replaces `GridView3` content):

```html
<table id="selectedItemsTable" class="min-w-full divide-y divide-gray-200 border border-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req. Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">A/c Head</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Del. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if selected_pr_items %}
            {% for item in selected_pr_items %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'pr_temp_item_delete' item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click send openModal to body">
                        Delete
                    </button>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ item.item.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left">{{ item.item.manf_desc }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ item.item.uom_basic.symbol }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.quantity|floatformat:3 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left">{{ item.supplier.supplier_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ item.rate|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ item.account_head.symbol }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ item.delivery_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left">{{ item.remarks }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="11" class="py-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#selectedItemsTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "responsive": true
    });
});
</script>
```

**`_pr_temp_item_form.html`** (Partial for 'Add Item' modal):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Add Item to PR</h3>
    <form hx-post="{{ form_action_url }}" hx-swap="outerHTML" hx-target="#modalContent" hx-indicator="#form-loading">
        {% csrf_token %}
        
        <!-- Hidden fields for context -->
        {{ form.item_id }}
        {{ form.parent_id }}
        {{ form.child_id }}
        {{ form.item_type }}
        {{ form.wo_no }}
        {{ form.company_id }}
        {{ form.max_req_qty_allowed }}

        <div class="space-y-4">
            <!-- Display non-editable item details -->
            <div class="flex items-center space-x-4 text-sm text-gray-600">
                <p><strong>Item Code:</strong> {{ form.initial.item_code }}</p>
                <p><strong>Description:</strong> {{ form.initial.description }}</p>
                <p><strong>UOM:</strong> {{ form.initial.uom }}</p>
                <p><strong>BOM Qty:</strong> {{ form.initial.bom_qty|floatformat:3 }}</p>
                <p><strong>PR Qty:</strong> {{ form.initial.pr_qty|floatformat:3 }}</p>
                <p><strong>MIN Qty:</strong> {{ form.initial.min_qty|floatformat:3 }}</p>
                <p><strong>WIS Qty:</strong> {{ form.initial.wis_qty|floatformat:3 }}</p>
            </div>

            <!-- Form fields -->
            <div>
                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.quantity.label }}
                </label>
                {{ form.quantity }}
                {% if form.quantity.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>
                {% endif %}
            </div>

            <div class="relative">
                <label for="{{ form.supplier_display.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.supplier_display.label }}
                </label>
                {{ form.supplier_display }}
                {{ form.supplier }} {# Hidden field for actual supplier ID #}
                <div id="supplier-suggestions" class="absolute bg-white border border-gray-300 w-full z-10 shadow-lg mt-1 max-h-60 overflow-y-auto"></div>
                {% if form.supplier_display.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.supplier_display.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.rate.label }}
                </label>
                {{ form.rate }}
                {% if form.rate.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.account_head.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.account_head.label }}
                </label>
                {{ form.account_head }}
                {% if form.account_head.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.account_head.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.delivery_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.delivery_date.label }}
                </label>
                {{ form.delivery_date }}
                {% if form.delivery_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.delivery_date.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remarks.label }}
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click send closeModal to body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Add Item
            </button>
            <span id="form-loading" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
<script>
    // Specific logic for supplier autocomplete in the modal form
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            const supplierDisplayInput = document.getElementById('id_supplier_display');
            const supplierHiddenInput = document.getElementById('id_supplier');
            const supplierSuggestionsDiv = document.getElementById('supplier-suggestions');

            if (supplierDisplayInput && supplierHiddenInput && supplierSuggestionsDiv) {
                supplierSuggestionsDiv.addEventListener('click', function(e) {
                    const target = e.target.closest('.supplier-suggestion-item');
                    if (target) {
                        supplierDisplayInput.value = target.dataset.supplierName;
                        supplierHiddenInput.value = target.dataset.supplierId;
                        supplierSuggestionsDiv.innerHTML = '';
                    }
                });
            }
        }
    });
</script>

```

**`_pr_temp_item_confirm_delete.html`** (Partial for Delete confirmation modal):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to remove item "{{ object.item.item_code }}" from the temporary PR list?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click send closeModal to body">
            Cancel
        </button>
        <button 
            hx-post="{% url 'pr_temp_item_delete' object.pk %}"
            hx-swap="none"
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-indicator="#delete-loading">
            Delete
        </button>
        <span id="delete-loading" class="htmx-indicator ml-2">
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </span>
    </div>
</div>
```

**`_supplier_autocomplete_suggestions.html`** (for `SupplierAutocompleteView`):

```html
{% if suggestions %}
<div class="border border-gray-300 bg-white shadow-lg mt-1 absolute z-10 w-full" id="supplier-suggestions-list">
    {% for name, id_val in suggestions %}
        <div class="p-2 hover:bg-gray-200 cursor-pointer supplier-suggestion-item" 
             data-input-id="{{ input_id }}" 
             data-hidden-id="{{ hidden_id }}" 
             data-supplier-name="{{ name }}"
             data-supplier-id="{{ id_val }}">
            {{ name }} [{{ id_val }}]
        </div>
    {% endfor %}
</div>
{% endif %}
```
Note: The `_supplier_autocomplete_suggestions.html` needs to be rendered by `SupplierAutocompleteView` to pass `input_id` and `hidden_id`. I've updated `SupplierAutocompleteView` to render raw HTML for simplicity and direct HTMX usage, as the target is often just the suggestions div. For more complex use cases, a partial template approach is better.

Corrected `SupplierAutocompleteView` `post` method:
```python
class SupplierAutocompleteView(View):
    # ... (other methods)

    def post(self, request, *args, **kwargs):
        prefix_text = request.POST.get('value', '') # HTMX sends value by default for form inputs
        # Also need to figure out which specific input is being autocompleted
        # This requires passing the input's ID from the HTMX request
        input_name = request.POST.get('name', '') # e.g. supplier_display_1
        
        user_context = get_user_context_data(request)
        company_id = user_context['company_id']
        
        if not prefix_text:
            return HttpResponse('<div id="supplier-suggestions-list"></div>') # Clear suggestions

        suppliers = SupplierMaster.objects.filter(
            compid_id=company_id,
            supplier_name__istartswith=prefix_text
        ).values('supplier_name', 'supplier_id')[:10] # Limit results

        html_suggestions = []
        for supplier in suppliers:
            name = supplier['supplier_name']
            id_val = supplier['supplier_id']
            # Pass original input names to correctly set values in the corresponding fields
            html_suggestions.append(f"""
                <div class="p-2 hover:bg-gray-200 cursor-pointer supplier-suggestion-item" 
                     data-input-id="{input_name}" 
                     data-hidden-id="{input_name.replace('supplier_display_', 'supplier_id_')}" 
                     data-supplier-name="{name}"
                     data-supplier-id="{id_val}">
                    {name} [{id_val}]
                </div>
            """)
        
        return HttpResponse(f'<div class="border border-gray-300 bg-white shadow-lg mt-1 absolute z-10 w-full" id="supplier-suggestions-list">{"".join(html_suggestions)}</div>')
```
This new `SupplierAutocompleteView` creates `div` elements with `data-*` attributes that can be used by the jQuery/Alpine.js snippet in the table templates to populate the correct input fields. The `input_name` would come from the `hx-post` on the input element like `hx-post="{% url 'supplier_autocomplete' %}" hx-vals='js:{name: this.name}'`. This means the `_list_items_table.html` needs to be updated with `hx-vals` and the `_pr_temp_item_form.html` will have the same logic.

#### 4.5 URLs (`material_management/urls.py`)

Define the URL patterns for the views, including HTMX-specific endpoints.

```python
from django.urls import path
from .views import (
    PrItemsDashboardView,
    PrListItemsPartialView,
    PrSelectedItemsPartialView,
    PrTempItemAddView,
    PrTempItemDeleteView,
    GeneratePrView,
    SupplierAutocompleteView,
)

urlpatterns = [
    # Main dashboard view
    path('pr-items/', PrItemsDashboardView.as_view(), name='pr_items_dashboard'),

    # HTMX partials for table content
    path('pr-temp-items/list-table/', PrListItemsPartialView.as_view(), name='pr_list_items_table'),
    path('pr-temp-items/selected-table/', PrSelectedItemsPartialView.as_view(), name='pr_selected_items_table'),

    # CRUD operations for temporary PR items (HTMX-driven)
    path('pr-temp-items/add/', PrTempItemAddView.as_view(), name='pr_temp_item_add'),
    path('pr-temp-items/delete/<int:pk>/', PrTempItemDeleteView.as_view(), name='pr_temp_item_delete'),

    # Action to finalize PR
    path('pr-items/generate/', GeneratePrView.as_view(), name='generate_pr'),
    
    # Autocomplete endpoint for suppliers
    path('pr-temp-items/supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # Placeholder for redirect after "Generate PR" (e.g., a PR Master list page)
    # This would be an actual view in your ERP application
    path('pr-master-list/', PrItemsDashboardView.as_view(), name='pr_master_list'), # Redirect to dashboard for demo
]

```
Remember to include these URLs in your project's main `urls.py`:
`path('material-management/', include('material_management.urls')),`

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests are crucial. We'll include unit tests for model methods and integration tests for all views, focusing on the fat model/thin view principle and HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.conf import settings
from django.db import transaction
from unittest.mock import patch, MagicMock
from datetime import date

from .models import PrTempItem, ItemMaster, UnitMaster, SupplierMaster, AccHead, Company, FinancialYear, PrMaster, PrDetail

# Helper to add session to request in tests
def add_session_to_request(request, session_data=None):
    middleware = SessionMiddleware(lambda req: HttpResponse()) # Dummy response
    middleware.process_request(request)
    if session_data:
        for key, value in session_data.items():
            request.session[key] = value
    request.session.save()

class PrTempItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2024-2025')
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item1 = ItemMaster.objects.create(id=1, compid=cls.company, item_code='ITEM001', manf_desc='Test Item 1', uom_basic=cls.unit)
        cls.item2 = ItemMaster.objects.create(id=2, compid=cls.company, item_code='ITEM002', manf_desc='Test Item 2', uom_basic=cls.unit)
        cls.supplier1 = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Test Supplier 1', compid=cls.company)
        cls.supplier2 = SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='Test Supplier 2', compid=cls.company)
        cls.ac_head = AccHead.objects.create(id=1, symbol='TEST_AC_HEAD')
        
        # Create a test PrTempItem
        cls.pr_temp_item1 = PrTempItem.objects.create(
            session_id='test_session_id',
            company=cls.company,
            wo_no='WO-TEST-001',
            parent_id=10,
            child_id=20,
            item=cls.item1,
            quantity=10.5,
            supplier=cls.supplier1,
            rate=100.0,
            account_head=cls.ac_head,
            delivery_date=date(2024, 12, 31),
            remarks='Test remarks for item 1',
            item_type=1 # Component
        )
        cls.pr_temp_item2 = PrTempItem.objects.create(
            session_id='test_session_id',
            company=cls.company,
            wo_no='WO-TEST-001',
            parent_id=11,
            child_id=21,
            item=cls.item2,
            quantity=5.0,
            supplier=cls.supplier2,
            rate=200.0,
            account_head=cls.ac_head,
            delivery_date=date(2024, 12, 30),
            remarks='Test remarks for item 2',
            item_type=0 # Finish Item
        )

    def test_pr_temp_item_creation(self):
        self.assertEqual(PrTempItem.objects.count(), 2)
        item = PrTempItem.objects.get(pk=self.pr_temp_item1.pk)
        self.assertEqual(item.quantity, 10.5)
        self.assertEqual(item.item.item_code, 'ITEM001')
        self.assertEqual(item.supplier.supplier_name, 'Test Supplier 1')

    def test_unique_together_constraint(self):
        # Attempt to create a duplicate item (same session, company, WO, item, PId, CId)
        with self.assertRaises(Exception): # Will raise IntegrityError or similar depending on DB
            PrTempItem.objects.create(
                session_id='test_session_id',
                company=self.company,
                wo_no='WO-TEST-001',
                parent_id=10,
                child_id=20,
                item=self.item1,
                quantity=1.0,
                supplier=self.supplier1,
                rate=1.0,
                account_head=self.ac_head,
                delivery_date=date(2024, 1, 1),
                remarks='Duplicate',
                item_type=1
            )
        self.assertEqual(PrTempItem.objects.count(), 2) # Should not have increased

    def test_get_max_pr_no(self):
        # Test initial PR number
        self.assertEqual(PrTempItem.get_max_pr_no(self.company.id, self.fin_year.id), "0001")
        # Create a PR Master record
        PrMaster.objects.create(
            pr_no='0001', sys_date=date.today(), sys_time='10:00:00',
            session_id='dummy', compid=self.company, finyearid=self.fin_year, wo_no='WO-100'
        )
        self.assertEqual(PrTempItem.get_max_pr_no(self.company.id, self.fin_year.id), "0002")

    @patch('material_management.models.PrTempItem.get_bom_recur_qty', return_value=100)
    @patch('material_management.models.PrTempItem.get_pr_qty', return_value=20)
    @patch('material_management.models.PrTempItem.get_min_qty', return_value=5)
    @patch('material_management.models.PrTempItem.get_wis_qty', return_value=15)
    @patch('material_management.models.PrTempItem.get_item_min_rate', return_value=50.0)
    def test_get_filtered_pr_list_items(self, mock_get_item_min_rate, mock_get_wis_qty, mock_get_min_qty, mock_get_pr_qty, mock_get_bom_recur_qty):
        # Clear existing temp items to simulate fresh list
        PrTempItem.objects.all().delete()
        
        # Test with no supplier filter
        items = PrTempItem.get_filtered_pr_list_items(self.company.id, 'WO-001', 1)
        self.assertGreater(len(items), 0)
        first_item = items[0]
        self.assertIn('item_code', first_item)
        self.assertIn('req_qty_calculated', first_item)
        self.assertEqual(first_item['req_qty_calculated'], 100 - (20 + 5 + 15)) # Based on mocked values

        # Test with supplier filter (assuming the random supplier matches)
        items_filtered = PrTempItem.get_filtered_pr_list_items(self.company.id, 'WO-001', 1, supplier_id_filter='SUP001')
        self.assertLessEqual(len(items_filtered), len(items))

    def test_generate_pr_from_temp(self):
        self.assertEqual(PrMaster.objects.count(), 0)
        self.assertEqual(PrDetail.objects.count(), 0)
        self.assertEqual(PrTempItem.objects.count(), 2)

        # Mock request.user for generate_pr_from_temp
        mock_user = MagicMock()
        mock_user.username = 'testuser'
        mock_user.id = 99

        pr_master = PrTempItem.generate_pr_from_temp(
            session_id='test_session_id',
            company_id=self.company.id,
            wo_no='WO-TEST-001',
            user=mock_user,
            fin_year_id=self.fin_year.id
        )

        self.assertIsNotNone(pr_master)
        self.assertEqual(PrMaster.objects.count(), 1)
        self.assertEqual(PrDetail.objects.count(), 2) # Two temp items moved to details
        self.assertEqual(PrTempItem.objects.count(), 0) # Temp items should be cleared

        detail1 = PrDetail.objects.get(item=self.item1)
        self.assertEqual(detail1.qty, 10.5)
        self.assertEqual(detail1.supplier, self.supplier1)
        self.assertEqual(detail1.mid, pr_master)
        
    def test_get_supplier_details_by_name_or_code(self):
        supplier = PrTempItem.get_supplier_details_by_name_or_code('SUP001', self.company.id)
        self.assertEqual(supplier, self.supplier1)
        
        supplier_by_name = PrTempItem.get_supplier_details_by_name_or_code('Test Supplier 1', self.company.id)
        self.assertEqual(supplier_by_name, self.supplier1)
        
        supplier_not_found = PrTempItem.get_supplier_details_by_name_or_code('NONEXISTENT', self.company.id)
        self.assertIsNone(supplier_not_found)


class PrItemsViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Setup minimal required data for FKs like in model tests
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2024-2025')
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item1 = ItemMaster.objects.create(id=101, compid=cls.company, item_code='VIEWITEM1', manf_desc='View Test Item 1', uom_basic=cls.unit)
        cls.item2 = ItemMaster.objects.create(id=102, compid=cls.company, item_code='VIEWITEM2', manf_desc='View Test Item 2', uom_basic=cls.unit)
        cls.supplier1 = SupplierMaster.objects.create(supplier_id='V_SUP001', supplier_name='View Supplier 1', compid=cls.company)
        cls.supplier2 = SupplierMaster.objects.create(supplier_id='V_SUP002', supplier_name='View Supplier 2', compid=cls.company)
        cls.ac_head = AccHead.objects.create(id=100, symbol='VIEW_AC_HEAD')
        
        # Set up a temp item
        cls.existing_temp_item = PrTempItem.objects.create(
            session_id='existing_session',
            company=cls.company,
            wo_no='WO-VIEW-001',
            parent_id=1,
            child_id=1,
            item=cls.item1,
            quantity=10.0,
            supplier=cls.supplier1,
            rate=50.0,
            account_head=cls.ac_head,
            delivery_date=date(2025, 1, 1),
            remarks='Existing temp item',
            item_type=1
        )
    
    def setUp(self):
        # Initialize session for each test
        self.client = Client()
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year.id
        session.save()
        self.session_key = self.client.session.session_key
        self.base_url_params = f"?wono=WO-VIEW-001&c=1"

    def test_pr_items_dashboard_view(self):
        response = self.client.get(reverse('pr_items_dashboard') + self.base_url_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_items/dashboard.html')
        self.assertContains(response, 'WO-VIEW-001')
        self.assertContains(response, 'Finish Items') # Default 'c' is 1

    @patch('material_management.models.PrTempItem.get_filtered_pr_list_items')
    def test_pr_list_items_partial_view(self, mock_get_filtered_pr_list_items):
        mock_get_filtered_pr_list_items.return_value = [{
            'item_id': self.item2.id, 'item_code': self.item2.item_code,
            'description': self.item2.manf_desc, 'uom': self.unit.symbol,
            'bom_qty': 100.0, 'pr_qty': 20.0, 'wis_qty': 5.0, 'min_qty': 15.0,
            'req_qty_calculated': 60.0, 'supplier_obj': self.supplier2,
            'rate': 200.0, 'delivery_date': date(2024, 12, 25),
            'parent_id': 2, 'child_id': 2, 'item_type': 1, 'remarks': ''
        }]
        
        response = self.client.get(reverse('pr_list_items_table') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_items/_list_items_table.html')
        self.assertContains(response, 'VIEWITEM2')
        self.assertContains(response, '60.000') # Req. Qty
        
        mock_get_filtered_pr_list_items.assert_called_once_with(
            company_id=self.company.id,
            wo_no='WO-VIEW-001',
            item_type_code=1,
            supplier_id_filter=None
        )

    def test_pr_selected_items_partial_view(self):
        # Create a temp item for the current session/WO to be displayed
        PrTempItem.objects.create(
            session_id=self.session_key,
            company=self.company,
            wo_no='WO-VIEW-001',
            parent_id=3,
            child_id=3,
            item=self.item2,
            quantity=5.5,
            supplier=self.supplier2,
            rate=150.0,
            account_head=self.ac_head,
            delivery_date=date(2025, 2, 1),
            remarks='Newly added temp item',
            item_type=1
        )
        
        response = self.client.get(reverse('pr_selected_items_table') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_items/_selected_items_table.html')
        self.assertContains(response, 'VIEWITEM2')
        self.assertContains(response, '5.500')

    def test_pr_temp_item_add_view_get(self):
        # Simulating getting form for modal
        add_url_params = f"{self.base_url_params}&item_id={self.item1.id}&parent_id=1&child_id=1&item_type=1&req_qty_calculated=75.0&rate=100.0&delivery_date=2025-01-01&supplier_id={self.supplier1.supplier_id}"
        response = self.client.get(reverse('pr_temp_item_add') + add_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_items/_pr_temp_item_form.html')
        self.assertContains(response, 'value="75.0"') # Pre-filled quantity
        self.assertContains(response, 'value="100.0"') # Pre-filled rate

    def test_pr_temp_item_add_view_post_success(self):
        initial_count = PrTempItem.objects.count()
        post_data = {
            'item_id': self.item2.id,
            'parent_id': 2,
            'child_id': 2,
            'item_type': 1,
            'wo_no': 'WO-VIEW-001',
            'company_id': self.company.id,
            'quantity': 25.0,
            'supplier_display': self.supplier2.supplier_name,
            'supplier': self.supplier2.supplier_id, # Should be correctly set by clean_supplier_display
            'rate': 210.0,
            'account_head': self.ac_head.id,
            'delivery_date': '2025-03-01',
            'remarks': 'New item via form',
            'max_req_qty_allowed': 100.0 # From initial calculation
        }
        response = self.client.post(reverse('pr_temp_item_add') + self.base_url_params, data=post_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(PrTempItem.objects.count(), initial_count + 1)
        self.assertTrue(PrTempItem.objects.filter(item=self.item2, wo_no='WO-VIEW-001', quantity=25.0).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPrSelectedItemsList', response.headers['HX-Trigger'])
        self.assertIn('refreshPrListItemsList', response.headers['HX-Trigger'])


    def test_pr_temp_item_add_view_post_invalid_quantity(self):
        initial_count = PrTempItem.objects.count()
        post_data = {
            'item_id': self.item2.id,
            'parent_id': 2,
            'child_id': 2,
            'item_type': 1,
            'wo_no': 'WO-VIEW-001',
            'company_id': self.company.id,
            'quantity': 150.0, # Exceeds max_req_qty_allowed
            'supplier_display': self.supplier2.supplier_name,
            'supplier': self.supplier2.supplier_id,
            'rate': 210.0,
            'account_head': self.ac_head.id,
            'delivery_date': '2025-03-01',
            'remarks': 'New item via form',
            'max_req_qty_allowed': 100.0 # From initial calculation
        }
        response = self.client.post(reverse('pr_temp_item_add') + self.base_url_params, data=post_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 400) # Bad request due to validation error
        self.assertEqual(PrTempItem.objects.count(), initial_count)
        self.assertContains(response, 'Entered required quantity (150.0) exceeds the allowed limit (100.000).')

    def test_pr_temp_item_delete_view_get(self):
        response = self.client.get(reverse('pr_temp_item_delete', args=[self.existing_temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_items/_pr_temp_item_confirm_delete.html')
        self.assertContains(response, f'remove item "{self.item1.item_code}"')

    def test_pr_temp_item_delete_view_post_success(self):
        initial_count = PrTempItem.objects.count()
        response = self.client.post(reverse('pr_temp_item_delete', args=[self.existing_temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(PrTempItem.objects.count(), initial_count - 1)
        self.assertFalse(PrTempItem.objects.filter(pk=self.existing_temp_item.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPrSelectedItemsList', response.headers['HX-Trigger'])
        self.assertIn('refreshPrListItemsList', response.headers['HX-Trigger'])

    def test_pr_temp_item_delete_view_post_not_found(self):
        response = self.client.post(reverse('pr_temp_item_delete', args=[9999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404)

    @patch('material_management.models.PrTempItem.generate_pr_from_temp')
    def test_generate_pr_view_post_success(self, mock_generate_pr_from_temp):
        mock_pr_master = MagicMock(spec=PrMaster)
        mock_pr_master.pr_no = '0001'
        mock_generate_pr_from_temp.return_value = mock_pr_master

        # Ensure there are temp items to generate
        PrTempItem.objects.create(
            session_id=self.session_key, company=self.company, wo_no='WO-VIEW-001',
            parent_id=1, child_id=1, item=self.item1, quantity=1.0, supplier=self.supplier1,
            rate=1.0, account_head=self.ac_head, delivery_date=date(2025, 1, 1), item_type=1
        )

        response = self.client.post(reverse('generate_pr') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('pr_master_list'))
        mock_generate_pr_from_temp.assert_called_once()
    
    def test_generate_pr_view_post_no_items(self):
        # No temp items exist
        PrTempItem.objects.all().delete()
        response = self.client.post(reverse('generate_pr') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertContains(response, 'No temporary PR items found to generate.')

    def test_supplier_autocomplete_view(self):
        response = self.client.post(reverse('supplier_autocomplete'), data={'value': 'View'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'View Supplier 1 [V_SUP001]')
        self.assertContains(response, 'View Supplier 2 [V_SUP002]')
        
        response_specific = self.client.post(reverse('supplier_autocomplete'), data={'value': 'View Supplier 1'}, HTTP_HX_REQUEST='true')
        self.assertContains(response_specific, 'View Supplier 1 [V_SUP001]')
        self.assertNotContains(response_specific, 'View Supplier 2 [V_SUP002]')

```

### Step 5: HTMX and Alpine.js Integration

The provided Django code templates already incorporate HTMX for dynamic interactions and Alpine.js for UI state management (e.g., tabs, modals).

*   **HTMX for Tabs:** The `dashboard.html` uses `hx-get` and `hx-target` on tab buttons to load content from `pr_list_items_table` and `pr_selected_items_table` URLs into the respective content divs. `hx-trigger="click once"` ensures content is loaded only on the first click, then `refreshPrListItemsList` and `refreshPrSelectedItemsList` events triggered from the server keep them updated.
*   **HTMX for CRUD (Add/Delete):**
    *   **Add:** The "Add" button in `_list_items_table.html` makes an `hx-get` request to `pr_temp_item_add` to fetch the form into the `#modalContent`. The form itself (`_pr_temp_item_form.html`) then makes an `hx-post` to the same URL. Upon successful submission, the view returns `status=204` with `HX-Trigger` headers (`refreshPrSelectedItemsList`, `refreshPrListItemsList`, `closeModal`) to update both grids and close the modal.
    *   **Delete:** The "Delete" button in `_selected_items_table.html` makes an `hx-get` to `pr_temp_item_delete` to load the confirmation modal. The confirmation form then `hx-post`s to the same URL. Success triggers `HX-Trigger` to refresh tables and close the modal.
*   **HTMX for Search:** The "Search" button and supplier `input` in `_list_items_table.html` use `hx-get` to refresh the `listItemsTable` partial with filtered data.
*   **HTMX for Generate PR:** The "Generate PR" button uses `hx-post` to `generate_pr`. On success, it triggers `HX-Redirect` to the PR master list.
*   **DataTables:** Both `_list_items_table.html` and `_selected_items_table.html` include `$(document).ready(function() { $('#tableName').DataTable({}); });` to initialize DataTables for client-side search, sort, and pagination. This script is run whenever the partial is loaded by HTMX.
*   **Alpine.js for Modals:** The `dashboard.html` includes an Alpine.js component (`x-data="{ showModal: false }"`) to manage the visibility of the global modal (`#modal`). HTMX interactions trigger custom events (`openModal`, `closeModal`) which Alpine.js listens for to control the modal's display. `_=` attributes are used for client-side interactivity like closing the modal by clicking outside it.
*   **Supplier Autocomplete:** HTMX `hx-post` to `supplier_autocomplete` sends the input value. The view returns an HTML string with `div` elements representing suggestions. These `div`s have `hx-on:click` or `onclick` attributes (depending on complexity) to set the input value and hide the suggestions.

### Final Notes:

*   **Placeholders:** The database details (`managed = False`, `db_table='[TABLE_NAME]'`) and relationships are based on inferred schema. In a real migration, these would be confirmed with the actual database schema. The placeholder methods for `BOMRecurQty`, `get_pr_qty`, `get_min_qty`, `get_wis_qty`, `get_item_min_rate`, `is_rate_locked` represent complex ERP logic that would need to be fully implemented based on the source system's database structure and business rules.
*   **Security:** Ensure proper Django authentication and authorization middleware is active in your project settings to secure views and prevent unauthorized access, especially for `CompId`, `FinYearId`, and `SessionId` which are currently derived from `request.session` or query params.
*   **User Context:** The `get_user_context_data` helper function is a simplified way to represent session variables from ASP.NET. In a production Django application, `company_id`, `fin_year_id`, etc., would typically be stored in a user profile model or an enterprise context manager, associated with the authenticated `request.user`.
*   **Error Handling:** The provided code includes basic error handling and messages. In a production environment, robust logging and more user-friendly error pages should be implemented.
*   **CSS/Tailwind:** The classes used (`box3`, `redbox`, `bg-blue-500` etc.) are Tailwind CSS classes or direct mappings from ASP.NET. Ensure Tailwind CSS is configured in your Django project.
*   **Test Coverage:** The provided tests serve as a strong starting point. Continue to expand them to cover all edge cases, validations, and complex business logic scenarios for comprehensive test coverage.
*   **Automation:** This plan provides the target Django code structure. The actual "AI-assisted automation" would involve tools parsing the ASP.NET code, identifying patterns, mapping them to these Django components, and generating the initial Python/HTML files, reducing manual effort significantly. The output provided here is what a high-quality automation tool would aim to produce.