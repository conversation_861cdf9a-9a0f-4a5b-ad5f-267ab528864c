## ASP.NET to Django Conversion Script: PR Edit Details

This document outlines the comprehensive plan to migrate the provided ASP.NET `PR_Edit_Details.aspx` and its C# code-behind to a modern Django-based application. The focus is on leveraging Django's "Fat Model, Thin View" architecture, HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience, while minimizing manual coding through automation-driven strategies.

### Business Value of Django Modernization:

Migrating this ASP.NET application to Django offers significant business advantages:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms, which are costly to maintain and hard to scale, to a modern, actively supported framework.
2.  **Improved Performance & Scalability:** Django's robust architecture and efficient ORM, combined with HTMX for partial page updates, results in faster load times and a more responsive user interface. This directly translates to improved user productivity and a better experience for your team.
3.  **Enhanced Maintainability & Development Efficiency:** Django's clear structure, Python's readability, and its emphasis on conventions (DRY) make the codebase easier to understand, debug, and extend. This reduces development time for new features and lowers long-term maintenance costs.
4.  **Cost Savings:** Python's extensive open-source libraries and vibrant community mean less need for proprietary tools and more readily available talent, leading to potential cost reductions in development and infrastructure.
5.  **Future-Proofing:** Adopting a modern, popular framework like Django ensures your application remains relevant and adaptable to future business needs and technological advancements.
6.  **Better User Experience:** The combination of HTMX for dynamic interactions and DataTables for powerful grid features provides a smooth, intuitive experience akin to a Single Page Application (SPA) without the complexity of traditional JavaScript frameworks.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Explanation:**
The ASP.NET code primarily interacts with `tblMM_PR_Details` for editing PR item specifics and `tblMM_PR_Master` for the PR header. It also pulls extensive lookup data from `tblDG_Item_Master`, `Unit_Master`, `tblMM_Supplier_master`, `AccHead`, and performs complex calculations involving inventory and rate history tables.
For this modernization, we will focus on `PrDetail` as the core editable entity, modeling its direct columns and adding methods/properties to fetch related lookup data, thereby embodying the "fat model" principle. We'll also need placeholder models for the other tables to enable ORM queries, with `managed = False` to ensure they map to the existing database.

*   **Primary Tables Involved:**
    *   `tblMM_PR_Details` (main entity for editing PR details)
    *   `tblMM_PR_Master` (header for PR, linked by `MId` and `PRNo`)
*   **Associated Lookup/Reference Tables (for data fetching and validation):**
    *   `tblDG_Item_Master` (for ItemCode, ManfDesc, UOMBasic)
    *   `Unit_Master` (for UOM Symbol)
    *   `tblMM_Supplier_master` (for Supplier Name, SupplierId)
    *   `AccHead` (for Account Head Symbol)
    *   `tblMM_PO_Details`, `tblMM_PO_Master` (for `disableCheck` logic)
    *   `tblInv_MaterialIssue_Details`, `tblInv_MaterialIssue_Master`, `tblInv_MaterialRequisition_Details`, `tblInv_MaterialRequisition_Master` (for MINQty calculation)
    *   `tblMM_Rate_Register`, `tblMM_RateLockUnLock_Master` (for rate validation)

*   **Key Columns from `tblMM_PR_Details` (mapped to `PrDetail` model):**
    *   `Id` (int, Primary Key inferred) -> `id`
    *   `PRNo` (string) -> `pr_no`
    *   `MId` (int) -> `m_id`
    *   `ItemId` (int) -> `item_id`
    *   `Qty` (double) -> `qty`
    *   `Rate` (double) -> `rate`
    *   `Discount` (double) -> `discount`
    *   `SupplierId` (int) -> `supplier_id`
    *   `DelDate` (date) -> `del_date`
    *   `AHId` (int) -> `ah_id`
    *   `Remarks` (string) -> `remarks`
    *   `Type` (string) -> `type`
    *   `PId` (int) -> `p_id`
    *   `CId` (int) -> `c_id`

*   **Additional data inferred/calculated for display:**
    *   `ItemCode` (from `tblDG_Item_Master`) -> `item_code_display` property
    *   `PurchDesc` (from `tblDG_Item_Master`) -> `description` property
    *   `Symbol` (UOM, from `Unit_Master`) -> `uom_symbol` property
    *   `TotQty` (calculated via `fun.AllComponentBOMQty`) -> `total_bom_qty` property (needs implementation)
    *   `Supplier` (Name + ID from `tblMM_Supplier_master`) -> `supplier_name_display` property
    *   `AccHead` (Symbol from `AccHead`) -> `acc_head_symbol` property
    *   `MINQty` (calculated from inventory tables) -> `min_qty` property (needs implementation)
    *   `has_po_link` (conditional checkbox visibility) -> `has_po_link` property

### Step 2: Identify Backend Functionality

**Explanation:**
The ASP.NET page functions as a bulk editor for Purchase Requisition (PR) line items.

1.  **Data Retrieval (Read):** The `fillGrid` method is responsible for querying `tblMM_PR_Details` along with multiple joins to related tables (`tblDG_Item_Master`, `Unit_Master`, `tblMM_Supplier_master`, `AccHead`, inventory tables) to prepare a comprehensive data table for display in the `GridView`. In Django, this complex data aggregation will be encapsulated within the `PrDetail` model's properties and methods.
2.  **Bulk Update (Update):** The `Btnupdate_Click` method iterates through selected rows in the `GridView`, performs a series of validations (including complex business rules involving rate comparisons with `tblMM_Rate_Register` and `tblMM_RateLockUnLock_Master`), and then updates the `SupplierId`, `Rate`, `Discount`, `DelDate`, and `Remarks` for chosen `PrDetail` items in `tblMM_PR_Details`. In Django, this logic will primarily reside in a `PrDetail` model method (`validate_and_update`) and the view's POST handler.
3.  **Conditional Display Logic:** The `disableCheck` method determines whether a `PrDetail` item has already been linked to a Purchase Order (PO) and conditionally hides/shows a checkbox and a "PO" label. This will be a property on the `PrDetail` model.
4.  **Client-Side Autocomplete:** The `GetCompletionList` web method provides autocomplete suggestions for the supplier name. In Django, this will be handled by a dedicated HTMX endpoint.
5.  **Navigation:** Redirections for 'Cancel' and 'Rate History Report'. These will be handled by Django `HttpResponseRedirect` or `reverse_lazy` with appropriate URLs.

### Step 3: Infer UI Components

**Explanation:**
The `.aspx` file primarily uses an `asp:GridView` to present data and allow inline editing of specific fields.

*   **Main Display:** A `GridView` (`GridView2`) presents PR item details, including Item Code, Description, UOM, Quantities (Total, PR, MIN), Supplier, Rate, Discount, A/c Head, Delivery Date, and Remarks. This will be replaced by Django Template + DataTables + HTMX.
*   **Editable Fields (within Grid Rows):**
    *   `TxtSupplier`: Textbox for supplier name, with an `AutoCompleteExtender`.
    *   `TxtRate`: Textbox for item rate.
    *   `TxtDiscount`: Textbox for item discount.
    *   `TxtDelDate`: Textbox for delivery date, with a `CalendarExtender`.
    *   `TxtRmk`: Textbox for remarks.
    *   `CheckBox1`: A checkbox to select items for update.
*   **Display-Only Fields (within Grid Rows):** Labels (`lblit`, `lbldesc`, `lbluom`, `lbltotqty`, `lblprqty`, `lblminqty`, `lblAhId`, `LblId`, `lblitemid`, `lbltype`, `lblpid`, `lblcid`, `lblEdit`).
*   **Action Buttons:** `Btnupdate` (for applying changes), `btnCancel` (for discarding changes and navigating back), and `ImageButton1` (to view rate history).

---

### Step 4: Generate Django Code

We will create a Django app named `material_management`.

#### 4.1 Models (`material_management/models.py`)

**Explanation:**
We'll define `PrDetail` as the main model mapped to `tblMM_PR_Details`. To ensure a "fat model" approach, `PrDetail` will include methods and properties to encapsulate the complex data retrieval and business logic found in the ASP.NET `fillGrid` and `Btnupdate_Click` methods. This involves defining helper models for all related tables used in the ASP.NET code, setting `managed = False` for them.
*Note: The `fun.AllComponentBOMQty` logic and `fun.GetItemCode_PartNo` are highly specific to the original ERP's database schema and logic. Placeholder implementations are provided, but a full migration would require detailed re-engineering or integration with the ERP's existing data services for these complex calculations.*

```python
from django.db import models
from django.db.models import F, Case, When, Value, DecimalField
from django.db.models.functions import Coalesce
from django.utils import timezone
from datetime import datetime
from django.core.validators import RegexValidator
from django.forms import ValidationError

# Helper function to parse ASP.NET-like date format to Django-friendly date
def parse_date_dmy(date_string):
    """Parses 'DD-MM-YYYY' string to a date object."""
    if not date_string:
        return None
    try:
        return datetime.strptime(date_string, '%d-%m-%Y').date()
    except ValueError:
        return None

# --- Placeholder Models for related tables (managed=False) ---
# These models map to existing tables in the database but are not managed by Django migrations.

class PrMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no or f"PR Master {self.id}"

class ItemMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.IntegerField(db_column='UOMBasic', blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True) # Assuming ItemCode exists directly or is derived from another field in your schema

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.manf_desc or f"Item {self.id}"

class UnitMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class SupplierMaster(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True) # Assuming SupplierId is a CHAR PK
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class AccHead(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol or f"Account Head {self.id}"

class PoDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # Refers to PrDetail.id
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    mid = models.IntegerField(db_column='MId', blank=True, null=True) # Refers to PoMaster.id

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

class PoMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

class RateRegister(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=3, blank=True, null=True)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'

class RateLockUnlockMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    lock_unlock = models.BooleanField(db_column='LockUnlock', blank=True, null=True) # 1 for locked
    type = models.IntegerField(db_column='Type', blank=True, null=True) # 0 for this specific check

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock Unlock Master'
        verbose_name_plural = 'Rate Lock Unlock Masters'

class InvMaterialIssueDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    min_no = models.CharField(db_column='MINNo', max_length=50, blank=True, null=True)
    mrs_id = models.IntegerField(db_column='MRSId', blank=True, null=True) # Refers to InvMaterialRequisitionDetail.id
    issue_qty = models.DecimalField(db_column='IssueQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Details'
        verbose_name = 'Material Issue Detail'
        verbose_name_plural = 'Material Issue Details'

class InvMaterialIssueMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    min_no = models.CharField(db_column='MINNo', max_length=50, blank=True, null=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Master'
        verbose_name_plural = 'Material Issue Masters'

class InvMaterialRequisitionDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    won_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Assuming WONo is stored here

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

class InvMaterialRequisitionMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Master'
        verbose_name_plural = 'Material Requisition Masters'

# --- Main PrDetail Model ---
class PrDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50) # Assuming this is linked to PrMaster's PRNo
    m_id = models.IntegerField(db_column='MId') # Linked to PrMaster.Id
    item_id = models.IntegerField(db_column='ItemId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, default=0.000)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=3, default=0.00)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3, default=0.00)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True) # FK to SupplierMaster
    del_date = models.DateField(db_column='DelDate', blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True) # FK to AccHead
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    type = models.CharField(db_column='Type', max_length=50, blank=True, null=True)
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR: {self.pr_no}, Item: {self.item_id}, ID: {self.id}"

    # --- Fat Model: Derived properties and business logic ---

    @property
    def item_code_display(self):
        """Returns the item code from ItemMaster."""
        item = ItemMaster.objects.filter(id=self.item_id).first()
        # The original fun.GetItemCode_PartNo likely had more complex logic.
        # This is a basic direct lookup. Adapt if complex logic is needed.
        return item.item_code if item else ''

    @property
    def description(self):
        """Returns the manufacturing description from ItemMaster."""
        item = ItemMaster.objects.filter(id=self.item_id).first()
        return item.manf_desc if item else ''

    @property
    def uom_symbol(self):
        """Returns the UOM symbol from UnitMaster."""
        item = ItemMaster.objects.filter(id=self.item_id).first()
        if item and item.uom_basic:
            unit = UnitMaster.objects.filter(id=item.uom_basic).first()
            return unit.symbol if unit else ''
        return ''

    @property
    def supplier_name_display(self):
        """Returns the supplier name and ID from SupplierMaster."""
        supplier = SupplierMaster.objects.filter(supplier_id=self.supplier_id).first()
        return str(supplier) if supplier else ''

    @property
    def acc_head_symbol(self):
        """Returns the Account Head symbol from AccHead."""
        acc_head = AccHead.objects.filter(id=self.ah_id).first()
        return acc_head.symbol if acc_head else ''

    @property
    def has_po_link(self):
        """Checks if this PR detail is linked to a PO detail (mimics disableCheck)."""
        # Original logic: tblMM_PO_Details.Id from tblMM_PO_Details,tblMM_PO_Master
        # WHERE tblMM_PO_Details.PRNo=prno AND tblMM_PO_Master.PONo=tblMM_PO_Details.PONo AND tblMM_PO_Master.CompId=CompId AND tblMM_PO_Details.PRId=Id AND tblMM_PO_Details.MId=tblMM_PO_Master.Id
        return PoDetail.objects.filter(
            pr_id=self.id,
            pr_no=self.pr_no,
            mid__in=PoMaster.objects.filter(comp_id=self.pr_master.comp_id).values_list('id', flat=True) # Assuming pr_master attribute available for comp_id
        ).exists()

    @property
    def pr_master(self):
        """Helper to get the associated PrMaster object."""
        return PrMaster.objects.filter(pr_no=self.pr_no, id=self.m_id).first()

    def get_total_bom_qty(self, wo_no, comp_id, fy_id):
        """
        Mimics fun.AllComponentBOMQty. This would typically involve complex logic
        or a stored procedure call specific to your ERP's BOM structure.
        Placeholder: returns a fixed value. **Needs actual implementation.**
        """
        # This function is critical to your ERP's logic and cannot be accurately
        # inferred from the provided snippet. It likely involves a multi-table
        # join/aggregation related to Bill of Materials (BOM) and Work Orders.
        # Example (replace with your actual BOM logic):
        # bom_qty = YourBomModel.objects.filter(item_id=self.item_id, wo_no=wo_no).aggregate(Sum('qty'))['qty__sum']
        return 10.000 # Placeholder value

    def get_min_qty(self, wo_no, comp_id):
        """
        Calculates MINQty based on IssueQty from material issue/requisition details.
        Mimics the sqlMINQty query.
        """
        # Original SQL: sum(IssueQty) As MinQty from tblInv_MaterialIssue_Details,tblInv_MaterialIssue_Master,tblInv_MaterialRequisition_Details,tblInv_MaterialRequisition_Master
        # where tblInv_MaterialIssue_Details.MINNo=tblInv_MaterialIssue_Master.MINNo And tblInv_MaterialRequisition_Details.MRSNo=tblInv_MaterialRequisition_Master.MRSNo
        # and tblInv_MaterialRequisition_Details.ItemId='" + DSqty.Tables[0].Rows[i]["ItemId"].ToString() + "' And tblInv_MaterialRequisition_Details.Id=tblInv_MaterialIssue_Details.MRSId
        # And tblInv_MaterialIssue_Master.MRSNo =tblInv_MaterialRequisition_Details.MRSNo And tblInv_MaterialRequisition_Details.WONo='" + WONO + "' And tblInv_MaterialIssue_Master.CompId='" + CompId + "'"

        # Get MRS Details that match ItemId and WONo
        mrs_details_ids = InvMaterialRequisitionDetail.objects.filter(
            item_id=self.item_id,
            won_no=wo_no # WONo needs to be passed from the view context
        ).values_list('id', flat=True)

        # Get MIN Master based on CompId and MRS No
        min_master_min_nos = InvMaterialIssueMaster.objects.filter(
            comp_id=comp_id,
            mrs_no__in=InvMaterialRequisitionMaster.objects.filter(mrs_no__in=InvMaterialRequisitionDetail.objects.filter(id__in=mrs_details_ids).values_list('mrs_no', flat=True)).values_list('mrs_no', flat=True)
        ).values_list('min_no', flat=True)

        # Sum IssueQty from MIN Details matching MRS_IDs and MIN_NOs
        total_issue_qty = InvMaterialIssueDetail.objects.filter(
            mrs_id__in=mrs_details_ids,
            min_no__in=min_master_min_nos
        ).aggregate(sum_qty=Coalesce(models.Sum('issue_qty'), 0.000, output_field=DecimalField()))['sum_qty']

        return total_issue_qty

    def get_min_rate_with_discount(self, comp_id):
        """
        Calculates the minimum discounted rate from RateRegister for the item.
        Mimics sqlrt2 query.
        """
        # Original SQL: min(Rate-(Rate*Discount/100)) as DisRate from tblMM_Rate_Register
        # WHERE ItemId=ItemId AND CompId=CompId
        min_rate = RateRegister.objects.filter(
            item_id=self.item_id,
            comp_id=comp_id
        ).annotate(
            disc_rate=F('rate') - (F('rate') * F('discount') / 100)
        ).aggregate(min_disc_rate=Coalesce(models.Min('disc_rate'), 0.00, output_field=DecimalField()))['min_disc_rate']
        return min_rate

    def is_rate_locked_or_unlocked(self, comp_id):
        """
        Checks RateLockUnlockMaster for override based on ItemId, CompId, LockUnlock=1, Type=0.
        Mimics sqlrate query.
        """
        # Original SQL: LockUnlock,Type,ItemId from tblMM_RateLockUnLock_Master
        # WHERE ItemId=ItemId AND CompId=CompId AND LockUnlock='1' AND Type='0'
        return RateLockUnlockMaster.objects.filter(
            item_id=self.item_id,
            comp_id=comp_id,
            lock_unlock=True, # Assuming '1' means True
            type=0
        ).exists()

    def validate_and_update_item(self, new_supplier_id, new_rate, new_discount, new_del_date_str, new_remarks, wo_no, comp_id):
        """
        Encapsulates the complex validation and update logic from Btnupdate_Click for a single item.
        Returns a tuple: (bool, str) for (is_valid, error_message).
        """
        try:
            # 1. Supplier Code Validation (fun.chkSupplierCode)
            if not SupplierMaster.objects.filter(supplier_id=new_supplier_id).exists():
                return False, "Invalid Supplier Code."

            # 2. Rate and Discount Parsing
            try:
                rate_val = Decimal(new_rate)
                discount_val = Decimal(new_discount)
            except (ValueError, TypeError):
                return False, "Rate or Discount must be valid numbers."
            
            if rate_val <= 0:
                return False, "Rate must be greater than 0."

            disc_rate = rate_val - (rate_val * discount_val / 100)

            # 3. Delivery Date Validation (fun.DateValidation)
            parsed_del_date = parse_date_dmy(new_del_date_str)
            if not parsed_del_date:
                return False, "Invalid Delivery Date format (DD-MM-YYYY)."

            # 4. Complex Rate Business Logic (k=1 logic)
            min_reg_disc_rate = self.get_min_rate_with_discount(comp_id)

            can_update_rate = True
            if min_reg_disc_rate > 0: # Only if a minimum rate exists in Rate_Register
                if disc_rate < min_reg_disc_rate: # Check if new discounted rate is lower than registered min
                    # If new rate is lower, check for override in RateLockUnlockMaster
                    if not self.is_rate_locked_or_unlocked(comp_id):
                        can_update_rate = False
                        return False, f"Rate {disc_rate:.2f} is lower than minimum registered rate {min_reg_disc_rate:.2f} and not unlocked for item {self.item_code_display}."

            if not can_update_rate:
                return False, "Rate validation failed."
            
            # If all validations pass, update the model instance
            self.supplier_id = new_supplier_id
            self.rate = rate_val
            self.discount = discount_val
            self.del_date = parsed_del_date
            self.remarks = new_remarks
            self.save() # Persist changes to the database

            return True, "Update successful."

        except Exception as e:
            # Log the exception for debugging
            print(f"Error during validation/update for PR Detail {self.id}: {e}")
            return False, f"An unexpected error occurred during update: {e}"

```

#### 4.2 Forms (Since we're handling bulk updates via HTMX, a traditional Django `ModelForm` for the entire grid isn't suitable. We'll rely on direct input processing in the view and client-side validation using Alpine.js and HTMX's validation features. However, for the supplier autocomplete, we might need a simple form field.)

```python
from django import forms
from .models import SupplierMaster

# A dummy form for autocomplete, not for general PRDetail editing
class SupplierSearchForm(forms.Form):
    search_query = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Search supplier...',
            'hx-get': '/material_management/supplier_autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-suggestions',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off'
        })
    )

```

#### 4.3 Views (`material_management/views.py`)

**Explanation:**
This setup handles the main list display, the table refresh, the bulk update operation, and the supplier autocomplete using HTMX. The `PrDetailEditListView` now acts as the central hub, processing both GET (display) and POST (update) requests. The `PrDetailTablePartialView` renders just the table content, which is swapped by HTMX.

```python
from django.views.generic import View, ListView, TemplateView
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.shortcuts import get_object_or_404, redirect
from django.db import connection, transaction
from .models import PrDetail, PrMaster, SupplierMaster, parse_date_dmy # Import helper
from django.db.models import Prefetch

# Helper to get session values (replace with proper session management)
def get_session_context(request):
    """
    Retrieves required session/query string parameters.
    In a real ERP, these would come from authenticated user's session data
    or a dedicated context provider.
    """
    comp_id = request.session.get('compid', 1)  # Default to 1 if not in session
    fin_year_id = request.session.get('finyear', 1) # Default to 1 if not in session
    pr_no = request.GET.get('PRNo', 'PR-001') # Default for testing
    wo_no = request.GET.get('WONo', 'WO-001') # Default for testing
    m_id = request.GET.get('Id', 1) # Default for testing

    return {
        'comp_id': int(comp_id),
        'fin_year_id': int(fin_year_id),
        'pr_no': pr_no,
        'wo_no': wo_no,
        'm_id': int(m_id)
    }

class PrDetailEditListView(ListView):
    """
    Main view to display and handle bulk updates for PR details.
    Behaves like the ASP.NET page's Page_Load (GET) and Btnupdate_Click (POST).
    """
    model = PrDetail
    template_name = 'material_management/prdetail/edit_list.html'
    context_object_name = 'pr_details'

    def get_queryset(self):
        context = get_session_context(self.request)
        pr_no = context['pr_no']
        m_id = context['m_id']
        comp_id = context['comp_id']

        # Fetch PR details based on PRNo and MId, similar to original fillGrid
        # Pre-populate PrMaster to avoid repeated lookups if needed
        queryset = PrDetail.objects.filter(
            pr_no=pr_no,
            m_id=m_id
        ).select_related('pr_master') # pr_master is a custom property for lookup

        # Annotate with derived properties for efficient display
        # Note: Some complex aggregations like get_total_bom_qty, get_min_qty
        # are methods that require parameters (wo_no, comp_id, fy_id)
        # These cannot be directly annotated in a single queryset call without
        # significant custom SQL or iteration. We'll handle them in template/model properties.
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        session_ctx = get_session_context(self.request)
        context['pr_no_display'] = session_ctx['pr_no'] # For lblwo
        context['pr_type_display'] = self.object_list.first().type if self.object_list.first() else 'N/A' # For lbltype
        context['comp_id'] = session_ctx['comp_id']
        context['wo_no'] = session_ctx['wo_no']
        context['fin_year_id'] = session_ctx['fin_year_id']
        return context

    def post(self, request, *args, **kwargs):
        """
        Handles the bulk update logic (mimics Btnupdate_Click).
        """
        session_ctx = get_session_context(request)
        comp_id = session_ctx['comp_id']
        wo_no = session_ctx['wo_no']

        selected_ids = request.POST.getlist('selected_pr_details[]') # IDs of checked items
        success_count = 0
        errors = []

        with transaction.atomic(): # Ensure all updates are atomic
            for pr_detail_id in selected_ids:
                pr_detail = get_object_or_404(PrDetail, pk=pr_detail_id)

                # Extract data for the current row from POST
                new_supplier_full = request.POST.get(f'supplier_{pr_detail_id}', '').strip()
                new_supplier_id = new_supplier_full.split('[')[-1].replace(']', '') if '[' in new_supplier_full else ''
                new_rate = request.POST.get(f'rate_{pr_detail_id}', '0.00')
                new_discount = request.POST.get(f'discount_{pr_detail_id}', '0.00')
                new_del_date_str = request.POST.get(f'del_date_{pr_detail_id}', '').strip()
                new_remarks = request.POST.get(f'remarks_{pr_detail_id}', '').strip()

                is_valid, message = pr_detail.validate_and_update_item(
                    new_supplier_id, new_rate, new_discount, new_del_date_str, new_remarks,
                    wo_no=wo_no, comp_id=comp_id
                )

                if is_valid:
                    success_count += 1
                else:
                    errors.append(f"Row {pr_detail.id}: {message}")

        if success_count > 0:
            messages.success(request, f"{success_count} PR detail(s) updated successfully.")
        if errors:
            for error_msg in errors:
                messages.error(request, error_msg)

        # HTMX response: Trigger a refresh of the table
        return HttpResponse(
            status=204, # No content, tells HTMX to do nothing directly
            headers={'HX-Trigger': 'refreshPrDetailList'}
        )

class PrDetailTablePartialView(TemplateView):
    """
    Renders only the table content for HTMX swaps.
    """
    template_name = 'material_management/prdetail/_pr_details_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        session_ctx = get_session_context(self.request)
        pr_no = session_ctx['pr_no']
        m_id = session_ctx['m_id']
        comp_id = session_ctx['comp_id']
        wo_no = session_ctx['wo_no']
        fin_year_id = session_ctx['fin_year_id']

        # Fetch PR details again, as this view can be triggered by a refresh
        pr_details_qs = PrDetail.objects.filter(
            pr_no=pr_no,
            m_id=m_id
        )

        # Enrich each PrDetail object with dynamic properties requiring context
        # This is where the "fat model" methods are truly utilized.
        enriched_pr_details = []
        for pr_detail in pr_details_qs:
            # Attach dynamic data that requires session context
            pr_detail.display_total_qty = pr_detail.get_total_bom_qty(wo_no, comp_id, fin_year_id)
            pr_detail.display_min_qty = pr_detail.get_min_qty(wo_no, comp_id)
            # The has_po_link property is self-contained if pr_master is available or pr_no is used

            enriched_pr_details.append(pr_detail)

        context['pr_details'] = enriched_pr_details
        context['comp_id'] = comp_id # Pass comp_id for rate history redirect
        return context

class SupplierAutocompleteView(View):
    """
    Provides autocomplete suggestions for suppliers via HTMX.
    Mimics GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('search_query', '')
        comp_id = request.session.get('compid', 1) # Get comp_id from session

        # Filter suppliers based on prefix_text and comp_id
        suppliers = SupplierMaster.objects.filter(
            supplier_name__istartswith=prefix_text,
            comp_id=comp_id
        ).order_by('supplier_name')[:10] # Limit to 10 suggestions

        suggestions = [
            f"{s.supplier_name} [{s.supplier_id}]" for s in suppliers
        ]
        return JsonResponse(suggestions, safe=False)

class PrRateHistoryRedirectView(View):
    """
    Handles redirection to the Rate Register Report.
    Mimics GridView2_RowCommand "rate" logic.
    """
    def get(self, request, item_id):
        comp_id = request.session.get('compid', 1)
        # In a real application, you'd define a proper URL for your Django report view
        # For now, we'll simulate the redirect
        messages.info(request, f"Redirecting to Rate History Report for Item ID: {item_id}, Company ID: {comp_id}")
        return redirect(f"/reports/rate_register_single_item_print/?ItemId={item_id}&CompId={comp_id}")

# This might be part of a core app or material_management app itself
class PrEditCancelView(View):
    """
    Handles the cancel action, redirecting to the main PR edit list.
    Mimics btnCancel_Click.
    """
    def get(self, request):
        messages.info(request, "PR Edit cancelled.")
        # Assuming 'pr_master_list' is the URL name for PR_Edit.aspx equivalent
        return redirect(reverse_lazy('material_management:pr_master_list'))

```

#### 4.4 Templates (`material_management/prdetail/`)

**Explanation:**
The main `edit_list.html` loads the `_pr_details_table.html` partial via HTMX, allowing for dynamic updates without full page reloads. Alpine.js is used for the date picker and potentially showing/hiding validation messages or handling UI state like the checkbox enabling/disabling inputs. DataTables handles pagination, search, and sorting.

**`material_management/prdetail/edit_list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">PR Edit Details - PR No: {{ pr_no_display }} &nbsp; Type: {{ pr_type_display }}</h2>
    </div>

    {% comment %} Messages from Django's messages framework {% endcomment %}
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% elif message.tags == 'info' %}bg-blue-100 text-blue-800{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <form hx-post="{% url 'material_management:prdetail_edit_list' %}"
          hx-target="#prDetailTable-container"
          hx-swap="outerHTML"
          hx-indicator="#loadingIndicator"
          _="on submit show #loadingIndicator then disable <form/> then wait for htmx:afterSwap then hide #loadingIndicator then enable <form/>">
        {% csrf_token %}

        <div id="prDetailTable-container"
             hx-trigger="load, refreshPrDetailList from:body"
             hx-get="{% url 'material_management:prdetail_table_partial' pr_no=pr_no_display m_id=m_id wo_no=wo_no %}" {# Pass necessary context from parent view #}
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div id="loadingIndicator" class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading PR details...</p>
            </div>
        </div>

        <div class="flex justify-center mt-6 space-x-4">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75">
                Update
            </button>
            <a href="{% url 'material_management:pr_edit_cancel' %}" class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-6 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-75">
                Cancel
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize DataTables if a table was swapped in
        if (event.detail.target.id === 'prDetailTable-container') {
            $('#prDetailTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable instance before re-initialization
            });
        }
    });

    document.addEventListener('alpine:init', () => {
        Alpine.data('prDetailRow', (prDetail) => ({
            prDetail: prDetail,
            isPoLinked: prDetail.has_po_link, // Initial value from model
            isChecked: false,
            // Function to handle checkbox change and enable/disable inputs
            toggleInputs() {
                this.isChecked = !this.isChecked;
            },
            init() {
                // Initial check for PO Link
                this.isPoLinked = prDetail.has_po_link;
            }
        }));
    });

    // Function for flatpickr calendar (date picker)
    function initializeFlatpickr() {
        flatpickr(".datepicker", {
            dateFormat: "d-m-Y",
            allowInput: false, // Prevent manual input to enforce format
        });
    }

    // Call initializeFlatpickr after HTMX swaps content
    htmx.onLoad(function(elt) {
        if (elt.id === 'prDetailTable-container') {
            initializeFlatpickr();
        }
    });

    // Also call on initial page load
    document.addEventListener('DOMContentLoaded', initializeFlatpickr);

</script>
<!-- Flatpickr CSS & JS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
{% endblock %}
```

**`material_management/prdetail/_pr_details_table.html` (Partial for HTMX)**
```html
<div id="prDetailTable-container">
    {% if pr_details %}
        <table id="prDetailTable" class="min-w-full bg-white table-auto border-collapse border border-gray-300 shadow-sm">
            <thead class="bg-gray-100">
                <tr>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Select</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Tot Qty</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">PR Qty</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">MIN Qty</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Supplier</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Rate</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Discount (%)</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">A/c Head</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Del. Date</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Remarks</th>
                    <th class="py-2 px-3 border-b-2 border-gray-300 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">View Rate</th>
                </tr>
            </thead>
            <tbody>
                {% for pr_detail in pr_details %}
                <tr x-data="prDetailRow({{ pr_detail.to_dict }})" :class="{'bg-blue-50': isChecked}">
                    <td class="py-2 px-3 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-3 border-b border-gray-200 text-center">
                        {% if not pr_detail.has_po_link %}
                            <input type="checkbox"
                                   name="selected_pr_details[]"
                                   value="{{ pr_detail.id }}"
                                   x-model="isChecked"
                                   @change="toggleInputs()"
                                   class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                        {% else %}
                            <span class="text-sm font-medium text-gray-500">PO</span>
                            <input type="hidden" name="selected_pr_details[]" value="{{ pr_detail.id }}" /> {# Still include ID for POST, but read-only #}
                        {% endif %}
                    </td>
                    <td class="py-2 px-3 border-b border-gray-200 text-center">{{ pr_detail.item_code_display }}</td>
                    <td class="py-2 px-3 border-b border-gray-200 text-left">{{ pr_detail.description }}</td>
                    <td class="py-2 px-3 border-b border-gray-200 text-center">{{ pr_detail.uom_symbol }}</td>
                    <td class="py-2 px-3 border-b border-gray-200 text-right">{{ pr_detail.display_total_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-3 border-b border-gray-200 text-right">{{ pr_detail.qty|floatformat:"3" }}</td>
                    <td class="py-2 px-3 border-b border-gray-200 text-right">{{ pr_detail.display_min_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">
                        <input type="text"
                               name="supplier_{{ pr_detail.id }}"
                               value="{{ pr_detail.supplier_name_display }}"
                               x-bind:disabled="isPoLinked || !isChecked"
                               class="w-full border px-2 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-300 disabled:bg-gray-100 disabled:text-gray-500"
                               hx-get="{% url 'material_management:supplier_autocomplete' %}"
                               hx-trigger="keyup changed delay:500ms, search"
                               hx-target="#supplier_suggestions_{{ pr_detail.id }}"
                               hx-indicator=".htmx-indicator"
                               autocomplete="off"
                               placeholder="Start typing supplier name...">
                        <div id="supplier_suggestions_{{ pr_detail.id }}" class="absolute z-10 bg-white border border-gray-300 rounded shadow-lg max-h-48 overflow-y-auto w-full">
                            {# Suggestions will be loaded here #}
                        </div>
                    </td>
                    <td class="py-2 px-3 border-b border-gray-200">
                        <input type="text"
                               name="rate_{{ pr_detail.id }}"
                               value="{{ pr_detail.rate|floatformat:'2' }}"
                               x-bind:disabled="isPoLinked || !isChecked"
                               class="w-full border px-2 py-1 rounded text-sm text-right focus:outline-none focus:ring-2 focus:ring-blue-300 disabled:bg-gray-100 disabled:text-gray-500">
                    </td>
                    <td class="py-2 px-3 border-b border-gray-200">
                        <input type="text"
                               name="discount_{{ pr_detail.id }}"
                               value="{{ pr_detail.discount|floatformat:'2' }}"
                               x-bind:disabled="isPoLinked || !isChecked"
                               class="w-full border px-2 py-1 rounded text-sm text-right focus:outline-none focus:ring-2 focus:ring-blue-300 disabled:bg-gray-100 disabled:text-gray-500">
                    </td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ pr_detail.acc_head_symbol }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">
                        <input type="text"
                               name="del_date_{{ pr_detail.id }}"
                               value="{{ pr_detail.del_date|date:'d-m-Y' }}"
                               x-bind:disabled="isPoLinked || !isChecked"
                               class="datepicker w-full border px-2 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-300 disabled:bg-gray-100 disabled:text-gray-500">
                    </td>
                    <td class="py-2 px-3 border-b border-gray-200">
                        <input type="text"
                               name="remarks_{{ pr_detail.id }}"
                               value="{{ pr_detail.remarks|default_if_none:'' }}"
                               x-bind:disabled="isPoLinked || !isChecked"
                               class="w-full border px-2 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-300 disabled:bg-gray-100 disabled:text-gray-500">
                    </td>
                    <td class="py-2 px-3 border-b border-gray-200 text-center">
                        <a href="{% url 'material_management:pr_rate_history' item_id=pr_detail.item_id %}"
                           target="_blank"
                           class="inline-block bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded-lg text-xs"
                           title="View Rate History">
                           <img src="/static/images/Rupee.JPG" alt="Rupee" class="h-4 w-4 inline-block">
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="text-center py-10">
            <p class="text-lg text-gray-500">No data to display!</p>
        </div>
    {% endif %}
</div>
```

#### 4.5 URLs (`material_management/urls.py`)

**Explanation:**
This sets up the URL patterns for the `material_management` app, including the main list/update view, the HTMX partial for the table, the supplier autocomplete, and the cancel/rate history redirects.

```python
from django.urls import path
from .views import (
    PrDetailEditListView,
    PrDetailTablePartialView,
    SupplierAutocompleteView,
    PrRateHistoryRedirectView,
    PrEditCancelView,
)

app_name = 'material_management'

urlpatterns = [
    # Main PR Detail Edit List View (handles GET for display, POST for bulk update)
    path('pr_edit_details/', PrDetailEditListView.as_view(), name='prdetail_edit_list'),

    # HTMX endpoint for the table partial
    path('pr_edit_details/table/<str:pr_no>/<int:m_id>/<str:wo_no>/', PrDetailTablePartialView.as_view(), name='prdetail_table_partial'),

    # HTMX endpoint for supplier autocomplete
    path('supplier_autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # Redirect for rate history report (assuming reports are in a separate module/URL)
    path('pr_edit_details/rate_history/<int:item_id>/', PrRateHistoryRedirectView.as_view(), name='pr_rate_history'),

    # Redirect for cancel button
    path('pr_edit_details/cancel/', PrEditCancelView.as_view(), name='pr_edit_cancel'),

    # Placeholder for PR_Edit.aspx equivalent (assuming this is a parent list page)
    path('pr_master_list/', PrEditCancelView.as_view(), name='pr_master_list'), # Redirects to this on cancel
]

```
**Important:** You would need to include these URLs in your project's main `urls.py`:
`path('material_management/', include('material_management.urls')),`

#### 4.6 Tests (`material_management/tests.py`)

**Explanation:**
Comprehensive unit tests for the `PrDetail` model's properties and validation logic, and integration tests for the views. These tests ensure the core business logic and user interactions work as expected, covering the complexities migrated from the C# code-behind.

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from datetime import date
from unittest.mock import patch, MagicMock

from .models import (
    PrDetail, PrMaster, ItemMaster, UnitMaster, SupplierMaster, AccHead,
    PoDetail, PoMaster, RateRegister, RateLockUnlockMaster,
    InvMaterialIssueDetail, InvMaterialIssueMaster,
    InvMaterialRequisitionDetail, InvMaterialRequisitionMaster,
    parse_date_dmy
)

class PrDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related lookup tables (managed=False)
        # We need to simulate the database state for these tables.
        cls.pr_master = PrMaster.objects.create(id=1, pr_no='PR-TEST-001', comp_id=1)
        cls.item_master_1 = ItemMaster.objects.create(id=101, item_code='ITEM001', manf_desc='Test Item 1', uom_basic=1)
        cls.item_master_2 = ItemMaster.objects.create(id=102, item_code='ITEM002', manf_desc='Test Item 2', uom_basic=2)
        cls.unit_master_1 = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_master_2 = UnitMaster.objects.create(id=2, symbol='EA')
        cls.supplier_master_1 = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Test Supplier A', comp_id=1)
        cls.supplier_master_2 = SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='Test Supplier B', comp_id=1)
        cls.acc_head_1 = AccHead.objects.create(id=1, symbol='RAW_MAT')
        cls.acc_head_2 = AccHead.objects.create(id=2, symbol='PROD_EXP')

        # Create PrDetail instances
        cls.pr_detail_1 = PrDetail.objects.create(
            id=1, pr_no='PR-TEST-001', m_id=1, item_id=101, qty=Decimal('10.000'),
            rate=Decimal('100.00'), discount=Decimal('5.00'), supplier_id='SUP001',
            del_date=date(2024, 7, 30), ah_id=1, remarks='Initial remarks', type='STD',
            p_id=1, c_id=1
        )
        cls.pr_detail_2 = PrDetail.objects.create(
            id=2, pr_no='PR-TEST-001', m_id=1, item_id=102, qty=Decimal('5.000'),
            rate=Decimal('200.00'), discount=Decimal('0.00'), supplier_id='SUP002',
            del_date=date(2024, 8, 15), ah_id=2, remarks='Another item', type='EXP',
            p_id=2, c_id=2
        )

        # Data for has_po_link
        cls.po_master = PoMaster.objects.create(id=1, po_no='PO-001', comp_id=1)
        cls.po_detail_linked = PoDetail.objects.create(
            id=1, pr_no='PR-TEST-001', pr_id=cls.pr_detail_1.id, po_no='PO-001', mid=cls.po_master.id
        )

        # Data for rate validation
        RateRegister.objects.create(id=1, item_id=101, comp_id=1, rate=Decimal('105.00'), discount=Decimal('1.00')) # Disc rate 103.95
        RateRegister.objects.create(id=2, item_id=101, comp_id=1, rate=Decimal('90.00'), discount=Decimal('0.00')) # Disc rate 90.00 (Min)
        RateLockUnlockMaster.objects.create(id=1, item_id=101, comp_id=1, lock_unlock=True, type=0) # Lock for item 101

        # Data for MINQty
        cls.mrs_master_1 = InvMaterialRequisitionMaster.objects.create(mrs_no='MRS-001')
        cls.mrs_detail_1 = InvMaterialRequisitionDetail.objects.create(
            id=1, mrs_no='MRS-001', item_id=101, won_no='WO-001'
        )
        cls.min_master_1 = InvMaterialIssueMaster.objects.create(min_no='MIN-001', mrs_no='MRS-001', comp_id=1)
        cls.min_detail_1 = InvMaterialIssueDetail.objects.create(
            id=1, min_no='MIN-001', mrs_id=cls.mrs_detail_1.id, issue_qty=Decimal('7.500')
        )
        cls.min_detail_2 = InvMaterialIssueDetail.objects.create( # Another issue for the same MRS
            id=2, min_no='MIN-001', mrs_id=cls.mrs_detail_1.id, issue_qty=Decimal('2.500')
        )


    def test_prdetail_creation(self):
        pr_detail = PrDetail.objects.get(id=1)
        self.assertEqual(pr_detail.pr_no, 'PR-TEST-001')
        selfassertEqual(pr_detail.item_id, 101)
        self.assertEqual(pr_detail.qty, Decimal('10.000'))
        self.assertEqual(pr_detail.rate, Decimal('100.00'))
        self.assertEqual(pr_detail.discount, Decimal('5.00'))
        self.assertEqual(pr_detail.supplier_id, 'SUP001')
        self.assertEqual(pr_detail.del_date, date(2024, 7, 30))

    def test_derived_properties(self):
        pr_detail_1 = PrDetail.objects.get(id=1)
        pr_detail_2 = PrDetail.objects.get(id=2)

        self.assertEqual(pr_detail_1.item_code_display, 'ITEM001')
        self.assertEqual(pr_detail_1.description, 'Test Item 1')
        self.assertEqual(pr_detail_1.uom_symbol, 'KG')
        self.assertEqual(pr_detail_1.supplier_name_display, 'Test Supplier A [SUP001]')
        self.assertEqual(pr_detail_1.acc_head_symbol, 'RAW_MAT')

        self.assertEqual(pr_detail_2.item_code_display, 'ITEM002')
        self.assertEqual(pr_detail_2.description, 'Test Item 2')
        self.assertEqual(pr_detail_2.uom_symbol, 'EA')
        self.assertEqual(pr_detail_2.supplier_name_display, 'Test Supplier B [SUP002]')
        self.assertEqual(pr_detail_2.acc_head_symbol, 'PROD_EXP')

    def test_has_po_link(self):
        pr_detail_1 = PrDetail.objects.get(id=1) # Linked to PO
        pr_detail_2 = PrDetail.objects.get(id=2) # Not linked to PO
        self.assertTrue(pr_detail_1.has_po_link)
        self.assertFalse(pr_detail_2.has_po_link)

    @patch('material_management.models.PrDetail.get_total_bom_qty', return_value=Decimal('15.000'))
    def test_get_total_bom_qty(self, mock_bom_qty):
        pr_detail_1 = PrDetail.objects.get(id=1)
        # Mocking this as it's a placeholder. In real test, it would interact with BOM logic.
        qty = pr_detail_1.get_total_bom_qty('WO-001', 1, 1)
        self.assertEqual(qty, Decimal('15.000'))

    def test_get_min_qty(self):
        pr_detail_1 = PrDetail.objects.get(id=1)
        min_qty = pr_detail_1.get_min_qty('WO-001', 1)
        self.assertEqual(min_qty, Decimal('10.000')) # 7.500 + 2.500

    def test_get_min_rate_with_discount(self):
        pr_detail_1 = PrDetail.objects.get(id=1)
        min_rate = pr_detail_1.get_min_rate_with_discount(1)
        self.assertEqual(min_rate, Decimal('90.00'))

    def test_is_rate_locked_or_unlocked(self):
        pr_detail_1 = PrDetail.objects.get(id=1)
        self.assertTrue(pr_detail_1.is_rate_locked_or_unlocked(1))

        # Test for an item not locked or unlocked (e.g., item 102)
        pr_detail_2 = PrDetail.objects.get(id=2)
        self.assertFalse(pr_detail_2.is_rate_locked_or_unlocked(1))

    def test_validate_and_update_item_success(self):
        pr_detail = PrDetail.objects.get(id=2) # Item 2, current rate 200, discount 0
        original_supplier_id = pr_detail.supplier_id
        original_rate = pr_detail.rate

        # Update with valid data, new rate 190, discount 5, new del date
        is_valid, msg = pr_detail.validate_and_update_item(
            'SUP001', '190.00', '5.00', '10-09-2024', 'Updated remarks', 'WO-001', 1
        )
        self.assertTrue(is_valid)
        self.assertEqual(msg, "Update successful.")
        pr_detail.refresh_from_db()
        self.assertEqual(pr_detail.supplier_id, 'SUP001')
        self.assertEqual(pr_detail.rate, Decimal('190.00'))
        self.assertEqual(pr_detail.discount, Decimal('5.00'))
        self.assertEqual(pr_detail.del_date, date(2024, 9, 10))
        self.assertEqual(pr_detail.remarks, 'Updated remarks')

    def test_validate_and_update_item_invalid_supplier(self):
        pr_detail = PrDetail.objects.get(id=2)
        is_valid, msg = pr_detail.validate_and_update_item(
            'NONEXISTENT', '150.00', '0.00', '10-09-2024', 'Remarks', 'WO-001', 1
        )
        self.assertFalse(is_valid)
        self.assertEqual(msg, "Invalid Supplier Code.")
        pr_detail.refresh_from_db() # Ensure no change
        self.assertNotEqual(pr_detail.supplier_id, 'NONEXISTENT')

    def test_validate_and_update_item_invalid_rate_format(self):
        pr_detail = PrDetail.objects.get(id=2)
        is_valid, msg = pr_detail.validate_and_update_item(
            'SUP001', 'abc', '0.00', '10-09-2024', 'Remarks', 'WO-001', 1
        )
        self.assertFalse(is_valid)
        self.assertEqual(msg, "Rate or Discount must be valid numbers.")

    def test_validate_and_update_item_rate_less_than_zero(self):
        pr_detail = PrDetail.objects.get(id=2)
        is_valid, msg = pr_detail.validate_and_update_item(
            'SUP001', '0.00', '0.00', '10-09-2024', 'Remarks', 'WO-001', 1
        )
        self.assertFalse(is_valid)
        self.assertEqual(msg, "Rate must be greater than 0.")

    def test_validate_and_update_item_invalid_date_format(self):
        pr_detail = PrDetail.objects.get(id=2)
        is_valid, msg = pr_detail.validate_and_update_item(
            'SUP001', '150.00', '0.00', '2024-09-10', 'Remarks', 'WO-001', 1
        )
        self.assertFalse(is_valid)
        self.assertEqual(msg, "Invalid Delivery Date format (DD-MM-YYYY).")

    def test_validate_and_update_item_rate_lower_than_min_not_unlocked(self):
        pr_detail = PrDetail.objects.get(id=2) # Item 102, no lock/unlock setup
        # Set a min rate for item 102 (e.g., 200.00)
        RateRegister.objects.create(id=3, item_id=102, comp_id=1, rate=Decimal('200.00'), discount=Decimal('0.00'))

        is_valid, msg = pr_detail.validate_and_update_item(
            'SUP001', '150.00', '0.00', '10-09-2024', 'Remarks', 'WO-001', 1
        )
        self.assertFalse(is_valid)
        self.assertIn("Rate 150.00 is lower than minimum registered rate 200.00 and not unlocked", msg)

    def test_validate_and_update_item_rate_lower_than_min_but_unlocked(self):
        pr_detail = PrDetail.objects.get(id=2) # Item 102
        # Set a min rate for item 102 (e.g., 200.00)
        RateRegister.objects.create(id=4, item_id=102, comp_id=1, rate=Decimal('200.00'), discount=Decimal('0.00'))
        # And unlock it
        RateLockUnlockMaster.objects.create(id=2, item_id=102, comp_id=1, lock_unlock=True, type=0)

        is_valid, msg = pr_detail.validate_and_update_item(
            'SUP001', '150.00', '0.00', '10-09-2024', 'Remarks', 'WO-001', 1
        )
        self.assertTrue(is_valid)
        self.assertEqual(msg, "Update successful.")
        pr_detail.refresh_from_db()
        self.assertEqual(pr_detail.rate, Decimal('150.00'))


class PrDetailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup similar test data as model tests
        cls.pr_master = PrMaster.objects.create(id=1, pr_no='PR-VIEW-001', comp_id=1)
        cls.item_master_1 = ItemMaster.objects.create(id=101, item_code='VIEWITEM001', manf_desc='View Test Item 1', uom_basic=1)
        cls.item_master_2 = ItemMaster.objects.create(id=102, item_code='VIEWITEM002', manf_desc='View Test Item 2', uom_basic=2)
        cls.unit_master_1 = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_master_2 = UnitMaster.objects.create(id=2, symbol='EA')
        cls.supplier_master_1 = SupplierMaster.objects.create(supplier_id='VIEWSUP001', supplier_name='View Supplier A', comp_id=1)
        cls.supplier_master_2 = SupplierMaster.objects.create(supplier_id='VIEWSUP002', supplier_name='View Supplier B', comp_id=1)
        cls.acc_head_1 = AccHead.objects.create(id=1, symbol='RAW_MAT_V')
        cls.acc_head_2 = AccHead.objects.create(id=2, symbol='PROD_EXP_V')

        cls.pr_detail_1 = PrDetail.objects.create(
            id=10, pr_no='PR-VIEW-001', m_id=1, item_id=101, qty=Decimal('20.000'),
            rate=Decimal('200.00'), discount=Decimal('10.00'), supplier_id='VIEWSUP001',
            del_date=date(2024, 9, 1), ah_id=1, remarks='View remarks 1', type='STD',
            p_id=1, c_id=1
        )
        cls.pr_detail_2 = PrDetail.objects.create(
            id=11, pr_no='PR-VIEW-001', m_id=1, item_id=102, qty=Decimal('10.000'),
            rate=Decimal('300.00'), discount=Decimal('0.00'), supplier_id='VIEWSUP002',
            del_date=date(2024, 9, 10), ah_id=2, remarks='View remarks 2', type='EXP',
            p_id=2, c_id=2
        )

        cls.po_master = PoMaster.objects.create(id=10, po_no='PO-VIEW-001', comp_id=1)
        PoDetail.objects.create(
            id=10, pr_no='PR-VIEW-001', pr_id=cls.pr_detail_1.id, po_no='PO-VIEW-001', mid=cls.po_master.id
        )

        # Rate and Lock/Unlock setup for validation during update
        RateRegister.objects.create(id=10, item_id=101, comp_id=1, rate=Decimal('190.00'), discount=Decimal('0.00')) # Disc rate 190
        RateLockUnlockMaster.objects.create(id=10, item_id=101, comp_id=1, lock_unlock=False, type=0) # Not unlocked by default


    def setUp(self):
        self.client = Client()
        self.session_data = {
            'compid': 1,
            'finyear': 1,
            'username': 'testuser'
        }
        self.client.session.update(self.session_data)

        self.pr_no = 'PR-VIEW-001'
        self.m_id = 1
        self.wo_no = 'WO-001' # Needs to be consistent for MINQty/BOM Qty calculation in tests

    def test_prdetail_edit_list_view_get(self):
        url = reverse('material_management:prdetail_edit_list')
        response = self.client.get(url, {'PRNo': self.pr_no, 'Id': self.m_id, 'WONo': self.wo_no})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/edit_list.html')
        self.assertContains(response, 'PR Edit Details - PR No: PR-VIEW-001')
        self.assertContains(response, '<div id="prDetailTable-container"') # HTMX container

    def test_prdetail_table_partial_view_get(self):
        url = reverse('material_management:prdetail_table_partial', args=[self.pr_no, self.m_id, self.wo_no])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/_pr_details_table.html')
        self.assertContains(response, 'VIEWITEM001')
        self.assertContains(response, 'VIEWITEM002')
        self.assertContains(response, 'id="prDetailTable"')

        # Check for PO linked status (pr_detail_1 should be PO linked)
        self.assertContains(response, '<span>PO</span>') # For pr_detail_1

    @patch('material_management.models.PrDetail.validate_and_update_item', return_value=(True, "Update successful."))
    def test_prdetail_edit_list_view_post_success(self, mock_validate_and_update_item):
        pr_detail_2_initial_rate = PrDetail.objects.get(id=11).rate
        url = reverse('material_management:prdetail_edit_list')
        data = {
            'selected_pr_details[]': [str(self.pr_detail_2.id)], # Only select pr_detail_2
            f'supplier_{self.pr_detail_2.id}': 'View Supplier A [VIEWSUP001]',
            f'rate_{self.pr_detail_2.id}': '250.00',
            f'discount_{self.pr_detail_2.id}': '2.00',
            f'del_date_{self.pr_detail_2.id}': '20-10-2024',
            f'remarks_{self.pr_detail_2.id}': 'Updated remarks for item 2',
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertEqual(response['HX-Trigger'], 'refreshPrDetailList')
        mock_validate_and_update_item.assert_called_once_with(
            'VIEWSUP001', '250.00', '2.00', '20-10-2024', 'Updated remarks for item 2',
            wo_no=self.wo_no, comp_id=self.session_data['compid']
        )
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), '1 PR detail(s) updated successfully.')

    @patch('material_management.models.PrDetail.validate_and_update_item', return_value=(False, "Invalid supplier."))
    def test_prdetail_edit_list_view_post_failure(self, mock_validate_and_update_item):
        url = reverse('material_management:prdetail_edit_list')
        data = {
            'selected_pr_details[]': [str(self.pr_detail_2.id)],
            f'supplier_{self.pr_detail_2.id}': 'Invalid Supplier [XYZ001]', # This input triggers the mock failure
            f'rate_{self.pr_detail_2.id}': '250.00',
            f'discount_{self.pr_detail_2.id}': '2.00',
            f'del_date_{self.pr_detail_2.id}': '20-10-2024',
            f'remarks_{self.pr_detail_2.id}': 'Updated remarks for item 2',
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshPrDetailList')
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Row 11: Invalid supplier.')


    def test_supplier_autocomplete_view(self):
        url = reverse('material_management:supplier_autocomplete')
        response = self.client.get(url, {'search_query': 'View Supplier'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('View Supplier A [VIEWSUP001]', data)
        self.assertIn('View Supplier B [VIEWSUP002]', data)
        self.assertEqual(len(data), 2)

    def test_supplier_autocomplete_view_no_match(self):
        url = reverse('material_management:supplier_autocomplete')
        response = self.client.get(url, {'search_query': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 0)

    def test_pr_rate_history_redirect_view(self):
        item_id = self.pr_detail_1.item_id
        url = reverse('material_management:pr_rate_history', args=[item_id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertEqual(response['Location'], f"/reports/rate_register_single_item_print/?ItemId={item_id}&CompId={self.session_data['compid']}")
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertIn('Redirecting to Rate History Report for Item ID:', str(messages[0]))


    def test_pr_edit_cancel_view(self):
        url = reverse('material_management:pr_edit_cancel')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertEqual(response['Location'], reverse('material_management:pr_master_list')) # Redirects to parent list
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'PR Edit cancelled.')

```

### Step 5: HTMX and Alpine.js Integration

**Explanation:**
The provided Django code heavily relies on HTMX and Alpine.js for dynamic interactions:

*   **HTMX for Table Updates:**
    *   The `edit_list.html` uses `hx-get` on a `div` with `id="prDetailTable-container"` to initially load the table content from `_pr_details_table.html`.
    *   `hx-trigger="load, refreshPrDetailList from:body"` ensures the table loads on page load and refreshes whenever the `refreshPrDetailList` custom event is triggered (e.g., after a successful form submission).
    *   The main `form` uses `hx-post` to send the bulk update data. `hx-target` is set to `#prDetailTable-container` and `hx-swap="outerHTML"` to replace the entire table container after the POST, effectively refreshing the whole grid.
    *   `hx-indicator` is used to show a loading spinner during HTMX requests.
    *   `hx-trigger: 'refreshPrDetailList'` is returned from the `PrDetailEditListView` on POST success, ensuring the table reloads with the updated data.
*   **Alpine.js for Row-Specific State:**
    *   Each table row `<tr>` has `x-data="prDetailRow(pr_detail)"` to create an Alpine.js component for that row.
    *   `isPoLinked` determines if the "PO" label or the checkbox is shown, and if the inputs are disabled. This is based on a `has_po_link` property on the `PrDetail` model.
    *   `isChecked` tracks the state of the checkbox.
    *   `x-bind:disabled="isPoLinked || !isChecked"` dynamically disables input fields based on both the `has_po_link` status and whether the checkbox is checked, mimicking the ASP.NET `GetValidate` logic.
    *   `@change="toggleInputs()"` updates `isChecked` when the checkbox is clicked.
*   **DataTables Integration:**
    *   The `_pr_details_table.html` contains the `<table>` element with `id="prDetailTable"`.
    *   A JavaScript block within `edit_list.html` uses `htmx:afterSwap` event to re-initialize DataTables on the newly loaded table content, ensuring all DataTables features (pagination, search, sorting) work correctly after HTMX updates.
    *   `"destroy": true` is crucial for DataTables re-initialization within an HTMX context.
*   **Autocomplete with HTMX:**
    *   The `TxtSupplier` input in `_pr_details_table.html` uses `hx-get` to `supplier_autocomplete/` endpoint, `hx-trigger="keyup changed delay:500ms, search"` for dynamic search, and `hx-target` to a div for suggestions, creating a fully dynamic autocomplete without extra JavaScript.
*   **Date Picker with Flatpickr:**
    *   The `TxtDelDate` input uses a `datepicker` class. Flatpickr, a lightweight date picker library, is initialized on elements with this class after HTMX swaps complete and on initial page load.

### Final Notes

*   **Placeholder Logic:** The `get_total_bom_qty` and `get_min_qty` methods in the `PrDetail` model currently contain placeholder logic. These represent complex business rules from the original ERP that require detailed analysis of your existing database schema and logic (`fun.AllComponentBOMQty`, `sqlMINQty`). A true migration would involve re-implementing these queries accurately within the Django ORM or by calling existing stored procedures if your ERP logic resides there.
*   **Error Handling:** The `validate_and_update_item` method in the model returns a message on failure. This message is then displayed using Django's `messages` framework, visible at the top of the page.
*   **Static Files:** Ensure you have configured Django to serve static files correctly, and placed any images (like `Rupee.JPG`) in your `static` directory. The DataTables and Flatpickr CSS/JS links are pulled from CDNs for convenience in this example.
*   **User Session/Context:** The `get_session_context` helper function simulates accessing `Session["compid"]`, `Session["finyear"]`, `Request.QueryString["PRNo"]`, `Request.QueryString["WONo"]`, `Request.QueryString["Id"]`. In a real application, these should be handled securely, typically by Django's authentication system and proper URL design or middleware to pass context.
*   **External Links:** The "Rate History" link points to an external (or separate Django app) report, simulating the original redirect behavior. Ensure this target URL is configured in your project.
*   **DRY Templates:** The use of `_pr_details_table.html` as a partial template ensures the table rendering logic is not duplicated.