# Django App Generator Requirements
# Revolutionary AI-powered Django app generation using Gemini 2.5 Flash Preview

# Core AI Dependencies
google-genai>=0.8.0
google-cloud-aiplatform>=1.60.0

# Django Framework
Django>=5.0.14
django-extensions>=3.2.3

# Environment and Configuration
python-dotenv>=1.0.0

# Data Processing
pathlib2>=2.3.7
typing-extensions>=4.8.0

# Logging and Utilities
colorlog>=6.8.0
rich>=13.7.0

# Development and Testing (Optional)
pytest>=7.4.0
pytest-django>=4.8.0
black>=23.12.0
flake8>=6.1.0

# Documentation Processing
markdown>=3.5.0
beautifulsoup4>=4.12.0

# File Processing
chardet>=5.2.0

# JSON and Data Validation
jsonschema>=4.20.0
pydantic>=2.5.0

# Progress Tracking
tqdm>=4.66.0

# Caching
diskcache>=5.6.0

# Optional: Enhanced Terminal Output
click>=8.1.0
