
import sys

if "google.colab" in sys.modules:
    from google.colab import auth

    auth.authenticate_user()

from IPython.display import HTML, Image, Markdown, display
from google import genai
from google.genai.types import (
    FunctionDeclaration,
    GenerateContentConfig,
    GoogleSearch,
    HarmBlockThreshold,
    HarmCategory,
    Part,
    SafetySetting,
    ThinkingConfig,
    Tool,
    ToolCodeExecution,
)

import os
from dotenv import load_dotenv
load_dotenv()

API_KEY = os.getenv("GEMINI_API_KEY")
client = genai.Client( api_key=API_KEY)
MODEL_ID = os.getenv("MODEL_ID")


response = client.models.generate_content(
    model=MODEL_ID,
    contents="Roger has 5 tennis balls. He buys 2 more cans of tennis balls. Each can has 3 tennis balls. How many tennis balls does he have now?",
)

display(Markdown(response.text))
print(response.text)




"""### Generate content stream

By default, the model returns a response after completing the entire generation process. You can also use the `generate_content_stream` method to stream the response as it is being generated, and the model will return chunks of the response as soon as they are generated.
"""

# output_text = ""
# markdown_display_area = display(Markdown(output_text), display_id=True)

# for chunk in client.models.generate_content_stream(
#     model=<PERSON>ODEL_ID,
#     contents="On average Joe throws 25 punches per minute. A fight lasts 5 rounds of 3 minutes. How many punches did he throw?",
# ):
#     output_text += chunk.text
#     markdown_display_area.update(Markdown(output_text))



# """## Configure thinking

# ### Control thinking budget

# The thinking budget allows the model to dynamically think for a task or select how many tokens to use for reasoning for certain tasks. It allows users to control quality and speed of response.

# Setting budget to `0` turns off thinking and turns the model into a non-thinking model for simpler tasks.

# You set the optional `thinking_budget` parameter in the `ThinkingConfig` to control and configure how much a model thinks on a given user prompt.

# - When unset -> dynamic thinking (default)
# - When set to  `0` -> thinking is disabled.  
# - When set to `[1-24576]` ->  model uses the allocated thinking budget

# Then use the `generate_content` or `generate_content_stream` method to send a request to generate content with the `thinking_config`.
# """



# THINKING_BUDGET = 1024  # @param {type: "integer"}

# response = client.models.generate_content(
#     model=MODEL_ID,
#     contents="What are the practical implications of the P vs. NP problem for algorithm design and cryptography?",
#     config=GenerateContentConfig(
#         thinking_config=ThinkingConfig(
#             thinking_budget=THINKING_BUDGET,
#         )
#     ),
# )

# display(Markdown(response.text))
# print(response.text)



# """Optionally, you can print the `usage_metadata` and token counts from the model response."""

# print(response.usage_metadata)
# print(response.usage_metadata.thoughts_token_count)
# print(response.usage_metadata.total_token_count)

# """### View summarized thoughts

# You can optionally set the `include_thoughts` flag to enable the model to generate and return a summary of the "thoughts" that it generates in addition to the final answer.

# In this example, you use the `generate_content` method to send a request to generate content with summarized thoughts. The model responds with multiple parts, the thoughts and the model response. You can check the `part.thought` field to determine if a part is a thought or not.
# """

# response = client.models.generate_content(
#     model=MODEL_ID,
#     contents="How many R's are in the word strawberry?",
#     config=GenerateContentConfig(
#         thinking_config=ThinkingConfig(
#             include_thoughts=True,
#         )
#     ),
# )

# for part in response.candidates[0].content.parts:
#     if part.thought:
#         display(
#             Markdown(
#                 f"""## Summarized Thoughts:
#          {part.text}
#         """
#             )
#         )
#     else:
#         display(
#             Markdown(
#                 f"""## Answer:
#          {part.text}
#         """
#             )
#         )

# """This example shows how to set the `include_thoughts` and `thinking_budget` in the `generate_content_stream` method."""

THINKING_BUDGET = 1024  # @param {type: "integer"}
INCLUDE_THOUGHTS = True  # @param {type: "boolean"}

# responses = client.models.generate_content_stream(
#     model=MODEL_ID,
#     contents="How might quantum sensors improve the precision of measurements in fields like materials science or navigation?",
#     config=GenerateContentConfig(
#         thinking_config=ThinkingConfig(
#             thinking_budget=THINKING_BUDGET,
#             include_thoughts=INCLUDE_THOUGHTS,
#         )
#     ),
# )

# first_thought = True
# first_answer = True

# for response in responses:
#     for part in response.candidates[0].content.parts:
#         if part.thought and first_thought:
#             first_thought = False
#             display(Markdown("## Thoughts"))
#         elif not part.thought and first_answer:
#             first_answer = False
#             display(Markdown("## Answer"))
#         print(part.text, end="")

# """#### Example prompts

# - What are the potential advantages and limitations of applying federated learning to train models on sensitive financial data?
# - What are the challenges and benefits of using transformer models (like BERT or GPT) for protein structure prediction compared to traditional methods?
# - (Try your own prompts!)

# For more examples of prompt engineering, refer to [this notebook](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/prompts/intro_prompt_design.ipynb).

# For the following examples, we will set the thinking budget to `0` to reduce latency.
# """

# thinking_config = ThinkingConfig(thinking_budget=0)

# """### Start a multi-turn chat

# The Gemini API supports freeform multi-turn conversations across multiple turns with back-and-forth interactions.

# The context of the conversation is preserved between messages.
# """

thinking_config=ThinkingConfig(
            thinking_budget=THINKING_BUDGET,
            include_thoughts=INCLUDE_THOUGHTS,
        )

# thinking_config = ThinkingConfig(thinking_budget=0)

chat = client.chats.create(
    model=MODEL_ID,
    config=GenerateContentConfig(thinking_config=thinking_config),
)

response = chat.send_message("Write a function that checks if a year is a leap year.")
print(response.text)


display(Markdown(response.text))

"""This follow-up prompt shows how the model responds based on the previous prompt:"""

response = chat.send_message("Write a unit test of the generated function.")

display(Markdown(response.text))

"""### Send asynchronous requests

`client.aio` exposes all analogous [async](https://docs.python.org/3/library/asyncio.html) methods that are available on `client`.

For example, `client.aio.models.generate_content` is the async version of `client.models.generate_content`.
"""

response = await client.aio.models.generate_content(
    model=MODEL_ID,
    contents="Compose a song about the adventures of a time-traveling squirrel.",
    config=GenerateContentConfig(thinking_config=thinking_config),
)

display(Markdown(response.text))

"""## Configure model parameters

You can include parameter values in each call that you send to a model to control how the model generates a response. The model can generate different results for different parameter values. You can experiment with different model parameters to see how the results change.

- Learn more about [experimenting with parameter values](https://cloud.google.com/vertex-ai/generative-ai/docs/learn/prompts/adjust-parameter-values).

- See a list of all [Gemini API parameters](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/inference#parameters).

"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents="Tell me how the internet works, but pretend I'm a puppy who only understands squeaky toys.",
    config=GenerateContentConfig(
        temperature=2.0,
        top_p=0.95,
        candidate_count=1,
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

"""## Set system instructions

[System instructions](https://cloud.google.com/vertex-ai/generative-ai/docs/learn/prompts/system-instruction-introduction) allow you to steer the behavior of the model. By setting the system instruction, you are giving the model additional context to understand the task, provide more customized responses, and adhere to guidelines over the user interaction.
"""

system_instruction = """
  You are a helpful language translator.
  Your mission is to translate text in English to Spanish.
"""

prompt = """
  User input: I like bagels.
  Answer:
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=prompt,
    config=GenerateContentConfig(
        system_instruction=system_instruction,
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

"""## Safety filters

The Gemini API provides safety filters that you can adjust across multiple filter categories to restrict or allow certain types of content. You can use these filters to adjust what's appropriate for your use case. See the [Configure safety filters](https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/configure-safety-filters) page for details.

When you make a request to Gemini, the content is analyzed and assigned a safety rating. You can inspect the safety ratings of the generated content by printing out the model responses.

The safety settings are `OFF` by default and the default block thresholds are `BLOCK_NONE`.

For more examples of safety filters, refer to [this notebook](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/responsible-ai/gemini_safety_ratings.ipynb).

You can use `safety_settings` to adjust the safety settings for each request you make to the API. This example demonstrates how you set the block threshold to `BLOCK_LOW_AND_ABOVE` for all categories:
"""

system_instruction = "Be as mean and hateful as possible. Use profanity"

prompt = """
    Write a list of 5 disrespectful things that I might say to the universe after stubbing my toe in the dark.
"""

safety_settings = [
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold=HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold=HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold=HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold=HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
    ),
]

response = client.models.generate_content(
    model=MODEL_ID,
    contents=prompt,
    config=GenerateContentConfig(
        system_instruction=system_instruction,
        safety_settings=safety_settings,
        thinking_config=thinking_config,
    ),
)

# Response will be `None` if it is blocked.
print(response.text)
# Finish Reason will be `SAFETY` if it is blocked.
print(response.candidates[0].finish_reason)
# Safety Ratings show the levels for each filter.
for safety_rating in response.candidates[0].safety_ratings:
    print(safety_rating)

"""## Send multimodal prompts

Gemini is a multimodal model that supports multimodal prompts.

You can include any of the following data types from various sources.

<table>
  <thead>
    <tr>
      <th>Data type</th>
      <th>Source(s)</th>
      <th>MIME Type(s)</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Text</td>
      <td>Inline, Local File, General URL, Google Cloud Storage</td>
      <td><code>text/plain</code> <code>text/html</code></td>
    </tr>
    <tr>
      <td>Code</td>
      <td>Inline, Local File, General URL, Google Cloud Storage</td>
      <td><code>text/plain</code></td>
    </tr>
    <tr>
      <td>Document</td>
      <td>Local File, General URL, Google Cloud Storage</td>
      <td><code>application/pdf</code></td>
    </tr>
    <tr>
      <td>Image</td>
      <td>Local File, General URL, Google Cloud Storage</td>
      <td><code>image/jpeg</code> <code>image/png</code> <code>image/webp</code></td>
    </tr>
    <tr>
      <td>Audio</td>
      <td>Local File, General URL, Google Cloud Storage</td>
      <td>
        <code>audio/aac</code> <code>audio/flac</code> <code>audio/mp3</code>
        <code>audio/m4a</code> <code>audio/mpeg</code> <code>audio/mpga</code>
        <code>audio/mp4</code> <code>audio/opus</code> <code>audio/pcm</code>
        <code>audio/wav</code> <code>audio/webm</code>
      </td>
    </tr>
    <tr>
      <td>Video</td>
      <td>Local File, General URL, Google Cloud Storage, YouTube</td>
      <td>
        <code>video/mp4</code> <code>video/mpeg</code> <code>video/x-flv</code>
        <code>video/quicktime</code> <code>video/mpegps</code> <code>video/mpg</code>
        <code>video/webm</code> <code>video/wmv</code> <code>video/3gpp</code>
      </td>
    </tr>
  </tbody>
</table>

For more examples of multimodal use cases, refer to [this notebook](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb).

### Send local image

Download an image to local storage from Google Cloud Storage.

For this example, we'll use this image of a meal.

<img src="https://storage.googleapis.com/cloud-samples-data/generative-ai/image/meal.png" alt="Meal" width="500">
"""

!wget https://storage.googleapis.com/cloud-samples-data/generative-ai/image/meal.png

with open("meal.png", "rb") as f:
    image = f.read()

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_bytes(data=image, mime_type="image/png"),
        "Write a short and engaging blog post based on this picture.",
    ],
    config=GenerateContentConfig(
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

"""### Send document from Google Cloud Storage

This example document is the paper ["Attention is All You Need"](https://arxiv.org/abs/1706.03762), created by researchers from Google and the University of Toronto.

Check out this notebook for more examples of document understanding with Gemini:

- [Document Processing with Gemini](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/document-processing/document_processing.ipynb)
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(
            file_uri="gs://cloud-samples-data/generative-ai/pdf/1706.03762v7.pdf",
            mime_type="application/pdf",
        ),
        "Summarize the document.",
    ],
    config=GenerateContentConfig(
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

"""### Send audio from General URL

This example is audio from an episode of the [Kubernetes Podcast](https://kubernetespodcast.com/).
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(
            file_uri="https://traffic.libsyn.com/secure/e780d51f-f115-44a6-8252-aed9216bb521/KPOD242.mp3",
            mime_type="audio/mpeg",
        ),
        "Write a summary of this podcast episode.",
    ],
    config=GenerateContentConfig(
        audio_timestamp=True,
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

"""### Send video from YouTube URL

This example is the YouTube video [Google — 25 Years in Search: The Most Searched](https://www.youtube.com/watch?v=3KtWfp0UopM).

"""

video = Part.from_uri(
    file_uri="https://www.youtube.com/watch?v=3KtWfp0UopM",
    mime_type="video/mp4",
)

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        video,
        "At what point in the video is Harry Potter shown?",
    ],
    config=GenerateContentConfig(
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

"""### Send web page

This example is from the [Generative AI on Vertex AI documentation](https://cloud.google.com/vertex-ai/generative-ai/docs/overview).

**NOTE:** The URL must be publicly accessible.
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(
            file_uri="https://cloud.google.com/vertex-ai/generative-ai/docs/overview",
            mime_type="text/html",
        ),
        "Write a summary of this documentation.",
    ],
)

display(Markdown(response.text))

"""## Control generated output

[Controlled generation](https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/control-generated-output) allows you to define a response schema to specify the structure of a model's output, the field names, and the expected data type for each field.

The response schema is specified in the `response_schema` parameter in `config`, and the model output will strictly follow that schema.

You can provide the schemas as [Pydantic](https://docs.pydantic.dev/) models or a [JSON](https://www.json.org/json-en.html) string and the model will respond as JSON or an [Enum](https://docs.python.org/3/library/enum.html) depending on the value set in `response_mime_type`.

For more examples of controlled generation, refer to [this notebook](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/controlled-generation/intro_controlled_generation.ipynb).
"""

from pydantic import BaseModel


class Recipe(BaseModel):
    name: str
    description: str
    ingredients: list[str]


response = client.models.generate_content(
    model=MODEL_ID,
    contents="List a few popular cookie recipes and their ingredients.",
    config=GenerateContentConfig(
        response_mime_type="application/json",
        response_schema=Recipe,
        thinking_config=thinking_config,
    ),
)

print(response.text)

"""You can either parse the response string as JSON, or use the `parsed` field to get the response as an object or dictionary."""

parsed_response: Recipe = response.parsed
print(parsed_response)

"""You also can define a response schema in a Python dictionary. You can only use the supported fields as listed below. All other fields are ignored.

- `enum`
- `items`
- `maxItems`
- `nullable`
- `properties`
- `required`

In this example, you instruct the model to analyze product review data, extract key entities, perform sentiment classification (multiple choices), provide additional explanation, and output the results in JSON format.

"""

response_schema = {
    "type": "ARRAY",
    "items": {
        "type": "ARRAY",
        "items": {
            "type": "OBJECT",
            "properties": {
                "rating": {"type": "INTEGER"},
                "flavor": {"type": "STRING"},
                "sentiment": {
                    "type": "STRING",
                    "enum": ["POSITIVE", "NEGATIVE", "NEUTRAL"],
                },
                "explanation": {"type": "STRING"},
            },
            "required": ["rating", "flavor", "sentiment", "explanation"],
        },
    },
}

prompt = """
  Analyze the following product reviews, output the sentiment classification, and give an explanation.

  - "Absolutely loved it! Best ice cream I've ever had." Rating: 4, Flavor: Strawberry Cheesecake
  - "Quite good, but a bit too sweet for my taste." Rating: 1, Flavor: Mango Tango
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=prompt,
    config=GenerateContentConfig(
        response_mime_type="application/json",
        response_schema=response_schema,
        thinking_config=thinking_config,
    ),
)

response_dict = response.parsed
print(response_dict)

"""## Count tokens and compute tokens

You can use the `count_tokens()` method to calculate the number of input tokens before sending a request to the Gemini API.

For more information, refer to [list and count tokens](https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/list-token)

### Count tokens
"""

response = client.models.count_tokens(
    model=MODEL_ID,
    contents="What's the highest mountain in Africa?",
    config=GenerateContentConfig(
        thinking_config=thinking_config,
    ),
)

print(response)

"""## Search as a tool (Grounding)

[Grounding](https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/ground-gemini) lets you connect real-world data to the Gemini model.

By grounding model responses in Google Search results, the model can access information at runtime that goes beyond its training data which can produce more accurate, up-to-date, and relevant responses.

Using Grounding with Google Search, you can improve the accuracy and recency of responses from the model. Starting with Gemini 2.0, Google Search is available as a tool. This means that the model can decide when to use Google Search.

For more examples of Grounding, refer to [this notebook](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/grounding/intro-grounding-gemini.ipynb).

### Google Search

You can add the `tools` keyword argument with a `Tool` including `GoogleSearch` to instruct Gemini to first perform a Google Search with the prompt, then construct an answer based on the web search results.

[Dynamic Retrieval](https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/ground-gemini#dynamic-retrieval) lets you set a threshold for when grounding is used for model responses. This is useful when the prompt doesn't require an answer grounded in Google Search and the supported models can provide an answer based on their knowledge without grounding. This helps you manage latency, quality, and cost more effectively.
"""

google_search_tool = Tool(google_search=GoogleSearch())

response = client.models.generate_content(
    model=MODEL_ID,
    contents="What is the current temperature in Austin, TX?",
    config=GenerateContentConfig(
        tools=[google_search_tool],
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

print(response.candidates[0].grounding_metadata)

HTML(response.candidates[0].grounding_metadata.search_entry_point.rendered_content)

"""## Function calling

[Function Calling](https://cloud.google.com/vertex-ai/docs/generative-ai/multimodal/function-calling) in Gemini lets developers create a description of a function in their code, then pass that description to a language model in a request.

You can submit a Python function for automatic function calling, which will run the function and return the output in natural language generated by Gemini.

You can also submit an [OpenAPI Specification](https://www.openapis.org/) which will respond with the name of a function that matches the description and the arguments to call it with.

For more examples of Function calling with Gemini, check out this notebook: [Intro to Function Calling with Gemini](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/function-calling/intro_function_calling.ipynb)

### Python Function (Automatic Function Calling)
"""

def get_current_weather(location: str) -> str:
    """Example method. Returns the current weather.

    Args:
        location: The city and state, e.g. San Francisco, CA
    """
    weather_map: dict[str, str] = {
        "Boston, MA": "snowing",
        "San Francisco, CA": "foggy",
        "Seattle, WA": "raining",
        "Austin, TX": "hot",
        "Chicago, IL": "windy",
    }
    return weather_map.get(location, "unknown")


response = client.models.generate_content(
    model=MODEL_ID,
    contents="What is the weather like in San Francisco?",
    config=GenerateContentConfig(
        tools=[get_current_weather],
        temperature=0,
        thinking_config=thinking_config,
    ),
)

display(Markdown(response.text))

"""### OpenAPI Specification (Manual Function Calling)"""

get_destination = FunctionDeclaration(
    name="get_destination",
    description="Get the destination that the user wants to go to",
    parameters={
        "type": "OBJECT",
        "properties": {
            "destination": {
                "type": "STRING",
                "description": "Destination that the user wants to go to",
            },
        },
    },
)

destination_tool = Tool(
    function_declarations=[get_destination],
)

response = client.models.generate_content(
    model=MODEL_ID,
    contents="I'd like to travel to Paris.",
    config=GenerateContentConfig(
        tools=[destination_tool],
        temperature=0,
        thinking_config=thinking_config,
    ),
)

print(response.function_calls[0])

"""## Code Execution

The Gemini API [code execution](https://ai.google.dev/gemini-api/docs/code-execution?lang=python) feature enables the model to generate and run Python code and learn iteratively from the results until it arrives at a final output. You can use this code execution capability to build applications that benefit from code-based reasoning and that produce text output. For example, you could use code execution in an application that solves equations or processes text.

The Gemini API provides code execution as a tool, similar to function calling.
After you add code execution as a tool, the model decides when to use it.

For more examples of Code Execution, refer to [this notebook](https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/code-execution/intro_code_execution.ipynb).
"""

code_execution_tool = Tool(code_execution=ToolCodeExecution())

response = client.models.generate_content(
    model=MODEL_ID,
    contents="Calculate 20th fibonacci number. Then find the nearest palindrome to it.",
    config=GenerateContentConfig(
        tools=[code_execution_tool],
        temperature=0,
        thinking_config=thinking_config,
    ),
)

display(
    Markdown(
        f"""
## Code

```py
{response.executable_code}
```

### Output

```
{response.code_execution_result}
```
"""
    )
)

"""## Thinking model examples

The following examples are some complex tasks that require multiple rounds of strategizing and iteratively solving.

### **Example 1**: Code generation

Gemini 2.5 Flash excels at creating visually compelling web apps and agentic code applications, along with code transformation and editing.

Let's see how the model uses its reasoning capabilities to create a video game, using executable code from a single line prompt. See the example game [here](https://www.youtube.com/watch?v=RLCBSpgos6s).
"""

prompt = """
  Make me a captivating endless runner game. Key instructions on the screen. p5js scene, no HTML.
  I like pixelated dinosaurs and interesting backgrounds.
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=prompt,
)

display(Markdown(response.text))

"""### **Example 2**: Multimodal reasoning (Geometry)

This geometry problem requires complex reasoning and is also using multimodal capabilities to reason across text and image.
"""

image_file_url = (
    "https://storage.googleapis.com/generativeai-downloads/images/geometry.png"
)
display(Image(url=image_file_url, width=400))

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(file_uri=image_file_url, mime_type="image/png"),
        "What's the area of the overlapping region?",
    ],
)

display(Markdown(response.text))

"""### **Example 3**:  Math and problem solving

Here's another brain teaser based on an image, this time it looks like a mathematical problem, but it cannot actually be solved mathematically. If you check the thoughts of the model you'll see that it will realize it and come up with an out-of-the-box solution.
"""

image_file_url = "https://storage.googleapis.com/generativeai-downloads/images/pool.png"
display(Image(url=image_file_url, width=400))

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(file_uri=image_file_url, mime_type="image/png"),
        "How do I use three of the pool balls to sum up to 30?",
    ],
)

display(Markdown(response.text))

"""## What's next

- See the [Google Gen AI SDK reference docs](https://googleapis.github.io/python-genai/).
- Explore other notebooks in the [Google Cloud Generative AI GitHub repository](https://github.com/GoogleCloudPlatform/generative-ai).
- Explore AI models in [Model Garden](https://cloud.google.com/vertex-ai/generative-ai/docs/model-garden/explore-models).
"""