# -*- coding: utf-8 -*-
"""Gemini 2.5 Pro.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/12_rCTOYs0i5p_G7XsVyR6_pQdBSLQH5s

# Function Calling with Gemini Models: A Comprehensive Tutorial

## Introduction

Function calling allows Gemini models to intelligently call external functions to perform actions beyond text generation. This creates opportunities for building powerful AI agents that can interact with external systems, access real-time data, and perform complex operations based on user requests.

This tutorial will guide you through using function calling with the latest Google GenAI Python SDK, covering basic concepts and advancing to more complex use cases.

## Table of Contents

1. [Setting Up Your Environment](#setting-up-your-environment)
2. [Basic Function Calling](#basic-function-calling)
3. [Working with Function Responses](#working-with-function-responses)
4. [Parallel Function Calling](#parallel-function-calling)
5. [Building a SQL Assistant](#building-a-sql-assistant)
6. [Real-World Use Case: Company News and Insights](#real-world-use-case-company-news-and-insights)
7. [Trip Planning with Complex Function calling](#trip-planning-with-complex-function-calling)
8. [Text-to-SQL with Complementary Function Calling - Business Intelligence Agent](#Text-to-SQL-with-Complementary-Function-Calling)

## Setting Up Your Environment

The Google GenAI SDK provides a unified interface to Gemini models through both the Gemini Developer API and the Gemini API on Vertex AI.

### Installation

First, install the latest version of the Google GenAI SDK:
"""

!pip install --upgrade -q google-genai

"""### Authentication

You can authenticate with either the Gemini Developer API (using an API key) or with Vertex AI.

"""

#### Option 1: Gemini Developer API
import os
from google import genai



# Set up with API key
from google.colab import userdata
API_KEY = userdata.get('GOOGLE_EAP_API')

# os.environ["GOOGLE_API_KEY"] = API_KEY

# Create a client
client = genai.Client(api_key=API_KEY)

# #### Option 2: Vertex AI

# import os
# from google import genai

# # Set environment variables for Vertex AI
# os.environ["GOOGLE_CLOUD_PROJECT"] = "your-project-id"  # Replace with your project ID
# os.environ["GOOGLE_CLOUD_LOCATION"] = "us-central1"  # Or your preferred region
# os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "True"

# # Create a client
# client = genai.Client(
#     vertexai=True,
#     project="your-project-id",
#     location="us-central1"
# )

"""### Choose a Model

For this tutorial, we'll use the Gemini 2.0 Flash model, which provides excellent performance for function calling:
"""

MODEL_ID = "gemini-2.5-pro-exp-03-25"

# Generate a response with function calling
response = client.models.generate_content(
    model=MODEL_ID,
    contents="What is the weather like in san francisco?",
)

response.to_json_dict()

response.json()

"""## Basic Function Calling

Function calling allows your model to identify when to use a function and what arguments to provide. Let's start with a simple weather function example.

### Define a Function

First, define the function that you want the model to call:
"""

def get_current_weather(location: str) -> str:
    """Returns the current weather.

    Args:
        location: The city and state, e.g. San Francisco, CA
    """
    # In a real application, this would call a weather API
    # For demo purposes, we'll return a mock response
    if "san francisco" in location.lower():
        return "Foggy, 15°C"
    elif "new york" in location.lower():
        return "Sunny, 22°C"
    else:
        return "Partly cloudy, 20°C"

from google.genai import types

# Generate a response with function calling
response = client.models.generate_content(
    model=MODEL_ID,
    contents="What is the weather like in san francisco?",
    config=types.GenerateContentConfig(
        tools=[get_current_weather],
    ),
)

print(response.text)

response.to_json_dict()



"""### Using Function Calling in a Single Turn

The model will automatically:
1. Detect that it needs weather information
2. Call your `get_current_weather` function with "Boston" as the argument
3. Incorporate the result into its response

### How It Works

When you pass a Python function to the `tools` parameter:

1. The SDK transforms your function into a `FunctionDeclaration` using the function name, docstring, and parameter type annotations
2. The model determines if the function is needed to answer the user query
3. If needed, the model generates a structured function call with appropriate parameters
4. By default, the SDK automatically executes the function with those parameters
5. The function response is sent back to the model
6. The model incorporates the function response into its final answer

## Working with Function Responses

To better understand the model's reasoning process, you can disable automatic function calling and handle the function calls manually.

"""

# Disable automatic function calling
response = client.models.generate_content(
    model=MODEL_ID,
    contents="What is the weather like in san francisco?",
    config=types.GenerateContentConfig(
        tools=[get_current_weather],
        automatic_function_calling=types.AutomaticFunctionCallingConfig(
            disable=True
        ),
    ),
)

# Extract function calls from the response
function_calls = response.function_calls
function_calls

"""## Parallel Function Calling

Gemini models can call multiple functions in parallel for more complex scenarios. Here's how to set up multiple functions:

"""

def get_current_weather(location: str) -> str:
    """Returns the current weather.

    Args:
        location: The city and state, e.g. San Francisco, CA
    """
    # In a real application, this would call a weather API
    # For demo purposes, we'll return a mock response
    if "san francisco" in location.lower():
        return "Foggy, 15°C"
    elif "new york" in location.lower():
        return "Sunny, 22°C"
    else:
        return "Partly cloudy, 20°C"

def get_population(city: str, country: str) -> int:
    """Returns the population of a city.

    Args:
        city: The name of the city
        country: The country the city is in (default: USA)
    """
    # Mock implementation
    populations = {
        "new york": 8336817,
        "los angeles": 3979576,
        "chicago": 2693976,
        "san francisco": 873965,
    }
    return populations.get(city.lower(), 0)

# Create a list of tools
tools = [get_current_weather, get_population]

# Generate a response using both functions
response = client.models.generate_content(
    model=MODEL_ID,
    contents="Compare the weather and population of New York and San Francisco.",
    config=types.GenerateContentConfig(
        tools=tools,
    ),
)

print(response.text)

response.to_json_dict()



"""### Building a SQL Assistant
One powerful application of function calling is building a natural language interface to databases. Let's create a simple SQL assistant:
"""

import sqlite3

# Create a simple database
conn = sqlite3.connect(':memory:')
cursor = conn.cursor()

# Create sample tables
cursor.execute('''
CREATE TABLE employees (
    id INTEGER PRIMARY KEY,
    name TEXT,
    department TEXT,
    salary REAL
)
''')

# Insert sample data
employees = [
    (1, 'John Smith', 'Engineering', 85000),
    (2, 'Maria Garcia', 'Marketing', 78000),
    (3, 'Robert Johnson', 'HR', 65000),
    (4, 'Lisa Chen', 'Engineering', 92000),
    (5, 'Michael Brown', 'Finance', 88000),
]

cursor.executemany('INSERT INTO employees VALUES (?, ?, ?, ?)', employees)
conn.commit()

def execute_sql_query(query: str) -> list:
    """Execute an SQL query and return the results.

    Args:
        query: A valid SQL query

    Returns:
        The query results as a list of rows
    """
    try:
        # In a production environment, you'd want to add security checks here
        # print(f"Executing query: {query}")
        cursor.execute(query)
        columns = [desc[0] for desc in cursor.description] if cursor.description else []
        rows = cursor.fetchall()

        # Convert to list of dictionaries for better readability
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))

        return results
    except Exception as e:
        return {"error": str(e)}

MODEL_ID

# Use the SQL assistant
response = client.models.generate_content(
    model=MODEL_ID,
    contents="What are the average salaries by department?",
    config=types.GenerateContentConfig(
        tools=[execute_sql_query],
        system_instruction="Always check the database schema first. The database has an 'employees' table with columns: id, name, department, and salary."
    ),
)

print(response.text)

response.to_json_dict()



"""The model will generate an appropriate SQL query, execute it, and present the results in a user-friendly format.

## Real-World Use Case: Company News and Insights

Let's build a more complex application that retrieves company news and extracts insights:
"""

import requests
from datetime import datetime, timedelta

def get_company_news(company: str, days: int) -> list:
    """Get recent news articles about a company.

    Args:
        company: The name of the company
        days: Number of days to look back (default: 7)

    Returns:
        A list of news articles
    """
    # In a real application, you would use a news API
    # For demo purposes, we'll return mock data

    # Calculate the date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

    # Mock news database
    news_database = {
        "apple": [
            {"title": "Apple Unveils New iPhone Model", "date": "2025-03-22", "source": "Tech Today"},
            {"title": "Apple's AI Strategy Shows Promise", "date": "2025-03-20", "source": "AI Insider"},
            {"title": "Apple Reports Record Quarterly Revenue", "date": "2025-03-15", "source": "Financial Times"},
        ],
        "microsoft": [
            {"title": "Microsoft Cloud Services See 30% Growth", "date": "2025-03-23", "source": "Cloud Report"},
            {"title": "Microsoft Expands AI Research Division", "date": "2025-03-18", "source": "Tech Weekly"},
            {"title": "Microsoft Partners with OpenAI on New Projects", "date": "2025-03-14", "source": "AI News"},
        ],
        "google": [
            {"title": "Google Announces Gemini 3.0", "date": "2025-03-21", "source": "AI Today"},
            {"title": "Google's Quantum Computing Breakthrough", "date": "2025-03-19", "source": "Science Daily"},
            {"title": "Google Cloud Platform Adds New Enterprise Features", "date": "2025-03-16", "source": "Enterprise Tech"},
        ]
    }

    # Get news for the requested company
    company_lower = company.lower()
    if company_lower in news_database:
        # Filter by date range
        filtered_news = []
        for article in news_database[company_lower]:
            article_date = datetime.strptime(article["date"], "%Y-%m-%d")
            if start_date <= article_date <= end_date:
                filtered_news.append(article)
        return filtered_news
    else:
        return []

def parse_sentiment(text: str) -> dict:
    """Analyze the sentiment of the text.

    Args:
        text: The text to analyze

    Returns:
        A dictionary with sentiment scores
    """
    # In a real application, this would use a sentiment analysis API
    # For demo, we'll use a simple keyword approach
    positive_words = ["good", "great", "excellent", "happy", "love"]
    negative_words = ["bad", "terrible", "awful", "unhappy", "hate"]

    positive_count = sum(1 for word in positive_words if word in text.lower())
    negative_count = sum(1 for word in negative_words if word in text.lower())

    if positive_count > negative_count:
        sentiment = "positive"
        score = min(0.5 + (positive_count - negative_count) * 0.1, 0.9)
    elif negative_count > positive_count:
        sentiment = "negative"
        score = min(0.5 + (negative_count - positive_count) * 0.1, 0.9)
    else:
        sentiment = "neutral"
        score = 0.5

    return {
        "sentiment": sentiment,
        "score": score,
        "positive_count": positive_count,
        "negative_count": negative_count
    }

# Create a multi-function system
tools = [get_company_news, parse_sentiment]

# Use both functions in a conversation
response = client.models.generate_content(
    model=MODEL_ID,
    contents="What's the recent news about Apple and what's the general sentiment?",
    config=types.GenerateContentConfig(
        tools=tools,
    ),
)

print(response.text)









"""## Trip Planning with Complex Function calling"""

from datetime import datetime, timedelta
import json
from typing import List, Dict, Any, Optional

# 1. Weather forecasting function
def get_weather_forecast(location: str, date: str) -> Dict[str, Any]:
    """Get weather forecast for a specific location and date.

    Args:
        location: City name (e.g., "Paris", "Tokyo")
        date: Date in YYYY-MM-DD format

    Returns:
        Weather forecast information
    """
    # Mock weather database
    weather_db = {
        "paris": {
            "2025-04-10": {"temp": 18, "condition": "Sunny", "precipitation": 0},
            "2025-04-11": {"temp": 16, "condition": "Partly Cloudy", "precipitation": 20},
            "2025-04-12": {"temp": 15, "condition": "Rainy", "precipitation": 80},
            "2025-04-13": {"temp": 17, "condition": "Cloudy", "precipitation": 30},
        },
        "tokyo": {
            "2025-04-10": {"temp": 22, "condition": "Sunny", "precipitation": 0},
            "2025-04-11": {"temp": 23, "condition": "Sunny", "precipitation": 0},
            "2025-04-12": {"temp": 21, "condition": "Partly Cloudy", "precipitation": 10},
            "2025-04-13": {"temp": 20, "condition": "Rainy", "precipitation": 70},
        },
        "new york": {
            "2025-04-10": {"temp": 15, "condition": "Windy", "precipitation": 0},
            "2025-04-11": {"temp": 17, "condition": "Sunny", "precipitation": 0},
            "2025-04-12": {"temp": 16, "condition": "Cloudy", "precipitation": 40},
            "2025-04-13": {"temp": 14, "condition": "Rainy", "precipitation": 90},
        }
    }

    location_lower = location.lower()
    if location_lower in weather_db and date in weather_db[location_lower]:
        result = weather_db[location_lower][date].copy()
        result["location"] = location
        result["date"] = date
        return result
    else:
        return {"error": f"No forecast available for {location} on {date}", "location": location, "date": date}

# 2. Flight search function
def search_flights(origin: str, destination: str, date: str) -> List[Dict[str, Any]]:
    """Search for flights between origin and destination on a specific date.

    Args:
        origin: Departure city or airport code
        destination: Arrival city or airport code
        date: Date in YYYY-MM-DD format

    Returns:
        List of available flights
    """
    # Mock flight database
    flight_routes = {
        ("new york", "paris"): [
            {"flight_number": "AF123", "airline": "Air France", "departure": "08:30", "arrival": "20:45", "price": 850},
            {"flight_number": "DL234", "airline": "Delta", "departure": "16:45", "arrival": "05:20+1", "price": 790},
            {"flight_number": "UA456", "airline": "United", "departure": "21:15", "arrival": "10:30+1", "price": 920}
        ],
        ("new york", "tokyo"): [
            {"flight_number": "JL005", "airline": "Japan Airlines", "departure": "13:45", "arrival": "16:30+1", "price": 1250},
            {"flight_number": "NH009", "airline": "ANA", "departure": "10:30", "arrival": "13:45+1", "price": 1180}
        ],
        ("paris", "tokyo"): [
            {"flight_number": "AF276", "airline": "Air France", "departure": "12:30", "arrival": "08:45+1", "price": 1050},
            {"flight_number": "JL046", "airline": "Japan Airlines", "departure": "14:15", "arrival": "10:20+1", "price": 1150}
        ]
    }

    # Normalize inputs
    origin_lower = origin.lower()
    destination_lower = destination.lower()

    # Find matching flights
    key = (origin_lower, destination_lower)
    if key in flight_routes:
        flights = flight_routes[key]
        # Add date and route info to each flight
        for flight in flights:
            flight["date"] = date
            flight["origin"] = origin
            flight["destination"] = destination
        return flights
    else:
        # Try reverse route
        key = (destination_lower, origin_lower)
        if key in flight_routes:
            # Suggest the reverse route is available
            return [{"error": f"No direct flights from {origin} to {destination}",
                    "suggestion": f"Flights are available from {destination} to {origin}"}]
        return [{"error": f"No flights found between {origin} and {destination}"}]

# 3. Hotel search function
def search_hotels(location: str, check_in: str, check_out: str, guests: int) -> List[Dict[str, Any]]:
    """Search for hotels in a location for specific dates.

    Args:
        location: City name
        check_in: Check-in date (YYYY-MM-DD)
        check_out: Check-out date (YYYY-MM-DD)
        guests: Number of guests (default: 2)

    Returns:
        List of available hotels
    """
    # Mock hotel database
    hotels_db = {
        "paris": [
            {"name": "Grand Hotel Paris", "stars": 5, "price_per_night": 350, "amenities": ["spa", "restaurant", "pool"]},
            {"name": "Eiffel View Inn", "stars": 4, "price_per_night": 220, "amenities": ["restaurant", "gym"]},
            {"name": "Montmartre Boutique", "stars": 3, "price_per_night": 150, "amenities": ["breakfast"]}
        ],
        "tokyo": [
            {"name": "Tokyo Luxury Suites", "stars": 5, "price_per_night": 400, "amenities": ["spa", "restaurant", "pool", "gym"]},
            {"name": "Shinjuku Central Hotel", "stars": 4, "price_per_night": 250, "amenities": ["restaurant", "laundry"]},
            {"name": "Asakusa Budget Inn", "stars": 3, "price_per_night": 120, "amenities": ["breakfast", "laundry"]}
        ],
        "new york": [
            {"name": "Manhattan Grand", "stars": 5, "price_per_night": 450, "amenities": ["spa", "restaurant", "gym"]},
            {"name": "Central Park Inn", "stars": 4, "price_per_night": 280, "amenities": ["restaurant", "gym", "breakfast"]},
            {"name": "Brooklyn Heights Hotel", "stars": 3, "price_per_night": 180, "amenities": ["breakfast"]}
        ]
    }

    location_lower = location.lower()
    if location_lower in hotels_db:
        # Calculate nights based on dates
        try:
            check_in_date = datetime.strptime(check_in, "%Y-%m-%d")
            check_out_date = datetime.strptime(check_out, "%Y-%m-%d")
            nights = (check_out_date - check_in_date).days

            hotels = hotels_db[location_lower]
            # Add booking details to each hotel
            for hotel in hotels:
                hotel["location"] = location
                hotel["check_in"] = check_in
                hotel["check_out"] = check_out
                hotel["nights"] = nights
                hotel["guests"] = guests
                hotel["total_price"] = hotel["price_per_night"] * nights

            return hotels
        except ValueError:
            return [{"error": "Invalid date format. Please use YYYY-MM-DD format."}]
    else:
        return [{"error": f"No hotels found in {location}"}]

# 4. Attractions function
def get_attractions(location: str) -> List[Dict[str, Any]]:
    """Get tourist attractions for a specific location.

    Args:
        location: City name

    Returns:
        List of attractions and activities
    """
    # Mock attractions database
    attractions_db = {
        "paris": [
            {"name": "Eiffel Tower", "category": "Monument", "rating": 4.7, "price": 25, "time_needed": "3 hours"},
            {"name": "Louvre Museum", "category": "Museum", "rating": 4.8, "price": 17, "time_needed": "4 hours"},
            {"name": "Notre-Dame Cathedral", "category": "Monument", "rating": 4.6, "price": 0, "time_needed": "1 hour"},
            {"name": "Seine River Cruise", "category": "Activity", "rating": 4.5, "price": 35, "time_needed": "1.5 hours"}
        ],
        "tokyo": [
            {"name": "Tokyo Skytree", "category": "Monument", "rating": 4.5, "price": 18, "time_needed": "2 hours"},
            {"name": "Senso-ji Temple", "category": "Cultural", "rating": 4.7, "price": 0, "time_needed": "1.5 hours"},
            {"name": "Tokyo Disneyland", "category": "Theme Park", "rating": 4.8, "price": 75, "time_needed": "Full day"},
            {"name": "Tsukiji Fish Market", "category": "Food", "rating": 4.6, "price": 0, "time_needed": "2 hours"}
        ],
        "new york": [
            {"name": "Statue of Liberty", "category": "Monument", "rating": 4.7, "price": 23, "time_needed": "3 hours"},
            {"name": "Metropolitan Museum of Art", "category": "Museum", "rating": 4.8, "price": 25, "time_needed": "4 hours"},
            {"name": "Central Park", "category": "Park", "rating": 4.9, "price": 0, "time_needed": "2-3 hours"},
            {"name": "Broadway Show", "category": "Entertainment", "rating": 4.8, "price": 120, "time_needed": "3 hours"}
        ]
    }

    location_lower = location.lower()
    if location_lower in attractions_db:
        attractions = attractions_db[location_lower]
        # Add location to each attraction
        for attraction in attractions:
            attraction["location"] = location
        return attractions
    else:
        return [{"error": f"No attractions found for {location}"}]

# 5. Currency conversion function
def convert_currency(amount: float, from_currency: str, to_currency: str) -> Dict[str, Any]:
    """Convert an amount from one currency to another.

    Args:
        amount: Amount to convert
        from_currency: Source currency code (e.g., USD, EUR, JPY)
        to_currency: Target currency code

    Returns:
        Conversion result
    """
    # Mock exchange rates (relative to USD)
    exchange_rates = {
        "USD": 1.0,
        "EUR": 0.92,
        "JPY": 150.25,
        "GBP": 0.79,
        "CAD": 1.35,
        "AUD": 1.48
    }

    from_currency = from_currency.upper()
    to_currency = to_currency.upper()

    if from_currency in exchange_rates and to_currency in exchange_rates:
        # Convert to USD first, then to target currency
        amount_in_usd = amount / exchange_rates[from_currency]
        converted_amount = amount_in_usd * exchange_rates[to_currency]

        return {
            "original_amount": amount,
            "from_currency": from_currency,
            "to_currency": to_currency,
            "converted_amount": round(converted_amount, 2),
            "exchange_rate": round(exchange_rates[to_currency] / exchange_rates[from_currency], 4)
        }
    else:
        return {"error": f"Currency not supported. Supported currencies: {', '.join(exchange_rates.keys())}"}

# 6. Trip planning function that uses the results of other functions
def plan_trip_itinerary(location: str, start_date: str, end_date: str) -> Dict[str, Any]:
    """Create a daily itinerary for a trip based on location and dates.

    Args:
        location: Destination city
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)

    Returns:
        A day-by-day itinerary
    """
    try:
        # Parse dates
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")

        # Get attractions for the location
        attractions = get_attractions(location)

        if isinstance(attractions, list) and len(attractions) > 0 and "error" not in attractions[0]:
            # Create itinerary
            itinerary = {"location": location, "start_date": start_date, "end_date": end_date, "days": []}
            current_date = start

            # Create itinerary for each day
            day_count = 0
            attractions_per_day = min(2, len(attractions))  # Limit attractions per day

            while current_date <= end and day_count < 7:  # Limit to 7 days max
                date_str = current_date.strftime("%Y-%m-%d")

                # Get weather for the day
                weather = get_weather_forecast(location, date_str)

                # Select attractions for the day
                day_attractions = attractions[(day_count * attractions_per_day) % len(attractions):
                                             ((day_count * attractions_per_day) % len(attractions)) + attractions_per_day]

                # Create day plan
                day_plan = {
                    "date": date_str,
                    "day": (current_date - start).days + 1,
                    "weather": weather,
                    "morning": day_attractions[0]["name"] if day_attractions else "Free time",
                    "afternoon": day_attractions[1]["name"] if len(day_attractions) > 1 else "Free time",
                    "evening": "Dinner and relaxation"
                }

                itinerary["days"].append(day_plan)
                current_date += timedelta(days=1)
                day_count += 1

            return itinerary
        else:
            return {"error": f"Could not create itinerary for {location}", "reason": "No attractions found"}
    except ValueError:
        return {"error": "Invalid date format. Please use YYYY-MM-DD format."}
    except Exception as e:
        return {"error": f"Failed to create itinerary: {str(e)}"}

# Create a list of tools for the travel assistant
travel_tools = [
    get_weather_forecast,
    search_flights,
    search_hotels,
    get_attractions,
    convert_currency,
    plan_trip_itinerary
]

# Now let's use our travel assistant with a complex query that will require both parallel and sequential function calls
response = client.models.generate_content(
    model=MODEL_ID,
    contents="""
    I'm planning a trip from New York to Paris from April 10-13, 2025. I need help with:
    1. Finding flights from New York to Paris on April 10
    2. Checking the weather during my stay
    3. Recommending hotels for 2 people
    4. Planning a daily itinerary
    5. Converting 1000 USD to EUR for my budget
    """,
    config=types.GenerateContentConfig(
        tools=travel_tools,
        system_instruction="""
        You are a travel planning assistant. Help users plan trips by using the available tools to gather information
        and create comprehensive travel plans. When a user asks about a trip:
        1. First gather necessary flight, weather, and accommodation information using parallel function calls
        2. Then use sequential calls to build on that information (like creating itineraries based on weather)
        3. Always convert currency when budget information is provided
        4. Provide a well-structured travel plan with all relevant details
        """
    ),
)

print(response.text)

response.to_json_dict()

"""## Text-to-SQL with Complementary Function Calling
### Building a Business Intelligence Dashboard
This example demonstrates how to combine text-to-SQL capabilities with other function calling to create a comprehensive business intelligence tool. Users can query internal company data through SQL while also accessing external market information and performing analysis.
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json
from typing import List, Dict, Any, Optional

# Set up a sample SQLite database with company data
conn = sqlite3.connect(':memory:')
cursor = conn.cursor()

# Create tables for our company database
def setup_company_database():
    """Set up sample database tables with company data"""

    # Create sales table
    cursor.execute('''
    CREATE TABLE sales (
        id INTEGER PRIMARY KEY,
        product_id INTEGER,
        region_id INTEGER,
        employee_id INTEGER,
        sale_date TEXT,
        quantity INTEGER,
        revenue REAL
    )
    ''')

    # Create products table
    cursor.execute('''
    CREATE TABLE products (
        id INTEGER PRIMARY KEY,
        name TEXT,
        category TEXT,
        unit_price REAL,
        launch_date TEXT
    )
    ''')

    # Create regions table
    cursor.execute('''
    CREATE TABLE regions (
        id INTEGER PRIMARY KEY,
        name TEXT,
        country TEXT
    )
    ''')

    # Create employees table
    cursor.execute('''
    CREATE TABLE employees (
        id INTEGER PRIMARY KEY,
        name TEXT,
        department TEXT,
        title TEXT,
        hire_date TEXT
    )
    ''')

    # Insert sample data

    # Products data
    products = [
        (1, 'Laptop Pro', 'Electronics', 1299.99, '2022-03-15'),
        (2, 'Smartphone X', 'Electronics', 899.99, '2022-05-20'),
        (3, 'Wireless Earbuds', 'Electronics', 149.99, '2022-01-10'),
        (4, 'Smart Watch', 'Electronics', 249.99, '2022-08-03'),
        (5, 'Coffee Maker', 'Home', 89.99, '2021-11-25'),
        (6, 'Blender', 'Home', 69.99, '2021-09-12'),
        (7, 'Office Chair', 'Furniture', 199.99, '2021-07-08'),
        (8, 'Desk Lamp', 'Furniture', 49.99, '2021-10-15')
    ]
    cursor.executemany('INSERT INTO products VALUES (?, ?, ?, ?, ?)', products)

    # Regions data
    regions = [
        (1, 'Northeast', 'USA'),
        (2, 'Midwest', 'USA'),
        (3, 'South', 'USA'),
        (4, 'West', 'USA'),
        (5, 'Central', 'Canada'),
        (6, 'Eastern', 'Canada'),
        (7, 'Western Europe', 'EU'),
        (8, 'Eastern Europe', 'EU')
    ]
    cursor.executemany('INSERT INTO regions VALUES (?, ?, ?)', regions)

    # Employees data
    employees = [
        (1, 'John Smith', 'Sales', 'Sales Representative', '2020-05-15'),
        (2, 'Maria Garcia', 'Sales', 'Sales Manager', '2019-03-10'),
        (3, 'Robert Johnson', 'Marketing', 'Marketing Specialist', '2021-01-20'),
        (4, 'Lisa Chen', 'Product', 'Product Manager', '2020-11-08'),
        (5, 'Michael Brown', 'Finance', 'Financial Analyst', '2018-07-22'),
        (6, 'Emma Wilson', 'Sales', 'Sales Representative', '2021-08-15'),
        (7, 'James Lee', 'Product', 'Product Designer', '2022-02-01'),
        (8, 'Sophia Martinez', 'Marketing', 'Marketing Manager', '2019-09-30')
    ]
    cursor.executemany('INSERT INTO employees VALUES (?, ?, ?, ?, ?)', employees)

    # Generate sample sales data
    import random
    from datetime import datetime, timedelta

    # Start date for sales data
    start_date = datetime(2023, 1, 1)

    # Generate 500 sample sales records
    sales_data = []
    sales_id = 1

    for _ in range(500):
        product_id = random.randint(1, 8)
        region_id = random.randint(1, 8)
        employee_id = random.randint(1, 8)

        # Random date in 2023
        days_offset = random.randint(0, 365)
        sale_date = (start_date + timedelta(days=days_offset)).strftime('%Y-%m-%d')

        # Random quantity between 1 and 10
        quantity = random.randint(1, 10)

        # Revenue based on product price and quantity with some randomness
        product_price = next(p[3] for p in products if p[0] == product_id)
        base_revenue = product_price * quantity
        revenue = round(base_revenue * random.uniform(0.95, 1.05), 2)  # +/- 5% random variation

        sales_data.append((sales_id, product_id, region_id, employee_id, sale_date, quantity, revenue))
        sales_id += 1

    cursor.executemany('INSERT INTO sales VALUES (?, ?, ?, ?, ?, ?, ?)', sales_data)

    # Commit the changes
    conn.commit()

    return "Database initialized with company data"

# Run the setup
setup_company_database()

# 1. SQL execution function
def execute_sql_query(query: str) -> List[Dict[str, Any]]:
    """Execute an SQL query against the company database and return results.

    Args:
        query: A valid SQL query

    Returns:
        The query results as a list of dictionaries
    """
    try:
        # Execute the query
        cursor.execute(query)

        # Get column names
        columns = [desc[0] for desc in cursor.description] if cursor.description else []

        # Fetch results
        rows = cursor.fetchall()

        # Convert to list of dictionaries
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))

        return results
    except Exception as e:
        return [{"error": f"SQL Error: {str(e)}"}]

# 2. Get database schema function
def get_database_schema(table_name: str) -> Dict[str, Any]:
    """Retrieve the schema of the company database.

    Args:
        table_name: Table name to get specific schema, or empty string for all tables

    Returns:
        Database schema information
    """
    # Get schema based on table name parameter
    schema = {"tables": []}

    if table_name == "":
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        for table in tables:
            table_name = table[0]

            # Get column information for this table
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()

            table_schema = {
                "name": table_name,
                "columns": []
            }

            for col in columns:
                table_schema["columns"].append({
                    "name": col[1],
                    "type": col[2],
                    "primary_key": bool(col[5])
                })

            schema["tables"].append(table_schema)
    else:
        # Get specific table schema
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()

        if columns:  # Make sure the table exists
            table_schema = {
                "name": table_name,
                "columns": []
            }

            for col in columns:
                table_schema["columns"].append({
                    "name": col[1],
                    "type": col[2],
                    "primary_key": bool(col[5])
                })

            schema["tables"].append(table_schema)

    return schema

# 3. Market data function
def get_market_data(company: str, category: str):
    """Retrieve market data for a company or product category.

    Args:
        company: Company name (optional)
        category: Product category (optional)

    Returns:
        Market data and insights
    """
    # Mock market data
    market_data = {
        "Electronics": {
            "market_size": "$1.5 trillion",
            "growth_rate": 8.5,
            "top_players": ["Apple", "Samsung", "Dell", "HP", "Lenovo"],
            "trends": ["AI integration", "Eco-friendly materials", "Modular design"],
            "future_outlook": "Strong growth expected through 2030"
        },
        "Home": {
            "market_size": "$520 billion",
            "growth_rate": 4.2,
            "top_players": ["Dyson", "Cuisinart", "KitchenAid", "Breville", "Ninja"],
            "trends": ["Smart home integration", "Energy efficiency", "Premium aesthetics"],
            "future_outlook": "Steady growth tied to housing market"
        },
        "Furniture": {
            "market_size": "$650 billion",
            "growth_rate": 5.1,
            "top_players": ["IKEA", "Ashley Furniture", "Herman Miller", "Steelcase", "La-Z-Boy"],
            "trends": ["Sustainable materials", "Ergonomic design", "Multi-functional pieces"],
            "future_outlook": "Moderate growth with focus on sustainability"
        }
    }

    companies = {
        "apple": {"category": "Electronics", "market_share": 15.3, "stock_price": 187.45, "annual_revenue": "$394.3 billion"},
        "samsung": {"category": "Electronics", "market_share": 14.2, "stock_price": 47.90, "annual_revenue": "$241.5 billion"},
        "ikea": {"category": "Furniture", "market_share": 18.7, "stock_price": "Private", "annual_revenue": "$44.6 billion"},
        "dyson": {"category": "Home", "market_share": 12.5, "stock_price": "Private", "annual_revenue": "$9.7 billion"}
    }

    if company and company.lower() in companies:
        company_info = companies[company.lower()]
        category_info = market_data[company_info["category"]]

        return {
            "company": company,
            "company_info": company_info,
            "category": company_info["category"],
            "category_info": category_info
        }
    elif category and category in market_data:
        return {
            "category": category,
            "category_info": market_data[category]
        }
    el    # If both parameters are empty strings, return available options
    if company == "" and category == "":
        return {
            "available_categories": list(market_data.keys()),
            "available_companies": list(companies.keys())
        }
    else:
        return {"error": f"No market data found for the specified parameters"}

# 4. Sales analysis function
def analyze_sales_trend(product_category: str, region: str, timeframe: str) -> Dict[str, Any]:
    """Analyze sales trends from the database.

    Args:
        product_category: Product category to analyze
        region: Sales region to analyze
        timeframe: Time period for analysis (e.g., "last 12 months", "last 6 months", "last 3 months", "year to date")

    Returns:
        Sales trend analysis
    """
    try:
        # Build the SQL query based on parameters
        base_query = """
            SELECT
                strftime('%Y-%m', sales.sale_date) as month,
                SUM(sales.revenue) as total_revenue,
                SUM(sales.quantity) as total_units
            FROM sales
            JOIN products ON sales.product_id = products.id
            JOIN regions ON sales.region_id = regions.id
        """

        conditions = []
        if product_category:
            conditions.append(f"products.category = '{product_category}'")

        if region:
            conditions.append(f"regions.name = '{region}' OR regions.country = '{region}'")

        # Handle timeframe
        today = datetime.now()
        if timeframe == "last 12 months":
            start_date = (today - timedelta(days=365)).strftime('%Y-%m-%d')
            conditions.append(f"sales.sale_date >= '{start_date}'")
        elif timeframe == "last 6 months":
            start_date = (today - timedelta(days=180)).strftime('%Y-%m-%d')
            conditions.append(f"sales.sale_date >= '{start_date}'")
        elif timeframe == "last 3 months":
            start_date = (today - timedelta(days=90)).strftime('%Y-%m-%d')
            conditions.append(f"sales.sale_date >= '{start_date}'")
        elif timeframe == "year to date":
            start_date = datetime(today.year, 1, 1).strftime('%Y-%m-%d')
            conditions.append(f"sales.sale_date >= '{start_date}'")
        else:
            # Default to last 12 months if timeframe is not recognized
            start_date = (today - timedelta(days=365)).strftime('%Y-%m-%d')
            conditions.append(f"sales.sale_date >= '{start_date}'")

        # Add WHERE clause if conditions exist
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)

        # Group by month and order
        base_query += " GROUP BY month ORDER BY month"

        # Execute query
        cursor.execute(base_query)
        columns = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()

        # Prepare the result
        sales_by_month = [dict(zip(columns, row)) for row in rows]

        # Calculate overall metrics
        total_revenue = sum(month["total_revenue"] for month in sales_by_month)
        total_units = sum(month["total_units"] for month in sales_by_month)
        avg_monthly_revenue = total_revenue / len(sales_by_month) if sales_by_month else 0

        # Calculate growth metrics if we have at least 2 months of data
        growth_rate = None
        if len(sales_by_month) >= 2:
            first_month = sales_by_month[0]["total_revenue"]
            last_month = sales_by_month[-1]["total_revenue"]
            growth_rate = ((last_month / first_month) - 1) * 100 if first_month > 0 else 0

        # Return the analysis
        result = {
            "timeframe": timeframe,
            "total_revenue": round(total_revenue, 2),
            "total_units": total_units,
            "avg_monthly_revenue": round(avg_monthly_revenue, 2),
            "growth_rate": round(growth_rate, 2) if growth_rate is not None else None,
            "monthly_data": sales_by_month
        }

        if product_category:
            result["product_category"] = product_category

        if region:
            result["region"] = region

        return result
    except Exception as e:
        return {"error": f"Analysis failed: {str(e)}"}

# 5. Competitor analysis function
def analyze_competitors(category: str) -> Dict[str, Any]:
    """Provide competitive analysis for a product category.

    Args:
        category: Product category to analyze

    Returns:
        Competitive landscape analysis
    """
    # Mock competitor data
    competitor_analysis = {
        "Electronics": {
            "competitors": [
                {"name": "TechGiant", "market_share": 22.5, "strengths": ["Brand recognition", "R&D budget", "Distribution"], "weaknesses": ["Premium pricing", "Less customization"]},
                {"name": "ValueTech", "market_share": 18.3, "strengths": ["Affordable pricing", "Wide product range"], "weaknesses": ["Lower build quality", "Customer service"]},
                {"name": "InnovateCorp", "market_share": 11.7, "strengths": ["Cutting-edge features", "Design"], "weaknesses": ["Limited availability", "Higher failure rates"]}
            ],
            "market_dynamics": "Highly competitive with rapid innovation cycles",
            "entry_barriers": "High due to IP requirements and scale advantages",
            "disruption_risk": "Medium-high due to AI and new materials technology"
        },
        "Home": {
            "competitors": [
                {"name": "HomeEssentials", "market_share": 15.2, "strengths": ["Brand trust", "Retail presence"], "weaknesses": ["Slow innovation", "Traditional designs"]},
                {"name": "SmartLife", "market_share": 12.8, "strengths": ["Smart integration", "Modern design"], "weaknesses": ["Higher prices", "Dependency on WiFi"]},
                {"name": "EcoHome", "market_share": 9.5, "strengths": ["Sustainability focus", "Energy efficiency"], "weaknesses": ["Limited product range", "Availability"]}
            ],
            "market_dynamics": "Steady with increasing smart home integration",
            "entry_barriers": "Medium - brand recognition important but new entrants possible",
            "disruption_risk": "Medium due to smart home and IoT integration"
        },
        "Furniture": {
            "competitors": [
                {"name": "LivingSpaces", "market_share": 14.3, "strengths": ["Store experience", "Variety"], "weaknesses": ["Delivery times", "Assembly complexity"]},
                {"name": "ModernDesign", "market_share": 11.9, "strengths": ["Aesthetic appeal", "Quality materials"], "weaknesses": ["Premium pricing", "Limited styles"]},
                {"name": "ValueFurnish", "market_share": 16.8, "strengths": ["Affordability", "Fast delivery"], "weaknesses": ["Durability", "Limited warranty"]}
            ],
            "market_dynamics": "Shifting to online with showroom experiences",
            "entry_barriers": "Low to medium with opportunities in niche segments",
            "disruption_risk": "Low to medium, primarily from supply chain innovations"
        }
    }

    if category in competitor_analysis:
        return {
            "category": category,
            "analysis": competitor_analysis[category]
        }
    else:
        return {"error": f"No competitor analysis available for {category}", "available_categories": list(competitor_analysis.keys())}

# 6. Generate business recommendations
def generate_recommendations(product_category: str, sales_data: str, market_data: str, competitor_data: str) -> Dict[str, Any]:
    """Generate business recommendations based on combined insights.

    Args:
        product_category: Product category to analyze
        sales_data: JSON string containing internal sales data analysis
        market_data: JSON string containing external market information
        competitor_data: JSON string containing competitive landscape analysis

    Returns:
        Strategic business recommendations
    """
    # Parse the JSON strings into dictionaries
    sales_data = json.loads(sales_data)
    market_data = json.loads(market_data)
    competitor_data = json.loads(competitor_data)
    # Check for errors in input data
    errors = []
    if isinstance(sales_data, dict) and "error" in sales_data:
        errors.append(f"Sales data error: {sales_data['error']}")
    if isinstance(market_data, dict) and "error" in market_data:
        errors.append(f"Market data error: {market_data['error']}")
    if isinstance(competitor_data, dict) and "error" in competitor_data:
        errors.append(f"Competitor data error: {competitor_data['error']}")

    if errors:
        return {"error": "Cannot generate recommendations due to missing data", "details": errors}

    # Extract key metrics for analysis with safer access
    internal_growth = 0
    if isinstance(sales_data, dict) and "growth_rate" in sales_data:
        internal_growth = sales_data["growth_rate"]

    market_growth = 0
    market_trends = []
    if isinstance(market_data, dict) and "category_info" in market_data:
        category_info = market_data["category_info"]
        if isinstance(category_info, dict):
            if "growth_rate" in category_info:
                market_growth = category_info["growth_rate"]
            if "trends" in category_info and isinstance(category_info["trends"], list):
                market_trends = category_info["trends"]

    top_competitors = []
    if isinstance(competitor_data, dict) and "analysis" in competitor_data:
        analysis = competitor_data["analysis"]
        if isinstance(analysis, dict) and "competitors" in analysis and isinstance(analysis["competitors"], list):
            top_competitors = analysis["competitors"]

    # Generate recommendations based on data comparison
    recommendations = []

    # Growth comparison
    if internal_growth is not None and market_growth is not None:
        if internal_growth < market_growth - 2:
            recommendations.append({
                "type": "growth",
                "title": "Address Growth Gap",
                "description": f"Our growth ({internal_growth}%) is below market average ({market_growth}%). Consider product refresh and marketing initiatives.",
                "priority": "High"
            })
        elif internal_growth > market_growth + 2:
            recommendations.append({
                "type": "growth",
                "title": "Capitalize on Momentum",
                "description": f"Our growth ({internal_growth}%) exceeds market average ({market_growth}%). Consider expanding product line and entering new markets.",
                "priority": "Medium"
            })

    # Trend alignment
    if market_trends:
        recommendations.append({
            "type": "innovation",
            "title": "Align with Market Trends",
            "description": f"Incorporate key market trends: {', '.join(market_trends[:3])}",
            "priority": "Medium"
        })

    # Competitive positioning
    if top_competitors:
        # Find largest competitor
        largest_competitor = max(top_competitors, key=lambda x: x.get("market_share", 0))
        largest_competitor_strengths = largest_competitor.get("strengths", [])

        if largest_competitor_strengths:
            recommendations.append({
                "type": "competitive",
                "title": "Address Competitive Challenges",
                "description": f"Develop strategies to counter {largest_competitor['name']}'s key strengths: {', '.join(largest_competitor_strengths[:2])}",
                "priority": "High"
            })

    # Pricing strategy (based on revenue per unit from sales data)
    if "total_revenue" in sales_data and "total_units" in sales_data and sales_data["total_units"] > 0:
        avg_unit_price = sales_data["total_revenue"] / sales_data["total_units"]
        recommendations.append({
            "type": "pricing",
            "title": "Review Pricing Strategy",
            "description": f"Current average unit price is ${avg_unit_price:.2f}. Analyze price elasticity to optimize revenue.",
            "priority": "Medium"
        })

    # Return recommendations with context
    return {
        "product_category": product_category,
        "recommendations": recommendations,
        "summary": f"Generated {len(recommendations)} recommendations based on internal sales data and external market intelligence.",
        "data_sources": {
            "internal_sales": bool(sales_data),
            "market_data": bool(market_data),
            "competitor_analysis": bool(competitor_data)
        }
    }

# Create a list of tools for our BI system with properly declared functions
bi_tools = [
    execute_sql_query,
    get_database_schema,
    get_market_data,
    analyze_sales_trend,
    analyze_competitors,
    generate_recommendations
]

# Example complex query that will require SQL and function calling
response = client.models.generate_content(
    model=MODEL_ID,
    contents="""
    I need a comprehensive analysis of our Electronics product category:
    1. How are our Electronics sales doing compared to other categories?
    2. What's the market outlook for Electronics?
    3. Who are our main competitors in this space?
    """,
    config=types.GenerateContentConfig(
        tools=bi_tools,
        system_instruction="""
        You are a business intelligence assistant with access to company data and market intelligence.
        When users ask about company performance and market conditions:
        1. First query the database using SQL to retrieve internal company data
        2. Then get relevant external market and competitor data using function calls
        3. Present a well-structured analysis with data-backed insights

        The company database has the following tables:
        - sales: Records of sales transactions (id, product_id, region_id, employee_id, sale_date, quantity, revenue)
        - products: Product catalog (id, name, category, unit_price, launch_date)
        - regions: Sales regions (id, name, country)
        - employees: Employee information (id, name, department, title, hire_date)

        Always explain your reasoning and the SQL queries you're using.
        """
    ),
)

print(response.text)

response.to_json_dict()

