# Django App Generator Environment Variables
# Copy this file to .env and fill in your values

# Required: Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Model Configuration
MODEL_ID=gemini-2.5-flash-preview-05-20

# Optional: Vertex AI Configuration (for enterprise users)
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_CLOUD_LOCATION=us-central1
# GOOGLE_GENAI_USE_VERTEXAI=True

# Optional: Logging Configuration
# LOG_LEVEL=INFO
# LOG_FILE=django_generator.log
