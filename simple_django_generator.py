#!/usr/bin/env python3
"""
Simple Django App Generator - WORKING VERSION

This is a simplified version that actually works by using:
1. Directory-based app discovery
2. Template-based code generation
3. No complex AI parsing (which was failing)
"""

import os
import sys
import subprocess
import re
from pathlib import Path
from typing import List, Dict
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SimpleDjangoGenerator:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.docs_dir = self.project_root / "docs"
        self.manage_py = self.project_root / "manage.py"
        self.settings_file = self.project_root / "autoerp" / "settings.py"

        # Validate project structure
        if not self.manage_py.exists():
            raise FileNotFoundError(
                "manage.py not found. Run from Django project root."
            )
        if not self.settings_file.exists():
            raise FileNotFoundError("autoerp/settings.py not found.")
        if not self.docs_dir.exists():
            raise FileNotFoundError("docs/ directory not found.")

        logger.info(f"✅ Simple Django Generator initialized")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"Documentation directory: {self.docs_dir}")

    def discover_apps(self) -> List[str]:
        """Discover Django apps from documentation structure"""
        logger.info("🔍 Discovering Django apps from documentation...")

        apps = []
        skip_apps = ["scheduler", "sys_admin", "mr_office"]

        for item in self.docs_dir.iterdir():
            if item.is_dir() and not item.name.startswith("."):
                if item.name not in skip_apps:
                    # Count markdown files to ensure it's a substantial app
                    md_files = list(item.rglob("*.md"))
                    if len(md_files) >= 3:  # At least 3 markdown files
                        apps.append(item.name)

        logger.info(f"✅ Discovered {len(apps)} Django apps: {apps}")
        return apps

    def create_django_app(self, app_name: str) -> bool:
        """Create Django app using manage.py"""
        logger.info(f"📦 Creating Django app: {app_name}")

        app_path = self.project_root / app_name
        if app_path.exists():
            logger.info(f"App {app_name} already exists, skipping creation")
            return True

        try:
            result = subprocess.run(
                [sys.executable, "manage.py", "startapp", app_name],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True,
            )
            logger.info(f"✅ Created Django app: {app_name}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create app {app_name}: {e}")
            return False

    def generate_basic_models(self, app_name: str) -> str:
        """Generate basic models based on documentation structure"""
        app_docs = self.docs_dir / app_name

        models = []
        models.append("from django.db import models")
        models.append("from django.contrib.auth.models import User")
        models.append("")

        # Look for common patterns in documentation
        if (app_docs / "masters").exists():
            models.append("# Master Data Models")

            # Category model (common in many apps)
            if any((app_docs / "masters").rglob("*category*")):
                models.append(
                    """
class Category(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "Categories"
    
    def __str__(self):
        return self.name
"""
                )

            # Product/Item model (for inventory-related apps)
            if "inventory" in app_name or "sales" in app_name or "material" in app_name:
                models.append(
                    """
class Product(models.Model):
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=50, unique=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True, blank=True)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.code} - {self.name}"
"""
                )

        # Customer model for sales apps
        if "sales" in app_name or "customer" in app_name:
            models.append(
                """
class Customer(models.Model):
    name = models.CharField(max_length=200)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
"""
            )

        return "\n".join(models)

    def generate_basic_views(self, app_name: str) -> str:
        """Generate basic views"""
        views = []
        views.append("from django.shortcuts import render, get_object_or_404, redirect")
        views.append(
            "from django.views.generic import ListView, DetailView, CreateView, UpdateView"
        )
        views.append("from django.contrib import messages")
        views.append("from .models import *")
        views.append("")

        views.append(
            f"""
def {app_name}_dashboard(request):
    \"\"\"Dashboard view for {app_name} app\"\"\"
    context = {{
        'title': '{app_name.title()} Dashboard',
        'app_name': '{app_name}'
    }}
    return render(request, '{app_name}/dashboard.html', context)

class CategoryListView(ListView):
    model = Category
    template_name = '{app_name}/category_list.html'
    context_object_name = 'categories'
    paginate_by = 20

class CategoryCreateView(CreateView):
    model = Category
    template_name = '{app_name}/category_form.html'
    fields = ['name', 'description', 'is_active']
    success_url = '/{app_name}/categories/'
"""
        )

        return "\n".join(views)

    def generate_basic_urls(self, app_name: str) -> str:
        """Generate basic URLs"""
        urls = []
        urls.append("from django.urls import path")
        urls.append("from . import views")
        urls.append("")
        urls.append(f"app_name = '{app_name}'")
        urls.append("")
        urls.append("urlpatterns = [")
        urls.append(f"    path('', views.{app_name}_dashboard, name='dashboard'),")
        urls.append(
            "    path('categories/', views.CategoryListView.as_view(), name='category_list'),"
        )
        urls.append(
            "    path('categories/new/', views.CategoryCreateView.as_view(), name='category_create'),"
        )
        urls.append("]")

        return "\n".join(urls)

    def generate_basic_admin(self, app_name: str) -> str:
        """Generate basic admin"""
        admin = []
        admin.append("from django.contrib import admin")
        admin.append("from .models import *")
        admin.append("")
        admin.append("@admin.register(Category)")
        admin.append("class CategoryAdmin(admin.ModelAdmin):")
        admin.append("    list_display = ['name', 'is_active', 'created_at']")
        admin.append("    list_filter = ['is_active', 'created_at']")
        admin.append("    search_fields = ['name']")

        return "\n".join(admin)

    def generate_basic_template(self, app_name: str) -> str:
        """Generate basic dashboard template"""
        template = f"""
{{% extends "core/base.html" %}}

{{% block title %}}{app_name.title()} Dashboard{{% endblock %}}

{{% block content %}}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>{app_name.title()} Dashboard</h1>
            <p>Welcome to the {app_name} management system.</p>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Categories</h5>
                            <p class="card-text">Manage categories</p>
                            <a href="{{% url '{app_name}:category_list' %}}" class="btn btn-primary">View Categories</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{% endblock %}}
"""
        return template

    def create_app_structure(self, app_name: str):
        """Create organized app structure with generated code"""
        logger.info(f"📁 Creating structure for app: {app_name}")

        app_path = self.project_root / app_name

        # Create directories
        dirs = [
            "models",
            "views",
            "forms",
            "urls",
            "templates",
            "static",
            "admin",
            "tests",
        ]
        for dir_name in dirs:
            dir_path = app_path / dir_name
            dir_path.mkdir(exist_ok=True)

            # Create __init__.py files
            init_file = dir_path / "__init__.py"
            if not init_file.exists():
                init_file.touch()

        # Generate and write code files

        # Models - write to models.py (overwrite Django default)
        models_content = self.generate_basic_models(app_name)
        (app_path / "models.py").write_text(models_content)

        # Views - write to views.py (overwrite Django default)
        views_content = self.generate_basic_views(app_name)
        (app_path / "views.py").write_text(views_content)

        # URLs - create urls.py
        urls_content = self.generate_basic_urls(app_name)
        (app_path / "urls.py").write_text(urls_content)

        # Admin - write to admin.py (overwrite Django default)
        admin_content = self.generate_basic_admin(app_name)
        (app_path / "admin.py").write_text(admin_content)

        # Templates
        template_dir = app_path / "templates" / app_name
        template_dir.mkdir(parents=True, exist_ok=True)

        dashboard_template = self.generate_basic_template(app_name)
        (template_dir / "dashboard.html").write_text(dashboard_template)

        logger.info(f"✅ Created structure for app: {app_name}")

    def update_settings(self, app_names: List[str]):
        """Update Django settings.py with new apps"""
        logger.info(f"📝 Updating settings.py with {len(app_names)} apps")

        try:
            settings_content = self.settings_file.read_text()

            # Find INSTALLED_APPS
            pattern = r"INSTALLED_APPS\s*=\s*\[(.*?)\]"
            match = re.search(pattern, settings_content, re.DOTALL)

            if match:
                current_apps = match.group(1)

                # Add new apps
                new_apps_str = ""
                for app_name in app_names:
                    if (
                        f"'{app_name}'" not in current_apps
                        and f'"{app_name}"' not in current_apps
                    ):
                        new_apps_str += f"    '{app_name}',\n"

                if new_apps_str:
                    # Insert before the closing bracket
                    updated_apps = current_apps.rstrip() + "\n" + new_apps_str
                    new_settings = settings_content.replace(
                        f"INSTALLED_APPS = [{current_apps}]",
                        f"INSTALLED_APPS = [{updated_apps}]",
                    )

                    self.settings_file.write_text(new_settings)
                    logger.info(f"✅ Updated settings.py with apps: {app_names}")
                else:
                    logger.info("ℹ️ All apps already in settings.py")
            else:
                logger.error("Could not find INSTALLED_APPS in settings.py")

        except Exception as e:
            logger.error(f"Failed to update settings.py: {e}")

    def generate_all_apps(self) -> Dict[str, bool]:
        """Generate all Django apps"""
        logger.info("🚀 Starting simple Django app generation...")

        # Discover apps
        apps = self.discover_apps()

        if not apps:
            logger.warning("No apps discovered")
            return {}

        results = {}
        generated_apps = []

        for app_name in apps:
            logger.info(f"\n📦 Processing app: {app_name}")

            try:
                # Create Django app
                if self.create_django_app(app_name):
                    # Create organized structure
                    self.create_app_structure(app_name)

                    generated_apps.append(app_name)
                    results[app_name] = True
                    logger.info(f"✅ Successfully created app: {app_name}")
                else:
                    logger.error(f"❌ Failed to create app: {app_name}")
                    results[app_name] = False

            except Exception as e:
                logger.error(f"❌ Error processing app {app_name}: {e}")
                results[app_name] = False

        # Update settings.py
        if generated_apps:
            self.update_settings(generated_apps)

        # Summary
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"\n🎉 Generation complete: {successful}/{total} apps successful")

        return results


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Simple Django App Generator - WORKING VERSION"
    )
    parser.add_argument(
        "--generate-apps", action="store_true", help="Generate all Django apps"
    )
    parser.add_argument("--list-apps", action="store_true", help="List discovered apps")
    parser.add_argument("--app", type=str, help="Generate specific app")

    args = parser.parse_args()

    try:
        generator = SimpleDjangoGenerator()

        if args.list_apps:
            apps = generator.discover_apps()
            print(f"\n🔍 Discovered {len(apps)} Django apps:")
            for app in apps:
                print(f"  - {app}")

        elif args.app:
            logger.info(f"Generating single app: {args.app}")
            if generator.create_django_app(args.app):
                generator.create_app_structure(args.app)
                generator.update_settings([args.app])
                print(f"✅ Successfully generated app: {args.app}")
            else:
                print(f"❌ Failed to generate app: {args.app}")

        elif args.generate_apps:
            results = generator.generate_all_apps()

            print(f"\n📊 Generation Summary:")
            for app_name, success in results.items():
                status = "✅ Success" if success else "❌ Failed"
                print(f"  - {app_name}: {status}")

        else:
            parser.print_help()

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 Simple Django App Generator - WORKING VERSION")
    print("=" * 60)
    main()
