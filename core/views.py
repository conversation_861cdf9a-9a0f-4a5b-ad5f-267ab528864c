from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required

def user_login(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                return redirect('dashboard')  # Redirect to dashboard on successful login
            else:
                # This case should ideally be handled by form.is_valid()
                # but added for explicit clarity if authenticate returns None
                form.add_error(None, "Invalid username or password.")
        # If form is not valid, it will fall through to render with errors
    else:
        form = AuthenticationForm()
    return render(request, 'core/login.html', {'form': form})

@login_required
def dashboard(request):
    return render(request, 'core/dashboard.html')

def user_logout(request):
    logout(request)
    return redirect('login') # Redirect to login page after logout
