{% extends 'core/base.html' %}

{% block title %}Dashboard - MechERP{% endblock %}

{% block content %}
<div class="container mx-auto p-4">
    <h1 class="text-3xl font-bold mb-6">Welcome, {{ user.username }}!</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Quick Access Card: System Administration -->
        <div class="bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center">
            <i class="fas fa-cogs text-blue-500 text-5xl mb-4"></i>
            <h2 class="text-xl font-semibold mb-2">System Administration</h2>
            <p class="text-gray-600 mb-4">Manage countries, states, cities, companies, and financial years.</p>
            <a href="{% url 'sys_admin:dashboard' %}" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full transition duration-300">
                Go to Sys Admin
            </a>
        </div>

        <!-- Placeholder Card 1 -->
        <div class="bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center">
            <i class="fas fa-chart-line text-green-500 text-5xl mb-4"></i>
            <h2 class="text-xl font-semibold mb-2">Reports & Analytics</h2>
            <p class="text-gray-600 mb-4">View financial reports, sales data, and operational insights.</p>
            <a href="#" class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-full transition duration-300">
                View Reports
            </a>
        </div>

        <!-- Placeholder Card 2 -->
        <div class="bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center">
            <i class="fas fa-users text-purple-500 text-5xl mb-4"></i>
            <h2 class="text-xl font-semibold mb-2">HR Management</h2>
            <p class="text-gray-600 mb-4">Manage employee records, payroll, and human resources.</p>
            <a href="#" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-full transition duration-300">
                Manage HR
            </a>
        </div>

        <!-- Add more cards as needed for other modules -->
    </div>

    <div class="mt-8 p-6 bg-white rounded-lg shadow-md">
        <h2 class="text-2xl font-bold mb-4">Recent Activity</h2>
        <ul class="list-disc list-inside text-gray-700">
            <li>User 'admin' logged in from 192.168.1.100 at 2023-10-26 10:30 AM.</li>
            <li>Country 'India' created by 'admin'.</li>
            <li>State 'Maharashtra' updated by 'admin'.</li>
            <li>City 'Mumbai' deleted by 'admin'.</li>
            <li>Financial Year '2023-2024' opened.</li>
        </ul>
        <p class="text-right mt-4">
            <a href="#" class="text-blue-500 hover:underline">View All Activity</a>
        </p>
    </div>
</div>
{% endblock %}
