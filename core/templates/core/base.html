<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MechERP{% endblock %}</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- HTMX CDN -->
    <script src="https://unpkg.com/htmx.org@1.9.10" crossorigin="anonymous"></script>
    <!-- Alpine.js CDN -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    {% block head_extra %}{% endblock %}
</head>
<body class="bg-gray-100 font-sans leading-normal tracking-normal">
    <div id="main-layout" class="min-h-screen flex flex-col">
        <!-- Navigation Bar -->
        <nav class="bg-gray-800 p-4 text-white shadow-md">
            <div class="container mx-auto flex justify-between items-center">
                <a href="{% url 'dashboard' %}" class="text-2xl font-bold">MechERP</a>
                <div class="space-x-4">
                    {% if user.is_authenticated %}
                        <a href="{% url 'dashboard' %}" class="hover:text-gray-300">Dashboard</a>
                        <a href="{% url 'sys_admin:dashboard' %}" class="hover:text-gray-300">Sys Admin</a>
                        <a href="{% url 'logout' %}" class="hover:text-gray-300">Logout</a>
                    {% else %}
                        <a href="{% url 'login' %}" class="hover:text-gray-300">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="flex-grow container mx-auto mt-4 p-4">
            {% block content %}
            <!-- Content will be injected here -->
            {% endblock %}
        </main>

        <!-- Footer -->
        <footer class="bg-gray-800 p-4 text-white text-center mt-8">
            <div class="container mx-auto">
                &copy; {% now "Y" %} MechERP. All rights reserved.
            </div>
        </footer>
    </div>

    {% block body_extra %}{% endblock %}
    {% block extra_js %}{% endblock %}
</body>
</html>
