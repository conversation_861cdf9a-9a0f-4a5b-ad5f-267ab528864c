#!/usr/bin/env python3
"""
Setup Script for Django App Generator

This script helps users set up the Django App Generator environment
and configure the necessary API keys and dependencies.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_generator.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Set up environment variables"""
    print("🔧 Setting up environment...")
    
    env_file = Path(".env")
    
    if env_file.exists():
        print("ℹ️ .env file already exists")
        with open(env_file, 'r') as f:
            content = f.read()
            if "GEMINI_API_KEY" in content:
                print("✅ GEMINI_API_KEY found in .env")
                return True
    
    print("\n🔑 Gemini API Key Setup")
    print("You need a Gemini API key to use this tool.")
    print("Get your API key from: https://aistudio.google.com/app/apikey")
    
    api_key = input("\nEnter your Gemini API key (or press Enter to skip): ").strip()
    
    if api_key:
        env_content = f"""# Django App Generator Environment Variables
GEMINI_API_KEY={api_key}
MODEL_ID=gemini-2.5-flash-preview-05-20

# Optional: Vertex AI Configuration
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_CLOUD_LOCATION=us-central1
# GOOGLE_GENAI_USE_VERTEXAI=True
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ Environment file created")
        return True
    else:
        print("⚠️ Skipped API key setup. You'll need to set GEMINI_API_KEY manually.")
        return False

def validate_django_project():
    """Validate Django project structure"""
    print("🔍 Validating Django project structure...")
    
    manage_py = Path("manage.py")
    settings_py = Path("autoerp/settings.py")
    docs_dir = Path("docs")
    
    issues = []
    
    if not manage_py.exists():
        issues.append("manage.py not found")
    else:
        print("✅ manage.py found")
    
    if not settings_py.exists():
        issues.append("autoerp/settings.py not found")
    else:
        print("✅ autoerp/settings.py found")
    
    if not docs_dir.exists():
        issues.append("docs/ directory not found")
    else:
        print("✅ docs/ directory found")
        
        # Check for documentation files
        md_files = list(docs_dir.rglob("*.md"))
        print(f"📄 Found {len(md_files)} markdown files")
    
    if issues:
        print("\n❌ Project validation issues:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    
    print("✅ Django project structure validated")
    return True

def create_sample_env():
    """Create sample environment file"""
    sample_env = Path(".env.example")
    
    sample_content = """# Django App Generator Environment Variables
# Copy this file to .env and fill in your values

# Required: Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Model Configuration
MODEL_ID=gemini-2.5-flash-preview-05-20

# Optional: Vertex AI Configuration (for enterprise users)
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_CLOUD_LOCATION=us-central1
# GOOGLE_GENAI_USE_VERTEXAI=True

# Optional: Logging Configuration
# LOG_LEVEL=INFO
# LOG_FILE=django_generator.log
"""
    
    with open(sample_env, 'w') as f:
        f.write(sample_content)
    
    print("✅ Created .env.example file")

def run_test():
    """Run a basic test of the generator"""
    print("🧪 Running basic test...")
    
    try:
        # Import the generator to test basic functionality
        from django_app_generator import DjangoAppGenerator
        
        # Test initialization (without API key for now)
        print("✅ Django App Generator imports successfully")
        
        # Test directory discovery
        generator = DjangoAppGenerator()
        apps = generator._fallback_app_discovery()
        print(f"✅ Found {len(apps)} potential apps: {apps}")
        
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Django App Generator Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("⚠️ Continuing with existing dependencies...")
    
    # Setup environment
    setup_environment()
    
    # Create sample environment file
    create_sample_env()
    
    # Validate Django project
    if not validate_django_project():
        print("\n⚠️ Project validation failed. Please ensure you're in a Django project directory.")
    
    # Run basic test
    if run_test():
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Ensure your .env file has the correct GEMINI_API_KEY")
        print("2. Run: python django_app_generator.py --list-apps")
        print("3. Run: python django_app_generator.py --scan-docs --generate-apps")
    else:
        print("\n⚠️ Setup completed with issues. Check the error messages above.")

if __name__ == "__main__":
    main()
