# To run this code you need to install the following dependencies:
# pip install google-genai python-dotenv tenacity tqdm

import os
import sys
import time
import hashlib
import pickle
from tqdm import tqdm

# Add immediate debug output to console
print("Debug: Script starting...")
print(f"Debug: Python version: {sys.version}")
print(f"Debug: Current directory: {os.getcwd()}")

try:
    # Try importing optional dependencies - will continue with reduced functionality if missing
    import google.genai
    import google.api_core.exceptions
    from google.genai import types
    from tenacity import retry, stop_after_attempt, wait_exponential
    from dotenv import load_dotenv

    API_AVAILABLE = True
    print("Debug: API dependencies loaded successfully")
except ImportError as e:
    print(f"Debug: API dependencies not available - {e}")
    API_AVAILABLE = False

# --- Configuration for Logging ---
log_file_path = "aspnet_migration.log"
original_stdout = sys.stdout
print(f"Debug: Setting up logging to {log_file_path}")

# Global configuration
module_path = "Inventory"
output_root = "markdown-outputs"
cache_dir = "api_cache"
test_mode = True  # Set to True to process only a few files
test_limit = 3  # Number of files to process in test mode


# Helper function to log to both file and console
def log_message(message):
    """Logs a message to both the file and the console."""
    print(message, file=sys.stdout)
    if sys.stdout != original_stdout:
        print(message, file=original_stdout)


# Optimize file reading for large files
def read_file_optimized(file_path, chunk_size=1024 * 1024):
    """
    Reads a file in chunks to avoid loading large files entirely into memory.
    """
    try:
        content = ""
        with open(file_path, "r", encoding="utf-8") as f:
            for chunk in iter(lambda: f.read(chunk_size), ""):
                content += chunk
        return content
    except Exception as e:
        log_message(f"Error reading file {file_path}: {e}")
        return ""


def get_aspx_files_recursively(root_dir):
    """
    Recursively scans a directory for .aspx files and returns a list of their paths.
    """
    aspx_files = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.endswith(".aspx"):
                file_path = os.path.join(dirpath, filename)
                aspx_files.append(file_path)
    return aspx_files


def create_output_directory(output_root, input_path):
    """
    Creates an output directory structure mirroring the input path hierarchy.
    Converts directory names to snake_case.
    """
    # Extract the directory part of the input path
    input_dir = os.path.dirname(input_path)

    # Skip the 'aaspnet' part from the input path
    if input_dir.startswith("aaspnet/"):
        input_dir = input_dir[len("aaspnet/") :]

    # Convert directory names to snake_case
    path_parts = input_dir.split("/")
    snake_case_parts = [convert_to_snake_case(part) for part in path_parts]
    relative_output_path = os.path.join(*snake_case_parts)

    # Create the full output path
    output_dir = os.path.join(output_root, relative_output_path)

    # Create the directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    return output_dir


def convert_to_snake_case(text):
    """
    Converts a string to snake_case.
    """
    # Replace any non-alphanumeric characters with underscore
    import re

    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", text)
    s2 = re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1)
    return s2.lower()


# Simple disk-based caching
def get_cached_response(cache_key):
    cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")
    if os.path.exists(cache_file):
        try:
            with open(cache_file, "rb") as f:
                log_message(f"Using cached response")
                return pickle.load(f)
        except Exception as e:
            log_message(f"Error loading cached response: {e}")
    return None


def save_to_cache(cache_key, response):
    os.makedirs(cache_dir, exist_ok=True)
    cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")
    try:
        with open(cache_file, "wb") as f:
            pickle.dump(response, f)
        log_message(f"Response saved to cache")
    except Exception as e:
        log_message(f"Error caching response: {e}")


def process_file_with_api(
    asp_net_file_path,
    client,
    model_id,
    prompt_template_from_file,
    output_root,
    script_dir,
    system_instruction,
):
    """Process a file using the Gemini API"""
    # Make the path relative to script directory for better logging
    relative_path = os.path.relpath(asp_net_file_path, script_dir)
    aspnet_filename = os.path.basename(asp_net_file_path)
    log_message(f"\n--- Processing '{relative_path}' with API ---")

    # Read ASPX and CS files
    try:
        aspnet_code = read_file_optimized(asp_net_file_path)
        log_message(
            f"Successfully loaded ASP.NET code (ASPX): {len(aspnet_code)} bytes"
        )

        # Read code-behind file if it exists
        asp_net_code_behind_file_path = asp_net_file_path + ".cs"
        if os.path.exists(asp_net_code_behind_file_path):
            aspnet_code_behind = read_file_optimized(asp_net_code_behind_file_path)
            log_message(
                f"Successfully loaded ASP.NET code-behind (C#): {len(aspnet_code_behind)} bytes"
            )
        else:
            aspnet_code_behind = ""
            log_message(f"No code-behind file found")

        # Generate a unique cache key
        cache_key = hashlib.md5(
            (relative_path + aspnet_code[:1000] + aspnet_code_behind[:1000]).encode()
        ).hexdigest()

        # Check cache first
        cached_response = get_cached_response(cache_key)
        if cached_response:
            response = cached_response
        else:
            # Create the user prompt
            user_prompt = f"""
Analyze the following ASP.NET code from the given .aspx file and its corresponding C# code-behind file. Based on this analysis, provide a comprehensive Django modernization plan, broken down into distinct Django application files. Your response should be a single Markdown document structured as follows, including all necessary code blocks and explanations. Focus on generating complete and runnable Django code where applicable, adhering to all principles outlined in your system instructions (fat models, thin views, HTMX, Alpine.js, DataTables, etc.).

ASP.NET Code (ASPX):
```html
{aspnet_code}
```

ASP.NET Code-Behind (C#):
```csharp
{aspnet_code_behind}
```

Desired Markdown Structure:
{prompt_template_from_file}
"""

            log_message(f"Calling Gemini API for '{relative_path}'...")
            start_time = time.time()

            # Create a thinking config for more thorough analysis
            thinking_config = types.ThinkingConfig(include_thoughts=True)

            # Make the API call with retries
            try:
                response = client.models.generate_content(
                    model=model_id,
                    contents=user_prompt,
                    config=types.GenerateContentConfig(
                        system_instruction=system_instruction,
                        thinking_config=thinking_config,
                    ),
                )

                # Cache the successful response
                save_to_cache(cache_key, response)

            except Exception as e:
                log_message(f"API Error: {e}")
                return None

            end_time = time.time()
            log_message(f"API call completed in {end_time - start_time:.2f} seconds")

        # Create output directory and save response
        output_dir = create_output_directory(output_root, relative_path)
        output_filename = (
            f"django_modernization_plan_{os.path.splitext(aspnet_filename)[0]}.md"
        )
        output_file_path = os.path.join(output_dir, output_filename)

        with open(output_file_path, "w", encoding="utf-8") as f:
            f.write(response.text)

        log_message(f"Django Modernization Plan saved to: {output_file_path}")
        return (asp_net_file_path, response.text)

    except Exception as e:
        log_message(f"Error processing file {relative_path}: {e}")
        return None


def process_file_without_api(asp_net_file_path, script_dir):
    """Process a file without using API (fallback mode)"""
    # Make the path relative to script directory for better logging
    relative_path = os.path.relpath(asp_net_file_path, script_dir)
    aspnet_filename = os.path.basename(asp_net_file_path)
    log_message(f"\n--- Processing '{relative_path}' without API ---")

    try:
        # Just log file sizes
        aspx_size = os.path.getsize(asp_net_file_path)
        log_message(f"ASPX file size: {aspx_size} bytes")

        asp_net_code_behind_file_path = asp_net_file_path + ".cs"
        if os.path.exists(asp_net_code_behind_file_path):
            cs_size = os.path.getsize(asp_net_code_behind_file_path)
            log_message(f"CS file size: {cs_size} bytes")
        else:
            log_message("No code-behind file found")

        # Create output directory and placeholder file
        output_dir = create_output_directory(output_root, relative_path)
        output_filename = (
            f"django_placeholder_{os.path.splitext(aspnet_filename)[0]}.md"
        )
        output_file_path = os.path.join(output_dir, output_filename)

        with open(output_file_path, "w", encoding="utf-8") as f:
            f.write(
                f"# Django Modernization Plan for {aspnet_filename}\n\nThis is a placeholder file."
            )

        log_message(f"Created placeholder file at: {output_file_path}")
        return True

    except Exception as e:
        log_message(f"Error processing file: {e}")
        return False


def main():
    """Main function with conditional API usage"""
    # --- Logging Setup ---
    global original_stdout
    sys.stdout = open(log_file_path, "w", encoding="utf-8")

    log_message(f"--- Starting ASP.NET to Django Migration script ---")
    log_message(
        f"Console output is being displayed and also logged to '{log_file_path}'."
    )

    # Start timing
    start_time = time.time()

    # --- Input Directory Configuration ---
    if len(sys.argv) > 1:
        input_dir = sys.argv[1]
    else:
        input_dir = f"aaspnet/Module/{module_path}"

    log_message(f"Scanning for ASP.NET files in directory: '{input_dir}'")

    # --- Output Directory Configuration ---
    log_message(f"Markdown outputs will be saved to: '{output_root}'")
    os.makedirs(output_root, exist_ok=True)
    os.makedirs(cache_dir, exist_ok=True)

    # Get script directory
    script_dir = (
        os.path.dirname(os.path.abspath(__file__))
        if os.path.dirname(os.path.abspath(__file__))
        else "."
    )
    log_message(f"Script directory for file operations: {script_dir}")

    # Get all .aspx files recursively from the input directory
    input_dir_full_path = os.path.join(script_dir, input_dir)
    aspx_files = get_aspx_files_recursively(input_dir_full_path)
    log_message(f"Found {len(aspx_files)} .aspx files to process.")

    # Limit files in test mode
    if test_mode and len(aspx_files) > test_limit:
        log_message(
            f"TESTING MODE: Processing only {test_limit} files for quicker testing"
        )
        aspx_files = aspx_files[:test_limit]

    # API-dependent setup
    client = None
    model_id = None
    system_instruction = None
    prompt_template_from_file = ""

    # Initialize API if available
    if API_AVAILABLE:
        try:
            # Load environment variables and initialize the API client
            load_dotenv()
            api_key = os.environ.get("GEMINI_API_KEY")

            if not api_key:
                log_message("GEMINI_API_KEY not found. Running in fallback mode.")
            else:
                # Initialize API client
                client = google.genai.Client(api_key=api_key)
                model_id = "gemini-2.5-flash-preview-05-20"
                log_message(f"Google GenAI client initialized with model: {model_id}")

                # Load prompt template
                prompt_file_path = os.path.join(script_dir, "prompt.md")
                if os.path.exists(prompt_file_path):
                    prompt_template_from_file = read_file_optimized(prompt_file_path)
                    log_message(f"Loaded prompt template from: {prompt_file_path}")
                else:
                    log_message(f"Warning: prompt.md not found at {prompt_file_path}")

                # Set system instruction
                system_instruction = """
You are an expert ASP.NET to Django migration specialist and modernization consultant. Your primary role is to help organizations transition from legacy ASP.NET applications to modern Django-based solutions using AI-assisted automation approaches.

CORE RESPONSIBILITIES:
- Analyze ASP.NET applications and create comprehensive modernization plans
- Provide migration strategies using conversational AI with plain English instructions
- Focus on automation-driven approaches rather than manual coding examples
- Communicate technical concepts in non-technical language suitable for business stakeholders

TARGET FRAMEWORK AND ARCHITECTURE:
- Always recommend Django as the target framework for ASP.NET modernization
- Emphasize modern Django 5.0+ patterns and best practices
- Advocate for fat model/thin view architecture with strict 15-line view method limits
- Promote clean, compact Django class-based views (5-15 lines maximum)
- Ensure strict separation of concerns with no HTML in views and business logic in models only
- Enforce DRY (Don't Repeat Yourself) principles throughout the migration

TECHNOLOGY STACK PREFERENCES:
- Recommend exclusive use of HTMX + Alpine.js for frontend interactions (no additional JavaScript)
- Suggest DataTables for all list views and data presentation
- Promote DRY template inheritance where base.html contains all CDN links
- Ensure HTMX-only interactions without custom JavaScript requirements
- Include comprehensive HTML templates with CRUD operations and base template inheritance

COMMUNICATION STYLE:
- Write all modernization plans in non-technical language accessible to business stakeholders
- Use plain English instructions that non-technical team members can understand
- Focus on business benefits and outcomes rather than technical implementation details
- Provide clear, actionable steps that can be communicated through conversational AI
- Avoid technical jargon and complex programming terminology

MIGRATION APPROACH:
- Prioritize AI-assisted automation strategies over manual development approaches
- Create step-by-step migration plans that can be executed through conversational AI guidance
- Focus on systematic, automated conversion processes rather than manual code rewriting
- Emphasize tools and techniques that reduce manual effort and human error
- Provide guidance that enables non-technical stakeholders to understand and oversee the migration process

When responding to migration requests, always structure your advice to be implementable through automated processes and clearly communicate the business value of the proposed Django modernization approach.
"""
        except Exception as e:
            log_message(f"Error initializing API: {e}")
            client = None

    # Process files based on API availability
    successful_files = 0

    for i, file_path in enumerate(aspx_files):
        log_message(f"Processing file {i+1} of {len(aspx_files)}")

        if client and prompt_template_from_file:
            # Use API processing
            result = process_file_with_api(
                file_path,
                client,
                model_id,
                prompt_template_from_file,
                output_root,
                script_dir,
                system_instruction,
            )
            if result:
                successful_files += 1
        else:
            # Use fallback processing
            if process_file_without_api(file_path, script_dir):
                successful_files += 1

    # Calculate and display the total execution time
    end_time = time.time()
    total_time = end_time - start_time

    log_message(f"\n--- All ASP.NET files in '{input_dir}' have been processed. ---")
    log_message(
        f"Successfully processed {successful_files} out of {len(aspx_files)} files."
    )
    log_message(f"Output files are available in the '{output_root}' directory.")
    log_message(f"Total execution time: {total_time:.2f} seconds")


# --- Script Entry Point ---
if __name__ == "__main__":
    try:
        main()
    finally:
        if sys.stdout != original_stdout:
            sys.stdout.close()
            sys.stdout = original_stdout
            print(
                f"\nScript execution finished. Detailed logs are available in '{log_file_path}'."
            )
