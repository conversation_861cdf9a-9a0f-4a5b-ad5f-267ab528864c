#!/usr/bin/env python3
"""
Test Script for Django App Generator

This script tests the Django App Generator functionality without requiring
API keys or making actual API calls.
"""

import os
import sys
from pathlib import Path
import tempfile
import shutil

def test_project_structure():
    """Test project structure validation"""
    print("🧪 Testing project structure validation...")
    
    # Check current project structure
    manage_py = Path("manage.py")
    settings_py = Path("autoerp/settings.py")
    docs_dir = Path("docs")
    
    results = {
        "manage.py": manage_py.exists(),
        "settings.py": settings_py.exists(),
        "docs directory": docs_dir.exists()
    }
    
    for item, exists in results.items():
        status = "✅" if exists else "❌"
        print(f"  {status} {item}")
    
    return all(results.values())

def test_documentation_discovery():
    """Test documentation directory discovery"""
    print("\n🧪 Testing documentation discovery...")
    
    docs_dir = Path("docs")
    if not docs_dir.exists():
        print("  ❌ docs directory not found")
        return False
    
    # Count documentation directories
    app_dirs = [item for item in docs_dir.iterdir() if item.is_dir() and not item.name.startswith('.')]
    print(f"  📁 Found {len(app_dirs)} potential app directories:")
    
    for app_dir in app_dirs:
        md_files = list(app_dir.rglob("*.md"))
        print(f"    - {app_dir.name}: {len(md_files)} markdown files")
    
    return len(app_dirs) > 0

def test_fallback_extraction():
    """Test fallback code extraction without AI"""
    print("\n🧪 Testing fallback code extraction...")
    
    # Sample markdown content with Django code
    sample_content = """
# Sample Django Documentation

## Models

```python
class Customer(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name
```

## Views

```python
class CustomerListView(ListView):
    model = Customer
    template_name = 'customers/list.html'
    context_object_name = 'customers'
```

## Forms

```python
class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = ['name', 'email']
```

## Templates

```html
<div class="customer-list">
    {% for customer in customers %}
        <div class="customer">{{ customer.name }}</div>
    {% endfor %}
</div>
```
"""
    
    try:
        # Import the generator class
        from django_app_generator import DjangoAppGenerator
        
        # Create a temporary generator instance (without API key)
        class TestGenerator(DjangoAppGenerator):
            def __init__(self):
                # Skip API initialization for testing
                self.project_root = Path(".").resolve()
                self.docs_dir = self.project_root / "docs"
                self.manage_py = self.project_root / "manage.py"
                self.settings_file = self.project_root / "autoerp" / "settings.py"
        
        generator = TestGenerator()
        
        # Test fallback extraction
        components = generator._fallback_code_extraction(sample_content)
        
        print("  📊 Extracted components:")
        for component_type, blocks in components.items():
            if blocks:
                print(f"    - {component_type}: {len(blocks)} blocks")
        
        # Validate extraction
        expected_components = ['models', 'views', 'forms', 'templates']
        found_components = [comp for comp in expected_components if components[comp]]
        
        success = len(found_components) == len(expected_components)
        status = "✅" if success else "❌"
        print(f"  {status} Extraction test: {len(found_components)}/{len(expected_components)} components found")
        
        return success
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Test error: {e}")
        return False

def test_app_discovery():
    """Test app discovery functionality"""
    print("\n🧪 Testing app discovery...")
    
    try:
        from django_app_generator import DjangoAppGenerator
        
        class TestGenerator(DjangoAppGenerator):
            def __init__(self):
                self.project_root = Path(".").resolve()
                self.docs_dir = self.project_root / "docs"
                self.manage_py = self.project_root / "manage.py"
                self.settings_file = self.project_root / "autoerp" / "settings.py"
        
        generator = TestGenerator()
        
        # Test fallback app discovery
        apps = generator._fallback_app_discovery()
        
        print(f"  📱 Discovered apps: {apps}")
        
        # Test directory structure generation
        if generator.docs_dir.exists():
            structure = generator._get_directory_structure(generator.docs_dir)
            print(f"  📁 Directory structure preview:")
            print("    " + "\n    ".join(structure.split("\n")[:10]))  # First 10 lines
        
        return len(apps) > 0
        
    except Exception as e:
        print(f"  ❌ App discovery test error: {e}")
        return False

def test_file_operations():
    """Test file operation utilities"""
    print("\n🧪 Testing file operations...")
    
    try:
        from django_app_generator import DjangoAppGenerator
        
        class TestGenerator(DjangoAppGenerator):
            def __init__(self):
                self.project_root = Path(".").resolve()
                self.cache_dir = self.project_root / ".test_cache"
                self.cache_dir.mkdir(exist_ok=True)
        
        generator = TestGenerator()
        
        # Test cache operations
        test_data = {"test": "data", "components": ["model1", "view1"]}
        cache_key = "test_key"
        
        # Test caching
        generator._cache_result(cache_key, test_data)
        cached_data = generator._get_cached_result(cache_key)
        
        cache_success = cached_data == test_data
        status = "✅" if cache_success else "❌"
        print(f"  {status} Cache operations")
        
        # Cleanup
        shutil.rmtree(generator.cache_dir, ignore_errors=True)
        
        return cache_success
        
    except Exception as e:
        print(f"  ❌ File operations test error: {e}")
        return False

def test_dependencies():
    """Test required dependencies"""
    print("\n🧪 Testing dependencies...")
    
    required_modules = [
        "pathlib",
        "json",
        "re",
        "hashlib",
        "argparse"
    ]
    
    optional_modules = [
        "google.genai",
        "dotenv"
    ]
    
    results = {}
    
    # Test required modules
    for module in required_modules:
        try:
            __import__(module)
            results[module] = True
            print(f"  ✅ {module}")
        except ImportError:
            results[module] = False
            print(f"  ❌ {module} (required)")
    
    # Test optional modules
    for module in optional_modules:
        try:
            __import__(module)
            print(f"  ✅ {module} (optional)")
        except ImportError:
            print(f"  ⚠️ {module} (optional - install for full functionality)")
    
    return all(results.values())

def run_all_tests():
    """Run all tests"""
    print("🚀 Django App Generator Test Suite")
    print("=" * 50)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Documentation Discovery", test_documentation_discovery),
        ("Fallback Code Extraction", test_fallback_extraction),
        ("App Discovery", test_app_discovery),
        ("File Operations", test_file_operations),
        ("Dependencies", test_dependencies)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The generator is ready to use.")
        print("\nNext steps:")
        print("1. Set up your GEMINI_API_KEY in .env")
        print("2. Run: python django_app_generator.py --list-apps")
    else:
        print("⚠️ Some tests failed. Check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
