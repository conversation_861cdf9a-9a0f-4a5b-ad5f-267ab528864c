{"models": ["from django.db import models\nfrom django.utils import timezone\nfrom django.core.exceptions import ValidationError\n\nclass Category(models.Model):\n    # Using existing CId as primary key, though Django typically uses 'id'\n    cid = models.IntegerField(db_column='CId', primary_key=True)\n    cname = models.CharField(db_column='CName', max_length=255)\n    symbol = models.CharField(db_column='Symbol', max_length=50)\n    compid = models.IntegerField(db_column='CompId')\n    finyearid = models.IntegerField(db_column='FinYearId')\n    has_subcat = models.BooleanField(db_column='HasSubCat', default=True) # Inferred from '!='0'\n\n    class Meta:\n        managed = False  # Important: Django will not manage this table's schema\n        db_table = 'tblSD_WO_Category'\n        verbose_name = 'Category'\n        verbose_name_plural = 'Categories'\n\n    def __str__(self):\n        return f\"{self.symbol} - {self.cname}\"\n\nclass SubCategory(models.Model):\n    # Using existing SCId as primary key\n    scid = models.IntegerField(db_column='SCId', primary_key=True)\n    category = models.ForeignKey(Category, on_delete=models.PROTECT, db_column='CId', related_name='subcategories')\n    scname = models.CharField(db_column='SCName', max_length=255, verbose_name='SubCategory Name')\n    symbol = models.CharField(db_column='Symbol', max_length=1, verbose_name='Symbol') # MaxLength=1 as per ASP.NET\n\n    # System/Audit fields as found in ASP.NET\n    compid = models.IntegerField(db_column='CompId')\n    sysdate = models.CharField(db_column='SysDate', max_length=10) # Stored as string in legacy\n    systime = models.CharField(db_column='SysTime', max_length=8)  # Stored as string in legacy\n    finyearid = models.IntegerField(db_column='FinYearId')\n    sessionid = models.CharField(db_column='SessionId', max_length=255)\n\n    class Meta:\n        managed = False  # Important: Django will not manage this table's schema\n        db_table = 'tblSD_WO_SubCategory'\n        verbose_name = 'Sub Category'\n        verbose_name_plural = 'Sub Categories'\n        # Composite unique constraint for the symbol validation\n        unique_together = (('symbol', 'category', 'compid', 'finyearid'),)\n\n\n    def __str__(self):\n        return f\"{self.scname} ({self.symbol})\"\n\n    def clean(self):\n        # Business logic for unique symbol per category/company/financial year\n        if self.symbol:\n            self.symbol = self.symbol.upper() # Ensure symbol is uppercase as in legacy code\n\n        # Uniqueness check (handled by unique_together in Meta, but keeping specific check for clarity and error message)\n        # This explicit check also handles case-insensitivity if needed or more complex logic\n        if self.pk is None: # Only for new objects\n            if SubCategory.objects.filter(\n                symbol=self.symbol,\n                category=self.category,\n                compid=self.compid,\n                finyearid=self.finyearid\n            ).exists():\n                raise ValidationError(\"SubCategory symbol is already used for this Category, Company, and Financial Year.\")\n    \n    def save(self, *args, **kwargs):\n        # Auto-populate system fields before saving\n        # Assuming request context (e.g., user, session data) is passed via view/form or accessible\n        # For demonstration, we'll use placeholder values that would ideally come from session/user context.\n        # In a real app, you might use request.user.compid, request.session.get('finyear'), etc.\n        # Or have a middleware or custom manager set these.\n        \n        # This is a placeholder for how these values would be set.\n        # For testing, we'll set sensible defaults or pass them.\n        if not self.sysdate:\n            self.sysdate = timezone.now().strftime('%d-%m-%Y') # Matches legacy string format\n        if not self.systime:\n            self.systime = timezone.now().strftime('%H:%M:%S') # Matches legacy string format\n        \n        # These would come from the user's session or profile\n        # For simplicity in this auto-generated code, they would be passed from the view/form\n        # Example: self.compid = request.user.profile.company_id\n        # Example: self.finyearid = request.session.get('current_fin_year_id')\n        # Example: self.sessionid = request.user.username\n        \n        # Ensure symbol is uppercase upon saving, as in legacy code\n        self.symbol = self.symbol.upper()\n\n        super().save(*args, **kwargs)"], "views": ["from django.views.generic import List<PERSON>ie<PERSON>, <PERSON><PERSON><PERSON>iew, UpdateView, DeleteView, Template<PERSON>iew\nfrom django.urls import reverse_lazy\nfrom django.contrib import messages\nfrom django.http import HttpResponse\nfrom django.template.loader import render_to_string\nfrom django.db import IntegrityError # For handling unique_together errors\n\nfrom .models import SubCategory\nfrom .forms import SubCategoryForm\n\nclass SubCategoryListView(ListView):\n    model = SubCategory\n    template_name = 'sales_distribution/subcategory/list.html'\n    context_object_name = 'subcategories'\n\n    # The actual data for the DataTable is fetched by HTMX via SubCategoryTablePartialView\n    def get_queryset(self):\n        # This queryset might be used for initial context if needed, but not for DataTable itself\n        # For actual DataTable, data will be fetched by the partial view, potentially filtered by session\n        # Example: Filter by current user's company and financial year\n        # compid = self.request.user.get_company_id() if hasattr(self.request.user, 'get_company_id') else 1\n        # finyearid = self.request.session.get('finyear_id', 1)\n        # return SubCategory.objects.filter(compid=compid, finyearid__lte=finyearid)\n        return SubCategory.objects.all() # Return all for now, filtering handled in partial view if needed\n\n\nclass SubCategoryTablePartialView(ListView):\n    model = SubCategory\n    template_name = 'sales_distribution/subcategory/_subcategory_table.html'\n    context_object_name = 'subcategories'\n\n    def get_queryset(self):\n        # Implement filtering based on session data as in ASP.NET SqlDataSource\n        # This logic needs to be dynamic based on your session/user management in Django\n        # Example: Assuming user has 'company_id' and 'financial_year_id' attributes\n        # And assuming tblSD_WO_Category.FinYearId <= @FinYearId maps to FinYearId <= current_fin_year_id\n        \n        # Placeholder for actual session/user data\n        compid_val = self.request.user.get_company_id() if hasattr(self.request.user, 'get_company_id') else 1 \n        finyearid_val = self.request.session.get('finyear_id', 1) # Default to 1 if not found\n\n        # Replicating the ASP.NET SELECT command's WHERE clause\n        # SELECT ... FROM [tblSD_WO_SubCategory],[tblSD_WO_Category] Where [tblSD_WO_SubCategory].[CId]=[tblSD_WO_Category].[CId] And [tblSD_WO_SubCategory].[CompId] = @CompId AND [tblSD_WO_SubCategory].[FinYearId] <= @FinYearId order by [tblSD_WO_SubCategory].[SCId] desc\n        queryset = SubCategory.objects.select_related('category').filter(\n            compid=compid_val,\n            finyearid__lte=finyearid_val # Use __lte for '<='\n        ).order_by('-scid') # Order by SCId desc\n\n        return queryset\n\nclass SubCategoryCreateView(CreateView):\n    model = SubCategory\n    form_class = SubCategoryForm\n    template_name = 'sales_distribution/subcategory/_subcategory_form.html' # Use partial template\n    success_url = reverse_lazy('subcategory_list') # Not directly used for HTMX, but good practice\n\n    def get_form_kwargs(self):\n        kwargs = super().get_form_kwargs()\n        kwargs['request'] = self.request # Pass request to form for session/user data\n        return kwargs\n\n    def form_valid(self, form):\n        try:\n            response = super().form_valid(form)\n            messages.success(self.request, 'Sub Category added successfully.')\n            # HTMX response for successful form submission\n            if self.request.headers.get('HX-Request'):\n                return HttpResponse(\n                    status=204, # No content, indicates success without navigating\n                    headers={\n                        'HX-Trigger': 'refreshSubCategoryList, closeModals' # Custom event to refresh list and close modal\n                    }\n                )\n            return response\n        except IntegrityError:\n            # Handle potential unique constraint violation if not caught by form.clean\n            messages.error(self.request, \"A sub category with this symbol already exists for the selected category, company, and financial year.\")\n            return self.form_invalid(form)\n\n\n    def form_invalid(self, form):\n        messages.error(self.request, \"Please correct the errors below.\")\n        if self.request.headers.get('HX-Request'):\n            # Re-render the form with errors for HTMX\n            return self.render_to_response(self.get_context_data(form=form))\n        return super().form_invalid(form)\n\n\nclass SubCategoryUpdateView(UpdateView):\n    model = SubCategory\n    form_class = SubCategoryForm\n    template_name = 'sales_distribution/subcategory/_subcategory_form.html'\n    success_url = reverse_lazy('subcategory_list')\n\n    def get_form_kwargs(self):\n        kwargs = super().get_form_kwargs()\n        kwargs['request'] = self.request\n        return kwargs\n\n    def form_valid(self, form):\n        try:\n            response = super().form_valid(form)\n            messages.success(self.request, 'Sub Category updated successfully.')\n            if self.request.headers.get('HX-Request'):\n                return HttpResponse(\n                    status=204,\n                    headers={\n                        'HX-Trigger': 'refreshSubCategoryList, closeModals'\n                    }\n                )\n            return response\n        except IntegrityError:\n            messages.error(self.request, \"A sub category with this symbol already exists for the selected category, company, and financial year.\")\n            return self.form_invalid(form)\n\n    def form_invalid(self, form):\n        messages.error(self.request, \"Please correct the errors below.\")\n        if self.request.headers.get('HX-Request'):\n            return self.render_to_response(self.get_context_data(form=form))\n        return super().form_invalid(form)\n\nclass SubCategoryDeleteView(DeleteView):\n    model = SubCategory\n    template_name = 'sales_distribution/subcategory/_subcategory_confirm_delete.html'\n    success_url = reverse_lazy('subcategory_list')\n\n    def delete(self, request, *args, **kwargs):\n        response = super().delete(request, *args, **kwargs)\n        messages.success(self.request, 'Sub Category deleted successfully.')\n        if request.headers.get('HX-Request'):\n            return HttpResponse(\n                status=204,\n                headers={\n                    'HX-Trigger': 'refreshSubCategoryList, closeModals'\n                }\n            )\n        return response\n\n    def get(self, request, *args, **kwargs):\n        # Render delete confirmation for HTMX\n        self.object = self.get_object()\n        context = self.get_context_data(object=self.object)\n        return self.render_to_response(context)"], "forms": ["from django import forms\nfrom .models import SubCategory, Category\n\nclass SubCategoryForm(forms.ModelForm):\n    # Category dropdown to filter by HasSubCat and display 'Symbol - CName'\n    category = forms.ModelChoiceField(\n        queryset=Category.objects.filter(has_subcat=True), # Filter categories that 'HasSubCat'\n        empty_label=\"Select Category\",\n        label=\"Category\",\n        to_field_name='cid', # Use cid as the value for the dropdown\n        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})\n    )\n\n    class Meta:\n        model = SubCategory\n        fields = ['category', 'scname', 'symbol']\n        widgets = {\n            'scname': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter SubCategory Name'}),\n            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'maxlength': '1', 'placeholder': 'Enter Symbol (1 char)'}),\n        }\n        \n    def __init__(self, *args, **kwargs):\n        self.request = kwargs.pop('request', None) # Get request object to access session/user\n        super().__init__(*args, **kwargs)\n        # Customize category queryset if dynamic filtering by CompId/FinYearId is needed\n        # self.fields['category'].queryset = Category.objects.filter(\n        #     compid=self.request.user.profile.company_id,\n        #     finyearid=self.request.session.get('current_fin_year_id'),\n        #     has_subcat=True\n        # )\n\n    def clean_symbol(self):\n        symbol = self.cleaned_data['symbol']\n        return symbol.upper() # Ensure symbol is uppercase for consistency and validation\n\n    def clean(self):\n        cleaned_data = super().clean()\n        \n        # Placeholder for dynamic CompId and FinYearId from session/user\n        # In a real app, these would come from request.user or request.session\n        compid_val = self.request.user.get_company_id() if self.request and hasattr(self.request.user, 'get_company_id') else 1 # Example placeholder\n        finyearid_val = self.request.session.get('finyear_id', 1) if self.request else 1 # Example placeholder\n        \n        category = cleaned_data.get('category')\n        symbol = cleaned_data.get('symbol')\n\n        if category and symbol:\n            # Check for existing symbol only if adding a new object\n            if not self.instance.pk: # For create operations\n                if SubCategory.objects.filter(\n                    symbol=symbol,\n                    category=category,\n                    compid=compid_val,\n                    finyearid=finyearid_val\n                ).exists():\n                    self.add_error('symbol', \"This symbol is already used for the selected Category, Company, and Financial Year.\")\n        \n        # Populate CompId, FinYearId, SessionId into instance for model's save method\n        if self.instance:\n            self.instance.compid = compid_val\n            self.instance.finyearid = finyearid_val\n            self.instance.sessionid = self.request.user.username if self.request and self.request.user.is_authenticated else 'unknown'\n\n        return cleaned_data"], "urls": ["from django.urls import path\nfrom .views import SubCategoryListView, SubCategoryCreateView, SubCategoryUpdateView, SubCategoryDeleteView, SubCategoryTablePartialView\n\nurlpatterns = [\n    path('subcategories/', SubCategoryListView.as_view(), name='subcategory_list'),\n    path('subcategories/table/', SubCategoryTablePartialView.as_view(), name='subcategory_table'), # HTMX partial\n    path('subcategories/add/', SubCategoryCreateView.as_view(), name='subcategory_add'),\n    path('subcategories/<int:pk>/edit/', SubCategoryUpdateView.as_view(), name='subcategory_edit'),\n    path('subcategories/<int:pk>/delete/', SubCategoryDeleteView.as_view(), name='subcategory_delete'),\n]"], "templates": ["<!-- sales_distribution/subcategory/list.html -->\n{% extends 'core/base.html' %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h2 class=\"text-2xl font-bold\">Sub Categories</h2>\n        <button \n            class=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out\"\n            hx-get=\"{% url 'subcategory_add' %}\"\n            hx-target=\"#modalContent\"\n            hx-trigger=\"click\"\n            _=\"on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent\"\n            aria-label=\"Add New Sub Category\">\n            <i class=\"fas fa-plus-circle mr-2\"></i> Add New Sub Category\n        </button>\n    </div>\n    \n    <div id=\"subcategoryTable-container\"\n         hx-trigger=\"load, refreshSubCategoryList from:body\"\n         hx-get=\"{% url 'subcategory_table' %}\"\n         hx-swap=\"innerHTML\"\n         class=\"bg-white rounded-lg shadow-md p-4\">\n        <!-- DataTable will be loaded here via HTMX -->\n        <div class=\"text-center py-10\">\n            <div class=\"inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500\"></div>\n            <p class=\"mt-4 text-lg text-gray-600\">Loading Sub Categories...</p>\n        </div>\n    </div>\n    \n    <!-- Modals -->\n    <div id=\"modal\" class=\"fixed inset-0 z-50 hidden opacity-0 transition-opacity duration-300 ease-out flex items-center justify-center bg-gray-900 bg-opacity-50\"\n         _=\"on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from me then remove .scale-100 from #modalContent\"\n         x-data=\"{ show: false }\" x-show=\"show\" x-transition:enter=\"ease-out duration-300\" x-transition:enter-start=\"opacity-0\" x-transition:enter-end=\"opacity-100\"\n         x-transition:leave=\"ease-in duration-200\" x-transition:leave-start=\"opacity-100\" x-transition:leave-end=\"opacity-0\">\n        \n        <div id=\"modalContent\" class=\"bg-white rounded-lg shadow-xl transform scale-95 transition-transform duration-300 ease-out max-w-2xl w-full mx-4\"\n             _=\"on closeModals from body remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from me\">\n            <!-- Content will be loaded by HTMX -->\n            <div class=\"text-center py-10\">\n                <div class=\"inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500\"></div>\n                <p class=\"mt-2 text-gray-600\">Loading content...</p>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    document.addEventListener('alpine:init', () => {\n        Alpine.data('modal', () => ({\n            show: false,\n            open() { this.show = true },\n            close() { this.show = false },\n        }));\n    });\n\n    // Initialize DataTable after HTMX content load\n    document.body.addEventListener('htmx:afterSwap', function(evt) {\n        if (evt.target.id === 'subcategoryTable-container') {\n            $('#subcategoryTable').DataTable({\n                \"pageLength\": 10,\n                \"lengthMenu\": [[10, 25, 50, -1], [10, 25, 50, \"All\"]],\n                \"dom\": '<\"flex justify-between items-center flex-wrap\"lf><\"block w-full overflow-x-auto\"t><\"flex justify-between items-center flex-wrap\"ip>',\n                \"language\": {\n                    \"search\": \"Search:\",\n                    \"lengthMenu\": \"Show _MENU_ entries\",\n                    \"info\": \"Showing _START_ to _END_ of _TOTAL_ entries\",\n                    \"infoEmpty\": \"Showing 0 to 0 of 0 entries\",\n                    \"infoFiltered\": \"(filtered from _MAX_ total entries)\",\n                    \"paginate\": {\n                        \"first\": \"First\",\n                        \"last\": \"Last\",\n                        \"next\": \"Next\",\n                        \"previous\": \"Previous\"\n                    }\n                }\n            });\n        }\n    });\n</script>\n{% endblock %}", "<!-- sales_distribution/subcategory/_subcategory_table.html -->\n<table id=\"subcategoryTable\" class=\"min-w-full bg-white border border-gray-200 divide-y divide-gray-200 rounded-lg shadow-sm\">\n    <thead class=\"bg-gray-50\">\n        <tr>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">SN</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Category</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">SubCategory Name</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Symbol</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Actions</th>\n        </tr>\n    </thead>\n    <tbody class=\"divide-y divide-gray-100\">\n        {% for obj in subcategories %}\n        <tr class=\"hover:bg-gray-50\">\n            <td class=\"py-3 px-4 whitespace-nowrap text-sm text-gray-800\">{{ forloop.counter }}</td>\n            <td class=\"py-3 px-4 whitespace-nowrap text-sm text-gray-800\">{{ obj.category.symbol }} - {{ obj.category.cname }}</td>\n            <td class=\"py-3 px-4 whitespace-nowrap text-sm text-gray-800\">{{ obj.scname }}</td>\n            <td class=\"py-3 px-4 whitespace-nowrap text-sm text-gray-800\">{{ obj.symbol }}</td>\n            <td class=\"py-3 px-4 whitespace-nowrap text-sm\">\n                <button \n                    class=\"bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1.5 px-3 rounded-md mr-2 transition duration-200 ease-in-out shadow-sm\"\n                    hx-get=\"{% url 'subcategory_edit' obj.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent\"\n                    aria-label=\"Edit Sub Category\">\n                    <i class=\"fas fa-edit\"></i> Edit\n                </button>\n                <button \n                    class=\"bg-red-500 hover:bg-red-600 text-white font-medium py-1.5 px-3 rounded-md transition duration-200 ease-in-out shadow-sm\"\n                    hx-get=\"{% url 'subcategory_delete' obj.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent\"\n                    aria-label=\"Delete Sub Category\">\n                    <i class=\"fas fa-trash-alt\"></i> Delete\n                </button>\n            </td>\n        </tr>\n        {% empty %}\n        <tr>\n            <td colspan=\"5\" class=\"py-4 px-4 text-center text-gray-500\">No Sub Categories found.</td>\n        </tr>\n        {% endfor %}\n    </tbody>\n</table>", "<!-- sales_distribution/subcategory/_subcategory_form.html -->\n<div class=\"p-6\">\n    <h3 class=\"text-2xl font-semibold text-gray-900 mb-6\">{{ form.instance.pk|yesno:'Edit,Add' }} Sub Category</h3>\n    <form hx-post=\"{{ request.path }}\" hx-swap=\"none\" class=\"space-y-6\">\n        {% csrf_token %}\n        \n        {% for field in form %}\n        <div class=\"mb-4\">\n            <label for=\"{{ field.id_for_label }}\" class=\"block text-sm font-medium text-gray-700 mb-1\">\n                {{ field.label }}\n                {% if field.field.required %}<span class=\"text-red-500\">*</span>{% endif %}\n            </label>\n            {{ field }}\n            {% if field.errors %}\n            <p class=\"text-red-600 text-sm mt-1\">{{ field.errors }}</p>\n            {% endif %}\n            {% if field.help_text %}\n            <p class=\"text-gray-500 text-xs mt-1\">{{ field.help_text }}</p>\n            {% endif %}\n        </div>\n        {% endfor %}\n        \n        {% if form.non_field_errors %}\n        <div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\" role=\"alert\">\n            {% for error in form.non_field_errors %}\n            <p>{{ error }}</p>\n            {% endfor %}\n        </div>\n        {% endif %}\n\n        <div class=\"mt-8 flex justify-end space-x-4\">\n            <button \n                type=\"button\" \n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out\"\n                _=\"on click remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent\">\n                Cancel\n            </button>\n            <button \n                type=\"submit\" \n                class=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out\">\n                Save\n            </button>\n        </div>\n    </form>\n</div>", "<!-- sales_distribution/subcategory/_subcategory_confirm_delete.html -->\n<div class=\"p-6\">\n    <h3 class=\"text-2xl font-semibold text-gray-900 mb-6\">Confirm Delete</h3>\n    <p class=\"text-lg text-gray-700 mb-8\">Are you sure you want to delete the Sub Category \"<span class=\"font-bold\">{{ object.scname }} ({{ object.symbol }})</span>\" from Category \"<span class=\"font-bold\">{{ object.category.cname }}</span>\"?</p>\n    \n    <form hx-post=\"{% url 'subcategory_delete' object.pk %}\" hx-swap=\"none\">\n        {% csrf_token %}\n        <div class=\"flex justify-end space-x-4\">\n            <button \n                type=\"button\" \n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out\"\n                _=\"on click remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent\">\n                Cancel\n            </button>\n            <button \n                type=\"submit\" \n                class=\"bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out\">\n                Delete\n            </button>\n        </div>\n    </form>\n</div>"], "tests": ["from django.test import TestCase, Client\nfrom django.urls import reverse\nfrom django.core.exceptions import ValidationError\nfrom datetime import datetime\nfrom unittest.mock import patch\n\nfrom .models import Category, SubCategory\n\n# Mock request.user methods for testing purposes if they are used in forms/views\nclass MockUser:\n    def __init__(self, username='testuser', company_id=1, is_authenticated=True):\n        self.username = username\n        self.company_id = company_id\n        self.is_authenticated = is_authenticated\n\n    def get_company_id(self):\n        return self.company_id\n\nclass SubCategoryModelTest(TestCase):\n    @classmethod\n    def setUpTestData(cls):\n        # Create a mock Category for foreign key\n        cls.category1 = Category.objects.create(\n            cid=101, cname='Test Category A', symbol='A', compid=1, finyearid=2023, has_subcat=True\n        )\n        cls.category2 = Category.objects.create(\n            cid=102, cname='Test Category B', symbol='B', compid=1, finyearid=2023, has_subcat=True\n        )\n\n        # Create initial SubCategory for tests\n        cls.subcategory1 = SubCategory.objects.create(\n            scid=1,\n            category=cls.category1,\n            scname='Test SubCategory 1',\n            symbol='X',\n            compid=1,\n            sysdate='01-01-2023',\n            systime='10:00:00',\n            finyearid=2023,\n            sessionid='testuser'\n        )\n  \n    def test_subcategory_creation(self):\n        obj = SubCategory.objects.get(scid=1)\n        self.assertEqual(obj.scname, 'Test SubCategory 1')\n        self.assertEqual(obj.symbol, 'X')\n        self.assertEqual(obj.category, self.category1)\n        self.assertEqual(obj.compid, 1)\n        self.assertEqual(obj.finyearid, 2023)\n        self.assertEqual(obj.sessionid, 'testuser')\n        \n    def test_subcategory_str_representation(self):\n        obj = SubCategory.objects.get(scid=1)\n        self.assertEqual(str(obj), \"Test SubCategory 1 (X)\")\n\n    def test_category_str_representation(self):\n        self.assertEqual(str(self.category1), \"A - Test Category A\")\n\n    def test_symbol_uppercase_on_save(self):\n        new_sub = SubCategory(\n            scid=2, category=self.category1, scname='New Sub', symbol='y',\n            compid=1, finyearid=2023, sysdate='02-01-2023', systime='11:00:00', sessionid='testuser'\n        )\n        new_sub.save()\n        self.assertEqual(new_sub.symbol, 'Y')\n        \n    def test_unique_symbol_validation(self):\n        # This should fail due to unique_together constraint\n        duplicate_sub = SubCategory(\n            scid=3, category=self.category1, scname='Duplicate Sub', symbol='X',\n            compid=1, finyearid=2023, sysdate='03-01-2023', systime='12:00:00', sessionid='testuser'\n        )\n        with self.assertRaises(ValidationError): # Model's clean method handles this\n            duplicate_sub.full_clean() # Calls clean() and validates unique_together\n        \n        # Test unique for different category\n        new_sub_different_category = SubCategory(\n            scid=4, category=self.category2, scname='Unique Sub', symbol='X',\n            compid=1, finyearid=2023, sysdate='04-01-2023', systime='13:00:00', sessionid='testuser'\n        )\n        try:\n            new_sub_different_category.full_clean()\n            new_sub_different_category.save()\n        except ValidationError:\n            self.fail(\"ValidationError raised unexpectedly for different category.\")\n\n    def test_category_has_subcat_filter(self):\n        Category.objects.create(cid=103, cname='No SubCat', symbol='N', compid=1, finyearid=2023, has_subcat=False)\n        form = SubCategoryForm()\n        # Only categories with has_subcat=True should be in the queryset\n        self.assertIn(self.category1, form.fields['category'].queryset)\n        self.assertNotIn(Category.objects.get(cid=103), form.fields['category'].queryset)\n\nclass SubCategoryViewsTest(TestCase):\n    @classmethod\n    def setUpTestData(cls):\n        cls.category1 = Category.objects.create(\n            cid=101, cname='Test Category A', symbol='A', compid=1, finyearid=2023, has_subcat=True\n        )\n        cls.category2 = Category.objects.create(\n            cid=102, cname='Test Category B', symbol='B', compid=1, finyearid=2023, has_subcat=True\n        )\n        cls.subcategory1 = SubCategory.objects.create(\n            scid=1, category=cls.category1, scname='View SubCategory 1', symbol='V',\n            compid=1, sysdate='01-01-2023', systime='10:00:00', finyearid=2023, sessionid='testuser'\n        )\n        cls.subcategory2 = SubCategory.objects.create(\n            scid=2, category=cls.category2, scname='View SubCategory 2', symbol='W',\n            compid=1, sysdate='01-01-2023', systime='10:00:00', finyearid=2023, sessionid='testuser'\n        )\n    \n    def setUp(self):\n        self.client = Client()\n        self.mock_user = MockUser(username='testuser', company_id=1)\n        self.client.force_login(self.mock_user) # Assuming user is logged in\n        # Mocking get_company_id and finyear_id for form/view to pick up\n        with patch('sales_distribution.forms.SubCategoryForm.clean') as mock_clean:\n            mock_clean.return_value = {} # Allow clean to pass without custom logic for these tests\n            self.client.session['finyear_id'] = 2023 # Mock session data\n\n    def test_list_view(self):\n        response = self.client.get(reverse('subcategory_list'))\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/list.html')\n        # subcategories context will be empty as it's loaded by partial view\n        self.assertContains(response, '<div id=\"subcategoryTable-container\"')\n\n    def test_table_partial_view_htmx(self):\n        # Test for HTMX request for the table partial\n        response = self.client.get(reverse('subcategory_table'), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_table.html')\n        self.assertContains(response, 'View SubCategory 1')\n        self.assertContains(response, 'View SubCategory 2')\n        self.assertContains(response, 'id=\"subcategoryTable\"')\n\n    def test_create_view_get(self):\n        response = self.client.get(reverse('subcategory_add'), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertTrue('form' in response.context)\n        \n    @patch('sales_distribution.models.SubCategory.save')\n    def test_create_view_post_success(self, mock_save):\n        mock_save.return_value = None # Prevent actual DB save for unit test\n        data = {\n            'category': self.category1.cid,\n            'scname': 'New Test SubCategory',\n            'symbol': 'Z',\n        }\n        # Simulate HTMX request\n        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 204) # HTMX success response\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertIn('refreshSubCategoryList', response.headers['HX-Trigger'])\n        messages = list(response.wsgi_request._messages)\n        self.assertEqual(len(messages), 1)\n        self.assertEqual(str(messages[0]), 'Sub Category added successfully.')\n        mock_save.assert_called_once()\n        \n    def test_create_view_post_invalid(self):\n        data = {\n            'category': self.category1.cid,\n            'scname': '', # Invalid, required field\n            'symbol': 'A',\n        }\n        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertFormError(response, 'form', 'scname', ['This field is required.'])\n        messages = list(response.wsgi_request._messages)\n        self.assertEqual(len(messages), 1)\n        self.assertEqual(str(messages[0]), 'Please correct the errors below.')\n\n    def test_create_view_post_duplicate_symbol(self):\n        # Create a duplicate entry data\n        data = {\n            'category': self.category1.cid,\n            'scname': 'Another Sub',\n            'symbol': 'V', # This symbol already exists for category1, compid=1, finyearid=2023\n        }\n        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertFormError(response, 'form', 'symbol', [\"This symbol is already used for the selected Category, Company, and Financial Year.\"])\n        messages = list(response.wsgi_request._messages)\n        self.assertEqual(len(messages), 1)\n        self.assertEqual(str(messages[0]), 'Please correct the errors below.')\n\n    def test_update_view_get(self):\n        obj = self.subcategory1\n        response = self.client.get(reverse('subcategory_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertTrue('form' in response.context)\n        self.assertEqual(response.context['form'].instance, obj)\n        \n    @patch('sales_distribution.models.SubCategory.save')\n    def test_update_view_post_success(self, mock_save):\n        mock_save.return_value = None\n        obj = self.subcategory1\n        data = {\n            'category': obj.category.cid,\n            'scname': 'Updated SubCategory',\n            'symbol': 'X', # Changed symbol\n        }\n        response = self.client.post(reverse('subcategory_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertIn('refreshSubCategoryList', response.headers['HX-Trigger'])\n        messages = list(response.wsgi_request._messages)\n        self.assertEqual(len(messages), 1)\n        self.assertEqual(str(messages[0]), 'Sub Category updated successfully.')\n        mock_save.assert_called_once()\n        # Verify changes (if mock_save wasn't used)\n        # updated_obj = SubCategory.objects.get(pk=obj.pk)\n        # self.assertEqual(updated_obj.scname, 'Updated SubCategory')\n\n    def test_delete_view_get(self):\n        obj = self.subcategory1\n        response = self.client.get(reverse('subcategory_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_confirm_delete.html')\n        self.assertTrue('object' in response.context)\n        self.assertEqual(response.context['object'], obj)\n        \n    def test_delete_view_post_success(self):\n        obj_to_delete = SubCategory.objects.create(\n            scid=99, category=self.category1, scname='Delete Test', symbol='D',\n            compid=1, sysdate='01-01-2023', systime='10:00:00', finyearid=2023, sessionid='testuser'\n        )\n        self.assertTrue(SubCategory.objects.filter(pk=obj_to_delete.pk).exists())\n        \n        response = self.client.post(reverse('subcategory_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertIn('refreshSubCategoryList', response.headers['HX-Trigger'])\n        messages = list(response.wsgi_request._messages)\n        self.assertEqual(len(messages), 1)\n        self.assertEqual(str(messages[0]), 'Sub Category deleted successfully.')\n        self.assertFalse(SubCategory.objects.filter(pk=obj_to_delete.pk).exists())"], "admin": []}