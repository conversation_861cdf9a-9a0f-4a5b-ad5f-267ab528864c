{"models": ["from django.db import models\nfrom django.core.exceptions import ValidationError\n\nclass Category(models.Model):\n    \"\"\"\n    Represents the 'tblSD_WO_Category' table.\n    Used as a lookup for SubCategory's CId.\n    \"\"\"\n    CId = models.IntegerField(db_column='CId', primary_key=True)\n    CName = models.CharField(db_column='CName', max_length=255) # Assuming appropriate max_length\n    Symbol = models.CharField(db_column='Symbol', max_length=50) # Assuming appropriate max_length\n\n    class Meta:\n        managed = False  # Tells Django not to manage this table's schema\n        db_table = 'tblSD_WO_Category' # Maps to the existing database table\n        verbose_name = 'Category'\n        verbose_name_plural = 'Categories'\n\n    def __str__(self):\n        \"\"\"Returns a user-friendly representation of the category.\"\"\"\n        return f\"{self.Symbol} - {self.CName}\"\n\nclass SubCategory(models.Model):\n    \"\"\"\n    Represents the 'tblSD_WO_SubCategory' table.\n    This is the core model for the SubCategory module.\n    \"\"\"\n    SCId = models.IntegerField(db_column='SCId', primary_key=True)\n    CId = models.ForeignKey(Category, on_delete=models.PROTECT, db_column='CId', related_name='subcategories')\n    SCName = models.CharField(db_column='SCName', max_length=255) # Assuming appropriate max_length\n    Symbol = models.CharField(db_column='Symbol', max_length=1) # MaxLength derived from ASPX\n\n    class Meta:\n        managed = False\n        db_table = 'tblSD_WO_SubCategory'\n        verbose_name = 'SubCategory'\n        verbose_name_plural = 'SubCategories'\n        # Enforce uniqueness of 'Symbol' within each 'Category'\n        unique_together = (('CId', 'Symbol'),)\n\n    def __str__(self):\n        \"\"\"Returns the subcategory name for display.\"\"\"\n        return self.SCName\n\n    def clean(self):\n        \"\"\"\n        Custom validation and data normalization for SubCategory.\n        This method is called before saving the model (e.g., by ModelForms).\n        \"\"\"\n        # Business logic: Ensure Symbol is always uppercase, as per ASP.NET\n        if self.Symbol:\n            self.Symbol = self.Symbol.upper()\n\n        # Business logic: Check if Symbol is unique for the given Category.\n        # Although unique_together handles this at the DB level, this provides\n        # a more user-friendly validation message within the form.\n        if self.CId and self.Symbol:\n            qs = SubCategory.objects.filter(CId=self.CId, Symbol=self.Symbol)\n            if self.pk:  # Exclude self if updating an existing record\n                qs = qs.exclude(pk=self.pk)\n            if qs.exists():\n                raise ValidationError({'Symbol': 'SubCategory symbol is already used for this category.'})\n\n    def is_deletable(self):\n        \"\"\"\n        Business logic: Checks if the SubCategory can be deleted.\n        A SubCategory cannot be deleted if it's referenced in a Customer Work Order.\n        \"\"\"\n        return not CustomerWorkOrder.objects.filter(CId=self.CId, SCId=self.SCId).exists()\n\n    @property\n    def category_display(self):\n        \"\"\"\n        Provides the combined 'Category Symbol - Category Name' string for display,\n        mimicking the 'catsy' field in the original ASP.NET SelectCommand.\n        \"\"\"\n        if self.CId:\n            return f\"{self.CId.Symbol} - {self.CId.CName}\"\n        return \"N/A\"\n\nclass CustomerWorkOrder(models.Model):\n    \"\"\"\n    Represents the 'SD_Cust_WorkOrder_Master' table.\n    Only includes fields relevant to the SubCategory deletion business rule.\n    \"\"\"\n    # Assuming a primary key exists, though not explicitly used in original query\n    WOId = models.AutoField(db_column='WOId', primary_key=True) # Placeholder PK\n\n    CId = models.ForeignKey(Category, on_delete=models.PROTECT, db_column='CId')\n    SCId = models.ForeignKey(SubCategory, on_delete=models.PROTECT, db_column='SCId')\n\n    class Meta:\n        managed = False\n        db_table = 'SD_Cust_WorkOrder_Master'\n        verbose_name = 'Customer Work Order'\n        verbose_name_plural = 'Customer Work Orders'\n        # If there's no natural unique key, Django might complain, but for this use case,\n        # we just need to query it for the deletion check."], "views": ["from django.views.generic import ListView, <PERSON>reateView, UpdateView, DeleteView, View\nfrom django.urls import reverse_lazy\nfrom django.contrib import messages\nfrom django.http import HttpResponse, HttpResponseRedirect\nfrom django.shortcuts import render, get_object_or_404\nfrom django.template.loader import render_to_string\nfrom django.core.exceptions import ValidationError # Import ValidationError\nfrom .models import SubCategory, Category # Ensure Category is imported\nfrom .forms import SubCategoryForm\n\nclass SubCategoryListView(ListView):\n    \"\"\"\n    Displays a list of all SubCategories.\n    This is the main view that renders the base page.\n    \"\"\"\n    model = SubCategory\n    template_name = 'sales_distribution/subcategory/list.html'\n    context_object_name = 'subcategories' # Renamed for clarity in template\n\n    def get_queryset(self):\n        \"\"\"\n        Orders the queryset by SCId in descending order, matching ASP.NET behavior.\n        \"\"\"\n        return SubCategory.objects.order_by('-SCId')\n\nclass SubCategoryTablePartialView(ListView):\n    \"\"\"\n    Returns only the HTML fragment for the SubCategory table.\n    This view is specifically designed for HTMX requests to refresh the table.\n    \"\"\"\n    model = SubCategory\n    template_name = 'sales_distribution/subcategory/_subcategory_table.html'\n    context_object_name = 'subcategories'\n\n    def get_queryset(self):\n        \"\"\"\n        Orders the queryset by SCId in descending order.\n        \"\"\"\n        return SubCategory.objects.order_by('-SCId')\n\nclass SubCategoryCreateView(CreateView):\n    \"\"\"\n    Handles the creation of new SubCategory objects.\n    Renders a form within a modal triggered by HTMX.\n    \"\"\"\n    model = SubCategory\n    form_class = SubCategoryForm\n    template_name = 'sales_distribution/subcategory/_subcategory_form.html' # Partial template for modal\n    success_url = reverse_lazy('subcategory_list') # Fallback URL, not directly used with HTMX\n\n    def form_valid(self, form):\n        \"\"\"\n        Called when form data is valid. Saves the object and sends HTMX trigger.\n        Business logic (symbol uppercase, uniqueness check) is handled by model's clean() method.\n        \"\"\"\n        try:\n            # Call clean() explicitly to ensure model-level validation runs for HTMX\n            form.instance.full_clean()\n            response = super().form_valid(form)\n            messages.success(self.request, 'SubCategory added successfully.')\n            if self.request.headers.get('HX-Request'):\n                # Send 204 No Content to close modal and trigger list refresh\n                return HttpResponse(\n                    status=204,\n                    headers={\n                        'HX-Trigger': 'refreshSubCategoryList' # Custom event to refresh table\n                    }\n                )\n            return response\n        except ValidationError as e:\n            # If model clean() raises ValidationError, add to form errors and re-render\n            form.add_error(None, e) # Add non-field errors (global validation)\n            return self.form_invalid(form) # Re-render form with errors\n\n    def form_invalid(self, form):\n        \"\"\"\n        Called when form data is invalid. Rerenders the form with errors for HTMX.\n        \"\"\"\n        return render(self.request, self.template_name, {'form': form})\n\nclass SubCategoryUpdateView(UpdateView):\n    \"\"\"\n    Handles the update of existing SubCategory objects.\n    Renders a pre-filled form within a modal triggered by HTMX.\n    \"\"\"\n    model = SubCategory\n    form_class = SubCategoryForm\n    template_name = 'sales_distribution/subcategory/_subcategory_form.html' # Partial template for modal\n    success_url = reverse_lazy('subcategory_list') # Fallback URL\n\n    def form_valid(self, form):\n        \"\"\"\n        Called when form data is valid. Saves updates and sends HTMX trigger.\n        Business logic (symbol uppercase, uniqueness check) handled by model's clean() method.\n        \"\"\"\n        try:\n            form.instance.full_clean()\n            response = super().form_valid(form)\n            messages.success(self.request, 'SubCategory updated successfully.')\n            if self.request.headers.get('HX-Request'):\n                return HttpResponse(\n                    status=204,\n                    headers={\n                        'HX-Trigger': 'refreshSubCategoryList'\n                    }\n                )\n            return response\n        except ValidationError as e:\n            form.add_error(None, e)\n            return self.form_invalid(form)\n\n    def form_invalid(self, form):\n        \"\"\"\n        Called when form data is invalid. Rerenders the form with errors for HTMX.\n        \"\"\"\n        return render(self.request, self.template_name, {'form': form})\n\nclass SubCategoryDeleteView(DeleteView):\n    \"\"\"\n    Handles the deletion of SubCategory objects.\n    Renders a confirmation prompt within a modal triggered by HTMX.\n    \"\"\"\n    model = SubCategory\n    template_name = 'sales_distribution/subcategory/_subcategory_confirm_delete.html' # Partial template for modal\n    success_url = reverse_lazy('subcategory_list') # Fallback URL\n\n    def delete(self, request, *args, **kwargs):\n        \"\"\"\n        Performs the deletion after checking business rules.\n        \"\"\"\n        self.object = self.get_object()\n        # Business logic: Check if the SubCategory is deletable using the model method\n        if not self.object.is_deletable():\n            messages.error(request, 'SubCategory cannot be deleted as it is linked to existing work orders.')\n            if request.headers.get('HX-Request'):\n                # Re-render the confirmation modal with an error message, keeping modal open\n                context = self.get_context_data(object=self.object)\n                # Add a flag to the context to display the error in the template\n                context['cannot_delete_error'] = True\n                html = render_to_string(self.template_name, context, request=request)\n                return HttpResponse(html, status=200) # Return 200 OK to swap content\n            return HttpResponseRedirect(self.get_success_url()) # Fallback for non-HTMX\n\n        # If deletable, proceed with actual deletion\n        response = super().delete(request, *args, **kwargs)\n        messages.success(self.request, 'SubCategory deleted successfully.')\n        if request.headers.get('HX-Request'):\n            # Send 204 No Content to close modal and trigger list refresh\n            return HttpResponse(\n                status=204,\n                headers={\n                    'HX-Trigger': 'refreshSubCategoryList'\n                }\n            )\n        return response"], "forms": ["from django import forms\nfrom .models import SubCategory, Category\n\nclass SubCategoryForm(forms.ModelForm):\n    \"\"\"\n    Form for creating and updating SubCategory instances.\n    \"\"\"\n    class Meta:\n        model = SubCategory\n        fields = ['CId', 'SCName', 'Symbol'] # Fields to be included in the form\n        widgets = {\n            'CId': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),\n            'SCName': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),\n            'Symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'maxlength': '1'}),\n        }\n        labels = {\n            'CId': 'Category',\n            'SCName': 'SubCategory Name',\n            'Symbol': 'Symbol',\n        }\n\n    def __init__(self, *args, **kwargs):\n        super().__init__(*args, **kwargs)\n        # Populate CId dropdown with all Category objects\n        self.fields['CId'].queryset = Category.objects.order_by('CName')\n\n        # If this form is being used to update an existing object (instance exists),\n        # make the 'Symbol' field read-only as per the original ASP.NET behavior.\n        if self.instance and self.instance.pk:\n            self.fields['Symbol'].widget.attrs['readonly'] = 'readonly'\n            self.fields['Symbol'].widget.attrs['class'] += ' bg-gray-100 cursor-not-allowed' # Add styling for readonly state"], "urls": ["from django.urls import path\nfrom .views import (\n    SubCategoryListView,\n    SubCategoryTablePartialView, # Added for HTMX-specific table refresh\n    SubCategoryCreateView,\n    SubCategoryUpdateView,\n    SubCategoryDeleteView\n)\n\nurlpatterns = [\n    # Main list view for SubCategories\n    path('subcategory/', SubCategoryListView.as_view(), name='subcategory_list'),\n\n    # HTMX-specific endpoint to get the table content (for initial load and refreshes)\n    path('subcategory/table/', SubCategoryTablePartialView.as_view(), name='subcategory_table'),\n\n    # Endpoints for CRUD operations, used by HTMX modals\n    path('subcategory/add/', SubCategoryCreateView.as_view(), name='subcategory_add'),\n    path('subcategory/edit/<int:pk>/', SubCategoryUpdateView.as_view(), name='subcategory_edit'),\n    path('subcategory/delete/<int:pk>/', SubCategoryDeleteView.as_view(), name='subcategory_delete'),\n]"], "templates": ["<!-- list.html -->\n{% extends 'core/base.html' %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h2 class=\"text-2xl font-bold\">SubCategories</h2>\n        <button\n            class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n            hx-get=\"{% url 'subcategory_add' %}\" {# HTMX GET request to load add form #}\n            hx-target=\"#modalContent\" {# Load into the modal's content div #}\n            hx-trigger=\"click\"\n            _=\"on click add .is-active to #modal\"> {# Alpine.js/Hyperscript to show modal #}\n            Add New SubCategory\n        </button>\n    </div>\n\n    {# Container for the DataTables-powered list, loaded via HTMX #}\n    <div id=\"subcategoryTable-container\"\n         hx-trigger=\"load, refreshSubCategoryList from:body\" {# Load on page load and on custom event #}\n         hx-get=\"{% url 'subcategory_table' %}\" {# HTMX GET request for table content #}\n         hx-swap=\"innerHTML\"> {# Replace the inner HTML of this div #}\n        <!-- Loading indicator while DataTable loads -->\n        <div class=\"text-center\">\n            <div class=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            <p class=\"mt-2\">Loading SubCategories...</p>\n        </div>\n    </div>\n\n    <!-- Universal Modal for forms and confirmations -->\n    <div id=\"modal\" class=\"fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden\"\n         x-data=\"{ showModal: false }\" x-show=\"showModal\" {# Alpine.js state for modal visibility #}\n         _=\"on click if event.target.id == 'modal' remove .is-active from me then set showModal to false\"> {# Close modal on backdrop click #}\n        <div id=\"modalContent\" class=\"bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full\"\n             _=\"on htmx:afterSwap add .is-active to #modal then set showModal to true\"> {# Show modal after HTMX loads content #}\n            <!-- Content for forms/delete confirmation will be loaded here via HTMX -->\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{# No extra JavaScript needed beyond what's in base.html for HTMX/Alpine.js/DataTables #}\n{% block extra_js %}\n{# Keep this block if you have any page-specific JS, otherwise it can be empty #}\n{% endblock %}", "<!-- _subcategory_table.html -->\n<table id=\"subcategoryTable\" class=\"min-w-full bg-white border border-gray-200 divide-y divide-gray-200\">\n    <thead>\n        <tr class=\"bg-gray-50\">\n            <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">SN</th>\n            <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Category</th>\n            <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">SubCategory</th>\n            <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Symbol</th>\n            <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n        </tr>\n    </thead>\n    <tbody>\n        {% for subcategory in subcategories %}\n        <tr class=\"hover:bg-gray-100\">\n            <td class=\"py-2 px-4 border-b border-gray-200\">{{ forloop.counter }}</td>\n            <td class=\"py-2 px-4 border-b border-gray-200\">{{ subcategory.category_display }}</td>\n            <td class=\"py-2 px-4 border-b border-gray-200\">{{ subcategory.SCName }}</td>\n            <td class=\"py-2 px-4 border-b border-gray-200\">{{ subcategory.Symbol }}</td>\n            <td class=\"py-2 px-4 border-b border-gray-200 flex space-x-2\">\n                <button\n                    class=\"bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-sm\"\n                    hx-get=\"{% url 'subcategory_edit' subcategory.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .is-active to #modal\">\n                    Edit\n                </button>\n                {% if subcategory.is_deletable %} {# Business rule: show delete if deletable #}\n                <button\n                    class=\"bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm\"\n                    hx-get=\"{% url 'subcategory_delete' subcategory.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .is-active to #modal\">\n                    Delete\n                </button>\n                {% else %}\n                <span class=\"bg-gray-300 text-gray-600 font-bold py-1 px-2 rounded text-sm cursor-not-allowed\">\n                    Used\n                </span>\n                {% endif %}\n            </td>\n        </tr>\n        {% empty %}\n        <tr>\n            <td colspan=\"5\" class=\"py-4 px-4 text-center text-gray-500\">\n                No SubCategories found.\n                <button\n                    class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4\"\n                    hx-get=\"{% url 'subcategory_add' %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .is-active to #modal\">\n                    Add New SubCategory\n                </button>\n            </td>\n        </tr>\n        {% endfor %}\n    </tbody>\n</table>\n\n<script>\n// Initialize DataTables after the table has been loaded into the DOM by HTMX\n$(document).ready(function() {\n    $('#subcategoryTable').DataTable({\n        \"pageLength\": 15, // Matching ASP.NET PageSize\n        \"lengthMenu\": [[10, 15, 25, 50, -1], [10, 15, 25, 50, \"All\"]]\n    });\n});\n</script>", "<!-- _subcategory_form.html -->\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium text-gray-900 mb-5\">\n        {% if form.instance.pk %}Edit{% else %}Add{% endif %} SubCategory\n    </h3>\n    {# hx-swap=\"none\" means HTMX won't change the DOM, but listens for HX-Trigger #}\n    <form hx-post=\"{{ request.path }}\" hx-swap=\"none\">\n        {% csrf_token %} {# Django's protection against Cross-Site Request Forgeries #}\n\n        <div class=\"space-y-4\">\n            {% for field in form %}\n            <div class=\"mb-4\">\n                <label for=\"{{ field.id_for_label }}\" class=\"block text-sm font-medium text-gray-700\">\n                    {{ field.label }}\n                    {% if field.field.required %} <span class=\"text-red-500\">*</span> {% endif %}\n                </label>\n                {{ field }} {# Renders the Django form field with its widget and attrs #}\n                {% if field.help_text %}\n                <p class=\"mt-2 text-sm text-gray-500\">{{ field.help_text }}</p>\n                {% endif %}\n                {% if field.errors %}\n                <p class=\"text-red-500 text-xs mt-1\">{{ field.errors }}</p>\n                {% endif %}\n            </div>\n            {% endfor %}\n            {# Display non-field errors (e.g., model clean() errors) #}\n            {% if form.non_field_errors %}\n                <div class=\"text-red-500 text-xs mt-1\">\n                    {% for error in form.non_field_errors %}\n                        <p>{{ error }}</p>\n                    {% endfor %}\n                </div>\n            {% endif %}\n        </div>\n\n        <div class=\"mt-6 flex items-center justify-end space-x-4\">\n            <button\n                type=\"button\"\n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n                _=\"on click remove .is-active from #modal\"> {# Closes the modal without submitting #}\n                Cancel\n            </button>\n            <button\n                type=\"submit\"\n                class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\n                Save\n            </button>\n        </div>\n    </form>\n</div>", "<!-- _subcategory_confirm_delete.html -->\n<div class=\"p-6\">\n    <h3 class=\"text-lg font-medium text-gray-900 mb-5\">Confirm Deletion</h3>\n    <p class=\"text-gray-700 mb-4\">\n        Are you sure you want to delete the SubCategory \"<strong>{{ object.SCName }} ({{ object.Symbol }})</strong>\" under Category \"<strong>{{ object.category_display }}</strong>\"?\n        This action cannot be undone.\n    </p>\n\n    {# Display messages (e.g., from messages framework) if deletion failed #}\n    {% if messages %}\n        <div class=\"mt-4\">\n            {% for message in messages %}\n                <div class=\"p-3 mb-2 rounded {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% endif %}\">\n                    {{ message }}\n                </div>\n            {% endfor %}\n        </div>\n    {% endif %}\n\n    {# Specific error message if the model's is_deletable() check failed #}\n    {% if cannot_delete_error %}\n        <div class=\"mt-4 p-3 mb-2 rounded bg-red-100 text-red-700\">\n            This SubCategory cannot be deleted as it is linked to existing work orders.\n        </div>\n    {% endif %}\n\n    <div class=\"mt-6 flex items-center justify-end space-x-4\">\n        <button\n            type=\"button\"\n            class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded\"\n            _=\"on click remove .is-active from #modal\">\n            Cancel\n        </button>\n        <button\n            hx-post=\"{% url 'subcategory_delete' object.pk %}\" {# HTMX POST request to trigger deletion #}\n            hx-swap=\"none\" {# No swap, relies on HX-Trigger for UI update #}\n            class=\"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded\">\n            Confirm Delete\n        </button>\n    </div>\n</div>"], "tests": ["from django.test import TestCase, Client\nfrom django.urls import reverse\nfrom django.core.exceptions import ValidationError\nfrom .models import Category, SubCategory, CustomerWorkOrder\n\n# --- Model Tests ---\n\nclass CategoryModelTest(TestCase):\n    \"\"\"\n    Tests for the Category model.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Create a dummy category for FK relations to be used across all tests\n        Category.objects.create(CId=101, CName='Test Category', Symbol='TC')\n\n    def test_category_creation(self):\n        \"\"\"Verify Category object creation and attribute assignment.\"\"\"\n        category = Category.objects.get(CId=101)\n        self.assertEqual(category.CName, 'Test Category')\n        self.assertEqual(category.Symbol, 'TC')\n\n    def test_str_method(self):\n        \"\"\"Test the __str__ representation of the Category model.\"\"\"\n        category = Category.objects.get(CId=101)\n        self.assertEqual(str(category), 'TC - Test Category')\n\n\nclass SubCategoryModelTest(TestCase):\n    \"\"\"\n    Tests for the SubCategory model, including business logic.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Create categories for testing foreign key relationships\n        cls.category1 = Category.objects.create(CId=1, CName='Category A', Symbol='A')\n        cls.category2 = Category.objects.create(CId=2, CName='Category B', Symbol='B')\n\n        # Create a test subcategory\n        cls.subcategory1 = SubCategory.objects.create(\n            SCId=1, CId=cls.category1, SCName='SubCategory Alpha', Symbol='X'\n        )\n\n    def test_subcategory_creation(self):\n        \"\"\"Verify SubCategory object creation and attribute assignment.\"\"\"\n        self.assertEqual(self.subcategory1.CId, self.category1)\n        self.assertEqual(self.subcategory1.SCName, 'SubCategory Alpha')\n        self.assertEqual(self.subcategory1.Symbol, 'X')\n\n    def test_str_method(self):\n        \"\"\"Test the __str__ representation of the SubCategory model.\"\"\"\n        self.assertEqual(str(self.subcategory1), 'SubCategory Alpha')\n\n    def test_category_display_property(self):\n        \"\"\"Test the custom 'category_display' property.\"\"\"\n        self.assertEqual(self.subcategory1.category_display, 'A - Category A')\n\n    def test_symbol_uppercase_on_clean(self):\n        \"\"\"Verify that the Symbol is converted to uppercase on cleaning.\"\"\"\n        subcat = SubCategory(CId=self.category1, SCName='Test Sub', Symbol='y')\n        subcat.full_clean() # Triggers the model's clean() method\n        self.assertEqual(subcat.Symbol, 'Y')\n\n    def test_symbol_uniqueness_within_category(self):\n        \"\"\"\n        Test that a symbol must be unique within the same category.\n        This uses the model's clean() method validation.\n        \"\"\"\n        with self.assertRaisesMessage(ValidationError, 'SubCategory symbol is already used for this category.'):\n            subcat_duplicate_symbol = SubCategory(CId=self.category1, SCName='Duplicate Sub', Symbol='X')\n            subcat_duplicate_symbol.full_clean() # Should raise validation error\n\n    def test_symbol_uniqueness_across_categories(self):\n        \"\"\"\n        Test that the same symbol can be used in different categories.\n        \"\"\"\n        subcat_diff_category = SubCategory(CId=self.category2, SCName='Another Alpha', Symbol='X')\n        try:\n            subcat_diff_category.full_clean() # Should not raise an error\n        except ValidationError:\n            self.fail(\"ValidationError raised unexpectedly for unique symbol across different categories\")\n\n    def test_is_deletable_no_work_order(self):\n        \"\"\"\n        Verify that a SubCategory is deletable when no associated work orders exist.\n        \"\"\"\n        self.assertTrue(self.subcategory1.is_deletable())\n\n    def test_is_deletable_with_work_order(self):\n        \"\"\"\n        Verify that a SubCategory is NOT deletable when an associated work order exists.\n        \"\"\"\n        # Create a dummy work order linking to subcategory1\n        CustomerWorkOrder.objects.create(CId=self.category1, SCId=self.subcategory1)\n        self.assertFalse(self.subcategory1.is_deletable())\n\n\nclass SubCategoryViewsTest(TestCase):\n    \"\"\"\n    Integration tests for SubCategory views, including HTMX interactions.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Create categories and subcategories for testing view interactions\n        cls.category1 = Category.objects.create(CId=1, CName='Category A', Symbol='A')\n        cls.category2 = Category.objects.create(CId=2, CName='Category B', Symbol='B')\n\n        cls.subcategory1 = SubCategory.objects.create(\n            SCId=1, CId=cls.category1, SCName='SubCategory Alpha', Symbol='X'\n        )\n        SubCategory.objects.create(\n            SCId=2, CId=cls.category2, SCName='SubCategory Beta', Symbol='Y'\n        )\n\n    def setUp(self):\n        \"\"\"Set up client for each test method.\"\"\"\n        self.client = Client()\n\n    def test_list_view(self):\n        \"\"\"Test the main SubCategory list page.\"\"\"\n        response = self.client.get(reverse('subcategory_list'))\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/list.html')\n        self.assertIn('subcategories', response.context)\n        self.assertContains(response, 'SubCategory Alpha')\n        self.assertContains(response, 'SubCategory Beta')\n\n    def test_table_partial_view_htmx(self):\n        \"\"\"Test the HTMX endpoint for the table content.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'} # Mimic HTMX request header\n        response = self.client.get(reverse('subcategory_table'), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_table.html')\n        self.assertContains(response, 'SubCategory Alpha')\n        self.assertContains(response, 'SubCategory Beta')\n        self.assertContains(response, '<table id=\"subcategoryTable\"') # Check for DataTable rendering\n\n    def test_create_view_get_htmx(self):\n        \"\"\"Test loading the add form via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.get(reverse('subcategory_add'), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertIn('form', response.context)\n        self.assertContains(response, 'Add SubCategory')\n        self.assertContains(response, 'Symbol') # Check that symbol field is present for add\n\n    def test_create_view_post_htmx_success(self):\n        \"\"\"Test successful SubCategory creation via HTMX.\"\"\"\n        data = {\n            'CId': self.category2.CId,\n            'SCName': 'New SubCategory',\n            'Symbol': 'Z',\n        }\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.post(reverse('subcategory_add'), data, **headers)\n        self.assertEqual(response.status_code, 204) # HTMX success status for 'no content'\n        self.assertTrue(SubCategory.objects.filter(SCName='New SubCategory').exists())\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')\n\n    def test_create_view_post_htmx_validation_fail(self):\n        \"\"\"Test SubCategory creation failure due to validation via HTMX.\"\"\"\n        # Attempt to create with existing symbol in same category\n        data = {\n            'CId': self.category1.CId,\n            'SCName': 'Another Alpha',\n            'Symbol': 'X', # Duplicate symbol for category1\n        }\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.post(reverse('subcategory_add'), data, **headers)\n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertContains(response, 'SubCategory symbol is already used for this category.')\n        self.assertFalse(SubCategory.objects.filter(SCName='Another Alpha').exists()) # Object not created\n\n    def test_update_view_get_htmx(self):\n        \"\"\"Test loading the edit form via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.get(reverse('subcategory_edit', args=[self.subcategory1.SCId]), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertIn('form', response.context)\n        self.assertContains(response, 'Edit SubCategory')\n        self.assertContains(response, 'SubCategory Alpha') # Check pre-filled data\n        self.assertContains(response, 'readonly') # Symbol field should be readonly for edit\n\n    def test_update_view_post_htmx_success(self):\n        \"\"\"Test successful SubCategory update via HTMX.\"\"\"\n        data = {\n            'CId': self.category1.CId, # Keep same category\n            'SCName': 'Updated SubCategory Name',\n            'Symbol': 'X', # Symbol is readonly, so this value should be the original\n        }\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.post(reverse('subcategory_edit', args=[self.subcategory1.SCId]), data, **headers)\n        self.assertEqual(response.status_code, 204)\n        self.subcategory1.refresh_from_db() # Refresh instance from DB to get updated values\n        self.assertEqual(self.subcategory1.SCName, 'Updated SubCategory Name')\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')\n\n    def test_update_view_post_htmx_validation_fail(self):\n        \"\"\"\n        Test SubCategory update failure due to validation via HTMX (e.g., symbol clash on category change).\n        \"\"\"\n        # Create another subcategory with a symbol that would clash if we change category of subcategory1\n        SubCategory.objects.create(SCId=3, CId=self.category2, SCName='Temp Subcat', Symbol='Z')\n\n        data = {\n            'CId': self.category2.CId, # Change category to category2\n            'SCName': 'Updated SC with Clash',\n            'Symbol': 'X', # Original Symbol for subcategory1. If changed to category2, it would clash with Z.\n                         # This test scenario implies the existing symbol 'X' is checked against new category 'B'\n                         # which should pass, unless a subcategory with Symbol 'X' already exists in category 'B'.\n                         # Let's adjust for a clear clash based on the model's unique_together logic.\n                         # Make the original symbol 'X' clash with an existing one in category2.\n        }\n        SubCategory.objects.create(SCId=4, CId=self.category2, SCName='Existing in B', Symbol='X') # Create a clash\n\n        data_clash = {\n            'CId': self.category2.CId, # Now attempting to move subcategory1 to category2\n            'SCName': 'Updated SubCategory Name',\n            'Symbol': 'X', # subcategory1's original symbol, which now clashes in category2\n        }\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.post(reverse('subcategory_edit', args=[self.subcategory1.SCId]), data_clash, **headers)\n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')\n        self.assertContains(response, 'SubCategory symbol is already used for this category.')\n        self.subcategory1.refresh_from_db()\n        self.assertNotEqual(self.subcategory1.CId.CId, self.category2.CId) # Category should not have changed\n        self.assertEqual(self.subcategory1.SCName, 'SubCategory Alpha') # SCName should not have changed either\n\n    def test_delete_view_get_htmx(self):\n        \"\"\"Test loading the delete confirmation via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.get(reverse('subcategory_delete', args=[self.subcategory1.SCId]), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_confirm_delete.html')\n        self.assertContains(response, 'Confirm Deletion')\n        self.assertContains(response, 'SubCategory Alpha')\n\n    def test_delete_view_post_htmx_success(self):\n        \"\"\"Test successful SubCategory deletion via HTMX.\"\"\"\n        # Ensure it's deletable before trying to delete\n        self.assertTrue(self.subcategory1.is_deletable())\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.post(reverse('subcategory_delete', args=[self.subcategory1.SCId]), **headers)\n        self.assertEqual(response.status_code, 204)\n        self.assertFalse(SubCategory.objects.filter(SCId=self.subcategory1.SCId).exists())\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')\n\n    def test_delete_view_post_htmx_fail_due_to_dependency(self):\n        \"\"\"\n        Test SubCategory deletion failure when linked to a work order.\n        Should re-render modal with an error message.\n        \"\"\"\n        # Link subcategory1 to a work order to make it undeletable\n        CustomerWorkOrder.objects.create(CId=self.category1, SCId=self.subcategory1)\n        self.assertFalse(self.subcategory1.is_deletable())\n\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.post(reverse('subcategory_delete', args=[self.subcategory1.SCId]), **headers)\n        self.assertEqual(response.status_code, 200) # Should render modal with error, not 204\n        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_confirm_delete.html')\n        self.assertContains(response, 'This SubCategory cannot be deleted as it is linked to existing work orders.')\n        self.assertTrue(SubCategory.objects.filter(SCId=self.subcategory1.SCId).exists()) # Object should still exist\n        self.assertNotIn('HX-Trigger', response.headers) # No trigger to refresh list on failure"], "admin": []}